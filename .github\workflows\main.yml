name: 🚀 Smart Deploy Website on Push to Main

on:
  push:
    branches:
      - main # This workflow triggers on pushes to the dev branch

jobs:
  deploy-development: # Renamed job for clarity
    name: 🎉 Self-Healing Deploy to Development
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: SSH and Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          port: ${{ secrets.PORT }}
          key: ${{ secrets.KEY }}
          passphrase: ${{ secrets.PASSPHRASE }}
          script: |
            # Define the base directory for deployments
            BASE_DIR="/home/<USER>/domains/vieraoffice.com/public_html/"
            
            # Define the two subfolders to deploy to
            FOLDERS=("voo" "fnb")

            # Loop through each folder and perform deployment steps
            for FOLDER in "${FOLDERS[@]}"; do
              echo "--- Deploying to $BASE_DIR/$FOLDER ---"
              
              # Navigate to the specific sub-directory
              cd "$BASE_DIR/$FOLDER" || { echo "Error: Directory $BASE_DIR/$FOLDER not found. Skipping."; continue; }

              echo "Pulling latest code for $FOLDER..."
              git pull

              echo "Running Laravel optimizations for $FOLDER..."
              php artisan migrate --force || echo "Migrations skipped or already up to date for $FOLDER."
              php artisan optimize
              php artisan filament:optimize

              echo "Installing npm packages for $FOLDER..."
              npm install

              echo "Building frontend assets for $FOLDER..."
              npm run build

              echo "Deployment to $FOLDER completed successfully!"
              echo "" # Add an empty line for better readability between deployments
            done

            echo "All deployments completed!"
