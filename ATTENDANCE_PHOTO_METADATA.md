# Fitur Foto Absensi dengan Metadata

## Overview
Fitur ini mengimplementasikan sistem foto absensi yang hanya menggunakan kamera (camera-only) dengan metadata lengkap seperti koordinat GPS, timestamp, status kehadiran, dan informasi perangkat.

## Fitur Utama

### 1. Camera-Only Interface
- **Tidak ada upload file manual** - Hanya menggunakan kamera untuk mengambil foto
- **Live preview** dengan metadata overlay
- **Automatic geolocation** detection
- **Real-time timestamp** display

### 2. Metadata Lengkap
Setiap foto absensi menyimpan metadata berikut:
- **Koordinat GPS** (latitude, longitude)
- **Timestamp** (tanggal dan waktu pengambilan foto)
- **Status Kehadiran** (Tepat Waktu/Telat)
- **Informasi Perangkat** (model kamera/device)
- **Akurasi Lokasi**

### 3. Display Metadata
- **Table view** dengan indicator metadata
- **Modal popup** untuk melihat foto dengan metadata overlay
- **Interactive maps** untuk lokasi absensi
- **Status badges** untuk kehadiran

## Implementasi Teknis

### Database Schema
```sql
-- Tambahan field di tabel absensi
ALTER TABLE absensi ADD COLUMN metadata_foto_masuk JSON NULL;
ALTER TABLE absensi ADD COLUMN metadata_foto_keluar JSON NULL;
```

### File Structure
```
app/
├── Services/
│   └── PhotoMetadataService.php          # Service untuk processing metadata
├── Filament/Karyawan/Resources/
│   └── AbsensiResource.php               # Updated dengan camera interface
└── Models/
    └── Absensi.php                       # Updated dengan metadata fields

resources/
├── js/
│   └── camera-metadata.js                # JavaScript untuk camera capture
└── views/components/
    ├── photo-metadata.blade.php          # Component untuk display metadata
    └── photo-metadata-column.blade.php   # Table column component

database/migrations/
└── 2025_06_01_000004_add_photo_metadata_to_absensi_table.php

tests/Feature/
└── AttendancePhotoMetadataTest.php       # Comprehensive tests
```

## Cara Penggunaan

### 1. Untuk Karyawan
1. Buka halaman **Absensi** di panel karyawan
2. Klik **Create** untuk absensi baru
3. Izinkan akses **kamera** dan **lokasi** saat diminta
4. Klik **📷 Buka Kamera** untuk memulai
5. Posisikan wajah dan klik **📸 Ambil Foto**
6. Review foto dengan metadata di preview
7. Klik **✅ Gunakan Foto** jika sudah sesuai
8. Isi keterangan (opsional) dan submit

### 2. Untuk Supervisor/Admin
1. Buka **Absensi Dashboard** atau **HR Dashboard**
2. Lihat daftar absensi dengan indicator metadata (✓)
3. Klik foto untuk melihat detail metadata
4. Review lokasi di **Google Maps** atau **OpenStreetMap**
5. Approve/reject absensi sesuai kebijakan

## Metadata Format

### JSON Structure
```json
{
  "timestamp": "2024-01-20T08:00:00.000000Z",
  "datetime_display": "20/01/2024 08:00:00",
  "latitude": -6.200000,
  "longitude": 106.816666,
  "coordinates_display": "-6.200000°, 106.816666°",
  "status_kehadiran": "Tepat Waktu",
  "camera_info": "XIAOMI 13T",
  "user_agent": "Mozilla/5.0...",
  "processed_at": "2024-01-20T08:00:05.000000Z"
}
```

### Display Format
- **Koordinat**: `-6.200000°, 106.816666°`
- **Waktu**: `20/01/2024 08:00:00`
- **Status**: `Tepat Waktu` (hijau) / `Telat` (kuning)
- **Device**: `XIAOMI 13T`

## Security & Privacy

### 1. Data Protection
- Metadata disimpan dalam format **JSON encrypted**
- Akses foto dibatasi sesuai **role-based permissions**
- **Audit trail** untuk semua akses metadata

### 2. Location Privacy
- Koordinat GPS hanya digunakan untuk **verifikasi lokasi kerja**
- Data lokasi **tidak dibagikan** ke pihak ketiga
- Karyawan dapat melihat **lokasi mereka sendiri**

### 3. Photo Security
- Foto disimpan dengan **secure file naming**
- **Watermark metadata** langsung di foto
- **Backup otomatis** ke cloud storage

## Performance Optimization

### 1. Image Processing
- **Automatic compression** (max 1024x768, 80% quality)
- **Progressive JPEG** untuk loading cepat
- **Lazy loading** untuk table view

### 2. Database
- **JSON indexing** untuk metadata queries
- **Eager loading** untuk relasi
- **Database caching** untuk frequent queries

### 3. Frontend
- **Async camera loading**
- **Progressive enhancement**
- **Mobile-first responsive design**

## Browser Compatibility

### Supported Browsers
- ✅ **Chrome 80+** (Desktop & Mobile)
- ✅ **Firefox 75+** (Desktop & Mobile)
- ✅ **Safari 13+** (Desktop & Mobile)
- ✅ **Edge 80+** (Desktop)
- ⚠️ **Internet Explorer** (Not supported)

### Required Permissions
- 📷 **Camera Access** (Required)
- 📍 **Location Access** (Required)
- 🔊 **Microphone** (Optional, for video)

## Troubleshooting

### Common Issues

#### 1. Kamera Tidak Bisa Diakses
**Solusi:**
- Pastikan browser memiliki permission kamera
- Refresh halaman dan coba lagi
- Gunakan HTTPS (required untuk camera access)

#### 2. Lokasi Tidak Terdeteksi
**Solusi:**
- Enable location services di browser
- Pastikan GPS aktif di device
- Gunakan tombol "Jakarta" untuk fallback location

#### 3. Foto Tidak Tersimpan
**Solusi:**
- Check koneksi internet
- Pastikan storage space cukup
- Coba compress foto lebih kecil

#### 4. Metadata Tidak Muncul
**Solusi:**
- Clear browser cache
- Pastikan JavaScript enabled
- Update browser ke versi terbaru

## Testing

### Unit Tests
```bash
# Run all metadata tests
php artisan test tests/Feature/AttendancePhotoMetadataTest.php

# Run specific test
php artisan test --filter=photo_metadata_service_can_extract_metadata
```

### Manual Testing Checklist
- [ ] Camera interface loads correctly
- [ ] Geolocation permission requested
- [ ] Photo capture works on mobile
- [ ] Metadata overlay displays correctly
- [ ] Table view shows metadata indicator
- [ ] Modal popup works with all metadata
- [ ] Maps integration functional
- [ ] Status determination accurate

## Future Enhancements

### Planned Features
1. **Face Recognition** untuk verifikasi identitas
2. **QR Code Scanning** untuk lokasi verification
3. **Offline Mode** dengan sync ketika online
4. **Advanced Analytics** untuk attendance patterns
5. **Integration** dengan payroll system
6. **Mobile App** dengan native camera

### Performance Improvements
1. **WebP Image Format** untuk compression lebih baik
2. **Service Worker** untuk offline capability
3. **CDN Integration** untuk faster loading
4. **Real-time Notifications** untuk supervisors

## Support

Untuk bantuan teknis atau pertanyaan tentang fitur ini:
- 📧 Email: <EMAIL>
- 📱 WhatsApp: +62-xxx-xxx-xxxx
- 🌐 Documentation: [Internal Wiki]
- 🐛 Bug Reports: [GitHub Issues]
