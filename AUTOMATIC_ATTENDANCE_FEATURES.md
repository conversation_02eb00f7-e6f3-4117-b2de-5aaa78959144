# Automatic Attendance Features

This document describes the automatic attendance management features implemented in the system.

## Features Overview

### 1. Automatic Attendance Record Creation for Approved Leave

When a leave request (cuti/izin/sakit) is approved, the system automatically creates attendance records for each day in the leave period.

**How it works:**
- CutiIzinObserver listens for status changes on CutiIzin model
- When status changes to 'approved', it creates attendance records
- Status is set based on leave type: 'cuti', 'izin', or 'sakit'
- Existing attendance records are updated if they have generic status ('hadir', 'terlambat', 'alpha')
- Records with special status are skipped to avoid conflicts

**Benefits:**
- Eliminates manual attendance entry for approved leave
- Ensures consistent attendance tracking
- Maintains audit trail with approval information

### 2. Automatic Lateness Calculation and Status Updates

The system automatically determines attendance status based on schedule timing and tolerance settings.

**How it works:**
- AttendanceService calculates if attendance is late based on shift timing
- AbsensiObserver automatically sets status when attendance records are created/updated
- Supports both regular and split shifts
- Respects tolerance settings configured in shift

**Status determination logic:**
- `hadir`: On time or within tolerance
- `terlambat`: Beyond tolerance period
- Special statuses (`cuti`, `izin`, `sakit`) are preserved

### 3. Lateness Deduction Calculation

Automatic calculation of deductions based on lateness rules (AturanKeterlambatan).

**How it works:**
- AttendanceService calculates exact lateness minutes
- Finds applicable lateness rules based on minutes late
- Calculates deduction amount using rule configuration
- Supports different deduction types: fixed amount, per minute, percentage of salary

**Integration:**
- Works with existing PayrollService
- Maintains compatibility with current payroll calculations
- Provides detailed lateness data for reporting

### 4. Automatic Status Updates

Real-time status updates when attendance times are recorded.

**How it works:**
- AbsensiObserver monitors attendance record changes
- Automatically updates status when `waktu_masuk` is set
- Preserves special statuses (leave types)
- Logs all status changes for audit purposes

## Technical Implementation

### Services

#### AttendanceService
- `determineAttendanceStatus()`: Determines status based on timing
- `calculateLatenessMinutes()`: Calculates exact lateness
- `calculateLatenessDeduction()`: Calculates deduction with rules
- `updateAttendanceStatus()`: Updates status automatically
- `processAttendanceRecord()`: Complete processing pipeline

### Observers

#### CutiIzinObserver
- Monitors CutiIzin model for approval status changes
- Creates attendance records for approved leave periods
- Handles date ranges and existing record updates

#### AbsensiObserver
- Monitors Absensi model for creation and updates
- Automatically sets status based on timing
- Logs changes for audit trail

### Models

#### Enhanced Absensi Model
- New `lateness_minutes` attribute using AttendanceService
- Maintains existing `is_late` attribute for compatibility
- Automatic status determination on creation/update

#### Enhanced CutiIzin Model
- Existing `jenis_permohonan_label` attribute for display
- Observer integration for automatic processing

## Commands

### Process Attendance Status
```bash
php artisan attendance:process-status [options]
```

Options:
- `--date=YYYY-MM-DD`: Process specific date
- `--days=N`: Process last N days (default: 30)
- `--dry-run`: Preview changes without applying

### Process Approved Leave
```bash
php artisan attendance:process-approved-leave [options]
```

Options:
- `--date=YYYY-MM-DD`: Process specific date
- `--days=N`: Process last N days (default: 30)
- `--dry-run`: Preview changes without applying

## Configuration

### Shift Settings
- `toleransi_keterlambatan`: Minutes of tolerance before marking late
- Split shift support with per-period tolerance
- Flexible timing configuration

### Lateness Rules (AturanKeterlambatan)
- Range-based rules (from/to minutes)
- Multiple deduction types
- Active/inactive status
- Priority-based application

## Usage Examples

### Approving Leave Request
```php
$leave = CutiIzin::find(1);
$leave->update([
    'status' => 'approved',
    'approved_by' => auth()->id(),
    'approved_at' => now()
]);
// Attendance records automatically created
```

### Creating Attendance Record
```php
$attendance = Absensi::create([
    'karyawan_id' => 1,
    'jadwal_id' => 1,
    'tanggal_absensi' => today(),
    'waktu_masuk' => now(),
    // Status automatically determined
]);
```

### Calculating Lateness
```php
$latenessData = AttendanceService::calculateLatenessDeduction($attendance);
// Returns: minutes_late, deduction_amount, applicable_rule
```

## Testing

Comprehensive test suite covers:
- Automatic attendance creation for approved leave
- Status determination based on timing
- Lateness calculation accuracy
- Deduction calculation with rules
- Observer functionality
- Edge cases and error handling

Run tests:
```bash
php artisan test tests/Feature/AutomaticAttendanceTest.php
```

## Migration and Deployment

### Existing Data Processing
Use the provided commands to process existing data:

1. Process approved leave requests:
```bash
php artisan attendance:process-approved-leave --days=90 --dry-run
php artisan attendance:process-approved-leave --days=90
```

2. Update attendance statuses:
```bash
php artisan attendance:process-status --days=90 --dry-run
php artisan attendance:process-status --days=90
```

### Monitoring
- All automatic actions are logged for audit purposes
- Status changes are tracked with timestamps
- Error handling with detailed logging

## Benefits

1. **Reduced Manual Work**: Automatic attendance creation and status updates
2. **Consistency**: Standardized lateness calculation across the system
3. **Accuracy**: Precise timing-based status determination
4. **Integration**: Seamless integration with existing payroll system
5. **Audit Trail**: Complete logging of all automatic actions
6. **Flexibility**: Configurable rules and tolerance settings
