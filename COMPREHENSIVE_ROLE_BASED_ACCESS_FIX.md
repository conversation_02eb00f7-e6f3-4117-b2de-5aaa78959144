# Comprehensive Role-based Access Control Fix

Dokumentasi lengkap perbaikan sistem role-based access control di semua resource utama: AbsensiResource, JadwalKerjaResource, JadwalMasalResource, dan Cuti<PERSON>zinResource.

## 🎯 Tujuan Perbaikan

Memastikan konsistensi sistem role-based access control di seluruh aplikasi untuk:
1. **Unified Role System** - Konsisten menggunakan role field dan Shield roles
2. **Hierarchical Access** - Akses berdasarkan hierarki organisasi
3. **Security Compliance** - Setiap role hanya akses data sesuai level mereka
4. **Maintainability** - Code yang konsisten dan mudah dipahami

## 📋 Resources yang Diperbaiki

### 1. ✅ **AbsensiResource**
- Form karyawan selection
- Action permissions (approve)
- Bulk action permissions

### 2. ✅ **JadwalKerjaResource** 
- Access permissions (canAccess, canView, canCreate, etc.)
- Form karyawan selection
- Bulk action permissions (approve/unapprove)

### 3. ✅ **JadwalMasalResource**
- Query filter di getEloquentQuery()
- Form entitas_filter visibility
- Form karyawan selection
- Generate action permissions

### 4. ✅ **CutiIzinResource**
- Form karyawan selection
- Table filter karyawan selection
- Action permissions (approve/reject)

## 🔧 Pattern Perbaikan yang Konsisten

### 1. **Role Checking Pattern**

**Sebelum (Inkonsisten):**
```php
// Campuran Shield roles dan role field
if ($user->hasRole(['kepala_toko'])) {
    // logic
} elseif ($user->hasAnyRole(['supervisor'])) {
    // logic
} elseif ($user->role === 'supervisor') {
    // logic
}
```

**Sesudah (Konsisten):**
```php
if ($user->role === 'keptok') {
    // Keptok logic
} elseif ($user->role === 'supervisor') {
    // Supervisor logic  
} elseif ($user->role === 'manager') {
    // Manager logic
} elseif (in_array($user->role, ['admin']) || $user->hasAnyRole(['manager_hrd', 'super_admin'])) {
    // Admin logic
}
```

### 2. **Karyawan Selection Pattern**

```php
if ($user->role === 'keptok') {
    // Keptok hanya bisa pilih karyawan di entitas mereka
    if ($user->karyawan && $user->karyawan->id_entitas) {
        $query->where('id_entitas', $user->karyawan->id_entitas);
    } else {
        $query->whereRaw('1 = 0');
    }
} elseif ($user->role === 'supervisor') {
    // Supervisor hanya bisa pilih karyawan di divisi mereka
    if ($user->karyawan && $user->karyawan->id_divisi) {
        $query->where('id_divisi', $user->karyawan->id_divisi);
    } else {
        $query->whereRaw('1 = 0');
    }
} elseif ($user->role === 'manager') {
    // Manager hanya bisa pilih karyawan di departemen mereka
    if ($user->karyawan && $user->karyawan->id_departemen) {
        $query->where('id_departemen', $user->karyawan->id_departemen);
    } else {
        $query->whereRaw('1 = 0');
    }
}
```

### 3. **Action Permission Pattern**

```php
->visible(function ($record) {
    $user = Auth::user();
    $allowedRoles = ['admin', 'manager', 'supervisor', 'keptok'];
    $allowedShieldRoles = ['manager_hrd', 'super_admin'];
    
    return (in_array($user->role, $allowedRoles) || $user->hasAnyRole($allowedShieldRoles))
        && $record->status === 'pending'; // Additional condition if needed
})
```

## 📊 Role Hierarchy & Access Matrix

| Resource | Keptok | Supervisor | Manager | Admin/Manager HRD |
|----------|--------|------------|---------|-------------------|
| **AbsensiResource** | | | | |
| - Create | Entitas mereka | Divisi mereka | Departemen mereka | Global |
| - View | Entitas mereka | Divisi mereka | Departemen mereka | Global |
| - Approve | ✅ | ✅ | ✅ | ✅ |
| **JadwalKerjaResource** | | | | |
| - Create | Entitas mereka | Divisi mereka | Departemen mereka | Global |
| - View | Entitas mereka | Divisi mereka | Departemen mereka | Global |
| - Approve | ✅ | ✅ | ✅ | ✅ |
| **JadwalMasalResource** | | | | |
| - Create | Entitas mereka | Multi-entitas (divisi) | Multi-entitas (dept) | Global |
| - View | Entitas mereka | Multi-entitas (divisi) | Multi-entitas (dept) | Global |
| - Generate | ✅ | ✅ | ✅ | ✅ |
| **CutiIzinResource** | | | | |
| - Create | Entitas mereka (exclude self) | Divisi mereka (exclude self) | Departemen mereka (exclude self) | Global |
| - View | Entitas mereka | Divisi mereka | Departemen mereka | Global |
| - Approve | ✅ | ✅ | ✅ | ✅ |

## 🛡️ Security Features

### 1. **Error Handling**
```php
if ($user->karyawan && $user->karyawan->id_entitas) {
    // Normal filtering
} else {
    $query->whereRaw('1 = 0'); // Show nothing if invalid
}
```

### 2. **Self-exclusion (untuk Cuti/Izin)**
```php
// Exclude diri sendiri untuk mencegah conflict of interest
$query->where('id', '!=', $user->karyawan->id);
```

### 3. **Hierarchical Filtering**
- **Keptok**: Entitas level (single location)
- **Supervisor**: Divisi level (multiple locations possible)
- **Manager**: Departemen level (multiple divisions/locations)
- **Admin**: Global level (all data)

## 🔄 Konsistensi Implementasi

### 1. **Form Fields**
- ✅ Karyawan selection sesuai role
- ✅ Error handling untuk user tanpa karyawan record
- ✅ Consistent filtering logic

### 2. **Table Queries**
- ✅ getEloquentQuery() filtering sesuai role
- ✅ Table filter options sesuai role
- ✅ Consistent whereHas patterns

### 3. **Action Permissions**
- ✅ Approve/reject actions sesuai role
- ✅ Bulk actions sesuai role
- ✅ Generate actions sesuai role

### 4. **Access Control**
- ✅ canAccess() methods sesuai role
- ✅ canView(), canCreate(), canEdit(), canDelete() sesuai role
- ✅ Consistent permission checking

## 🧪 Testing Matrix

### Test Case 1: Keptok User
```php
// Login sebagai keptok
$keptok = User::where('role', 'keptok')->first();
Auth::login($keptok);

// Expected Results:
// - AbsensiResource: Hanya karyawan di entitas keptok
// - JadwalKerjaResource: Hanya karyawan di entitas keptok  
// - JadwalMasalResource: Hanya jadwal masal untuk entitas keptok
// - CutiIzinResource: Hanya karyawan di entitas keptok (exclude self)
```

### Test Case 2: Supervisor User
```php
// Login sebagai supervisor
$supervisor = User::where('role', 'supervisor')->first();
Auth::login($supervisor);

// Expected Results:
// - AbsensiResource: Hanya karyawan di divisi supervisor
// - JadwalKerjaResource: Hanya karyawan di divisi supervisor
// - JadwalMasalResource: Jadwal masal untuk entitas yang ada karyawan divisi supervisor
// - CutiIzinResource: Hanya karyawan di divisi supervisor (exclude self)
```

### Test Case 3: Manager User
```php
// Login sebagai manager
$manager = User::where('role', 'manager')->first();
Auth::login($manager);

// Expected Results:
// - AbsensiResource: Hanya karyawan di departemen manager
// - JadwalKerjaResource: Hanya karyawan di departemen manager
// - JadwalMasalResource: Jadwal masal untuk entitas yang ada karyawan departemen manager
// - CutiIzinResource: Hanya karyawan di departemen manager (exclude self)
```

### Test Case 4: Admin User
```php
// Login sebagai admin
$admin = User::where('role', 'admin')->first();
Auth::login($admin);

// Expected Results:
// - Semua resource: Akses penuh ke semua data
// - Tidak ada filtering berdasarkan hierarki
// - Semua action permissions tersedia
```

## ✅ Hasil Perbaikan

### 1. **✅ Konsistensi Role Checking**
- Unified pattern di semua resource
- Consistent use of role field vs Shield roles
- Predictable behavior across application

### 2. **✅ Hierarchical Access Control**
- Keptok: Entitas level access
- Supervisor: Divisi level access  
- Manager: Departemen level access
- Admin: Global access

### 3. **✅ Security Compliance**
- No unauthorized data access
- Proper error handling for edge cases
- Self-exclusion where appropriate

### 4. **✅ Maintainability**
- Consistent code patterns
- Easy to add new roles
- Clear documentation and comments

### 5. **✅ User Experience**
- Appropriate data filtering
- Relevant action availability
- Intuitive access levels

## 🎯 Benefits

1. **Security**: Role-based access control yang ketat dan konsisten
2. **Scalability**: Mudah menambah role baru dengan pattern yang sama
3. **Maintainability**: Code yang konsisten dan terdokumentasi dengan baik
4. **User Experience**: Interface yang sesuai dengan role dan tanggung jawab user
5. **Business Logic**: Sesuai dengan hierarki organisasi yang ada

Sistem role-based access control sekarang telah konsisten di seluruh aplikasi dan siap untuk production use! 🎉
