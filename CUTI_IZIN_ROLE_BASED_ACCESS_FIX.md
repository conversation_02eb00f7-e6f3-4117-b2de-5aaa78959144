# Perbaikan Role-based Access di CutiIzinResource

Dokumentasi perbaikan sistem akses berdasarkan role di CutiIzinResource untuk memastikan setiap role hanya dapat membuat dan mengelola cuti/izin sesuai level akses mereka.

## 🎯 Tujuan Perbaikan

Memastikan sistem cuti/izin memiliki kontrol akses yang tepat untuk:
1. **Karyawan Selection** - Setiap role hanya bisa pilih karyawan sesuai hierarki mereka
2. **Data Filtering** - Table filter sesuai dengan akses role
3. **Action Permissions** - Approve/reject hanya untuk role yang berwenang
4. **Konsistensi** - Logic yang sama dengan resource lain (JadwalMasal, Absensi)

## 📋 Masalah yang Diperbaiki

### 1. Inkonsistensi Role Checking
**Masalah Sebelumnya:**
- Campuran antara Shield roles (`hasRole`, `hasAnyRole`) dan role field
- Role `keptok` tidak dikenali karena menggunakan `kepala_toko`
- Logic yang tidak konsisten dengan resource lain

**Solusi:**
- Konsisten menggunakan `$user->role` untuk role baru (manager, keptok)
- Tetap menggunakan Shield roles untuk role lama yang sudah ada
- Logic yang sama dengan JadwalMasalResource

### 2. Karyawan Selection Tidak Sesuai Hierarki
**Masalah Sebelumnya:**
- Supervisor menggunakan `supervisor_id` field yang mungkin tidak terisi
- Manager tidak ada filtering
- Keptok tidak bisa create cuti/izin

**Solusi:**
- Role-based filtering berdasarkan hierarki organisasi
- Konsisten dengan struktur departemen → divisi → entitas

## 🔧 Perbaikan yang Dilakukan

### 1. Form - Karyawan Selection

**File:** `app/Filament/Resources/CutiIzinResource.php`

**Sebelum:**
```php
if ($user->hasRole(['kepala_toko'])) {
    $query->where('id_entitas', $user->karyawan->id_entitas);
} elseif ($user->hasAnyRole(['supervisor'])) {
    $employeeIds = Karyawan::where('supervisor_id', $user->id)->pluck('id')->toArray();
    $query->whereIn('id', $employeeIds);
}
```

**Sesudah:**
```php
if ($user->role === 'keptok') {
    // Keptok hanya bisa buat cuti/izin untuk karyawan di entitas mereka
    if ($user->karyawan && $user->karyawan->id_entitas) {
        $query->where('id_entitas', $user->karyawan->id_entitas);
        $query->where('id', '!=', $user->karyawan->id); // Exclude diri sendiri
    } else {
        $query->whereRaw('1 = 0');
    }
} elseif ($user->role === 'supervisor') {
    // Supervisor hanya bisa buat cuti/izin untuk karyawan di divisi mereka
    if ($user->karyawan && $user->karyawan->id_divisi) {
        $query->where('id_divisi', $user->karyawan->id_divisi);
        $query->where('id', '!=', $user->karyawan->id);
    } else {
        $query->whereRaw('1 = 0');
    }
} elseif ($user->role === 'manager') {
    // Manager hanya bisa buat cuti/izin untuk karyawan di departemen mereka
    if ($user->karyawan && $user->karyawan->id_departemen) {
        $query->where('id_departemen', $user->karyawan->id_departemen);
        $query->where('id', '!=', $user->karyawan->id);
    } else {
        $query->whereRaw('1 = 0');
    }
}
```

### 2. Table Filter - Karyawan Selection

**Perbaikan yang sama** diterapkan pada filter di table untuk konsistensi.

### 3. Action Permissions - Approve/Reject

**Sebelum:**
```php
->visible(fn($record) => (Auth::user()->hasAnyRole(['kepala_toko', 'manager_hrd'])) && $record->status === 'pending')
```

**Sesudah:**
```php
->visible(function ($record) {
    $user = Auth::user();
    $allowedRoles = ['admin', 'manager', 'supervisor', 'keptok'];
    $allowedShieldRoles = ['manager_hrd', 'super_admin'];
    
    return (in_array($user->role, $allowedRoles) || $user->hasAnyRole($allowedShieldRoles))
        && $record->status === 'pending';
})
```

## 📊 Level Akses per Role

### 🟣 **Keptok (Kepala Toko)**
- **Create Cuti/Izin**: Hanya untuk karyawan di entitas mereka
- **View**: Hanya cuti/izin karyawan di entitas mereka
- **Approve/Reject**: Bisa approve/reject cuti/izin di entitas mereka
- **Exclude**: Tidak bisa buat cuti/izin untuk diri sendiri

### 🟡 **Supervisor**
- **Create Cuti/Izin**: Hanya untuk karyawan di divisi mereka
- **View**: Hanya cuti/izin karyawan di divisi mereka
- **Approve/Reject**: Bisa approve/reject cuti/izin di divisi mereka
- **Exclude**: Tidak bisa buat cuti/izin untuk diri sendiri

### 🔵 **Manager**
- **Create Cuti/Izin**: Hanya untuk karyawan di departemen mereka
- **View**: Hanya cuti/izin karyawan di departemen mereka
- **Approve/Reject**: Bisa approve/reject cuti/izin di departemen mereka
- **Exclude**: Tidak bisa buat cuti/izin untuk diri sendiri

### 🔴 **Admin/Manager HRD**
- **Create Cuti/Izin**: Untuk semua karyawan
- **View**: Semua cuti/izin
- **Approve/Reject**: Bisa approve/reject semua cuti/izin
- **No Restrictions**: Akses penuh

## 🛡️ Error Handling

### 1. User Tanpa Karyawan Record
```php
if ($user->karyawan && $user->karyawan->id_entitas) {
    // Normal filtering
} else {
    $query->whereRaw('1 = 0'); // Show nothing
}
```

### 2. Self-exclusion
```php
// Exclude diri sendiri dari selection
$query->where('id', '!=', $user->karyawan->id);
```

### 3. Role Validation
```php
$allowedRoles = ['admin', 'manager', 'supervisor', 'keptok'];
$allowedShieldRoles = ['manager_hrd', 'super_admin'];

return (in_array($user->role, $allowedRoles) || $user->hasAnyRole($allowedShieldRoles));
```

## 🧪 Testing Scenarios

### Test Case 1: Keptok Create Cuti
```php
// Login sebagai keptok
$keptok = User::where('role', 'keptok')->first();
Auth::login($keptok);

// Akses form create cuti
// Expected: Hanya melihat karyawan dari entitas keptok (exclude diri sendiri)
```

### Test Case 2: Supervisor Create Cuti
```php
// Login sebagai supervisor
$supervisor = User::where('role', 'supervisor')->first();
Auth::login($supervisor);

// Akses form create cuti
// Expected: Hanya melihat karyawan dari divisi supervisor (exclude diri sendiri)
```

### Test Case 3: Manager Approve Cuti
```php
// Login sebagai manager
$manager = User::where('role', 'manager')->first();
Auth::login($manager);

// Akses table cuti/izin
// Expected: Hanya melihat cuti/izin dari departemen manager
// Expected: Bisa approve/reject cuti/izin yang pending
```

## 📋 Hierarchy Akses

```
ADMIN (Global)
├── MANAGER_HRD (Global via Shield)
├── MANAGER (Departemen level)
├── SUPERVISOR (Divisi level)
└── KEPTOK (Entitas level)
```

## 🔄 Konsistensi dengan Resource Lain

### JadwalMasalResource
- ✅ Logic role checking yang sama
- ✅ Hierarki akses yang konsisten
- ✅ Error handling yang sama

### AbsensiResource
- ✅ Filter berdasarkan role yang sama
- ✅ Permission checking yang konsisten

## ✅ Hasil Perbaikan

### 1. **✅ Karyawan Selection Sesuai Role**
- Keptok: Hanya entitas mereka
- Supervisor: Hanya divisi mereka
- Manager: Hanya departemen mereka
- Admin: Semua karyawan

### 2. **✅ Self-exclusion**
- Role non-admin tidak bisa buat cuti/izin untuk diri sendiri
- Mencegah conflict of interest

### 3. **✅ Action Permissions**
- Semua role management bisa approve/reject
- Konsisten dengan hierarki organisasi

### 4. **✅ Error Handling**
- Graceful handling untuk user tanpa karyawan record
- Fallback ke empty result jika data tidak valid

### 5. **✅ Konsistensi**
- Logic yang sama dengan resource lain
- Role checking yang konsisten
- Maintainable code

## 🎯 Manfaat

1. **Security**: Setiap role hanya akses data sesuai level mereka
2. **Business Logic**: Sesuai dengan hierarki organisasi
3. **User Experience**: Interface yang sesuai dengan role user
4. **Maintainability**: Code yang konsisten dan mudah dipahami
5. **Scalability**: Mudah menambah role baru dengan pattern yang sama

Sistem cuti/izin sekarang memiliki kontrol akses yang tepat dan konsisten dengan sistem role-based access control yang ada!
