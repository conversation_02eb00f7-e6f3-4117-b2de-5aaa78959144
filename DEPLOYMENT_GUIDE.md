# Deployment Guide - Enhanced Karyawan Panel Attendance System

## 🚀 Pre-Deployment Checklist

### 1. **Environment Verification**
```bash
# Verify PHP version (8.1+)
php --version

# Verify Node.js version (16+)
node --version

# Verify Composer
composer --version

# Verify NPM
npm --version
```

### 2. **Dependencies Check**
```bash
# Install PHP dependencies
composer install --optimize-autoloader --no-dev

# Install Node.js dependencies
npm install

# Build assets for production
npm run build
```

### 3. **Database Preparation**
```bash
# Run migrations
php artisan migrate

# Seed basic data (if needed)
php artisan db:seed

# Clear and cache configurations
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## 🔧 Configuration Steps

### 1. **Environment Configuration**
Update your `.env` file with production settings:

```env
# Application
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_PORT=3306
DB_DATABASE=your-database
DB_USERNAME=your-username
DB_PASSWORD=your-password

# File Storage
FILESYSTEM_DISK=public

# Session & Cache
SESSION_DRIVER=database
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis

# Localization (as per user preference)
APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US
```

### 2. **File Permissions**
```bash
# Set proper permissions
chmod -R 755 storage
chmod -R 755 bootstrap/cache
chmod -R 755 public/storage

# Create storage link
php artisan storage:link
```

### 3. **Asset Compilation**
```bash
# Build production assets
npm run build

# Verify assets are compiled
ls -la public/build/
```

## 📱 Feature Verification

### 1. **Geolocation System**
- [ ] GPS detection works on HTTPS
- [ ] Manual coordinate input available
- [ ] Error handling displays properly
- [ ] Location accuracy validation works
- [ ] Retry mechanism functions correctly

### 2. **Camera Upload System**
- [ ] Camera access permissions work
- [ ] Image compression functions
- [ ] File size validation works
- [ ] Multiple format support (JPEG, PNG, WebP)
- [ ] Preview and retake functionality

### 3. **Attendance Workflow**
- [ ] Check-in process completes
- [ ] Check-out process completes
- [ ] Geolocation data saves correctly
- [ ] Images upload and store properly
- [ ] Status calculation works (on-time/late)
- [ ] Duration calculation accurate

### 4. **Dashboard Widgets**
- [ ] Stats display correctly
- [ ] Recent attendance shows
- [ ] Data calculations accurate
- [ ] Real-time updates work
- [ ] Responsive design functions

## 🧪 Testing in Production

### 1. **Run Test Suite**
```bash
# Run all tests
php artisan test

# Run specific test groups
php artisan test --filter="KaryawanModelTest|AbsensiTest|AttendanceBusinessLogicTest|KaryawanWidgetsTest"

# Expected result: 26 tests, 82 assertions, all passing
```

### 2. **Manual Testing Checklist**

#### **Employee Authentication**
- [ ] Karyawan can login to `/karyawan` panel
- [ ] Non-karyawan users are redirected
- [ ] Session management works correctly

#### **Attendance Creation**
- [ ] Access `/karyawan/absensis/create`
- [ ] Geolocation detection activates
- [ ] Camera interface appears
- [ ] Form submission works
- [ ] Data saves to database

#### **Dashboard Functionality**
- [ ] Stats widgets display data
- [ ] Recent attendance table shows records
- [ ] Responsive design on mobile
- [ ] Icons and styling correct

## 🔒 Security Verification

### 1. **Access Control**
- [ ] Role-based access enforced
- [ ] Policies working correctly
- [ ] CSRF protection active
- [ ] File upload restrictions enforced

### 2. **Data Validation**
- [ ] Input sanitization working
- [ ] File type validation active
- [ ] Size limits enforced
- [ ] Coordinate validation working

## 📊 Performance Monitoring

### 1. **Asset Loading**
```bash
# Check asset sizes
ls -lh public/build/assets/

# Verify compression
gzip -l public/build/assets/*.js
gzip -l public/build/assets/*.css
```

### 2. **Database Performance**
```sql
-- Check attendance table indexes
SHOW INDEX FROM absensi;

-- Monitor query performance
EXPLAIN SELECT * FROM absensi WHERE karyawan_id = 1 ORDER BY tanggal_absensi DESC LIMIT 10;
```

## 🚨 Troubleshooting

### Common Issues & Solutions

#### **Geolocation Not Working**
- Ensure HTTPS is enabled
- Check browser permissions
- Verify JavaScript console for errors
- Test manual coordinate input

#### **Camera Access Denied**
- Check HTTPS requirement
- Verify browser permissions
- Test file upload fallback
- Check console for errors

#### **Assets Not Loading**
```bash
# Rebuild assets
npm run build

# Clear caches
php artisan cache:clear
php artisan view:clear
php artisan config:clear
```

#### **Database Connection Issues**
```bash
# Test database connection
php artisan tinker
>>> DB::connection()->getPdo();

# Check migrations
php artisan migrate:status
```

## 📈 Monitoring & Maintenance

### 1. **Log Monitoring**
```bash
# Monitor Laravel logs
tail -f storage/logs/laravel.log

# Monitor web server logs
tail -f /var/log/nginx/error.log
```

### 2. **Performance Metrics**
- Monitor page load times
- Track JavaScript errors
- Monitor database query performance
- Check storage usage for uploaded images

### 3. **Regular Maintenance**
```bash
# Weekly tasks
php artisan queue:restart
php artisan cache:clear

# Monthly tasks
php artisan storage:link
composer install --optimize-autoloader --no-dev
```

## 🎯 Success Criteria

### Deployment is successful when:
- [ ] All 26 tests pass
- [ ] Geolocation works on production domain
- [ ] Camera upload functions correctly
- [ ] Attendance workflow completes end-to-end
- [ ] Dashboard displays real data
- [ ] Mobile responsiveness verified
- [ ] Performance meets requirements
- [ ] Security measures active

## 📞 Support & Documentation

### Resources
- **Implementation Documentation**: `ATTENDANCE_SYSTEM_IMPLEMENTATION.md`
- **Test Coverage**: 26 tests, 82 assertions
- **Code Location**: 
  - Frontend: `resources/js/geolocation.js`, `resources/js/camera-upload.js`
  - Backend: `app/Filament/Karyawan/Resources/AbsensiResource.php`
  - Models: `app/Models/Absensi.php`, `app/Models/Karyawan.php`
  - Tests: `tests/Unit/` directory

### Emergency Rollback
If issues occur, rollback steps:
1. Revert to previous asset build
2. Restore database backup
3. Clear all caches
4. Restart web server

---

**Deployment completed successfully when all checklist items are verified and the system functions as expected in the production environment.**
