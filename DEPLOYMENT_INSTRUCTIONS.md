# 🚀 Deployment Instructions for Hostinger Server

## ✅ **Migration Order Fixed**

Masalah foreign key constraint sudah diperbaiki dengan mengubah urutan migration:

### 📋 **Urutan Migration yang Benar:**

1. **Basic Tables** (2014-2019): users, password_reset_tokens, failed_jobs, personal_access_tokens
2. **Master Data Tables** (2025-02-20): data_master, imports, exports, failed_import_rows
3. **Core Tables** (2025-05-06 onwards): karyawan, pelanggarans, dokumens, dll
4. **New Master Data** (2025-06-01-020 onwards):
   - `2025_06_01_000020_create_jenis_pelanggaran_table.php`
   - `2025_06_01_000021_create_aturan_keterlambatan_table.php` 
   - `2025_06_01_000022_create_payroll_periods_table.php`
5. **Payroll System** (2025-06-02 onwards):
   - `2025_06_02_000001_create_payroll_transactions_table.php`
   - `2025_06_02_000002_create_payroll_deductions_table.php`
   - `2025_06_02_000003_update_pelanggarans_table_add_jenis_id.php`

## 🔧 **Commands to Run on Server:**

```bash
# 1. Fresh migration with seeding
php artisan migrate:fresh --seed

# OR if you want to be more careful:

# 2. Step by step approach
php artisan migrate:fresh
php artisan db:seed
```

## 📊 **Expected Results:**

Setelah migration dan seeding berhasil, Anda akan mendapatkan:

- ✅ **28,944+ records** total
- ✅ **325 users** (admin, supervisor, karyawan)
- ✅ **300 karyawan** dengan data lengkap
- ✅ **8 jenis pelanggaran** dengan aturan denda
- ✅ **4 aturan keterlambatan** bertingkat
- ✅ **7,105 jadwal kerja** (30 hari terakhir)
- ✅ **6,867 absensi** dengan geolocation
- ✅ **467 pelanggaran** dengan jenis yang beragam
- ✅ **600 payroll transactions** dengan perhitungan denda otomatis
- ✅ **3 payroll periods** (Jan, Feb, Mar 2025)

## 🎯 **Features Ready for Testing:**

1. **Attendance System** dengan geolocation tracking
2. **Violation System** dengan master data pelanggaran
3. **Lateness Penalties** dengan aturan bertingkat
4. **Payroll System** dengan automatic deductions
5. **Complete Employee Relations** (kontrak, gaji, pendidikan, dll)

## 🔐 **Default Login Credentials:**

- **Admin**: <EMAIL> / password
- **Supervisor**: <EMAIL> / password  
- **Employee**: <EMAIL> / password

## ⚠️ **Troubleshooting:**

Jika masih ada error foreign key:
1. Pastikan semua migration files sudah ter-upload dengan benar
2. Cek urutan timestamp migration
3. Jalankan `php artisan migrate:status` untuk melihat status migration

## 📝 **Notes:**

- Migration order sudah diperbaiki untuk menghindari foreign key constraint errors
- Semua seeder yang tidak diperlukan sudah dihapus
- Database akan di-truncate dan di-seed ulang dengan data fresh dan komprehensif
