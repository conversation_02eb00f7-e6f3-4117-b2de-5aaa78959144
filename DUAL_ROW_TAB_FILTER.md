# 📋 Dual Row Tab Filter - Karyawan Resource

## ✅ **Layout Dua Baris Tab Filter:**

### **BARIS 1: Tab Filter U<PERSON>a (Filament Native)**
```
[Semua] [Aktif] [Non-Aktif] [👔 Tetap] [📝 Kontrak] [🔄 Probation] [💼 Freelance]
```

### **BARIS 2: Filter Departemen (Custom Header)**
```
🏢 Filter Departemen
[🏢 Semua] [🏢 IT] [🏢 HR] [🏢 Finance] [🏢 Marketing] [🏢 Operations]
```

## 🎯 **Struktur Layout:**

### **Baris 1 - Tab Filter (Native Filament):**
- **Status**: Semua, Aktif, Non-Aktif
- **Kontrak**: Tetap, Kontrak, Probation, Freelance
- **Behavior**: Native Filament tabs dengan query modification
- **Position**: Di atas tabel (standard Filament position)

### **Baris 2 - Departemen Filter (Custom Header):**
- **Departemen**: Semua, IT, HR, Finance, dll
- **Behavior**: URL-based filter menggunakan tableFilters
- **Position**: Custom header di atas baris tab pertama
- **Style**: Card-based dengan visual yang konsisten

## 🔧 **Technical Implementation:**

### **Baris 1 - getTabs() Method:**
```php
public function getTabs(): array
{
    // Status + Kontrak tabs
    return [
        'semua' => Tab::make('Semua')->badge($total),
        'aktif' => Tab::make('Aktif')->badge($aktif),
        // ... kontrak tabs
    ];
}
```

### **Baris 2 - getHeader() Method:**
```php
public function getHeader(): ?\Illuminate\Contracts\View\View
{
    return view('filament.resources.karyawan-resource.pages.departemen-filter-header');
}
```

### **URL-based Filtering:**
```php
// Filter departemen menggunakan tableFilters parameter
$filterUrl = request()->fullUrlWithQuery([
    'tableFilters' => [
        'id_departemen' => ['value' => $dept->id]
    ]
]);
```

## 🎨 **Visual Design:**

### **Baris 1 (Native Tabs):**
- Standard Filament tab styling
- Badge dengan count
- Color coding per jenis
- Hover effects

### **Baris 2 (Custom Header):**
- Card container dengan shadow
- Button-style links
- Active state highlighting
- Consistent badge styling
- Responsive flex layout

### **Color Scheme:**
```css
/* Active State */
bg-primary-100 text-primary-800 ring-2 ring-primary-600/20

/* Departemen Active */
bg-blue-100 text-blue-800 ring-2 ring-blue-600/20

/* Inactive State */
bg-gray-50 text-gray-700 hover:bg-gray-100 ring-1 ring-gray-200
```

## 📱 **Responsive Behavior:**

### **Desktop:**
- Dua baris terpisah jelas
- Semua filter terlihat
- Optimal spacing

### **Tablet:**
- Baris kedua wrap secara natural
- Maintain visual hierarchy
- Touch-friendly buttons

### **Mobile:**
- Stack secara vertikal
- Scroll horizontal jika diperlukan
- Compact button sizes

## 🔄 **Filter Interaction:**

### **Baris 1 (Tab Filter):**
- **Single Selection**: Hanya satu tab aktif
- **Query Modification**: Langsung modify Eloquent query
- **State Management**: Handled by Filament
- **URL**: Clean URLs tanpa query parameters

### **Baris 2 (Departemen Filter):**
- **URL Parameters**: Menggunakan tableFilters query
- **State Persistence**: Maintained via URL
- **Integration**: Compatible dengan filter dropdown
- **Reset**: Klik "Semua" untuk clear filter

## 💡 **Benefits:**

### **Separation of Concerns:**
- ✅ **Logical Grouping**: Status/Kontrak vs Departemen
- ✅ **Visual Clarity**: Dua baris terpisah jelas
- ✅ **User Experience**: Intuitive navigation
- ✅ **Scalability**: Easy to add more filters

### **Technical Advantages:**
- ✅ **Native Integration**: Menggunakan Filament features
- ✅ **Performance**: Efficient query handling
- ✅ **Maintainability**: Clean code structure
- ✅ **Flexibility**: Easy customization

### **User Experience:**
- ✅ **Clear Hierarchy**: Primary vs secondary filters
- ✅ **Quick Access**: Fast filtering options
- ✅ **Visual Feedback**: Active states dan counts
- ✅ **Consistent Design**: Unified styling

## 🎯 **Use Cases:**

### **HR Manager:**
1. **Baris 1**: Filter by kontrak type (Tetap vs Kontrak)
2. **Baris 2**: Drill down by departemen
3. **Combined**: "Karyawan tetap di IT department"

### **Department Head:**
1. **Baris 2**: Quick access to own department
2. **Baris 1**: Check status distribution
3. **Analysis**: Department-specific insights

### **Admin:**
1. **Overview**: Use "Semua" on both rows
2. **Specific**: Combine filters for targeted views
3. **Reporting**: Export filtered data

## ✅ **Final Result:**

```
┌─────────────────────────────────────────────────────────────┐
│ 🏢 Filter Departemen                    Baris Filter Kedua │
│ [🏢 Semua] [🏢 IT] [🏢 HR] [🏢 Finance] [🏢 Marketing]     │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│ [Semua] [Aktif] [Non-Aktif] [👔 Tetap] [📝 Kontrak] [🔄 Probation] [💼 Freelance] │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                     TABEL KARYAWAN                         │
└─────────────────────────────────────────────────────────────┘
```

Sekarang tab filter sudah terpisah menjadi dua baris yang independen! 🎉
