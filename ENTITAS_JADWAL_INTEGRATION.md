# Integrasi Entitas dengan Jadwal Kerja dan Jadwal Masal

Dokumentasi ini menjelaskan implementasi sistem entitas pada jadwal kerja dan jadwal masal untuk mendukung absensi multi-lokasi.

## 🎯 Tujuan Implementasi

Sistem ini memungkinkan:
1. **Karyawan bekerja di berbagai entitas/toko** - Jadwal kerja dapat menentukan lokasi kerja spesifik
2. **Absensi berbasis lokasi jadwal** - Validasi geofencing menggunakan entitas dari jadwal, bukan hanya entitas karyawan
3. **Fleksibilitas penugasan** - Ka<PERSON>wan dapat dijadwalkan di entitas yang berbeda dari entitas utama mereka
4. **Tracking lokasi kerja yang akurat** - Setiap absensi terkait dengan lokasi kerja yang tepat

## 📋 Perubahan Database

### 1. Migration: `add_entitas_id_to_jadwal_tables.php`

**Kolom Baru:**
- `jadwal_kerja.entitas_id` - ID entitas untuk jadwal kerja individual
- `jadwal_masal.entitas_id` - ID entitas untuk jadwal masal

**Indexes Baru:**
- `idx_jadwal_kerja_entitas` - Index pada entitas_id di jadwal_kerja
- `idx_jadwal_masal_entitas` - Index pada entitas_id di jadwal_masal
- `idx_jadwal_entitas_tanggal` - Composite index untuk filter berdasarkan entitas dan tanggal
- `idx_jadwal_karyawan_entitas` - Composite index untuk filter berdasarkan karyawan dan entitas

**Foreign Keys:**
- `jadwal_kerja.entitas_id` → `entitas.id`
- `jadwal_masal.entitas_id` → `entitas.id`

### 2. Seeder: `JadwalEntitasSeeder`

**Fungsi:**
- Mengisi `entitas_id` pada jadwal yang sudah ada berdasarkan entitas karyawan
- Menangani jadwal orphan (karyawan tidak ditemukan)
- Memberikan fallback ke entitas pertama jika diperlukan

**Strategi Pengisian:**
1. **Jadwal Kerja**: Ambil dari `karyawan.id_entitas`
2. **Jadwal Masal**: Tentukan dari entitas mayoritas karyawan yang ditugaskan
3. **Fallback**: Gunakan entitas pertama yang tersedia

## 🔧 Perubahan Model

### Schedule Model
```php
// Tambahan fillable
'entitas_id'

// Relationship baru
public function entitas()
{
    return $this->belongsTo(Entitas::class, 'entitas_id');
}
```

### JadwalMasal Model
```php
// Tambahan fillable
'entitas_id'

// Relationship baru
public function entitas()
{
    return $this->belongsTo(Entitas::class, 'entitas_id');
}
```

### Absensi Model
```php
// Method baru untuk mendapatkan entitas
public function getEntitasAttribute()
{
    // Prioritas: jadwal->entitas, lalu karyawan->entitas
    if ($this->jadwal && $this->jadwal->entitas) {
        return $this->jadwal->entitas;
    }
    
    if ($this->karyawan && $this->karyawan->entitas) {
        return $this->karyawan->entitas;
    }
    
    return null;
}

public function getEntitasNameAttribute()
{
    return $this->entitas?->nama;
}

public function getEntitasIdAttribute()
{
    return $this->entitas?->id;
}
```

## 🚀 Command dan Tools

### 1. Migration Command
```bash
php artisan migrate
```

### 2. Update Entitas Command
```bash
# Lihat statistik saat ini
php artisan jadwal:update-entitas --dry-run

# Update data
php artisan jadwal:update-entitas

# Force update (jika sudah ada entitas_id)
php artisan jadwal:update-entitas --force
```

### 3. Seeder Command
```bash
php artisan db:seed --class=JadwalEntitasSeeder
```

## 📊 Logika Prioritas Entitas

### Untuk Absensi
1. **Prioritas 1**: `jadwal.entitas_id` (lokasi kerja spesifik untuk hari itu)
2. **Prioritas 2**: `karyawan.id_entitas` (entitas utama karyawan)
3. **Fallback**: `null` (tidak ada validasi lokasi)

### Untuk Geofencing
```php
// Sebelum
$entitas = $absensi->karyawan->entitas;

// Sesudah
$entitas = $absensi->entitas; // Menggunakan method baru yang prioritas jadwal
```

## 🔄 Update Sistem Existing

### 1. AttendanceService
- Load relationship `entitas` pada schedule
- Gunakan entitas dari jadwal untuk validasi

### 2. CutiIzinObserver
- Load relationship `entitas` saat mencari jadwal
- Tetap menggunakan logika existing untuk pembuatan attendance

### 3. AbsensiResource (Filament)
- Update geofencing status untuk menggunakan `$record->entitas`
- Update tooltip untuk menggunakan entitas dari jadwal

### 4. JadwalMasalResource (Filament)
- Tampilkan entitas dari `jadwal_masal.entitas_id` sebagai prioritas
- Fallback ke entitas dari karyawan yang ditugaskan

## 🧪 Update Unit Tests

Semua unit test diupdate untuk menyertakan `entitas_id` saat membuat schedule:

```php
Schedule::factory()->create([
    'karyawan_id' => $this->karyawan->id,
    'shift_id' => $this->shift->id,
    'entitas_id' => $this->karyawan->id_entitas, // Tambahan ini
    'tanggal_jadwal' => Carbon::today()
]);
```

## 📈 Manfaat Implementasi

### 1. Fleksibilitas Operasional
- Karyawan dapat dijadwalkan di berbagai lokasi
- Supervisor dapat menugaskan karyawan ke toko/entitas yang berbeda
- Mendukung rotasi karyawan antar lokasi

### 2. Akurasi Tracking
- Absensi terkait dengan lokasi kerja yang tepat
- Geofencing berdasarkan lokasi kerja aktual, bukan entitas utama karyawan
- Laporan absensi per lokasi lebih akurat

### 3. Skalabilitas
- Mudah menambah entitas baru
- Sistem dapat menangani karyawan multi-lokasi
- Persiapan untuk fitur advanced seperti transfer karyawan

### 4. Backward Compatibility
- Sistem existing tetap berfungsi
- Fallback ke entitas karyawan jika jadwal tidak memiliki entitas
- Tidak ada breaking changes pada API existing

## 🔍 Monitoring dan Maintenance

### 1. Statistik Entitas
```bash
# Lihat distribusi jadwal per entitas
php artisan jadwal:update-entitas --dry-run
```

### 2. Data Integrity
- Pastikan semua jadwal memiliki `entitas_id`
- Monitor jadwal orphan (tanpa karyawan)
- Validasi konsistensi entitas antara karyawan dan jadwal

### 3. Performance
- Index database sudah dioptimalkan
- Query menggunakan relationship yang efisien
- Caching dapat ditambahkan jika diperlukan

## 🚨 Troubleshooting

### 1. Jadwal Tanpa Entitas
```sql
-- Cek jadwal tanpa entitas
SELECT COUNT(*) FROM jadwal_kerja WHERE entitas_id IS NULL;
SELECT COUNT(*) FROM jadwal_masal WHERE entitas_id IS NULL;
```

### 2. Karyawan Tanpa Entitas
```sql
-- Cek karyawan tanpa entitas
SELECT COUNT(*) FROM karyawan WHERE id_entitas IS NULL AND status_aktif = 1;
```

### 3. Absensi dengan Geofencing Error
- Pastikan entitas memiliki koordinat latitude/longitude
- Pastikan radius entitas sudah diset
- Cek apakah geofencing diaktifkan untuk entitas

Implementasi ini memberikan fondasi yang kuat untuk sistem absensi multi-lokasi yang fleksibel dan akurat.
