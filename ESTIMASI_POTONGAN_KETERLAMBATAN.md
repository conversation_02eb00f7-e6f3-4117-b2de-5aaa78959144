# Estimasi Potongan Keterlambatan

Dokumentasi implementasi fitur estimasi potongan keterlambatan di resource absensi untuk panel admin dan karyawan.

## 🎯 Tujuan Implementasi

Fitur ini memungkinkan:
1. **Menampilkan estimasi potongan** berdasarkan aturan keterlambatan yang aktif
2. **Transparansi untuk karyawan** - karyawan dapat melihat estimasi potongan dari keterlambatan mereka
3. **Monitoring untuk admin** - admin dapat melihat total estimasi potongan dari semua karyawan
4. **Perhitungan otomatis** berdasarkan menit terlambat dan aturan yang berlaku

## 📋 Fitur yang Diimplementasikan

### 1. Kolom Estimasi Potongan di Resource Absensi

**Lokasi:**
- `app/Filament/Resources/AbsensiResource.php` (Panel Admin)
- `app/Filament/Karyawan/Resources/AbsensiResource.php` (Panel Karyawan)

**Fitur:**
- <PERSON><PERSON><PERSON> "Estimasi Potongan" setelah kolom "Menit Terlambat"
- Menampilkan estimasi dalam format mata uang (IDR)
- Badge dengan warna warning untuk potongan > 0, success untuk 0
- Hanya menghitung untuk status 'terlambat'
- Mendukung split shift dan regular shift

### 2. Logika Perhitungan

**Algoritma:**
1. Cek apakah absensi memiliki status 'terlambat'
2. Hitung menit terlambat berdasarkan waktu masuk vs jadwal shift
3. Cari aturan keterlambatan yang sesuai dengan menit terlambat
4. Hitung potongan berdasarkan jenis denda:
   - **Nominal Tetap**: Jumlah tetap
   - **Per Menit**: Menit terlambat × tarif per menit
   - **Persentase Gaji**: Persentase dari gaji pokok karyawan

**Prioritas Aturan:**
- Aturan dengan `menit_dari` tertinggi yang masih applicable
- Hanya aturan dengan `is_active = true`
- Mendukung range (menit_dari - menit_sampai) dan open-ended (menit_sampai = null)

### 3. Dukungan Split Shift

**Implementasi:**
- Deteksi periode shift berdasarkan waktu masuk
- Perhitungan toleransi per periode
- Fallback ke regular shift jika method split shift tidak tersedia

## 🔧 Struktur Kode

### Model AturanKeterlambatan

**Method Utama:**
```php
public function hitungDenda($menitTerlambat, $gajiPokok = 0)
```

**Jenis Denda:**
- `nominal_tetap`: Menggunakan field `denda_nominal`
- `per_menit`: Menggunakan field `denda_per_menit`
- `persentase_gaji`: Menggunakan field `persentase_denda`

**Scope Methods:**
- `scopeActive($query)`: Filter aturan aktif
- `scopeForLateness($query, $menitTerlambat)`: Cari aturan untuk menit tertentu

### Resource Absensi

**Kolom Menit Terlambat:**
```php
TextColumn::make('menit_terlambat')
    ->label('Menit Terlambat')
    ->getStateUsing(function ($record) {
        // Logika perhitungan menit terlambat
    })
    ->badge()
    ->color(fn($state) => $state > 0 ? 'danger' : 'success')
```

**Kolom Estimasi Potongan:**
```php
TextColumn::make('estimasi_potongan')
    ->label('Estimasi Potongan')
    ->getStateUsing(function ($record) {
        // Logika perhitungan estimasi potongan
    })
    ->money('IDR')
    ->badge()
    ->color(fn($state) => $state > 0 ? 'warning' : 'success')
```

## 🧪 Testing

### Unit Tests

**File:** `tests/Unit/AturanKeterlambatanTest.php`
- Test perhitungan nominal tetap
- Test perhitungan per menit
- Test perhitungan persentase gaji
- Test validasi range menit
- Test scope methods
- Test format display

**File:** `tests/Unit/EstimasiPotonganSimpleTest.php`
- Test perhitungan sederhana
- Test pencarian aturan yang tepat
- Test berbagai jenis denda

### Feature Tests

**File:** `tests/Feature/AbsensiEstimasiPotonganTest.php`
- Test integrasi dengan model Absensi
- Test dengan berbagai skenario keterlambatan
- Test dukungan split shift
- Test edge cases

## 📊 Contoh Penggunaan

### Skenario 1: Keterlambatan 20 Menit
- **Aturan**: 16-30 menit = Rp 50.000 (nominal tetap)
- **Hasil**: Estimasi potongan Rp 50.000

### Skenario 2: Keterlambatan 45 Menit
- **Aturan**: >30 menit = Rp 2.000 per menit
- **Hasil**: 45 × Rp 2.000 = Rp 90.000

### Skenario 3: Keterlambatan 60 Menit
- **Aturan**: >60 menit = 1% dari gaji pokok
- **Gaji**: Rp 5.000.000
- **Hasil**: 1% × Rp 5.000.000 = Rp 50.000

## 🔍 Monitoring dan Maintenance

### 1. Validasi Data
- Pastikan semua aturan keterlambatan memiliki `is_active = true/false`
- Cek tidak ada overlap range yang ambigu
- Validasi field gaji_pokok pada karyawan untuk perhitungan persentase

### 2. Performance
- Query menggunakan index pada `is_active`, `menit_dari`, `menit_sampai`
- Relationship loading yang efisien
- Caching dapat ditambahkan untuk aturan yang sering diakses

### 3. Error Handling
- Try-catch untuk parsing waktu
- Fallback ke 0 jika terjadi error
- Validasi keberadaan jadwal dan shift

## 🚨 Troubleshooting

### 1. Estimasi Potongan Tidak Muncul
- Cek status absensi harus 'terlambat'
- Pastikan ada aturan keterlambatan yang aktif
- Validasi perhitungan menit terlambat

### 2. Perhitungan Salah
- Cek toleransi keterlambatan di shift
- Validasi range aturan keterlambatan
- Pastikan gaji_pokok terisi untuk perhitungan persentase

### 3. Split Shift Issues
- Pastikan method `getCurrentPeriod` dan `getWorkPeriods` tersedia di model Shift
- Fallback ke regular shift jika split shift method tidak ada

## 📝 Catatan Implementasi

1. **Konsistensi**: Logika perhitungan sama antara panel admin dan karyawan
2. **Transparansi**: Karyawan dapat melihat estimasi potongan mereka sendiri
3. **Fleksibilitas**: Mendukung berbagai jenis aturan keterlambatan
4. **Performance**: Optimized query dengan proper indexing
5. **Error Handling**: Graceful fallback untuk edge cases

## 🔄 Future Enhancements

1. **Caching**: Cache aturan keterlambatan untuk performance
2. **Audit Trail**: Log perubahan aturan keterlambatan
3. **Bulk Operations**: Update estimasi potongan secara batch
4. **Reporting**: Dashboard untuk analisis keterlambatan dan potongan
5. **Notifications**: Notifikasi untuk karyawan tentang estimasi potongan
