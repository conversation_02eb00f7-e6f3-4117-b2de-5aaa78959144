# Dokumentasi Fitur SOP (Standard Operating Procedure)

## Overview

Fitur SOP memungkinkan supervisor dan admin untuk mengupload dokumen SOP dalam format PDF yang dapat diakses oleh karyawan berdasarkan departemen atau divisi mereka.

## Fitur Utama

### 1. **Upload SOP per Departemen/Divisi**

-   Admin/Supervisor dapat mengupload dokumen SOP dalam format PDF
-   SOP dapat ditargetkan untuk:
    -   **Departemen**: Berlaku untuk seluruh karyawan di departemen tertentu
    -   **Divisi**: Berlaku untuk karyawan di divisi tertentu
-   Validasi file: Hanya menerima format PDF dengan maksimal 10MB

### 2. **Manajemen SOP**

-   **Status**: Aktif/Tidak Aktif
-   **Versi**: Tracking versi dokumen SOP
-   **Periode Berlaku**: Tanggal mulai dan berakhir (opsional)
-   **Deskripsi**: Penjelasan singkat tentang SOP

### 3. **Aks<PERSON>wan**

-   <PERSON><PERSON>wan hanya dapat melihat SOP yang relevan dengan departemen/divisi mereka
-   Interface read-only (tidak dapat edit/hapus)
-   Fitur download dan view PDF
-   Widget dashboard menampilkan SOP terbaru

## Struktur Database

### Tabel: `sop_dokumens`

```sql
- id (Primary Key)
- judul_sop (String)
- deskripsi (Text, nullable)
- file_path (String)
- scope_type (Enum: 'departemen', 'divisi')
- departemen_id (Foreign Key, nullable)
- divisi_id (Foreign Key, nullable)
- status (Enum: 'aktif', 'tidak_aktif')
- tanggal_berlaku (Date, nullable)
- tanggal_berakhir (Date, nullable)
- versi (String, default: '1.0')
- created_by (Foreign Key to users)
- timestamps
- soft_deletes
```

## File yang Dibuat/Dimodifikasi

### 1. **Database & Models**

-   `database/migrations/2025_01_20_000001_create_sop_dokumens_table.php`
-   `app/Models/SopDokumen.php`
-   `app/Models/Departemen.php` (ditambah relasi)
-   `app/Models/Divisi.php` (ditambah relasi)
-   `database/seeders/SopDokumenSeeder.php`

### 2. **Admin Panel Resources**

-   `app/Filament/Resources/SopDokumenResource.php`
-   `app/Filament/Resources/SopDokumenResource/Pages/ListSopDokumens.php`
-   `app/Filament/Resources/SopDokumenResource/Pages/CreateSopDokumen.php`
-   `app/Filament/Resources/SopDokumenResource/Pages/ViewSopDokumen.php`
-   `app/Filament/Resources/SopDokumenResource/Pages/EditSopDokumen.php`

### 3. **Panel Karyawan Resources**

-   `app/Filament/Karyawan/Resources/SopResource.php`
-   `app/Filament/Karyawan/Resources/SopResource/Pages/ListSops.php`

### 4. **Views & Widgets**

-   `resources/views/filament/karyawan/sop-detail.blade.php`
-   `app/Filament/Widgets/SopOverview.php` (Admin Dashboard)
-   `app/Filament/Karyawan/Widgets/SopWidget.php` (Karyawan Dashboard)
-   `resources/views/filament/karyawan/widgets/sop-widget.blade.php`

### 5. **Panel Providers (Updated)**

-   `app/Providers/Filament/AdminPanelProvider.php`
-   `app/Providers/Filament/KaryawanPanelProvider.php`

## Cara Penggunaan

### Untuk Admin/Supervisor:

1. **Mengakses Menu SOP**

    - Login ke admin panel
    - Navigasi ke "Dokumen & SOP" → "SOP Dokumen"

2. **Membuat SOP Baru**

    - Klik "Tambah SOP"
    - Isi informasi SOP:
        - Judul SOP
        - Deskripsi (opsional)
        - Pilih scope: Departemen atau Divisi
        - Pilih departemen/divisi target
        - Upload file PDF
        - Set status, versi, dan periode berlaku
    - Simpan

3. **Mengelola SOP**
    - View, Edit, Delete SOP
    - Filter berdasarkan scope, status, departemen
    - Download file PDF
    - Tracking soft delete

### Untuk Karyawan:

1. **Melihat SOP di Dashboard**

    - Login ke panel karyawan
    - Widget "SOP Terbaru" menampilkan 5 SOP terbaru yang relevan

2. **Mengakses Semua SOP**

    - Navigasi ke "Dokumen" → "SOP"
    - Melihat semua SOP yang berlaku untuk departemen/divisi mereka
    - Filter berdasarkan tab: Semua, Berlaku, Departemen, Divisi

3. **Melihat Detail SOP**
    - Klik "View" untuk melihat detail lengkap
    - Download atau view PDF langsung di browser

## Fitur Keamanan & Akses

### 1. **Role-Based Access**

-   **Admin/Supervisor**: Full access (CRUD SOP)
-   **Karyawan**: Read-only access untuk SOP yang relevan

### 2. **Data Filtering**

-   Karyawan hanya melihat SOP untuk departemen/divisi mereka
-   SOP tidak aktif atau expired tidak ditampilkan ke karyawan
-   Automatic filtering berdasarkan relasi karyawan

### 3. **File Security**

-   File disimpan di `storage/app/public/sop_dokumens/`
-   Validasi format PDF dan ukuran maksimal
-   Symbolic link untuk akses public

## Widget Dashboard

### Admin Dashboard:

-   **SopOverview**: Statistik total SOP, aktif, berlaku, per departemen/divisi

### Karyawan Dashboard:

-   **SopWidget**: Menampilkan 5 SOP terbaru yang relevan dengan quick actions

## Optimisasi Performa

### 1. **Database Indexes**

-   Index pada `scope_type` + `departemen_id`
-   Index pada `scope_type` + `divisi_id`
-   Index pada `status` + `tanggal_berlaku`

### 2. **Eager Loading**

-   Load relasi departemen dan divisi untuk menghindari N+1 queries
-   Optimized queries dengan scopes

### 3. **Caching**

-   File PDF di-cache oleh browser
-   Symbolic link untuk akses langsung

## Testing Data

-   Seeder membuat 6 contoh SOP dengan berbagai skenario
-   File PDF dummy untuk testing
-   Data mencakup SOP aktif, tidak aktif, dengan periode terbatas

## Maintenance

### 1. **File Management**

-   Pastikan symbolic link `storage:link` aktif
-   Monitor ukuran direktori `storage/app/public/sop_dokumens/`
-   Backup file PDF secara berkala

### 2. **Database Maintenance**

-   Soft delete memungkinkan recovery data
-   Cleanup file orphan jika diperlukan
-   Monitor performa query dengan indexes

## Update & Perbaikan

### 1. **Relasi Model yang Diperbaiki**

-   Menambahkan relasi `departemen()` di model `Karyawan`
-   Menambahkan relasi `karyawan()` di model `Departemen` dan `Divisi`
-   Update trait `HasOptimizedQueries` untuk eager loading departemen
-   Perbaikan query di `SopResource` dan `SopWidget` dengan eager loading

### 2. **Perbaikan Deprecated Components**

-   Mengganti `BadgeColumn` dengan `TextColumn::badge()` di semua resource
-   Update color mapping untuk compatibility dengan Filament terbaru

### 3. **Optimisasi Query**

-   Eager loading relasi departemen dan divisi di semua query karyawan
-   Menghindari N+1 query problem dengan proper relationship loading

### 4. **Perbaikan Filtering SOP untuk Karyawan**

-   Menambahkan scope `forKaryawan()` di model `SopDokumen` untuk filtering yang ketat
-   Karyawan hanya melihat SOP yang benar-benar di-assign untuk departemen/divisi mereka
-   Karyawan tanpa departemen/divisi tidak melihat SOP apapun
-   Perbaikan logika `orWhere` untuk mencegah kebocoran data
-   Simplifikasi kode dengan menggunakan scope yang reusable

## Troubleshooting

### 1. **File Upload Issues**

-   Cek permission direktori storage
-   Pastikan symbolic link aktif: `php artisan storage:link`
-   Validasi ukuran file dan format

### 2. **Access Issues**

-   Pastikan karyawan memiliki departemen/divisi
-   Cek status SOP (aktif/tidak aktif)
-   Validasi periode berlaku SOP
-   Pastikan relasi model sudah benar

### 3. **Performance Issues**

-   Monitor query performance
-   Pastikan indexes aktif
-   Optimize eager loading jika diperlukan
-   Gunakan `withBasicRelations()` scope untuk query karyawan

### 4. **Relasi Issues**

-   Pastikan model `Karyawan` memiliki relasi `departemen()`
-   Cek foreign key `id_departemen` dan `id_divisi` di tabel karyawan
-   Validasi data seeder sudah dijalankan dengan benar

### 5. **Filtering Issues**

-   Pastikan karyawan memiliki data departemen atau divisi yang valid
-   Cek apakah SOP sudah di-assign ke departemen/divisi yang benar
-   Validasi scope `forKaryawan()` berfungsi dengan benar
-   Karyawan tanpa departemen/divisi tidak akan melihat SOP apapun (by design)

## Logika Filtering SOP

### **Aturan Filtering:**

1. **Karyawan dengan departemen saja**: Hanya melihat SOP dengan `scope_type='departemen'` dan `departemen_id` sesuai
2. **Karyawan dengan divisi saja**: Hanya melihat SOP dengan `scope_type='divisi'` dan `divisi_id` sesuai
3. **Karyawan dengan departemen dan divisi**: Melihat SOP untuk departemen ATAU divisi mereka
4. **Karyawan tanpa departemen/divisi**: Tidak melihat SOP apapun
5. **Karyawan tidak ada**: Tidak melihat SOP apapun

### **Contoh Query:**

```sql
-- Karyawan dengan departemen ID=1 dan divisi ID=2
SELECT * FROM sop_dokumens
WHERE status = 'aktif'
AND ((scope_type = 'departemen' AND departemen_id = 1)
     OR (scope_type = 'divisi' AND divisi_id = 2))
```
