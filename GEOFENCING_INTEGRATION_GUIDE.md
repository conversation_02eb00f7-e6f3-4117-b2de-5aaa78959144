# 🔗 Panduan Integrasi Geofencing dengan Sistem Absensi

## ✅ **Status Implementasi:**

### **Backend (Completed):**
- ✅ Database schema dengan field geolocation
- ✅ Model Entitas dengan method geofencing
- ✅ GeofencingService untuk validasi lokasi
- ✅ Model Absensi terintegrasi dengan geofencing
- ✅ Admin interface untuk setup dan monitoring

### **Frontend Integration (Next Steps):**
- 🔄 Integrasi dengan form absensi mobile/web
- 🔄 Real-time GPS validation
- 🔄 User feedback untuk lokasi invalid

## 🎯 **Cara Menggunakan Geofencing:**

### **1. Setup Entitas (Admin):**

1. **Login sebagai Admin**
2. **Masuk ke Data Master → Entitas**
3. **Edit entitas** yang ingin diatur geofencing
4. **Aktifkan toggle "Aktifkan Geofencing"**
5. **Input koordinat lokasi:**
   - **Latitude**: Contoh `-6.2088` (Jakarta)
   - **Longitude**: Contoh `106.8456` (Jakarta)
   - **Radius**: Contoh `100` meter
6. **Save pengaturan**

### **2. Mendapatkan Koordinat Lokasi:**

**Cara 1: Google Maps**
1. Buka Google Maps
2. Klik kanan pada lokasi kantor
3. Pilih koordinat yang muncul
4. Copy latitude dan longitude

**Cara 2: GPS Phone**
1. Buka aplikasi GPS/Maps di HP
2. Pergi ke lokasi kantor
3. Catat koordinat yang ditampilkan

**Cara 3: Online Tools**
- Gunakan latlong.net
- Input alamat untuk mendapatkan koordinat

### **3. Testing Geofencing:**

```bash
# Jalankan test script
php test-geofencing.php
```

**Output yang diharapkan:**
- ✅ Database schema OK
- ✅ Distance calculation working
- ✅ Sample entitas created
- ✅ Validation logic working
- ✅ Karyawan-entitas relations OK

## 🔧 **Integrasi dengan Form Absensi:**

### **JavaScript untuk Web/Mobile:**

```javascript
// Fungsi untuk mendapatkan lokasi user
function getCurrentLocation() {
    return new Promise((resolve, reject) => {
        if (!navigator.geolocation) {
            reject('Geolocation tidak didukung browser');
            return;
        }

        navigator.geolocation.getCurrentPosition(
            (position) => {
                resolve({
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                    accuracy: position.coords.accuracy
                });
            },
            (error) => {
                reject('Error mendapatkan lokasi: ' + error.message);
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    });
}

// Validasi lokasi sebelum submit absensi
async function validateLocationBeforeAttendance(karyawanId) {
    try {
        const location = await getCurrentLocation();
        
        const response = await fetch('/api/validate-geofencing', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                karyawan_id: karyawanId,
                latitude: location.latitude,
                longitude: location.longitude
            })
        });
        
        const result = await response.json();
        
        if (!result.allowed) {
            alert('Absensi tidak diizinkan: ' + result.message);
            return false;
        }
        
        return {
            allowed: true,
            latitude: location.latitude,
            longitude: location.longitude
        };
        
    } catch (error) {
        alert('Error validasi lokasi: ' + error);
        return false;
    }
}

// Contoh penggunaan di form absensi
document.getElementById('btn-absen').addEventListener('click', async function() {
    const karyawanId = this.dataset.karyawanId;
    
    const validation = await validateLocationBeforeAttendance(karyawanId);
    
    if (validation && validation.allowed) {
        // Lanjutkan proses absensi
        submitAttendance({
            karyawan_id: karyawanId,
            latitude: validation.latitude,
            longitude: validation.longitude,
            // ... data lainnya
        });
    }
});
```

### **API Endpoint untuk Validasi:**

```php
// routes/api.php
Route::post('/validate-geofencing', function (Request $request) {
    $request->validate([
        'karyawan_id' => 'required|exists:karyawan,id',
        'latitude' => 'required|numeric|between:-90,90',
        'longitude' => 'required|numeric|between:-180,180',
    ]);

    $validation = \App\Models\Absensi::validateGeofencing(
        $request->karyawan_id,
        $request->latitude,
        $request->longitude
    );

    return response()->json($validation);
});
```

### **Update Form Absensi Existing:**

```php
// Di CreateAbsensi page atau form absensi
public function beforeCreate(): void
{
    // Validasi geofencing sebelum create
    if ($this->data['latitude'] && $this->data['longitude']) {
        $validation = \App\Models\Absensi::validateGeofencing(
            $this->data['karyawan_id'],
            $this->data['latitude'],
            $this->data['longitude']
        );

        if (!$validation['allowed']) {
            throw new \Exception($validation['message']);
        }
    }
}
```

## 📱 **Mobile Integration:**

### **React Native / Flutter:**

```javascript
// React Native example
import Geolocation from '@react-native-community/geolocation';

const validateLocation = async (karyawanId) => {
    return new Promise((resolve, reject) => {
        Geolocation.getCurrentPosition(
            async (position) => {
                const { latitude, longitude } = position.coords;
                
                try {
                    const response = await fetch('/api/validate-geofencing', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            karyawan_id: karyawanId,
                            latitude,
                            longitude
                        })
                    });
                    
                    const result = await response.json();
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            },
            (error) => reject(error),
            { enableHighAccuracy: true, timeout: 15000 }
        );
    });
};
```

## 🎨 **UI/UX Recommendations:**

### **Loading States:**
```html
<div id="location-status">
    <div class="loading">📍 Mendapatkan lokasi...</div>
    <div class="success">✅ Lokasi valid - dalam radius</div>
    <div class="error">❌ Di luar area kerja</div>
</div>
```

### **Error Messages:**
- **GPS Off**: "Mohon aktifkan GPS untuk melanjutkan absensi"
- **Outside Radius**: "Anda berada 150m dari lokasi kerja (max: 100m)"
- **No Permission**: "Izin akses lokasi diperlukan untuk absensi"

### **Success Feedback:**
- **Valid Location**: "✅ Lokasi valid - Anda dapat melakukan absensi"
- **Distance Info**: "📍 Jarak dari kantor: 45m"

## 🔍 **Monitoring & Analytics:**

### **Admin Dashboard Metrics:**
- **Absensi Valid**: Jumlah absensi dalam radius
- **Absensi Invalid**: Jumlah percobaan di luar radius
- **Average Distance**: Rata-rata jarak absensi dari kantor
- **Geofencing Violations**: Pelanggaran per karyawan

### **Reports:**
- **Daily Geofencing Report**: Status validasi per hari
- **Employee Location Patterns**: Pola lokasi absensi karyawan
- **Radius Optimization**: Analisis untuk optimasi radius

## ⚙️ **Configuration Options:**

### **Per Entitas Settings:**
```php
// Flexible radius per shift/time
$entitas->radius_normal = 100;      // Normal hours
$entitas->radius_overtime = 200;    // Overtime hours
$entitas->radius_weekend = 150;     // Weekend

// Grace period for GPS accuracy
$entitas->gps_tolerance = 10;       // Additional 10m tolerance
```

### **System-wide Settings:**
```php
// config/geofencing.php
return [
    'enabled' => env('GEOFENCING_ENABLED', true),
    'default_radius' => 100,
    'max_radius' => 1000,
    'min_radius' => 10,
    'gps_timeout' => 15000,
    'accuracy_threshold' => 50, // meters
];
```

## 🚀 **Deployment Checklist:**

### **Pre-deployment:**
- [ ] Test geofencing dengan berbagai lokasi
- [ ] Setup koordinat untuk semua entitas
- [ ] Test dengan device mobile real
- [ ] Verify GPS accuracy di lokasi kantor

### **Post-deployment:**
- [ ] Monitor geofencing violations
- [ ] Collect user feedback
- [ ] Adjust radius berdasarkan real usage
- [ ] Setup alerts untuk anomali lokasi

## 📊 **Success Metrics:**

- **Accuracy**: 95%+ absensi valid dalam radius
- **User Experience**: <5 detik untuk validasi lokasi
- **Compliance**: 90%+ karyawan absen dari lokasi kerja
- **Error Rate**: <1% false positives/negatives

## 🎉 **Implementation Complete!**

Geofencing system siap digunakan dengan:
- ✅ **Backend validation** fully implemented
- ✅ **Admin interface** untuk setup dan monitoring
- ✅ **Database schema** optimized untuk geolocation
- ✅ **Service layer** untuk business logic
- 🔄 **Frontend integration** ready untuk implementasi

Next: Integrate dengan form absensi mobile/web! 🚀
