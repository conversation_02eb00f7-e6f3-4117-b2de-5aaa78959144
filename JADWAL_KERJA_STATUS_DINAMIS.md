# Status Dinamis Jadwal Kerja

## 🎯 Tujuan Implementasi

Mengimplementasikan sistem status dinamis pada jadwal kerja yang secara otomatis menentukan status berdasarkan:

1. **Data absensi** - <PERSON><PERSON> karyawan sudah absen
2. **Data cuti/izin/sakit** - <PERSON><PERSON> ada permohonan yang approved
3. **Kondisi waktu** - Berdasarkan waktu saat ini vs jadwal kerja

## 📋 Logika Status Dinamis

### 1. **Prioritas Status (Berurutan)**

```php
// 1. Cek Cuti/Izin/Sakit (Prioritas Tertinggi)
if ($cutiIzin && $cutiIzin->status === 'approved') {
    return ucfirst($cutiIzin->jenis_permohonan); // "Cuti", "Izin", "Sakit"
}

// 2. Cek Data Absensi
if ($absensi) {
    if ($absensi->waktu_masuk && $absensi->waktu_keluar) {
        return $absensi->is_late ? 'Hadir Terlambat' : 'Hadir <PERSON>pat W<PERSON>tu';
    } elseif ($absensi->waktu_masuk) {
        return $absensi->is_late ? 'Hadir Terlambat (Belum Keluar)' : 'Hadir Tepat Waktu (Belum Keluar)';
    }
}

// 3. Cek Kondisi Waktu (Belum Ada Absensi)
$now = Carbon::now();
$waktuMasuk = Carbon::parse($jadwal->tanggal_jadwal . ' ' . $jadwal->waktu_masuk);

if ($now->lessThan($waktuMasuk)) {
    return 'Belum Absen'; // Jadwal belum tiba
}

$batasAlfa = $waktuMasuk->copy()->addHours(2);
if ($now->greaterThan($batasAlfa)) {
    return 'Alfa'; // Sudah lewat 2 jam dari jadwal
}

return 'Belum Absen'; // Dalam rentang waktu kerja
```

### 2. **Status yang Mungkin Muncul**

| Status                               | Kondisi                                          | Warna Badge           |
| ------------------------------------ | ------------------------------------------------ | --------------------- |
| **Cuti**                             | Ada cuti approved pada tanggal jadwal            | `info` (biru)         |
| **Izin**                             | Ada izin approved pada tanggal jadwal            | `info` (biru)         |
| **Sakit**                            | Ada sakit approved pada tanggal jadwal           | `info` (biru)         |
| **Hadir Tepat Waktu**                | Sudah absen masuk+keluar, tidak terlambat        | `success` (hijau)     |
| **Hadir Terlambat**                  | Sudah absen masuk+keluar, terlambat              | `warning` (kuning)    |
| **Hadir Tepat Waktu (Belum Keluar)** | Sudah absen masuk, belum keluar, tidak terlambat | `success` (hijau)     |
| **Hadir Terlambat (Belum Keluar)**   | Sudah absen masuk, belum keluar, terlambat       | `warning` (kuning)    |
| **Belum Absen**                      | Jadwal belum tiba atau dalam rentang waktu kerja | `secondary` (abu-abu) |
| **Alfa**                             | Sudah lewat 2 jam dari jadwal, belum absen       | `danger` (merah)      |

## 🔧 Implementasi Teknis

### 1. **Method di Model Schedule**

```php
/**
 * Get dynamic status based on attendance and current time
 */
public function getStatusDinamis(): string
{
    // 1. Cek cuti/izin/sakit
    $cutiIzin = CutiIzin::where('karyawan_id', $this->karyawan_id)
        ->where('status', 'approved')
        ->whereDate('tanggal_mulai', '<=', $this->tanggal_jadwal)
        ->whereDate('tanggal_selesai', '>=', $this->tanggal_jadwal)
        ->first();

    if ($cutiIzin) {
        return ucfirst($cutiIzin->jenis_permohonan);
    }

    // 2. Cek absensi
    $absensi = $this->absensi;
    if ($absensi) {
        // Logic untuk menentukan status berdasarkan absensi
    }

    // 3. Cek kondisi waktu
    // Logic untuk menentukan status berdasarkan waktu
}

/**
 * Get status color for display
 */
public function getStatusColor(): string
{
    $status = $this->getStatusDinamis();

    return match (true) {
        str_contains($status, 'Tepat Waktu') => 'success',
        str_contains($status, 'Terlambat') => 'warning',
        $status === 'Alfa' => 'danger',
        in_array($status, ['Cuti', 'Izin', 'Sakit']) => 'info',
        $status === 'Belum Absen' => 'secondary',
        default => 'secondary'
    };
}
```

### 2. **Update JadwalKerjaResource**

```php
TextColumn::make('status_dinamis')
    ->label('Status')
    ->badge()
    ->getStateUsing(function (Schedule $record): string {
        return $record->getStatusDinamis();
    })
    ->color(function (Schedule $record): string {
        return $record->getStatusColor();
    })
    ->sortable(false)
    ->searchable(false),
```

### 3. **Update Modal Jadwal Masal**

```php
@php
    $statusDinamis = $schedule->getStatusDinamis();
    $statusColor = $schedule->getStatusColor();
    // ... color mapping
@endphp

<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $colorClass }}">
    {{ $statusDinamis }}
</span>
```

## ✅ Keunggulan Sistem Status Dinamis

### 1. **Real-time Status**

-   Status berubah otomatis berdasarkan kondisi terkini
-   Tidak perlu update manual status jadwal
-   Akurat sesuai dengan data absensi dan waktu

### 2. **Prioritas yang Jelas**

-   Cuti/Izin/Sakit memiliki prioritas tertinggi
-   Data absensi aktual lebih prioritas dari estimasi waktu
-   Logika yang konsisten dan mudah dipahami

### 3. **Informasi Lengkap**

-   Menampilkan detail waktu absensi (masuk/keluar)
-   Membedakan status terlambat vs tepat waktu
-   Indikator visual yang jelas dengan warna badge

### 4. **Fleksibilitas**

-   Batas waktu alfa dapat dikonfigurasi (saat ini 2 jam)
-   Mudah ditambahkan status baru jika diperlukan
-   Kompatibel dengan sistem absensi yang ada

## 🎯 Manfaat untuk User

### 1. **Untuk Admin/Supervisor**

-   Monitoring real-time status karyawan
-   Identifikasi cepat karyawan yang alfa
-   Laporan kehadiran yang akurat

### 2. **Untuk Karyawan**

-   Status jadwal yang jelas dan up-to-date
-   Informasi absensi yang transparan
-   Reminder visual untuk absensi

### 3. **Untuk Sistem**

-   Otomatisasi status tanpa intervensi manual
-   Konsistensi data di seluruh aplikasi
-   Integrasi yang mulus dengan fitur existing

## 🔄 Backward Compatibility

-   Field `status` lama di tabel `jadwal_kerja` tetap ada
-   Status dinamis tidak mengubah data di database
-   Sistem lama tetap berfungsi normal
-   Migrasi bertahap tanpa downtime

## 📊 Contoh Skenario

### Skenario 1: Karyawan Hadir Tepat Waktu

-   Jadwal: 08:00 - 17:00
-   Absen masuk: 07:55
-   Absen keluar: 17:05
-   **Status: "Hadir Tepat Waktu"** (badge hijau)

### Skenario 2: Karyawan Terlambat

-   Jadwal: 08:00 - 17:00
-   Absen masuk: 08:30 (lewat toleransi)
-   Absen keluar: 17:05
-   **Status: "Hadir Terlambat"** (badge kuning)

### Skenario 3: Karyawan Cuti

-   Jadwal: 08:00 - 17:00
-   Cuti approved: 01-03 Januari 2024
-   Tanggal jadwal: 02 Januari 2024
-   **Status: "Cuti"** (badge biru)

### Skenario 4: Karyawan Alfa

-   Jadwal: 08:00 - 17:00
-   Waktu sekarang: 10:30
-   Belum ada absensi
-   **Status: "Alfa"** (badge merah)

### Skenario 5: Belum Waktunya

-   Jadwal: 08:00 - 17:00
-   Waktu sekarang: 07:30
-   Belum ada absensi
-   **Status: "Belum Absen"** (badge abu-abu)

Sistem ini memberikan visibilitas real-time yang sangat baik untuk manajemen kehadiran karyawan dengan logika yang jelas dan user-friendly.

## 🔧 Perbaikan Error Parsing Waktu

### Error: "Could not parse '2025-07-12 00:00:00 09:00:00'"

**Penyebab:**

-   Error terjadi saat parsing waktu dengan format yang salah
-   Menggabungkan tanggal dan waktu dengan cara yang tidak tepat
-   Format datetime yang duplikat

**Solusi yang Diimplementasikan:**

1. **Parsing Waktu yang Benar:**

```php
// Sebelum (Error)
$waktuMasuk = Carbon::parse($this->tanggal_jadwal . ' ' . $this->waktu_masuk);

// Sesudah (Fixed)
$waktuMasukJadwal = Carbon::createFromFormat('H:i:s', $this->waktu_masuk);
$waktuMasukHariIni = $tanggalJadwal->copy()
    ->setHour($waktuMasukJadwal->hour)
    ->setMinute($waktuMasukJadwal->minute)
    ->setSecond($waktuMasukJadwal->second);
```

2. **Fallback Pencarian Absensi:**

```php
// Cari absensi berdasarkan jadwal_id terlebih dahulu
$absensi = $this->absensi;

// Jika tidak ada, cari berdasarkan karyawan_id dan tanggal
if (!$absensi) {
    $absensi = Absensi::where('karyawan_id', $this->karyawan_id)
        ->whereDate('tanggal_absensi', $this->tanggal_jadwal)
        ->first();
}
```

3. **Error Handling yang Robust:**

```php
try {
    // Logic parsing waktu
} catch (\Exception) {
    // Return default jika ada error
    return 'Belum Absen';
}
```

4. **Method Helper untuk Cek Keterlambatan:**

```php
private function checkIfLate($waktuMasuk): bool
{
    $actualEntry = Carbon::parse($waktuMasuk);
    $shiftStart = Carbon::createFromFormat('H:i:s', $this->shift->waktu_mulai);

    $shiftStartToday = $actualEntry->copy()
        ->setHour($shiftStart->hour)
        ->setMinute($shiftStart->minute)
        ->setSecond($shiftStart->second);

    $toleranceMinutes = $this->shift->toleransi_keterlambatan ?? 0;
    return $actualEntry->greaterThan($shiftStartToday->addMinutes($toleranceMinutes));
}
```

### Perbaikan Tambahan:

-   ✅ Eager loading field `toleransi_keterlambatan` dari shift
-   ✅ Penanganan kasus jadwal masa lalu vs masa depan
-   ✅ Validasi data sebelum parsing
-   ✅ Fallback mechanism untuk pencarian absensi
-   ✅ Error handling yang comprehensive
