# Fitur Crosscheck Jadwal Masal

## 🎯 Tujuan Fitur

Fitur crosscheck memungkinkan Anda untuk **memverifikasi apakah jumlah jadwal kerja yang ter-generate dari jadwal masal sesuai dengan yang seharusnya**. Ini penting untuk memastikan tidak ada jadwal yang hilang atau terlewat saat proses generate.

## 📊 Cara Kerja Crosscheck

### 1. **Perhitungan Expected vs Actual**

```php
// Expected (Yang Seharusnya)
$totalKaryawan = jumlah karyawan yang ditugaskan
$totalDays = jumlah hari dalam periode jadwal
$expectedTotal = $totalKaryawan × $totalDays

// Actual (Yang Benar-benar Ada)
$actualTotal = jumlah jadwal kerja yang ter-generate dengan kriteria:
- karyawan_id sesuai dengan yang ditugaskan
- tanggal_jadwal dalam rentang periode
- shift_id sesuai jadwal masal
- entitas_id sesuai jadwal masal
- is_approved = true
```

### 2. **Analisis Breakdown**

#### **Per Karyawan:**
- <PERSON><PERSON><PERSON> jadwal yang seharusnya dimiliki setiap karyawan
- <PERSON><PERSON><PERSON> jadwal yang benar-benar ada
- Tanggal mana saja yang hilang untuk setiap karyawan

#### **Per Tanggal:**
- Berapa karyawan yang seharusnya dijadwalkan setiap hari
- Berapa karyawan yang benar-benar dijadwalkan
- Karyawan mana saja yang hilang untuk setiap tanggal

## 🔍 Fitur Crosscheck

### 1. **Action Button "Crosscheck"**
- Muncul di table JadwalMasal untuk jadwal yang sudah di-generate
- Icon: `heroicon-o-clipboard-document-check`
- Warna: Warning (kuning)

### 2. **Modal Crosscheck Komprehensif**
- **Header Summary**: Ringkasan jadwal masal dan status crosscheck
- **Status Badge**: Indikator visual apakah crosscheck berhasil atau gagal
- **Tab Navigation**: Dua view berbeda (Per Karyawan & Per Tanggal)
- **Detailed Breakdown**: Tabel lengkap dengan informasi missing data

### 3. **Kolom Status Crosscheck di Table**
- Badge yang menampilkan status kelengkapan
- Format: "✓ Lengkap (150/150)" atau "✗ Kurang (145/150)"
- Warna: Hijau (lengkap), Merah (kurang), Abu-abu (belum generate)

## 📋 Informasi yang Ditampilkan

### **Summary Section:**
```
Nama Jadwal: Jadwal Januari 2024
Periode: 01 Jan 2024 - 31 Jan 2024
Total Karyawan: 5 orang
Total Hari: 31 hari
Jadwal Diharapkan: 155
Jadwal Aktual: 150
Jadwal Hilang: 5
Persentase Lengkap: 96.77%
```

### **Per Karyawan Tab:**
| Karyawan | Diharapkan | Aktual | Hilang | Status | Tanggal Hilang |
|----------|------------|--------|--------|--------|----------------|
| John Doe | 31 | 30 | 1 | ✗ Tidak Lengkap | 15 Jan 2024 (Monday) |
| Jane Smith | 31 | 31 | 0 | ✓ Lengkap | - |

### **Per Tanggal Tab:**
| Tanggal | Diharapkan | Aktual | Hilang | Status | Karyawan Hilang |
|---------|------------|--------|--------|--------|-----------------|
| 15 Jan 2024 | 5 | 4 | 1 | ✗ Tidak Lengkap | John Doe (EMP001) |
| 16 Jan 2024 | 5 | 5 | 0 | ✓ Lengkap | - |

## 🎨 Visual Indicators

### **Status Badge Colors:**
- 🟢 **Hijau (Success)**: Crosscheck berhasil, semua jadwal lengkap
- 🔴 **Merah (Danger)**: Crosscheck gagal, ada jadwal yang hilang
- 🟡 **Kuning (Warning)**: Error saat melakukan crosscheck
- ⚪ **Abu-abu (Secondary)**: Jadwal belum di-generate

### **Counter Badges:**
- Tab "Per Karyawan" menampilkan jumlah karyawan yang tidak lengkap
- Tab "Per Tanggal" menampilkan jumlah tanggal yang tidak lengkap

## 🔧 Implementasi Teknis

### **Method di Model JadwalMasal:**

```php
public function getCrosscheckData(): array
{
    return [
        'summary' => [
            'total_karyawan' => $totalKaryawan,
            'total_days' => $totalDays,
            'expected_total' => $expectedTotal,
            'actual_total' => $actualTotal,
            'missing_total' => $expectedTotal - $actualTotal,
            'completion_percentage' => round(($actualTotal / $expectedTotal) * 100, 2),
            'is_complete' => $actualTotal === $expectedTotal,
        ],
        'employee_breakdown' => [...],
        'date_breakdown' => [...],
    ];
}
```

### **Action di JadwalMasalResource:**

```php
Tables\Actions\Action::make('crosscheck')
    ->label('Crosscheck')
    ->icon('heroicon-o-clipboard-document-check')
    ->color('warning')
    ->visible(fn (JadwalMasal $record): bool => $record->isGenerated())
    ->modalContent(function (JadwalMasal $record) {
        $crosscheckData = $record->getCrosscheckData();
        return view('filament.modals.jadwal-masal-crosscheck', [
            'jadwalMasal' => $record,
            'crosscheckData' => $crosscheckData
        ]);
    })
```

## ✅ Manfaat Fitur Crosscheck

### **1. Quality Assurance**
- Memastikan tidak ada jadwal yang hilang saat generate
- Deteksi dini masalah dalam proses generate
- Validasi integritas data jadwal

### **2. Troubleshooting**
- Identifikasi karyawan atau tanggal yang bermasalah
- Informasi detail untuk debugging
- Tracking completion percentage

### **3. Reporting & Audit**
- Dokumentasi kelengkapan jadwal
- Audit trail untuk proses generate
- Laporan untuk management

### **4. User Experience**
- Interface yang user-friendly dengan tabs
- Visual indicators yang jelas
- Informasi yang actionable

## 🚀 Skenario Penggunaan

### **Skenario 1: Crosscheck Berhasil**
- Generate jadwal masal untuk 10 karyawan, 30 hari
- Expected: 300 jadwal
- Actual: 300 jadwal
- Status: ✅ "Crosscheck BERHASIL - Semua jadwal lengkap"

### **Skenario 2: Ada Jadwal Hilang**
- Generate jadwal masal untuk 10 karyawan, 30 hari
- Expected: 300 jadwal
- Actual: 295 jadwal
- Status: ❌ "Crosscheck GAGAL - Ada jadwal yang hilang"
- Detail: 5 jadwal hilang untuk 2 karyawan pada tanggal tertentu

### **Skenario 3: Error Generate**
- Generate jadwal masal gagal di tengah proses
- Expected: 300 jadwal
- Actual: 150 jadwal
- Status: ❌ "Crosscheck GAGAL - Ada jadwal yang hilang"
- Detail: 50% jadwal hilang, perlu re-generate

## 🔄 Best Practices

1. **Selalu Crosscheck Setelah Generate**
   - Jalankan crosscheck setiap kali selesai generate jadwal masal
   - Pastikan status "✓ Lengkap" sebelum melanjutkan

2. **Monitor Completion Percentage**
   - Target: 100% completion
   - Jika < 100%, investigasi penyebabnya

3. **Review Missing Data**
   - Periksa tab "Per Karyawan" untuk karyawan bermasalah
   - Periksa tab "Per Tanggal" untuk tanggal bermasalah

4. **Dokumentasi Issues**
   - Screenshot hasil crosscheck jika ada masalah
   - Catat pattern missing data untuk troubleshooting

Fitur crosscheck ini memberikan confidence dan transparency dalam proses generate jadwal masal, memastikan semua karyawan mendapat jadwal yang seharusnya! 🎯
