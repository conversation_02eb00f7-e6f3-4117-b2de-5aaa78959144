# Implementasi Penyimpanan entitas_id di Jadwal Masal

Dokumentasi implementasi untuk memastikan field `entitas_id` tersimpan dengan benar saat membuat jadwal masal.

## 🎯 Tujuan Implementasi

Memastikan setiap jadwal masal memiliki `entitas_id` yang valid untuk:
1. **Filter berdasarkan role** - Setiap role hanya melihat jadwal masal sesuai akses mereka
2. **Tracking lokasi kerja** - <PERSON><PERSON>wal masal terkait dengan entitas/toko tertentu
3. **Generate jadwal individual** - Jadwal yang dihasilkan memiliki entitas yang tepat
4. **Reporting dan analytics** - Data dapat difilter berdasarkan entitas

## 📋 Masalah yang Diperbaiki

### 1. Field entitas_id Tidak Tersimpan
**Masalah Sebelumnya:**
- Form hanya memiliki `entitas_filter` untuk filtering
- Tidak ada field `entitas_id` yang sebenarnya disimpan ke database
- Jadwal masal dibuat tanpa informasi entitas

**Solusi:**
- <PERSON><PERSON> field `Hidden::make('entitas_id')` di form
- Auto-set nilai berdasarkan role user
- Logic fallback untuk memastikan selalu ada nilai

### 2. Inkonsistensi Auto-fill berdasarkan Role
**Masalah Sebelumnya:**
- Role keptok tidak otomatis terisi entitas mereka
- Admin/manager harus manual pilih entitas setiap kali

**Solusi:**
- Auto-fill entitas_id berdasarkan role dan hierarki
- Konsisten dengan filter yang sudah ada

## 🔧 Implementasi yang Dilakukan

### 1. Form Field - Hidden entitas_id

**File:** `app/Filament/Resources/JadwalMasalResource.php`

```php
// Hidden field untuk menyimpan entitas_id
Hidden::make('entitas_id')
    ->default(function () {
        $user = Auth::user();
        
        // Auto-set entitas_id berdasarkan role user
        if ($user->role === 'keptok' && $user->karyawan && $user->karyawan->id_entitas) {
            return $user->karyawan->id_entitas;
        } elseif ($user->role === 'supervisor' && $user->karyawan && $user->karyawan->id_divisi) {
            // Untuk supervisor, ambil entitas pertama dari divisi mereka
            $entitasId = \App\Models\Karyawan::where('id_divisi', $user->karyawan->id_divisi)
                ->whereNotNull('id_entitas')
                ->value('id_entitas');
            return $entitasId;
        } elseif ($user->role === 'manager' && $user->karyawan && $user->karyawan->id_departemen) {
            // Untuk manager, ambil entitas pertama dari departemen mereka
            $entitasId = \App\Models\Karyawan::where('id_departemen', $user->karyawan->id_departemen)
                ->whereNotNull('id_entitas')
                ->value('id_entitas');
            return $entitasId;
        }
        
        // Untuk role lain, akan diset dari entitas_filter
        return null;
    }),
```

### 2. Reactive Update dari entitas_filter

**File:** `app/Filament/Resources/JadwalMasalResource.php`

```php
Select::make('entitas_filter')
    // ... other configurations
    ->afterStateUpdated(function (callable $set, $state) {
        // Reset karyawan selection
        $set('karyawan', []);
        
        // Set entitas_id untuk disimpan ke database
        if ($state) {
            $set('entitas_id', $state);
        }
    })
```

### 3. Validation di Create Page

**File:** `app/Filament/Resources/JadwalMasalResource/Pages/CreateJadwalMasal.php`

```php
protected function mutateFormDataBeforeCreate(array $data): array
{
    $data['created_by'] = Auth::id();
    $user = Auth::user();

    // Store karyawan data temporarily for afterCreate
    $this->karyawanData = $data['karyawan'] ?? [];

    // Ensure entitas_id is set
    if (empty($data['entitas_id'])) {
        // Auto-set entitas_id berdasarkan role user jika belum diset
        if ($user->role === 'keptok' && $user->karyawan && $user->karyawan->id_entitas) {
            $data['entitas_id'] = $user->karyawan->id_entitas;
        } elseif (isset($data['entitas_filter']) && $data['entitas_filter']) {
            // Gunakan entitas_filter jika ada
            $data['entitas_id'] = $data['entitas_filter'];
        } elseif (!empty($this->karyawanData)) {
            // Fallback: ambil entitas dari karyawan pertama yang dipilih
            $firstKaryawan = \App\Models\Karyawan::find($this->karyawanData[0]);
            if ($firstKaryawan && $firstKaryawan->id_entitas) {
                $data['entitas_id'] = $firstKaryawan->id_entitas;
            }
        }
    }

    // Remove karyawan from main data as it's not a direct field
    unset($data['karyawan']);
    unset($data['entitas_filter']); // Remove filter field too

    return $data;
}
```

## 📊 Logic Auto-fill berdasarkan Role

### 🟣 **Keptok (Kepala Toko)**
- **Auto-fill**: `user.karyawan.id_entitas`
- **Behavior**: Otomatis terisi entitas toko mereka
- **Editable**: Tidak (hidden field, tidak bisa diubah)

### 🟡 **Supervisor**
- **Auto-fill**: Entitas pertama dari divisi mereka
- **Query**: `SELECT id_entitas FROM karyawan WHERE id_divisi = user.karyawan.id_divisi LIMIT 1`
- **Behavior**: Terisi otomatis, bisa diubah via entitas_filter

### 🔵 **Manager**
- **Auto-fill**: Entitas pertama dari departemen mereka
- **Query**: `SELECT id_entitas FROM karyawan WHERE id_departemen = user.karyawan.id_departemen LIMIT 1`
- **Behavior**: Terisi otomatis, bisa diubah via entitas_filter

### 🔴 **Admin/Manager HRD**
- **Auto-fill**: Tidak ada (null)
- **Behavior**: Harus pilih manual via entitas_filter
- **Editable**: Ya, wajib pilih entitas

## 🛡️ Fallback Strategy

### 1. Primary: Role-based Auto-fill
```php
if ($user->role === 'keptok') {
    return $user->karyawan->id_entitas;
}
```

### 2. Secondary: entitas_filter Selection
```php
if (isset($data['entitas_filter']) && $data['entitas_filter']) {
    $data['entitas_id'] = $data['entitas_filter'];
}
```

### 3. Tertiary: First Selected Karyawan
```php
if (!empty($this->karyawanData)) {
    $firstKaryawan = \App\Models\Karyawan::find($this->karyawanData[0]);
    if ($firstKaryawan && $firstKaryawan->id_entitas) {
        $data['entitas_id'] = $firstKaryawan->id_entitas;
    }
}
```

## 🧪 Testing Scenarios

### Test Case 1: Keptok Create Jadwal Masal
```php
// Login sebagai keptok
$keptok = User::where('role', 'keptok')->first();
Auth::login($keptok);

// Create jadwal masal
// Expected: entitas_id = keptok.karyawan.id_entitas
```

### Test Case 2: Admin Create Jadwal Masal
```php
// Login sebagai admin
$admin = User::where('role', 'admin')->first();
Auth::login($admin);

// Create jadwal masal dengan entitas_filter
// Expected: entitas_id = selected entitas_filter
```

### Test Case 3: Supervisor Create Jadwal Masal
```php
// Login sebagai supervisor
$supervisor = User::where('role', 'supervisor')->first();
Auth::login($supervisor);

// Create jadwal masal
// Expected: entitas_id = first entitas from supervisor's divisi
```

## 📋 Validation Rules

### 1. entitas_id Required
- Setiap jadwal masal harus memiliki entitas_id
- Tidak boleh null atau empty

### 2. entitas_id Valid
- entitas_id harus exist di tabel entitas
- entitas harus aktif (is_active = 1)

### 3. Role-based Access
- Keptok hanya bisa create untuk entitas mereka
- Supervisor/Manager sesuai hierarki mereka
- Admin bebas pilih entitas

## ✅ Hasil Implementasi

### 1. **✅ entitas_id Selalu Tersimpan**
- Hidden field memastikan nilai tersimpan
- Fallback strategy mencegah null values
- Validation di create page sebagai safety net

### 2. **✅ Auto-fill Berdasarkan Role**
- Keptok otomatis terisi entitas toko mereka
- Supervisor/Manager terisi entitas dari hierarki mereka
- Admin tetap manual untuk fleksibilitas

### 3. **✅ Konsistensi dengan Filter**
- Logic auto-fill konsisten dengan filter query
- Role hierarchy yang sama
- Behavior yang predictable

### 4. **✅ User Experience**
- Keptok tidak perlu pilih entitas (otomatis)
- Admin tetap fleksibel pilih entitas
- Form validation mencegah error

## 🔮 Future Enhancements

### 1. Multi-entitas Support
- Supervisor/Manager bisa pilih dari multiple entitas
- Dropdown terbatas sesuai akses mereka

### 2. Validation Enhancement
- Real-time validation entitas_id
- Warning jika entitas tidak aktif

### 3. Audit Trail
- Log perubahan entitas_id
- Track siapa yang create jadwal untuk entitas mana

## 🎯 Manfaat

1. **Data Integrity**: Setiap jadwal masal memiliki entitas yang valid
2. **Security**: Role-based access control yang konsisten
3. **User Experience**: Auto-fill mengurangi manual input
4. **Maintainability**: Logic yang terpusat dan konsisten
5. **Reporting**: Data entitas yang akurat untuk analytics

Implementasi penyimpanan entitas_id sekarang berfungsi dengan benar dan konsisten dengan sistem role-based access control yang ada!
