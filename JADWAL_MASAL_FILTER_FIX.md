# Perbaikan Filter Jadwal Masal Resource

Dokumentasi perbaikan filter berdasarkan role di JadwalMasalResource untuk memastikan setiap role hanya dapat melihat dan mengelola jadwal masal sesuai level akses mereka.

## 🎯 Masalah yang Diperbaiki

### 1. Query Filter yang Salah
**Masalah Sebelumnya:**
```php
// ❌ SALAH - JadwalMasal tidak memiliki relasi langsung ke karyawan
$query->whereHas('karyawan', function ($q) use ($user) {
    $q->where('id_entitas', $user->karyawan->id_entitas);
});
```

**Solusi:**
```php
// ✅ BENAR - JadwalMasal memiliki entitas_id langsung
$query->where('entitas_id', $user->karyawan->id_entitas);
```

### 2. Inkonsistensi Role Checking
**Masalah Sebelumnya:**
- Campuran antara Shield roles (`hasRole`, `hasAnyRole`) dan role field (`$user->role`)
- Role `keptok` tidak dikenali karena menggunakan Shield role `kepala_toko`

**Solusi:**
- Konsisten menggunakan `$user->role` untuk role baru (manager, keptok)
- Tetap menggunakan Shield roles untuk role lama yang sudah ada

## 📋 Perbaikan yang Dilakukan

### 1. Filter Query di `getEloquentQuery()`

**Sebelum:**
```php
if ($user->role === 'keptok') {
    $query->whereHas('karyawan', function ($q) use ($user) {
        $q->where('id_entitas', $user->karyawan->id_entitas);
    });
}
```

**Sesudah:**
```php
if ($user->role === 'keptok') {
    // Keptok can only see jadwal masal for their entitas
    if ($user->karyawan && $user->karyawan->id_entitas) {
        $query->where('entitas_id', $user->karyawan->id_entitas);
    } else {
        $query->whereRaw('1 = 0'); // Show nothing if no karyawan record
    }
}
```

### 2. Filter Level Akses per Role

#### 🟣 **Keptok (Kepala Toko)**
- **Akses**: Hanya jadwal masal untuk entitas mereka
- **Query**: `WHERE entitas_id = user.karyawan.id_entitas`
- **Scope**: Toko/entitas tertentu

#### 🟡 **Supervisor**
- **Akses**: Jadwal masal untuk entitas yang memiliki karyawan dari divisi mereka
- **Query**: `WHERE entitas_id IN (SELECT DISTINCT id_entitas FROM karyawan WHERE id_divisi = user.karyawan.id_divisi)`
- **Scope**: Multi-entitas berdasarkan divisi

#### 🔵 **Manager**
- **Akses**: Jadwal masal untuk entitas yang memiliki karyawan dari departemen mereka
- **Query**: `WHERE entitas_id IN (SELECT DISTINCT id_entitas FROM karyawan WHERE id_departemen = user.karyawan.id_departemen)`
- **Scope**: Multi-entitas berdasarkan departemen

#### 🔴 **Admin/Manager HRD**
- **Akses**: Semua jadwal masal
- **Query**: Tidak ada filter tambahan
- **Scope**: Global

### 3. Form Field Filtering

#### Entitas Filter
```php
// Hanya admin, manager, manager_hrd yang bisa pilih entitas
->visible(fn($record) => in_array(Auth::user()->role, ['admin', 'manager']) || 
                         Auth::user()->hasAnyRole(['manager_hrd', 'super_admin']))
```

#### Karyawan Selection
```php
if ($user->role === 'keptok') {
    // Keptok hanya bisa pilih karyawan dari entitas mereka
    if ($user->karyawan && $user->karyawan->id_entitas) {
        $query->where('id_entitas', $user->karyawan->id_entitas);
    }
} elseif ($user->role === 'supervisor') {
    // Supervisor hanya bisa pilih karyawan dari divisi mereka
    if ($user->karyawan && $user->karyawan->id_divisi) {
        $query->where('id_divisi', $user->karyawan->id_divisi);
    }
} elseif ($user->role === 'manager') {
    // Manager hanya bisa pilih karyawan dari departemen mereka
    if ($user->karyawan && $user->karyawan->id_departemen) {
        $query->where('id_departemen', $user->karyawan->id_departemen);
    }
}
```

### 4. Action Permissions

#### Generate Action
```php
->visible(function (JadwalMasal $record) {
    $user = Auth::user();
    $allowedRoles = ['admin', 'manager', 'supervisor', 'keptok'];
    $allowedShieldRoles = ['manager_hrd', 'super_admin', 'hrd_manager'];
    
    return (in_array($user->role, $allowedRoles) || $user->hasAnyRole($allowedShieldRoles))
        && !$record->isGenerated();
})
```

## 🔧 Struktur Data yang Digunakan

### JadwalMasal Model
```php
// Fields yang relevan untuk filtering
'entitas_id'     // ID entitas untuk jadwal masal
'created_by'     // User yang membuat jadwal masal
'nama_jadwal'    // Nama jadwal masal
'tanggal_mulai'  // Tanggal mulai periode
'tanggal_selesai' // Tanggal selesai periode

// Relationships
public function entitas()
{
    return $this->belongsTo(Entitas::class, 'entitas_id');
}

public function karyawan()
{
    return $this->belongsToMany(Karyawan::class, 'jadwal_masal_karyawan');
}
```

### User-Karyawan Relationship
```php
// User memiliki relasi ke Karyawan
public function karyawan()
{
    return $this->hasOne(Karyawan::class, 'id_user');
}

// Karyawan memiliki hierarki organisasi
'id_entitas'     // Entitas/toko tempat bekerja
'id_departemen'  // Departemen
'id_divisi'      // Divisi
```

## 🧪 Testing Filter

### Test Case 1: Keptok Role
```php
// Login sebagai keptok
$keptok = User::where('role', 'keptok')->first();
Auth::login($keptok);

// Akses JadwalMasalResource
// Harus hanya melihat jadwal masal untuk entitas keptok tersebut
```

### Test Case 2: Supervisor Role
```php
// Login sebagai supervisor
$supervisor = User::where('role', 'supervisor')->first();
Auth::login($supervisor);

// Harus melihat jadwal masal untuk entitas yang memiliki karyawan dari divisi supervisor
```

### Test Case 3: Manager Role
```php
// Login sebagai manager
$manager = User::where('role', 'manager')->first();
Auth::login($manager);

// Harus melihat jadwal masal untuk entitas yang memiliki karyawan dari departemen manager
```

## 📊 Hierarchy Akses

```
ADMIN (Global)
├── MANAGER_HRD (Global via Shield)
├── MANAGER (Multi-entitas by departemen)
├── SUPERVISOR (Multi-entitas by divisi)
└── KEPTOK (Single entitas)
```

## 🔍 Error Handling

### 1. User Tanpa Karyawan Record
```php
if ($user->karyawan && $user->karyawan->id_entitas) {
    // Normal filtering
} else {
    $query->whereRaw('1 = 0'); // Show nothing
}
```

### 2. Entitas Tidak Ditemukan
```php
$entitasIds = Karyawan::where('id_divisi', $user->karyawan->id_divisi)
    ->whereNotNull('id_entitas')
    ->distinct()
    ->pluck('id_entitas');

if ($entitasIds->isNotEmpty()) {
    $query->whereIn('entitas_id', $entitasIds);
} else {
    $query->whereRaw('1 = 0');
}
```

## ✅ Hasil Perbaikan

1. **✅ Filter Query Benar**: Menggunakan `entitas_id` langsung, bukan `whereHas('karyawan')`
2. **✅ Role Consistency**: Konsisten menggunakan `$user->role` untuk role baru
3. **✅ Level Access Control**: Setiap role hanya melihat data sesuai level mereka
4. **✅ Form Filtering**: Karyawan selection dibatasi sesuai role
5. **✅ Action Permissions**: Generate action hanya untuk role yang berwenang
6. **✅ Error Handling**: Graceful handling untuk edge cases

## 🎯 Manfaat

1. **Security**: Setiap role hanya dapat akses data yang sesuai
2. **Performance**: Query lebih efisien tanpa `whereHas` yang tidak perlu
3. **Consistency**: Role checking yang konsisten di seluruh aplikasi
4. **Maintainability**: Code yang lebih mudah dipahami dan maintain
5. **Scalability**: Mudah menambah role baru dengan pattern yang sama

Filter jadwal masal sekarang berfungsi dengan benar sesuai hierarki role yang telah ditetapkan!
