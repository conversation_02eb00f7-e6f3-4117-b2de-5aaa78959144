# Solusi Relation Manager <PERSON><PERSON><PERSON> Masal ke Jadwal Kerja

## 🎯 Ma<PERSON>ah yang Diselesaikan

Anda ingin membuat relation manager pada jadwal masal yang berisi jadwal kerjanya, tetapi pada database tidak ada `id_jadwal_masal` di table `jadwal_kerja`. <PERSON><PERSON> ada `id_jadwal_masal` di table `jadwal_masal_karyawan`.

## 🔍 Analisis Struktur Database

### Struktur yang Ada:

```
jadwal_masal
├── id
├── nama_jadwal
├── tanggal_mulai
├── tanggal_selesai
├── shift_id
├── entitas_id
├── created_by
└── generated_at

jadwal_masal_karyawan (pivot table)
├── jadwal_masal_id
├── karyawan_id
└── timestamps

jadwal_kerja (Schedule model)
├── id
├── karyawan_id
├── entitas_id
├── shift_id
├── supervisor_id
├── tanggal_jadwal
├── waktu_masuk
├── waktu_keluar
├── status
├── is_approved
└── timestamps
```

### Hubungan Data:

1. **JadwalMasal** ↔ **<PERSON><PERSON>wan** (Many-to-Many via `jadwal_masal_karyawan`)
2. **JadwalMasal** → **JadwalKerja** (Tidak ada foreign key langsung)
3. **Koneksi**: Jadwal kerja dibuat dari jadwal masal berdasarkan kriteria matching

## 💡 Solusi yang Diimplementasikan

### 1. Method di Model JadwalMasal

```php
/**
 * Get the generated schedules from this bulk schedule
 * Returns a collection of Schedule models that were generated from this jadwal masal
 */
public function getGeneratedSchedules()
{
    // Get karyawan IDs that are assigned to this jadwal masal
    $karyawanIds = $this->karyawan()->pluck('karyawan.id');

    // Return schedules that match the criteria
    return \App\Models\Schedule::whereIn('karyawan_id', $karyawanIds)
        ->where('shift_id', $this->shift_id)
        ->where('entitas_id', $this->entitas_id)
        ->whereBetween('tanggal_jadwal', [$this->tanggal_mulai, $this->tanggal_selesai])
        ->where('is_approved', true);
}
```

### 2. Action di JadwalMasalResource

Menambahkan action "Lihat Jadwal" yang menampilkan modal dengan daftar jadwal kerja:

```php
Tables\Actions\Action::make('lihat_jadwal')
    ->label('Lihat Jadwal')
    ->icon('heroicon-o-calendar-days')
    ->color('info')
    ->visible(fn (JadwalMasal $record): bool => $record->isGenerated())
    ->modalHeading(fn (JadwalMasal $record): string => 'Jadwal Kerja - ' . $record->nama_jadwal)
    ->modalContent(function (JadwalMasal $record) {
        $schedules = $record->getGeneratedSchedules()->with([
            'karyawan:id,nama_lengkap,nip',
            'shift:id,nama_shift,waktu_mulai,waktu_selesai',
            'entitas:id,nama_entitas',
            'supervisor:id,name'
        ])->get();

        return view('filament.modals.jadwal-masal-schedules', [
            'jadwalMasal' => $record,
            'schedules' => $schedules
        ]);
    })
    ->modalWidth('7xl')
    ->slideOver()
```

### 3. View Modal yang Komprehensif

File: `resources/views/filament/modals/jadwal-masal-schedules.blade.php`

Menampilkan:

-   **Header Information**: Nama jadwal, periode, total jadwal
-   **Statistics**: Jumlah karyawan, hari kerja, shift, lokasi
-   **Schedules Table**: Daftar lengkap jadwal kerja dengan detail
-   **Footer Information**: Info kapan di-generate dan petunjuk

### 4. Kolom Tambahan di Table

Menambahkan kolom "Jumlah Jadwal" untuk memberikan indikasi:

```php
TextColumn::make('jadwal_count')
    ->label('Jumlah Jadwal')
    ->badge()
    ->color('info')
    ->getStateUsing(function (JadwalMasal $record): string {
        if (!$record->isGenerated()) {
            return '0';
        }
        $count = $record->getGeneratedSchedules()->count();
        return (string) $count;
    })
    ->tooltip('Jumlah jadwal kerja yang dihasilkan')
    ->toggleable(isToggledHiddenByDefault: true)
```

## ✅ Keunggulan Solusi Ini

### 1. **Tanpa Migrasi Database**

-   Tidak perlu menambah kolom `jadwal_masal_id` ke table `jadwal_kerja`
-   Tidak mengubah struktur database yang sudah ada
-   Menghindari risiko error pada data existing

### 2. **Relasi Logis yang Kuat**

-   Menggunakan kriteria matching yang akurat:
    -   Karyawan yang sama (via pivot table)
    -   Shift yang sama
    -   Entitas yang sama
    -   Tanggal dalam range yang sama
    -   Status approved (dari jadwal masal)

### 3. **UI/UX yang Baik**

-   Modal slide-over yang luas (7xl)
-   Statistik yang informatif
-   Table yang responsive dengan dark mode support
-   Informasi lengkap per jadwal kerja

### 4. **Performance Optimized**

-   Eager loading untuk relasi
-   Query yang efisien dengan where conditions
-   Hanya tampil jika jadwal sudah di-generate

### 5. **Maintainable**

-   Kode yang clean dan mudah dipahami
-   Tidak mengganggu existing functionality
-   Mudah untuk di-extend di masa depan

## 🔧 Cara Kerja

1. **User membuat jadwal masal** → Data tersimpan di `jadwal_masal` dan `jadwal_masal_karyawan`
2. **User klik "Generate"** → Sistem membuat records di `jadwal_kerja` berdasarkan jadwal masal
3. **User klik "Lihat Jadwal"** → Sistem query jadwal kerja yang match dengan kriteria jadwal masal
4. **Modal terbuka** → Menampilkan semua jadwal kerja yang dihasilkan dari jadwal masal tersebut

## 🎉 Hasil Akhir

Anda sekarang memiliki:

-   ✅ Tampilan jadwal kerja yang dihasilkan dari jadwal masal
-   ✅ Tidak perlu migrasi database
-   ✅ UI yang user-friendly dan informatif
-   ✅ Performance yang optimal
-   ✅ Kode yang maintainable

Solusi ini memberikan fungsionalitas relation manager tanpa perlu mengubah struktur database, dengan memanfaatkan relasi logis yang sudah ada antara jadwal masal dan jadwal kerja.

## 🔧 Perbaikan Error Handling

### Error: "Attempt to read property nama_entitas on null"

**Penyebab:**

-   Ada jadwal kerja yang memiliki `entitas_id` null atau tidak valid
-   Relasi `entitas` tidak ter-load dengan benar
-   Data inconsistency antara jadwal masal dan jadwal kerja

**Solusi yang Diimplementasikan:**

1. **Null Checks di View:**

```php
// Sebelum
{{ $jadwalMasal->entitas->nama_entitas }}

// Sesudah
@if($jadwalMasal->entitas)
    {{ $jadwalMasal->entitas->nama }}
@else
    <span class="text-red-600">Tidak ada entitas</span>
@endif
```

2. **Flexible Query di Model:**

```php
// Menangani kasus entitas_id null
if ($this->entitas_id) {
    $query->where(function($q) {
        $q->where('entitas_id', $this->entitas_id)
          ->orWhereNull('entitas_id'); // Backward compatibility
    });
}
```

3. **Error Handling di Action:**

```php
try {
    $schedules = $record->getGeneratedSchedules()->with([...])->get();
    return view('filament.modals.jadwal-masal-schedules', [...]);
} catch (\Exception $e) {
    Log::error('Error loading jadwal masal schedules', [...]);
    return view('filament.modals.error-modal', [...]);
}
```

4. **Robust Eager Loading:**

```php
// Menggunakan field yang benar
'entitas:id,nama' // bukan 'nama_entitas'
```

### Fitur Error Handling:

-   ✅ Null checks untuk semua relasi (entitas, shift, karyawan, supervisor)
-   ✅ Graceful error handling dengan try-catch
-   ✅ Error logging untuk debugging
-   ✅ User-friendly error modal
-   ✅ Backward compatibility untuk data lama
-   ✅ Troubleshooting guide untuk user
