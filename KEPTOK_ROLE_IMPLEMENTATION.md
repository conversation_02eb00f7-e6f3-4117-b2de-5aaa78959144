# Keptok (Kepala Toko) Role Implementation

Dokumentasi implementasi role "keptok" (Kepala Toko) baru dalam sistem HRD.

## 🎯 Tujuan Implementasi

Menambahkan role "keptok" sebagai level akses khusus untuk kepala toko dengan permissions yang sesuai untuk:
1. **Man<PERSON><PERSON>en karyawan** di toko yang dipimpin
2. **Approval attendance** dan leave requests untuk toko
3. **Akses panel admin** dengan fokus operasional toko
4. **Monitoring dan reporting** untuk toko yang dikelola

## 📋 Fitur yang Diimplementasikan

### 1. Enum UserRole Update

**File:** `app/Enums/UserRole.php`

**Perubahan:**
- ✅ Menambahkan `case KEPTOK = 'keptok'`
- ✅ Update method `label()` untuk include "Kepala Toko"
- ✅ Update permissions methods:
  - `canAccessAdmin()` - Keptok dapat akses admin panel
  - `canManageEmployees()` - Keptok dapat manage karyawan toko
  - `canApproveAttendance()` - Keptok dapat approve attendance

**Updated Hierarchy Role:**
```
ADMIN (highest) → SUPERVISOR → MANAGER → KEPTOK → KARYAWAN (lowest)
```

### 2. Database Migration

**File:** `database/migrations/2025_07_11_153113_add_keptok_role_to_users_table.php`

**Fitur:**
- ✅ Update comment field role untuk include "keptok"
- ✅ Auto-update existing users dengan pattern email/nama keptok
- ✅ Rollback support untuk convert keptok → karyawan

**SQL Changes:**
```sql
-- Update role column comment
ALTER TABLE users MODIFY COLUMN role VARCHAR(50) DEFAULT 'karyawan' 
COMMENT 'User role: admin, supervisor, manager, keptok, karyawan';

-- Auto-update users with keptok patterns
UPDATE users SET role = 'keptok' 
WHERE (email LIKE '%keptok%' OR name LIKE '%Kepala Toko%' OR name LIKE '%Keptok%') 
AND role != 'admin';
```

### 3. Seeder Implementation

**File:** `database/seeders/KeptokRoleSeeder.php`

**Fitur:**
- ✅ Create 5 default keptok users
- ✅ Add keptok role to reference table (jika ada)
- ✅ Prevent duplicate creation
- ✅ Update existing users to keptok role

**Default Keptok Users:**
1. **Kepala Toko Pusat** - `<EMAIL>`
2. **Kepala Toko Cabang A** - `<EMAIL>`
3. **Kepala Toko Cabang B** - `<EMAIL>`
4. **Kepala Toko Cabang C** - `<EMAIL>`
5. **Kepala Toko Online** - `<EMAIL>`

**Default Password:** `password123`

### 4. UI Updates

**File:** `app/Filament/Resources/UserResource.php`

**Perubahan:**
- ✅ Badge color untuk role keptok: `purple` (ungu)
- ✅ Dropdown options include "keptok"

**Updated Color Scheme:**
- 🔴 **Admin**: `danger` (red)
- 🟡 **Supervisor**: `warning` (yellow)
- 🔵 **Manager**: `info` (blue)
- 🟣 **Keptok**: `purple` (purple) ← **NEW**
- 🟢 **Karyawan**: `success` (green)

## 🚀 Cara Menjalankan

### 1. Jalankan Migration
```bash
php artisan migrate --path=database/migrations/2025_07_11_153113_add_keptok_role_to_users_table.php
```

### 2. Jalankan Seeder
```bash
php artisan db:seed --class=KeptokRoleSeeder
```

### 3. Verifikasi Implementation
```bash
# Check users table
php artisan tinker
>>> App\Models\User::where('role', 'keptok')->get()

# Check enum
>>> App\Enums\UserRole::KEPTOK->value
>>> App\Enums\UserRole::KEPTOK->label()
```

## 🔐 Permissions & Access Control

### Keptok Role Permissions

**✅ Dapat Akses:**
- Admin panel (Filament)
- Manage karyawan di toko yang dipimpin
- Approve attendance requests untuk toko
- Approve leave requests untuk toko
- View reports toko
- Manage jadwal kerja toko
- Access absensi management toko
- Monitor performance toko

**❌ Tidak Dapat Akses:**
- System settings (admin only)
- User role management (admin only)
- Global configuration (admin only)
- Financial settings global (admin only)
- Data toko lain (kecuali admin/supervisor)

### Implementation di Code

```php
// Check if user can access admin panel
if (auth()->user()->role->canAccessAdmin()) {
    // Keptok, Manager, Supervisor, Admin dapat akses
}

// Check if user can manage employees
if (auth()->user()->role->canManageEmployees()) {
    // Keptok, Manager, Supervisor, Admin dapat manage
}

// Check specific role
if (auth()->user()->role === 'keptok') {
    // Specific logic untuk kepala toko
}
```

## 📊 Use Cases

### 1. Kepala Toko Pusat
- Manage semua karyawan toko pusat
- Approve cuti dan izin karyawan toko
- Generate laporan attendance toko pusat
- Manage jadwal kerja toko pusat
- Monitor sales performance

### 2. Kepala Toko Cabang
- Manage karyawan cabang tertentu
- Approve attendance requests cabang
- Monitor operational metrics cabang
- Coordinate dengan toko pusat
- Handle customer complaints

### 3. Kepala Toko Online
- Manage tim e-commerce
- Monitor online sales metrics
- Approve digital marketing requests
- Manage inventory online
- Handle online customer service

## 🧪 Testing

### Manual Testing

1. **Login sebagai Keptok:**
   ```
   Email: <EMAIL>
   Password: password123
   ```

2. **Verifikasi Access:**
   - ✅ Dapat akses admin panel
   - ✅ Dapat melihat menu karyawan
   - ✅ Dapat approve attendance
   - ❌ Tidak dapat akses system settings

3. **Test Role Badge:**
   - Buka User Management
   - Verifikasi badge keptok berwarna ungu

### Automated Testing

```php
// Test enum functionality
public function test_keptok_role_enum()
{
    $this->assertEquals('keptok', UserRole::KEPTOK->value);
    $this->assertEquals('Kepala Toko', UserRole::KEPTOK->label());
    $this->assertTrue(UserRole::KEPTOK->canAccessAdmin());
}

// Test user creation
public function test_keptok_user_creation()
{
    $keptok = User::factory()->create(['role' => 'keptok']);
    $this->assertTrue($keptok->role->canManageEmployees());
}
```

## 🔄 Migration & Rollback

### Forward Migration
```bash
php artisan migrate --path=database/migrations/2025_07_11_153113_add_keptok_role_to_users_table.php
```

### Rollback (jika diperlukan)
```bash
php artisan migrate:rollback --path=database/migrations/2025_07_11_153113_add_keptok_role_to_users_table.php
```

**Rollback Effect:**
- Semua user dengan role 'keptok' akan diubah ke 'karyawan'
- Comment field role dikembalikan ke versi sebelumnya

## 📝 Catatan Implementasi

### 1. Scope Permissions
- Keptok memiliki permissions terbatas pada toko yang dipimpin
- Tidak dapat akses data toko lain
- Level akses setara manager tapi scope lebih spesifik

### 2. Business Logic
- Setiap toko sebaiknya memiliki satu kepala toko
- Keptok dapat memiliki anak buah (karyawan toko)
- Reporting hierarchy: Keptok → Manager/Supervisor → Admin

### 3. Integration Points
- Sistem absensi: filter berdasarkan toko
- Sistem cuti: approval workflow per toko
- Reporting: dashboard khusus per toko

## 🔮 Future Enhancements

1. **Store-specific Filtering**
   - Filter data berdasarkan toko yang dipimpin
   - Restrict access ke data toko lain

2. **Store Performance Dashboard**
   - KPI dashboard khusus kepala toko
   - Sales metrics per toko
   - Employee performance metrics

3. **Multi-store Management**
   - Satu keptok dapat manage multiple stores
   - Store assignment system

4. **Approval Workflow**
   - Store-level approval untuk cuti/izin
   - Escalation ke manager jika diperlukan

## ✅ Checklist Implementasi

- [x] Update UserRole enum
- [x] Create database migration
- [x] Create seeder for keptok users
- [x] Update UI badge colors
- [x] Test migration execution
- [x] Test seeder execution
- [x] Verify permissions functionality
- [x] Create documentation
- [ ] Implement store-specific filtering (future)
- [ ] Create store dashboard (future)

## 🎉 Kesimpulan

Role "keptok" telah berhasil diimplementasikan dengan:
- ✅ **5 default keptok users** telah dibuat
- ✅ **Permissions yang sesuai** untuk level kepala toko
- ✅ **UI updates** dengan badge color ungu
- ✅ **Database migration** yang aman dan reversible
- ✅ **Backward compatibility** terjaga

Keptok role sekarang siap digunakan untuk manajemen operasional toko dengan akses yang tepat sesuai tanggung jawab kepala toko.
