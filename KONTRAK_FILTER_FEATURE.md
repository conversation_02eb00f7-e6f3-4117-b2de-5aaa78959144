# 📋 Filter Ka<PERSON>wan per Kategori Kontrak

## ✅ **Fitur Baru yang Ditambahkan:**

### **1. Tab Filter per <PERSON><PERSON>ntrak**
<PERSON>laman Data Karyawan, sekarang tersedia tab filter berdasarkan jenis kontrak:

- 🟢 **Tetap** - <PERSON><PERSON>wan dengan kontrak PKWTT (badge hijau)
- 🟡 **Kontrak** - Karyawan dengan kontrak PKWT (badge kuning)
- 🔵 **Probation** - <PERSON><PERSON><PERSON> dalam masa probation (badge biru)
- ⚫ **Freelance** - Karyawan freelance (badge abu-abu)

### **2. Kolom Jenis Kontrak**
Ditambahkan kolom baru di tabel karyawan:
- **Kolom**: "Jenis Kontrak"
- **Format**: Badge dengan warna sesuai jenis kontrak
- **Data**: Menampilkan jenis kontrak aktif terbaru

### **3. Filter Dropdown Jenis Kontrak**
Ditambahkan filter dropdown di bagian filter:
- **Filter**: "Filter Jenis Kontrak"
- **Options**: PKWTT, PKWT, Probation, Freelance
- **Fungsi**: Filter karyawan berdasarkan kontrak aktif

## 🎯 **Cara Menggunakan:**

### **Tab Filter:**
1. Buka halaman **Data Karyawan**
2. Lihat tab di bagian atas tabel
3. Klik tab sesuai jenis kontrak yang diinginkan
4. Tab akan menampilkan badge dengan jumlah karyawan

### **Filter Dropdown:**
1. Klik tombol **Filter** di bagian atas tabel
2. Pilih **"Filter Jenis Kontrak"**
3. Pilih jenis kontrak dari dropdown
4. Klik **Apply** untuk menerapkan filter

### **Kolom Jenis Kontrak:**
- Kolom otomatis menampilkan jenis kontrak aktif
- Badge berwarna untuk identifikasi cepat
- Data diambil dari kontrak aktif terbaru

## 📊 **Logika Filter:**

### **Kontrak Aktif:**
- Filter hanya menampilkan karyawan dengan kontrak `is_active = 1`
- Menggunakan kontrak terbaru berdasarkan `tgl_mulai`
- Jika tidak ada kontrak aktif, menampilkan "—"

### **Badge Colors:**
```php
'PKWTT' => 'success',    // Hijau
'PKWT' => 'warning',     // Kuning
'Probation' => 'info',   // Biru
'Freelance' => 'gray',   // Abu-abu
```

### **Tab Count:**
- Tab hanya muncul jika ada karyawan dengan jenis kontrak tersebut
- Jumlah di badge dihitung real-time
- Query optimized untuk performa

## 🔧 **Technical Details:**

### **Query Optimization:**
```php
// Efficient query untuk tab filter
$query->whereHas('riwayatKontrak', function ($q) use ($jenis) {
    $q->where('jenis_kontrak', $jenis)
      ->where('is_active', 1);
});
```

### **Column State:**
```php
// Mengambil kontrak aktif terbaru
$kontrak = $record->riwayatKontrak()
    ->where('is_active', 1)
    ->latest('tgl_mulai')
    ->first();
```

## 📋 **Fitur Lengkap di KaryawanResource:**

### **Tab Filters:**
1. ✅ **Semua** - Total karyawan
2. ✅ **Aktif** - Karyawan aktif
3. ✅ **Non-Aktif** - Karyawan non-aktif
4. ✅ **Per Departemen** - Filter per departemen
5. 🆕 **Per Jenis Kontrak** - Filter per jenis kontrak

### **Dropdown Filters:**
1. ✅ **Filter Departemen**
2. ✅ **Filter Status**
3. 🆕 **Filter Jenis Kontrak**
4. ✅ **Kontrak < 30 Hari**
5. ✅ **Ulang Tahun Bulan Ini**

### **Table Columns:**
1. ✅ **Foto Profil**
2. ✅ **Nama Lengkap**
3. ✅ **NIP**
4. ✅ **NIK**
5. ✅ **Tanggal Lahir**
6. ✅ **Umur**
7. ✅ **Entitas**
8. ✅ **Departemen**
9. ✅ **Jabatan**
10. ✅ **Divisi**
11. ✅ **Status Aktif**
12. 🆕 **Jenis Kontrak**
13. ✅ **Sisa Kontrak**

## 🎉 **Benefits:**

- ✅ **Filter Cepat** - Tab untuk akses cepat per jenis kontrak
- ✅ **Visual Clear** - Badge berwarna untuk identifikasi mudah
- ✅ **Data Akurat** - Hanya menampilkan kontrak aktif
- ✅ **Performance Optimized** - Query efficient dengan proper indexing
- ✅ **User Friendly** - Interface intuitif dan mudah digunakan

Sekarang HR dapat dengan mudah melihat dan memfilter karyawan berdasarkan jenis kontrak mereka! 🚀
