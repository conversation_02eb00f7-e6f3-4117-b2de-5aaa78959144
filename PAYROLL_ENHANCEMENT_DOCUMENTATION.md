# Dokumentasi Enhancement Payroll Transaction

## Overview

Implementasi fitur baru untuk menampilkan informasi tambahan di Data Absensi & Pelanggaran pada halaman transaksi payroll, serta menambahkan kemampuan edit untuk payroll yang belum di-approve.

## Fitur yang Ditambahkan

### 1. Data Absensi Tambahan

Menambahkan informasi berikut di section "Data Absensi & Pelanggaran":

-   **Sakit dengan Surat**: <PERSON><PERSON><PERSON> hari sakit dengan dokumen pendukung
-   **Sakit tanpa Surat**: <PERSON><PERSON><PERSON> hari sakit tanpa dokumen pendukung
-   **Izin**: <PERSON><PERSON><PERSON> hari izin yang diambil
-   **Ambil Cuti**: <PERSON><PERSON><PERSON> hari cuti yang diambil dalam periode
-   **Sisa Cuti**: Sisa kuota cuti tahunan karyawan

### 2. Fitur Edit Payroll

-   Payroll dengan status 'draft' dapat diedit
-   Form edit lengkap dengan semua komponen gaji dan potongan
-   Auto-calculation untuk total gaji kotor, total potongan, dan take home pay
-   Validasi untuk mencegah edit payroll yang sudah di-approve

### 3. Fitur Toleransi Keterlambatan

-   Tombol "Kelola Toleransi" di header actions untuk payroll draft
-   Modal form untuk memberikan/menghapus toleransi keterlambatan
-   Dropdown untuk memilih absensi terlambat dalam periode
-   Informasi detail absensi dan status toleransi
-   Integrasi dengan sistem toleransi yang sudah ada

### 4. Edit Potongan melalui Relation Manager

-   PayrollDeductionsRelationManager dapat diedit untuk payroll draft
-   Create, Edit, Delete potongan manual (kecuali BPJS, keterlambatan, pelanggaran)
-   Auto-recalculation total potongan dan take home pay
-   Form lengkap dengan validasi jenis potongan

### 5. Relation Manager Absensi

-   AbsensiRelationManager untuk melihat data absensi terkait payroll
-   Fitur toleransi keterlambatan langsung dari relation manager
-   Filter berdasarkan status dan toleransi
-   View dan edit absensi dengan permission check

## File yang Dimodifikasi

### 1. Database Migration

**File**: `database/migrations/2025_08_19_092330_add_additional_attendance_fields_to_payroll_transactions_table.php`

-   Menambahkan 5 field baru: `sakit_dengan_surat`, `sakit_tanpa_surat`, `izin`, `ambil_cuti`, `sisa_cuti`

### 2. Model PayrollTransaction

**File**: `app/Models/PayrollTransaction.php`

-   Menambahkan field baru ke `$fillable` array

### 3. PayrollService

**File**: `app/Services/PayrollService.php`

-   Method baru: `calculateAbsensiTambahanData()` - menghitung data absensi tambahan
-   Method baru: `calculateWorkingDaysBetween()` - helper untuk menghitung hari kerja
-   Update method `generatePayrollForKaryawan()` untuk menyimpan data absensi tambahan

### 4. ViewPayrollTransaction Page

**File**: `app/Filament/Resources/PayrollTransactionResource/Pages/ViewPayrollTransaction.php`

-   Update section "Data Absensi & Pelanggaran" untuk menampilkan field baru
-   Menambahkan suffix "hari" dan color coding untuk sisa cuti

### 5. EditPayrollTransaction Page (Baru)

**File**: `app/Filament/Resources/PayrollTransactionResource/Pages/EditPayrollTransaction.php`

-   Page baru untuk edit payroll transaction
-   Validasi status draft
-   Auto-calculation untuk total-total
-   Redirect ke view page setelah save

### 6. PayrollTransactionResource

**File**: `app/Filament/Resources/PayrollTransactionResource.php`

-   Form schema lengkap dengan semua field yang dapat diedit
-   Menambahkan EditAction di table actions
-   Update method `canEdit()` untuk mengizinkan edit status draft
-   Menambahkan route edit di `getPages()`
-   Menambahkan AbsensiRelationManager ke relations

### 7. PayrollDeductionsRelationManager (Updated)

**File**: `app/Filament/Resources/PayrollTransactionResource/RelationManagers/PayrollDeductionsRelationManager.php`

-   Form schema lengkap untuk create/edit potongan
-   Create, Edit, Delete actions untuk payroll draft
-   Method `recalculatePayrollTotals()` untuk auto-calculation
-   Validasi untuk mencegah edit potongan sistem (BPJS, keterlambatan, pelanggaran)
-   Update `isReadOnly()` berdasarkan status payroll

### 8. AbsensiRelationManager (Baru)

**File**: `app/Filament/Resources/PayrollTransactionResource/RelationManagers/AbsensiRelationManager.php`

-   Relation manager baru untuk menampilkan data absensi
-   Form toleransi keterlambatan dengan permission check
-   Table dengan filter status dan toleransi
-   Edit action untuk memberikan toleransi
-   Integrasi dengan PermissionService

### 9. PayrollTransaction Model (Updated)

**File**: `app/Models/PayrollTransaction.php`

-   Relationship `absensiRecords()` untuk mengakses absensi dalam periode
-   Relationship `lateAbsensiRecords()` untuk absensi terlambat tanpa toleransi
-   Update fillable array dengan field baru

### 10. ViewPayrollTransaction (Updated)

**File**: `app/Filament/Resources/PayrollTransactionResource/Pages/ViewPayrollTransaction.php`

-   Tombol "Kelola Toleransi" di header actions
-   Modal form untuk toleransi keterlambatan
-   Method helper untuk mengelola toleransi
-   Integrasi dengan sistem toleransi yang ada

## Cara Penggunaan

### Melihat Data Absensi Tambahan

1. Buka halaman Payroll Transactions
2. Klik "View" pada record payroll
3. Lihat section "Data Absensi & Pelanggaran" yang sekarang menampilkan informasi tambahan

### Mengedit Payroll

1. Buka halaman Payroll Transactions
2. Untuk payroll dengan status "Draft", akan muncul tombol "Edit"
3. Klik "Edit" untuk membuka form edit
4. Ubah nilai yang diperlukan
5. Klik "Save" untuk menyimpan perubahan
6. Sistem akan otomatis menghitung ulang total-total

### Mengelola Toleransi Keterlambatan

1. Buka halaman view payroll transaction (status draft)
2. Jika ada absensi terlambat, akan muncul tombol "Kelola Toleransi"
3. Klik tombol tersebut untuk membuka modal
4. Pilih absensi terlambat dari dropdown
5. Aktifkan toggle "Berikan Toleransi" dan isi alasan
6. Klik "Simpan" untuk memberikan toleransi
7. Untuk menghapus toleransi, matikan toggle dan simpan

### Mengedit Potongan melalui Relation Manager

1. Buka halaman view payroll transaction (status draft)
2. Scroll ke tab "Detail Potongan"
3. Klik "Create" untuk menambah potongan baru
4. Atau klik "Edit" pada potongan yang dapat diedit
5. Isi form dengan jenis potongan, nominal, dan keterangan
6. Sistem akan otomatis menghitung ulang total potongan

### Melihat Data Absensi

1. Buka halaman view payroll transaction
2. Scroll ke tab "Data Absensi"
3. Lihat semua absensi dalam periode payroll
4. Gunakan filter untuk melihat berdasarkan status atau toleransi
5. Klik "Edit" pada absensi terlambat untuk memberikan toleransi

## Validasi dan Keamanan

-   Hanya payroll dengan status 'draft' yang dapat diedit
-   Payroll yang sudah di-approve tidak dapat diedit
-   Auto-calculation mencegah kesalahan perhitungan manual
-   Redirect otomatis jika mencoba edit payroll yang sudah di-approve

## Testing

Semua fitur telah ditest dan berfungsi dengan baik:

### Fitur Dasar

-   ✅ Field baru berhasil ditambahkan ke database
-   ✅ Model PayrollTransaction dapat mengakses field baru
-   ✅ PayrollService dapat menghitung data absensi tambahan
-   ✅ View page menampilkan informasi tambahan dengan benar
-   ✅ Edit page berfungsi dengan validasi yang tepat
-   ✅ Route edit terdaftar dengan benar

### Fitur Toleransi

-   ✅ Relationship absensiRecords dan lateAbsensiRecords berfungsi
-   ✅ Tombol "Kelola Toleransi" muncul untuk payroll draft dengan absensi terlambat
-   ✅ Modal toleransi dapat memberikan dan menghapus toleransi
-   ✅ Integrasi dengan sistem toleransi yang ada berfungsi
-   ✅ Permission check untuk manage_absensi berfungsi

### Fitur Edit Potongan

-   ✅ PayrollDeductionsRelationManager dapat create/edit/delete
-   ✅ Auto-recalculation total potongan berfungsi
-   ✅ Validasi untuk potongan sistem (BPJS, keterlambatan, pelanggaran)
-   ✅ Form validation dan field requirements berfungsi

### Relation Manager Absensi

-   ✅ AbsensiRelationManager menampilkan data absensi dengan benar
-   ✅ Filter status dan toleransi berfungsi
-   ✅ Edit toleransi melalui relation manager berfungsi
-   ✅ Permission check dan visibility rules berfungsi

### Class dan Method

-   ✅ Semua class relation manager berhasil dibuat
-   ✅ Method helper untuk toleransi berfungsi
-   ✅ Relationship model berfungsi dengan benar
-   ✅ Auto-calculation method berfungsi

## Catatan Implementasi

-   Data absensi tambahan dihitung berdasarkan record CutiIzin yang approved dalam periode payroll
-   Sisa cuti dihitung menggunakan method `getRemainingLeaveQuota()` yang sudah ada
-   Perhitungan hari kerja mengecualikan weekend (Sabtu-Minggu)
-   Form edit menggunakan section yang terorganisir untuk kemudahan penggunaan
