# Dokumentasi Export Excel Payroll Transaction

## Overview
Implementasi fitur export Excel untuk transaksi payroll dengan format header yang disesuaikan dengan kebutuhan bisnis. Export ini menghasilkan file Excel dengan 33 kolom data yang komprehensif.

## Format Header Excel

### Kolom 1-6: <PERSON> & Kehadiran
- **NO**: <PERSON><PERSON> u<PERSON>
- **NRK**: <PERSON>mor <PERSON> (NIP)
- **Nama**: <PERSON>a le<PERSON> karyawan
- **TMK**: Total Menit Keterlambatan
- **HKE**: <PERSON>if (total hari kerja dalam periode)
- **HK yang Dibayar**: <PERSON> k<PERSON>ja yang di<PERSON>ar (total hari hadir)

### Kolom 7-12: Data Absensi Khusus
- **SAKIT + SURAT**: <PERSON><PERSON><PERSON> hari sakit dengan surat dokter
- **SAKIT - SURAT**: <PERSON><PERSON><PERSON> hari sakit tanpa surat dokter
- **TANPA KET / IZIN**: Jumlah hari izin
- **TERLAMBAT**: <PERSON><PERSON><PERSON> hari terl<PERSON>bat (konversi dari menit ke hari)
- **AMBIL CUTI**: <PERSON><PERSON><PERSON> hari cuti yang diambil
- **SISA CUTI**: Sisa kuota cuti tahunan

### Kolom 13-16: Komponen Gaji Pokok & Tunjangan
- **GAJI POKOK**: Gaji pokok karyawan
- **TUNJANGAN JABATAN**: Tunjangan berdasarkan jabatan
- **TUNJANGAN UMUM**: Tunjangan umum
- **TUNJANGAN SEMBAKO**: Tunjangan sembako

### Kolom 17-20: Komponen Lembur
- **LEMBUR BIASA**: Lembur hari biasa
- **MASUK TANGGAL MERAH**: Lembur tanggal merah/hari libur
- **JML LEMBUR HK**: Jumlah lembur hari kerja
- **LEMBUR TAMBAH HKE**: Lembur tambahan hari kerja efektif

### Kolom 21-24: Potongan Khusus
- **POTONGAN MINUS KASIR**: Potongan karena minus kasir
- **POTONGAN STOCK OPNAME**: Potongan stock opname
- **POTONGAN RETUR**: Potongan retur barang
- **POTONGAN KASBON**: Potongan kasbon/pinjaman

### Kolom 25-28: Data Tambahan
- **TAKE HOME PAY (Gross)**: Gaji bersih yang diterima
- **KEKURANGAN GAJI BULAN LALU**: Kekurangan gaji bulan sebelumnya
- **POTONGAN MAKAN**: Potongan makan
- **CHECK BOX**: Kolom kosong untuk checklist manual

### Kolom 29-33: Perhitungan Khusus
- **Hitungan rahasia**: Kolom kosong untuk perhitungan internal
- **Tot. Hari tanpa hitungan**: Total hari yang tidak dihitung (alpha, dll)
- **Proporsional**: Persentase kehadiran
- **Tot. Gaji Proporsional**: Gaji proporsional berdasarkan kehadiran
- **KPI 1**: Kolom kosong untuk KPI

## Fitur Export

### 1. Filter Periode
- Dropdown untuk memilih periode payroll tertentu
- Opsi "Semua Periode" untuk export seluruh data
- Nama file otomatis menyertakan nama periode

### 2. Styling Excel
- Header dengan background abu-abu dan font bold
- Border pada semua cell
- Text wrapping pada header
- Right alignment untuk kolom angka/currency
- Column width yang disesuaikan dengan content

### 3. Format Data
- Currency tanpa simbol rupiah (format: 1.000.000)
- Persentase dengan 1 desimal (format: 85.5%)
- Tanggal dalam format Indonesia
- Nomor urut otomatis

## Implementasi Teknis

### File yang Dimodifikasi

#### 1. PayrollTransactionExport.php
**Path**: `app/Exports/PayrollTransactionExport.php`
- Constructor dengan parameter `$periodId` untuk filter
- Method `headings()` dengan 33 kolom sesuai format
- Method `map()` untuk mapping data ke format export
- Helper methods untuk perhitungan khusus
- Styling dan column width configuration

#### 2. PayrollTransactionResource.php
**Path**: `app/Filament/Resources/PayrollTransactionResource.php`
- Custom export action dengan form filter periode
- Integration dengan PayrollTransactionExport class
- Error handling dan notification

### Method Helper

#### getDeductionsByType()
Mengambil data potongan berdasarkan jenis dari PayrollDeduction records:
- Kasir, Stock Opname, Retur, Kasbon, Makan

#### calculateLatenessDays()
Konversi menit keterlambatan ke hari (asumsi 8 jam = 480 menit per hari)

#### calculateTotalDaysWithoutCalculation()
Menghitung hari yang tidak masuk perhitungan gaji

#### calculateProportional()
Menghitung persentase kehadiran (hari hadir / hari kerja * 100%)

#### calculateProportionalSalary()
Menghitung gaji proporsional berdasarkan kehadiran

## Cara Penggunaan

### Export dari Halaman Payroll Transaction
1. Buka halaman "Transaksi Payroll"
2. Klik tombol "Export Excel" di header actions
3. Pilih periode payroll (opsional)
4. Klik "Export" untuk download file Excel

### Format File Output
- Nama file: `payroll_transaction_[periode]_[tanggal-waktu].xlsx`
- Format: Excel (.xlsx) dengan styling lengkap
- Encoding: UTF-8 untuk support karakter Indonesia

## Validasi Data

### Data yang Diekspor
- Hanya data PayrollTransaction dengan relasi lengkap
- Data diurutkan berdasarkan karyawan_id
- Include semua field absensi tambahan yang baru
- Include detail potongan dari PayrollDeduction

### Error Handling
- Try-catch untuk menangani error export
- Notification error jika export gagal
- Fallback untuk data yang kosong/null

## Testing

### Fitur yang Ditest
- ✅ Class PayrollTransactionExport dapat diinstansiasi
- ✅ Method headings() menghasilkan 33 kolom
- ✅ Method collection() mengambil data dengan benar
- ✅ Method map() dapat mapping data ke format export
- ✅ Filter periode berfungsi dengan benar
- ✅ Styling dan column width diterapkan

### Skenario Testing
1. Export semua periode
2. Export periode tertentu
3. Export dengan data kosong
4. Export dengan data lengkap
5. Validasi format output Excel

## Catatan Implementasi

### Perhitungan Khusus
- Konversi menit ke hari menggunakan asumsi 8 jam kerja
- Proporsional dihitung dari hari hadir vs hari kerja
- Gaji proporsional = (hari hadir / hari kerja) * gaji pokok

### Kolom Kosong
- CHECK BOX, Hitungan rahasia, KPI 1 sengaja dikosongkan
- Dapat diisi manual setelah export
- Memberikan fleksibilitas untuk kebutuhan khusus

### Format Currency
- Tanpa simbol rupiah untuk kemudahan import ke sistem lain
- Menggunakan separator titik untuk ribuan
- Format: 1.000.000 (bukan Rp 1.000.000)

Fitur export ini memberikan fleksibilitas maksimal untuk analisis data payroll dengan format yang sesuai kebutuhan bisnis.
