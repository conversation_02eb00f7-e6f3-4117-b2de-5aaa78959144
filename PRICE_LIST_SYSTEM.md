# Price List System Documentation

## Overview

The Price List System allows you to manage different pricing strategies for products across different outlets. It supports:

- **Global Price Lists**: Default pricing that applies to all outlets
- **Outlet-Specific Price Lists**: Custom pricing for specific outlets
- **Priority System**: Multiple price lists per outlet with priority ordering
- **Automatic Fallback**: Smart price resolution with fallback logic

## Database Structure

### Tables Created

1. **`price_lists`** - Main price list definitions
2. **`price_list_items`** - Product prices within each price list
3. **`outlet_price_lists`** - Assigns price lists to outlets with priority
4. **`user_outlets`** - User access to outlets

## How It Works

### Price Resolution Logic

When a product price is requested for an outlet, the system follows this priority:

1. **Outlet-Specific Price Lists** (ordered by priority, 1 = highest)
2. **Global Price List** (fallback)
3. **Product Default Price** (final fallback)

### Example Scenario

```
Product: "Coffee" - Default Price: Rp 25,000

Outlet: "Downtown Cafe"
- VIP Price List (Priority 1): Coffee = Rp 20,000
- Regular Price List (Priority 2): Coffee = Rp 22,000

Global Price List: Coffee = Rp 23,000

Result: Rp 20,000 (from VIP Price List - highest priority)
```

## API Endpoints

### Authentication Required
All endpoints require `pos:read` ability token.

### Available Endpoints

#### 1. Get Product Prices for Outlet
```
GET /api/pos/prices/products?outlet_id={outlet_id}
```

**Parameters:**
- `outlet_id` (required): Outlet ID
- `product_ids[]` (optional): Specific product IDs
- `category_id` (optional): Filter by category
- `search` (optional): Search products
- `per_page` (optional): Pagination limit (max 100)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Coffee",
      "sku": "COFFEE001",
      "default_price": 25000,
      "outlet_price": 20000,
      "price_source": "outlet_price_list",
      "price_list_id": 2,
      "price_list_name": "VIP Price List"
    }
  ],
  "pagination": {...}
}
```

#### 2. Get Single Product Price
```
GET /api/pos/prices/products/{product_id}?outlet_id={outlet_id}
```

#### 3. Get Outlet's Price Lists
```
GET /api/pos/prices/outlets/{outlet_id}/price-lists
```

#### 4. Compare Product Prices Across Outlets
```
GET /api/pos/prices/products/{product_id}/comparison?outlet_ids[]={outlet_id}
```

#### 5. Bulk Price Retrieval
```
POST /api/pos/prices/bulk
```

**Body:**
```json
{
  "items": [
    {"product_id": 1, "outlet_id": 1},
    {"product_id": 2, "outlet_id": 1}
  ]
}
```

#### 6. Debug Price Resolution
```
GET /api/pos/prices/debug/{product_id}/{outlet_id}
```

#### 7. Validate Outlet Configuration
```
GET /api/pos/prices/validate-outlet/{outlet_id}
```

## Filament POS Panel

### Accessing Price Lists

1. Navigate to POS Panel: `/pos`
2. Go to **Product Management** → **Price Lists**

### Managing Price Lists

#### Creating a Price List

1. Click **"Create Price List"**
2. Fill in:
   - **Name**: Descriptive name (e.g., "VIP Customer Prices")
   - **Code**: Unique identifier (e.g., "VIP_001")
   - **Description**: Optional description
   - **Global**: Check if this should be the global price list
   - **Active**: Enable/disable the price list
   - **Effective Dates**: Optional date range

#### Managing Products in Price List

1. From price list index, click **"Manage Products"**
2. Use the table to:
   - Add/remove products
   - Edit prices directly in the table
   - Bulk add all products
   - Copy from global price list

#### Assigning to Outlets

1. From price list index, click **"Assign Outlets"**
2. Use the table to:
   - Assign/unassign outlets
   - Set priority (1 = highest)
   - Configure effective dates
   - Bulk assign to multiple outlets

## Service Usage

### PriceResolutionService

```php
use App\Services\PriceResolutionService;

$priceService = app(PriceResolutionService::class);

// Get price for a product at an outlet
$priceInfo = $priceService->getProductPrice($productId, $outletId);

// Get prices for multiple products
$prices = $priceService->getProductPrices([$productId1, $productId2], $outletId);

// Clear cache
$priceService->clearPriceCache($productId, $outletId);

// Debug price resolution
$summary = $priceService->getPriceResolutionSummary($productId, $outletId);

// Validate outlet configuration
$validation = $priceService->validateOutletPriceConfiguration($outletId);
```

## Model Usage

### Creating Price Lists

```php
use App\Models\PriceList;

// Create global price list
$globalPriceList = PriceList::create([
    'name' => 'Global Price List',
    'code' => 'GLOBAL',
    'is_global' => true,
    'is_active' => true,
    'created_by' => auth()->id(),
]);

// Add products
$globalPriceList->addProduct($productId, $price, $costPrice);
```

### Assigning Price Lists to Outlets

```php
use App\Models\Outlet;

$outlet = Outlet::find($outletId);

// Assign price list with priority
$outlet->assignPriceList($priceListId, $priority = 1);

// Remove price list
$outlet->removePriceList($priceListId);
```

### Getting Product Prices

```php
use App\Models\Product;

$product = Product::find($productId);

// Get price for specific outlet
$price = $product->getPriceForOutlet($outletId);

// Get cost price for specific outlet
$costPrice = $product->getCostPriceForOutlet($outletId);
```

## Cache Management

The system uses caching for performance. Cache is automatically cleared when:

- Price list items are updated
- Price lists are assigned/unassigned to outlets
- Price list priorities are changed

Manual cache clearing:
```php
$priceService->clearPriceCache($productId, $outletId);
$priceService->clearProductPriceCache($productId);
$priceService->clearOutletPriceCache($outletId);
$priceService->clearAllPriceCaches();
```

## Best Practices

1. **Always have a Global Price List** as fallback
2. **Use meaningful codes** for price lists (e.g., VIP_001, WHOLESALE_001)
3. **Set clear priorities** when assigning multiple price lists to outlets
4. **Use effective dates** for temporary pricing campaigns
5. **Test price resolution** using debug endpoints before going live
6. **Monitor cache performance** and clear when needed

## Troubleshooting

### Common Issues

1. **Product shows default price instead of price list price**
   - Check if price list is active
   - Verify product is added to the price list
   - Check outlet assignment and priority
   - Use debug endpoint to trace resolution

2. **Price list not appearing in outlet**
   - Verify price list is assigned to outlet
   - Check effective dates
   - Ensure price list is active

3. **API returns 403 Forbidden**
   - Check token has `pos:read` ability
   - Verify user authentication

### Debug Commands

```bash
# Check price resolution for specific product/outlet
curl -H "Authorization: Bearer {token}" \
     "http://your-app.com/api/pos/prices/debug/{product_id}/{outlet_id}"

# Validate outlet configuration
curl -H "Authorization: Bearer {token}" \
     "http://your-app.com/api/pos/prices/validate-outlet/{outlet_id}"
```

## Migration and Setup

1. **Run migrations**: `php artisan migrate`
2. **Seed global price list**: `php artisan db:seed --class=PriceListSeeder`
3. **Access POS panel**: Navigate to `/pos` and go to Price Lists
4. **Create outlet-specific price lists** as needed
5. **Assign price lists to outlets** with appropriate priorities

The system is now ready for use!
