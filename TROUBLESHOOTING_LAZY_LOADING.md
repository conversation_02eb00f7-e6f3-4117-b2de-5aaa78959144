# Troubleshooting Lazy Loading Error

## Masalah yang Diselesaikan

Error **"Attempted to lazy load [karyawan] on model [App\Models\User] but lazy loading is disabled"** terjadi ketika mengelola akun karyawan.

## Penyebab Masalah

1. **Lazy Loading Dinonaktifkan**: Di `QueryOptimizationServiceProvider`, lazy loading dinonaktifkan untuk mencegah N+1 queries
2. **Akses Relasi Tanpa Eager Loading**: Beberapa kode mengakses relasi `karyawan` dari model `User` tanpa eager loading
3. **Missing Eager Loading**: Resource dan controller tidak menggunakan eager loading yang konsisten

## Solusi yang Diterapkan

### 1. **Perbaikan UserResource**

```php
// Menambahkan eager loading di UserResource
public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
{
    return parent::getEloquentQuery()->with(['karyawan']);
}
```

### 2. **Perbaikan UserKaryawanManager**

```php
// Menggunakan eager loading di form options
->options(User::with('karyawan')->get()->pluck('name', 'id'))

// Menggunakan eager loading di afterStateUpdated
$user = User::with('karyawan')->find($state);

// Menggunakan eager loading di submit action
$user = User::with('karyawan')->findOrFail($data['userId']);
$karyawan = Karyawan::with('user')->findOrFail($data['karyawanId']);
```

### 3. **Perbaikan UserKaryawanController**

```php
// Menggunakan eager loading di semua query
$user = User::with('karyawan')->findOrFail($request->user_id);
$karyawan = Karyawan::with('user')->findOrFail($request->karyawan_id);
$existingUser = User::with('karyawan')->find($karyawan->id_user);
```

### 4. **Perbaikan KaryawanResource**

```php
// Menggunakan eager loading saat cek existing user
$existingUser = \App\Models\User::with('karyawan')->where('email', $karyawan->email)->first();
```

### 5. **Perbaikan QueryOptimizationServiceProvider**

```php
// Lazy loading hanya dinonaktifkan di production
if (app()->environment('production')) {
    Model::preventLazyLoading();
}

// Di development, allow lazy loading untuk debugging
// Model::preventLazyLoading(!app()->environment('local'));
```

### 6. **Perbaikan Widget Karyawan**

```php
// KaryawanOverviewWidget.php
$karyawan = \App\Models\Karyawan::with(['departemen', 'divisi'])
    ->where('id_user', $user->id)
    ->first();

// KaryawanStats.php, RecentAttendance.php, UpcomingSchedule.php
$karyawan = Karyawan::with(['departemen', 'divisi'])->where('id_user', $user->id)->first();
```

### 7. **Perbaikan View UserKaryawanManager**

```php
// UserKaryawanManager.php - Menambah method untuk eager loading
public function getUsersWithKaryawan()
{
    return User::with('karyawan')->get();
}
```

```blade
{{-- user-karyawan-manager.blade.php - Menggunakan method dengan eager loading --}}
@foreach($this->getUsersWithKaryawan() as $user)
    {{-- Akses $user->karyawan sudah aman --}}
    @if($user->karyawan)
        {{ $user->karyawan->nama_lengkap }}
    @endif
@endforeach
```

## File yang Diperbaiki

### 1. **Resources**

-   `app/Filament/Resources/UserResource.php` - Menambah eager loading
-   `app/Filament/Resources/KaryawanResource.php` - Perbaikan query existing user

### 2. **Pages & Controllers**

-   `app/Filament/Pages/UserKaryawanManager.php` - Eager loading di form dan actions
-   `app/Http/Controllers/UserKaryawanController.php` - Eager loading di semua queries
-   `app/Filament/Resources/UserResource/Pages/EditUser.php` - Perbaikan akses relasi

### 3. **Widgets**

-   `app/Filament/Karyawan/Widgets/KaryawanOverviewWidget.php` - Eager loading karyawan
-   `app/Filament/Karyawan/Widgets/KaryawanStats.php` - Eager loading karyawan
-   `app/Filament/Karyawan/Widgets/RecentAttendance.php` - Eager loading karyawan
-   `app/Filament/Karyawan/Widgets/UpcomingSchedule.php` - Eager loading karyawan

### 4. **Views**

-   `resources/views/filament/pages/user-karyawan-manager.blade.php` - Perbaikan akses relasi di view

### 5. **Service Provider**

-   `app/Providers/QueryOptimizationServiceProvider.php` - Konfigurasi lazy loading

## Cara Mencegah Masalah Serupa

### 1. **Selalu Gunakan Eager Loading**

```php
// ❌ Salah - akan menyebabkan lazy loading
$user = User::find(1);
$karyawanName = $user->karyawan->nama_lengkap;

// ✅ Benar - menggunakan eager loading
$user = User::with('karyawan')->find(1);
$karyawanName = $user->karyawan?->nama_lengkap;
```

### 2. **Gunakan Scope Optimized Queries**

```php
// ✅ Menggunakan scope yang sudah ada
$karyawan = Karyawan::withBasicRelations()->find(1);
$karyawan = Karyawan::withAllRelations()->find(1);
```

### 3. **Cek Relasi Sebelum Akses**

```php
// ✅ Cek relasi ada atau tidak
if ($user->karyawan) {
    $name = $user->karyawan->nama_lengkap;
}

// ✅ Atau gunakan null-safe operator
$name = $user->karyawan?->nama_lengkap;
```

### 4. **Eager Loading di Resource**

```php
// ✅ Tambahkan di setiap Resource yang mengakses relasi
public static function getEloquentQuery(): Builder
{
    return parent::getEloquentQuery()->with(['relasi1', 'relasi2']);
}
```

## Testing

### 1. **Test Manual**

-   Akses halaman User Resource
-   Edit user yang memiliki karyawan
-   Gunakan fitur "Kaitkan dengan Karyawan"
-   Gunakan halaman "Kelola Akun Karyawan"

### 2. **Monitoring**

```php
// Cek query yang dijalankan
DB::enableQueryLog();
// ... lakukan operasi
dd(DB::getQueryLog());
```

## Status Saat Ini

✅ **RESOLVED**: Error lazy loading sudah diperbaiki
✅ **TESTED**: Semua fitur kelola akun karyawan berfungsi
✅ **OPTIMIZED**: Eager loading diterapkan konsisten
✅ **DOCUMENTED**: Troubleshooting guide tersedia

## Best Practices

### 1. **Selalu Eager Load Relasi yang Dibutuhkan**

-   Identifikasi relasi yang akan diakses
-   Gunakan `with()` untuk eager loading
-   Gunakan scope optimized queries jika tersedia

### 2. **Gunakan Null-Safe Operators**

-   Gunakan `?->` untuk akses relasi yang mungkin null
-   Cek relasi dengan `if ($model->relationLoaded('relation'))`

### 3. **Monitor Query Performance**

-   Aktifkan query logging di development
-   Gunakan tools seperti Laravel Debugbar
-   Monitor N+1 queries

### 4. **Konsisten dalam Resource**

-   Semua Resource harus memiliki eager loading yang sesuai
-   Gunakan trait `HasOptimizedQueries` untuk konsistensi
-   Dokumentasikan relasi yang dibutuhkan

Dengan perbaikan ini, sistem sudah tidak akan mengalami error lazy loading lagi dan performa query menjadi lebih optimal.
