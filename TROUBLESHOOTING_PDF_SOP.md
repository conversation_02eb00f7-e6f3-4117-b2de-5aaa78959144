# Troubleshooting File PDF SOP - 404 Not Found

## Masalah yang Diselesaikan
File PDF SOP mengembalikan error **404 Not Found** ketika diakses melalui URL `http://localhost:8000/storage/sop_dokumens/sop_keluhan_pelanggan.pdf`

## Penyebab Masalah
1. **Symbolic Link Rusak/Tidak Ada**: Link dari `public/storage` ke `storage/app/public` tidak berfungsi
2. **File PDF Tidak Valid**: File PDF dummy yang dibuat sebelumnya tidak proper
3. **Permission Issues**: File tidak memiliki permission yang benar

## Solusi yang Diterapkan

### 1. **Perbaikan Symbolic Link**
```bash
# Hapus symbolic link lama
rm -rf public/storage

# Buat ulang symbolic link
php artisan storage:link
```

### 2. **Regenerasi File PDF yang Proper**
- Menggunakan library **Dompdf** untuk membuat PDF yang valid
- File PDF sekarang berisi konten HTML yang di-convert ke PDF
- Ukuran file lebih besar (~2.2KB) dibanding dummy sebelumnya (~486B)

### 3. **Validasi Akses File**
Semua 14 file PDF SOP berhasil diakses dengan HTTP 200:
- ✅ sop_departemen_hr.pdf
- ✅ sop_departemen_it.pdf  
- ✅ sop_departemen_finance.pdf
- ✅ sop_keselamatan_hr.pdf
- ✅ sop_keselamatan_it.pdf
- ✅ sop_keselamatan_finance.pdf
- ✅ sop_divisi_recruitment.pdf
- ✅ sop_divisi_development.pdf
- ✅ sop_divisi_accounting.pdf
- ✅ sop_lama_recruitment.pdf
- ✅ sop_lama_development.pdf
- ✅ sop_lama_accounting.pdf
- ✅ sop_iso_9001.pdf
- ✅ sop_keluhan_pelanggan.pdf

## Cara Test Manual

### 1. **Test di Browser**
Buka URL berikut di browser:
```
http://localhost:8000/storage/sop_dokumens/sop_keluhan_pelanggan.pdf
```

### 2. **Test dengan cURL**
```bash
curl -I http://localhost:8000/storage/sop_dokumens/sop_keluhan_pelanggan.pdf
```
Harus mengembalikan `HTTP/1.1 200 OK`

### 3. **Cek File di Storage**
```bash
ls -la storage/app/public/sop_dokumens/
ls -la public/storage/sop_dokumens/
```

## Troubleshooting untuk Masa Depan

### Jika File PDF 404 Not Found:

1. **Cek Server Laravel**
   ```bash
   php artisan serve
   ```

2. **Cek Symbolic Link**
   ```bash
   ls -la public/storage
   # Harus menunjuk ke ../storage/app/public
   ```

3. **Recreate Storage Link**
   ```bash
   rm -rf public/storage
   php artisan storage:link
   ```

4. **Cek Permission File**
   ```bash
   ls -la storage/app/public/sop_dokumens/
   # File harus readable (r--r--r--)
   ```

5. **Cek Konfigurasi Filesystem**
   ```php
   // config/filesystems.php
   'public' => [
       'driver' => 'local',
       'root' => storage_path('app/public'),
       'url' => env('APP_URL').'/storage',
       'visibility' => 'public',
   ],
   ```

### Jika Upload File Baru Gagal:

1. **Cek Permission Direktori**
   ```bash
   chmod 755 storage/app/public/sop_dokumens/
   ```

2. **Cek Disk Space**
   ```bash
   df -h
   ```

3. **Cek Upload Limits**
   ```php
   // php.ini
   upload_max_filesize = 10M
   post_max_size = 10M
   ```

## Status Saat Ini
✅ **RESOLVED**: Semua file PDF SOP dapat diakses dengan baik
✅ **TESTED**: 14 file PDF berhasil divalidasi
✅ **DOCUMENTED**: Troubleshooting guide tersedia

## File yang Terlibat
- `storage/app/public/sop_dokumens/` - Lokasi file PDF
- `public/storage/` - Symbolic link ke storage
- `app/Filament/Resources/SopDokumenResource.php` - Resource admin
- `app/Filament/Karyawan/Resources/SopResource.php` - Resource karyawan
- `resources/views/filament/karyawan/sop-detail.blade.php` - View detail SOP
