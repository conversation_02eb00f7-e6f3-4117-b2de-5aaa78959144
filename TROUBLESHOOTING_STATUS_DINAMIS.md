# Troubleshooting Status Dinamis Jadwal Kerja

## 🎯 Ma<PERSON>ah yang Di<PERSON>

**Issue**: Status dinamis di jadwal kerja tidak menunjukkan "terlambat" meskipun di data absensi banyak yang terlambat.

## 🔍 Penyebab yang Mungkin

### 1. **Status Database vs Perhitungan**
- Field `status` di tabel `absensi` mungkin tidak ter-update dengan benar
- Logic perhitungan keterlambatan berbeda antara sistem absensi dan status dinamis
- Observer atau service yang mengupdate status absensi tidak berjalan

### 2. **Relasi Data**
- Absensi tidak ter-link dengan `jadwal_id` yang benar
- Pencarian fallback berdasarkan `karyawan_id` + `tanggal_absensi` tidak menemukan data
- Eager loading tidak memuat data absensi dengan benar

### 3. **Logic Parsing Waktu**
- Format waktu yang berbeda antara database dan perhitungan
- Timezone issues
- Split shift handling yang tidak konsisten

## 🔧 Perbaikan yang Dilakukan

### 1. **Perbaikan Logic Status Dinamis**

```php
// Prioritas status (berurutan):
if ($absensi->status === 'terlambat') {
    return 'Hadir Terlambat';
} elseif (in_array($absensi->status, ['hadir'])) {
    return 'Hadir Tepat Waktu';
} else {
    // Fallback: hitung keterlambatan jika status tidak jelas
    $isLate = $this->checkIfLateFromAbsensi($absensi);
    return $isLate ? 'Hadir Terlambat' : 'Hadir Tepat Waktu';
}
```

### 2. **Method `checkIfLateFromAbsensi()` yang Konsisten**

```php
private function checkIfLateFromAbsensi($absensi): bool
{
    $shift = $this->shift;
    $actualEntry = Carbon::parse($absensi->waktu_masuk);

    if ($shift->isSplitShift()) {
        // Handle split shift logic
        $currentPeriod = $shift->getCurrentPeriod($actualEntry->format('H:i:s'));
        $periods = $shift->getWorkPeriods();
        
        foreach ($periods as $period) {
            if ($period['periode'] == $currentPeriod) {
                $shiftStart = Carbon::parse($period['waktu_mulai']);
                $toleranceMinutes = $period['toleransi_keterlambatan'] ?? 0;
                
                // Set same date for comparison
                $shiftStartToday = $actualEntry->copy()
                    ->setHour($shiftStart->hour)
                    ->setMinute($shiftStart->minute)
                    ->setSecond($shiftStart->second);

                return $actualEntry->greaterThan($shiftStartToday->addMinutes($toleranceMinutes));
            }
        }
    } else {
        // Regular shift logic
        $shiftStart = Carbon::parse($shift->waktu_mulai);
        $toleranceMinutes = $shift->toleransi_keterlambatan ?? 0;
        
        // Set same date for comparison
        $shiftStartToday = $actualEntry->copy()
            ->setHour($shiftStart->hour)
            ->setMinute($shiftStart->minute)
            ->setSecond($shiftStart->second);

        return $actualEntry->greaterThan($shiftStartToday->addMinutes($toleranceMinutes));
    }
}
```

### 3. **Fallback Pencarian Absensi**

```php
// Cari absensi berdasarkan jadwal_id terlebih dahulu
$absensi = $this->absensi;

// Jika tidak ada, cari berdasarkan karyawan_id dan tanggal
if (!$absensi) {
    $absensi = Absensi::where('karyawan_id', $this->karyawan_id)
        ->whereDate('tanggal_absensi', $this->tanggal_jadwal)
        ->first();
}
```

### 4. **Debug Tools**

#### **Action Debug Status** (Environment Local)
- Tersedia di JadwalKerjaResource
- Menampilkan informasi detail tentang perhitungan status
- Menunjukkan data absensi, status database, dan hasil perhitungan

#### **Debug Information di Modal**
- Status database ditampilkan di environment local
- Informasi waktu masuk/keluar yang detail

## 🔍 Cara Troubleshooting

### 1. **Gunakan Action Debug Status**
```
1. Buka JadwalKerjaResource
2. Klik tombol "Debug Status" pada jadwal yang bermasalah
3. Periksa informasi berikut:
   - Apakah ada data absensi?
   - Apa status di database?
   - Apakah perhitungan keterlambatan benar?
   - Apa status final yang dihasilkan?
```

### 2. **Periksa Data Manual**
```sql
-- Cek data absensi untuk karyawan tertentu
SELECT 
    a.id,
    a.jadwal_id,
    a.karyawan_id,
    a.tanggal_absensi,
    a.waktu_masuk,
    a.waktu_keluar,
    a.status,
    a.periode,
    s.nama_shift,
    s.waktu_mulai,
    s.toleransi_keterlambatan
FROM absensi a
JOIN jadwal_kerja jk ON a.jadwal_id = jk.id
JOIN shift s ON jk.shift_id = s.id
WHERE a.karyawan_id = [KARYAWAN_ID]
AND DATE(a.tanggal_absensi) = '[TANGGAL]'
ORDER BY a.created_at DESC;
```

### 3. **Periksa Observer dan Service**
```php
// Cek apakah AbsensiObserver berjalan dengan benar
// Cek apakah AttendanceService mengupdate status dengan benar
// Cek log aplikasi untuk error
```

## 📋 Checklist Troubleshooting

### ✅ **Data Absensi**
- [ ] Apakah ada data absensi untuk karyawan dan tanggal tersebut?
- [ ] Apakah `jadwal_id` di absensi sesuai dengan jadwal kerja?
- [ ] Apakah `waktu_masuk` dan `waktu_keluar` ter-record dengan benar?
- [ ] Apakah field `status` di database ter-update?

### ✅ **Konfigurasi Shift**
- [ ] Apakah shift memiliki `toleransi_keterlambatan` yang benar?
- [ ] Apakah waktu shift (`waktu_mulai`, `waktu_selesai`) benar?
- [ ] Untuk split shift: apakah konfigurasi periode 2 benar?

### ✅ **Logic Perhitungan**
- [ ] Apakah method `checkIfLateFromAbsensi()` mengembalikan hasil yang benar?
- [ ] Apakah parsing waktu tidak ada error?
- [ ] Apakah timezone konsisten?

### ✅ **Eager Loading**
- [ ] Apakah relasi `absensi` ter-load dengan benar?
- [ ] Apakah relasi `shift` ter-load dengan benar?
- [ ] Apakah field yang diperlukan sudah di-select?

## 🎯 Expected Behavior

### **Skenario 1: Status Database Jelas**
```
Absensi.status = 'terlambat' → Status Dinamis = 'Hadir Terlambat'
Absensi.status = 'hadir' → Status Dinamis = 'Hadir Tepat Waktu'
```

### **Skenario 2: Status Database Tidak Jelas**
```
Absensi.status = null/other → Hitung keterlambatan
Jika terlambat → Status Dinamis = 'Hadir Terlambat'
Jika tidak terlambat → Status Dinamis = 'Hadir Tepat Waktu'
```

### **Skenario 3: Belum Keluar**
```
Ada waktu_masuk, tidak ada waktu_keluar → Tambah "(Belum Keluar)"
```

## 🚀 Next Steps

1. **Test dengan Action Debug Status** untuk beberapa jadwal yang bermasalah
2. **Periksa data manual** di database untuk memastikan konsistensi
3. **Review Observer dan Service** yang menghandle update status absensi
4. **Monitor log aplikasi** untuk error yang mungkin terjadi
5. **Update dokumentasi** berdasarkan findings

## 📝 Notes

- Debug tools hanya muncul di environment `local`
- Status dinamis tidak mengubah data di database, hanya untuk display
- Prioritas: Status DB > Perhitungan > Kondisi waktu
- Fallback mechanism memastikan selalu ada status yang ditampilkan
