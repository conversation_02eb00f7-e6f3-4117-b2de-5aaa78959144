# Unit Tests Documentation - Automatic Attendance Features

Dokumentasi ini menjelaskan unit test yang telah dibuat untuk fitur automatic attendance yang baru diimplementasikan.

## 📋 Overview Unit Tests

Telah dibuat 5 file unit test yang komprehensif untuk menguji semua komponen fitur automatic attendance:

### 1. **CutiIzinObserverTest.php** - Test untuk Observer Cuti/Izin
- **Lokasi**: `tests/Unit/CutiIzinObserverTest.php`
- **Fungsi**: Menguji automatic attendance creation ketika cuti disetujui
- **Total Test Cases**: 8 test methods

### 2. **AttendanceServiceTest.php** - Test untuk Service Attendance
- **Lokasi**: `tests/Unit/AttendanceServiceTest.php`
- **Fungsi**: Menguji logika penentuan status dan perhitungan keterlambatan
- **Total Test Cases**: 12 test methods

### 3. **AbsensiObserverTest.php** - Test untuk Observer Absensi
- **Lokasi**: `tests/Unit/AbsensiObserverTest.php`
- **Fungsi**: Menguji automatic status update pada attendance records
- **Total Test Cases**: 11 test methods

### 4. **ProcessApprovedLeaveCommandTest.php** - Test untuk Command Leave
- **Lokasi**: `tests/Unit/ProcessApprovedLeaveCommandTest.php`
- **Fungsi**: Menguji command untuk memproses approved leave
- **Total Test Cases**: 9 test methods

### 5. **ProcessAttendanceStatusCommandTest.php** - Test untuk Command Status
- **Lokasi**: `tests/Unit/ProcessAttendanceStatusCommandTest.php`
- **Fungsi**: Menguji command untuk update attendance status
- **Total Test Cases**: 8 test methods

## 🧪 Detail Test Cases

### CutiIzinObserverTest

#### Test Cases:
1. **it_creates_attendance_records_when_leave_is_approved**
   - Menguji pembuatan attendance records otomatis saat cuti disetujui
   - Verifikasi jumlah record yang dibuat sesuai periode cuti

2. **it_sets_correct_status_based_on_leave_type**
   - Menguji status yang tepat berdasarkan jenis permohonan (cuti/izin/sakit)
   - Verifikasi mapping jenis_permohonan ke status attendance

3. **it_updates_existing_attendance_records_with_generic_status**
   - Menguji update record attendance yang sudah ada dengan status generic
   - Verifikasi record dengan status 'hadir'/'terlambat' diupdate

4. **it_skips_existing_attendance_records_with_special_status**
   - Menguji bahwa record dengan status khusus tidak diubah
   - Verifikasi record dengan status 'cuti'/'izin'/'sakit' dipertahankan

5. **it_does_not_create_records_when_status_is_not_approved**
   - Menguji bahwa tidak ada record dibuat jika status bukan 'approved'
   - Verifikasi untuk status 'pending' dan 'rejected'

6. **it_links_attendance_to_schedule_when_available**
   - Menguji linking attendance record ke jadwal yang tersedia
   - Verifikasi jadwal_id diset dengan benar

7. **it_handles_leave_spanning_multiple_days**
   - Menguji pembuatan record untuk cuti multi-hari
   - Verifikasi semua tanggal dalam periode cuti diproses

### AttendanceServiceTest

#### Test Cases:
1. **it_determines_on_time_status_for_regular_shift**
   - Menguji penentuan status 'hadir' untuk shift regular
   - Test untuk tepat waktu, dalam toleransi, dan batas toleransi

2. **it_determines_late_status_for_regular_shift**
   - Menguji penentuan status 'terlambat' untuk shift regular
   - Test untuk melewati toleransi dan sangat terlambat

3. **it_determines_status_for_split_shift**
   - Menguji penentuan status untuk split shift
   - Test untuk periode 1 dan periode 2 dengan toleransi berbeda

4. **it_calculates_lateness_minutes_correctly**
   - Menguji perhitungan menit keterlambatan yang akurat
   - Verifikasi perhitungan setelah dikurangi toleransi

5. **it_calculates_zero_lateness_for_on_time_attendance**
   - Menguji perhitungan 0 menit untuk attendance tepat waktu
   - Verifikasi tidak ada keterlambatan dalam toleransi

6. **it_calculates_lateness_deduction_with_fixed_amount_rule**
   - Menguji perhitungan denda dengan aturan nominal tetap
   - Verifikasi penerapan AturanKeterlambatan jenis 'nominal_tetap'

7. **it_calculates_lateness_deduction_with_per_minute_rule**
   - Menguji perhitungan denda per menit
   - Verifikasi penerapan AturanKeterlambatan jenis 'per_menit'

8. **it_calculates_lateness_deduction_with_percentage_rule**
   - Menguji perhitungan denda persentase gaji
   - Verifikasi penerapan AturanKeterlambatan jenis 'persentase_gaji'

9. **it_returns_zero_deduction_for_on_time_attendance**
   - Menguji tidak ada denda untuk attendance tepat waktu
   - Verifikasi return value untuk attendance dalam toleransi

10. **it_returns_default_status_when_no_schedule_exists**
    - Menguji default status ketika tidak ada jadwal
    - Verifikasi fallback ke status 'hadir'

11. **it_handles_attendance_without_schedule_in_lateness_calculation**
    - Menguji perhitungan keterlambatan tanpa jadwal
    - Verifikasi return 0 menit keterlambatan

12. **it_processes_attendance_record_completely**
    - Menguji proses lengkap attendance record
    - Verifikasi update status dan perhitungan denda

### AbsensiObserverTest

#### Test Cases:
1. **it_automatically_sets_status_when_creating_on_time_attendance**
   - Menguji automatic status setting untuk attendance tepat waktu
   - Verifikasi status 'hadir' dipertahankan

2. **it_automatically_sets_late_status_when_creating_late_attendance**
   - Menguji automatic status setting untuk attendance terlambat
   - Verifikasi status diubah ke 'terlambat'

3. **it_preserves_special_status_when_creating_attendance**
   - Menguji preservasi status khusus (cuti/izin/sakit)
   - Verifikasi status khusus tidak diubah meski waktu terlambat

4. **it_updates_status_when_waktu_masuk_is_modified**
   - Menguji update status ketika waktu_masuk diubah
   - Verifikasi observer bekerja pada update record

5. **it_does_not_update_special_status_when_waktu_masuk_changes**
   - Menguji bahwa status khusus tidak berubah saat waktu diubah
   - Verifikasi preservasi status cuti/izin/sakit

6. **it_handles_attendance_without_schedule**
   - Menguji handling attendance tanpa jadwal
   - Verifikasi default behavior

7. **it_works_with_split_shift_period_2**
   - Menguji observer dengan split shift periode 2
   - Verifikasi toleransi berbeda per periode

### ProcessApprovedLeaveCommandTest

#### Test Cases:
1. **it_processes_approved_leave_requests**
   - Menguji command dalam mode dry-run
   - Verifikasi tidak ada perubahan dalam dry-run

2. **it_creates_attendance_records_for_approved_leave**
   - Menguji pembuatan attendance records via command
   - Verifikasi record dibuat dengan status yang benar

3. **it_processes_different_leave_types_correctly**
   - Menguji berbagai jenis permohonan (cuti/izin/sakit)
   - Verifikasi mapping status yang tepat

4. **it_updates_existing_attendance_with_generic_status**
   - Menguji update record existing via command
   - Verifikasi record generic status diupdate

5. **it_skips_existing_attendance_with_special_status**
   - Menguji skip record dengan status khusus
   - Verifikasi record khusus tidak diubah

6. **it_processes_leave_spanning_multiple_days**
   - Menguji pemrosesan cuti multi-hari
   - Verifikasi semua hari dalam periode diproses

7. **it_ignores_pending_leave_requests**
   - Menguji bahwa cuti pending diabaikan
   - Verifikasi hanya approved yang diproses

8. **it_ignores_rejected_leave_requests**
   - Menguji bahwa cuti rejected diabaikan
   - Verifikasi status selain approved diabaikan

9. **it_links_attendance_to_schedule_when_available**
   - Menguji linking ke jadwal via command
   - Verifikasi jadwal_id diset dengan benar

### ProcessAttendanceStatusCommandTest

#### Test Cases:
1. **it_processes_attendance_status_in_dry_run_mode**
   - Menguji command dalam mode dry-run
   - Verifikasi tidak ada perubahan dalam dry-run

2. **it_updates_attendance_status_correctly**
   - Menguji update status attendance yang salah
   - Verifikasi koreksi status berdasarkan timing

3. **it_processes_multiple_attendance_records**
   - Menguji pemrosesan multiple records sekaligus
   - Verifikasi batch processing

4. **it_skips_special_status_attendance_records**
   - Menguji skip record dengan status khusus
   - Verifikasi cuti/izin/sakit tidak diubah

5. **it_skips_attendance_without_waktu_masuk**
   - Menguji skip record tanpa waktu masuk
   - Verifikasi record incomplete diabaikan

6. **it_handles_attendance_without_schedule**
   - Menguji handling record tanpa jadwal
   - Verifikasi default behavior

7. **it_processes_split_shift_attendance**
   - Menguji pemrosesan split shift
   - Verifikasi toleransi berbeda per periode

8. **it_shows_summary_statistics**
   - Menguji output statistik command
   - Verifikasi summary yang informatif

## 🚀 Cara Menjalankan Tests

### Menjalankan Semua Unit Tests
```bash
php artisan test tests/Unit/ --verbose
```

### Menjalankan Test Specific
```bash
# Test CutiIzinObserver
php artisan test tests/Unit/CutiIzinObserverTest.php

# Test AttendanceService
php artisan test tests/Unit/AttendanceServiceTest.php

# Test AbsensiObserver
php artisan test tests/Unit/AbsensiObserverTest.php

# Test Commands
php artisan test tests/Unit/ProcessApprovedLeaveCommandTest.php
php artisan test tests/Unit/ProcessAttendanceStatusCommandTest.php
```

### Menjalankan Test dengan Coverage
```bash
php artisan test tests/Unit/ --coverage
```

## 📊 Test Coverage

Unit tests ini mencakup:

### ✅ **Functional Coverage**
- ✓ Automatic attendance creation untuk approved leave
- ✓ Status determination berdasarkan timing dan toleransi
- ✓ Lateness calculation dengan berbagai aturan
- ✓ Observer functionality untuk create dan update
- ✓ Command functionality dengan dry-run mode
- ✓ Error handling dan edge cases

### ✅ **Scenario Coverage**
- ✓ Regular shift dan split shift
- ✓ Berbagai jenis permohonan (cuti/izin/sakit)
- ✓ Multiple aturan keterlambatan
- ✓ Existing records dengan berbagai status
- ✓ Records tanpa jadwal atau waktu masuk
- ✓ Multi-day leave periods

### ✅ **Edge Cases Coverage**
- ✓ Null values dan missing data
- ✓ Invalid status combinations
- ✓ Boundary conditions (tepat di batas toleransi)
- ✓ Conflicting existing records
- ✓ Command dengan berbagai parameter

## 🎯 Benefits Unit Tests

1. **Confidence**: Memastikan semua fitur bekerja sesuai ekspektasi
2. **Regression Prevention**: Mencegah bug saat ada perubahan kode
3. **Documentation**: Test cases berfungsi sebagai dokumentasi behavior
4. **Refactoring Safety**: Aman melakukan refactor dengan test coverage
5. **Quality Assurance**: Memastikan kualitas kode yang tinggi

## 📝 Maintenance

Unit tests ini harus dijalankan:
- ✅ Sebelum deploy ke production
- ✅ Setelah ada perubahan pada fitur attendance
- ✅ Saat menambah aturan keterlambatan baru
- ✅ Saat mengubah logika shift atau toleransi
- ✅ Sebagai bagian dari CI/CD pipeline

Tests ini memberikan confidence penuh bahwa semua fitur automatic attendance bekerja dengan benar dan reliable.
