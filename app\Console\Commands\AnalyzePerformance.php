<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AnalyzePerformance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:analyze-performance {--table=} {--check-indexes} {--check-queries}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Analyze database performance and suggest optimizations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Analyzing Database Performance...');
        $this->newLine();

        if ($this->option('check-indexes')) {
            $this->checkIndexes();
        }

        if ($this->option('check-queries')) {
            $this->checkSlowQueries();
        }

        if (!$this->option('check-indexes') && !$this->option('check-queries')) {
            $this->checkIndexes();
            $this->checkSlowQueries();
            $this->checkTableSizes();
            $this->suggestOptimizations();
        }

        $this->newLine();
        $this->info('✅ Performance analysis completed!');
    }

    /**
     * Check for missing indexes
     */
    protected function checkIndexes()
    {
        $this->info('📊 Checking Database Indexes...');
        $this->newLine();

        $tables = [
            'karyawan' => ['id_entitas', 'id_departemen', 'id_divisi', 'id_jabatan', 'supervisor_id', 'status_aktif'],
            'absensi' => ['karyawan_id', 'tanggal_absensi', 'status', 'approved_by'],
            'jadwal_kerja' => ['karyawan_id', 'shift_id', 'tanggal_jadwal', 'supervisor_id'],
            'penggajian_karyawan' => ['karyawan_id', 'periode_gaji'],
            'riwayat_kontrak' => ['karyawan_id', 'tanggal_mulai', 'tanggal_selesai'],
        ];

        foreach ($tables as $table => $columns) {
            if (!Schema::hasTable($table)) {
                $this->warn("⚠️  Table '{$table}' does not exist");
                continue;
            }

            $this->line("🔍 Analyzing table: {$table}");
            
            $indexes = $this->getTableIndexes($table);
            $indexedColumns = collect($indexes)->pluck('column_name')->toArray();

            foreach ($columns as $column) {
                if (Schema::hasColumn($table, $column)) {
                    if (!in_array($column, $indexedColumns)) {
                        $this->warn("  ❌ Missing index on column: {$column}");
                    } else {
                        $this->info("  ✅ Index exists on column: {$column}");
                    }
                } else {
                    $this->warn("  ⚠️  Column '{$column}' does not exist in table '{$table}'");
                }
            }
            $this->newLine();
        }
    }

    /**
     * Get table indexes
     */
    protected function getTableIndexes(string $table): array
    {
        try {
            return DB::select("SHOW INDEX FROM {$table}");
        } catch (\Exception $e) {
            $this->error("Error getting indexes for table {$table}: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Check for slow queries
     */
    protected function checkSlowQueries()
    {
        $this->info('🐌 Checking for Slow Query Patterns...');
        $this->newLine();

        // Check for queries without WHERE clauses on large tables
        $largeTables = ['karyawan', 'absensi', 'jadwal_kerja', 'penggajian_karyawan'];
        
        foreach ($largeTables as $table) {
            if (Schema::hasTable($table)) {
                $count = DB::table($table)->count();
                $this->line("📊 Table '{$table}': {$count} records");
                
                if ($count > 1000) {
                    $this->warn("  ⚠️  Large table - ensure queries use proper WHERE clauses and indexes");
                }
            }
        }

        $this->newLine();
        $this->info('💡 Query Optimization Tips:');
        $this->line('  • Use SELECT with specific columns instead of SELECT *');
        $this->line('  • Add WHERE clauses to limit result sets');
        $this->line('  • Use eager loading for relationships');
        $this->line('  • Consider pagination for large datasets');
        $this->line('  • Use indexes on frequently queried columns');
    }

    /**
     * Check table sizes
     */
    protected function checkTableSizes()
    {
        $this->info('📏 Checking Table Sizes...');
        $this->newLine();

        try {
            $sizes = DB::select("
                SELECT 
                    table_name,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                    table_rows
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                ORDER BY (data_length + index_length) DESC
                LIMIT 10
            ");

            $this->table(['Table', 'Size (MB)', 'Rows'], array_map(function($size) {
                return [$size->table_name, $size->size_mb, $size->table_rows];
            }, $sizes));

        } catch (\Exception $e) {
            $this->warn('Could not retrieve table sizes: ' . $e->getMessage());
        }
    }

    /**
     * Suggest optimizations
     */
    protected function suggestOptimizations()
    {
        $this->newLine();
        $this->info('🚀 Optimization Suggestions:');
        $this->newLine();

        $suggestions = [
            '1. Database Indexes' => [
                'Run the migration: php artisan migrate (for new indexes)',
                'Monitor slow query log',
                'Use EXPLAIN to analyze query execution plans'
            ],
            '2. Query Optimization' => [
                'Use eager loading: ->with([\'relation\'])',
                'Implement pagination for large datasets',
                'Use select() to limit columns returned',
                'Cache frequently accessed data'
            ],
            '3. Application Level' => [
                'Enable query caching',
                'Use Redis for session storage',
                'Implement database connection pooling',
                'Consider read replicas for heavy read workloads'
            ],
            '4. Monitoring' => [
                'Enable slow query logging',
                'Monitor database performance metrics',
                'Set up alerts for long-running queries',
                'Regular database maintenance'
            ]
        ];

        foreach ($suggestions as $category => $items) {
            $this->line("<fg=yellow>{$category}:</>");
            foreach ($items as $item) {
                $this->line("  • {$item}");
            }
            $this->newLine();
        }
    }
}
