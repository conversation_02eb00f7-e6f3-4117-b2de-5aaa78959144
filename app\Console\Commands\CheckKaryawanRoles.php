<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;

class CheckKaryawanRoles extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'check:karyawan-roles';

    /**
     * The console command description.
     */
    protected $description = 'Check the status of karyawan roles (both user.role and Shield roles)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== KARYAWAN ROLE STATUS CHECK ===');

        // Check users with role='karyawan' in users table
        $usersWithKaryawanRole = User::where('role', 'karyawan')->get();
        $this->info("\n1. Users with role='karyawan' in users table: " . $usersWithKaryawanRole->count());

        // Check Shield karyawan role
        $shieldKaryawanRole = Role::where('name', 'karyawan')->first();
        if ($shieldKaryawanRole) {
            $this->info("2. Shield 'karyawan' role exists: ✅");
            $this->info("   - Permissions count: " . $shieldKaryawanRole->permissions->count());
        } else {
            $this->error("2. Shield 'karyawan' role exists: ❌");
            return;
        }

        // Check users assigned to Shield karyawan role
        $usersWithShieldRole = User::role('karyawan')->get();
        $this->info("3. Users assigned to Shield 'karyawan' role: " . $usersWithShieldRole->count());

        // Find mismatches
        $this->info("\n=== MISMATCH ANALYSIS ===");
        
        $usersNeedingShieldRole = [];
        $usersWithBothRoles = [];

        foreach ($usersWithKaryawanRole as $user) {
            if ($user->hasRole('karyawan')) {
                $usersWithBothRoles[] = $user;
            } else {
                $usersNeedingShieldRole[] = $user;
            }
        }

        $this->info("Users with BOTH role='karyawan' AND Shield role: " . count($usersWithBothRoles));
        $this->warn("Users with role='karyawan' but NO Shield role: " . count($usersNeedingShieldRole));

        if (!empty($usersNeedingShieldRole)) {
            $this->warn("\nUsers needing Shield role assignment:");
            foreach ($usersNeedingShieldRole as $user) {
                $this->warn("  - {$user->name} ({$user->email})");
            }
            
            $this->info("\n💡 Run this to fix: php artisan db:seed --class=SyncKaryawanRolesSeeder");
        } else {
            $this->info("\n✅ All users with role='karyawan' have Shield karyawan role!");
        }

        // Show key permissions for karyawan role
        $this->info("\n=== KARYAWAN PERMISSIONS ===");
        $permissions = $shieldKaryawanRole->permissions->pluck('name')->toArray();
        
        $keyPermissions = [
            'view_absensi' => 'View attendance records',
            'create_absensi' => 'Create attendance (needed for /create)',
            'view_karyawan' => 'View employee data',
            'view_schedule' => 'View schedules'
        ];

        foreach ($keyPermissions as $perm => $description) {
            $hasIt = in_array($perm, $permissions) ? '✅' : '❌';
            $this->info("  {$hasIt} {$perm} - {$description}");
        }

        return 0;
    }
}
