<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\PayrollTransaction;
use App\Models\PenggajianKaryawan;

class CreateRiwayatGajiFromPayroll extends Command
{
    protected $signature = 'payroll:create-riwayat-gaji';
    protected $description = 'Create riwayat gaji from approved/paid payroll transactions';

    public function handle()
    {
        $this->info('Creating riwayat gaji from payroll transactions...');

        // Get all approved or paid payroll transactions
        $payrolls = PayrollTransaction::whereIn('status', ['approved', 'paid'])
            ->with(['karyawan', 'payrollPeriod'])
            ->get();

        $this->info("Found {$payrolls->count()} payroll transactions");

        $created = 0;
        $existing = 0;

        foreach ($payrolls as $payroll) {
            // Check if record already exists
            $existingRecord = PenggajianKaryawan::where('karyawan_id', $payroll->karyawan_id)
                ->where('periode_gaji', $payroll->payrollPeriod->nama_periode)
                ->first();

            if (!$existingRecord) {
                try {
                    PenggajianKaryawan::create([
                        'karyawan_id' => $payroll->karyawan_id,
                        'no_penggajian' => $payroll->no_payroll,
                        'periode_gaji' => $payroll->payrollPeriod->nama_periode,
                        'gaji_pokok' => $payroll->gaji_pokok,
                        'tunjangan_jabatan' => $payroll->tunjangan_jabatan,
                        'tunjangan_umum' => $payroll->tunjangan_umum,
                        'tunjangan_sembako' => $payroll->tunjangan_sembako,
                        'bpjs_kesehatan_dipotong' => $payroll->potongan_bpjs_kesehatan,
                        'bpjs_tk_dipotong' => $payroll->potongan_bpjs_tk,
                        'potongan_lainnya' => $payroll->potongan_keterlambatan + $payroll->potongan_pelanggaran + $payroll->potongan_lainnya,
                        'keterangan' => "Hasil payroll periode {$payroll->payrollPeriod->nama_periode} - Created via command",
                        'periode_gaji' => $payroll->payrollPeriod->nama_periode,
                    ]);

                    $this->info("✓ Created riwayat gaji for {$payroll->karyawan->nama_lengkap}");
                    $created++;
                } catch (\Exception $e) {
                    $this->error("✗ Failed to create for {$payroll->karyawan->nama_lengkap}: {$e->getMessage()}");
                }
            } else {
                $this->line("- Already exists for {$payroll->karyawan->nama_lengkap}");
                $existing++;
            }
        }

        $this->info("\nSummary:");
        $this->info("Created: {$created}");
        $this->info("Already existing: {$existing}");
        $this->info("Total processed: " . ($created + $existing));
    }
}
