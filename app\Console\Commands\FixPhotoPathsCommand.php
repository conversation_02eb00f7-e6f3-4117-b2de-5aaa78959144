<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixPhotoPathsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:photo-paths';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix photo paths in absensi records from old attendance/ to new absensi/masuk/ and absensi/keluar/';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Fixing photo paths in absensi records...');

        // Count records with wrong paths
        $wrongPathCount = DB::table('absensi')
            ->where(function ($query) {
                $query->where('foto_masuk', 'LIKE', 'attendance/%')
                    ->orWhere('foto_keluar', 'LIKE', 'attendance/%');
            })
            ->count();

        if ($wrongPathCount === 0) {
            $this->info('✅ No records found with wrong photo paths.');
            return;
        }

        $this->info("📊 Found {$wrongPathCount} records with wrong photo paths.");

        if (!$this->confirm('Do you want to fix these paths?')) {
            $this->info('❌ Operation cancelled.');
            return;
        }

        // Update foto_masuk paths
        $masukUpdated = DB::table('absensi')
            ->where('foto_masuk', 'LIKE', 'attendance/%')
            ->update([
                'foto_masuk' => DB::raw("REPLACE(foto_masuk, 'attendance/', 'absensi/masuk/')")
            ]);

        // Update foto_keluar paths
        $keluarUpdated = DB::table('absensi')
            ->where('foto_keluar', 'LIKE', 'attendance/%')
            ->update([
                'foto_keluar' => DB::raw("REPLACE(foto_keluar, 'attendance/', 'absensi/keluar/')")
            ]);

        $this->info("✅ Updated {$masukUpdated} foto_masuk paths.");
        $this->info("✅ Updated {$keluarUpdated} foto_keluar paths.");

        // Verify the fix
        $remainingWrongPaths = DB::table('absensi')
            ->where(function ($query) {
                $query->where('foto_masuk', 'LIKE', 'attendance/%')
                    ->orWhere('foto_keluar', 'LIKE', 'attendance/%');
            })
            ->count();

        if ($remainingWrongPaths === 0) {
            $this->info('🎉 All photo paths have been fixed successfully!');
        } else {
            $this->warn("⚠️  Still {$remainingWrongPaths} records with wrong paths remaining.");
        }

        // Show sample of fixed records
        $this->info('📋 Sample of fixed records:');
        $samples = DB::table('absensi')
            ->where(function ($query) {
                $query->where('foto_masuk', 'LIKE', 'absensi/masuk/%')
                    ->orWhere('foto_keluar', 'LIKE', 'absensi/keluar/%');
            })
            ->select('id', 'foto_masuk', 'foto_keluar')
            ->limit(5)
            ->get();

        foreach ($samples as $sample) {
            $this->line("ID: {$sample->id} | Masuk: {$sample->foto_masuk} | Keluar: {$sample->foto_keluar}");
        }
    }
}
