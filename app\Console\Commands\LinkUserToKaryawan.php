<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Karyawan;

class LinkUserToKaryawan extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:link-user-to-karyawan {user_id} {karyawan_id} {--role=karyawan}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Link a user to a karyawan record and set their role';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        $karyawanId = $this->argument('karyawan_id');
        $role = $this->option('role');

        // Validate role
        if (!in_array($role, ['admin', 'supervisor', 'karyawan'])) {
            $this->error('Invalid role. Must be one of: admin, supervisor, karyawan');
            return 1;
        }

        // Find user
        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return 1;
        }

        // Find karyawan
        $karyawan = Karyawan::find($karyawanId);
        if (!$karyawan) {
            $this->error("Karyawan with ID {$karyawanId} not found");
            return 1;
        }

        // Update user role
        $user->role = $role;
        $user->save();

        // Link karyawan to user
        $karyawan->id_user = $user->id;
        $karyawan->save();

        $this->info("Successfully linked user {$user->name} to karyawan {$karyawan->nama_lengkap} with role {$role}");
        return 0;
    }
}
