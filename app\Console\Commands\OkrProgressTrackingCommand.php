<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\OkrProgressTracker;

class OkrProgressTrackingCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'okr:track-progress {--notify : Send notifications for items needing attention}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Track OKR progress and send notifications for items needing attention';

    /**
     * Execute the console command.
     */
    public function handle(OkrProgressTracker $tracker)
    {
        $this->info('Starting OKR progress tracking...');

        // Update all progress
        $tracker->updateAllProgress();
        $this->info('✅ Progress updated for all objectives');

        // Send notifications if requested
        if ($this->option('notify')) {
            $this->info('Checking for items needing attention...');
            $tracker->checkAndNotify();
            $this->info('✅ Notifications sent for items needing attention');
        }

        // Display summary
        $summary = $tracker->getProgressSummary();

        $this->newLine();
        $this->info('📊 Progress Summary:');
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Objectives', $summary['total_objectives']],
                ['Completed Objectives', $summary['completed_objectives']],
                ['Overdue Objectives', $summary['overdue_objectives']],
                ['At Risk Key Results', $summary['at_risk_key_results']],
                ['Blocked Tactics', $summary['blocked_tactics']],
                ['Overall Progress', $summary['overall_progress'] . '%'],
            ]
        );

        // Show items needing attention
        $attention = $tracker->getObjectivesNeedingAttention();

        if ($attention['overdue']->count() > 0) {
            $this->newLine();
            $this->warn('🚨 Overdue Objectives (' . $attention['overdue']->count() . '):');
            foreach ($attention['overdue'] as $objective) {
                $this->line('  - ' . $objective->nama_objective . ' (Owner: ' . $objective->owner->name . ')');
            }
        }

        if ($attention['at_risk']->count() > 0) {
            $this->newLine();
            $this->warn('⚠️  At Risk Objectives (' . $attention['at_risk']->count() . '):');
            foreach ($attention['at_risk'] as $objective) {
                $this->line('  - ' . $objective->nama_objective . ' (' . $objective->progress_percentage . '% progress)');
            }
        }

        if ($attention['stalled']->count() > 0) {
            $this->newLine();
            $this->warn('⏸️  Stalled Objectives (' . $attention['stalled']->count() . '):');
            foreach ($attention['stalled'] as $objective) {
                $this->line('  - ' . $objective->nama_objective . ' (Last updated: ' . $objective->updated_at->diffForHumans() . ')');
            }
        }

        $this->newLine();
        $this->info('✅ OKR progress tracking completed!');

        return Command::SUCCESS;
    }
}
