<?php

namespace App\Console\Commands;

use App\Models\CutiIzin;
use App\Models\Absensi;
use App\Models\Schedule;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ProcessApprovedLeave extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:process-approved-leave 
                            {--date= : Process leave for specific date (Y-m-d format)}
                            {--days=30 : Number of days to process from today backwards}
                            {--dry-run : Show what would be created without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process approved leave requests to create attendance records';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $date = $this->option('date');
        $days = (int) $this->option('days');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('🔍 DRY RUN MODE - No changes will be made');
        }

        $this->info('🚀 Processing approved leave requests...');

        $query = CutiIzin::with(['karyawan'])
            ->where('status', 'approved');

        if ($date) {
            $query->where(function ($q) use ($date) {
                $q->whereDate('tanggal_mulai', '<=', $date)
                  ->whereDate('tanggal_selesai', '>=', $date);
            });
            $this->info("📅 Processing leave for date: {$date}");
        } else {
            $startDate = now()->subDays($days)->format('Y-m-d');
            $endDate = now()->format('Y-m-d');
            $query->where(function ($q) use ($startDate, $endDate) {
                $q->whereBetween('tanggal_mulai', [$startDate, $endDate])
                  ->orWhereBetween('tanggal_selesai', [$startDate, $endDate])
                  ->orWhere(function ($subQ) use ($startDate, $endDate) {
                      $subQ->where('tanggal_mulai', '<=', $startDate)
                           ->where('tanggal_selesai', '>=', $endDate);
                  });
            });
            $this->info("📅 Processing leave for last {$days} days ({$startDate} to {$endDate})");
        }

        $leaveRequests = $query->get();
        $totalRequests = $leaveRequests->count();

        if ($totalRequests === 0) {
            $this->warn('⚠️  No approved leave requests found to process');
            return;
        }

        $this->info("📊 Found {$totalRequests} approved leave requests to process");

        $createdCount = 0;
        $updatedCount = 0;
        $skippedCount = 0;
        $errorCount = 0;

        foreach ($leaveRequests as $leave) {
            try {
                $this->info("Processing leave for {$leave->karyawan->nama_lengkap} ({$leave->jenis_permohonan})");
                
                $result = $this->processLeaveRequest($leave, $dryRun);
                $createdCount += $result['created'];
                $updatedCount += $result['updated'];
                $skippedCount += $result['skipped'];

            } catch (\Exception $e) {
                $errorCount++;
                $this->error("❌ Error processing leave ID {$leave->id}: " . $e->getMessage());
            }
        }

        $this->newLine();

        // Summary
        $this->info('✅ Processing completed!');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Leave Requests Processed', $totalRequests],
                ['Attendance Records Created', $createdCount],
                ['Attendance Records Updated', $updatedCount],
                ['Records Skipped', $skippedCount],
                ['Errors', $errorCount],
            ]
        );

        if ($dryRun && ($createdCount > 0 || $updatedCount > 0)) {
            $this->warn("🔄 Run without --dry-run to apply changes");
        }

        return 0;
    }

    /**
     * Process a single leave request
     */
    private function processLeaveRequest(CutiIzin $leave, bool $dryRun): array
    {
        $startDate = Carbon::parse($leave->tanggal_mulai);
        $endDate = Carbon::parse($leave->tanggal_selesai);
        $karyawanId = $leave->karyawan_id;
        
        // Determine status based on leave type
        $status = match($leave->jenis_permohonan) {
            'cuti' => 'cuti',
            'izin' => 'izin',
            'sakit' => 'sakit',
            default => 'cuti'
        };

        $created = 0;
        $updated = 0;
        $skipped = 0;

        $currentDate = $startDate->copy();
        while ($currentDate->lte($endDate)) {
            // Check if attendance record already exists for this date
            $existingAttendance = Absensi::where('karyawan_id', $karyawanId)
                ->whereDate('tanggal_absensi', $currentDate->format('Y-m-d'))
                ->first();

            if ($existingAttendance) {
                // Update existing record if it's not already processed
                if (in_array($existingAttendance->status, ['hadir', 'terlambat', 'alpha'])) {
                    if (!$dryRun) {
                        $existingAttendance->update([
                            'status' => $status,
                            'keterangan' => "Auto-generated from approved {$leave->jenis_permohonan}: {$leave->alasan}",
                            'approved_by' => $leave->approved_by,
                            'approved_at' => $leave->approved_at,
                        ]);
                    }
                    $updated++;
                    
                    if ($this->output->isVerbose()) {
                        $this->line("  📝 Updated attendance for {$currentDate->format('Y-m-d')}");
                    }
                } else {
                    $skipped++;
                    if ($this->output->isVerbose()) {
                        $this->line("  ⏭️  Skipped {$currentDate->format('Y-m-d')} (status: {$existingAttendance->status})");
                    }
                }
            } else {
                // Find schedule for this date
                $schedule = Schedule::where('karyawan_id', $karyawanId)
                    ->whereDate('tanggal_jadwal', $currentDate->format('Y-m-d'))
                    ->first();

                // Create new attendance record
                if (!$dryRun) {
                    Absensi::create([
                        'karyawan_id' => $karyawanId,
                        'jadwal_id' => $schedule?->id,
                        'tanggal_absensi' => $currentDate->format('Y-m-d'),
                        'status' => $status,
                        'keterangan' => "Auto-generated from approved {$leave->jenis_permohonan}: {$leave->alasan}",
                        'approved_by' => $leave->approved_by,
                        'approved_at' => $leave->approved_at,
                        'periode' => 1, // Default to period 1
                    ]);
                }
                $created++;
                
                if ($this->output->isVerbose()) {
                    $this->line("  ✅ Created attendance for {$currentDate->format('Y-m-d')}");
                }
            }

            $currentDate->addDay();
        }

        return [
            'created' => $created,
            'updated' => $updated,
            'skipped' => $skipped
        ];
    }
}
