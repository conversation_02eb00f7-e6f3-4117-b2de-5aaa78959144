<?php

namespace App\Console\Commands;

use App\Models\Absensi;
use App\Services\AttendanceService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ProcessAttendanceStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:process-status 
                            {--date= : Process attendance for specific date (Y-m-d format)}
                            {--days=30 : Number of days to process from today backwards}
                            {--dry-run : Show what would be updated without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process attendance records to update status based on lateness rules';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $date = $this->option('date');
        $days = (int) $this->option('days');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('🔍 DRY RUN MODE - No changes will be made');
        }

        $this->info('🚀 Processing attendance status updates...');

        $query = Absensi::with(['jadwal.shift', 'karyawan'])
            ->whereNotNull('waktu_masuk')
            ->whereNotIn('status', ['cuti', 'izin', 'sakit']); // Don't process special statuses

        if ($date) {
            $query->whereDate('tanggal_absensi', $date);
            $this->info("📅 Processing attendance for date: {$date}");
        } else {
            $startDate = now()->subDays($days)->format('Y-m-d');
            $endDate = now()->format('Y-m-d');
            $query->whereBetween('tanggal_absensi', [$startDate, $endDate]);
            $this->info("📅 Processing attendance for last {$days} days ({$startDate} to {$endDate})");
        }

        $attendanceRecords = $query->get();
        $totalRecords = $attendanceRecords->count();

        if ($totalRecords === 0) {
            $this->warn('⚠️  No attendance records found to process');
            return;
        }

        $this->info("📊 Found {$totalRecords} attendance records to process");

        $updatedCount = 0;
        $lateCount = 0;
        $onTimeCount = 0;
        $errorCount = 0;

        $progressBar = $this->output->createProgressBar($totalRecords);
        $progressBar->start();

        foreach ($attendanceRecords as $attendance) {
            try {
                $oldStatus = $attendance->status;
                $newStatus = AttendanceService::determineAttendanceStatus(
                    $attendance->karyawan_id,
                    $attendance->waktu_masuk,
                    $attendance->periode ?? 1
                );

                if ($oldStatus !== $newStatus) {
                    if (!$dryRun) {
                        $attendance->update(['status' => $newStatus]);
                    }
                    
                    $updatedCount++;
                    
                    if ($this->output->isVerbose()) {
                        $this->line("\n📝 Updated: {$attendance->karyawan->nama_lengkap} ({$attendance->tanggal_absensi}) - {$oldStatus} → {$newStatus}");
                    }
                }

                if ($newStatus === 'terlambat') {
                    $lateCount++;
                } else {
                    $onTimeCount++;
                }

            } catch (\Exception $e) {
                $errorCount++;
                if ($this->output->isVerbose()) {
                    $this->error("\n❌ Error processing attendance ID {$attendance->id}: " . $e->getMessage());
                }
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        // Summary
        $this->info('✅ Processing completed!');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Records Processed', $totalRecords],
                ['Records Updated', $updatedCount],
                ['Late Attendances', $lateCount],
                ['On-Time Attendances', $onTimeCount],
                ['Errors', $errorCount],
            ]
        );

        if ($dryRun && $updatedCount > 0) {
            $this->warn("🔄 Run without --dry-run to apply {$updatedCount} updates");
        }

        return 0;
    }
}
