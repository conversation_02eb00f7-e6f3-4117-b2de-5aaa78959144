<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Customer;
use Carbon\Carbon;

class SegmentCustomers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:segment-customers {--dry-run : Show what would be changed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically segment customers based on their transaction behavior';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting customer segmentation...');

        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        $customers = Customer::with(['posTransactions'])->get();
        $segmentedCount = 0;

        $this->withProgressBar($customers, function ($customer) use ($dryRun, &$segmentedCount) {
            $newSegment = $this->determineCustomerSegment($customer);

            if ($customer->segment !== $newSegment) {
                if (!$dryRun) {
                    $customer->update(['segment' => $newSegment]);
                }
                $segmentedCount++;

                if ($dryRun) {
                    $this->newLine();
                    $this->line("Customer {$customer->nama}: {$customer->segment} -> {$newSegment}");
                }
            }
        });

        $this->newLine(2);

        if ($dryRun) {
            $this->info("Would update {$segmentedCount} customers");
        } else {
            $this->info("Successfully updated {$segmentedCount} customers");
        }

        // Show segment distribution
        $this->showSegmentDistribution();

        return Command::SUCCESS;
    }

    /**
     * Determine customer segment based on transaction behavior
     */
    private function determineCustomerSegment(Customer $customer): string
    {
        $transactions = $customer->posTransactions;

        if ($transactions->isEmpty()) {
            return 'new_customer';
        }

        $totalSpent = $transactions->sum('net_amount');
        $transactionCount = $transactions->count();
        $lastTransactionDate = $transactions->max('transaction_date');
        $avgTransactionValue = $transactionCount > 0 ? $totalSpent / $transactionCount : 0;

        // Calculate days since last transaction
        $daysSinceLastTransaction = $lastTransactionDate
            ? Carbon::parse($lastTransactionDate)->diffInDays(now())
            : 999;

        // Top Spenders: High total spending (>= 5,000,000) regardless of frequency
        if ($totalSpent >= 5000000) {
            return 'top_spenders';
        }

        // Lapsed Customers: No transaction in last 180 days
        if ($daysSinceLastTransaction > 180) {
            return 'lapsed_customers';
        }

        // Frequent Buyers: High transaction frequency (>= 10 transactions) and recent activity
        if ($transactionCount >= 10 && $daysSinceLastTransaction <= 30) {
            return 'frequent_buyers';
        }

        // Product Specific Buyers: Moderate spending with specific patterns
        if ($totalSpent >= 1000000 && $avgTransactionValue >= 100000) {
            return 'product_specific_buyers';
        }

        // Default segment for others
        return 'regular_customers';
    }

    /**
     * Show current segment distribution
     */
    private function showSegmentDistribution()
    {
        $this->info('Current segment distribution:');

        $segments = Customer::selectRaw('segment, COUNT(*) as count')
            ->groupBy('segment')
            ->orderBy('count', 'desc')
            ->get();

        $table = [];
        foreach ($segments as $segment) {
            $table[] = [
                'Segment' => $segment->segment ?: 'No Segment',
                'Count' => $segment->count,
                'Percentage' => round(($segment->count / Customer::count()) * 100, 2) . '%'
            ];
        }

        $this->table(['Segment', 'Count', 'Percentage'], $table);
    }
}
