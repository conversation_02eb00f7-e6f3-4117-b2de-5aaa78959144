<?php

namespace App\Console\Commands;

use App\Models\CutiIzin;
use App\Models\Karyawan;
use App\Services\WhatsAppService;
use Illuminate\Console\Command;
use Carbon\Carbon;

class TestCutiWhatsAppCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'whatsapp:test-cuti {karyawan_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test WhatsApp notification for cuti/izin submission';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $karyawanId = $this->argument('karyawan_id');

        // Check WhatsApp service status
        $whatsAppService = app(WhatsAppService::class);
        $status = $whatsAppService->getStatus();
        
        $this->info('WhatsApp Service Status:');
        $this->table(
            ['Property', 'Value'],
            [
                ['Enabled', $status['enabled'] ? 'Yes' : 'No'],
                ['Configured', $status['configured'] ? 'Yes' : 'No'],
                ['API URL', $status['api_url']],
            ]
        );

        if (!$whatsAppService->isEnabled()) {
            $this->error('WhatsApp service is not enabled or not configured properly.');
            return 1;
        }

        // Get karyawan
        if ($karyawanId) {
            $karyawan = Karyawan::find($karyawanId);
            if (!$karyawan) {
                $this->error("Karyawan with ID {$karyawanId} not found.");
                return 1;
            }
        } else {
            // Get first karyawan
            $karyawan = Karyawan::first();
            if (!$karyawan) {
                $this->error('No karyawan found in database.');
                return 1;
            }
        }

        $this->info("Testing with karyawan: {$karyawan->nama_lengkap} (ID: {$karyawan->id})");

        // Create test cuti request
        $this->info('Creating test cuti request...');
        
        $cutiIzin = CutiIzin::create([
            'karyawan_id' => $karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::tomorrow(),
            'tanggal_selesai' => Carbon::tomorrow()->addDays(2),
            'jumlah_hari' => 3,
            'alasan' => 'Test cuti untuk keperluan keluarga',
            'keterangan_tambahan' => 'Test notification WhatsApp',
            'status' => 'pending'
        ]);

        $this->info("✅ Test cuti created with ID: {$cutiIzin->id}");
        $this->info("📱 WhatsApp notification should be sent to: 085272726519");
        
        // Wait a moment then test status update
        $this->info('Testing status update notification...');
        
        if ($this->confirm('Do you want to test approval notification?', true)) {
            $cutiIzin->update([
                'status' => 'approved',
                'approved_by' => 1, // Assuming user ID 1 exists
                'approved_at' => now()
            ]);
            
            $this->info("✅ Cuti approved - WhatsApp notifications should be sent");
        }

        if ($this->confirm('Do you want to test rejection notification?', false)) {
            $cutiIzin->update([
                'status' => 'rejected',
                'approved_by' => 1,
                'approved_at' => now(),
                'rejection_reason' => 'Test rejection reason'
            ]);
            
            $this->info("❌ Cuti rejected - WhatsApp notifications should be sent");
        }

        // Clean up
        if ($this->confirm('Do you want to delete the test cuti record?', true)) {
            $cutiIzin->delete();
            $this->info("🗑️ Test cuti record deleted");
        }

        $this->info('Test completed!');
        return 0;
    }
}
