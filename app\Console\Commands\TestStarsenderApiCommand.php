<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestStarsenderApiCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'starsender:test {phone} {message}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Starsender API directly with correct format';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $phone = $this->argument('phone');
        $message = $this->argument('message');

        $apiUrl = config('services.starsender.api_url');
        $apiToken = config('services.starsender.api_token');

        if (empty($apiToken)) {
            $this->error('STARSENDER_API_TOKEN is not configured in .env');
            return 1;
        }

        $this->info('Testing Starsender API with correct format...');
        $this->info("API URL: {$apiUrl}/api/send");
        $this->info("Phone: {$phone}");
        $this->info("Message: {$message}");
        $this->info("Token: " . substr($apiToken, 0, 10) . '...');

        // Payload sesuai format Starsender yang benar
        $payload = [
            "messageType" => "text",
            "to" => $phone,
            "body" => $message,
        ];

        $this->info('Payload: ' . json_encode($payload, JSON_PRETTY_PRINT));

        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => $apiToken, // Tanpa "Bearer"
            ])->post($apiUrl . '/api/send', $payload);

            $this->info('Response Status: ' . $response->status());
            $this->info('Response Headers: ' . json_encode($response->headers(), JSON_PRETTY_PRINT));
            
            $responseData = $response->json();
            $this->info('Response Body: ' . json_encode($responseData, JSON_PRETTY_PRINT));

            if ($response->successful()) {
                $this->info('✅ Message sent successfully!');
                return 0;
            } else {
                $this->error('❌ Failed to send message');
                $this->error('Status Code: ' . $response->status());
                return 1;
            }

        } catch (\Exception $e) {
            $this->error('❌ Exception occurred: ' . $e->getMessage());
            return 1;
        }
    }
}
