<?php

namespace App\Console\Commands;

use App\Services\WhatsAppService;
use Illuminate\Console\Command;

class TestWhatsAppCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'whatsapp:test {phone} {message}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test WhatsApp service by sending a message';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $phone = $this->argument('phone');
        $message = $this->argument('message');

        $whatsAppService = app(WhatsAppService::class);

        // Check service status
        $status = $whatsAppService->getStatus();
        $this->info('WhatsApp Service Status:');
        $this->table(
            ['Property', 'Value'],
            [
                ['Enabled', $status['enabled'] ? 'Yes' : 'No'],
                ['Configured', $status['configured'] ? 'Yes' : 'No'],
                ['API URL', $status['api_url']],
            ]
        );

        if (!$whatsAppService->isEnabled()) {
            $this->error('WhatsApp service is not enabled or not configured properly.');
            return 1;
        }

        $this->info("Sending WhatsApp message to: {$phone}");
        $this->info("Message: {$message}");
        $this->info("API Endpoint: " . config('services.starsender.api_url') . '/api/send');

        try {
            $result = $whatsAppService->sendMessage($phone, $message);

            if ($result['success']) {
                $this->info('✅ Message sent successfully!');
                $this->line('Response: ' . json_encode($result['data'], JSON_PRETTY_PRINT));
            } else {
                $this->error('❌ Failed to send message');
                $this->error('Error: ' . ($result['error'] ?? 'Unknown error'));
                if (isset($result['status_code'])) {
                    $this->error('Status Code: ' . $result['status_code']);
                }
            }
        } catch (\Exception $e) {
            $this->error('❌ Exception occurred: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
