<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\JadwalEntitasSeeder;

class UpdateJadwalEntitas extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jadwal:update-entitas 
                            {--force : Force update even if entitas_id already exists}
                            {--dry-run : Show what would be updated without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update jadwal_kerja and jadwal_masal tables with entitas_id based on karyawan entitas';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $force = $this->option('force');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('🔍 DRY RUN MODE - No changes will be made');
        }

        $this->info('🚀 Starting Jadwal Entitas Update...');
        $this->newLine();

        // Check if migration has been run
        if (!\Schema::hasColumn('jadwal_kerja', 'entitas_id')) {
            $this->error('❌ Migration not found! Please run the migration first:');
            $this->line('   php artisan migrate');
            return 1;
        }

        // Show current statistics
        $this->showCurrentStatistics();

        if (!$force && !$dryRun) {
            if (!$this->confirm('Do you want to proceed with updating entitas_id?')) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        // Run the seeder
        $seeder = new JadwalEntitasSeeder();
        $seeder->setCommand($this);
        
        if (!$dryRun) {
            $seeder->run();
        } else {
            $this->simulateDryRun();
        }

        $this->newLine();
        $this->info('✅ Jadwal Entitas Update completed!');
        
        // Show final statistics
        if (!$dryRun) {
            $this->showFinalStatistics();
        }

        return 0;
    }

    /**
     * Show current statistics
     */
    private function showCurrentStatistics(): void
    {
        $this->info('📊 Current Statistics:');

        // Jadwal Kerja statistics
        $jadwalKerjaTotal = \DB::table('jadwal_kerja')->count();
        $jadwalKerjaWithEntitas = \DB::table('jadwal_kerja')->whereNotNull('entitas_id')->count();
        $jadwalKerjaWithoutEntitas = $jadwalKerjaTotal - $jadwalKerjaWithEntitas;
        
        $this->line("   📅 Jadwal Kerja:");
        $this->line("      - Total: {$jadwalKerjaTotal}");
        $this->line("      - With entitas_id: {$jadwalKerjaWithEntitas}");
        $this->line("      - Without entitas_id: {$jadwalKerjaWithoutEntitas}");

        // Jadwal Masal statistics
        $jadwalMasalTotal = \DB::table('jadwal_masal')->count();
        $jadwalMasalWithEntitas = \DB::table('jadwal_masal')->whereNotNull('entitas_id')->count();
        $jadwalMasalWithoutEntitas = $jadwalMasalTotal - $jadwalMasalWithEntitas;
        
        $this->line("   📋 Jadwal Masal:");
        $this->line("      - Total: {$jadwalMasalTotal}");
        $this->line("      - With entitas_id: {$jadwalMasalWithEntitas}");
        $this->line("      - Without entitas_id: {$jadwalMasalWithoutEntitas}");

        // Entitas count
        $entitasCount = \DB::table('entitas')->count();
        $this->line("   🏢 Available Entitas: {$entitasCount}");

        $this->newLine();
    }

    /**
     * Show final statistics
     */
    private function showFinalStatistics(): void
    {
        $this->info('📊 Final Statistics:');

        // Jadwal Kerja statistics
        $jadwalKerjaTotal = \DB::table('jadwal_kerja')->count();
        $jadwalKerjaWithEntitas = \DB::table('jadwal_kerja')->whereNotNull('entitas_id')->count();
        
        $this->line("   📅 Jadwal Kerja: {$jadwalKerjaWithEntitas}/{$jadwalKerjaTotal} have entitas_id");

        // Jadwal Masal statistics
        $jadwalMasalTotal = \DB::table('jadwal_masal')->count();
        $jadwalMasalWithEntitas = \DB::table('jadwal_masal')->whereNotNull('entitas_id')->count();
        
        $this->line("   📋 Jadwal Masal: {$jadwalMasalWithEntitas}/{$jadwalMasalTotal} have entitas_id");

        // Entitas distribution
        $entitasDistribution = \DB::table('jadwal_kerja as jk')
            ->join('entitas as e', 'jk.entitas_id', '=', 'e.id')
            ->select('e.nama', \DB::raw('COUNT(*) as count'))
            ->groupBy('e.id', 'e.nama')
            ->orderBy('count', 'desc')
            ->get();

        if ($entitasDistribution->count() > 0) {
            $this->line('   🏢 Schedule distribution by entitas:');
            foreach ($entitasDistribution as $dist) {
                $this->line("      - {$dist->nama}: {$dist->count} schedules");
            }
        }
    }

    /**
     * Simulate dry run
     */
    private function simulateDryRun(): void
    {
        $this->info('🔍 Simulating changes...');

        // Count schedules that would be updated
        $jadwalKerjaToUpdate = \DB::table('jadwal_kerja as jk')
            ->join('karyawan as k', 'jk.karyawan_id', '=', 'k.id')
            ->whereNull('jk.entitas_id')
            ->whereNotNull('k.id_entitas')
            ->count();

        $this->line("   📅 Would update {$jadwalKerjaToUpdate} jadwal_kerja records");

        // Count bulk schedules that would be updated
        $jadwalMasalToUpdate = \DB::table('jadwal_masal')
            ->whereNull('entitas_id')
            ->count();

        $this->line("   📋 Would update {$jadwalMasalToUpdate} jadwal_masal records");

        // Show which entitas would be used
        $entitasList = \DB::table('entitas')->get();
        $this->line('   🏢 Available entitas for assignment:');
        foreach ($entitasList as $entitas) {
            $this->line("      - {$entitas->nama} (ID: {$entitas->id})");
        }
    }
}
