<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // OKR Progress Tracking
        $schedule->command('okr:track-progress --notify')
            ->dailyAt('09:00')
            ->description('Track OKR progress and send notifications');

        // Weekly progress summary
        $schedule->command('okr:track-progress')
            ->weeklyOn(1, '08:00') // Every Monday at 8 AM
            ->description('Weekly OKR progress summary');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
