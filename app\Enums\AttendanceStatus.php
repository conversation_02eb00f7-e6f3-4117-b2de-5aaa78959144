<?php

namespace App\Enums;

enum AttendanceStatus: string
{
    case HADIR = 'hadir';
    case TERLAMBAT = 'terlambat';
    case IZIN = 'izin';
    case SAKIT = 'sakit';
    case CUTI = 'cuti';
    case ALPHA = 'alpha';

    public function label(): string
    {
        return match($this) {
            self::HADIR => 'Hadir',
            self::TERLAMBAT => 'Terlambat',
            self::IZIN => 'Izin',
            self::SAKIT => 'Sakit',
            self::CUTI => 'Cuti',
            self::ALPHA => 'Alpha (Tanpa Keterangan)',
        };
    }

    public static function options(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn($case) => [$case->value => $case->label()])
            ->toArray();
    }

    public function badgeColor(): string
    {
        return match($this) {
            self::HADIR => 'success',
            self::TERLAMBAT => 'warning',
            self::IZIN => 'info',
            self::SAKIT => 'info',
            self::CUTI => 'primary',
            self::ALPHA => 'danger',
        };
    }

    public function isPresent(): bool
    {
        return in_array($this, [self::HADIR, self::TERLAMBAT]);
    }

    public function isAbsent(): bool
    {
        return in_array($this, [self::IZIN, self::SAKIT, self::CUTI, self::ALPHA]);
    }

    public function requiresApproval(): bool
    {
        return in_array($this, [self::IZIN, self::SAKIT, self::CUTI]);
    }
}
