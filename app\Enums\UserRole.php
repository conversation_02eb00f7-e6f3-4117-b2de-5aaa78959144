<?php

namespace App\Enums;

enum UserRole: string
{
    case ADMIN = 'admin';
    case SUPERVISOR = 'supervisor';
    case MANAGER = 'manager';
    case KEPTOK = 'keptok';
    case KARYAWAN = 'karyawan';

    public function label(): string
    {
        return match ($this) {
            self::ADMIN => 'Admin',
            self::SUPERVISOR => 'Supervisor',
            self::MANAGER => 'Manager',
            self::KEPTOK => 'Kepala Toko',
            self::KARYAWAN => 'Karyawan',
        };
    }

    public static function options(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn($case) => [$case->value => $case->label()])
            ->toArray();
    }

    public function canAccessAdmin(): bool
    {
        return in_array($this, [self::ADMIN, self::SUPERVISOR, self::MANAGER, self::KEPTOK]);
    }

    public function canManageEmployees(): bool
    {
        return in_array($this, [self::ADMIN, self::SUPERVISOR, self::MANAGER, self::KEPTOK]);
    }

    public function canApproveAttendance(): bool
    {
        return in_array($this, [self::ADMIN, self::SUPERVISOR, self::MANAGER, self::KEPTOK]);
    }

    public function canCreateSchedules(): bool
    {
        return in_array($this, [self::ADMIN, self::SUPERVISOR]);
    }

    public function canViewReports(): bool
    {
        return in_array($this, [self::ADMIN, self::SUPERVISOR]);
    }
}
