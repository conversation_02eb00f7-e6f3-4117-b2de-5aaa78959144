<?php

namespace App\Exports;

use App\Models\Absensi;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AbsensiExport implements FromCollection, WithHeadings, WithMapping
{
    public function collection()
    {
        return Absensi::with(['karyawan', 'jadwal.shift'])->get();
    }

    public function headings(): array
    {
        return [
            'Nama Karyawan',
            'NIP',
            'Tanggal',
            'Shift',
            'Jam Masuk',
            'Jam Keluar',
            'Status',
            'Terlambat',
            'Lokasi Masuk',
            'Lokasi Keluar',
        ];
    }

    public function map($absensi): array
    {
        return [
            $absensi->karyawan->nama_lengkap ?? '-',
            $absensi->karyawan->nip ?? '-',
            $absensi->tanggal_absensi ? $absensi->tanggal_absensi->format('d/m/Y') : '-',
            $absensi->jadwal->shift->nama_shift ?? '-',
            $absensi->waktu_masuk ? $absensi->waktu_masuk->format('H:i') : '-',
            $absensi->waktu_keluar ? $absensi->waktu_keluar->format('H:i') : '-',
            ucfirst($absensi->status),
            $absensi->is_late ? 'Ya' : 'Tidak',
            $absensi->lokasi_masuk ?? '-',
            $absensi->lokasi_keluar ?? '-',
        ];
    }
}
