<?php

namespace App\Exports;

use App\Models\AturanKeterlambatan;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AturanKeterlambatanExport implements FromCollection, WithHeadings, WithMapping
{
    public function collection()
    {
        return AturanKeterlambatan::all();
    }

    public function headings(): array
    {
        return [
            'Menit Dari',
            'Menit Sampai',
            'Potongan (Rp)',
            'Deskripsi',
            'Status',
            'Tanggal Dibuat',
        ];
    }

    public function map($aturan): array
    {
        return [
            $aturan->menit_dari,
            $aturan->menit_sampai,
            number_format($aturan->potongan, 0, ',', '.'),
            $aturan->deskripsi ?? '-',
            $aturan->is_active ? 'Aktif' : 'Tidak Aktif',
            $aturan->created_at->format('d/m/Y'),
        ];
    }
}
