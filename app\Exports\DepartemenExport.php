<?php

namespace App\Exports;

use App\Models\Departemen;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class DepartemenExport implements FromCollection, WithHeadings, WithMapping
{
    public function collection()
    {
        return Departemen::withCount('karyawan')->get();
    }

    public function headings(): array
    {
        return [
            'Nama Departemen',
            'Ju<PERSON><PERSON>wan',
            'Tanggal Dibuat',
        ];
    }

    public function map($departemen): array
    {
        return [
            $departemen->nama_departemen,
            $departemen->karyawan_count,
            $departemen->created_at->format('d/m/Y'),
        ];
    }
}
