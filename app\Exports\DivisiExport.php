<?php

namespace App\Exports;

use App\Models\Divisi;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class DivisiExport implements FromCollection, WithHeadings, WithMapping
{
    public function collection()
    {
        return Divisi::with(['departemen'])->withCount('karyawan')->get();
    }

    public function headings(): array
    {
        return [
            'Nama Divisi',
            'Departemen',
            'Deskripsi',
            'Ju<PERSON><PERSON>wan',
            'Tanggal Dibuat',
        ];
    }

    public function map($divisi): array
    {
        return [
            $divisi->nama_divisi,
            $divisi->departemen->nama_departemen ?? '-',
            $divisi->deskripsi ?? '-',
            $divisi->karyawan_count,
            $divisi->created_at->format('d/m/Y'),
        ];
    }
}
