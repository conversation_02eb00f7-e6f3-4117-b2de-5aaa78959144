<?php

namespace App\Exports;

use App\Models\Entitas;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class EntitasExport implements FromCollection, WithHeadings, WithMapping
{
    public function collection()
    {
        return Entitas::withCount('karyawan')->get();
    }

    public function headings(): array
    {
        return [
            'Nama Entitas',
            'Alamat',
            'Latitude',
            'Longitude',
            'Radius (meter)',
            'Jumlah Karyawan',
            'Tanggal Dibuat',
        ];
    }

    public function map($entitas): array
    {
        return [
            $entitas->nama_entitas,
            $entitas->alamat ?? '-',
            $entitas->latitude ?? '-',
            $entitas->longitude ?? '-',
            $entitas->radius ?? '-',
            $entitas->karyawan_count,
            $entitas->created_at->format('d/m/Y'),
        ];
    }
}
