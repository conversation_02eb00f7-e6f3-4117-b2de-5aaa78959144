<?php

namespace App\Exports;

use App\Models\Jabatan;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class JabatanExport implements FromCollection, WithHeadings, WithMapping
{
    public function collection()
    {
        return Jabatan::withCount('karyawan')->get();
    }

    public function headings(): array
    {
        return [
            'Nama Jabatan',
            '<PERSON><PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            'Tanggal Dibuat',
        ];
    }

    public function map($jabatan): array
    {
        return [
            $jabatan->nama_jabatan,
            $jabatan->deskripsi ?? '-',
            $jabatan->karyawan_count,
            $jabatan->created_at->format('d/m/Y'),
        ];
    }
}
