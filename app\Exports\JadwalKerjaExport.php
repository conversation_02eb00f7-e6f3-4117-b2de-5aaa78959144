<?php

namespace App\Exports;

use App\Models\Schedule;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Facades\Auth;
use App\Models\Karyawan;

class JadwalKerjaExport implements FromQuery, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    public function query()
    {
        $query = Schedule::query()
            ->with(['karyawan.entitas', 'shift', 'supervisor'])
            ->orderBy('tanggal_jadwal', 'desc');

        $user = Auth::user();
        
        // Filter by supervisor if user is supervisor
        if ($user->role === 'supervisor') {
            $employeeIds = Karyawan::where('supervisor_id', $user->id)->pluck('id')->toArray();
            $query->whereIn('karyawan_id', $employeeIds);
        }

        return $query;
    }

    public function headings(): array
    {
        return [
            'Tanggal Jadwal',
            'Nama Karyawan',
            'Entitas',
            'Shift',
            'Waktu Masuk',
            'Waktu Keluar',
            'Status',
            'Disetujui',
            'Keterangan',
            'Dibuat Oleh',
            'Tanggal Dibuat',
        ];
    }

    public function map($schedule): array
    {
        return [
            $schedule->tanggal_jadwal?->format('d/m/Y'),
            $schedule->karyawan?->nama_lengkap,
            $schedule->karyawan?->entitas?->nama_entitas,
            $schedule->shift?->nama_shift,
            $schedule->waktu_masuk,
            $schedule->waktu_keluar,
            $schedule->status,
            $schedule->is_approved ? 'Ya' : 'Tidak',
            $schedule->keterangan,
            $schedule->supervisor?->name,
            $schedule->created_at?->format('d/m/Y H:i'),
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}
