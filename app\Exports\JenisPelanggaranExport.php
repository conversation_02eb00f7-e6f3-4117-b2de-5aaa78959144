<?php

namespace App\Exports;

use App\Models\JenisPelanggaran;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class JenisPelanggaranExport implements FromCollection, WithHeadings, WithMapping
{
    public function collection()
    {
        return JenisPelanggaran::all();
    }

    public function headings(): array
    {
        return [
            'Nama Pelanggaran',
            'Des<PERSON>ripsi',
            'Tingkat Pelanggaran',
            'Denda (Rp)',
            'Status',
            'Tanggal Dibuat',
        ];
    }

    public function map($jenis): array
    {
        return [
            $jenis->nama_pelanggaran,
            $jenis->deskripsi ?? '-',
            ucfirst($jenis->kategori),
            $jenis->formatted_denda,
            $jenis->is_active ? 'Aktif' : 'Tidak Aktif',
            $jenis->created_at->format('d/m/Y'),
        ];
    }
}
