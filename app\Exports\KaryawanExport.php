<?php

namespace App\Exports;

use App\Models\Karyawan;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;


class KaryawanExport implements FromCollection, WithHeadings, WithMapping
{
    public function collection()
    {
        // Pakai with() untuk eager load relasi jabatan
        return Karyawan::with(['jabatan'])->select([
            'nama_lengkap',
            'nip',
            'nik',
            'kota_lahir',
            'tanggal_lahir',
            'nomor_telepon',
            'alamat',
            'status_aktif',
            'id_jabatan', // include id_jabatan untuk kebutuhan mapping
        ])->get();
    }

    public function headings(): array
    {
        return [
            'Nama <PERSON>ka<PERSON>',
            'NIP',
            'NIK',
            'Kota Lahir',
            'Tanggal Lahir',
            'Nomor Telepon',
            'Alamat',
            'Status Aktif',
            'Jabatan', // tambah heading jabatan
        ];
    }

    public function map($row): array
    {
        return [
            $row->nama_lengkap,
            $row->nip,
            $row->nik,
            $row->kota_lahir,
            $row->tanggal_lahir,
            $row->nomor_telepon,
            $row->alamat,
            $row->status_aktif ? 'Aktif' : 'Tidak Aktif',
            optional($row->jabatan)->nama_jabatan, // ambil nama jabatan dari relasi
        ];
    }
}
