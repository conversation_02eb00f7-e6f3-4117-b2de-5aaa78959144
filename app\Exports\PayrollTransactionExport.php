<?php

namespace App\Exports;

use App\Models\PayrollTransaction;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class PayrollTransactionExport implements FromCollection, WithHeadings, WithMapping
{
    public function collection()
    {
        return PayrollTransaction::with(['karyawan', 'payrollPeriod'])->get();
    }

    public function headings(): array
    {
        return [
            'Nama Karyawan',
            'NIP',
            'Periode',
            'Gaji <PERSON>',
            'Total Potongan',
            'Gaj<PERSON>',
            'Status',
            'Tanggal Diproses',
        ];
    }

    public function map($transaction): array
    {
        return [
            $transaction->karyawan->nama_lengkap ?? '-',
            $transaction->karyawan->nip ?? '-',
            $transaction->payrollPeriod->nama_periode ?? '-',
            number_format($transaction->gaji_pokok, 0, ',', '.'),
            number_format($transaction->total_potongan, 0, ',', '.'),
            number_format($transaction->gaji_bersih, 0, ',', '.'),
            ucfirst($transaction->status),
            $transaction->created_at->format('d/m/Y'),
        ];
    }
}
