<?php

namespace App\Exports;

use App\Models\PayrollTransaction;
use App\Models\PayrollDeduction;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class PayrollTransactionExport implements FromCollection, WithHeadings, WithMapping, WithColumnWidths, WithStyles
{
    protected $periodId;

    public function __construct($periodId = null)
    {
        $this->periodId = $periodId;
    }

    public function collection()
    {
        $query = PayrollTransaction::with([
            'karyawan.jabatan',
            'payrollPeriod',
            'payrollDeductions'
        ]);

        if ($this->periodId) {
            $query->where('payroll_period_id', $this->periodId);
        }

        return $query->orderBy('karyawan_id')->get();
    }

    public function headings(): array
    {
        return [
            'NO',
            'NRK',
            'Nama',
            'TMK',
            'HKE',
            'HK yang Dibayar',
            'SAKIT + SURAT',
            'SAKIT - SURAT',
            'TANPA KET / IZIN',
            'TERLAMBAT',
            'AMBIL CUTI',
            'SISA CUTI',
            'GAJI POKOK',
            'TUNJANGAN JABATAN',
            'TUNJANGAN UMUM',
            'TUNJANGAN SEMBAKO',
            'LEMBUR BIASA',
            'MASUK TANGGAL MERAH',
            'JML LEMBUR HK',
            'LEMBUR TAMBAH HKE',
            'POTONGAN MINUS KASIR',
            'POTONGAN STOCK OPNAME',
            'POTONGAN RETUR',
            'POTONGAN KASBON',
            'TAKE HOME PAY (Gross)',
            'KEKURANGAN GAJI BULAN LALU',
            'POTONGAN MAKAN',
            'CHECK BOX',
            'Hitungan rahasia',
            'Tot. Hari tanpa hitungan',
            'Proporsional',
            'Tot. Gaji Proporsional',
            'KPI 1'
        ];
    }
