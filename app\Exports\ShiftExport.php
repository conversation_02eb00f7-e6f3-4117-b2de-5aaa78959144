<?php

namespace App\Exports;

use App\Models\Shift;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ShiftExport implements FromCollection, WithHeadings, WithMapping
{
    public function collection()
    {
        return Shift::all();
    }

    public function headings(): array
    {
        return [
            'Nama Shift',
            'Waktu <PERSON>',
            'Waktu <PERSON>',
            'Toleransi Keterlambatan (Menit)',
            'Status',
            'Tanggal Dibuat',
        ];
    }

    public function map($shift): array
    {
        return [
            $shift->nama_shift,
            $shift->waktu_mulai->format('H:i'),
            $shift->waktu_selesai->format('H:i'),
            $shift->toleransi_keterlambatan ?? 0,
            $shift->is_active ? 'Aktif' : 'Tidak Aktif',
            $shift->created_at->format('d/m/Y'),
        ];
    }
}
