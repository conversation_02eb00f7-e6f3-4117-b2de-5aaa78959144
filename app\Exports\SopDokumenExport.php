<?php

namespace App\Exports;

use App\Models\SopDokumen;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class SopDokumenExport implements FromCollection, WithHeadings, WithMapping
{
    public function collection()
    {
        return SopDokumen::with(['departemen', 'divisi', 'creator'])->get();
    }

    public function headings(): array
    {
        return [
            'Judul SOP',
            'Deskripsi',
            'Scope',
            'Berlaku Untuk',
            'Status',
            'Versi',
            'Tanggal Berlaku',
            'Tanggal Berakhir',
            'Dibuat Oleh',
            'Tanggal Dibuat',
        ];
    }

    public function map($sop): array
    {
        return [
            $sop->judul_sop,
            $sop->deskripsi ?? '-',
            ucfirst($sop->scope_type),
            $sop->scope_name,
            ucfirst($sop->status),
            $sop->versi,
            $sop->tanggal_berlaku ? $sop->tanggal_berlaku->format('d/m/Y') : '-',
            $sop->tanggal_berakhir ? $sop->tanggal_berakhir->format('d/m/Y') : 'Tidak terbatas',
            $sop->creator->name ?? '-',
            $sop->created_at->format('d/m/Y'),
        ];
    }
}
