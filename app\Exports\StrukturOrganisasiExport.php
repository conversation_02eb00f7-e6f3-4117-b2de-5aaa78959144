<?php

namespace App\Exports;

use App\Models\Entitas;
use App\Models\Karyawan;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StrukturOrganisasiExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    protected $entitasId;

    public function __construct($entitasId = null)
    {
        $this->entitasId = $entitasId;
    }

    public function collection()
    {
        $query = Karyawan::with(['entitas', 'departemen', 'divisi', 'jabatan'])
            ->where('status_aktif', true);

        if ($this->entitasId) {
            $query->where('id_entitas', $this->entitasId);
        }

        return $query->orderBy('id_entitas')
            ->orderBy('id_departemen')
            ->orderBy('id_divisi')
            ->orderBy('id_jabatan')
            ->get();
    }

    public function headings(): array
    {
        return [
            'Entitas',
            'Departemen',
            'Divisi',
            'Jabatan',
            'Nama Karyawan',
            'NIP',
            'Email',
            'Nomor Telepon',
            'Status',
        ];
    }

    public function map($karyawan): array
    {
        return [
            $karyawan->entitas?->nama ?? 'Tidak Ditentukan',
            $karyawan->departemen?->nama_departemen ?? 'Tidak Ditentukan',
            $karyawan->divisi?->nama_divisi ?? 'Tidak Ditentukan',
            $karyawan->jabatan?->nama_jabatan ?? 'Tidak Ditentukan',
            $karyawan->nama_lengkap,
            $karyawan->nip ?? '-',
            $karyawan->email ?? '-',
            $karyawan->nomor_telepon ?? '-',
            $karyawan->status_aktif ? 'Aktif' : 'Tidak Aktif',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
            
            // Style the header row
            'A1:I1' => [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E8F0'],
                ],
            ],
        ];
    }
}
