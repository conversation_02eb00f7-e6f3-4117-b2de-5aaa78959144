<?php

namespace App\Exports;

use App\Models\Supplier;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class SupplierExport implements FromCollection, WithHeadings, WithMapping
{
    public function collection()
    {
        return Supplier::with(['akunHutang', 'createdBy'])->get();
    }

    public function headings(): array
    {
        return [
            'Nama Supplier',
            'Nama Perusahaan',
            'Email',
            'Nomor Handphone',
            'NPWP',
            'Alamat',
            'Akun Bank',
            'Nama Bank',
            'Kantor Cabang Bank',
            'Nomor Rekening',
            'Pemegang Akun Bank',
            'Akun Hutang',
            'Syarat Pembayaran',
            'Informasi Lainnya',
            'Dibuat Oleh',
            'Tanggal Dibuat',
        ];
    }

    public function map($supplier): array
    {
        return [
            $supplier->nama,
            $supplier->nama_perusahaan,
            $supplier->email,
            $supplier->nomor_handphone,
            $supplier->npwp,
            $supplier->alamat,
            $supplier->akun_bank,
            $supplier->nama_bank,
            $supplier->kantor_cabang_bank,
            $supplier->nomor_rekening,
            $supplier->pemegang_akun_bank,
            $supplier->akunHutang->nama_akun ?? '-',
            $supplier->syarat_pembayaran_utama,
            $supplier->info_lainnya ?? '-',
            $supplier->createdBy->name ?? '-',
            $supplier->created_at->format('d/m/Y H:i'),
        ];
    }
}
