<?php

namespace App\Exports;

use App\Models\User;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class UserExport implements FromCollection, WithHeadings, WithMapping
{
    public function collection()
    {
        return User::with(['karyawan'])->get();
    }

    public function headings(): array
    {
        return [
            'Nama',
            'Email',
            'Role',
            'Karyawan Terkait',
            'Email Verified',
            'Tanggal Dibuat',
        ];
    }

    public function map($user): array
    {
        return [
            $user->name,
            $user->email,
            ucfirst($user->role),
            $user->karyawan->nama_lengkap ?? '-',
            $user->email_verified_at ? 'Ya' : 'Tidak',
            $user->created_at->format('d/m/Y'),
        ];
    }
}
