<?php

namespace App\Filament\Akunting\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use App\Models\DailyTransaction;
use Carbon\Carbon;

class FnBMonthlyReport extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-cake';

    protected static ?string $navigationLabel = 'Laporan Bulanan FnB';

    protected static ?string $title = 'Laporan Bulanan FnB';

    protected static ?string $navigationGroup = 'Laporan';

    protected static string $view = 'filament.akunting.pages.fn-b-monthly-report';

    public ?array $data = [];
    public $selectedMonth = null;
    public $selectedYear = null;
    public $reportData = [];

    public function mount(): void
    {
        $this->selectedMonth = now()->month;
        $this->selectedYear = now()->year;
        $this->form->fill([
            'month' => $this->selectedMonth,
            'year' => $this->selectedYear,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('month')
                    ->label('Month')
                    ->options([
                        1 => 'January',
                        2 => 'February',
                        3 => 'March',
                        4 => 'April',
                        5 => 'May',
                        6 => 'June',
                        7 => 'July',
                        8 => 'August',
                        9 => 'September',
                        10 => 'October',
                        11 => 'November',
                        12 => 'December'
                    ])
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(fn($state) => $this->selectedMonth = $state),
                Select::make('year')
                    ->label('Year')
                    ->options(collect(range(date('Y') - 5, date('Y') + 1))->mapWithKeys(fn($year) => [$year => $year]))
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(fn($state) => $this->selectedYear = $state),
            ])
            ->statePath('data')
            ->columns(2);
    }

    public function generateReport()
    {
        $data = $this->form->getState();

        if (!$data['month'] || !$data['year']) {
            return;
        }

        $this->selectedMonth = $data['month'];
        $this->selectedYear = $data['year'];

        $this->reportData = $this->getReportData();
    }

    protected function getReportData(): array
    {
        if (!$this->selectedMonth || !$this->selectedYear) {
            return [];
        }

        // Get FnB transactions only
        $transactions = DailyTransaction::forCategory('FnB')
            ->forMonth($this->selectedYear, $this->selectedMonth)
            ->with(['outlet'])
            ->get();

        $summary = [
            'total_revenue' => $transactions->where('type', 'revenue')->sum('amount'),
            'total_expense' => $transactions->where('type', 'expense')->sum('amount'),
            'total_receivable' => $transactions->where('type', 'receivable')->sum('amount'),
            'total_cash_deficit' => $transactions->where('type', 'cash_deficit')->sum('amount'),
        ];

        $summary['net_profit'] = $summary['total_revenue'] - $summary['total_expense'];

        // Group by outlet
        $byOutlet = $transactions->groupBy('outlet.name')->map(function ($outletTransactions) {
            return [
                'revenue' => $outletTransactions->where('type', 'revenue')->sum('amount'),
                'expense' => $outletTransactions->where('type', 'expense')->sum('amount'),
                'receivable' => $outletTransactions->where('type', 'receivable')->sum('amount'),
                'cash_deficit' => $outletTransactions->where('type', 'cash_deficit')->sum('amount'),
            ];
        });

        return [
            'period' => Carbon::create($this->selectedYear, $this->selectedMonth, 1)->format('F Y'),
            'summary' => $summary,
            'by_outlet' => $byOutlet,
            'transactions' => $transactions,
        ];
    }
}
