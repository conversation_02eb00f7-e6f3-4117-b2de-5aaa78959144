<?php

namespace App\Filament\Akunting\Resources\DailyTransactionResource\Pages;

use App\Filament\Akunting\Resources\DailyTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditDailyTransaction extends EditRecord
{
    protected static string $resource = DailyTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
