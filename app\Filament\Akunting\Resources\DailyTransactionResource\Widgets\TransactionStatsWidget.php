<?php

namespace App\Filament\Akunting\Resources\DailyTransactionResource\Widgets;

use App\Models\DailyTransaction;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class TransactionStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalRevenue = DailyTransaction::where('type', 'revenue')->sum('amount');
        $totalExpense = DailyTransaction::where('type', 'expense')->sum('amount');
        $totalReceivable = DailyTransaction::where('type', 'receivable')->sum('amount');
        $totalCashDeficit = DailyTransaction::where('type', 'cash_deficit')->sum('amount');
        $netProfit = $totalRevenue - $totalExpense;

        return [
            Stat::make('Total Pendapatan', 'Rp ' . number_format($totalRevenue, 0, ',', '.'))
                ->description('Total pendapatan dari semua outlet')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('success'),
            Stat::make('Total Pengeluaran', 'Rp ' . number_format($totalExpense, 0, ',', '.'))
                ->description('Total pengeluaran dari semua outlet')
                ->descriptionIcon('heroicon-m-arrow-trending-down')
                ->color('danger'),
            Stat::make('Total Piutang', 'Rp ' . number_format($totalReceivable, 0, ',', '.'))
                ->description('Total piutang yang belum terbayar')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),
            Stat::make('Kekurangan Kas', 'Rp ' . number_format($totalCashDeficit, 0, ',', '.'))
                ->description('Total kekurangan kas')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('gray'),
            Stat::make('Net Profit', 'Rp ' . number_format($netProfit, 0, ',', '.'))
                ->description($netProfit >= 0 ? 'Keuntungan bersih' : 'Kerugian bersih')
                ->descriptionIcon($netProfit >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($netProfit >= 0 ? 'success' : 'danger'),
        ];
    }
}
