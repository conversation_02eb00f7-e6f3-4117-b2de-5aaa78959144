<?php

namespace App\Filament\Akunting\Resources;

use App\Filament\Akunting\Resources\TransactionSubcategoryResource\Pages;
use App\Filament\Akunting\Resources\TransactionSubcategoryResource\RelationManagers;
use App\Models\TransactionSubcategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TransactionSubcategoryResource extends Resource
{
    protected static ?string $model = TransactionSubcategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';

    protected static ?string $navigationLabel = 'Sub-kategori Transaksi';

    protected static ?string $navigationGroup = 'Master Data';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('category_id')
                    ->relationship('category', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->label('Kategori Induk')
                    ->helperText('Pilih kategori induk untuk sub-kategori ini'),
                Forms\Components\TextInput::make('code')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(255)
                    ->label('Kode Sub-kategori')
                    ->helperText('Kode unik untuk sub-kategori (contoh: tagihan_rkv, listrik)'),
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255)
                    ->label('Nama Sub-kategori'),
                Forms\Components\Textarea::make('description')
                    ->maxLength(65535)
                    ->columnSpanFull()
                    ->label('Deskripsi'),
                Forms\Components\Toggle::make('is_active')
                    ->default(true)
                    ->label('Status Aktif'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('category.name')
                    ->searchable()
                    ->sortable()
                    ->label('Kategori Induk')
                    ->badge()
                    ->color(fn($record): string => match ($record->category->type) {
                        'revenue' => 'success',
                        'expense' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('code')
                    ->searchable()
                    ->sortable()
                    ->label('Kode'),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->label('Nama Sub-kategori'),
                Tables\Columns\TextColumn::make('category.type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'revenue' => 'success',
                        'expense' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'revenue' => 'Pendapatan',
                        'expense' => 'Pengeluaran',
                        default => $state,
                    })
                    ->label('Jenis'),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->label('Status'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Dibuat'),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Diupdate'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category_id')
                    ->relationship('category', 'name')
                    ->label('Kategori Induk'),
                Tables\Filters\SelectFilter::make('category.type')
                    ->options([
                        'revenue' => 'Pendapatan',
                        'expense' => 'Pengeluaran',
                    ])
                    ->label('Jenis Transaksi'),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransactionSubcategories::route('/'),
            'create' => Pages\CreateTransactionSubcategory::route('/create'),
            'edit' => Pages\EditTransactionSubcategory::route('/{record}/edit'),
        ];
    }
}
