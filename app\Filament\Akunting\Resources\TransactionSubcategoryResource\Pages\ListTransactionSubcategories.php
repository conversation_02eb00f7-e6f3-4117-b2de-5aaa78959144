<?php

namespace App\Filament\Akunting\Resources\TransactionSubcategoryResource\Pages;

use App\Filament\Akunting\Resources\TransactionSubcategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTransactionSubcategories extends ListRecords
{
    protected static string $resource = TransactionSubcategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
