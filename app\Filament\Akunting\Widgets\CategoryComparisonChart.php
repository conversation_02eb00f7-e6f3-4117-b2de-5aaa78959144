<?php

namespace App\Filament\Akunting\Widgets;

use Filament\Widgets\ChartWidget;
use App\Models\DailyTransaction;
use Carbon\Carbon;

class CategoryComparison<PERSON>hart extends ChartWidget
{
    protected static ?string $heading = 'Perbandingan Revenue FnB vs VOO (Bulan Ini)';

    protected static ?int $sort = 3;

    protected function getData(): array
    {
        $currentMonth = Carbon::now();

        $categories = ['FnB', 'VOO'];
        $labels = [];
        $data = [];
        $colors = [];

        foreach ($categories as $category) {
            $revenue = DailyTransaction::revenue()
                ->whereHas('outlet', function ($q) use ($category) {
                    $q->where('category', $category);
                })
                ->whereYear('transaction_date', $currentMonth->year)
                ->whereMonth('transaction_date', $currentMonth->month)
                ->sum('amount');

            if ($revenue > 0) {
                $labels[] = $category;
                $data[] = $revenue / 1000000; // Convert to millions

                // Set colors based on category
                if ($category === 'FnB') {
                    $colors[] = '#10b981'; // Green
                } else if ($category === 'VOO') {
                    $colors[] = '#f59e0b'; // Amber
                } else {
                    $colors[] = '#6b7280'; // Gray
                }
            }
        }

        return [
            'datasets' => [
                [
                    'data' => $data,
                    'backgroundColor' => $colors,
                    'borderColor' => $colors,
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            return context.label + ": Rp " + (context.parsed * 1000000).toLocaleString("id-ID") + " (" + Math.round((context.parsed / context.dataset.data.reduce((a, b) => a + b, 0)) * 100) + "%)";
                        }',
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}
