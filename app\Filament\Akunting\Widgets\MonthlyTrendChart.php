<?php

namespace App\Filament\Akunting\Widgets;

use Filament\Widgets\ChartWidget;
use App\Models\DailyTransaction;
use Carbon\Carbon;

class MonthlyTrend<PERSON>hart extends ChartWidget
{
    protected static ?string $heading = 'Trend Revenue & Expense (6 Bulan Terakhir)';

    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $months = [];
        $revenueData = [];
        $expenseData = [];

        // Get data for last 6 months
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthName = $date->format('M Y');
            $months[] = $monthName;

            $revenue = DailyTransaction::revenue()
                ->whereYear('transaction_date', $date->year)
                ->whereMonth('transaction_date', $date->month)
                ->sum('amount');

            $expense = DailyTransaction::expense()
                ->whereYear('transaction_date', $date->year)
                ->whereMonth('transaction_date', $date->month)
                ->sum('amount');

            $revenueData[] = $revenue / 1000000; // Convert to millions
            $expenseData[] = $expense / 1000000; // Convert to millions
        }

        return [
            'datasets' => [
                [
                    'label' => 'Revenue (Juta Rp)',
                    'data' => $revenueData,
                    'borderColor' => '#10b981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'fill' => true,
                ],
                [
                    'label' => 'Expense (Juta Rp)',
                    'data' => $expenseData,
                    'borderColor' => '#ef4444',
                    'backgroundColor' => 'rgba(239, 68, 68, 0.1)',
                    'fill' => true,
                ],
            ],
            'labels' => $months,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Amount (Juta Rupiah)',
                    ],
                ],
                'x' => [
                    'title' => [
                        'display' => true,
                        'text' => 'Bulan',
                    ],
                ],
            ],
        ];
    }
}
