<?php

namespace App\Filament\Akunting\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use App\Models\DailyTransaction;

class RecentTransactionsWidget extends BaseWidget
{
    protected static ?string $heading = 'Transaksi Terbaru';

    protected static ?int $sort = 4;

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                DailyTransaction::query()
                    ->with(['outlet', 'karyawan'])
                    ->latest('transaction_date')
                    ->latest('created_at')
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('transaction_date')
                    ->date('d/m/Y')
                    ->sortable()
                    ->label('Tanggal'),

                Tables\Columns\TextColumn::make('outlet.name')
                    ->searchable()
                    ->label('Outlet'),

                Tables\Columns\TextColumn::make('outlet.category')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'FnB' => 'success',
                        'VOO' => 'warning',
                        default => 'gray',
                    })
                    ->label('Kategori'),

                Tables\Columns\TextColumn::make('description')
                    ->searchable()
                    ->limit(30)
                    ->label('Deskripsi'),
                Tables\Columns\TextColumn::make('karyawan.nama_lengkap')
                    ->searchable()
                    ->label('Karyawan')
                    ->placeholder('—')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'revenue' => 'success',
                        'expense' => 'danger',
                        'receivable' => 'warning',
                        'cash_deficit' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'revenue' => 'Pendapatan',
                        'expense' => 'Pengeluaran',
                        'receivable' => 'Piutang',
                        'cash_deficit' => 'Kekurangan Kas',
                        default => $state,
                    })
                    ->label('Jenis'),

                Tables\Columns\TextColumn::make('amount')
                    ->money('IDR')
                    ->sortable()
                    ->label('Jumlah'),
            ])
            ->defaultSort('transaction_date', 'desc')
            ->paginated(false);
    }
}
