<?php

namespace App\Filament\Forms\Components;

use Filament\Forms\Components\Field;
use Filament\Forms\Components\Concerns\HasPlaceholder;

class MentionTextarea extends Field
{
    use HasPlaceholder;

    protected string $view = 'filament.forms.components.mention-textarea';

    protected int $rows = 3;
    protected $projectId = null;
    protected string $contextType = 'general';
    protected $contextId = null;

    public function rows(int $rows): static
    {
        $this->rows = $rows;
        return $this;
    }

    public function getRows(): int
    {
        return $this->rows;
    }

    public function projectId($projectId): static
    {
        $this->projectId = $projectId;
        return $this;
    }

    public function getProjectId()
    {
        return $this->evaluate($this->projectId);
    }

    public function contextType(string $contextType): static
    {
        $this->contextType = $contextType;
        return $this;
    }

    public function getContextType(): string
    {
        return $this->contextType;
    }

    public function contextId($contextId): static
    {
        $this->contextId = $contextId;
        return $this;
    }

    public function getContextId(): ?int
    {
        return $this->evaluate($this->contextId);
    }
}
