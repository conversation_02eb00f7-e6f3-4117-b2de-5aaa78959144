<?php

namespace App\Filament\Karyawan\Pages;

use Filament\Pages\Dashboard as BaseDashboard;
use App\Models\Karyawan;
use Illuminate\Support\Facades\Auth;
use Filament\Notifications\Notification;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;

class AbsensiDashboard extends BaseDashboard implements HasForms
{
    use InteractsWithForms;

    protected static string $routePath = '/absensi-dashboard';
    protected static ?string $navigationIcon = 'heroicon-o-finger-print';
    protected static ?string $navigationLabel = 'Dashboard Absensi';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationGroup = 'Jadwal & Absensi';
    protected ?string $heading = 'Dashboard Absensi';
    protected ?string $subheading = 'Monitoring kehadiran dan absensi Anda';
    protected static string $view = 'filament.karyawan.pages.absensi-dashboard';

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('attendance_today')
                ->label('Absensi Hari Ini')
                ->icon('heroicon-o-finger-print')
                ->color('primary')
                ->url(fn(): string => route('filament.karyawan.resources.absensis.create')),

            Action::make('attendance_history')
                ->label('Riwayat Absensi')
                ->icon('heroicon-o-clock')
                ->color('info')
                ->url(fn(): string => route('filament.karyawan.resources.absensis.index')),

            Action::make('schedule')
                ->label('Jadwal Saya')
                ->icon('heroicon-o-calendar-days')
                ->color('success')
                ->url(fn(): string => route('filament.karyawan.resources.schedules.index')),
        ];
    }

    public function getWidgets(): array
    {
        return [
            \App\Filament\Karyawan\Widgets\AbsensiOverview::class,
            \App\Filament\Karyawan\Widgets\UpcomingSchedule::class,
            \App\Filament\Karyawan\Widgets\RecentAttendance::class,
        ];
    }

    public function mount(): void
    {
        // Check if the logged-in user has a karyawan record
        $user = Auth::user();
        $karyawan = Karyawan::where('id_user', $user->id)->first();

        if (!$karyawan) {
            Notification::make()
                ->title('Akun tidak terhubung dengan data karyawan')
                ->body('Silahkan hubungi administrator untuk mengaitkan akun Anda dengan data karyawan.')
                ->danger()
                ->persistent()
                ->send();
        }
    }
}
