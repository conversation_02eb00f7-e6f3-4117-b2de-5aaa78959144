<?php

namespace App\Filament\Karyawan\Pages;

use Filament\Pages\Dashboard as BaseDashboard;
use App\Models\Karyawan;
use Illuminate\Support\Facades\Auth;
use Filament\Notifications\Notification;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;

class PayrollDashboard extends BaseDashboard implements HasForms
{
    use InteractsWithForms;

    protected static string $routePath = '/payroll-dashboard';
    protected static ?string $navigationIcon = 'heroicon-o-banknotes';
    protected static ?string $navigationLabel = 'Dashboard Payroll';
    protected static ?int $navigationSort = 3;
    protected ?string $heading = 'Dashboard Payroll & Potongan';
    protected ?string $subheading = 'Informasi gaji, potongan, dan riwayat payroll Anda';
    protected static string $view = 'filament.karyawan.pages.payroll-dashboard';

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('salary_history')
                ->label('Riwayat Gaji')
                ->icon('heroicon-o-banknotes')
                ->color('success')
                ->url(fn(): string => route('filament.karyawan.resources.karyawan-profiles.index')),

            Action::make('violations')
                ->label('Riwayat Pelanggaran')
                ->icon('heroicon-o-exclamation-triangle')
                ->color('warning')
                ->url(fn(): string => route('filament.karyawan.resources.karyawan-profiles.index')),

            Action::make('profile')
                ->label('Profil Lengkap')
                ->icon('heroicon-o-user')
                ->color('info')
                ->url(fn(): string => route('filament.karyawan.pages.profile')),
        ];
    }

    public function getWidgets(): array
    {
        return [
            \App\Filament\Karyawan\Widgets\PotonganOverview::class,
            \App\Filament\Karyawan\Widgets\DetailPotonganChart::class,
            \App\Filament\Karyawan\Widgets\DetailPotonganTable::class,
        ];
    }

    public function mount(): void
    {
        // Check if the logged-in user has a karyawan record
        $user = Auth::user();
        $karyawan = Karyawan::where('id_user', $user->id)->first();

        if (!$karyawan) {
            Notification::make()
                ->title('Akun tidak terhubung dengan data karyawan')
                ->body('Silahkan hubungi administrator untuk mengaitkan akun Anda dengan data karyawan.')
                ->danger()
                ->persistent()
                ->send();
        }
    }
}
