<?php

namespace App\Filament\Karyawan\Resources\AbsensiResource\Pages;

use App\Filament\Karyawan\Resources\AbsensiResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewAbsensi extends ViewRecord
{
    protected static string $resource = AbsensiResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Informasi Absensi')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('karyawan.nama_lengkap')
                                    ->label('<PERSON><PERSON>ryawan'),
                                Infolists\Components\TextEntry::make('tanggal_absensi')
                                    ->label('Tanggal')
                                    ->date('d M Y'),
                                Infolists\Components\TextEntry::make('waktu_masuk')
                                    ->label('<PERSON> Masuk')
                                    ->time('H:i'),
                                Infolists\Components\TextEntry::make('waktu_keluar')
                                    ->label('Jam Keluar')
                                    ->time('H:i')
                                    ->placeholder('-'),
                                Infolists\Components\TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn(string $state): string => match (strtolower($state)) {
                                        'hadir' => 'success',
                                        'terlambat' => 'warning',
                                        'izin' => 'info',
                                        'sakit' => 'info',
                                        'cuti' => 'primary',
                                        'alpha' => 'danger',
                                        default => 'gray',
                                    }),
                                Infolists\Components\TextEntry::make('jadwal.shift.nama_shift')
                                    ->label('Shift')
                                    ->placeholder('-'),
                            ]),

                        Infolists\Components\TextEntry::make('keterangan')
                            ->label('Keterangan')
                            ->placeholder('-')
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Lokasi')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('lokasi_masuk')
                                    ->label('Lokasi Masuk')
                                    ->placeholder('-')
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) return '-';
                                        $coords = explode(',', $state);
                                        if (count($coords) === 2) {
                                            return "Lat: {$coords[0]}, Lng: {$coords[1]}";
                                        }
                                        return $state;
                                    }),
                                Infolists\Components\TextEntry::make('lokasi_keluar')
                                    ->label('Lokasi Keluar')
                                    ->placeholder('-')
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) return '-';
                                        $coords = explode(',', $state);
                                        if (count($coords) === 2) {
                                            return "Lat: {$coords[0]}, Lng: {$coords[1]}";
                                        }
                                        return $state;
                                    }),
                            ]),
                    ]),

                Infolists\Components\Section::make('Peta Lokasi')
                    ->schema([
                        Infolists\Components\TextEntry::make('maps_info')
                            ->label('Akses Peta Interaktif')
                            ->formatStateUsing(function ($record) {
                                $content = '<div style="background: #f0f9ff; padding: 16px; border-radius: 8px; border: 1px solid #0ea5e9;">';
                                $content .= '<h4 style="margin: 0 0 12px 0; font-weight: 600; color: #0c4a6e;">🗺️ Lokasi Absensi</h4>';

                                if ($record->latitude_masuk && $record->longitude_masuk) {
                                    $content .= '<div style="margin-bottom: 12px;">';
                                    $content .= '<strong style="color: #059669;">📍 Lokasi Masuk:</strong><br>';
                                    $content .= '<span style="font-family: monospace; background: #ecfdf5; padding: 4px 8px; border-radius: 4px; font-size: 14px;">';
                                    $content .= $record->latitude_masuk . ', ' . $record->longitude_masuk;
                                    $content .= '</span><br>';
                                    $content .= '<a href="https://www.google.com/maps?q=' . $record->latitude_masuk . ',' . $record->longitude_masuk . '" target="_blank" style="color: #2563eb; text-decoration: underline; font-size: 14px;">🗺️ Buka di Google Maps</a>';
                                    $content .= '</div>';
                                }

                                if ($record->latitude_keluar && $record->longitude_keluar) {
                                    $content .= '<div style="margin-bottom: 12px;">';
                                    $content .= '<strong style="color: #dc2626;">📍 Lokasi Keluar:</strong><br>';
                                    $content .= '<span style="font-family: monospace; background: #fef2f2; padding: 4px 8px; border-radius: 4px; font-size: 14px;">';
                                    $content .= $record->latitude_keluar . ', ' . $record->longitude_keluar;
                                    $content .= '</span><br>';
                                    $content .= '<a href="https://www.google.com/maps?q=' . $record->latitude_keluar . ',' . $record->longitude_keluar . '" target="_blank" style="color: #2563eb; text-decoration: underline; font-size: 14px;">🗺️ Buka di Google Maps</a>';
                                    $content .= '</div>';
                                }

                                $content .= '<p style="margin: 12px 0 0 0; padding: 8px; background: #eff6ff; border-radius: 4px; font-size: 13px; color: #1e40af;">';
                                $content .= '💡 <strong>Tips:</strong> Gunakan tombol "Lihat Lokasi" di halaman daftar absensi untuk melihat peta interaktif dengan detail lengkap.';
                                $content .= '</p>';
                                $content .= '</div>';

                                return new \Illuminate\Support\HtmlString($content);
                            })
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => ($record->latitude_masuk && $record->longitude_masuk) || ($record->latitude_keluar && $record->longitude_keluar)),

                Infolists\Components\Section::make('Foto Absensi')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('foto_masuk_display')
                                    ->label('Foto Masuk')
                                    ->formatStateUsing(function ($record) {
                                        if (!$record->foto_masuk) {
                                            return new \Illuminate\Support\HtmlString('
                                                <div style="
                                                    width: 300px;
                                                    height: 300px;
                                                    border: 2px dashed #d1d5db;
                                                    border-radius: 12px;
                                                    display: flex;
                                                    align-items: center;
                                                    justify-content: center;
                                                    color: #9ca3af;
                                                    font-size: 16px;
                                                    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
                                                    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                                                ">
                                                    <div style="text-align: center;">
                                                        <div style="font-size: 48px; margin-bottom: 8px;">📷</div>
                                                        <div>Tidak ada foto masuk</div>
                                                    </div>
                                                </div>
                                            ');
                                        }

                                        $photoUrl = asset('storage/' . $record->foto_masuk);
                                        $metadata = $record->metadata_foto_masuk ?? [];
                                        $hasMetadata = !empty($metadata);

                                        // Format metadata untuk display
                                        $formattedMetadata = \App\Services\PhotoMetadataService::formatMetadataForDisplay($metadata);

                                        return new \Illuminate\Support\HtmlString('
                                            <div style="position: relative; display: inline-block; border-radius: 12px; overflow: hidden; box-shadow: 0 8px 32px rgba(0,0,0,0.12);">
                                                <img src="' . $photoUrl . '"
                                                     style="
                                                        width: 300px;
                                                        height: 300px;
                                                        object-fit: cover;
                                                        cursor: pointer;
                                                        transition: transform 0.3s ease;
                                                     "
                                                     onclick="window.open(\'' . $photoUrl . '\', \'_blank\')"
                                                     onmouseover="this.style.transform=\'scale(1.02)\'"
                                                     onmouseout="this.style.transform=\'scale(1)\'"
                                                     title="Klik untuk melihat ukuran penuh">

                                                <!-- Camera info overlay (top right) -->
                                                ' . (isset($formattedMetadata['camera_info']) ? '
                                                <div style="
                                                    position: absolute;
                                                    top: 12px;
                                                    right: 12px;
                                                    background: rgba(255,255,255,0.95);
                                                    color: #1f2937;
                                                    padding: 6px 12px;
                                                    border-radius: 20px;
                                                    font-size: 11px;
                                                    font-weight: 600;
                                                    backdrop-filter: blur(10px);
                                                    border: 1px solid rgba(255,255,255,0.2);
                                                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                                                ">
                                                    📱 ' . $formattedMetadata['camera_info'] . '
                                                </div>' : '') . '

                                                <!-- Metadata indicator (top left) -->
                                                ' . ($hasMetadata ? '
                                                <div style="
                                                    position: absolute;
                                                    top: 12px;
                                                    left: 12px;
                                                    width: 24px;
                                                    height: 24px;
                                                    background: linear-gradient(135deg, #10B981, #059669);
                                                    border: 2px solid white;
                                                    border-radius: 50%;
                                                    display: flex;
                                                    align-items: center;
                                                    justify-content: center;
                                                    color: white;
                                                    font-size: 12px;
                                                    font-weight: bold;
                                                    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
                                                " title="Foto dengan metadata GPS">
                                                    ✓
                                                </div>' : '') . '

                                                <!-- Bottom overlay with metadata -->
                                                <div style="
                                                    position: absolute;
                                                    bottom: 0;
                                                    left: 0;
                                                    right: 0;
                                                    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 70%, transparent 100%);
                                                    color: white;
                                                    padding: 16px;
                                                    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                                                ">
                                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                        <div style="
                                                            background: linear-gradient(135deg, #3B82F6, #1D4ED8);
                                                            padding: 4px 8px;
                                                            border-radius: 6px;
                                                            font-size: 12px;
                                                            font-weight: 600;
                                                        ">
                                                            📸 MASUK
                                                        </div>
                                                        ' . (isset($formattedMetadata['status_kehadiran']) ? '
                                                        <div style="
                                                            background: ' . ($formattedMetadata['status_kehadiran'] === 'Tepat Waktu' ? 'linear-gradient(135deg, #10B981, #059669)' : 'linear-gradient(135deg, #F59E0B, #D97706)') . ';
                                                            padding: 4px 8px;
                                                            border-radius: 6px;
                                                            font-size: 12px;
                                                            font-weight: 600;
                                                        ">
                                                            ' . ($formattedMetadata['status_kehadiran'] === 'Tepat Waktu' ? '✅' : '⏰') . ' ' . $formattedMetadata['status_kehadiran'] . '
                                                        </div>' : '') . '
                                                    </div>

                                                    ' . (isset($formattedMetadata['datetime_display']) ? '
                                                    <div style="font-size: 13px; margin-bottom: 4px; opacity: 0.9;">
                                                        🕐 ' . $formattedMetadata['datetime_display'] . '
                                                    </div>' : '') . '

                                                    ' . (isset($formattedMetadata['coordinates_display']) ? '
                                                    <div style="font-size: 12px; opacity: 0.8; font-family: monospace;">
                                                        📍 ' . $formattedMetadata['coordinates_display'] . '
                                                    </div>' : '') . '
                                                </div>
                                            </div>
                                        ');
                                    })
                                    ->html(),

                                Infolists\Components\TextEntry::make('foto_keluar_display')
                                    ->label('Foto Keluar')
                                    ->formatStateUsing(function ($record) {
                                        if (!$record->foto_keluar) {
                                            return new \Illuminate\Support\HtmlString('
                                                <div style="
                                                    width: 300px;
                                                    height: 300px;
                                                    border: 2px dashed #d1d5db;
                                                    border-radius: 12px;
                                                    display: flex;
                                                    align-items: center;
                                                    justify-content: center;
                                                    color: #9ca3af;
                                                    font-size: 16px;
                                                    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
                                                    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                                                ">
                                                    <div style="text-align: center;">
                                                        <div style="font-size: 48px; margin-bottom: 8px;">🏠</div>
                                                        <div>Tidak ada foto keluar</div>
                                                    </div>
                                                </div>
                                            ');
                                        }

                                        $photoUrl = asset('storage/' . $record->foto_keluar);
                                        $metadata = $record->metadata_foto_keluar ?? [];
                                        $hasMetadata = !empty($metadata);

                                        // Format metadata untuk display
                                        $formattedMetadata = \App\Services\PhotoMetadataService::formatMetadataForDisplay($metadata);

                                        return new \Illuminate\Support\HtmlString('
                                            <div style="position: relative; display: inline-block; border-radius: 12px; overflow: hidden; box-shadow: 0 8px 32px rgba(0,0,0,0.12);">
                                                <img src="' . $photoUrl . '"
                                                     style="
                                                        width: 300px;
                                                        height: 300px;
                                                        object-fit: cover;
                                                        cursor: pointer;
                                                        transition: transform 0.3s ease;
                                                     "
                                                     onclick="window.open(\'' . $photoUrl . '\', \'_blank\')"
                                                     onmouseover="this.style.transform=\'scale(1.02)\'"
                                                     onmouseout="this.style.transform=\'scale(1)\'"
                                                     title="Klik untuk melihat ukuran penuh">

                                                <!-- Camera info overlay (top right) -->
                                                ' . (isset($formattedMetadata['camera_info']) ? '
                                                <div style="
                                                    position: absolute;
                                                    top: 12px;
                                                    right: 12px;
                                                    background: rgba(255,255,255,0.95);
                                                    color: #1f2937;
                                                    padding: 6px 12px;
                                                    border-radius: 20px;
                                                    font-size: 11px;
                                                    font-weight: 600;
                                                    backdrop-filter: blur(10px);
                                                    border: 1px solid rgba(255,255,255,0.2);
                                                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                                                ">
                                                    📱 ' . $formattedMetadata['camera_info'] . '
                                                </div>' : '') . '

                                                <!-- Metadata indicator (top left) -->
                                                ' . ($hasMetadata ? '
                                                <div style="
                                                    position: absolute;
                                                    top: 12px;
                                                    left: 12px;
                                                    width: 24px;
                                                    height: 24px;
                                                    background: linear-gradient(135deg, #10B981, #059669);
                                                    border: 2px solid white;
                                                    border-radius: 50%;
                                                    display: flex;
                                                    align-items: center;
                                                    justify-content: center;
                                                    color: white;
                                                    font-size: 12px;
                                                    font-weight: bold;
                                                    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
                                                " title="Foto dengan metadata GPS">
                                                    ✓
                                                </div>' : '') . '

                                                <!-- Bottom overlay with metadata -->
                                                <div style="
                                                    position: absolute;
                                                    bottom: 0;
                                                    left: 0;
                                                    right: 0;
                                                    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 70%, transparent 100%);
                                                    color: white;
                                                    padding: 16px;
                                                    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                                                ">
                                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                        <div style="
                                                            background: linear-gradient(135deg, #EF4444, #DC2626);
                                                            padding: 4px 8px;
                                                            border-radius: 6px;
                                                            font-size: 12px;
                                                            font-weight: 600;
                                                        ">
                                                            🏠 KELUAR
                                                        </div>
                                                        ' . (isset($formattedMetadata['status_kehadiran']) ? '
                                                        <div style="
                                                            background: ' . ($formattedMetadata['status_kehadiran'] === 'Tepat Waktu' ? 'linear-gradient(135deg, #10B981, #059669)' : 'linear-gradient(135deg, #F59E0B, #D97706)') . ';
                                                            padding: 4px 8px;
                                                            border-radius: 6px;
                                                            font-size: 12px;
                                                            font-weight: 600;
                                                        ">
                                                            ' . ($formattedMetadata['status_kehadiran'] === 'Tepat Waktu' ? '✅' : '⏰') . ' ' . $formattedMetadata['status_kehadiran'] . '
                                                        </div>' : '') . '
                                                    </div>

                                                    ' . (isset($formattedMetadata['datetime_display']) ? '
                                                    <div style="font-size: 13px; margin-bottom: 4px; opacity: 0.9;">
                                                        🕐 ' . $formattedMetadata['datetime_display'] . '
                                                    </div>' : '') . '

                                                    ' . (isset($formattedMetadata['coordinates_display']) ? '
                                                    <div style="font-size: 12px; opacity: 0.8; font-family: monospace;">
                                                        📍 ' . $formattedMetadata['coordinates_display'] . '
                                                    </div>' : '') . '
                                                </div>
                                            </div>
                                        ');
                                    })
                                    ->html(),
                            ]),
                    ]),

                Infolists\Components\Section::make('Metadata Foto')
                    ->schema([
                        Infolists\Components\TextEntry::make('metadata_info')
                            ->label('Informasi Metadata')
                            ->formatStateUsing(function ($record) {
                                $content = '';

                                // Metadata foto masuk
                                if ($record->metadata_foto_masuk) {
                                    $metadata = $record->metadata_foto_masuk;
                                    $content .= '<div style="background: #f0f9ff; padding: 16px; border-radius: 8px; border: 1px solid #0ea5e9; margin-bottom: 16px;">';
                                    $content .= '<h4 style="margin: 0 0 12px 0; font-weight: 600; color: #0c4a6e;">📸 Metadata Foto Masuk</h4>';

                                    if (isset($metadata['datetime_display'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>🕐 Waktu:</strong> ' . $metadata['datetime_display'] . '</div>';
                                    }
                                    if (isset($metadata['coordinates_display'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>📍 Koordinat:</strong> ' . $metadata['coordinates_display'] . '</div>';
                                    }
                                    if (isset($metadata['status_kehadiran'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>⏰ Status:</strong> ' . $metadata['status_kehadiran'] . '</div>';
                                    }
                                    if (isset($metadata['camera_info'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>📱 Device:</strong> ' . $metadata['camera_info'] . '</div>';
                                    }
                                    $content .= '</div>';
                                }

                                // Metadata foto keluar
                                if ($record->metadata_foto_keluar) {
                                    $metadata = $record->metadata_foto_keluar;
                                    $content .= '<div style="background: #fef2f2; padding: 16px; border-radius: 8px; border: 1px solid #f87171; margin-bottom: 16px;">';
                                    $content .= '<h4 style="margin: 0 0 12px 0; font-weight: 600; color: #7f1d1d;">🏠 Metadata Foto Keluar</h4>';

                                    if (isset($metadata['datetime_display'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>🕐 Waktu:</strong> ' . $metadata['datetime_display'] . '</div>';
                                    }
                                    if (isset($metadata['coordinates_display'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>📍 Koordinat:</strong> ' . $metadata['coordinates_display'] . '</div>';
                                    }
                                    if (isset($metadata['status_kehadiran'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>⏰ Status:</strong> ' . $metadata['status_kehadiran'] . '</div>';
                                    }
                                    if (isset($metadata['camera_info'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>📱 Device:</strong> ' . $metadata['camera_info'] . '</div>';
                                    }
                                    $content .= '</div>';
                                }

                                if (empty($content)) {
                                    $content = '<div style="color: #9ca3af; font-style: italic;">Tidak ada metadata foto tersedia</div>';
                                }

                                return new \Illuminate\Support\HtmlString($content);
                            })
                            ->html()
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => !empty($record->metadata_foto_masuk) || !empty($record->metadata_foto_keluar)),

                Infolists\Components\Section::make('Persetujuan')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('approvedBy.name')
                                    ->label('Disetujui Oleh')
                                    ->placeholder('Belum disetujui'),
                                Infolists\Components\TextEntry::make('approved_at')
                                    ->label('Tanggal Persetujuan')
                                    ->dateTime('d M Y H:i')
                                    ->placeholder('Belum disetujui'),
                            ]),
                    ]),
            ]);
    }
}
