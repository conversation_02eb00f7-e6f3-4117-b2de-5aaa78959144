<?php

namespace App\Filament\Karyawan\Resources\CutiIzinResource\Pages;

use App\Filament\Karyawan\Resources\CutiIzinResource;
use App\Models\CutiIzin;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;

class EditCutiIzin extends EditRecord
{
    protected static string $resource = CutiIzinResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make()
                ->visible(fn() => $this->record->status === 'pending'),
        ];
    }

    public function getTitle(): string
    {
        return 'Edit Permohonan Cuti/Izin';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Only allow editing if status is pending
        if ($record->status !== 'pending') {
            Notification::make()
                ->title('Tidak Dapat Mengedit')
                ->body('Permohonan yang sudah disetujui atau ditolak tidak dapat diedit.')
                ->danger()
                ->send();

            $this->halt();
        }

        // Validate dates and quota before updating
        $tempModel = new CutiIzin(array_merge($record->toArray(), $data));
        $tempModel->id = $record->id; // Set ID for update validation
        $errors = $tempModel->validateDates();

        if (!empty($errors)) {
            foreach ($errors as $error) {
                // Check if it's a warning (monthly quota) or blocking error
                if (str_contains($error, '⚠️ Peringatan:')) {
                    Notification::make()
                        ->title('Peringatan Kuota Bulanan')
                        ->body($error)
                        ->warning()
                        ->send();
                } else {
                    Notification::make()
                        ->title('Validasi Gagal')
                        ->body($error)
                        ->danger()
                        ->send();

                    $this->halt();
                }
            }
        }

        // Update the record
        $record->update($data);

        // Send success notification
        Notification::make()
            ->title('Permohonan Berhasil Diperbarui')
            ->body('Permohonan cuti/izin Anda telah berhasil diperbarui.')
            ->success()
            ->send();

        return $record;
    }
}
