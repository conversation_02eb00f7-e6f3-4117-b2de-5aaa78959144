<?php

namespace App\Filament\Karyawan\Resources\CutiIzinResource\Pages;

use App\Filament\Karyawan\Resources\CutiIzinResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCutiIzins extends ListRecords
{
    protected static string $resource = CutiIzinResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Ajukan Cuti/Izin/Sakit')
                ->icon('heroicon-o-plus'),
        ];
    }

    public function getTitle(): string
    {
        return 'Cuti, Izin & Sakit';
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // Add widgets if needed
        ];
    }
}
