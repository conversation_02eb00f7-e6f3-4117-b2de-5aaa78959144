<?php

namespace App\Filament\Karyawan\Resources\CutiIzinResource\Pages;

use App\Filament\Karyawan\Resources\CutiIzinResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewCutiIzin extends ViewRecord
{
    protected static string $resource = CutiIzinResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn() => $this->record->status === 'pending'),
        ];
    }

    public function getTitle(): string
    {
        return 'Detail Permohonan Cuti/Izin';
    }
}
