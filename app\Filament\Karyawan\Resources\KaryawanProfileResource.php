<?php

namespace App\Filament\Karyawan\Resources;

use App\Models\Karyawan;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\ImageEntry;
use App\Filament\Karyawan\Resources\KaryawanProfileResource\Pages;
use App\Filament\Karyawan\Resources\KaryawanProfileResource\RelationManagers;

class KaryawanProfileResource extends Resource
{
    protected static ?string $model = Karyawan::class;
    protected static ?string $navigationIcon = 'heroicon-o-user-circle';
    protected static ?string $navigationLabel = 'Profil Saya';
    protected static ?string $modelLabel = 'Profil Karyawan';
    protected static ?string $pluralModelLabel = 'Profil Karyawan';
    protected static ?string $navigationGroup = 'Pengaturan';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak diperlukan karena read-only
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nama_lengkap')
                    ->label('Nama Lengkap')
                    ->searchable(),

                Tables\Columns\TextColumn::make('nip')
                    ->label('NIP')
                    ->searchable(),

                Tables\Columns\TextColumn::make('entitas.nama')
                    ->label('Entitas')
                    ->sortable(),

                Tables\Columns\TextColumn::make('jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('Lihat Detail'),
            ])
            ->bulkActions([
                // No bulk actions for employee view
            ]);
    }

    // Infolist akan didefinisikan di ViewKaryawanProfile untuk tampilan yang lebih kompleks

    public static function getRelations(): array
    {
        return [
            RelationManagers\RiwayatKontrakRelationManager::class,
            RelationManagers\PenggajianKaryawanRelationManager::class,
            RelationManagers\PayrollRelationManager::class,
            RelationManagers\ScheduleRelationManager::class,
            RelationManagers\KpiPenilaianRelationManager::class,
            RelationManagers\DokumenRelationManager::class,
            RelationManagers\PelanggaranRelationManager::class,
            RelationManagers\PendidikanRelationManager::class,
            RelationManagers\KerabatDaruratRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKaryawanProfiles::route('/'),
            'view' => Pages\ViewKaryawanProfile::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        // Hanya tampilkan data karyawan yang sedang login
        $user = Auth::user();
        return parent::getEloquentQuery()
            ->where('id_user', $user->id);
    }



    public static function canEdit($record): bool
    {
        return false; // Karyawan tidak bisa edit
    }

    public static function canDelete($record): bool
    {
        return false; // Karyawan tidak bisa delete
    }
}
