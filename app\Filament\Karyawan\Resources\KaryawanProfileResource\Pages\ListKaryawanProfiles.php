<?php

namespace App\Filament\Karyawan\Resources\KaryawanProfileResource\Pages;

use App\Filament\Karyawan\Resources\KaryawanProfileResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Auth;

class ListKaryawanProfiles extends ListRecords
{
    protected static string $resource = KaryawanProfileResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No create action for employee
        ];
    }

    public function mount(): void
    {
        // Redirect directly to view page if only one record (current user)
        $user = Auth::user();
        $karyawan = \App\Models\Karyawan::where('id_user', $user->id)->first();

        if ($karyawan) {
            $this->redirect(KaryawanProfileResource::getUrl('view', ['record' => $karyawan]));
        } else {
            // If no karyawan found, show error message
            \Filament\Notifications\Notification::make()
                ->title('Data Karyawan Tidak Ditemukan')
                ->body('Akun Anda belum terkait dengan data karyawan. Silakan hubungi administrator.')
                ->danger()
                ->persistent()
                ->send();
        }
    }
}
