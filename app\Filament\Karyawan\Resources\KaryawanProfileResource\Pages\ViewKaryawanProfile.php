<?php

namespace App\Filament\Karyawan\Resources\KaryawanProfileResource\Pages;

use App\Filament\Karyawan\Resources\KaryawanProfileResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Tabs;
use App\Models\Karyawan;

class ViewKaryawanProfile extends ViewRecord
{
    protected static string $resource = KaryawanProfileResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No edit action for employee
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('👤 Profil Karyawan')
                    ->description('Informasi personal dan data karyawan')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                ImageEntry::make('foto_profil')
                                    ->label('Foto Profil')
                                    ->circular()
                                    ->defaultImageUrl('https://ui-avatars.com/api/?name=' . urlencode('User') . '&color=7F9CF5&background=EBF4FF')
                                    ->size(120),

                                Grid::make(1)
                                    ->schema([
                                        TextEntry::make('nama_lengkap')
                                            ->label('Nama Lengkap')
                                            ->weight('bold')
                                            ->size('lg')
                                            ->icon('heroicon-m-user'),

                                        TextEntry::make('nip')
                                            ->label('NIP')
                                            ->copyable()
                                            ->icon('heroicon-m-identification')
                                            ->badge()
                                            ->color('primary'),

                                        TextEntry::make('email')
                                            ->label('Email')
                                            ->copyable()
                                            ->icon('heroicon-m-envelope'),
                                    ])->columns(3),

                                Grid::make(1)
                                    ->schema([
                                        TextEntry::make('entitas.nama')
                                            ->label('Entitas')
                                            ->icon('heroicon-m-building-office')
                                            ->badge()
                                            ->color('success'),

                                        TextEntry::make('jabatan.nama_jabatan')
                                            ->label('Jabatan')
                                            ->icon('heroicon-m-briefcase')
                                            ->badge()
                                            ->color('info'),

                                        TextEntry::make('status_aktif')
                                            ->label('Status')
                                            ->badge()
                                            ->color(fn(string $state): string => $state === 'Aktif' ? 'success' : 'danger')
                                            ->icon(fn(string $state): string => $state === 'Aktif' ? 'heroicon-m-check-circle' : 'heroicon-m-x-circle'),
                                    ])->columns(3),
                            ]),
                    ])
                    ->collapsible(),

                Section::make('📋 Informasi Detail')
                    ->description('Data personal dan kontak')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('nik')
                                    ->label('NIK')
                                    ->copyable()
                                    ->icon('heroicon-m-identification'),

                                TextEntry::make('nomor_kk')
                                    ->label('Nomor KK')
                                    ->copyable()
                                    ->icon('heroicon-m-document-text'),

                                TextEntry::make('jenis_kelamin')
                                    ->label('Jenis Kelamin')
                                    ->badge()
                                    ->color(fn(string $state): string => $state === 'Laki-laki' ? 'blue' : 'pink')
                                    ->icon(fn(string $state): string => $state === 'Laki-laki' ? 'heroicon-m-user' : 'heroicon-m-user'),

                                TextEntry::make('agama')
                                    ->label('Agama')
                                    ->icon('heroicon-m-heart'),

                                TextEntry::make('status_pernikahan')
                                    ->label('Status Pernikahan')
                                    ->badge()
                                    ->color('gray')
                                    ->icon('heroicon-m-heart'),

                                TextEntry::make('jumlah_anak')
                                    ->label('Jumlah Anak')
                                    ->icon('heroicon-m-users')
                                    ->suffix(' orang'),
                            ]),
                    ])
                    ->collapsible(),

                Section::make('📍 Informasi Tempat & Tanggal Lahir')
                    ->description('Data kelahiran dan alamat')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('kota_lahir')
                                    ->label('Tempat Lahir')
                                    ->icon('heroicon-m-map-pin'),

                                TextEntry::make('tanggal_lahir')
                                    ->label('Tanggal Lahir')
                                    ->date('d F Y')
                                    ->icon('heroicon-m-calendar-days'),

                                TextEntry::make('umur')
                                    ->label('Umur')
                                    ->suffix(' tahun')
                                    ->icon('heroicon-m-clock')
                                    ->badge()
                                    ->color('warning'),

                                TextEntry::make('golongan_darah')
                                    ->label('Golongan Darah')
                                    ->badge()
                                    ->color('danger')
                                    ->icon('heroicon-m-heart'),
                            ]),
                    ])
                    ->collapsible(),

                Section::make('🏠 Alamat & Kontak')
                    ->description('Informasi alamat dan kontak')
                    ->schema([
                        TextEntry::make('alamat')
                            ->label('Alamat Domisili')
                            ->icon('heroicon-m-home')
                            ->columnSpanFull(),

                        TextEntry::make('alamat_ktp')
                            ->label('Alamat KTP')
                            ->icon('heroicon-m-map')
                            ->columnSpanFull(),

                        Grid::make(2)
                            ->schema([
                                TextEntry::make('nomor_telepon')
                                    ->label('Nomor Telepon')
                                    ->copyable()
                                    ->icon('heroicon-m-phone')
                                    ->url(fn($state) => 'tel:' . $state),

                                TextEntry::make('nama_ibu_kandung')
                                    ->label('Nama Ibu Kandung')
                                    ->icon('heroicon-m-user'),
                            ]),
                    ])
                    ->collapsible(),

                Section::make('🏢 Informasi Pekerjaan')
                    ->description('Data jabatan dan organisasi')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('divisi.nama_divisi')
                                    ->label('Divisi')
                                    ->icon('heroicon-m-building-office-2')
                                    ->badge()
                                    ->color('purple'),

                                TextEntry::make('supervisor.name')
                                    ->label('Supervisor')
                                    ->icon('heroicon-m-user-group')
                                    ->default('Belum ditentukan'),

                                TextEntry::make('created_at')
                                    ->label('Bergabung Sejak')
                                    ->date('d F Y')
                                    ->icon('heroicon-m-calendar')
                                    ->badge()
                                    ->color('success'),
                            ]),
                    ])
                    ->collapsible(),
            ]);
    }
}
