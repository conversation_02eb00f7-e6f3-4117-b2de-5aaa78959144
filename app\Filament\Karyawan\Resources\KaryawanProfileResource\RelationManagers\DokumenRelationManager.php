<?php

namespace App\Filament\Karyawan\Resources\KaryawanProfileResource\RelationManagers;

use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class DokumenRelationManager extends RelationManager
{
    protected static string $relationship = 'dokumens';

    protected static ?string $title = 'Dokumen Karyawan';

    protected static ?string $modelLabel = 'Dokumen';

    protected static ?string $pluralModelLabel = 'Dokumen Karyawan';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak diperlukan karena read-only
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('nama_dokumen')
            ->columns([
                Tables\Columns\TextColumn::make('nama_dokumen')
                    ->label('Nama Dokumen')
                    ->searchable()
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('file_path')
                    ->label('File')
                    ->formatStateUsing(fn($state): string => $state ? 'Ada File' : 'Belum Upload')
                    ->badge()
                    ->color(fn($state): string => $state ? 'success' : 'danger')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tanggal Upload')
                    ->date('d M Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_by')
                    ->label('Dibuat Oleh')
                    ->getStateUsing(function ($record) {
                        return $record->creator?->name ?? 'System';
                    })
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('has_file')
                    ->label('Ada File')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('file_path')),

                Tables\Filters\Filter::make('no_file')
                    ->label('Belum Upload')
                    ->query(fn(Builder $query): Builder => $query->whereNull('file_path')),
            ])
            ->headerActions([
                // No create action for employee view
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Dokumen')
                    ->modalContent(function ($record) {
                        return view('filament.karyawan.dokumen-detail', compact('record'));
                    }),

                Tables\Actions\Action::make('download')
                    ->label('Download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('primary')
                    ->visible(fn($record) => $record->file_path)
                    ->url(fn($record) => asset('storage/' . $record->file_path))
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                // No bulk actions for employee view
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Belum Ada Dokumen')
            ->emptyStateDescription('Belum ada dokumen yang diupload.')
            ->emptyStateIcon('heroicon-o-document');
    }



    public function isReadOnly(): bool
    {
        return true; // Employee cannot create, edit, or delete
    }
}
