<?php

namespace App\Filament\Karyawan\Resources\KaryawanProfileResource\RelationManagers;

use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class KerabatDaruratRelationManager extends RelationManager
{
    protected static string $relationship = 'kerabatDarurat';

    protected static ?string $title = 'Kontak Darurat';

    protected static ?string $modelLabel = 'Kontak';

    protected static ?string $pluralModelLabel = 'Kontak Darurat';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak diperlukan karena read-only
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('nama_kerabat')
            ->columns([
                Tables\Columns\TextColumn::make('nama_kerabat')
                    ->label('Nama')
                    ->searchable()
                    ->weight('bold')
                    ->icon('heroicon-m-user'),

                Tables\Columns\TextColumn::make('hubungan')
                    ->label('Hubungan')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Ayah' => 'blue',
                        'Ibu' => 'pink',
                        'Suami' => 'indigo',
                        'Istri' => 'purple',
                        'Anak' => 'green',
                        'Saudara' => 'yellow',
                        'Kerabat' => 'gray',
                        'Teman' => 'orange',
                        default => 'primary',
                    })
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('no_hp_kerabat')
                    ->label('No. Telepon')
                    ->searchable()
                    ->copyable()
                    ->icon('heroicon-m-phone')
                    ->color('primary'),

                Tables\Columns\TextColumn::make('alamat_kerabat')
                    ->label('Alamat')
                    ->limit(40)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 40) {
                            return null;
                        }
                        return $state;
                    })
                    ->icon('heroicon-m-map-pin'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('hubungan')
                    ->label('Hubungan')
                    ->options([
                        'Ayah' => 'Ayah',
                        'Ibu' => 'Ibu',
                        'Suami' => 'Suami',
                        'Istri' => 'Istri',
                        'Anak' => 'Anak',
                        'Saudara' => 'Saudara',
                        'Kerabat' => 'Kerabat',
                        'Teman' => 'Teman',
                    ]),


            ])
            ->headerActions([
                // No create action for employee view
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Kontak Darurat')
                    ->modalContent(function ($record) {
                        return view('filament.karyawan.kerabat-detail', compact('record'));
                    }),

                Tables\Actions\Action::make('call')
                    ->label('Telepon')
                    ->icon('heroicon-o-phone')
                    ->color('success')
                    ->url(fn($record) => 'tel:' . $record->no_hp_kerabat)
                    ->visible(fn($record) => !empty($record->no_hp_kerabat)),

                Tables\Actions\Action::make('whatsapp')
                    ->label('WhatsApp')
                    ->icon('heroicon-o-chat-bubble-left-right')
                    ->color('green')
                    ->url(fn($record) => 'https://wa.me/' . preg_replace('/[^0-9]/', '', $record->no_hp_kerabat))
                    ->openUrlInNewTab()
                    ->visible(fn($record) => !empty($record->no_hp_kerabat)),
            ])
            ->bulkActions([
                // No bulk actions for employee view
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Belum Ada Kontak Darurat')
            ->emptyStateDescription('Belum ada kontak darurat yang terdaftar.')
            ->emptyStateIcon('heroicon-o-phone');
    }

    public function isReadOnly(): bool
    {
        return true; // Employee cannot create, edit, or delete
    }
}
