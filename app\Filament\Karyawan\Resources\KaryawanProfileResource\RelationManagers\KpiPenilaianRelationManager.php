<?php

namespace App\Filament\Karyawan\Resources\KaryawanProfileResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class KpiPenilaianRelationManager extends RelationManager
{
    protected static string $relationship = 'kpiPenilaians';

    protected static ?string $title = 'Penilaian KPI';

    protected static ?string $modelLabel = 'Penilaian';

    protected static ?string $pluralModelLabel = 'Penilaian KPI';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak diperlukan karena read-only
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('periode')
            ->columns([
                Tables\Columns\TextColumn::make('periode')
                    ->label('Periode')
                    ->sortable()
                    ->searchable()
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('target_kpi')
                    ->label('Target KPI')
                    ->numeric()
                    ->sortable()
                    ->suffix('%'),

                Tables\Columns\TextColumn::make('realisasi_kpi')
                    ->label('Realisasi KPI')
                    ->numeric()
                    ->sortable()
                    ->suffix('%')
                    ->color(
                        fn($state, $record) =>
                        $state >= $record->target_kpi ? 'success' : ($state >= ($record->target_kpi * 0.8) ? 'warning' : 'danger')
                    ),

                Tables\Columns\TextColumn::make('pencapaian')
                    ->label('Pencapaian')
                    ->getStateUsing(function ($record) {
                        if (!$record->target_kpi || $record->target_kpi == 0) return 0;
                        return round(($record->realisasi_kpi / $record->target_kpi) * 100, 2);
                    })
                    ->suffix('%')
                    ->badge()
                    ->color(
                        fn($state) =>
                        $state >= 100 ? 'success' : ($state >= 80 ? 'warning' : 'danger')
                    )
                    ->sortable(),

                Tables\Columns\TextColumn::make('nilai_akhir')
                    ->label('Nilai Akhir')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'A' => 'success',
                        'B' => 'info',
                        'C' => 'warning',
                        'D' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('status_penilaian')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Selesai' => 'success',
                        'Proses' => 'warning',
                        'Draft' => 'gray',
                        default => 'primary',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('penilai.name')
                    ->label('Penilai')
                    ->default('Belum ditentukan')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('tanggal_penilaian')
                    ->label('Tanggal Penilaian')
                    ->date('d M Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('nilai_akhir')
                    ->label('Nilai Akhir')
                    ->options([
                        'A' => 'A (Sangat Baik)',
                        'B' => 'B (Baik)',
                        'C' => 'C (Cukup)',
                        'D' => 'D (Kurang)',
                    ]),

                Tables\Filters\SelectFilter::make('status_penilaian')
                    ->label('Status Penilaian')
                    ->options([
                        'Selesai' => 'Selesai',
                        'Proses' => 'Proses',
                        'Draft' => 'Draft',
                    ]),

                Tables\Filters\Filter::make('periode')
                    ->form([
                        Forms\Components\TextInput::make('periode')
                            ->label('Periode (YYYY-MM)')
                            ->placeholder('2025-05'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['periode'],
                                fn(Builder $query, $periode): Builder => $query->where('periode', 'like', "%{$periode}%"),
                            );
                    }),

                Tables\Filters\Filter::make('pencapaian_tinggi')
                    ->label('Pencapaian ≥ 100%')
                    ->query(
                        fn(Builder $query): Builder =>
                        $query->whereRaw('(realisasi_kpi / target_kpi) * 100 >= 100')
                    ),
            ])
            ->headerActions([
                // No create action for employee view
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Penilaian KPI')
                    ->modalContent(function ($record) {
                        return view('filament.karyawan.kpi-detail', compact('record'));
                    }),
            ])
            ->bulkActions([
                // No bulk actions for employee view
            ])
            ->defaultSort('periode', 'desc')
            ->emptyStateHeading('Belum Ada Penilaian KPI')
            ->emptyStateDescription('Belum ada data penilaian KPI.')
            ->emptyStateIcon('heroicon-o-chart-bar');
    }

    public function isReadOnly(): bool
    {
        return true; // Employee cannot create, edit, or delete
    }
}
