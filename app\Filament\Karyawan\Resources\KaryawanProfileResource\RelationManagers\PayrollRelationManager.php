<?php

namespace App\Filament\Karyawan\Resources\KaryawanProfileResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PayrollRelationManager extends RelationManager
{
    protected static string $relationship = 'payrollTransactions';

    protected static ?string $title = 'Riwayat Payroll';

    protected static ?string $modelLabel = 'Payroll';

    protected static ?string $pluralModelLabel = 'Riwayat Payroll';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak diperlukan karena read-only
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('no_payroll')
            ->columns([
                Tables\Columns\TextColumn::make('no_payroll')
                    ->label('No. Payroll')
                    ->searchable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('payrollPeriod.nama_periode')
                    ->label('Periode')
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('gaji_kotor')
                    ->label('Gaji Kotor')
                    ->money('IDR')
                    ->getStateUsing(fn($record) => $record->gaji_pokok + $record->tunjangan_jabatan + $record->tunjangan_umum + $record->tunjangan_sembako),

                Tables\Columns\TextColumn::make('total_potongan')
                    ->label('Total Potongan')
                    ->money('IDR')
                    ->color('danger')
                    ->getStateUsing(fn($record) => $record->potongan_bpjs_kesehatan + $record->potongan_bpjs_tk + $record->potongan_keterlambatan + $record->potongan_pelanggaran + $record->potongan_lainnya),

                Tables\Columns\TextColumn::make('take_home_pay')
                    ->label('Take Home Pay')
                    ->money('IDR')
                    ->color('success')
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'draft' => 'gray',
                        'approved' => 'success',
                        'paid' => 'primary',
                        'rejected' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('approved_at')
                    ->label('Tanggal Approve')
                    ->dateTime('d M Y H:i')
                    ->placeholder('Belum diapprove'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'approved' => 'Approved',
                        'paid' => 'Paid',
                        'rejected' => 'Rejected',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Payroll')
                    ->modalContent(function ($record) {
                        return view('filament.karyawan.payroll-detail', compact('record'));
                    }),
            ])
            ->bulkActions([
                // No bulk actions for employee view
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Belum Ada Riwayat Payroll')
            ->emptyStateDescription('Belum ada data payroll yang diproses.')
            ->emptyStateIcon('heroicon-o-banknotes');
    }

    public function isReadOnly(): bool
    {
        return true; // Employee cannot create, edit, or delete
    }
}
