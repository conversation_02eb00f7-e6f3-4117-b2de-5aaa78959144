<?php

namespace App\Filament\Karyawan\Resources\KaryawanProfileResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class PelanggaranRelationManager extends RelationManager
{
    protected static string $relationship = 'pelanggaran';

    protected static ?string $title = 'Riwayat Pelanggaran';

    protected static ?string $modelLabel = 'Pelanggaran';

    protected static ?string $pluralModelLabel = 'Riwayat Pelanggaran';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak diperlukan karena read-only
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('tanggal')
            ->columns([
                Tables\Columns\TextColumn::make('tanggal')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('jenisPelanggaran.nama_pelanggaran')
                    ->label('Jenis Pelanggaran')
                    ->badge()
                    ->color('warning')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('jenisPelanggaran.denda')
                    ->label('Denda')
                    ->money('IDR')
                    ->color('danger'),

                Tables\Columns\TextColumn::make('keterangan')
                    ->label('Keterangan')
                    ->limit(50)
                    ->tooltip(function ($record) {
                        return $record->keterangan;
                    })
                    ->searchable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('bulan_ini')
                    ->label('Bulan Ini')
                    ->query(fn(Builder $query): Builder => $query->whereMonth('tanggal', now()->month)->whereYear('tanggal', now()->year)),

                Tables\Filters\Filter::make('3_bulan_terakhir')
                    ->label('3 Bulan Terakhir')
                    ->query(fn(Builder $query): Builder => $query->where('tanggal', '>=', now()->subMonths(3))),

                Tables\Filters\Filter::make('tanggal')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal', '<=', $date),
                            );
                    }),
            ])
            ->headerActions([
                Tables\Actions\Action::make('summary')
                    ->label('Ringkasan Pelanggaran')
                    ->icon('heroicon-o-chart-bar')
                    ->color('info')
                    ->action(function () {
                        // This will be handled by modal content
                    })
                    ->modalContent(function () {
                        $karyawan = $this->getOwnerRecord();

                        // Hitung total denda bulan ini
                        $dendaBulanIni = $karyawan->pelanggaran()
                            ->whereMonth('tanggal', now()->month)
                            ->whereYear('tanggal', now()->year)
                            ->get()
                            ->sum(function ($pelanggaran) {
                                // Gunakan nominal_denda yang sudah dihitung saat pelanggaran dibuat
                                return $pelanggaran->nominal_denda ?? 0;
                            });

                        // Hitung total denda 3 bulan terakhir
                        $denda3Bulan = $karyawan->pelanggaran()
                            ->where('tanggal', '>=', now()->subMonths(3))
                            ->get()
                            ->sum(function ($pelanggaran) {
                                // Gunakan nominal_denda yang sudah dihitung saat pelanggaran dibuat
                                return $pelanggaran->nominal_denda ?? 0;
                            });

                        // Hitung total pelanggaran per jenis
                        $pelanggaranPerJenis = $karyawan->pelanggaran()
                            ->with('jenisPelanggaran')
                            ->get()
                            ->groupBy('jenis_pelanggaran_id')
                            ->map(function ($group) {
                                $first = $group->first();
                                return [
                                    'nama' => $first->jenisPelanggaran->nama_pelanggaran ?? 'Unknown',
                                    'jumlah' => $group->count(),
                                    'total_denda' => $group->sum(function ($item) {
                                        // Gunakan nominal_denda yang sudah dihitung saat pelanggaran dibuat
                                        return $item->nominal_denda ?? 0;
                                    })
                                ];
                            });

                        return view('filament.karyawan.pelanggaran-summary', compact('dendaBulanIni', 'denda3Bulan', 'pelanggaranPerJenis'));
                    })
                    ->modalHeading('Ringkasan Pelanggaran')
                    ->modalWidth('2xl'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Pelanggaran')
                    ->modalContent(function ($record) {
                        return view('filament.karyawan.pelanggaran-detail', compact('record'));
                    }),
            ])
            ->bulkActions([
                // No bulk actions for employee view
            ])
            ->defaultSort('tanggal', 'desc')
            ->emptyStateHeading('Tidak Ada Pelanggaran')
            ->emptyStateDescription('Tidak ada catatan pelanggaran.')
            ->emptyStateIcon('heroicon-o-shield-check');
    }

    public function isReadOnly(): bool
    {
        return true; // Employee cannot create, edit, or delete
    }
}
