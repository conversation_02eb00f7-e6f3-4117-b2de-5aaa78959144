<?php

namespace App\Filament\Karyawan\Resources\KaryawanProfileResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class PendidikanRelationManager extends RelationManager
{
    protected static string $relationship = 'pendidikan';

    protected static ?string $title = 'Riwayat Pendidikan';

    protected static ?string $modelLabel = 'Pendidikan';

    protected static ?string $pluralModelLabel = 'Riwayat Pendidikan';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak diperlukan karena read-only
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('institusi')
            ->columns([
                Tables\Columns\TextColumn::make('tingkat')
                    ->label('Tingkat')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'SD' => 'gray',
                        'SMP' => 'blue',
                        'SMA/SMK' => 'green',
                        'D1' => 'yellow',
                        'D2' => 'orange',
                        'D3' => 'purple',
                        'S1' => 'indigo',
                        'S2' => 'pink',
                        'S3' => 'red',
                        default => 'primary',
                    })
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('institusi')
                    ->label('Institusi')
                    ->searchable()
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('jurusan')
                    ->label('Jurusan/Program Studi')
                    ->searchable()
                    ->limit(25)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 25) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('tahun_lulus')
                    ->label('Tahun Lulus')
                    ->sortable()
                    ->placeholder('Belum lulus'),

                Tables\Columns\TextColumn::make('ipk')
                    ->label('IPK/Nilai')
                    ->numeric(2)
                    ->sortable()
                    ->badge()
                    ->color(
                        fn($state) =>
                        $state >= 3.5 ? 'success' : ($state >= 3.0 ? 'warning' : ($state >= 2.5 ? 'orange' : 'danger'))
                    ),

                Tables\Columns\TextColumn::make('status_pendidikan')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Lulus' => 'success',
                        'Sedang Berjalan' => 'warning',
                        'Tidak Lulus' => 'danger',
                        'Cuti' => 'gray',
                        default => 'primary',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('no_ijazah')
                    ->label('No. Ijazah')
                    ->searchable()
                    ->copyable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('kota')
                    ->label('Kota')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\IconColumn::make('is_verified')
                    ->label('Terverifikasi')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-badge')
                    ->falseIcon('heroicon-o-question-mark-circle')
                    ->trueColor('success')
                    ->falseColor('gray'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenjang_pendidikan')
                    ->label('Jenjang Pendidikan')
                    ->options([
                        'SD' => 'SD',
                        'SMP' => 'SMP',
                        'SMA/SMK' => 'SMA/SMK',
                        'D1' => 'D1',
                        'D2' => 'D2',
                        'D3' => 'D3',
                        'S1' => 'S1',
                        'S2' => 'S2',
                        'S3' => 'S3',
                    ]),

                Tables\Filters\SelectFilter::make('status_pendidikan')
                    ->label('Status Pendidikan')
                    ->options([
                        'Lulus' => 'Lulus',
                        'Sedang Berjalan' => 'Sedang Berjalan',
                        'Tidak Lulus' => 'Tidak Lulus',
                        'Cuti' => 'Cuti',
                    ]),

                Tables\Filters\Filter::make('is_verified')
                    ->label('Sudah Terverifikasi')
                    ->query(fn(Builder $query): Builder => $query->where('is_verified', true)),

                Tables\Filters\Filter::make('ipk_tinggi')
                    ->label('IPK ≥ 3.5')
                    ->query(fn(Builder $query): Builder => $query->where('ipk', '>=', 3.5)),

                Tables\Filters\Filter::make('tahun_lulus')
                    ->form([
                        Forms\Components\TextInput::make('from')
                            ->label('Dari Tahun')
                            ->numeric(),
                        Forms\Components\TextInput::make('until')
                            ->label('Sampai Tahun')
                            ->numeric(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn(Builder $query, $year): Builder => $query->where('tahun_lulus', '>=', $year),
                            )
                            ->when(
                                $data['until'],
                                fn(Builder $query, $year): Builder => $query->where('tahun_lulus', '<=', $year),
                            );
                    }),
            ])
            ->headerActions([
                // No create action for employee view
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Pendidikan')
                    ->modalContent(function ($record) {
                        return view('filament.karyawan.pendidikan-detail', compact('record'));
                    }),
            ])
            ->bulkActions([
                // No bulk actions for employee view
            ])
            ->defaultSort('tahun_lulus', 'desc')
            ->emptyStateHeading('Belum Ada Riwayat Pendidikan')
            ->emptyStateDescription('Belum ada data riwayat pendidikan.')
            ->emptyStateIcon('heroicon-o-academic-cap');
    }

    public function isReadOnly(): bool
    {
        return true; // Employee cannot create, edit, or delete
    }
}
