<?php

namespace App\Filament\Karyawan\Resources\KaryawanProfileResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PenggajianKaryawanRelationManager extends RelationManager
{
    protected static string $relationship = 'penggajian';

    protected static ?string $title = 'Riwayat Gaji';

    protected static ?string $modelLabel = 'Gaji';

    protected static ?string $pluralModelLabel = 'Riwayat Gaji';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak diperlukan karena read-only
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('periode_gaji')
            ->columns([
                Tables\Columns\TextColumn::make('no_penggajian')
                    ->label('No. Penggajian')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('periode_gaji')
                    ->label('Periode')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('gaji_pokok')
                    ->label('Gaji Pokok')
                    ->money('IDR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('tunjangan_jabatan')
                    ->label('Tunjangan Jabatan')
                    ->money('IDR')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('tunjangan_umum')
                    ->label('Tunjangan Umum')
                    ->money('IDR')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('tunjangan_sembako')
                    ->label('Tunjangan Sembako')
                    ->money('IDR')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('bpjs_kesehatan_dipotong')
                    ->label('BPJS Kesehatan')
                    ->money('IDR')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('bpjs_tk_dipotong')
                    ->label('BPJS Ketenagakerjaan')
                    ->money('IDR')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('potongan_lainnya')
                    ->label('Potongan Lainnya')
                    ->money('IDR')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('take_home_pay')
                    ->label('Take Home Pay')
                    ->money('IDR')
                    ->weight('bold')
                    ->color('success')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('periode_gaji')
                    ->form([
                        Forms\Components\TextInput::make('periode')
                            ->label('Periode (YYYY-MM)')
                            ->placeholder('2025-05'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['periode'],
                                fn(Builder $query, $periode): Builder => $query->where('periode_gaji', 'like', "%{$periode}%"),
                            );
                    }),
            ])
            ->headerActions([
                // No create action for employee view
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Gaji')
                    ->modalContent(function ($record) {
                        return view('filament.karyawan.gaji-detail', compact('record'));
                    }),
            ])
            ->bulkActions([
                // No bulk actions for employee view
            ])
            ->defaultSort('periode_gaji', 'desc')
            ->emptyStateHeading('Belum Ada Riwayat Gaji')
            ->emptyStateDescription('Belum ada data riwayat penggajian.')
            ->emptyStateIcon('heroicon-o-banknotes');
    }

    public function isReadOnly(): bool
    {
        return true; // Employee cannot create, edit, or delete
    }
}
