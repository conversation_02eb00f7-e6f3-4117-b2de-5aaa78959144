<?php

namespace App\Filament\Karyawan\Resources\KaryawanProfileResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class RiwayatKontrakRelationManager extends RelationManager
{
    protected static string $relationship = 'riwayatKontrak';

    protected static ?string $title = 'Riwayat Kontrak Kerja';

    protected static ?string $modelLabel = 'Kontrak';

    protected static ?string $pluralModelLabel = 'Riwayat Kontrak';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak diperlukan karena read-only
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('jenis_kontrak')
            ->columns([
                Tables\Columns\TextColumn::make('jenis_kontrak')
                    ->label('Jenis Kontrak')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'PKWTT' => 'success',
                        'PKWT' => 'warning',
                        'Probation' => 'info',
                        'Freelance' => 'gray',
                        default => 'primary',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('no_kontrak')
                    ->label('No. Kontrak')
                    ->searchable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('tgl_mulai')
                    ->label('Tanggal Mulai')
                    ->date('d M Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('tgl_selesai')
                    ->label('Tanggal Selesai')
                    ->date('d M Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('tanggal_mulai_kerja')
                    ->label('Mulai Kerja')
                    ->date('d M Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('durasi')
                    ->label('Durasi')
                    ->getStateUsing(function ($record) {
                        if (!$record->tgl_mulai || !$record->tgl_selesai) {
                            return '-';
                        }
                        $start = \Carbon\Carbon::parse($record->tgl_mulai);
                        $end = \Carbon\Carbon::parse($record->tgl_selesai);
                        return $start->diffInMonths($end) . ' bulan';
                    })
                    ->badge()
                    ->color('info'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis_kontrak')
                    ->label('Jenis Kontrak')
                    ->options([
                        'PKWTT' => 'PKWTT (Tetap)',
                        'PKWT' => 'PKWT (Kontrak)',
                        'Probation' => 'Probation',
                        'Freelance' => 'Freelance',
                    ]),

                Tables\Filters\Filter::make('is_active')
                    ->label('Kontrak Aktif')
                    ->query(fn(Builder $query): Builder => $query->where('is_active', true)),
            ])
            ->headerActions([
                // No create action for employee view
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Kontrak')
                    ->modalContent(function ($record) {
                        return view('filament.karyawan.kontrak-detail', compact('record'));
                    }),
            ])
            ->bulkActions([
                // No bulk actions for employee view
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Belum Ada Riwayat Kontrak')
            ->emptyStateDescription('Belum ada data riwayat kontrak kerja.')
            ->emptyStateIcon('heroicon-o-document-text');
    }

    public function isReadOnly(): bool
    {
        return true; // Employee cannot create, edit, or delete
    }
}
