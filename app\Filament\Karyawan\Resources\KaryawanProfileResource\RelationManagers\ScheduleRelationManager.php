<?php

namespace App\Filament\Karyawan\Resources\KaryawanProfileResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Carbon\Carbon;

class ScheduleRelationManager extends RelationManager
{
    protected static string $relationship = 'schedules';

    protected static ?string $title = 'Jadwal Kerja';

    protected static ?string $modelLabel = 'Jadwal';

    protected static ?string $pluralModelLabel = 'Jadwal Kerja';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak diperlukan karena read-only
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('tanggal_jadwal')
            ->columns([
                Tables\Columns\TextColumn::make('tanggal_jadwal')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('hari')
                    ->label('Hari')
                    ->getStateUsing(function ($record) {
                        return Carbon::parse($record->tanggal_jadwal)->locale('id')->dayName;
                    })
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Minggu' => 'danger',
                        'Sabtu' => 'warning',
                        default => 'primary',
                    }),

                Tables\Columns\TextColumn::make('shift.nama_shift')
                    ->label('Shift')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('waktu_masuk')
                    ->label('Waktu Masuk')
                    ->time('H:i'),

                Tables\Columns\TextColumn::make('waktu_keluar')
                    ->label('Waktu Keluar')
                    ->time('H:i'),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state = null): string => match ($state) {
                        'Hadir' => 'success',
                        'Tidak Hadir' => 'danger',
                        'Terlambat' => 'warning',
                        'Izin' => 'info',
                        'Sakit' => 'gray',
                        'Cuti' => 'purple',
                        'Libur' => 'orange',
                        default => 'gray',
                    })
                    ->default('Belum Ditentukan'),

                Tables\Columns\IconColumn::make('is_approved')
                    ->label('Disetujui')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-clock')
                    ->trueColor('success')
                    ->falseColor('warning'),

                Tables\Columns\TextColumn::make('supervisor.name')
                    ->label('Supervisor')
                    ->default('Tidak ada')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('keterangan')
                    ->label('Keterangan')
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'Hadir' => 'Hadir',
                        'Tidak Hadir' => 'Tidak Hadir',
                        'Terlambat' => 'Terlambat',
                        'Izin' => 'Izin',
                        'Sakit' => 'Sakit',
                        'Cuti' => 'Cuti',
                        'Libur' => 'Libur',
                    ]),

                Tables\Filters\Filter::make('is_approved')
                    ->label('Sudah Disetujui')
                    ->query(fn(Builder $query): Builder => $query->where('is_approved', true)),

                Tables\Filters\Filter::make('tanggal_jadwal')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_jadwal', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_jadwal', '<=', $date),
                            );
                    }),

                Tables\Filters\Filter::make('minggu_ini')
                    ->label('Minggu Ini')
                    ->query(fn(Builder $query): Builder => $query->whereBetween('tanggal_jadwal', [
                        Carbon::now()->startOfWeek(),
                        Carbon::now()->endOfWeek()
                    ])),

                Tables\Filters\Filter::make('bulan_ini')
                    ->label('Bulan Ini')
                    ->query(fn(Builder $query): Builder => $query->whereBetween('tanggal_jadwal', [
                        Carbon::now()->startOfMonth(),
                        Carbon::now()->endOfMonth()
                    ])),
            ])
            ->headerActions([
                // No create action for employee view
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Jadwal')
                    ->modalContent(function ($record) {
                        return view('filament.karyawan.jadwal-detail', compact('record'));
                    }),
            ])
            ->bulkActions([
                // No bulk actions for employee view
            ])
            ->defaultSort('tanggal_jadwal', 'desc')
            ->emptyStateHeading('Belum Ada Jadwal Kerja')
            ->emptyStateDescription('Belum ada jadwal kerja yang ditetapkan.')
            ->emptyStateIcon('heroicon-o-calendar-days');
    }

    public function isReadOnly(): bool
    {
        return true; // Employee cannot create, edit, or delete
    }
}
