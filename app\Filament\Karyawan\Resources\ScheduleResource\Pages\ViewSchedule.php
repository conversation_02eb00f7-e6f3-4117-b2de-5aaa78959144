<?php

namespace App\Filament\Karyawan\Resources\ScheduleResource\Pages;

use App\Filament\Karyawan\Resources\ScheduleResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewSchedule extends ViewRecord
{
    protected static string $resource = ScheduleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No edit or delete actions for employee view
        ];
    }
}
