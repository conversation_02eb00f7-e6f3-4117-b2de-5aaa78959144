<?php

namespace App\Filament\Karyawan\Resources\SopResource\Pages;

use App\Filament\Karyawan\Resources\SopResource;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListSops extends ListRecords
{
    protected static string $resource = SopResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Tidak ada action untuk karyawan
        ];
    }

    public function getTabs(): array
    {
        return [
            'semua' => Tab::make('Semua SOP')
                ->icon('heroicon-o-document-text'),
                
            'berlaku' => Tab::make('SOP Berlaku')
                ->icon('heroicon-o-check-circle')
                ->modifyQueryUsing(fn (Builder $query) => $query->berlaku()),
                
            'departemen' => Tab::make('Departemen')
                ->icon('heroicon-o-building-office-2')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('scope_type', 'departemen')),
                
            'divisi' => Tab::make('Divisi')
                ->icon('heroicon-o-user-group')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('scope_type', 'divisi')),
        ];
    }
}
