<?php

namespace App\Filament\Karyawan\Widgets;

use App\Models\CutiIzin;
use App\Models\Karyawan;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class CutiIzinOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $user = Auth::user();
        $karyawan = Karyawan::where('id_user', $user->id)->first();

        if (!$karyawan) {
            return [];
        }

        // Get statistics for current year
        $currentYear = now()->year;

        $totalRequests = CutiIzin::where('karyawan_id', $karyawan->id)
            ->whereYear('created_at', $currentYear)
            ->count();

        $pendingRequests = CutiIzin::where('karyawan_id', $karyawan->id)
            ->where('status', 'pending')
            ->count();

        $approvedRequests = CutiIzin::where('karyawan_id', $karyawan->id)
            ->where('status', 'approved')
            ->whereYear('created_at', $currentYear)
            ->count();

        $totalDaysUsed = CutiIzin::where('karyawan_id', $karyawan->id)
            ->where('status', 'approved')
            ->whereYear('created_at', $currentYear)
            ->sum('jumlah_hari');

        $sickLeaveDays = CutiIzin::where('karyawan_id', $karyawan->id)
            ->where('status', 'approved')
            ->where('jenis_permohonan', 'sakit')
            ->whereYear('created_at', $currentYear)
            ->sum('jumlah_hari');

        return [
            Stat::make('Total Permohonan ' . $currentYear, $totalRequests)
                ->description('Semua permohonan cuti, izin, dan sakit tahun ini')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('primary'),

            Stat::make('Menunggu Persetujuan', $pendingRequests)
                ->description('Permohonan yang belum disetujui')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('Disetujui ' . $currentYear, $approvedRequests)
                ->description('Permohonan yang telah disetujui')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('Total Hari Digunakan', $totalDaysUsed)
                ->description('Hari kerja yang telah digunakan tahun ini')
                ->descriptionIcon('heroicon-m-calendar')
                ->color('info'),

            Stat::make('Hari Sakit ' . $currentYear, $sickLeaveDays)
                ->description('Hari cuti sakit yang telah digunakan')
                ->descriptionIcon('heroicon-m-heart')
                ->color('danger'),
        ];
    }

    public function getDisplayName(): string
    {
        return 'Ringkasan Cuti dan Izin';
    }
}
