<?php

namespace App\Filament\Karyawan\Widgets;

use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DetailPotonganChart extends ChartWidget
{
    protected static ?string $heading = 'Breakdown Potongan Bulan Ini';

    protected static ?string $description = 'Detail potongan yang akan mempengaruhi gaji Anda';

    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $user = Auth::user();
        $karyawan = $user->karyawan;

        if (!$karyawan) {
            return [
                'datasets' => [],
                'labels' => [],
            ];
        }

        // Hitung berbagai jenis potongan
        $potonganData = $this->hitungSemuaPotongan($karyawan);

        return [
            'datasets' => [
                [
                    'label' => 'Potongan (Rp)',
                    'data' => array_values($potonganData),
                    'backgroundColor' => [
                        '#ef4444', // Keterlambatan - Red
                        '#f97316', // Pelanggaran - Orange
                        '#3b82f6', // BPJS Kesehatan - Blue
                        '#10b981', // BPJS TK - Green
                        '#8b5cf6', // Lainnya - Purple
                    ],
                    'borderColor' => [
                        '#dc2626',
                        '#ea580c',
                        '#2563eb',
                        '#059669',
                        '#7c3aed',
                    ],
                    'borderWidth' => 2,
                ],
            ],
            'labels' => array_keys($potonganData),
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            return context.label + ": Rp " + context.parsed.toLocaleString("id-ID");
                        }'
                    ]
                ]
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }

    private function hitungSemuaPotongan($karyawan)
    {
        $potongan = [];

        // 1. Potongan Keterlambatan
        $potonganKeterlambatan = $this->hitungPotonganKeterlambatan($karyawan);
        if ($potonganKeterlambatan > 0) {
            $potongan['Keterlambatan'] = $potonganKeterlambatan;
        }

        // 2. Potongan Pelanggaran
        $potonganPelanggaran = $this->hitungPotonganPelanggaran($karyawan);
        if ($potonganPelanggaran > 0) {
            $potongan['Pelanggaran'] = $potonganPelanggaran;
        }

        // 3. BPJS dari basis gaji
        $basisGaji = $karyawan->penggajian()->where('periode_gaji', 'Basis Gaji')->first();
        if ($basisGaji) {
            if ($basisGaji->bpjs_kesehatan_dipotong > 0) {
                $potongan['BPJS Kesehatan'] = $basisGaji->bpjs_kesehatan_dipotong;
            }
            if ($basisGaji->bpjs_tk_dipotong > 0) {
                $potongan['BPJS Ketenagakerjaan'] = $basisGaji->bpjs_tk_dipotong;
            }
            if ($basisGaji->potongan_lainnya > 0) {
                $potongan['Potongan Lainnya'] = $basisGaji->potongan_lainnya;
            }
        }

        // Jika tidak ada potongan, tampilkan pesan
        if (empty($potongan)) {
            $potongan['Tidak Ada Potongan'] = 0;
        }

        return $potongan;
    }

    private function hitungPotonganKeterlambatan($karyawan)
    {
        $dateRange = $this->getDateRange();
        $absensiTerlambat = $karyawan->absensi()
            ->where('status', 'terlambat')
            ->whereBetween('tanggal_absensi', [$dateRange['start'], $dateRange['end']])
            ->get();

        $totalPotongan = 0;

        foreach ($absensiTerlambat as $absensi) {
            if ($absensi->waktu_masuk && $absensi->jadwal) {
                $shift = $absensi->jadwal->shift;
                if ($shift) {
                    $waktuMasukShift = Carbon::parse($absensi->tanggal_absensi->format('Y-m-d') . ' ' . $shift->waktu_masuk);
                    $waktuMasukAktual = Carbon::parse($absensi->waktu_masuk);
                    $menitTerlambat = $waktuMasukShift->diffInMinutes($waktuMasukAktual);

                    $aturanKeterlambatan = \App\Models\AturanKeterlambatan::where('is_active', true)
                        ->where('menit_dari', '<=', $menitTerlambat)
                        ->where('menit_sampai', '>=', $menitTerlambat)
                        ->first();

                    if ($aturanKeterlambatan) {
                        $totalPotongan += $aturanKeterlambatan->potongan;
                    }
                }
            }
        }

        return $totalPotongan;
    }

    private function hitungPotonganPelanggaran($karyawan)
    {
        $dateRange = $this->getDateRange();
        return $karyawan->pelanggaran()
            ->whereBetween('tanggal', [$dateRange['start'], $dateRange['end']])
            ->get()
            ->sum(function ($pelanggaran) {
                // Gunakan nominal_denda yang sudah dihitung saat pelanggaran dibuat
                return $pelanggaran->nominal_denda ?? 0;
            });
    }

    private function getDateRange(): array
    {
        // Ambil filter dari session atau gunakan default bulan ini
        $filters = session('dashboard_filters', ['date_range' => 'this_month']);

        if (isset($filters['date_range']) && $filters['date_range'] === 'monthly') {
            $month = $filters['month'] ?? now()->month;
            $year = $filters['year'] ?? now()->year;
            return [
                'start' => Carbon::create($year, $month, 1)->startOfMonth(),
                'end' => Carbon::create($year, $month, 1)->endOfMonth(),
            ];
        }

        // Default ke bulan ini
        return [
            'start' => now()->startOfMonth(),
            'end' => now()->endOfMonth(),
        ];
    }
}
