<?php

namespace App\Filament\Karyawan\Widgets;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DetailPotonganTable extends Widget
{
    protected static string $view = 'filament.karyawan.widgets.detail-potongan-table';

    protected static ?int $sort = 3;

    protected int | string | array $columnSpan = 'full';

    public function getViewData(): array
    {
        $user = Auth::user();
        $karyawan = $user->karyawan;

        if (!$karyawan) {
            return ['potonganData' => collect([])];
        }

        $potonganData = $this->getPotonganData($karyawan);

        return [
            'potonganData' => $potonganData,
            'totalPotongan' => $potonganData->sum('jumlah'),
        ];
    }

    private function getPotonganData($karyawan)
    {
        $potonganData = collect();

        // 1. Potongan Keterlambatan
        $dateRange = $this->getDateRange();
        $absensiTerlambat = $karyawan->absensi()
            ->where('status', 'terlambat')
            ->whereBetween('tanggal_absensi', [$dateRange['start'], $dateRange['end']])
            ->get();

        foreach ($absensiTerlambat as $absensi) {
            $potongan = $this->hitungPotonganKeterlambatan($absensi);
            if ($potongan > 0) {
                $menitTerlambat = $this->hitungMenitTerlambat($absensi);
                $potonganData->push((object) [
                    'tanggal' => $absensi->tanggal_absensi,
                    'jenis' => 'Keterlambatan',
                    'keterangan' => "Terlambat {$menitTerlambat} menit",
                    'jumlah' => $potongan,
                    'status' => 'Akan Dipotong',
                ]);
            }
        }

        // 2. Potongan Pelanggaran
        $pelanggaranBulanIni = $karyawan->pelanggaran()
            ->whereBetween('tanggal', [$dateRange['start'], $dateRange['end']])
            ->with('jenisPelanggaran')
            ->get();

        foreach ($pelanggaranBulanIni as $pelanggaran) {
            // Gunakan nominal_denda yang sudah dihitung saat pelanggaran dibuat
            $denda = $pelanggaran->nominal_denda ?? 0;
            if ($denda > 0) {
                $potonganData->push((object) [
                    'tanggal' => $pelanggaran->tanggal,
                    'jenis' => 'Pelanggaran',
                    'keterangan' => $pelanggaran->jenisPelanggaran->nama_pelanggaran ?? 'Pelanggaran',
                    'jumlah' => $denda,
                    'status' => 'Akan Dipotong',
                ]);
            }
        }

        // 3. Potongan BPJS (dari basis gaji)
        $basisGaji = $karyawan->penggajian()->where('periode_gaji', 'Basis Gaji')->first();
        if ($basisGaji) {
            if ($basisGaji->bpjs_kesehatan_dipotong > 0) {
                $potonganData->push((object) [
                    'tanggal' => now()->startOfMonth(),
                    'jenis' => 'BPJS Kesehatan',
                    'keterangan' => 'Potongan BPJS Kesehatan bulanan',
                    'jumlah' => $basisGaji->bpjs_kesehatan_dipotong,
                    'status' => 'Akan Dipotong',
                ]);
            }

            if ($basisGaji->bpjs_tk_dipotong > 0) {
                $potonganData->push((object) [
                    'tanggal' => now()->startOfMonth(),
                    'jenis' => 'BPJS Ketenagakerjaan',
                    'keterangan' => 'Potongan BPJS Ketenagakerjaan bulanan',
                    'jumlah' => $basisGaji->bpjs_tk_dipotong,
                    'status' => 'Akan Dipotong',
                ]);
            }

            if ($basisGaji->potongan_lainnya > 0) {
                $potonganData->push((object) [
                    'tanggal' => now()->startOfMonth(),
                    'jenis' => 'Lainnya',
                    'keterangan' => 'Potongan lainnya',
                    'jumlah' => $basisGaji->potongan_lainnya,
                    'status' => 'Akan Dipotong',
                ]);
            }
        }

        // 4. Potongan dari payroll yang sudah diproses dalam periode ini
        $payrollBulanIni = $karyawan->payrollTransactions()
            ->whereHas('payrollPeriod', function ($query) use ($dateRange) {
                $query->whereBetween('tanggal_mulai', [$dateRange['start'], $dateRange['end']]);
            })
            ->whereIn('status', ['approved', 'paid'])
            ->first();

        if ($payrollBulanIni) {
            // Update status menjadi "Sudah Dipotong" untuk potongan yang sudah diproses
            $potonganData = $potonganData->map(function ($item) {
                $item->status = 'Sudah Dipotong';
                return $item;
            });
        }

        return $potonganData;
    }

    private function hitungPotonganKeterlambatan($absensi)
    {
        if (!$absensi->waktu_masuk || !$absensi->jadwal) {
            return 0;
        }

        $shift = $absensi->jadwal->shift;
        if (!$shift) {
            return 0;
        }

        $menitTerlambat = $this->hitungMenitTerlambat($absensi);

        $aturanKeterlambatan = \App\Models\AturanKeterlambatan::where('is_active', true)
            ->where('menit_dari', '<=', $menitTerlambat)
            ->where('menit_sampai', '>=', $menitTerlambat)
            ->first();

        return $aturanKeterlambatan ? $aturanKeterlambatan->potongan : 0;
    }

    private function hitungMenitTerlambat($absensi)
    {
        if (!$absensi->waktu_masuk || !$absensi->jadwal) {
            return 0;
        }

        $shift = $absensi->jadwal->shift;
        if (!$shift) {
            return 0;
        }

        $waktuMasukShift = Carbon::parse($absensi->tanggal_absensi->format('Y-m-d') . ' ' . $shift->waktu_masuk);
        $waktuMasukAktual = Carbon::parse($absensi->waktu_masuk);

        return $waktuMasukShift->diffInMinutes($waktuMasukAktual);
    }

    private function getDateRange(): array
    {
        // Ambil filter dari session atau gunakan default bulan ini
        $filters = session('dashboard_filters', ['date_range' => 'this_month']);

        if (isset($filters['date_range']) && $filters['date_range'] === 'monthly') {
            $month = $filters['month'] ?? now()->month;
            $year = $filters['year'] ?? now()->year;
            return [
                'start' => Carbon::create($year, $month, 1)->startOfMonth(),
                'end' => Carbon::create($year, $month, 1)->endOfMonth(),
            ];
        }

        // Default ke bulan ini
        return [
            'start' => now()->startOfMonth(),
            'end' => now()->endOfMonth(),
        ];
    }
}
