<?php

namespace App\Filament\Karyawan\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class KaryawanOverviewWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $user = Auth::user();

        // Get karyawan with eager loading to avoid lazy loading
        $karyawan = \App\Models\Karyawan::with(['departemen', 'divisi'])
            ->where('id_user', $user->id)
            ->first();

        if (!$karyawan) {
            return [
                Stat::make('Data Karyawan', 'Tidak Ditemukan')
                    ->description('Silakan hubungi administrator')
                    ->descriptionIcon('heroicon-m-exclamation-triangle')
                    ->color('danger'),
            ];
        }

        // Get latest KPI
        $latestKpi = \App\Models\KpiPenilaian::where('karyawan_id', $karyawan->id)
            ->orderBy('periode', 'desc')
            ->first();

        // Get latest salary
        $latestGaji = \App\Models\PenggajianKaryawan::where('karyawan_id', $karyawan->id)
            ->orderBy('periode_gaji', 'desc')
            ->first();

        // Get attendance this month
        $totalHadir = \App\Models\Schedule::where('karyawan_id', $karyawan->id)
            ->whereDate('tanggal_jadwal', '>=', now()->startOfMonth())
            ->whereDate('tanggal_jadwal', '<=', now()->endOfMonth())
            ->where('status', 'Hadir')
            ->count();

        $totalJadwal = \App\Models\Schedule::where('karyawan_id', $karyawan->id)
            ->whereDate('tanggal_jadwal', '>=', now()->startOfMonth())
            ->whereDate('tanggal_jadwal', '<=', now()->endOfMonth())
            ->count();

        $persentaseKehadiran = $totalJadwal > 0 ? round(($totalHadir / $totalJadwal) * 100, 1) : 0;

        return [
            Stat::make('KPI Terakhir', $latestKpi ? number_format($latestKpi->realisasi_kpi, 1) . '%' : 'Belum ada')
                ->description($latestKpi ? 'Periode ' . $latestKpi->periode . ' (Nilai: ' . $latestKpi->nilai_akhir . ')' : 'Belum ada penilaian')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color($latestKpi ? $this->getKpiColor($latestKpi->realisasi_kpi) : 'gray'),

            Stat::make('Gaji Terakhir', $latestGaji ? 'Rp ' . number_format($latestGaji->take_home_pay, 0, ',', '.') : 'Belum ada')
                ->description($latestGaji ? 'Periode ' . $latestGaji->periode_gaji : 'Belum ada slip gaji')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success'),

            Stat::make('Kehadiran Bulan Ini', $persentaseKehadiran . '%')
                ->description($totalHadir . ' dari ' . $totalJadwal . ' hari kerja')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color($this->getKehadiranColor($persentaseKehadiran)),

            Stat::make('Profil Lengkap', 'Lihat Detail')
                ->description('Data personal & riwayat')
                ->descriptionIcon('heroicon-m-user')
                ->color('info')
                ->url(route('filament.karyawan.resources.karyawan-profiles.view', ['record' => $karyawan->id])),
        ];
    }

    private function getKpiColor($kpi): string
    {
        if ($kpi >= 90) return 'success';
        if ($kpi >= 80) return 'warning';
        if ($kpi >= 70) return 'danger';
        return 'gray';
    }

    private function getKehadiranColor($persentase): string
    {
        if ($persentase >= 95) return 'success';
        if ($persentase >= 85) return 'warning';
        return 'danger';
    }
}
