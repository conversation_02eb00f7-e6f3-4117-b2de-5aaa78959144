<?php

namespace App\Filament\Karyawan\Widgets;

use App\Models\Absensi;
use App\Models\Karyawan;
use App\Models\Schedule;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class KaryawanStats extends BaseWidget
{
    protected static ?string $pollingInterval = null;
    protected int | string | array $columnSpan = 'full';

    protected function getStats(): array
    {
        $user = Auth::user();
        $karyawan = Karyawan::select(['id', 'nama_lengkap', 'id_entitas', 'id_departemen', 'id_divisi'])
            ->with(['departemen', 'divisi'])
            ->where('id_user', $user->id)
            ->first();

        if (!$karyawan) {
            return [
                Stat::make('Status', 'Tidak Terhubung')
                    ->description('Akun tidak terhubung dengan data karyawan')
                    ->color('danger'),
            ];
        }

        $today = Carbon::today()->format('Y-m-d');
        $thisMonth = Carbon::today()->format('Y-m');

        // Get today's schedule
        $todaySchedule = Schedule::where('karyawan_id', $karyawan->id)
            ->where('tanggal_jadwal', $today)
            ->with('shift')
            ->first();

        // Get today's attendance (all periods)
        $todayAttendances = Absensi::where('karyawan_id', $karyawan->id)
            ->where('tanggal_absensi', $today)
            ->get();

        $todayAttendance = $todayAttendances->first(); // For backward compatibility

        // Count this month's attendance
        $monthlyAttendanceCount = Absensi::where('karyawan_id', $karyawan->id)
            ->whereRaw("DATE_FORMAT(tanggal_absensi, '%Y-%m') = ?", [$thisMonth])
            ->count();

        // Count this month's late attendance
        $monthlyLateCount = Absensi::where('karyawan_id', $karyawan->id)
            ->whereRaw("DATE_FORMAT(tanggal_absensi, '%Y-%m') = ?", [$thisMonth])
            ->where('status', 'terlambat')
            ->count();

        // Calculate late percentage
        $latePercentage = $monthlyAttendanceCount > 0
            ? round(($monthlyLateCount / $monthlyAttendanceCount) * 100, 2)
            : 0;

        // Determine today's attendance status
        $attendanceStatus = 'Belum Absen';
        $attendanceColor = 'danger';

        if ($todayAttendance) {
            if ($todayAttendance->waktu_masuk && $todayAttendance->waktu_keluar) {
                $attendanceStatus = 'Lengkap';
                $attendanceColor = 'success';
            } elseif ($todayAttendance->waktu_masuk) {
                $attendanceStatus = 'Masuk';
                $attendanceColor = 'warning';
            }
        }

        // Calculate work duration for today
        $todayWorkDuration = 0;
        if ($todayAttendance && $todayAttendance->durasi_kerja) {
            $todayWorkDuration = $todayAttendance->durasi_kerja;
        }

        // Calculate average work duration this month
        $monthlyWorkDurations = Absensi::where('karyawan_id', $karyawan->id)
            ->whereRaw("DATE_FORMAT(tanggal_absensi, '%Y-%m') = ?", [$thisMonth])
            ->whereNotNull('waktu_keluar')
            ->get()
            ->pluck('durasi_kerja')
            ->filter();

        $averageWorkDuration = $monthlyWorkDurations->count() > 0
            ? $monthlyWorkDurations->avg()
            : 0;

        // Get pending approvals count
        $pendingApprovals = Absensi::where('karyawan_id', $karyawan->id)
            ->whereNull('approved_at')
            ->count();

        // Determine next schedule info for split shift
        $scheduleInfo = $this->getScheduleInfo($todaySchedule, $todayAttendances);

        return [
            Stat::make('Jadwal Hari Ini', $scheduleInfo['title'])
                ->description($scheduleInfo['description'])
                ->descriptionIcon($scheduleInfo['icon'])
                ->color($scheduleInfo['color'])
                ->icon('heroicon-o-calendar-days'),

            Stat::make('Status Absensi', $attendanceStatus)
                ->description($todayAttendance
                    ? ($todayAttendance->waktu_masuk
                        ? '✅ Masuk: ' . Carbon::parse($todayAttendance->waktu_masuk)->format('H:i')
                        : 'Belum check-in') .
                    ($todayAttendance->waktu_keluar
                        ? ' | ✅ Keluar: ' . Carbon::parse($todayAttendance->waktu_keluar)->format('H:i')
                        : ($todayAttendance->waktu_masuk ? ' | ⏳ Belum check-out' : ''))
                    : 'Silakan lakukan absensi')
                ->descriptionIcon($attendanceStatus === 'Lengkap' ? 'heroicon-o-check-circle' : 'heroicon-o-clock')
                ->color($attendanceColor)
                ->icon('heroicon-o-finger-print'),

            Stat::make('Durasi Kerja Hari Ini', $todayWorkDuration > 0
                ? sprintf('%d jam %d menit', floor($todayWorkDuration / 60), $todayWorkDuration % 60)
                : 'Belum selesai')
                ->description($averageWorkDuration > 0
                    ? sprintf('Rata-rata bulan ini: %.1f jam', $averageWorkDuration / 60)
                    : 'Belum ada data rata-rata')
                ->descriptionIcon('heroicon-o-chart-bar')
                ->color($todayWorkDuration >= 480 ? 'success' : ($todayWorkDuration > 0 ? 'warning' : 'gray'))
                ->icon('heroicon-o-clock'),

            Stat::make('Keterlambatan', $latePercentage . '%')
                ->description($monthlyLateCount . ' dari ' . $monthlyAttendanceCount . ' hari kerja bulan ini')
                ->descriptionIcon($latePercentage > 10 ? 'heroicon-o-exclamation-triangle' : 'heroicon-o-check-circle')
                ->color($latePercentage > 10 ? 'danger' : ($latePercentage > 5 ? 'warning' : 'success'))
                ->icon('heroicon-o-clock'),

            Stat::make('Menunggu Persetujuan', $pendingApprovals)
                ->description($pendingApprovals > 0
                    ? 'Absensi menunggu persetujuan supervisor'
                    : 'Semua absensi sudah disetujui')
                ->descriptionIcon($pendingApprovals > 0 ? 'heroicon-o-clock' : 'heroicon-o-check-circle')
                ->color($pendingApprovals > 0 ? 'warning' : 'success')
                ->icon('heroicon-o-check-badge'),
        ];
    }

    /**
     * Get schedule information considering split shift periods
     */
    protected function getScheduleInfo($todaySchedule, $todayAttendances)
    {
        if (!$todaySchedule) {
            return [
                'title' => 'Libur',
                'description' => 'Tidak ada jadwal kerja hari ini',
                'icon' => 'heroicon-o-calendar',
                'color' => 'gray'
            ];
        }

        $shift = $todaySchedule->shift;

        if (!$shift->isSplitShift()) {
            // Regular shift
            return [
                'title' => $shift->nama_shift,
                'description' => '🕐 ' . Carbon::parse($shift->waktu_mulai)->format('H:i') . ' - ' . Carbon::parse($shift->waktu_selesai)->format('H:i'),
                'icon' => 'heroicon-o-clock',
                'color' => 'primary'
            ];
        }

        // Split shift logic
        $periode1 = $todayAttendances->where('periode', 1)->first();
        $periode2 = $todayAttendances->where('periode', 2)->first();

        // Check what's the next action needed
        if (!$periode1) {
            // Periode 1 belum absen
            return [
                'title' => $shift->nama_shift . ' (Periode 1)',
                'description' => '🕐 ' . Carbon::parse($shift->waktu_mulai)->format('H:i') . ' - ' . Carbon::parse($shift->waktu_selesai)->format('H:i') . ' | Belum absen periode 1',
                'icon' => 'heroicon-o-clock',
                'color' => 'warning'
            ];
        }

        if ($periode1 && !$periode1->waktu_keluar) {
            // Periode 1 sudah masuk, belum keluar
            return [
                'title' => $shift->nama_shift . ' (Periode 1)',
                'description' => '🕐 ' . Carbon::parse($shift->waktu_mulai)->format('H:i') . ' - ' . Carbon::parse($shift->waktu_selesai)->format('H:i') . ' | Belum absen keluar periode 1',
                'icon' => 'heroicon-o-clock',
                'color' => 'info'
            ];
        }

        if (!$periode2) {
            // Periode 1 selesai, periode 2 belum absen
            return [
                'title' => $shift->nama_shift . ' (Periode 2)',
                'description' => '🕐 ' . Carbon::parse($shift->waktu_mulai_periode2)->format('H:i') . ' - ' . Carbon::parse($shift->waktu_selesai_periode2)->format('H:i') . ' | Menunggu periode 2',
                'icon' => 'heroicon-o-clock',
                'color' => 'primary'
            ];
        }

        if ($periode2 && !$periode2->waktu_keluar) {
            // Periode 2 sudah masuk, belum keluar
            return [
                'title' => $shift->nama_shift . ' (Periode 2)',
                'description' => '🕐 ' . Carbon::parse($shift->waktu_mulai_periode2)->format('H:i') . ' - ' . Carbon::parse($shift->waktu_selesai_periode2)->format('H:i') . ' | Belum absen keluar periode 2',
                'icon' => 'heroicon-o-clock',
                'color' => 'info'
            ];
        }

        // Both periods completed
        return [
            'title' => $shift->nama_shift . ' (Selesai)',
            'description' => '✅ Split shift hari ini sudah lengkap',
            'icon' => 'heroicon-o-check-circle',
            'color' => 'success'
        ];
    }
}
