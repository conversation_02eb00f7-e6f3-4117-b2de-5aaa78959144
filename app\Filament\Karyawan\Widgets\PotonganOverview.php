<?php

namespace App\Filament\Karyawan\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use App\Traits\HasWidgetFilters;

class PotonganOverview extends BaseWidget
{
    use HasWidgetFilters;

    protected static ?string $pollingInterval = null;

    protected function getStats(): array
    {
        $user = Auth::user();
        $karyawan = $user->karyawan;

        if (!$karyawan) {
            return [];
        }

        // Hitung potongan keterlambatan bulan ini
        $potonganKeterlambatanBulanIni = $this->hitungPotonganKeterlambatanBulanIni($karyawan);

        // Hitung potongan pelanggaran bulan ini
        $potonganPelanggaranBulanIni = $this->hitungPotonganPelanggaranBulanIni($karyawan);

        // Hitung total potongan dari payroll terakhir
        $totalPotonganPayrollTerakhir = $this->hitungTotalPotonganPayrollTerakhir($karyawan);

        // Hitung estimasi potongan bulan ini
        $estimasiPotonganBulanIni = $potonganKeterlambatanBulanIni + $potonganPelanggaranBulanIni;

        return [
            Stat::make('Potongan Keterlambatan Bulan Ini', 'Rp ' . number_format($potonganKeterlambatanBulanIni, 0, ',', '.'))
                ->description($this->getKeteranganKeterlambatan($karyawan))
                ->descriptionIcon('heroicon-m-clock')
                ->color($potonganKeterlambatanBulanIni > 0 ? 'danger' : 'success')
                ->chart($this->getChartKeterlambatan($karyawan)),

            Stat::make('Potongan Pelanggaran Bulan Ini', 'Rp ' . number_format($potonganPelanggaranBulanIni, 0, ',', '.'))
                ->description($this->getKeteranganPelanggaran($karyawan))
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($potonganPelanggaranBulanIni > 0 ? 'danger' : 'success'),

            Stat::make('Total Potongan Payroll Terakhir', 'Rp ' . number_format($totalPotonganPayrollTerakhir, 0, ',', '.'))
                ->description($this->getKeteranganPayrollTerakhir($karyawan))
                ->descriptionIcon('heroicon-m-banknotes')
                ->color($totalPotonganPayrollTerakhir > 0 ? 'warning' : 'success'),

            Stat::make('Estimasi Potongan Bulan Ini', 'Rp ' . number_format($estimasiPotonganBulanIni, 0, ',', '.'))
                ->description('Berdasarkan keterlambatan & pelanggaran')
                ->descriptionIcon('heroicon-m-calculator')
                ->color($estimasiPotonganBulanIni > 0 ? 'danger' : 'success'),
        ];
    }

    private function hitungPotonganKeterlambatanBulanIni($karyawan)
    {
        // Ambil absensi terlambat berdasarkan filter dengan eager loading
        $dateRange = $this->getFilteredDateRange();
        $absensiTerlambat = $karyawan->absensi()
            ->with(['jadwal.shift'])
            ->where('status', 'terlambat')
            ->whereBetween('tanggal_absensi', [$dateRange['start'], $dateRange['end']])
            ->get();

        $totalPotongan = 0;

        foreach ($absensiTerlambat as $absensi) {
            if ($absensi->waktu_masuk && $absensi->jadwal) {
                $shift = $absensi->jadwal->shift;
                if ($shift) {
                    // Pastikan waktu_mulai hanya berformat H:i:s
                    $waktuMulaiShift = is_string($shift->waktu_mulai) ? $shift->waktu_mulai : $shift->waktu_mulai->format('H:i:s');
                    $waktuMasukShift = Carbon::parse($absensi->tanggal_absensi->format('Y-m-d') . ' ' . $waktuMulaiShift);
                    $waktuMasukAktual = Carbon::parse($absensi->waktu_masuk);
                    $menitTerlambat = $waktuMasukShift->diffInMinutes($waktuMasukAktual);

                    // Hitung potongan berdasarkan aturan keterlambatan
                    $potongan = $this->hitungPotonganBerdasarkanAturan($menitTerlambat);
                    $totalPotongan += $potongan;
                }
            }
        }

        return $totalPotongan;
    }

    private function hitungPotonganBerdasarkanAturan($menitTerlambat)
    {
        $aturanKeterlambatan = \App\Models\AturanKeterlambatan::where('is_active', true)
            ->where('menit_dari', '<=', $menitTerlambat)
            ->where('menit_sampai', '>=', $menitTerlambat)
            ->first();

        return $aturanKeterlambatan ? $aturanKeterlambatan->potongan : 0;
    }

    private function hitungPotonganPelanggaranBulanIni($karyawan)
    {
        $dateRange = $this->getFilteredDateRange();
        return $karyawan->pelanggaran()
            ->whereBetween('tanggal', [$dateRange['start'], $dateRange['end']])
            ->get()
            ->sum(function ($pelanggaran) {
                // Gunakan nominal_denda yang sudah dihitung saat pelanggaran dibuat
                return $pelanggaran->nominal_denda ?? 0;
            });
    }

    private function hitungTotalPotonganPayrollTerakhir($karyawan)
    {
        $payrollTerakhir = $karyawan->payrollTransactions()
            ->whereIn('status', ['approved', 'paid'])
            ->latest()
            ->first();

        if (!$payrollTerakhir) {
            return 0;
        }

        return $payrollTerakhir->potongan_bpjs_kesehatan +
            $payrollTerakhir->potongan_bpjs_tk +
            $payrollTerakhir->potongan_keterlambatan +
            $payrollTerakhir->potongan_pelanggaran +
            $payrollTerakhir->potongan_lainnya;
    }

    private function getKeteranganKeterlambatan($karyawan)
    {
        $dateRange = $this->getFilteredDateRange();
        $jumlahTerlambat = $karyawan->absensi()
            ->with(['jadwal.shift'])
            ->where('status', 'terlambat')
            ->whereBetween('tanggal_absensi', [$dateRange['start'], $dateRange['end']])
            ->count();

        if ($jumlahTerlambat == 0) {
            return 'Tidak ada keterlambatan periode ini';
        }

        return $jumlahTerlambat . ' hari terlambat periode ini';
    }

    private function getKeteranganPelanggaran($karyawan)
    {
        $dateRange = $this->getFilteredDateRange();
        $jumlahPelanggaran = $karyawan->pelanggaran()
            ->with(['jenisPelanggaran'])
            ->whereBetween('tanggal', [$dateRange['start'], $dateRange['end']])
            ->count();

        if ($jumlahPelanggaran == 0) {
            return 'Tidak ada pelanggaran periode ini';
        }

        return $jumlahPelanggaran . ' pelanggaran periode ini';
    }

    private function getKeteranganPayrollTerakhir($karyawan)
    {
        $payrollTerakhir = $karyawan->payrollTransactions()
            ->whereIn('status', ['approved', 'paid'])
            ->latest()
            ->first();

        if (!$payrollTerakhir) {
            return 'Belum ada payroll';
        }

        return 'Payroll ' . $payrollTerakhir->payrollPeriod->nama_periode;
    }

    private function getChartKeterlambatan($karyawan)
    {
        // Chart untuk 7 hari terakhir
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $tanggal = now()->subDays($i);
            $absensi = $karyawan->absensi()
                ->with(['jadwal.shift'])
                ->whereDate('tanggal_absensi', $tanggal)
                ->first();

            if ($absensi && $absensi->status === 'terlambat') {
                $data[] = 1;
            } else {
                $data[] = 0;
            }
        }

        return $data;
    }
}
