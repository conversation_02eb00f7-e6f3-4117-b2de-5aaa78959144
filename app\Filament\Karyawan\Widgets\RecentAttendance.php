<?php

namespace App\Filament\Karyawan\Widgets;

use App\Models\Absensi;
use App\Models\Karyawan;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class RecentAttendance extends BaseWidget
{
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->heading('Riwayat Absensi Terakhir')
            ->query(function (): Builder {
                $user = Auth::user();
                $karyawan = Karyawan::select(['id', 'nama_lengkap', 'id_entitas', 'id_departemen', 'id_divisi'])
                    ->with(['departemen', 'divisi'])
                    ->where('id_user', $user->id)
                    ->first();

                if (!$karyawan) {
                    return Absensi::query()->where('id', 0); // Empty query
                }

                return Absensi::query()
                    ->with([
                        'jadwal.shift:id,nama_shift,waktu_mulai,waktu_selesai',
                        'jadwal.entitas:id,nama',
                        'karyawan:id,nama_lengkap'
                    ])
                    ->where('karyawan_id', $karyawan->id)
                    ->orderBy('tanggal_absensi', 'desc')
                    ->limit(10);
            })
            ->columns([
                Tables\Columns\TextColumn::make('tanggal_absensi')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('waktu_masuk')
                    ->label('Masuk')
                    ->time('H:i')
                    ->sortable()
                    ->icon('heroicon-o-arrow-right-circle')
                    ->iconColor('success'),

                Tables\Columns\TextColumn::make('waktu_keluar')
                    ->label('Keluar')
                    ->time('H:i')
                    ->placeholder('-')
                    ->sortable()
                    ->icon('heroicon-o-arrow-left-circle')
                    ->iconColor('warning'),

                Tables\Columns\TextColumn::make('durasi_kerja')
                    ->label('Durasi')
                    ->formatStateUsing(function ($state) {
                        if (!$state) return '-';
                        $hours = floor($state / 60);
                        $minutes = $state % 60;
                        return sprintf('%d jam %d menit', $hours, $minutes);
                    })
                    ->icon('heroicon-o-clock')
                    ->iconColor('info'),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'hadir' => 'success',
                        'terlambat' => 'warning',
                        'izin' => 'info',
                        'sakit' => 'info',
                        'cuti' => 'primary',
                        'alpha' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => ucfirst($state))
                    ->sortable(),

                Tables\Columns\IconColumn::make('approved_at')
                    ->label('Disetujui')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-clock')
                    ->trueColor('success')
                    ->falseColor('warning')
                    ->getStateUsing(fn($record): bool => $record->approved_at !== null)
                    ->tooltip(fn($record): string => $record->approved_at
                        ? 'Disetujui pada ' . $record->approved_at->format('d M Y H:i')
                        : 'Menunggu persetujuan'),

                Tables\Columns\TextColumn::make('lokasi_masuk')
                    ->label('Lokasi')
                    ->formatStateUsing(function ($state) {
                        if (!$state) return '-';
                        $coords = explode(',', $state);
                        if (count($coords) !== 2) return 'Invalid';
                        return sprintf('%.4f, %.4f', (float)$coords[0], (float)$coords[1]);
                    })
                    ->icon('heroicon-o-map-pin')
                    ->iconColor('primary')
                    ->tooltip('Koordinat lokasi check-in')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\ImageColumn::make('foto_masuk')
                    ->label('Foto')
                    ->circular()
                    ->size(40)
                    ->tooltip('Foto check-in'),
            ])
            ->emptyStateHeading('Belum ada riwayat absensi')
            ->emptyStateDescription('Anda belum memiliki riwayat absensi.')
            ->emptyStateIcon('heroicon-o-finger-print')
            ->paginated(false);
    }
}
