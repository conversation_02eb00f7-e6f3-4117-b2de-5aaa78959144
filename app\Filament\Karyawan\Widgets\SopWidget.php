<?php

namespace App\Filament\Karyawan\Widgets;

use App\Models\SopDokumen;
use App\Models\Karyawan;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class SopWidget extends Widget
{
    protected static string $view = 'filament.karyawan.widgets.sop-widget';

    protected int | string | array $columnSpan = 'full';

    public function getSopData()
    {
        $user = Auth::user();
        $karyawan = Karyawan::with(['departemen', 'divisi'])->where('id_user', $user->id)->first();

        return SopDokumen::with(['departemen', 'divisi'])
            ->where('status', 'aktif')
            ->berlaku()
            ->forKaryawan($karyawan)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
    }

    public function getKaryawanData()
    {
        $user = Auth::user();
        return Karyawan::with(['departemen', 'divisi'])->where('id_user', $user->id)->first();
    }
}
