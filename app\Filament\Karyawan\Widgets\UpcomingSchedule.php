<?php

namespace App\Filament\Karyawan\Widgets;

use App\Models\Karyawan;
use App\Models\Schedule;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class UpcomingSchedule extends BaseWidget
{
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->heading('Jadwal Mendatang')
            ->query(function (): Builder {
                $user = Auth::user();
                $karyawan = Karyawan::select(['id', 'nama_lengkap', 'id_entitas', 'id_departemen', 'id_divisi'])
                    ->with(['departemen', 'divisi'])
                    ->where('id_user', $user->id)
                    ->first();

                if (!$karyawan) {
                    return Schedule::query()->where('id', 0); // Empty query
                }

                $today = Carbon::today();
                $nextWeek = Carbon::today()->addDays(7);

                return Schedule::query()
                    ->where('karyawan_id', $karyawan->id)
                    ->where('tanggal_jadwal', '>=', $today)
                    ->where('tanggal_jadwal', '<=', $nextWeek)
                    ->with('shift')
                    ->orderBy('tanggal_jadwal');
            })
            ->columns([
                Tables\Columns\TextColumn::make('tanggal_jadwal')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('shift_info')
                    ->label('Shift & Periode')
                    ->getStateUsing(function ($record) {
                        if (!$record->shift) {
                            return 'Tidak ada shift';
                        }

                        $shift = $record->shift;
                        $shiftName = $shift->nama_shift;

                        if ($shift->isSplitShift()) {
                            // Check attendance status to determine next period
                            $user = \Illuminate\Support\Facades\Auth::user();
                            $karyawan = \App\Models\Karyawan::select(['id', 'nama_lengkap', 'id_entitas'])
                                ->where('id_user', $user->id)
                                ->first();

                            if ($karyawan) {
                                $attendances = \App\Models\Absensi::where('karyawan_id', $karyawan->id)
                                    ->where('tanggal_absensi', $record->tanggal_jadwal)
                                    ->get();

                                $periode1 = $attendances->where('periode', 1)->first();
                                $periode2 = $attendances->where('periode', 2)->first();

                                // Determine which period to show
                                if (!$periode1) {
                                    return "{$shiftName} (P1)";
                                } elseif ($periode1 && !$periode1->waktu_keluar) {
                                    return "{$shiftName} (P1 - Keluar)";
                                } elseif (!$periode2) {
                                    return "{$shiftName} (P2)";
                                } elseif ($periode2 && !$periode2->waktu_keluar) {
                                    return "{$shiftName} (P2 - Keluar)";
                                } else {
                                    return "{$shiftName} (Selesai)";
                                }
                            }

                            return "{$shiftName} (Split)";
                        }

                        return $shiftName;
                    })
                    ->badge()
                    ->color(function ($record) {
                        if (!$record->shift) {
                            return 'gray';
                        }

                        if ($record->shift->isSplitShift()) {
                            return 'warning';
                        }

                        return 'primary';
                    }),

                Tables\Columns\TextColumn::make('waktu_info')
                    ->label('Waktu')
                    ->getStateUsing(function ($record) {
                        if (!$record->shift) {
                            return '-';
                        }

                        $shift = $record->shift;

                        if ($shift->isSplitShift()) {
                            // Check which period to show
                            $user = \Illuminate\Support\Facades\Auth::user();
                            $karyawan = \App\Models\Karyawan::where('id_user', $user->id)->first();

                            if ($karyawan) {
                                $attendances = \App\Models\Absensi::where('karyawan_id', $karyawan->id)
                                    ->where('tanggal_absensi', $record->tanggal_jadwal)
                                    ->get();

                                $periode1 = $attendances->where('periode', 1)->first();
                                $periode2 = $attendances->where('periode', 2)->first();

                                // Show time for next required period
                                if (!$periode1 || ($periode1 && !$periode1->waktu_keluar)) {
                                    return \Carbon\Carbon::parse($shift->waktu_mulai)->format('H:i') . ' - ' . \Carbon\Carbon::parse($shift->waktu_selesai)->format('H:i');
                                } elseif (!$periode2 || ($periode2 && !$periode2->waktu_keluar)) {
                                    return \Carbon\Carbon::parse($shift->waktu_mulai_periode2)->format('H:i') . ' - ' . \Carbon\Carbon::parse($shift->waktu_selesai_periode2)->format('H:i');
                                } else {
                                    return 'Selesai';
                                }
                            }
                        }

                        return \Carbon\Carbon::parse($shift->waktu_mulai)->format('H:i') . ' - ' . \Carbon\Carbon::parse($shift->waktu_selesai)->format('H:i');
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Hadir' => 'success',
                        'Izin' => 'info',
                        'Sakit' => 'info',
                        'Cuti' => 'primary',
                        'Alpha' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),
            ])
            ->emptyStateHeading('Tidak ada jadwal mendatang')
            ->emptyStateDescription('Anda tidak memiliki jadwal untuk 7 hari ke depan.')
            ->emptyStateIcon('heroicon-o-calendar')
            ->paginated(false);
    }
}
