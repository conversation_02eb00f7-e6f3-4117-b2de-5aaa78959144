<?php

namespace App\Filament\Marketing\Exports;

use App\Models\Customer;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class CustomerExporter extends Exporter
{
    protected static ?string $model = Customer::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('nama')
                ->label('Nama Lengkap'),
                
            ExportColumn::make('email')
                ->label('Email'),
                
            ExportColumn::make('telepon')
                ->label('Nomor Telepon'),
                
            ExportColumn::make('alamat')
                ->label('Alamat'),
                
            ExportColumn::make('tanggal_lahir')
                ->label('Tanggal Lahir'),
                
            ExportColumn::make('jenis_kelamin')
                ->label('Jenis Kelamin')
                ->formatStateUsing(fn (?string $state): string => match ($state) {
                    'L' => 'Laki-laki',
                    'P' => 'Perempuan',
                    default => 'Tidak Diketahui',
                }),
                
            ExportColumn::make('loyalty_points')
                ->label('Poin Loyalitas'),
                
            ExportColumn::make('segment')
                ->label('Segmen Pelanggan')
                ->formatStateUsing(fn (?string $state): string => match ($state) {
                    'top_spenders' => 'Top Spenders',
                    'frequent_buyers' => 'Frequent Buyers',
                    'lapsed_customers' => 'Lapsed Customers',
                    'product_specific_buyers' => 'Product Specific Buyers',
                    'regular_customers' => 'Regular Customers',
                    'new_customer' => 'New Customer',
                    default => $state ?? 'Tidak Ada Segmen',
                }),
                
            ExportColumn::make('membership_tier')
                ->label('Tier Membership')
                ->state(function (Customer $record): string {
                    return $record->membership_tier;
                }),
                
            ExportColumn::make('total_spending')
                ->label('Total Pembelian')
                ->state(function (Customer $record): string {
                    return 'Rp ' . number_format($record->total_spending, 0, ',', '.');
                }),
                
            ExportColumn::make('total_transactions')
                ->label('Total Transaksi')
                ->state(function (Customer $record): int {
                    return $record->total_transactions;
                }),
                
            ExportColumn::make('last_transaction_date')
                ->label('Transaksi Terakhir')
                ->state(function (Customer $record): string {
                    return $record->last_transaction_date 
                        ? $record->last_transaction_date->format('d/m/Y')
                        : 'Belum Ada Transaksi';
                }),
                
            ExportColumn::make('created_at')
                ->label('Tanggal Daftar')
                ->formatStateUsing(fn ($state): string => $state->format('d/m/Y H:i')),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Export data pelanggan telah selesai. ' . number_format($export->successful_rows) . ' ' . str('baris')->plural($export->successful_rows) . ' berhasil diekspor.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('baris')->plural($failedRowsCount) . ' gagal diekspor.';
        }

        return $body;
    }
}
