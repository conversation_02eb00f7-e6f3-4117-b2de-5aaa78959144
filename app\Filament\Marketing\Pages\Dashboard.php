<?php

namespace App\Filament\Marketing\Pages;

use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static string $view = 'filament.marketing.pages.dashboard';

    public function getColumns(): int | string | array
    {
        return 2;
    }

    public function getWidgets(): array
    {
        return [
            \App\Filament\Marketing\Widgets\TotalCustomersWidget::class,
            \App\Filament\Marketing\Widgets\CustomerSegmentChart::class,
            \App\Filament\Marketing\Widgets\TopProductsWidget::class,
        ];
    }
}
