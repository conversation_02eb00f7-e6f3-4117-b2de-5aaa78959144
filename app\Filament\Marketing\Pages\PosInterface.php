<?php

namespace App\Filament\Marketing\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Actions\Action;
use App\Models\PosTransaction;
use App\Models\PosTransactionItem;
use App\Models\Customer;
use App\Models\Product;
use App\Filament\Marketing\Resources\PosTransactionResource;
use Illuminate\Support\Facades\DB;

class PosInterface extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-calculator';
    
    protected static ?string $navigationGroup = 'Sales';
    
    protected static ?string $navigationLabel = 'POS Kasir';
    
    protected static string $view = 'filament.marketing.pages.pos-interface';
    
    protected static ?int $navigationSort = 1;

    public $selectedProducts = [];
    public $customer_id = null;
    public $payment_method = 'cash';
    public $discount_amount = 0;
    public $tax_amount = 0;
    public $amount_paid = 0;
    public $table_number = null;
    public $loyalty_points_used = 0;
    public $searchProduct = '';

    protected $listeners = ['productScanned' => 'handleScannedProduct'];

    public function mount()
    {
        $this->selectedProducts = [];
    }

    public function addProduct($productId, $quantity = 1)
    {
        $product = Product::find($productId);
        if (!$product) {
            $this->addError('product', 'Produk tidak ditemukan');
            return;
        }

        // Check if product is active
        if (!$product->is_active) {
            $this->addError('product', 'Produk tidak aktif');
            return;
        }

        // Check stock availability
        $currentQuantity = isset($this->selectedProducts[$productId]) ? $this->selectedProducts[$productId]['quantity'] : 0;
        $totalQuantity = $currentQuantity + $quantity;

        if ($product->stock_quantity !== null && $totalQuantity > $product->stock_quantity) {
            $this->addError('product', 'Stok tidak mencukupi. Stok tersedia: ' . $product->stock_quantity);
            return;
        }

        $key = $productId;
        if (isset($this->selectedProducts[$key])) {
            $this->selectedProducts[$key]['quantity'] += $quantity;
            $this->selectedProducts[$key]['total'] = $this->selectedProducts[$key]['price'] * $this->selectedProducts[$key]['quantity'];
        } else {
            $this->selectedProducts[$key] = [
                'product_id' => $product->id,
                'name' => $product->name,
                'price' => $product->price,
                'quantity' => $quantity,
                'total' => $product->price * $quantity,
                'stock_available' => $product->stock_quantity,
            ];
        }

        $this->updateTotals();
    }

    public function removeProduct($productId)
    {
        unset($this->selectedProducts[$productId]);
        $this->updateTotals();
    }

    public function updateQuantity($productId, $quantity)
    {
        if ($quantity <= 0) {
            $this->removeProduct($productId);
            return;
        }

        if (isset($this->selectedProducts[$productId])) {
            // Check stock availability
            $stockAvailable = $this->selectedProducts[$productId]['stock_available'] ?? null;
            if ($stockAvailable !== null && $quantity > $stockAvailable) {
                $this->addError('quantity', 'Stok tidak mencukupi. Stok tersedia: ' . $stockAvailable);
                return;
            }

            $this->selectedProducts[$productId]['quantity'] = $quantity;
            $this->selectedProducts[$productId]['total'] =
                $this->selectedProducts[$productId]['price'] * $quantity;
        }

        $this->updateTotals();
    }

    private function updateTotals()
    {
        // Calculate totals automatically
        $subtotal = collect($this->selectedProducts)->sum('total');
        $this->tax_amount = $subtotal * 0.11; // 11% tax
    }

    public function getTotalAmount()
    {
        $subtotal = collect($this->selectedProducts)->sum('total');
        return $subtotal - $this->discount_amount + $this->tax_amount;
    }

    public function getChangeAmount()
    {
        return max(0, $this->amount_paid - $this->getTotalAmount());
    }

    public function processTransaction()
    {
        if (empty($this->selectedProducts)) {
            $this->addError('products', 'Pilih minimal satu produk');
            return;
        }

        if ($this->amount_paid < $this->getTotalAmount()) {
            $this->addError('amount_paid', 'Jumlah bayar kurang dari total');
            return;
        }

        DB::transaction(function () {
            // Create POS Transaction
            $transaction = PosTransaction::create([
                'transaction_number' => 'POS-' . date('Ymd') . '-' . str_pad((PosTransaction::whereDate('created_at', today())->count() + 1), 3, '0', STR_PAD_LEFT),
                'customer_id' => $this->customer_id,
                'user_id' => auth()->id(),
                'transaction_date' => now(),
                'total_amount' => collect($this->selectedProducts)->sum('total'),
                'discount_amount' => $this->discount_amount,
                'tax_amount' => $this->tax_amount,
                'net_amount' => $this->getTotalAmount(),
                'payment_method' => $this->payment_method,
                'amount_paid' => $this->amount_paid,
                'change_given' => $this->getChangeAmount(),
                'loyalty_points_used' => $this->loyalty_points_used,
                'loyalty_points_earned' => $this->calculateLoyaltyPoints(),
                'table_number' => $this->table_number,
                'is_offline_transaction' => false,
            ]);

            // Create transaction items
            foreach ($this->selectedProducts as $item) {
                PosTransactionItem::create([
                    'pos_transaction_id' => $transaction->id,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['price'],
                    'total_price' => $item['total'],
                ]);
            }

            // Reset form
            $this->reset(['selectedProducts', 'customer_id', 'discount_amount', 'amount_paid', 'table_number', 'loyalty_points_used']);

            // Show success message and redirect to transaction view
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => 'Transaksi berhasil diproses! Nomor: ' . $transaction->transaction_number
            ]);

            $this->redirect(PosTransactionResource::getUrl('view', ['record' => $transaction]));
        });
    }

    private function calculateLoyaltyPoints(): int
    {
        // 1 point per 1000 rupiah - bisa diubah sesuai kebutuhan
        return (int) floor($this->getTotalAmount() / 1000);
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('clear')
                ->label('Clear All')
                ->color('danger')
                ->action(fn () => $this->reset(['selectedProducts', 'customer_id', 'discount_amount', 'amount_paid', 'table_number', 'loyalty_points_used'])),
        ];
    }

    public function getProducts()
    {
        return Product::when($this->searchProduct, function ($query) {
                return $query->where('name', 'like', '%' . $this->searchProduct . '%')
                           ->orWhere('sku', 'like', '%' . $this->searchProduct . '%')
                           ->orWhere('barcode', 'like', '%' . $this->searchProduct . '%');
            })
            ->active() // Use scope instead of where clause
            ->orderBy('name')
            ->limit(20)
            ->get();
    }

    public function getCustomers()
    {
        return Customer::activeStatus()
            ->orderBy('nama')
            ->limit(50)
            ->get();
    }

    public function handleScannedProduct($barcode)
    {
        $product = Product::where('barcode', $barcode)
                         ->orWhere('sku', $barcode)
                         ->active()
                         ->first();
        if ($product) {
            $this->addProduct($product->id);
        } else {
            $this->addError('barcode', 'Produk dengan barcode/SKU ' . $barcode . ' tidak ditemukan');
        }
    }

    public function updatedCustomerId()
    {
        // Reset loyalty points when customer changes
        $this->loyalty_points_used = 0;
    }

    public function updatedDiscountAmount()
    {
        $this->updateTotals();
    }

    public function getSelectedCustomer()
    {
        return $this->customer_id ? Customer::find($this->customer_id) : null;
    }
}
