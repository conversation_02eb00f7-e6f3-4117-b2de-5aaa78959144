<?php

namespace App\Filament\Marketing\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use App\Models\PosTransaction;
use App\Models\Product;
use App\Models\Category;
use Carbon\Carbon;

class SalesReportPage extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?string $navigationLabel = 'Laporan Penjualan';

    protected static ?string $title = 'Laporan Penjualan POS';

    protected static string $view = 'filament.marketing.pages.sales-report-page';

    public ?array $data = [];

    public $startDate;
    public $endDate;
    public $datePreset;
    public $selectedProducts = [];
    public $reportData = [];
    public $availableProducts = [];

    public function mount(): void
    {
        $this->startDate = null;
        $this->endDate = null;
        $this->datePreset = 'all';
        $this->selectedProducts = [];

        // Load available products
        $this->loadAvailableProducts();

        $this->generateReport();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Filter Laporan POS')
                    ->schema([
                        Forms\Components\Select::make('datePreset')
                            ->label('Pilihan Periode')
                            ->options([
                                'all' => 'Seluruh Transaksi',
                                'today' => 'Hari Ini',
                                'yesterday' => 'Kemarin',
                                'last_7_days' => '7 Hari Terakhir',
                                'this_week' => 'Minggu Ini',
                                'last_week' => 'Minggu Lalu',
                                'last_30_days' => '30 Hari Terakhir',
                                'this_month' => 'Bulan Ini',
                                'last_month' => 'Bulan Lalu',
                                'custom' => 'Custom Range',
                            ])
                            ->default('today')
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                $this->datePreset = $state;
                                $this->applyDatePreset();
                            })
                            ->native(false),

                        Forms\Components\DateTimePicker::make('startDate')
                            ->label('Tanggal & Jam Mulai')
                            ->required()
                            ->live(onBlur: true)
                            ->afterStateUpdated(function ($state) {
                                $this->startDate = $state;
                                $this->datePreset = 'custom';
                                $this->generateReport();
                            })
                            ->maxDate(now())
                            ->visible(fn () => $this->datePreset === 'custom'),

                        Forms\Components\DateTimePicker::make('endDate')
                            ->label('Tanggal & Jam Akhir')
                            ->required()
                            ->live(onBlur: true)
                            ->afterStateUpdated(function ($state) {
                                $this->endDate = $state;
                                $this->datePreset = 'custom';
                                $this->generateReport();
                            })
                            ->maxDate(now())
                            ->minDate(fn () => $this->startDate)
                            ->visible(fn () => $this->datePreset === 'custom'),
                    ])
                    ->columns(3),
            ]);
    }

    public function applyDatePreset()
    {
        switch ($this->datePreset) {
            case 'all':
                $this->startDate = null;
                $this->endDate = null;
                break;
            case 'today':
                $this->startDate = now()->startOfDay()->format('Y-m-d H:i:s');
                $this->endDate = now()->endOfDay()->format('Y-m-d H:i:s');
                break;
            case 'yesterday':
                $this->startDate = now()->subDay()->startOfDay()->format('Y-m-d H:i:s');
                $this->endDate = now()->subDay()->endOfDay()->format('Y-m-d H:i:s');
                break;
            case 'last_7_days':
                $this->startDate = now()->subDays(6)->startOfDay()->format('Y-m-d H:i:s');
                $this->endDate = now()->endOfDay()->format('Y-m-d H:i:s');
                break;
            case 'this_week':
                $this->startDate = now()->startOfWeek()->format('Y-m-d H:i:s');
                $this->endDate = now()->endOfWeek()->format('Y-m-d H:i:s');
                break;
            case 'last_week':
                $this->startDate = now()->subWeek()->startOfWeek()->format('Y-m-d H:i:s');
                $this->endDate = now()->subWeek()->endOfWeek()->format('Y-m-d H:i:s');
                break;
            case 'last_30_days':
                $this->startDate = now()->subDays(29)->startOfDay()->format('Y-m-d H:i:s');
                $this->endDate = now()->endOfDay()->format('Y-m-d H:i:s');
                break;
            case 'this_month':
                $this->startDate = now()->startOfMonth()->format('Y-m-d H:i:s');
                $this->endDate = now()->endOfMonth()->format('Y-m-d H:i:s');
                break;
            case 'last_month':
                $this->startDate = now()->subMonth()->startOfMonth()->format('Y-m-d H:i:s');
                $this->endDate = now()->subMonth()->endOfMonth()->format('Y-m-d H:i:s');
                break;
        }

        $this->generateReport();

        // Dispatch event to refresh charts
        $this->dispatch('charts-updated');
    }

    public function loadAvailableProducts()
    {
        $this->availableProducts = Product::with('category')
            ->where('is_active', true)
            ->orderBy('name')
            ->get()
            ->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'category' => $product->category->name ?? 'Uncategorized',
                    'category_id' => $product->category_id,
                ];
            })
            ->toArray();
    }

    public function generateReport()
    {
        $query = PosTransaction::with(['posTransactionItems.product.category', 'customer', 'user']);

        // Apply date filter only if dates are set (not for "all" preset)
        if ($this->startDate && $this->endDate) {
            $query->whereBetween('transaction_date', [$this->startDate, $this->endDate]);
        }

        $transactions = $query->get();

        // Summary data
        $this->reportData = [
            'totalTransactions' => $transactions->count(),
            'totalRevenue' => $transactions->sum('total_amount'),
            'averageOrderValue' => $transactions->count() > 0 ? $transactions->sum('total_amount') / $transactions->count() : 0,
            'totalDiscount' => $transactions->sum('discount_amount'),
            'totalNetAmount' => $transactions->sum('net_amount'),
            'topProducts' => $this->getTopProducts($transactions),
            'categorySales' => $this->getCategorySales($transactions),
            'hourlyAnalysis' => $this->getHourlyAnalysis($transactions),
            'hourlyProductAnalysis' => $this->getHourlyProductAnalysis($transactions),
            'topCustomers' => $this->getTopCustomers($transactions),
            'transactions' => $transactions,
        ];
    }

    private function getTopProducts($transactions)
    {
        $products = [];

        foreach ($transactions as $transaction) {
            foreach ($transaction->posTransactionItems as $item) {
                $productId = $item->product_id;
                if (!isset($products[$productId])) {
                    $products[$productId] = [
                        'name' => $item->product->name,
                        'category' => $item->product->category->name ?? 'N/A',
                        'quantity' => 0,
                        'revenue' => 0,
                    ];
                }
                $products[$productId]['quantity'] += $item->quantity;
                $products[$productId]['revenue'] += $item->total_price;
            }
        }

        return collect($products)
            ->sortByDesc('quantity')
            ->take(10)
            ->values()
            ->toArray();
    }

    private function getDailySales($transactions)
    {
        return $transactions
            ->groupBy(function ($transaction) {
                return Carbon::parse($transaction->transaction_date)->format('Y-m-d');
            })
            ->map(function ($dayTransactions) {
                return [
                    'date' => $dayTransactions->first()->transaction_date->format('Y-m-d'),
                    'count' => $dayTransactions->count(),
                    'revenue' => $dayTransactions->sum('total_amount'),
                ];
            })
            ->sortBy('date')
            ->values()
            ->toArray();
    }

    private function getCategorySales($transactions)
    {
        $categories = [];

        foreach ($transactions as $transaction) {
            foreach ($transaction->posTransactionItems as $item) {
                $categoryName = $item->product->category->name ?? 'Tidak Ada Kategori';
                if (!isset($categories[$categoryName])) {
                    $categories[$categoryName] = [
                        'name' => $categoryName,
                        'quantity' => 0,
                        'revenue' => 0,
                    ];
                }
                $categories[$categoryName]['quantity'] += $item->quantity;
                $categories[$categoryName]['revenue'] += $item->total_price;
            }
        }

        return collect($categories)
            ->sortByDesc('revenue')
            ->values()
            ->toArray();
    }

    private function getPaymentMethodBreakdown($transactions)
    {
        $paymentMethods = [];

        foreach ($transactions as $transaction) {
            $method = $transaction->payment_method;
            if (!isset($paymentMethods[$method])) {
                $paymentMethods[$method] = [
                    'method' => $method,
                    'label' => $this->getPaymentMethodLabel($method),
                    'count' => 0,
                    'revenue' => 0,
                ];
            }
            $paymentMethods[$method]['count']++;
            $paymentMethods[$method]['revenue'] += $transaction->total_amount;
        }

        return collect($paymentMethods)
            ->sortByDesc('revenue')
            ->values()
            ->toArray();
    }

    private function getHourlyAnalysis($transactions)
    {
        $hours = [];

        // Initialize hours array
        for ($i = 6; $i <= 22; $i++) {
            $hour = str_pad($i, 2, '0', STR_PAD_LEFT);
            $hours[$hour] = [
                'hour' => $hour . ':00',
                'count' => 0,
                'revenue' => 0,
            ];
        }

        // Process actual transaction data
        foreach ($transactions as $transaction) {
            $hour = $transaction->transaction_date->format('H');
            if (isset($hours[$hour])) {
                $hours[$hour]['count']++;
                $hours[$hour]['revenue'] += $transaction->total_amount;
            }
        }

        return array_values($hours);
    }

    private function getHourlyProductAnalysis($transactions)
    {
        $hours = [];
        $products = [];
        $productIds = [];

        // Initialize hours array (6 AM to 10 PM)
        for ($i = 6; $i <= 22; $i++) {
            $hour = str_pad($i, 2, '0', STR_PAD_LEFT) . ':00';
            $hours[] = $hour;
        }

        // Process transactions to get product sales per hour
        foreach ($transactions as $transaction) {
            $hour = $transaction->transaction_date->format('H') . ':00';

            // Only include hours within business hours
            if (in_array($hour, $hours)) {
                foreach ($transaction->posTransactionItems as $item) {
                    $productId = $item->product_id;
                    $productName = $item->product->name;

                    // Store product ID for reference
                    $productIds[$productName] = $productId;

                    if (!isset($products[$productName])) {
                        $products[$productName] = [
                            'id' => $productId,
                            'name' => $productName,
                            'category' => $item->product->category->name ?? 'Uncategorized',
                            'hours' => []
                        ];

                        // Initialize all hours for this product
                        foreach ($hours as $h) {
                            $products[$productName]['hours'][$h] = 0;
                        }
                    }

                    $products[$productName]['hours'][$hour] += $item->quantity;
                }
            }
        }

        // Calculate total sales for each product
        foreach ($products as $productName => &$productData) {
            $productData['total'] = array_sum($productData['hours']);
        }

        // Sort products by total sales
        uasort($products, function($a, $b) {
            return $b['total'] <=> $a['total'];
        });

        // Filter products based on selectedProducts if specified
        $filteredProducts = [];

        if (!empty($this->selectedProducts)) {
            // Filter by selected product IDs
            foreach ($products as $productName => $productData) {
                if (in_array($productData['id'], $this->selectedProducts)) {
                    $filteredProducts[$productName] = $productData;
                }
            }
        } else {
            // Take top 10 if no specific products selected
            $filteredProducts = array_slice($products, 0, 10, true);
        }

        // Format data for chart
        $chartData = [];
        foreach ($filteredProducts as $productName => $productData) {
            $chartData[$productName] = $productData['hours'];
        }

        return [
            'hours' => $hours,
            'products' => $chartData,
            'productDetails' => $filteredProducts,
            'allProducts' => $products
        ];
    }

    private function getTopCustomers($transactions)
    {
        $customers = [];

        foreach ($transactions as $transaction) {
            if ($transaction->customer) {
                $customerId = $transaction->customer_id;
                if (!isset($customers[$customerId])) {
                    $customers[$customerId] = [
                        'name' => $transaction->customer->nama,
                        'email' => $transaction->customer->email,
                        'count' => 0,
                        'revenue' => 0,
                        'loyalty_points' => $transaction->customer->loyalty_points ?? 0,
                    ];
                }
                $customers[$customerId]['count']++;
                $customers[$customerId]['revenue'] += $transaction->total_amount;
            }
        }

        return collect($customers)
            ->sortByDesc('revenue')
            ->take(10)
            ->values()
            ->toArray();
    }

    private function getPaymentMethodLabel($method)
    {
        return match ($method) {
            'cash' => 'Tunai',
            'card' => 'Kartu',
            'transfer' => 'Transfer',
            'ewallet' => 'E-Wallet',
            'qris' => 'QRIS',
            default => ucfirst($method),
        };
    }

    public function getFilterSummary()
    {
        $filters = [];

        if ($this->datePreset === 'all') {
            $filters[] = "Periode: Seluruh Transaksi";
        } elseif ($this->startDate && $this->endDate) {
            $startFormatted = Carbon::parse($this->startDate)->format('d/m/Y H:i');
            $endFormatted = Carbon::parse($this->endDate)->format('d/m/Y H:i');
            $filters[] = "Periode: $startFormatted - $endFormatted";
        }

        if ($this->datePreset && $this->datePreset !== 'custom' && $this->datePreset !== 'all') {
            $presetLabels = [
                'today' => 'Hari Ini',
                'yesterday' => 'Kemarin',
                'last_7_days' => '7 Hari Terakhir',
                'this_week' => 'Minggu Ini',
                'last_week' => 'Minggu Lalu',
                'last_30_days' => '30 Hari Terakhir',
                'this_month' => 'Bulan Ini',
                'last_month' => 'Bulan Lalu',
            ];
            $filters[] = "Preset: " . ($presetLabels[$this->datePreset] ?? $this->datePreset);
        }

        return $filters;
    }

    public function refreshData()
    {
        $this->generateReport();

        // Force re-render of charts
        $this->dispatch('charts-updated');

        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Data berhasil diperbarui!'
        ]);
    }

    public function getChartData()
    {
        // Return current chart data as JSON for JavaScript
        return [
            'topProducts' => $this->reportData['topProducts'] ?? [],
            'categorySales' => $this->reportData['categorySales'] ?? [],
            'hourlyProductAnalysis' => $this->reportData['hourlyProductAnalysis'] ?? [],
            'datePreset' => $this->datePreset,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ];
    }

    public function updatedDatePreset()
    {
        $this->applyDatePreset();

        // Force re-render of charts
        $this->dispatch('charts-updated');
    }

    public function updatedStartDate()
    {
        if ($this->startDate && $this->endDate) {
            $this->datePreset = 'custom';
            $this->generateReport();
            $this->dispatch('charts-updated');
        }
    }

    public function updatedEndDate()
    {
        if ($this->startDate && $this->endDate) {
            $this->datePreset = 'custom';
            $this->generateReport();
            $this->dispatch('charts-updated');
        }
    }

    public function updateSelectedProducts($productIds)
    {
        $this->selectedProducts = $productIds;
        $this->generateReport();
        $this->dispatch('charts-updated');

        return $this->reportData['hourlyProductAnalysis'];
    }

}
