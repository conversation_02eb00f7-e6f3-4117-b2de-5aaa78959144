<?php

namespace App\Filament\Marketing\Resources;

use App\Filament\Marketing\Resources\CustomerFeedbackResource\Pages;
use App\Filament\Marketing\Resources\CustomerFeedbackResource\RelationManagers;
use App\Models\CustomerFeedback;
use App\Models\Customer;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CustomerFeedbackResource extends Resource
{
    protected static ?string $model = CustomerFeedback::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-ellipsis';

    protected static ?string $navigationGroup = 'CRM';

    protected static ?string $navigationLabel = 'Feedback Pelanggan';

    protected static ?string $modelLabel = 'Feedback';

    protected static ?string $pluralModelLabel = 'Feedback Pelanggan';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Feedback')
                    ->schema([
                        Forms\Components\Select::make('customer_id')
                            ->label('Pelanggan')
                            ->relationship('customer', 'nama')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Select::make('type')
                            ->label('Jenis Feedback')
                            ->options([
                                'keluhan' => 'Keluhan',
                                'saran' => 'Saran',
                            ])
                            ->required(),

                        Forms\Components\TextInput::make('subject')
                            ->label('Subjek')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->required()
                            ->rows(4)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Penanganan')
                    ->schema([
                        Forms\Components\Select::make('assigned_to_user_id')
                            ->label('Ditugaskan Kepada')
                            ->relationship('assignedToUser', 'name')
                            ->searchable()
                            ->preload(),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'new' => 'Baru',
                                'in_progress' => 'Dalam Proses',
                                'resolved' => 'Selesai',
                            ])
                            ->default('new')
                            ->required(),

                        Forms\Components\Textarea::make('resolution_notes')
                            ->label('Catatan Penyelesaian')
                            ->rows(3)
                            ->columnSpanFull()
                            ->visible(fn (Forms\Get $get): bool => $get('status') === 'resolved'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('customer.nama')
                    ->label('Pelanggan')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->label('Jenis')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'keluhan' => 'danger',
                        'saran' => 'info',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'keluhan' => 'Keluhan',
                        'saran' => 'Saran',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('subject')
                    ->label('Subjek')
                    ->searchable()
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 30 ? $state : null;
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'new' => 'danger',
                        'in_progress' => 'warning',
                        'resolved' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'new' => 'Baru',
                        'in_progress' => 'Dalam Proses',
                        'resolved' => 'Selesai',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('assignedToUser.name')
                    ->label('Ditugaskan Kepada')
                    ->default('Belum Ditugaskan')
                    ->color(fn ($state): string => $state === 'Belum Ditugaskan' ? 'gray' : 'primary'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tanggal Dibuat')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('Jenis Feedback')
                    ->options([
                        'keluhan' => 'Keluhan',
                        'saran' => 'Saran',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'new' => 'Baru',
                        'in_progress' => 'Dalam Proses',
                        'resolved' => 'Selesai',
                    ]),

                Tables\Filters\SelectFilter::make('assigned_to_user_id')
                    ->label('Ditugaskan Kepada')
                    ->relationship('assignedToUser', 'name'),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

                Tables\Actions\Action::make('mark_in_progress')
                    ->label('Proses')
                    ->icon('heroicon-o-play')
                    ->color('warning')
                    ->action(fn (CustomerFeedback $record) => $record->update(['status' => 'in_progress']))
                    ->visible(fn (CustomerFeedback $record): bool => $record->status === 'new'),

                Tables\Actions\Action::make('mark_resolved')
                    ->label('Selesai')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->form([
                        Forms\Components\Textarea::make('resolution_notes')
                            ->label('Catatan Penyelesaian')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function (CustomerFeedback $record, array $data): void {
                        $record->update([
                            'status' => 'resolved',
                            'resolution_notes' => $data['resolution_notes'],
                        ]);
                    })
                    ->visible(fn (CustomerFeedback $record): bool => $record->status !== 'resolved'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomerFeedback::route('/'),
            'create' => Pages\CreateCustomerFeedback::route('/create'),
            'edit' => Pages\EditCustomerFeedback::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
