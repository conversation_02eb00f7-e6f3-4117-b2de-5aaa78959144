<?php

namespace App\Filament\Marketing\Resources;

use App\Filament\Marketing\Resources\CustomerResource\Pages;
use App\Filament\Marketing\Resources\CustomerResource\RelationManagers;
use App\Filament\Marketing\Exports\CustomerExporter;
use App\Models\Customer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Actions\ExportAction;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'CRM';

    protected static ?string $navigationLabel = 'Pelanggan';

    protected static ?string $modelLabel = 'Pelanggan';

    protected static ?string $pluralModelLabel = 'Pelanggan';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Dasar')
                    ->schema([
                        Forms\Components\TextInput::make('nama')
                            ->label('Nama Lengkap')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),

                        Forms\Components\TextInput::make('telepon')
                            ->label('Nomor Telepon')
                            ->tel()
                            ->maxLength(255),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Informasi Personal')
                    ->schema([
                        Forms\Components\DatePicker::make('tanggal_lahir')
                            ->label('Tanggal Lahir')
                            ->maxDate(now()),

                        Forms\Components\Select::make('jenis_kelamin')
                            ->label('Jenis Kelamin')
                            ->options([
                                'L' => 'Laki-laki',
                                'P' => 'Perempuan',
                            ]),

                        Forms\Components\Textarea::make('alamat')
                            ->label('Alamat Lama')
                            ->rows(3)
                            ->columnSpanFull()
                            ->helperText('Field ini akan deprecated, gunakan alamat detail di bawah'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Alamat Lengkap')
                    ->schema([
                        Forms\Components\Select::make('province_id')
                            ->label('Provinsi')
                            ->relationship('province', 'name')
                            ->searchable()
                            ->preload()
                            ->reactive()
                            ->afterStateUpdated(function (Forms\Set $set) {
                                $set('city_id', null);
                                $set('district_id', null);
                                $set('village_id', null);
                            }),

                        Forms\Components\Select::make('city_id')
                            ->label('Kota/Kabupaten')
                            ->options(function (Forms\Get $get) {
                                $provinceId = $get('province_id');
                                if (!$provinceId) {
                                    return [];
                                }
                                return \App\Models\City::where('province_id', $provinceId)
                                    ->where('is_active', true)
                                    ->get()
                                    ->mapWithKeys(function ($city) {
                                        return [$city->id => $city->type_label . ' ' . $city->name];
                                    });
                            })
                            ->searchable()
                            ->reactive()
                            ->afterStateUpdated(function (Forms\Set $set) {
                                $set('district_id', null);
                                $set('village_id', null);
                            }),

                        Forms\Components\Select::make('district_id')
                            ->label('Kecamatan')
                            ->options(function (Forms\Get $get) {
                                $cityId = $get('city_id');
                                if (!$cityId) {
                                    return [];
                                }
                                return \App\Models\District::where('city_id', $cityId)
                                    ->where('is_active', true)
                                    ->pluck('name', 'id');
                            })
                            ->searchable()
                            ->reactive()
                            ->afterStateUpdated(function (Forms\Set $set) {
                                $set('village_id', null);
                            }),

                        Forms\Components\Select::make('village_id')
                            ->label('Kelurahan/Desa')
                            ->options(function (Forms\Get $get) {
                                $districtId = $get('district_id');
                                if (!$districtId) {
                                    return [];
                                }
                                return \App\Models\Village::where('district_id', $districtId)
                                    ->where('is_active', true)
                                    ->get()
                                    ->mapWithKeys(function ($village) {
                                        return [$village->id => $village->type_label . ' ' . $village->name];
                                    });
                            })
                            ->searchable()
                            ->reactive(),

                        Forms\Components\TextInput::make('postal_code')
                            ->label('Kode Pos')
                            ->maxLength(10)
                            ->placeholder('Contoh: 12345'),

                        Forms\Components\Textarea::make('detail_address')
                            ->label('Alamat Detail')
                            ->placeholder('RT/RW, nama jalan, nomor rumah, dll')
                            ->rows(3)
                            ->columnSpanFull()
                            ->helperText('Masukkan detail alamat seperti RT/RW, nama jalan, nomor rumah'),
                    ])
                    ->columns(2)
                    ->collapsible(),

                Forms\Components\Section::make('Informasi Bisnis')
                    ->schema([
                        Forms\Components\TextInput::make('loyalty_points')
                            ->label('Poin Loyalitas')
                            ->numeric()
                            ->default(0)
                            ->minValue(0),

                        Forms\Components\Select::make('segment')
                            ->label('Segmen Pelanggan')
                            ->options([
                                'top_spenders' => 'Top Spenders',
                                'frequent_buyers' => 'Frequent Buyers',
                                'lapsed_customers' => 'Lapsed Customers',
                                'product_specific_buyers' => 'Product Specific Buyers',
                            ]),

                        Forms\Components\Textarea::make('notes')
                            ->label('Catatan')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Status Aktif')
                            ->default(true)
                            ->helperText('Customer aktif dapat melakukan transaksi'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nama')
                    ->label('Nama')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('telepon')
                    ->label('Telepon')
                    ->searchable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('loyalty_points')
                    ->label('Poin Loyalitas')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color('success'),

                Tables\Columns\TextColumn::make('segment')
                    ->label('Segmen')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'top_spenders' => 'success',
                        'frequent_buyers' => 'info',
                        'lapsed_customers' => 'warning',
                        'product_specific_buyers' => 'primary',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('short_address')
                    ->label('Alamat')
                    ->getStateUsing(fn ($record) => $record->short_address ?: $record->alamat)
                    ->limit(50)
                    ->tooltip(fn ($record) => $record->full_address ?: $record->alamat)
                    ->toggleable(),

                Tables\Columns\TextColumn::make('province.name')
                    ->label('Provinsi')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('city.name')
                    ->label('Kota/Kabupaten')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('membership_tier')
                    ->label('Tier')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Platinum' => 'success',
                        'Gold' => 'warning',
                        'Silver' => 'info',
                        'Bronze' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tanggal Daftar')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis_kelamin')
                    ->label('Jenis Kelamin')
                    ->options([
                        'L' => 'Laki-laki',
                        'P' => 'Perempuan',
                    ]),

                Tables\Filters\SelectFilter::make('segment')
                    ->label('Segmen')
                    ->options([
                        'top_spenders' => 'Top Spenders',
                        'frequent_buyers' => 'Frequent Buyers',
                        'lapsed_customers' => 'Lapsed Customers',
                        'product_specific_buyers' => 'Product Specific Buyers',
                    ]),

                Tables\Filters\SelectFilter::make('province_id')
                    ->label('Provinsi')
                    ->relationship('province', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('city_id')
                    ->label('Kota/Kabupaten')
                    ->relationship('city', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('has_complete_address')
                    ->label('Alamat Lengkap')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('village_id'))
                    ->toggle(),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status')
                    ->placeholder('Semua')
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif'),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->headerActions([
                Tables\Actions\ExportAction::make()
                    ->exporter(CustomerExporter::class)
                    ->label('Export Data Pelanggan')
                    ->color('success'),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\PosTransactionsRelationManager::class,
            RelationManagers\LoyaltyTransactionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
