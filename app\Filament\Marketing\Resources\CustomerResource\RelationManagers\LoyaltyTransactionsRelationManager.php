<?php

namespace App\Filament\Marketing\Resources\CustomerResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class LoyaltyTransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'loyaltyTransactions';

    protected static ?string $title = 'Riwayat Poin Loyalitas';

    protected static ?string $modelLabel = 'Transaksi Poin';

    protected static ?string $pluralModelLabel = 'Transaksi Poin';

    public function form(Form $form): Form
    {
        // This relation manager is view-only, no form needed
        return $form->schema([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('description')
            ->columns([
                Tables\Columns\TextColumn::make('type')
                    ->label('Tipe')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'earn' => 'success',
                        'redeem' => 'warning',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'earn' => 'Dapat Poin',
                        'redeem' => 'Tukar Poin',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('points')
                    ->label('Poin')
                    ->numeric()
                    ->badge()
                    ->color(fn ($record): string => $record->type === 'earn' ? 'success' : 'warning')
                    ->formatStateUsing(fn ($record): string =>
                        ($record->type === 'earn' ? '+' : '-') . number_format($record->points)
                    ),

                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->searchable()
                    ->wrap(),

                Tables\Columns\TextColumn::make('reference_type')
                    ->label('Referensi')
                    ->formatStateUsing(fn (?string $state): string => match ($state) {
                        'pos_transaction' => 'Transaksi POS',
                        'manual' => 'Manual',
                        'promotion' => 'Promosi',
                        'birthday' => 'Ulang Tahun',
                        default => $state ?? 'N/A',
                    })
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tanggal')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('Tipe')
                    ->options([
                        'earn' => 'Dapat Poin',
                        'redeem' => 'Tukar Poin',
                    ]),

                Tables\Filters\SelectFilter::make('reference_type')
                    ->label('Referensi')
                    ->options([
                        'pos_transaction' => 'Transaksi POS',
                        'manual' => 'Manual',
                        'promotion' => 'Promosi',
                        'birthday' => 'Ulang Tahun',
                    ]),

                Tables\Filters\Filter::make('today')
                    ->label('Hari Ini')
                    ->query(fn (Builder $query): Builder => $query->whereDate('created_at', now())),

                Tables\Filters\Filter::make('this_month')
                    ->label('Bulan Ini')
                    ->query(fn (Builder $query): Builder => $query->whereMonth('created_at', now()->month)
                        ->whereYear('created_at', now()->year)),
            ])
            ->headerActions([
                Tables\Actions\Action::make('add_manual_points')
                    ->label('Tambah Poin Manual')
                    ->icon('heroicon-o-plus')
                    ->color('success')
                    ->form([
                        Forms\Components\Select::make('type')
                            ->label('Tipe')
                            ->options([
                                'earn' => 'Dapat Poin',
                                'redeem' => 'Tukar Poin',
                            ])
                            ->required(),

                        Forms\Components\TextInput::make('points')
                            ->label('Jumlah Poin')
                            ->required()
                            ->numeric()
                            ->minValue(1),

                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function (array $data): void {
                        \App\Models\LoyaltyTransaction::create([
                            'customer_id' => $this->ownerRecord->id,
                            'type' => $data['type'],
                            'points' => $data['points'],
                            'description' => $data['description'],
                            'reference_type' => 'manual',
                        ]);

                        // Update customer loyalty points
                        if ($data['type'] === 'earn') {
                            $this->ownerRecord->increment('loyalty_points', $data['points']);
                        } else {
                            $this->ownerRecord->decrement('loyalty_points', $data['points']);
                        }

                        \Filament\Notifications\Notification::make()
                            ->title('Poin loyalitas berhasil ditambahkan')
                            ->success()
                            ->send();
                    }),
            ])
            ->actions([
                // No edit/delete actions for view-only relation
            ])
            ->bulkActions([
                // No bulk actions for view-only relation
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Belum Ada Transaksi Poin')
            ->emptyStateDescription('Pelanggan ini belum memiliki riwayat transaksi poin loyalitas.')
            ->emptyStateIcon('heroicon-o-star');
    }
}
