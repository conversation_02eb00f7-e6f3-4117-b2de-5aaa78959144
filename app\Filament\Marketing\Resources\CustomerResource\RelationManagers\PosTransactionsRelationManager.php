<?php

namespace App\Filament\Marketing\Resources\CustomerResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PosTransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'posTransactions';

    protected static ?string $title = 'Riwayat Pembelian';

    protected static ?string $modelLabel = 'Transaksi';

    protected static ?string $pluralModelLabel = 'Transaksi';

    public function form(Form $form): Form
    {
        // This relation manager is view-only, no form needed
        return $form->schema([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('transaction_number')
            ->columns([
                Tables\Columns\TextColumn::make('transaction_number')
                    ->label('Nomor Transaksi')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('Tanggal & Waktu')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Metode Bayar')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'cash' => 'success',
                        'card' => 'info',
                        'transfer' => 'warning',
                        'ewallet' => 'primary',
                        'qris' => 'secondary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'cash' => 'Tunai',
                        'card' => 'Kartu',
                        'transfer' => 'Transfer',
                        'ewallet' => 'E-Wallet',
                        'qris' => 'QRIS',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('posTransactionItems_count')
                    ->label('Jumlah Item')
                    ->counts('posTransactionItems')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('net_amount')
                    ->label('Total Bersih')
                    ->money('IDR')
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('loyalty_points_earned')
                    ->label('Poin Earned')
                    ->numeric()
                    ->badge()
                    ->color('success')
                    ->default(0),

                Tables\Columns\TextColumn::make('table_number')
                    ->label('No. Meja')
                    ->default('N/A')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\IconColumn::make('is_offline_transaction')
                    ->label('Offline')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('payment_method')
                    ->label('Metode Pembayaran')
                    ->options([
                        'cash' => 'Tunai',
                        'card' => 'Kartu',
                        'transfer' => 'Transfer',
                        'ewallet' => 'E-Wallet',
                        'qris' => 'QRIS',
                    ]),

                Tables\Filters\Filter::make('today')
                    ->label('Hari Ini')
                    ->query(fn (Builder $query): Builder => $query->whereDate('transaction_date', now())),

                Tables\Filters\Filter::make('this_week')
                    ->label('Minggu Ini')
                    ->query(fn (Builder $query): Builder => $query->whereBetween('transaction_date', [
                        now()->startOfWeek(),
                        now()->endOfWeek()
                    ])),

                Tables\Filters\Filter::make('this_month')
                    ->label('Bulan Ini')
                    ->query(fn (Builder $query): Builder => $query->whereMonth('transaction_date', now()->month)
                        ->whereYear('transaction_date', now()->year)),
            ])
            ->headerActions([
                // No create action - this is view-only
            ])
            ->actions([
                Tables\Actions\Action::make('view_details')
                    ->label('Detail')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->modalHeading(fn ($record) => 'Detail Transaksi: ' . $record->transaction_number)
                    ->modalContent(fn ($record) => view('filament.marketing.modals.pos-transaction-detail', ['record' => $record]))
                    ->modalWidth('5xl'),

                Tables\Actions\Action::make('view_items')
                    ->label('Items')
                    ->icon('heroicon-o-list-bullet')
                    ->color('warning')
                    ->modalHeading(fn ($record) => 'Items Transaksi: ' . $record->transaction_number)
                    ->modalContent(fn ($record) => view('filament.marketing.modals.pos-transaction-items', ['record' => $record]))
                    ->modalWidth('4xl'),
            ])
            ->bulkActions([
                // No bulk actions for view-only relation
            ])
            ->defaultSort('transaction_date', 'desc')
            ->emptyStateHeading('Belum Ada Transaksi')
            ->emptyStateDescription('Pelanggan ini belum melakukan transaksi pembelian.')
            ->emptyStateIcon('heroicon-o-shopping-cart');
    }
}
