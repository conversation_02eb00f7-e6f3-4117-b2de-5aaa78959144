<?php

namespace App\Filament\Marketing\Resources;

use App\Filament\Marketing\Resources\LoyaltyProgramResource\Pages;
use App\Filament\Marketing\Resources\LoyaltyProgramResource\RelationManagers;
use App\Models\LoyaltyProgram;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class LoyaltyProgramResource extends Resource
{
    protected static ?string $model = LoyaltyProgram::class;

    protected static ?string $navigationIcon = 'heroicon-o-gift';

    protected static ?string $navigationGroup = 'CRM';

    protected static ?string $navigationLabel = 'Program Loyalitas';

    protected static ?string $modelLabel = 'Program Loyalitas';

    protected static ?string $pluralModelLabel = 'Program Loyalitas';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Dasar')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nama Program')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->rows(3),

                        Forms\Components\Select::make('type')
                            ->label('Tipe Program')
                            ->options([
                                'points' => 'Points',
                                'cashback' => 'Cashback',
                                'discount' => 'Discount',
                            ])
                            ->default('points')
                            ->required(),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Aktif')
                            ->default(true),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Konfigurasi Poin')
                    ->schema([
                        Forms\Components\TextInput::make('points_per_amount')
                            ->label('Poin per Jumlah (Rp)')
                            ->helperText('1 poin per berapa rupiah')
                            ->numeric()
                            ->default(1000)
                            ->required(),

                        Forms\Components\TextInput::make('points_multiplier')
                            ->label('Multiplier Poin')
                            ->helperText('Pengali dasar untuk poin')
                            ->numeric()
                            ->step(0.1)
                            ->default(1.0)
                            ->required(),

                        Forms\Components\TextInput::make('minimum_transaction')
                            ->label('Minimum Transaksi (Rp)')
                            ->helperText('Minimum transaksi untuk dapat poin')
                            ->numeric()
                            ->default(10000)
                            ->required(),

                        Forms\Components\TextInput::make('max_points_per_transaction')
                            ->label('Maksimal Poin per Transaksi')
                            ->numeric()
                            ->default(1000)
                            ->required(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Konfigurasi Penukaran')
                    ->schema([
                        Forms\Components\TextInput::make('redemption_rate')
                            ->label('Rate Penukaran')
                            ->helperText('Berapa poin = 1000 rupiah')
                            ->numeric()
                            ->default(100)
                            ->required(),

                        Forms\Components\TextInput::make('minimum_redemption')
                            ->label('Minimum Penukaran Poin')
                            ->numeric()
                            ->default(50)
                            ->required(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Bonus & Multiplier')
                    ->schema([
                        Forms\Components\TextInput::make('birthday_bonus_points')
                            ->label('Bonus Poin Ulang Tahun')
                            ->numeric()
                            ->default(100),

                        Forms\Components\TextInput::make('vip_bonus_multiplier')
                            ->label('Multiplier VIP')
                            ->helperText('Multiplier untuk customer VIP')
                            ->numeric()
                            ->step(0.1)
                            ->default(1.5),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Periode Berlaku')
                    ->schema([
                        Forms\Components\DatePicker::make('start_date')
                            ->label('Tanggal Mulai'),

                        Forms\Components\DatePicker::make('end_date')
                            ->label('Tanggal Berakhir'),

                        Forms\Components\TextInput::make('points_expiry_months')
                            ->label('Poin Kadaluarsa (Bulan)')
                            ->helperText('Poin akan kadaluarsa setelah berapa bulan')
                            ->numeric()
                            ->default(12),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nama Program')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->label('Tipe')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'points' => 'primary',
                        'cashback' => 'success',
                        'discount' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('points_per_amount')
                    ->label('Poin per Rp')
                    ->formatStateUsing(fn ($state) => '1 poin / Rp ' . number_format($state, 0, ',', '.')),

                Tables\Columns\TextColumn::make('points_multiplier')
                    ->label('Multiplier')
                    ->formatStateUsing(fn ($state) => $state . 'x'),

                Tables\Columns\TextColumn::make('start_date')
                    ->label('Mulai')
                    ->date()
                    ->placeholder('Tidak terbatas'),

                Tables\Columns\TextColumn::make('end_date')
                    ->label('Berakhir')
                    ->date()
                    ->placeholder('Tidak terbatas'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('Tipe Program')
                    ->options([
                        'points' => 'Points',
                        'cashback' => 'Cashback',
                        'discount' => 'Discount',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status')
                    ->placeholder('Semua')
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif'),

                Tables\Filters\Filter::make('active_period')
                    ->label('Periode Aktif')
                    ->query(fn (Builder $query): Builder => $query
                        ->where(function ($q) {
                            $q->whereNull('start_date')
                              ->orWhere('start_date', '<=', now());
                        })
                        ->where(function ($q) {
                            $q->whereNull('end_date')
                              ->orWhere('end_date', '>=', now());
                        })
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLoyaltyPrograms::route('/'),
            'create' => Pages\CreateLoyaltyProgram::route('/create'),
            'edit' => Pages\EditLoyaltyProgram::route('/{record}/edit'),
        ];
    }
}
