<?php

namespace App\Filament\Marketing\Resources\LoyaltyProgramResource\Pages;

use App\Filament\Marketing\Resources\LoyaltyProgramResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListLoyaltyPrograms extends ListRecords
{
    protected static string $resource = LoyaltyProgramResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
