<?php

namespace App\Filament\Marketing\Resources;

use App\Filament\Marketing\Resources\LoyaltyTransactionResource\Pages;
use App\Filament\Marketing\Resources\LoyaltyTransactionResource\RelationManagers;
use App\Models\LoyaltyTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class LoyaltyTransactionResource extends Resource
{
    protected static ?string $model = LoyaltyTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-star';

    protected static ?string $navigationGroup = 'CRM';

    protected static ?string $navigationLabel = 'Transaksi Loyalitas';

    protected static ?string $modelLabel = 'Transaksi Loyalitas';

    protected static ?string $pluralModelLabel = 'Transaksi Loyalitas';

    // Make this resource read-only
    protected static bool $canCreate = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('customer.nama')
                    ->label('Pelanggan')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->label('Jenis')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'earn' => 'success',
                        'redeem' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'earn' => 'Mendapat Poin',
                        'redeem' => 'Tukar Poin',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('points')
                    ->label('Poin')
                    ->numeric()
                    ->sortable()
                    ->color(fn ($record): string => $record->points >= 0 ? 'success' : 'danger')
                    ->formatStateUsing(fn ($state): string => ($state >= 0 ? '+' : '') . number_format($state)),

                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),

                Tables\Columns\TextColumn::make('reference_type')
                    ->label('Referensi')
                    ->badge()
                    ->formatStateUsing(fn (?string $state): string => match ($state) {
                        'pos_transaction' => 'POS',
                        'sales_order' => 'Sales Order',
                        'manual' => 'Manual',
                        default => 'N/A',
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tanggal')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('Jenis Transaksi')
                    ->options([
                        'earn' => 'Mendapat Poin',
                        'redeem' => 'Tukar Poin',
                    ]),

                Tables\Filters\SelectFilter::make('reference_type')
                    ->label('Jenis Referensi')
                    ->options([
                        'pos_transaction' => 'POS Transaction',
                        'sales_order' => 'Sales Order',
                        'manual' => 'Manual',
                    ]),
            ])
            ->actions([
                // No edit/delete actions for read-only resource
            ])
            ->bulkActions([
                // No bulk actions for read-only resource
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLoyaltyTransactions::route('/'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit($record): bool
    {
        return false;
    }

    public static function canDelete($record): bool
    {
        return false;
    }
}
