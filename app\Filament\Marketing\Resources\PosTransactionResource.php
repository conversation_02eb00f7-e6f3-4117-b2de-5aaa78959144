<?php

namespace App\Filament\Marketing\Resources;

use App\Filament\Marketing\Resources\PosTransactionResource\Pages;
use App\Models\PosTransaction;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PosTransactionResource extends Resource
{
    protected static ?string $model = PosTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';

    protected static ?string $navigationGroup = 'Sales';

    protected static ?string $navigationLabel = 'Transaksi POS';

    protected static ?string $modelLabel = 'Transaksi POS';

    protected static ?string $pluralModelLabel = 'Transaksi POS';

    // Enable create for POS transactions
    protected static bool $canCreate = true;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('transaction_number')
                    ->label('Nomor Transaksi')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('customer.nama')
                    ->label('Pelanggan')
                    ->searchable()
                    ->default('Walk-in Customer'),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Kasir')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('Tanggal & Waktu')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Metode Bayar')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'cash' => 'success',
                        'card' => 'info',
                        'transfer' => 'warning',
                        'ewallet' => 'primary',
                        'qris' => 'secondary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'cash' => 'Tunai',
                        'card' => 'Kartu',
                        'transfer' => 'Transfer',
                        'ewallet' => 'E-Wallet',
                        'qris' => 'QRIS',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('net_amount')
                    ->label('Total Bersih')
                    ->money('IDR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('loyalty_points_earned')
                    ->label('Poin Earned')
                    ->numeric()
                    ->badge()
                    ->color('success')
                    ->default(0),

                Tables\Columns\TextColumn::make('table_number')
                    ->label('No. Meja')
                    ->default('N/A')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\IconColumn::make('is_offline_transaction')
                    ->label('Offline')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('payment_method')
                    ->label('Metode Pembayaran')
                    ->options([
                        'cash' => 'Tunai',
                        'card' => 'Kartu',
                        'transfer' => 'Transfer',
                        'ewallet' => 'E-Wallet',
                        'qris' => 'QRIS',
                    ]),

                Tables\Filters\SelectFilter::make('user_id')
                    ->label('Kasir')
                    ->relationship('user', 'name'),

                Tables\Filters\Filter::make('today')
                    ->label('Hari Ini')
                    ->query(fn (Builder $query): Builder => $query->today()),

                Tables\Filters\Filter::make('this_month')
                    ->label('Bulan Ini')
                    ->query(fn (Builder $query): Builder => $query->thisMonth()),

                Tables\Filters\Filter::make('fnb_only')
                    ->label('F&B Saja')
                    ->query(fn (Builder $query): Builder => $query->fnb()),

                Tables\Filters\Filter::make('offline_transactions')
                    ->label('Transaksi Offline')
                    ->query(fn (Builder $query): Builder => $query->offline()),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('Detail')
                    ->icon('heroicon-o-eye'),
            ])
            ->bulkActions([
                // No bulk actions for read-only resource
            ])
            ->defaultSort('transaction_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPosTransactions::route('/'),
            'view' => Pages\ViewPosTransaction::route('/{record}'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit($record): bool
    {
        return false;
    }

    public static function canDelete($record): bool
    {
        return false;
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['customer', 'user', 'posTransactionItems.product'])
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
