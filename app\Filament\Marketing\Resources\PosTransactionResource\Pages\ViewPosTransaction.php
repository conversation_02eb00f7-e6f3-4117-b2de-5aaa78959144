<?php

namespace App\Filament\Marketing\Resources\PosTransactionResource\Pages;

use App\Filament\Marketing\Resources\PosTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\RepeatableEntry;

class ViewPosTransaction extends ViewRecord
{
    protected static string $resource = PosTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn () => false), // Disable edit for POS transactions
        ];
    }

    protected function resolveRecord($key): \Illuminate\Database\Eloquent\Model
    {
        return static::getResource()::resolveRecordRouteBinding($key)
            ->load(['customer', 'user', 'posTransactionItems.product']);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Transaksi')
                    ->schema([
                        TextEntry::make('transaction_number')
                            ->label('Nomor Transaksi')
                            ->copyable(),

                        TextEntry::make('transaction_date')
                            ->label('Tanggal Transaksi')
                            ->dateTime(),

                        TextEntry::make('customer.nama')
                            ->label('Customer')
                            ->default('Walk-in Customer'),

                        TextEntry::make('payment_method')
                            ->label('Metode Pembayaran')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'cash' => 'success',
                                'card' => 'info',
                                'transfer' => 'warning',
                                'qris' => 'primary',
                                default => 'gray',
                            }),

                        TextEntry::make('status')
                            ->label('Status')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'completed' => 'success',
                                'pending' => 'warning',
                                'cancelled' => 'danger',
                                default => 'gray',
                            }),
                    ])
                    ->columns(2),

                Section::make('Detail Pembayaran')
                    ->schema([
                        TextEntry::make('total_amount')
                            ->label('Subtotal')
                            ->money('IDR'),

                        TextEntry::make('tax_amount')
                            ->label('Pajak')
                            ->money('IDR'),

                        TextEntry::make('discount_amount')
                            ->label('Diskon')
                            ->money('IDR'),

                        TextEntry::make('net_amount')
                            ->label('Total')
                            ->money('IDR')
                            ->weight('bold'),

                        TextEntry::make('amount_paid')
                            ->label('Dibayar')
                            ->money('IDR'),

                        TextEntry::make('change_given')
                            ->label('Kembalian')
                            ->money('IDR'),
                    ])
                    ->columns(3),

                Section::make('Item Transaksi')
                    ->schema([
                        RepeatableEntry::make('posTransactionItems')
                            ->label('')
                            ->schema([
                                TextEntry::make('product.name')
                                    ->label('Produk'),

                                TextEntry::make('quantity')
                                    ->label('Qty'),

                                TextEntry::make('unit_price')
                                    ->label('Harga Satuan')
                                    ->money('IDR'),

                                TextEntry::make('total_price')
                                    ->label('Total')
                                    ->money('IDR'),
                            ])
                            ->columns(4),
                    ]),

                Section::make('Informasi Tambahan')
                    ->schema([
                        TextEntry::make('notes')
                            ->label('Catatan')
                            ->default('Tidak ada catatan'),

                        TextEntry::make('created_at')
                            ->label('Dibuat')
                            ->dateTime(),

                        TextEntry::make('updated_at')
                            ->label('Diperbarui')
                            ->dateTime(),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }
}
