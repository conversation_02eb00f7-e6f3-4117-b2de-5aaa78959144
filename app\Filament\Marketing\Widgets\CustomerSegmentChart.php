<?php

namespace App\Filament\Marketing\Widgets;

use Filament\Widgets\ChartWidget;
use App\Models\Customer;

class CustomerSegmentChart extends ChartWidget
{
    protected static ?string $heading = 'Distribusi Segmen Pelanggan';

    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $segments = Customer::selectRaw('segment, COUNT(*) as count')
            ->groupBy('segment')
            ->pluck('count', 'segment')
            ->toArray();

        $segmentLabels = [
            'top_spenders' => 'Top Spenders',
            'frequent_buyers' => 'Frequent Buyers',
            'lapsed_customers' => 'Lapsed Customers',
            'product_specific_buyers' => 'Product Specific Buyers',
            'regular_customers' => 'Regular Customers',
            'new_customer' => 'New Customers',
        ];

        $labels = [];
        $data = [];
        $colors = [
            '#10B981', // green
            '#3B82F6', // blue
            '#F59E0B', // amber
            '#8B5CF6', // violet
            '#06B6D4', // cyan
            '#84CC16', // lime
        ];

        $colorIndex = 0;
        foreach ($segments as $segment => $count) {
            $labels[] = $segmentLabels[$segment] ?? ($segment ?: 'Tidak Ada Segmen');
            $data[] = $count;
            $colorIndex++;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Pelanggan',
                    'data' => $data,
                    'backgroundColor' => array_slice($colors, 0, count($data)),
                    'borderColor' => array_slice($colors, 0, count($data)),
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}
