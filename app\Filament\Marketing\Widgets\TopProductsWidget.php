<?php

namespace App\Filament\Marketing\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use App\Models\Product;
use Illuminate\Database\Eloquent\Builder;

class TopProductsWidget extends BaseWidget
{
    protected static ?string $heading = 'Top 5 Produk Terlaris';

    protected static ?int $sort = 3;

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Product::query()
                    ->withCount(['quotationItems as total_quoted' => function (Builder $query) {
                        $query->selectRaw('SUM(quantity)');
                    }])
                    ->orderBy('total_quoted', 'desc')
                    ->limit(5)
            )
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nama Produk')
                    ->searchable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('category.name')
                    ->label('Kategori')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('price')
                    ->label('Harga')
                    ->money('IDR'),

                Tables\Columns\TextColumn::make('stock_quantity')
                    ->label('Stok')
                    ->numeric()
                    ->color(fn ($record): string => $record->isOutOfStock() ? 'danger' : ($record->isLowStock() ? 'warning' : 'success')),

                Tables\Columns\TextColumn::make('total_quoted')
                    ->label('Total Terjual')
                    ->numeric()
                    ->badge()
                    ->color('success')
                    ->default(0),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Aktif')
                    ->boolean(),
            ])
            ->paginated(false);
    }
}
