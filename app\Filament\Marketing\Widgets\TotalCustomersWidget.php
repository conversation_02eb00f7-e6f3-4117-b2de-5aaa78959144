<?php

namespace App\Filament\Marketing\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Customer;
use App\Models\Quotation;
use App\Models\Product;

class TotalCustomersWidget extends BaseWidget
{
    protected function getStats(): array
    {
        // Customer statistics
        $totalCustomers = Customer::count();
        $newCustomersThisMonth = Customer::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();
        $newCustomersLastMonth = Customer::whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->count();

        $customerGrowth = $newCustomersLastMonth > 0
            ? (($newCustomersThisMonth - $newCustomersLastMonth) / $newCustomersLastMonth) * 100
            : 0;

        // Quotation statistics
        $totalQuotations = Quotation::count();
        $quotationsThisMonth = Quotation::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        // Product statistics
        $totalProducts = Product::count();
        $activeProducts = Product::where('is_active', true)->count();

        return [
            Stat::make('Total Pelanggan', number_format($totalCustomers))
                ->description($newCustomersThisMonth . ' pelanggan baru bulan ini')
                ->descriptionIcon($customerGrowth >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($customerGrowth >= 0 ? 'success' : 'danger')
                ->chart([7, 2, 10, 3, 15, 4, 17]),

            Stat::make('Total Quotation', number_format($totalQuotations))
                ->description($quotationsThisMonth . ' quotation bulan ini')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('info')
                ->chart([15, 4, 10, 2, 12, 4, 12]),

            Stat::make('Total Produk', number_format($totalProducts))
                ->description($activeProducts . ' produk aktif')
                ->descriptionIcon('heroicon-m-cube')
                ->color('warning')
                ->chart([3, 8, 5, 10, 15, 8, 12]),
        ];
    }
}
