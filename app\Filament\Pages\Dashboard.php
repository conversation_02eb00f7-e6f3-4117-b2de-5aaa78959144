<?php

namespace App\Filament\Pages;

use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use App\Traits\HasAdvancedDashboardFilters;
use Illuminate\Support\Facades\Auth;

class Dashboard extends BaseDashboard implements HasForms
{
    use InteractsWithForms, HasAdvancedDashboardFilters;

    protected static ?string $navigationIcon = 'heroicon-o-home';
    protected static ?string $navigationLabel = 'Dashboard';

    public function getTitle(): string
    {
        $user = Auth::user();

        if ($user && $user->hasAnyRole(['super_admin', 'direktur'])) {
            return 'Dashboard Admin';
        }

        return 'Dashboard';
    }
    protected static string $routePath = '/dashboard';

    /**
     * Check if user can access this dashboard
     */
    public static function canAccess(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // if role is karywan
        if ($user->hasRole('karyawan')) {
            return false;
        }

        return true;
    }

    /**
     * Control navigation visibility based on permissions
     */
    public static function shouldRegisterNavigation(): bool
    {
        return static::canAccess();
    }

    public function getWidgets(): array
    {
        $user = Auth::user();

        // Base widgets for all users
        $widgets = [
            \Filament\Widgets\AccountWidget::class,
        ];

        // Advanced widgets only for super_admin and direktur
        if ($user && $user->hasAnyRole(['super_admin', 'direktur'])) {
            $widgets = array_merge($widgets, [
                \App\Filament\Widgets\SystemOverviewWidget::class,
                \App\Filament\Widgets\QuickStatsWidget::class,
                \App\Filament\Widgets\RecentActivitiesWidget::class,
            ]);
        }

        return $widgets;
    }

    public function getView(): string
    {
        $user = Auth::user();

        // Advanced view for super_admin and direktur
        if ($user && $user->hasAnyRole(['super_admin', 'direktur'])) {
            return 'filament.pages.advanced-dashboard-with-filters';
        }

        // Use parent's default dashboard view for manager roles
        return parent::getView();
    }

    public function getColumns(): int | string | array
    {
        $user = Auth::user();

        // For super_admin and direktur - use multi-column layout
        if ($user && $user->hasAnyRole(['super_admin', 'direktur'])) {
            return [
                'sm' => 1,
                'md' => 2,
                'lg' => 3,
                'xl' => 4,
            ];
        }

        // For manager roles - use single column layout
        return 1;
    }
}
