<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Filament\Widgets\EnhancedProjectStats;
use App\Filament\Widgets\ProjectHealthOverview;
use App\Filament\Widgets\TeamPerformanceChart;
use App\Filament\Widgets\ProjectTimelineWidget;
use App\Filament\Widgets\RecentProjectActivity;
use App\Filament\Widgets\ProjectResourceAllocation;
use Illuminate\Support\Facades\Auth;

class EnhancedProjectDashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar-square';

    protected static string $view = 'filament.pages.enhanced-project-dashboard';

    protected static ?string $title = 'Dashboard Kegiatan';

    protected static ?string $navigationLabel = 'Dashboard Kegiatan';

    protected static ?string $navigationGroup = 'Manajemen Kegiatan';

    protected static ?int $navigationSort = 1;

    protected static ?string $slug = 'dashboard-proyek';

    /**
     * Check if user can access this Enhanced Project Dashboard page
     */
    public static function canAccess(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Allow super admin and direktur (they have all permissions)
        if ($user->hasRole(['super_admin', 'direktur'])) {
            return true;
        }

        // Allow manager_hrd role (project management)
        if ($user->hasRole('manager_hrd')) {
            return true;
        }
        return false;
    }

    /**
     * Control navigation visibility based on permissions
     */
    public static function shouldRegisterNavigation(): bool
    {
        return static::canAccess();
    }


    protected function getHeaderWidgets(): array
    {
        return [
            EnhancedProjectStats::class,
            ProjectHealthOverview::class,
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            TeamPerformanceChart::class,
            ProjectTimelineWidget::class,
            RecentProjectActivity::class,
            ProjectResourceAllocation::class,
        ];
    }

    public function getViewData(): array
    {
        return [
            'totalProjects' => \App\Models\Project::count(),
            'activeProjects' => \App\Models\Project::where('status', 'active')->count(),
            'completedProjects' => \App\Models\Project::where('status', 'completed')->count(),
            'overdueProjects' => \App\Models\Project::where('end_date', '<', now())
                ->whereNotIn('status', ['completed', 'cancelled'])
                ->count(),
        ];
    }
}
