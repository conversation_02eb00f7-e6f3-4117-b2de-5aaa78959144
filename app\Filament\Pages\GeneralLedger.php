<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Tables\Contracts\HasTable;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use App\Models\Akun;
use App\Models\JournalEntry;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class GeneralLedger extends Page implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    protected $listeners = ['refreshTable' => '$refresh'];

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'Buku Besar';

    protected static ?string $title = 'Laporan Buku Besar';

    protected static ?string $navigationGroup = 'Akuntansi';

    protected static string $view = 'filament.pages.general-ledger';

    protected static ?int $navigationSort = 30;

    /**
     * Check if user can access this General Ledger page
     */
    public static function canAccess(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Allow super admin and direktur (they have all permissions)
        if ($user->hasRole(['super_admin', 'direktur'])) {
            return true;
        }

        // Allow manager_accounting role
        if ($user->hasRole('manager_accounting')) {
            return true;
        }
        return false;
    }

    /**
     * Control navigation visibility based on permissions
     */
    public static function shouldRegisterNavigation(): bool
    {
        return static::canAccess();
    }

    public ?array $data = [];
    public $account_id = null;
    public $start_date = null;
    public $end_date = null;

    public function mount(): void
    {
        $this->form->fill([
            'account_id' => null,
            'start_date' => now()->startOfMonth(),
            'end_date' => now()->endOfMonth(),
        ]);

        // Set default values for properties
        $this->start_date = now()->startOfMonth()->format('Y-m-d');
        $this->end_date = now()->endOfMonth()->format('Y-m-d');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Filter Laporan')
                    ->schema([
                        Forms\Components\Select::make('account_id')
                            ->label('Pilih Akun')
                            ->options(Akun::orderBy('kode_akun')->pluck('nama_akun', 'id'))
                            ->searchable()
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->account_id = $state;
                                $this->data['account_id'] = $state;
                                $this->refreshTable();
                            }),
                        Forms\Components\DatePicker::make('start_date')
                            ->label('Tanggal Mulai')
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->start_date = $state;
                                $this->data['start_date'] = $state;
                                $this->refreshTable();
                            }),
                        Forms\Components\DatePicker::make('end_date')
                            ->label('Tanggal Akhir')
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->end_date = $state;
                                $this->data['end_date'] = $state;
                                $this->refreshTable();
                            }),
                    ])
                    ->columns(3),
            ])
            ->statePath('data');
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                Tables\Columns\TextColumn::make('journal.transaction_date')
                    ->label('Tanggal')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('journal.journal_number')
                    ->label('Nomor Jurnal')
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->searchable()
                    ->limit(50),
                Tables\Columns\TextColumn::make('debit')
                    ->label('Debit')
                    ->money('IDR')
                    ->placeholder('-'),
                Tables\Columns\TextColumn::make('credit')
                    ->label('Credit')
                    ->money('IDR')
                    ->placeholder('-'),
            ])
            ->defaultSort('journal.transaction_date', 'asc')
            ->paginated(false)
            ->headerActions([
                Tables\Actions\Action::make('export')
                    ->label('Export PDF')
                    ->icon('heroicon-o-document-arrow-down')
                    ->color('primary')
                    ->action(function () {
                        Notification::make()
                            ->title('Export PDF')
                            ->body('Fitur export PDF akan segera tersedia')
                            ->success()
                            ->send();
                    })
                    ->visible(fn() => $this->account_id && $this->start_date && $this->end_date),
            ]);
    }

    protected function getTableQuery(): Builder
    {
        $query = JournalEntry::query()->with(['journal', 'account']);

        if (!$this->account_id || !$this->start_date || !$this->end_date) {
            return $query->whereRaw('1 = 0');
        }

        return $query
            ->where('account_id', $this->account_id)
            ->whereHas('journal', function (Builder $query) {
                $query->where('status', 'Posted')
                    ->whereBetween('transaction_date', [
                        Carbon::parse($this->start_date)->startOfDay(),
                        Carbon::parse($this->end_date)->endOfDay(),
                    ]);
            });
    }

    public function getSelectedAccount()
    {
        if (!$this->account_id) {
            return null;
        }

        // Cache the selected account to avoid multiple queries
        static $cachedAccount = null;
        static $cachedAccountId = null;

        if ($cachedAccountId !== $this->account_id) {
            $cachedAccount = Akun::find($this->account_id);
            $cachedAccountId = $this->account_id;
        }

        return $cachedAccount;
    }

    public function getAccountSummary(): array
    {
        $selectedAccount = $this->getSelectedAccount();
        if (!$selectedAccount) {
            return [];
        }

        $entries = $this->getTableQuery()->get();
        $totalDebit = $entries->sum('debit');
        $totalCredit = $entries->sum('credit');

        // Calculate opening balance
        $openingBalance = $selectedAccount->saldo_awal ?? 0;
        $entriesBeforeStartDate = JournalEntry::with(['journal', 'account'])
            ->where('account_id', $this->account_id)
            ->whereHas('journal', function (Builder $query) {
                $query->where('status', 'Posted')
                    ->where('transaction_date', '<', Carbon::parse($this->start_date)->startOfDay());
            })
            ->get();

        foreach ($entriesBeforeStartDate as $entry) {
            if ($selectedAccount->tipe_akun === 'Debit') {
                $openingBalance += $entry->debit - $entry->credit;
            } else {
                $openingBalance += $entry->credit - $entry->debit;
            }
        }

        // Calculate ending balance
        if ($selectedAccount->tipe_akun === 'Debit') {
            $endingBalance = $openingBalance + $totalDebit - $totalCredit;
        } else {
            $endingBalance = $openingBalance + $totalCredit - $totalDebit;
        }

        return [
            'account' => $selectedAccount,
            'opening_balance' => $openingBalance,
            'total_debit' => $totalDebit,
            'total_credit' => $totalCredit,
            'ending_balance' => $endingBalance,
        ];
    }

    // Livewire property watchers
    public function updatedAccountId()
    {
        $this->resetTable();
        $this->dispatch('$refresh');
    }

    public function updatedStartDate()
    {
        $this->resetTable();
        $this->dispatch('$refresh');
    }

    public function updatedEndDate()
    {
        $this->resetTable();
        $this->dispatch('$refresh');
    }

    // Override resetTable to ensure proper refresh
    public function resetTable(): void
    {
        // Force table to refresh by clearing any cached data
        $this->cachedTableQuery = null;
    }

    // Add property to cache table query
    protected $cachedTableQuery = null;

    public function refreshTable()
    {
        $this->cachedTableQuery = null;
        // Force table to refresh by dispatching event
        $this->dispatch('refreshTable');
    }
}
