<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use App\Jobs\ProcessSalesImport;
use App\Models\Warehouse;
use Illuminate\Support\Facades\Auth;

class ImportSales extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-up-tray';

    protected static ?string $navigationLabel = 'Import Penjualan POS';

    protected static ?string $title = 'Import Data Penjualan dari POS';

    protected static ?string $navigationGroup = 'Akuntansi';

    protected static ?int $navigationSort = 40;

    protected static string $view = 'filament.pages.import-sales';

    /**
     * Check if user can access this Import Sales page
     */
    public static function canAccess(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Allow super admin and direktur (they have all permissions)
        if ($user->hasRole(['super_admin', 'direktur'])) {
            return true;
        }

        // Allow manager_accounting role
        if ($user->hasRole('manager_accounting')) {
            return true;
        }
        return false;
    }

    /**
     * Control navigation visibility based on permissions
     */
    public static function shouldRegisterNavigation(): bool
    {
        return static::canAccess();
    }

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'warehouse_id' => null,
            'sales_file' => null,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Import Data Penjualan POS')
                    ->description('Upload file CSV atau Excel yang berisi data penjualan dari sistem POS untuk diproses secara otomatis')
                    ->schema([
                        Forms\Components\Select::make('warehouse_id')
                            ->label('Gudang Asal')
                            ->options(Warehouse::where('is_active', true)->pluck('name', 'id'))
                            ->required()
                            ->searchable()
                            ->placeholder('Pilih gudang asal penjualan')
                            ->helperText('Pilih gudang dari mana barang dijual'),

                        Forms\Components\FileUpload::make('sales_file')
                            ->label('File Data Penjualan')
                            ->required()
                            ->acceptedFileTypes(['.csv', '.xlsx', '.xls'])
                            ->maxSize(10240) // 10MB
                            ->directory('sales-imports')
                            ->preserveFilenames()
                            ->helperText('Upload file CSV atau Excel dengan format yang sesuai. Maksimal 10MB.')
                            ->columnSpanFull(),

                        Forms\Components\Placeholder::make('format_info')
                            ->label('Format File yang Diharapkan')
                            ->content(view('filament.components.sales-import-format'))
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ])
            ->statePath('data');
    }

    public function import(): void
    {
        $data = $this->form->getState();

        if (!$data['sales_file'] || !$data['warehouse_id']) {
            Notification::make()
                ->title('Error')
                ->body('Harap pilih gudang dan upload file terlebih dahulu.')
                ->danger()
                ->send();
            return;
        }

        try {
            // Dispatch job untuk memproses import
            ProcessSalesImport::dispatch(
                $data['sales_file'],
                $data['warehouse_id'],
                Auth::id()
            );

            Notification::make()
                ->title('Import Dimulai')
                ->body('File sedang diproses. Anda akan mendapat notifikasi setelah selesai.')
                ->success()
                ->send();

            // Reset form
            $this->form->fill([
                'warehouse_id' => null,
                'sales_file' => null,
            ]);
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('Terjadi kesalahan: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
