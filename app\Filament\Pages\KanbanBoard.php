<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Models\Project;
use App\Models\Task;
use App\Models\User;
use Illuminate\Support\Collection;
use Livewire\Attributes\On;
use Filament\Notifications\Notification;
use Filament\Actions\Action;
use App\Filament\Resources\TaskResource;
use App\Models\ProjectActivity;
use Illuminate\Support\Facades\Auth;

class KanbanBoard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';

    protected static string $view = 'filament.pages.modern-kanban-board';

    protected static ?string $title = 'Papan Kanban';

    protected static ?string $navigationLabel = 'Papan Kanban';

    protected static ?string $navigationGroup = 'Manajemen Kegiatan';

    protected static ?int $navigationSort = 2;

    protected static ?string $slug = 'papan-kanban';

    /**
     * Check if user can access this Kanban Board page
     */
    public static function canAccess(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Allow super admin and direktur (they have all permissions)
        if ($user->hasRole(['super_admin', 'direktur'])) {
            return true;
        }

        // Allow manager_hrd role (project management)
        if ($user->hasRole('manager_hrd')) {
            return true;
        }
                return false;

    }

    /**
     * Control navigation visibility based on permissions
     */
    public static function shouldRegisterNavigation(): bool
    {
        return static::canAccess();
    }

    public ?Project $selectedProject = null;
    public Collection $projects;
    public Collection $taskStatuses;
    public Collection $teamMembers;
    public ?Task $selectedTask = null;
    public ?int $selectedProjectId = null;

    // Filter properties
    public ?int $filterAssignee = null;
    public ?string $filterDueDate = null;
    public ?string $filterPriority = null;
    public string $searchTerm = '';
    public string $swimlaneBy = 'none'; // none, assignee, priority
    public bool $showWipLimits = true;
    public bool $showCompletedTasks = true;

    // WIP Limits
    public array $wipLimits = [
        'todo' => 10,
        'in_progress' => 5,
        'completed' => 999,
    ];

    public function mount($project_id = null): void
    {
        $this->projects = Project::all();
        $this->teamMembers = User::whereHas('assignedTasks')->get();

        if ($project_id && $this->projects->contains('id', $project_id)) {
            $this->selectedProjectId = (int) $project_id;
            $this->selectedProject = Project::find($project_id);
            $this->loadTaskStatuses();
        } elseif ($this->projects->isNotEmpty() && !is_null($project_id)) {
            Notification::make()
                ->title('Proyek Tidak Ditemukan')
                ->danger()
                ->send();
            $this->redirect(static::getUrl());
        }
    }

    public function selectProject($projectId): void
    {
        $this->selectedTask = null;

        if ($projectId === null) {
            $this->selectedProjectId = null;
            $this->selectedProject = null;
            $this->taskStatuses = collect();
            return;
        }

        $this->selectedProjectId = (int) $projectId;
        $this->selectedProject = Project::find($projectId);

        if ($this->selectedProject) {
            $this->loadTaskStatuses();
        }
    }

    public function updatedSelectedProjectId($value): void
    {
        if ($value) {
            $this->selectProject((int) $value);
        } else {
            $this->selectedProject = null;
            $this->taskStatuses = collect();
        }
    }

    public function loadTaskStatuses(): void
    {
        if (!$this->selectedProject) {
            $this->taskStatuses = collect();
            return;
        }

        $statusDefinitions = [
            ['value' => 'todo', 'name' => 'To Do', 'color' => '#6b7280'],
            ['value' => 'in_progress', 'name' => 'In Progress', 'color' => '#f59e0b'],
            ['value' => 'completed', 'name' => 'Completed', 'color' => '#10b981'],
        ];

        $this->taskStatuses = collect($statusDefinitions)->map(function ($status) {
            $tasksQuery = Task::where('project_id', $this->selectedProject->id)
                ->where('status', $status['value'])
                ->with(['assignedUser', 'project'])
                ->withCount(['comments']);

            // Apply filters
            if ($this->filterAssignee) {
                $tasksQuery->where('assigned_to', $this->filterAssignee);
            }

            if ($this->filterDueDate) {
                switch ($this->filterDueDate) {
                    case 'overdue':
                        $tasksQuery->where('due_date', '<', now())->whereNotNull('due_date');
                        break;
                    case 'today':
                        $tasksQuery->whereDate('due_date', today());
                        break;
                    case 'this_week':
                        $tasksQuery->whereBetween('due_date', [now()->startOfWeek(), now()->endOfWeek()]);
                        break;
                    case 'no_due_date':
                        $tasksQuery->whereNull('due_date');
                        break;
                }
            }

            if ($this->searchTerm) {
                $tasksQuery->where(function ($query) {
                    $query->where('name', 'like', '%' . $this->searchTerm . '%')
                        ->orWhere('description', 'like', '%' . $this->searchTerm . '%');
                });
            }

            $tasks = $tasksQuery->orderBy('created_at', 'desc')->get();

            // Calculate WIP status
            $wipLimit = $this->wipLimits[$status['value']] ?? 999;
            $currentCount = $tasks->count();
            $wipStatus = $currentCount >= $wipLimit ? 'exceeded' : 'normal';

            return (object) [
                'id' => $status['value'],
                'name' => $status['name'],
                'color' => $status['color'],
                'tasks' => $tasks,
                'wip_limit' => $wipLimit,
                'current_count' => $currentCount,
                'wip_status' => $wipStatus,
            ];
        });
    }

    public function clearFilters(): void
    {
        $this->filterAssignee = null;
        $this->filterDueDate = null;
        $this->filterPriority = null;
        $this->searchTerm = '';
        $this->showCompletedTasks = true;
        $this->loadTaskStatuses();
    }

    public function getFilteredTasks(): Collection
    {
        if (!$this->selectedProject) {
            return collect();
        }

        $query = $this->selectedProject->tasks()
            ->with(['assignedUser', 'project'])
            ->withCount(['comments']);

        // Apply filters
        if ($this->filterAssignee) {
            $query->where('assigned_to', $this->filterAssignee);
        }

        if ($this->searchTerm) {
            $query->where(function ($q) {
                $q->where('name', 'like', "%{$this->searchTerm}%")
                    ->orWhere('description', 'like', "%{$this->searchTerm}%");
            });
        }

        if (!$this->showCompletedTasks) {
            $query->where('status', '!=', 'completed');
        }

        return $query->get();
    }

    public function getTasksByStatus(): Collection
    {
        $tasks = $this->getFilteredTasks();
        $grouped = collect();

        foreach ($this->taskStatuses as $status) {
            $statusTasks = $tasks->where('status', $status->id);
            $grouped->put($status->id, [
                'status' => $status,
                'tasks' => $statusTasks,
                'count' => $statusTasks->count(),
            ]);
        }

        return $grouped;
    }

    public function moveTask($taskId, $newStatus): void
    {
        $task = Task::find($taskId);

        if ($task && $task->project_id === $this->selectedProject?->id) {
            // Check WIP limits
            $newStatusColumn = $this->taskStatuses->firstWhere('id', $newStatus);
            if ($newStatusColumn && $this->showWipLimits) {
                $wipLimit = $this->wipLimits[$newStatus] ?? 999;
                if ($newStatusColumn->current_count >= $wipLimit) {
                    Notification::make()
                        ->title('WIP Limit Exceeded')
                        ->body("Cannot move task. {$newStatusColumn->name} column has reached its WIP limit of {$wipLimit}.")
                        ->warning()
                        ->send();
                    return;
                }
            }

            $oldStatus = $task->status;
            $task->update(['status' => $newStatus]);
            $this->loadTaskStatuses();
            $this->dispatch('task-updated');

            Notification::make()
                ->title('Task Updated')
                ->success()
                ->send();

            // log projectActivity
            $userName = auth()->user()?->name ?? 'System';
            \App\Models\ProjectActivity::log(
                'task_updated',
                $task,
                $userName . " moved task '{$task->name}' from {$oldStatus} to {$newStatus}"
            );
        }
    }

    #[On('refresh-board')]
    public function refreshBoard(): void
    {
        $this->loadTaskStatuses();
        $this->dispatch('task-updated');
    }

    public function updateWipLimit(string $status, int $limit): void
    {
        $this->wipLimits[$status] = $limit;
        $this->loadTaskStatuses();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('new_task')
                ->label('Tugas Baru')
                ->icon('heroicon-m-plus')
                ->visible(fn() => $this->selectedProject !== null)
                ->url(fn(): string => TaskResource::getUrl('create', [
                    'project_id' => $this->selectedProject?->id,
                ])),

            Action::make('refresh_board')
                ->label('Refresh')
                ->icon('heroicon-m-arrow-path')
                ->action('refreshBoard')
                ->color('gray'),

            Action::make('toggle_wip')
                ->label($this->showWipLimits ? 'Sembunyikan Batas WIP' : 'Tampilkan Batas WIP')
                ->icon('heroicon-m-chart-bar')
                ->action(function () {
                    $this->showWipLimits = !$this->showWipLimits;
                    $this->loadTaskStatuses();
                })
                ->color('warning'),
        ];
    }

    public function getFilteredTasksByAssignee(): Collection
    {
        if ($this->swimlaneBy !== 'assignee') {
            return collect();
        }

        return $this->teamMembers->map(function ($user) {
            return (object) [
                'user' => $user,
                'statuses' => $this->taskStatuses->map(function ($status) use ($user) {
                    $userTasks = $status->tasks->where('assigned_to', $user->id);
                    return (object) [
                        'id' => $status->id,
                        'name' => $status->name,
                        'color' => $status->color,
                        'tasks' => $userTasks,
                        'wip_limit' => $status->wip_limit,
                        'current_count' => $userTasks->count(),
                        'wip_status' => $userTasks->count() >= $status->wip_limit ? 'exceeded' : 'normal',
                    ];
                }),
            ];
        });
    }

    public function getTasksByStatusProperty(): Collection
    {
        return $this->taskStatuses->map(function ($status) {
            return [
                'status' => (object) [
                    'id' => $status->id,
                    'name' => $status->name,
                    'color' => $status->color,
                ],
                'tasks' => $status->tasks,
                'count' => $status->current_count,
            ];
        });
    }

    public function editTask($taskId): void
    {
        $this->redirect(TaskResource::getUrl('edit', ['record' => $taskId]));
    }

    public function viewTask($taskId): void
    {
        $this->redirect(TaskResource::getUrl('view', ['record' => $taskId]));
    }

    public function getViewData(): array
    {
        return [
            'projects' => $this->projects,
            'selectedProject' => $this->selectedProject,
            'tasksByStatus' => $this->selectedProject ? $this->getTasksByStatus() : collect(),
            'teamMembers' => $this->teamMembers,
            'filters' => [
                'assignee' => $this->filterAssignee,
                'priority' => $this->filterPriority,
                'dueDate' => $this->filterDueDate,
                'search' => $this->searchTerm,
            ],
            'viewOptions' => [
                'swimlaneBy' => $this->swimlaneBy,
                'showCompleted' => $this->showCompletedTasks,
            ],
        ];
    }
}
