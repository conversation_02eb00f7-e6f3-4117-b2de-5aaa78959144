<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Models\Objective;
use App\Models\KeyResult;
use App\Models\Tactic;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class OkrAnalytics extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationGroup = 'OKR Management';

    protected static ?string $navigationLabel = 'OKR Analytics';

    protected static ?string $title = 'OKR Analytics & Reporting';

    protected static ?int $navigationSort = 2;

    protected static string $view = 'filament.pages.okr-analytics';

    public $selectedPeriod = 'current_quarter';
    public $selectedDepartment = null;

    // Access control
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return in_array($user->role, ['admin', 'supervisor']);
    }

    public function mount(): void
    {
        $this->selectedPeriod = 'current_quarter';
    }

    public function getViewData(): array
    {
        $user = Auth::user();

        // Date range based on selected period
        $dateRange = $this->getDateRange($this->selectedPeriod);

        // Base query for objectives
        $objectivesQuery = Objective::with(['keyResults', 'tactics', 'departemen', 'divisi'])
            ->whereBetween('periode_mulai', [$dateRange['start'], $dateRange['end']])
            ->when(!in_array($user->role, ['admin', 'supervisor']), function ($query) use ($user) {
                $query->where('owner_id', $user->id);
            })
            ->when($this->selectedDepartment, function ($query) {
                $query->where('departemen_id', $this->selectedDepartment);
            });

        $objectives = $objectivesQuery->get();

        // Progress over time data
        $progressOverTime = $this->getProgressOverTime($objectives);

        // Achievement rates
        $achievementRates = $this->getAchievementRates($objectives);

        // Bottleneck analysis
        $bottlenecks = $this->getBottleneckAnalysis($objectives);

        // Department performance
        $departmentPerformance = $this->getDepartmentPerformance();

        // Key metrics
        $keyMetrics = $this->getKeyMetrics($objectives);

        // Trend analysis
        $trends = $this->getTrendAnalysis();

        return [
            'objectives' => $objectives,
            'progressOverTime' => $progressOverTime,
            'achievementRates' => $achievementRates,
            'bottlenecks' => $bottlenecks,
            'departmentPerformance' => $departmentPerformance,
            'keyMetrics' => $keyMetrics,
            'trends' => $trends,
            'selectedPeriod' => $this->selectedPeriod,
            'selectedDepartment' => $this->selectedDepartment,
        ];
    }

    private function getDateRange($period): array
    {
        return match ($period) {
            'current_quarter' => [
                'start' => Carbon::now()->startOfQuarter(),
                'end' => Carbon::now()->endOfQuarter(),
            ],
            'last_quarter' => [
                'start' => Carbon::now()->subQuarter()->startOfQuarter(),
                'end' => Carbon::now()->subQuarter()->endOfQuarter(),
            ],
            'current_year' => [
                'start' => Carbon::now()->startOfYear(),
                'end' => Carbon::now()->endOfYear(),
            ],
            'last_year' => [
                'start' => Carbon::now()->subYear()->startOfYear(),
                'end' => Carbon::now()->subYear()->endOfYear(),
            ],
            default => [
                'start' => Carbon::now()->startOfQuarter(),
                'end' => Carbon::now()->endOfQuarter(),
            ],
        };
    }

    private function getProgressOverTime($objectives): array
    {
        // Simulate progress over time data
        $data = [];
        $startDate = Carbon::now()->startOfQuarter();
        $endDate = Carbon::now();

        $current = $startDate->copy();
        while ($current <= $endDate) {
            $weekProgress = $objectives->where('created_at', '<=', $current)->avg('progress_percentage') ?? 0;
            $data[] = [
                'date' => $current->format('Y-m-d'),
                'progress' => round($weekProgress, 1),
            ];
            $current->addWeek();
        }

        return $data;
    }

    private function getAchievementRates($objectives): array
    {
        $total = $objectives->count();
        if ($total === 0) return [];

        return [
            'completed' => [
                'count' => $objectives->where('status', 'completed')->count(),
                'percentage' => round(($objectives->where('status', 'completed')->count() / $total) * 100, 1),
            ],
            'on_track' => [
                'count' => $objectives->where('progress_percentage', '>=', 70)->where('status', '!=', 'completed')->count(),
                'percentage' => round(($objectives->where('progress_percentage', '>=', 70)->where('status', '!=', 'completed')->count() / $total) * 100, 1),
            ],
            'at_risk' => [
                'count' => $objectives->where('progress_percentage', '<', 70)->where('status', '!=', 'completed')->count(),
                'percentage' => round(($objectives->where('progress_percentage', '<', 70)->where('status', '!=', 'completed')->count() / $total) * 100, 1),
            ],
        ];
    }

    private function getBottleneckAnalysis($objectives): array
    {
        $bottlenecks = [];

        // Analyze key results that are at risk
        $atRiskKeyResults = KeyResult::whereIn('objective_id', $objectives->pluck('id'))
            ->where('status', 'at_risk')
            ->with('objective')
            ->get();

        // Analyze blocked tactics
        $blockedTactics = Tactic::whereIn('objective_id', $objectives->pluck('id'))
            ->where('status', 'blocked')
            ->with('objective')
            ->get();

        // Analyze overdue objectives
        $overdueObjectives = $objectives->filter(function ($objective) {
            return $objective->is_overdue;
        });

        return [
            'at_risk_key_results' => $atRiskKeyResults,
            'blocked_tactics' => $blockedTactics,
            'overdue_objectives' => $overdueObjectives,
        ];
    }

    private function getDepartmentPerformance(): array
    {
        return DB::table('objectives')
            ->join('departemen', 'objectives.departemen_id', '=', 'departemen.id')
            ->select(
                'departemen.nama_departemen',
                DB::raw('COUNT(*) as total_objectives'),
                DB::raw('AVG(objectives.progress_percentage) as avg_progress'),
                DB::raw('COUNT(CASE WHEN objectives.status = "completed" THEN 1 END) as completed_objectives')
            )
            ->groupBy('departemen.id', 'departemen.nama_departemen')
            ->get()
            ->map(function ($item) {
                return [
                    'department' => $item->nama_departemen,
                    'total_objectives' => $item->total_objectives,
                    'avg_progress' => round($item->avg_progress, 1),
                    'completed_objectives' => $item->completed_objectives,
                    'completion_rate' => round(($item->completed_objectives / $item->total_objectives) * 100, 1),
                ];
            })
            ->toArray();
    }

    private function getKeyMetrics($objectives): array
    {
        $keyResults = KeyResult::whereIn('objective_id', $objectives->pluck('id'))->get();
        $tactics = Tactic::whereIn('objective_id', $objectives->pluck('id'))->get();

        return [
            'total_objectives' => $objectives->count(),
            'avg_progress' => round($objectives->avg('progress_percentage'), 1),
            'total_key_results' => $keyResults->count(),
            'completed_key_results' => $keyResults->where('status', 'completed')->count(),
            'total_tactics' => $tactics->count(),
            'completed_tactics' => $tactics->where('status', 'completed')->count(),
            'objectives_on_time' => $objectives->filter(function ($obj) {
                return !$obj->is_overdue && $obj->status !== 'completed';
            })->count(),
        ];
    }

    private function getTrendAnalysis(): array
    {
        // Compare current quarter with previous quarter
        $currentQuarter = Objective::whereBetween('periode_mulai', [
            Carbon::now()->startOfQuarter(),
            Carbon::now()->endOfQuarter()
        ])->avg('progress_percentage') ?? 0;

        $previousQuarter = Objective::whereBetween('periode_mulai', [
            Carbon::now()->subQuarter()->startOfQuarter(),
            Carbon::now()->subQuarter()->endOfQuarter()
        ])->avg('progress_percentage') ?? 0;

        $trend = $currentQuarter - $previousQuarter;

        return [
            'current_quarter_progress' => round($currentQuarter, 1),
            'previous_quarter_progress' => round($previousQuarter, 1),
            'trend' => round($trend, 1),
            'trend_direction' => $trend > 0 ? 'up' : ($trend < 0 ? 'down' : 'stable'),
        ];
    }

    public function updatedSelectedPeriod(): void
    {
        // This will trigger a re-render when the period changes
    }

    public function updatedSelectedDepartment(): void
    {
        // This will trigger a re-render when the department changes
    }
}
