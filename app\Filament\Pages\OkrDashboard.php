<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Objective;
use App\Models\KeyResult;
use App\Models\Tactic;
use Illuminate\Support\Facades\Auth;

class OkrDashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-flag';

    protected static ?string $navigationGroup = 'OKR Management';

    protected static ?string $navigationLabel = 'OKR Dashboard';

    protected static ?string $title = 'OKR Dashboard';

    protected static ?int $navigationSort = 0;

    protected static string $view = 'filament.pages.okr-dashboard';

    // Access control
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return in_array($user->role, ['admin', 'supervisor']);
    }

    protected function getHeaderWidgets(): array
    {
        return [
            \App\Filament\Widgets\OkrPerformanceWidget::class,
            \App\Filament\Widgets\OkrStatsWidget::class,
            \App\Filament\Widgets\OkrPerformanceComparisonWidget::class,
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            \App\Filament\Widgets\OkrDepartemenPerformanceWidget::class,
            \App\Filament\Widgets\OkrDivisiPerformanceWidget::class,
            \App\Filament\Widgets\OkrTrendAnalysisWidget::class,
            \App\Filament\Widgets\OkrLeaderboardWidget::class,
            \App\Filament\Widgets\OkrDepartemenTableWidget::class,
            \App\Filament\Widgets\OkrQuickActionsWidget::class,
            \App\Filament\Widgets\OkrAttentionWidget::class,
        ];
    }

    public function getViewData(): array
    {
        $user = Auth::user();

        // Get objectives data
        $objectives = Objective::with(['keyResults', 'tactics'])
            ->when(!in_array($user->role, ['admin', 'supervisor']), function ($query) use ($user) {
                $query->where('owner_id', $user->id);
            })
            ->get();

        // Calculate statistics
        $totalObjectives = $objectives->count();
        $activeObjectives = $objectives->where('status', 'active')->count();
        $completedObjectives = $objectives->where('status', 'completed')->count();
        $overallProgress = $objectives->avg('progress_percentage') ?? 0;

        // Get recent activities
        $recentObjectives = $objectives->sortByDesc('updated_at')->take(5);

        // Get objectives by status
        $objectivesByStatus = $objectives->groupBy('status');

        // Get key results summary
        $keyResults = KeyResult::whereIn('objective_id', $objectives->pluck('id'))->get();
        $atRiskKeyResults = $keyResults->where('status', 'at_risk')->count();
        $completedKeyResults = $keyResults->where('status', 'completed')->count();

        // Get tactics summary
        $tactics = Tactic::whereIn('objective_id', $objectives->pluck('id'))->get();
        $blockedTactics = $tactics->where('status', 'blocked')->count();
        $completedTactics = $tactics->where('status', 'completed')->count();

        return [
            'totalObjectives' => $totalObjectives,
            'activeObjectives' => $activeObjectives,
            'completedObjectives' => $completedObjectives,
            'overallProgress' => round($overallProgress),
            'recentObjectives' => $recentObjectives,
            'objectivesByStatus' => $objectivesByStatus,
            'atRiskKeyResults' => $atRiskKeyResults,
            'completedKeyResults' => $completedKeyResults,
            'blockedTactics' => $blockedTactics,
            'completedTactics' => $completedTactics,
            'objectives' => $objectives,
        ];
    }
}

class OkrOverviewWidget extends \Filament\Widgets\StatsOverviewWidget
{
    protected function getStats(): array
    {
        $user = Auth::user();

        // Get objectives data
        $objectives = Objective::when(!in_array($user->role, ['admin', 'supervisor']), function ($query) use ($user) {
            $query->where('owner_id', $user->id);
        })->get();

        $totalObjectives = $objectives->count();
        $activeObjectives = $objectives->where('status', 'active')->count();
        $completedObjectives = $objectives->where('status', 'completed')->count();
        $overallProgress = $objectives->avg('progress_percentage') ?? 0;

        // Get key results data
        $keyResults = KeyResult::whereIn('objective_id', $objectives->pluck('id'))->get();
        $atRiskKeyResults = $keyResults->where('status', 'at_risk')->count();
        $completedKeyResults = $keyResults->where('status', 'completed')->count();

        // Get tactics data
        $tactics = Tactic::whereIn('objective_id', $objectives->pluck('id'))->get();
        $blockedTactics = $tactics->where('status', 'blocked')->count();
        $completedTactics = $tactics->where('status', 'completed')->count();

        return [
            Stat::make('Total Objectives', $totalObjectives)
                ->description($activeObjectives . ' aktif, ' . $completedObjectives . ' selesai')
                ->descriptionIcon('heroicon-m-flag')
                ->color('primary'),

            Stat::make('Overall Progress', round($overallProgress) . '%')
                ->description('Progress keseluruhan OKR')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color($overallProgress >= 80 ? 'success' : ($overallProgress >= 60 ? 'warning' : 'danger')),

            Stat::make('Key Results', $keyResults->count())
                ->description($completedKeyResults . ' selesai, ' . $atRiskKeyResults . ' berisiko')
                ->descriptionIcon('heroicon-m-key')
                ->color('info'),

            Stat::make('Tactics', $tactics->count())
                ->description($completedTactics . ' selesai, ' . $blockedTactics . ' terblokir')
                ->descriptionIcon('heroicon-m-puzzle-piece')
                ->color('warning'),
        ];
    }
}
