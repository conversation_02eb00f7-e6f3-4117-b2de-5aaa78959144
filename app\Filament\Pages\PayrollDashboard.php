<?php

namespace App\Filament\Pages;

use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use App\Traits\HasAdvancedDashboardFilters;
use Illuminate\Support\Facades\Auth;

class PayrollDashboard extends Dashboard implements HasForms
{
    use InteractsWithForms, HasAdvancedDashboardFilters;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';
    protected static ?string $title = 'Payroll & Gaji Dashboard';
    protected static ?string $navigationLabel = 'Payroll Dashboard';
    protected static ?string $navigationGroup = 'Human Resource Management';
    protected static ?int $navigationSort = 3;
    protected static string $routePath = '/payroll-dashboard';
    protected static string $view = 'filament.pages.advanced-dashboard-with-filters';

    /**
     * Check if user can access this Payroll dashboard
     */
    public static function canAccess(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Allow super admin and direktur (they have all permissions)
        if ($user->hasRole(['super_admin', 'direktur'])) {
            return true;
        }

       
        return false;
    }

    /**
     * Control navigation visibility based on permissions
     */
    public static function shouldRegisterNavigation(): bool
    {
        return static::canAccess();
    }

    public function getWidgets(): array
    {
        return [
            \App\Filament\Widgets\PayrollOverviewWidget::class,
            \App\Filament\Widgets\PayrollTrendsWidget::class,
            \App\Filament\Widgets\PayrollByDepartmentWidget::class,
            \App\Filament\Widgets\CompensationBreakdownWidget::class,
            \App\Filament\Widgets\PayrollAlertsWidget::class,
        ];
    }

    public function getColumns(): int | string | array
    {
        return [
            'sm' => 1,
            'md' => 2,
            'lg' => 3,
            'xl' => 4,
        ];
    }
}
