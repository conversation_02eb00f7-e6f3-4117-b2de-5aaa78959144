<?php

namespace App\Filament\Pages;

use Filament\Pages\Dashboard;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use App\Traits\HasAdvancedDashboardFilters;
use Illuminate\Support\Facades\Auth;

class PerformanceDashboard extends Dashboard implements HasForms
{
    use InteractsWithForms, HasAdvancedDashboardFilters;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $title = 'Performance & KPI Dashboard';
    protected static ?string $navigationLabel = 'Performance Dashboard';
    protected static ?string $navigationGroup = 'Human Resource Management';
    protected static ?int $navigationSort = 4;
    protected static string $routePath = '/performance-dashboard';
    protected static string $view = 'filament.pages.advanced-dashboard-with-filters';

    /**
     * Check if user can access this Performance dashboard
     */
    public static function canAccess(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Allow super admin and direktur (they have all permissions)
        if ($user->hasRole(['super_admin', 'direktur'])) {
            return true;
        }

        // Allow manager_hrd role
        if ($user->hasRole('manager_hrd')) {
            return true;
        }
        return false;
    }

    /**
     * Control navigation visibility based on permissions
     */
    public static function shouldRegisterNavigation(): bool
    {
        return static::canAccess();
    }

    public function getWidgets(): array
    {
        return [
            \App\Filament\Widgets\PerformanceOverviewWidget::class,
            \App\Filament\Widgets\PerformanceAnalyticsWidget::class, // Reuse existing widget
        ];
    }

    public function getColumns(): int | string | array
    {
        return [
            'sm' => 1,
            'md' => 2,
            'lg' => 3,
            'xl' => 4,
        ];
    }
}
