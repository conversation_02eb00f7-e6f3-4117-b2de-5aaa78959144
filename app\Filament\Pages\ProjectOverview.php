<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Filament\Widgets\ProjectOverviewStats;
use App\Filament\Widgets\ProjectGanttChart;
use App\Filament\Widgets\ProjectCalendarView;
use App\Filament\Widgets\ProjectViewToggle;
use Illuminate\Support\Facades\Auth;

class ProjectOverview extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static string $view = 'filament.pages.project-overview';

    protected static ?string $title = 'Overview Kegiatan';

    protected static ?string $navigationLabel = 'Overview Kegiatan';

    protected static ?string $navigationGroup = 'Manajemen Kegiatan';

    protected static ?int $navigationSort = 1;

    protected static ?string $slug = 'project-overview';

    /**
     * Check if user can access this Project Overview page
     */
    public static function canAccess(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Allow super admin and direktur (they have all permissions)
        if ($user->hasRole(['super_admin', 'direktur'])) {
            return true;
        }

        // Allow manager_hrd role (project management)
        if ($user->hasRole('manager_hrd')) {
            return true;
        }
        return false;
    }

    /**
     * Control navigation visibility based on permissions
     */
    public static function shouldRegisterNavigation(): bool
    {
        return static::canAccess();
    }


    protected function getHeaderWidgets(): array
    {
        return [
            ProjectOverviewStats::class,
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            ProjectViewToggle::class,
            ProjectGanttChart::class,
            ProjectCalendarView::class,
        ];
    }

    public function getViewData(): array
    {
        return [
            'projects' => \App\Models\Project::with(['tasks', 'members'])
                ->orderBy('start_date')
                ->get(),
        ];
    }
}
