<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Models\Project;
use App\Models\Task;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class Timesheets extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static ?string $title = 'Timesheets';

    protected static ?string $navigationLabel = 'Timesheets';

    protected static ?string $navigationGroup = 'Manajemen Kegiatan';

    protected static ?int $navigationSort = 6;

    protected static ?string $slug = 'timesheets';

    protected static string $view = 'filament.pages.timesheets';

    /**
     * Check if user can access this Timesheets page
     */
    public static function canAccess(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Allow super admin and direktur (they have all permissions)
        if ($user->hasRole(['super_admin'])) {
            return true;
        }

        return false;
    }

    /**
     * Control navigation visibility based on permissions
     */
    public static function shouldRegisterNavigation(): bool
    {
        return static::canAccess();
    }

    public $selectedWeek;
    public $selectedUser;
    public $selectedProject;

    public function mount(): void
    {
        $this->selectedWeek = now()->startOfWeek()->format('Y-m-d');
        $this->selectedUser = auth()->id();
    }

    public function getViewData(): array
    {
        $startOfWeek = Carbon::parse($this->selectedWeek)->startOfWeek();
        $endOfWeek = $startOfWeek->copy()->endOfWeek();

        // Get users for filter
        $users = User::whereHas('assignedTasks')->get();

        // Get projects for filter
        $projects = Project::where('status', 'active')->get();

        // Get tasks for the selected period
        $tasksQuery = Task::with(['project', 'assignedUser'])
            ->whereBetween('created_at', [$startOfWeek, $endOfWeek]);

        if ($this->selectedUser) {
            $tasksQuery->where('assigned_to', $this->selectedUser);
        }

        if ($this->selectedProject) {
            $tasksQuery->where('project_id', $this->selectedProject);
        }

        $tasks = $tasksQuery->get();

        // Generate week days
        $weekDays = [];
        for ($i = 0; $i < 7; $i++) {
            $date = $startOfWeek->copy()->addDays($i);
            $weekDays[] = [
                'date' => $date,
                'day_name' => $date->format('l'),
                'day_short' => $date->format('D'),
                'day_number' => $date->format('j'),
                'is_today' => $date->isToday(),
                'is_weekend' => $date->isWeekend(),
            ];
        }

        // Calculate time entries (mock data for demonstration)
        $timeEntries = $this->generateMockTimeEntries($tasks, $weekDays);

        // Calculate totals
        $totalHours = collect($timeEntries)->sum('total_hours');
        $dailyTotals = [];

        foreach ($weekDays as $day) {
            $dayTotal = collect($timeEntries)->sum(function ($entry) use ($day) {
                return $entry['hours'][$day['date']->format('Y-m-d')] ?? 0;
            });
            $dailyTotals[$day['date']->format('Y-m-d')] = $dayTotal;
        }

        return [
            'weekDays' => $weekDays,
            'timeEntries' => $timeEntries,
            'totalHours' => $totalHours,
            'dailyTotals' => $dailyTotals,
            'users' => $users,
            'projects' => $projects,
            'selectedWeek' => $this->selectedWeek,
            'selectedUser' => $this->selectedUser,
            'selectedProject' => $this->selectedProject,
            'weekRange' => $startOfWeek->format('M j') . ' - ' . $endOfWeek->format('M j, Y'),
        ];
    }

    private function generateMockTimeEntries($tasks, $weekDays): array
    {
        $entries = [];

        foreach ($tasks->take(10) as $task) {
            $hours = [];
            $totalHours = 0;

            foreach ($weekDays as $day) {
                if (!$day['is_weekend'] && rand(0, 1)) {
                    $dayHours = rand(1, 8);
                    $hours[$day['date']->format('Y-m-d')] = $dayHours;
                    $totalHours += $dayHours;
                } else {
                    $hours[$day['date']->format('Y-m-d')] = 0;
                }
            }

            if ($totalHours > 0) {
                $entries[] = [
                    'task_id' => $task->id,
                    'task_name' => $task->name,
                    'project_name' => $task->project->name,
                    'project_color' => $task->project->color ?? '#3B82F6',
                    'hours' => $hours,
                    'total_hours' => $totalHours,
                    'description' => 'Working on ' . $task->name,
                ];
            }
        }

        return $entries;
    }

    public function previousWeek(): void
    {
        $this->selectedWeek = Carbon::parse($this->selectedWeek)->subWeek()->format('Y-m-d');
    }

    public function nextWeek(): void
    {
        $this->selectedWeek = Carbon::parse($this->selectedWeek)->addWeek()->format('Y-m-d');
    }

    public function currentWeek(): void
    {
        $this->selectedWeek = now()->startOfWeek()->format('Y-m-d');
    }

    public function updatedSelectedUser(): void
    {
        // Refresh data when user changes
    }

    public function updatedSelectedProject(): void
    {
        // Refresh data when project changes
    }
}
