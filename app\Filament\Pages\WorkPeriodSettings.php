<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use App\Models\CompanySettings;
use Illuminate\Support\Facades\Auth;

class WorkPeriodSettings extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static ?string $navigationLabel = 'Pengaturan Periode Kerja';

    protected static ?string $title = 'Pengaturan Periode Kerja';

    protected static ?string $navigationGroup = 'Sistem';

    protected static ?int $navigationSort = 1;

    protected static string $view = 'filament.pages.work-period-settings';

    /**
     * Check if user can access this Work Period Settings page
     */
    public static function canAccess(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Allow super admin and direktur (they have all permissions)
        if ($user->hasRole(['super_admin', 'direktur'])) {
            return true;
        }

        // Allow manager_hrd role (system settings)
        if ($user->hasRole('manager_hrd')) {
            return true;
        }
        return false;
    }

    /**
     * Control navigation visibility based on permissions
     */
    public static function shouldRegisterNavigation(): bool
    {
        return static::canAccess();
    }

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'work_period_start' => CompanySettings::get('work_period_start_date', 21),
            'work_period_end' => CompanySettings::get('work_period_end_date', 20),
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Pengaturan Periode Kerja Perusahaan')
                    ->description('Atur hari pertama kerja dan cut-off date untuk periode kerja perusahaan')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('work_period_start')
                                    ->label('Hari Pertama Kerja')
                                    ->helperText('Tanggal mulai periode kerja setiap bulan (1-31)')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(31)
                                    ->required()
                                    ->live()
                                    ->placeholder('Contoh: 21'),

                                TextInput::make('work_period_end')
                                    ->label('Cut-off Date (Hari Terakhir)')
                                    ->helperText('Tanggal akhir periode kerja setiap bulan (1-31)')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(31)
                                    ->required()
                                    ->live()
                                    ->placeholder('Contoh: 20'),
                            ]),

                        Placeholder::make('period_preview')
                            ->label('Preview Periode Kerja')
                            ->content(function ($get) {
                                $startDate = $get('work_period_start') ?: 21;
                                $endDate = $get('work_period_end') ?: 20;

                                $now = now();
                                $currentStart = $now->day >= $startDate ? $now->copy()->day($startDate) : $now->copy()->subMonth()->day($startDate);
                                $currentEnd = $now->day >= $startDate ? $now->copy()->addMonth()->day($endDate) : $now->copy()->day($endDate);

                                return "**Periode Saat Ini:** " . $currentStart->format('d M Y') . " - " . $currentEnd->format('d M Y') . "\n\n" .
                                    "**Pengaturan:** Periode kerja dimulai tanggal **{$startDate}** dan berakhir tanggal **{$endDate}** bulan berikutnya\n\n" .
                                    "**Contoh Periode:**\n" .
                                    "- Periode Juni: {$startDate} Juni - {$endDate} Juli\n" .
                                    "- Periode Juli: {$startDate} Juli - {$endDate} Agustus\n" .
                                    "- Periode Agustus: {$startDate} Agustus - {$endDate} September\n\n" .
                                    "**Dampak Pengaturan:**\n" .
                                    "- Dashboard akan menampilkan data sesuai periode ini\n" .
                                    "- Payroll akan dihitung berdasarkan periode ini\n" .
                                    "- Absensi dan pelanggaran akan diakumulasi per periode ini";
                            }),
                    ]),
            ])
            ->statePath('data');
    }



    public function save(): void
    {
        $data = $this->form->getState();

        // Validate the data
        $this->validate([
            'data.work_period_start' => 'required|integer|min:1|max:31',
            'data.work_period_end' => 'required|integer|min:1|max:31',
        ]);

        CompanySettings::set('work_period_start_date', (int)$data['work_period_start'], 'integer', 'Tanggal mulai periode kerja setiap bulan');
        CompanySettings::set('work_period_end_date', (int)$data['work_period_end'], 'integer', 'Tanggal akhir periode kerja setiap bulan');

        Notification::make()
            ->title('Pengaturan Berhasil Disimpan')
            ->body('Periode kerja perusahaan telah diperbarui. Semua dashboard akan menggunakan pengaturan baru.')
            ->success()
            ->send();

        // Refresh the form with new values
        $this->mount();
    }

    public function resetToDefault(): void
    {
        $this->form->fill([
            'work_period_start' => 21,
            'work_period_end' => 20,
        ]);

        Notification::make()
            ->title('Pengaturan Direset')
            ->body('Pengaturan periode kerja telah direset ke default (21-20). Jangan lupa klik "Simpan Pengaturan" untuk menyimpan perubahan.')
            ->info()
            ->send();
    }
}
