<?php

namespace App\Filament\Pos\Pages;

use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Widgets\AccountWidget;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static string $view = 'filament.pos.pages.dashboard';

    protected static ?string $title = 'POS Dashboard';

    protected static ?string $navigationLabel = 'Dashboard';

    

    public function getWidgets(): array
    {
        return [
            // AccountWidget::class,
            \App\Filament\Pos\Widgets\SalesOverviewWidget::class,
            \App\Filament\Pos\Widgets\RevenueAnalyticsWidget::class,
            \App\Filament\Pos\Widgets\HourlySalesWidget::class,
            \App\Filament\Pos\Widgets\RecentTransactionsWidget::class,
            // \App\Filament\Pos\Widgets\PaymentMethodsWidget::class,
            \App\Filament\Pos\Widgets\TopProductsWidget::class,
            // \App\Filament\Pos\Widgets\LocationPerformanceWidget::class,
            
        ];
    }

    
    public function getColumns(): int | string | array
    {
        return [
            'md' => 6,
            'xl' => 6,
        ];
    }

    public function getHeaderWidgets(): array
    {
        return [
            \App\Filament\Pos\Widgets\WelcomeWidget::class,
        ];
    }
}
