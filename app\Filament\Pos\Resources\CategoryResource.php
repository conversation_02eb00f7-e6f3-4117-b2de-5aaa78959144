<?php

namespace App\Filament\Pos\Resources;

use App\Filament\Pos\Resources\CategoryResource\Pages;
use App\Filament\Pos\Resources\CategoryResource\RelationManagers;
use App\Models\Category;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CategoryResource extends Resource
{
    protected static ?string $model = Category::class;

    protected static ?string $navigationIcon = 'heroicon-o-tag';

    protected static ?string $navigationGroup = 'Product Management';

    protected static ?string $navigationLabel = 'Categories';

    protected static ?string $modelLabel = 'Category';

    protected static ?string $pluralModelLabel = 'Categories';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Category Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Category Name')
                            ->required()
                            ->maxLength(255)
                            ->unique(Category::class, 'name', ignoreRecord: true),

                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->rows(4)
                            ->maxLength(500)
                            ->columnSpanFull(),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Category Name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('description')
                    ->label('Description')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),

                Tables\Columns\TextColumn::make('products_count')
                    ->label('Products Count')
                    ->counts('products')
                    ->sortable()
                    ->alignEnd()
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('total_stock')
                    ->label('Total Stock')
                    ->getStateUsing(function (Category $record): int {
                        return $record->products()->sum('stock_quantity');
                    })
                    ->sortable()
                    ->alignEnd()
                    ->badge()
                    ->color('success'),

                Tables\Columns\TextColumn::make('avg_price')
                    ->label('Avg. Price')
                    ->getStateUsing(function (Category $record): string {
                        $avgPrice = $record->products()->avg('price');
                        return $avgPrice ? 'Rp ' . number_format($avgPrice, 0, ',', '.') : 'N/A';
                    })
                    ->alignEnd()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('has_products')
                    ->label('Has Products')
                    ->query(fn (Builder $query): Builder => $query->has('products')),

                Tables\Filters\Filter::make('no_products')
                    ->label('No Products')
                    ->query(fn (Builder $query): Builder => $query->doesntHave('products')),

                Tables\Filters\Filter::make('products_count')
                    ->form([
                        Forms\Components\TextInput::make('min_products')
                            ->label('Minimum Products')
                            ->numeric()
                            ->minValue(0),
                        Forms\Components\TextInput::make('max_products')
                            ->label('Maximum Products')
                            ->numeric()
                            ->minValue(0),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['min_products'],
                                fn (Builder $query, $count): Builder => $query->has('products', '>=', $count),
                            )
                            ->when(
                                $data['max_products'],
                                fn (Builder $query, $count): Builder => $query->has('products', '<=', $count),
                            );
                    }),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name', 'asc');
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Category Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('name')
                            ->label('Category Name'),
                        Infolists\Components\TextEntry::make('description')
                            ->label('Description')
                            ->placeholder('No description available'),
                    ])
                    ->columns(1),

                Infolists\Components\Section::make('Statistics')
                    ->schema([
                        Infolists\Components\TextEntry::make('products_count')
                            ->label('Total Products')
                            ->getStateUsing(fn (Category $record): int => $record->products()->count())
                            ->badge()
                            ->color('info'),

                        Infolists\Components\TextEntry::make('active_products_count')
                            ->label('Active Products')
                            ->getStateUsing(fn (Category $record): int => $record->products()->where('is_active', true)->count())
                            ->badge()
                            ->color('success'),

                        Infolists\Components\TextEntry::make('total_stock')
                            ->label('Total Stock')
                            ->getStateUsing(fn (Category $record): int => $record->products()->sum('stock_quantity'))
                            ->badge()
                            ->color('warning'),

                        Infolists\Components\TextEntry::make('avg_price')
                            ->label('Average Price')
                            ->getStateUsing(function (Category $record): string {
                                $avgPrice = $record->products()->avg('price');
                                return $avgPrice ? 'Rp ' . number_format($avgPrice, 0, ',', '.') : 'N/A';
                            }),

                        Infolists\Components\TextEntry::make('total_value')
                            ->label('Total Inventory Value')
                            ->getStateUsing(function (Category $record): string {
                                $totalValue = $record->products()->selectRaw('SUM(price * stock_quantity) as total')->value('total');
                                return $totalValue ? 'Rp ' . number_format($totalValue, 0, ',', '.') : 'Rp 0';
                            }),

                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Created At')
                            ->dateTime(),
                    ])
                    ->columns(3),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ProductsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCategories::route('/'),
            'create' => Pages\CreateCategory::route('/create'),
            'view' => Pages\ViewCategory::route('/{record}'),
            'edit' => Pages\EditCategory::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
