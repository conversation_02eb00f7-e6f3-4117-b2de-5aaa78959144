<?php

namespace App\Filament\Pos\Resources\CategoryResource\Pages;

use App\Filament\Pos\Resources\CategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditCategory extends EditRecord
{
    protected static string $resource = CategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->icon('heroicon-o-eye'),
            Actions\DeleteAction::make()
                ->icon('heroicon-o-trash')
                ->before(function () {
                    // Check if category has products
                    if ($this->record->products()->count() > 0) {
                        \Filament\Notifications\Notification::make()
                            ->danger()
                            ->title('Cannot delete category')
                            ->body('This category has products associated with it. Please move or delete the products first.')
                            ->send();
                        
                        $this->halt();
                    }
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Category updated')
            ->body('The category information has been updated successfully.');
    }
}
