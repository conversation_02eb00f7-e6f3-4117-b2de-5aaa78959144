<?php

namespace App\Filament\Pos\Resources\CategoryResource\Pages;

use App\Filament\Pos\Resources\CategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListCategories extends ListRecords
{
    protected static string $resource = CategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->icon('heroicon-o-plus'),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Categories')
                ->badge($this->getModel()::count()),

            'with_products' => Tab::make('With Products')
                ->modifyQueryUsing(fn (Builder $query) => $query->has('products'))
                ->badge($this->getModel()::has('products')->count()),

            'empty' => Tab::make('Empty')
                ->modifyQueryUsing(fn (Builder $query) => $query->doesntHave('products'))
                ->badge($this->getModel()::doesntHave('products')->count()),

            'popular' => Tab::make('Popular (10+ Products)')
                ->modifyQueryUsing(fn (Builder $query) => $query->has('products', '>=', 10))
                ->badge($this->getModel()::has('products', '>=', 10)->count()),
        ];
    }
}
