<?php

namespace App\Filament\Pos\Resources\CategoryResource\Pages;

use App\Filament\Pos\Resources\CategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewCategory extends ViewRecord
{
    protected static string $resource = CategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->icon('heroicon-o-pencil'),
            
            Actions\DeleteAction::make()
                ->icon('heroicon-o-trash')
                ->requiresConfirmation()
                ->modalDescription('Are you sure you want to delete this category? This action cannot be undone.')
                ->before(function () {
                    // Check if category has products
                    if ($this->record->products()->count() > 0) {
                        \Filament\Notifications\Notification::make()
                            ->danger()
                            ->title('Cannot delete category')
                            ->body('This category has products associated with it. Please move or delete the products first.')
                            ->send();
                        
                        $this->halt();
                    }
                }),

            Actions\Action::make('bulk_update_products')
                ->label('Update All Products')
                ->icon('heroicon-o-cube')
                ->color('warning')
                ->visible(fn () => $this->record->products()->count() > 0)
                ->form([
                    \Filament\Forms\Components\Select::make('action')
                        ->label('Action')
                        ->options([
                            'activate' => 'Activate All Products',
                            'deactivate' => 'Deactivate All Products',
                            'move_category' => 'Move to Another Category',
                        ])
                        ->required()
                        ->reactive(),
                    
                    \Filament\Forms\Components\Select::make('new_category_id')
                        ->label('New Category')
                        ->relationship('category', 'name')
                        ->searchable()
                        ->preload()
                        ->visible(fn ($get) => $get('action') === 'move_category')
                        ->required(fn ($get) => $get('action') === 'move_category'),
                ])
                ->action(function (array $data): void {
                    $products = $this->record->products();
                    
                    match ($data['action']) {
                        'activate' => $products->update(['is_active' => true]),
                        'deactivate' => $products->update(['is_active' => false]),
                        'move_category' => $products->update(['category_id' => $data['new_category_id']]),
                    };

                    $count = $products->count();
                    $actionText = match ($data['action']) {
                        'activate' => 'activated',
                        'deactivate' => 'deactivated',
                        'move_category' => 'moved to new category',
                    };

                    \Filament\Notifications\Notification::make()
                        ->success()
                        ->title('Products updated')
                        ->body("{$count} products have been {$actionText}")
                        ->send();
                }),

            Actions\Action::make('export_products')
                ->label('Export Products')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('info')
                ->visible(fn () => $this->record->products()->count() > 0)
                ->action(function (): void {
                    // This would implement CSV export functionality
                    \Filament\Notifications\Notification::make()
                        ->info()
                        ->title('Export started')
                        ->body('Product export will be available soon')
                        ->send();
                }),
        ];
    }
}
