<?php

namespace App\Filament\Pos\Resources;

use App\Filament\Pos\Resources\CustomerResource\Pages;
use App\Filament\Pos\Resources\CustomerResource\RelationManagers;
use App\Models\Customer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Customer Management';

    protected static ?string $navigationLabel = 'Customers';

    protected static ?string $modelLabel = 'Customer';

    protected static ?string $pluralModelLabel = 'Customers';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Customer Information')
                    ->schema([
                        Forms\Components\TextInput::make('nama')
                            ->label('Full Name')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('email')
                            ->label('Email Address')
                            ->email()
                            ->unique(Customer::class, 'email', ignoreRecord: true)
                            ->maxLength(255),

                        Forms\Components\TextInput::make('telepon')
                            ->label('Phone Number')
                            ->tel()
                            ->maxLength(20),

                        Forms\Components\DatePicker::make('tanggal_lahir')
                            ->label('Date of Birth')
                            ->maxDate(now()),

                        Forms\Components\Select::make('jenis_kelamin')
                            ->label('Gender')
                            ->options([
                                'L' => 'Male',
                                'P' => 'Female',
                            ])
                            ->native(false),

                        Forms\Components\Select::make('segment')
                            ->label('Customer Segment')
                            ->options([
                                'top_spenders' => 'Top Spenders',
                                'frequent_buyers' => 'Frequent Buyers',
                                'lapsed_customers' => 'Lapsed Customers',
                                'product_specific_buyers' => 'Product Specific Buyers',
                                'new_customers' => 'New Customers',
                            ])
                            ->native(false),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Address Information')
                    ->schema([
                        Forms\Components\Textarea::make('alamat')
                            ->label('Address')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('detail_address')
                            ->label('Detailed Address')
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('postal_code')
                            ->label('Postal Code')
                            ->maxLength(10),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Business Information')
                    ->schema([
                        Forms\Components\TextInput::make('loyalty_points')
                            ->label('Loyalty Points')
                            ->numeric()
                            ->default(0)
                            ->minValue(0),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active Status')
                            ->default(true),

                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) =>
                $query->select([
                    'id', 'nama', 'email', 'telepon', 'loyalty_points',
                    'is_active', 'created_at'
                ])
            )
            ->columns([
                Tables\Columns\TextColumn::make('nama')
                    ->label('Name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->copyable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('telepon')
                    ->label('Phone')
                    ->searchable()
                    ->copyable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('segment')
                    ->label('Segment')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'top_spenders' => 'success',
                        'frequent_buyers' => 'info',
                        'lapsed_customers' => 'warning',
                        'product_specific_buyers' => 'gray',
                        'new_customers' => 'primary',
                        default => 'gray',
                    })
                    ->toggleable(),

                Tables\Columns\TextColumn::make('loyalty_points')
                    ->label('Loyalty Points')
                    ->numeric()
                    ->sortable()
                    ->alignEnd()
                    ->badge()
                    ->color('success'),

                Tables\Columns\TextColumn::make('posTransactions_count')
                    ->label('Total Orders')
                    ->counts('posTransactions')
                    ->sortable()
                    ->alignEnd()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('total_spent')
                    ->label('Total Spent')
                    ->getStateUsing(function (Customer $record): string {
                        $total = $record->posTransactions()->sum('net_amount');
                        return 'Rp ' . number_format($total, 0, ',', '.');
                    })
                    ->sortable()
                    ->alignEnd()
                    ->toggleable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Registered')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('segment')
                    ->options([
                        'top_spenders' => 'Top Spenders',
                        'frequent_buyers' => 'Frequent Buyers',
                        'lapsed_customers' => 'Lapsed Customers',
                        'product_specific_buyers' => 'Product Specific Buyers',
                        'new_customers' => 'New Customers',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),

                Tables\Filters\Filter::make('loyalty_points')
                    ->form([
                        Forms\Components\TextInput::make('min_points')
                            ->label('Minimum Points')
                            ->numeric(),
                        Forms\Components\TextInput::make('max_points')
                            ->label('Maximum Points')
                            ->numeric(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['min_points'],
                                fn (Builder $query, $value): Builder => $query->where('loyalty_points', '>=', $value),
                            )
                            ->when(
                                $data['max_points'],
                                fn (Builder $query, $value): Builder => $query->where('loyalty_points', '<=', $value),
                            );
                    }),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->defaultPaginationPageOption(25)
            ->paginationPageOptions([10, 25, 50, 100]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Customer Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('nama')
                            ->label('Full Name'),
                        Infolists\Components\TextEntry::make('email')
                            ->label('Email')
                            ->copyable(),
                        Infolists\Components\TextEntry::make('telepon')
                            ->label('Phone')
                            ->copyable(),
                        Infolists\Components\TextEntry::make('tanggal_lahir')
                            ->label('Date of Birth')
                            ->date(),
                        Infolists\Components\TextEntry::make('jenis_kelamin')
                            ->label('Gender')
                            ->formatStateUsing(fn (string $state): string => $state === 'L' ? 'Male' : 'Female'),
                        Infolists\Components\TextEntry::make('segment')
                            ->label('Segment')
                            ->badge(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Business Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('loyalty_points')
                            ->label('Loyalty Points')
                            ->badge()
                            ->color('success'),
                        Infolists\Components\TextEntry::make('posTransactions_count')
                            ->label('Total Orders')
                            ->getStateUsing(fn (Customer $record): int => $record->posTransactions()->count()),
                        Infolists\Components\TextEntry::make('total_spent')
                            ->label('Total Spent')
                            ->getStateUsing(function (Customer $record): string {
                                $total = $record->posTransactions()->sum('net_amount');
                                return 'Rp ' . number_format($total, 0, ',', '.');
                            }),
                        Infolists\Components\IconEntry::make('is_active')
                            ->label('Active Status')
                            ->boolean(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Address')
                    ->schema([
                        Infolists\Components\TextEntry::make('alamat')
                            ->label('Address'),
                        Infolists\Components\TextEntry::make('detail_address')
                            ->label('Detailed Address'),
                        Infolists\Components\TextEntry::make('postal_code')
                            ->label('Postal Code'),
                    ])
                    ->columns(1),

                Infolists\Components\Section::make('Notes')
                    ->schema([
                        Infolists\Components\TextEntry::make('notes')
                            ->label('Notes')
                            ->placeholder('No notes available'),
                    ])
                    ->columns(1),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\PosTransactionsRelationManager::class,
            RelationManagers\LoyaltyTransactionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'view' => Pages\ViewCustomer::route('/{record}'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
