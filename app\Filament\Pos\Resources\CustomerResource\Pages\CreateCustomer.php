<?php

namespace App\Filament\Pos\Resources\CustomerResource\Pages;

use App\Filament\Pos\Resources\CustomerResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateCustomer extends CreateRecord
{
    protected static string $resource = CustomerResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Customer created')
            ->body('The customer has been created successfully.');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set default segment for new customers
        if (empty($data['segment'])) {
            $data['segment'] = 'new_customers';
        }

        // Ensure loyalty points is set
        if (!isset($data['loyalty_points'])) {
            $data['loyalty_points'] = 0;
        }

        // Ensure is_active is set
        if (!isset($data['is_active'])) {
            $data['is_active'] = true;
        }

        return $data;
    }
}
