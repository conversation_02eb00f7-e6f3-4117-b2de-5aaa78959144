<?php

namespace App\Filament\Pos\Resources\CustomerResource\Pages;

use App\Filament\Pos\Resources\CustomerResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListCustomers extends ListRecords
{
    protected static string $resource = CustomerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->icon('heroicon-o-plus'),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Customers')
                ->badge($this->getModel()::count()),

            'active' => Tab::make('Active')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_active', true))
                ->badge($this->getModel()::where('is_active', true)->count()),

            'inactive' => Tab::make('Inactive')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_active', false))
                ->badge($this->getModel()::where('is_active', false)->count()),

            'top_spenders' => Tab::make('Top Spenders')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('segment', 'top_spenders'))
                ->badge($this->getModel()::where('segment', 'top_spenders')->count()),

            'frequent_buyers' => Tab::make('Frequent Buyers')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('segment', 'frequent_buyers'))
                ->badge($this->getModel()::where('segment', 'frequent_buyers')->count()),

            'new_customers' => Tab::make('New Customers')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('segment', 'new_customers'))
                ->badge($this->getModel()::where('segment', 'new_customers')->count()),
        ];
    }
}
