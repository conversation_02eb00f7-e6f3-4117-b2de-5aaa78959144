<?php

namespace App\Filament\Pos\Resources\CustomerResource\Pages;

use App\Filament\Pos\Resources\CustomerResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Components\Actions\Action;

class ViewCustomer extends ViewRecord
{
    protected static string $resource = CustomerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->icon('heroicon-o-pencil'),
            
            Actions\DeleteAction::make()
                ->icon('heroicon-o-trash'),

            Actions\Action::make('add_loyalty_points')
                ->label('Add Loyalty Points')
                ->icon('heroicon-o-plus-circle')
                ->color('success')
                ->form([
                    \Filament\Forms\Components\TextInput::make('points')
                        ->label('Points to Add')
                        ->numeric()
                        ->required()
                        ->minValue(1)
                        ->maxValue(10000),
                    \Filament\Forms\Components\Textarea::make('reason')
                        ->label('Reason')
                        ->required()
                        ->maxLength(255),
                ])
                ->action(function (array $data): void {
                    $this->record->increment('loyalty_points', $data['points']);
                    
                    // Create loyalty transaction record
                    $this->record->loyaltyTransactions()->create([
                        'transaction_type' => 'manual_add',
                        'points' => $data['points'],
                        'description' => $data['reason'],
                        'created_by' => auth()->id(),
                    ]);

                    \Filament\Notifications\Notification::make()
                        ->success()
                        ->title('Loyalty points added')
                        ->body("Added {$data['points']} points to {$this->record->nama}")
                        ->send();
                }),

            Actions\Action::make('deduct_loyalty_points')
                ->label('Deduct Loyalty Points')
                ->icon('heroicon-o-minus-circle')
                ->color('warning')
                ->form([
                    \Filament\Forms\Components\TextInput::make('points')
                        ->label('Points to Deduct')
                        ->numeric()
                        ->required()
                        ->minValue(1)
                        ->maxValue(fn () => $this->record->loyalty_points),
                    \Filament\Forms\Components\Textarea::make('reason')
                        ->label('Reason')
                        ->required()
                        ->maxLength(255),
                ])
                ->action(function (array $data): void {
                    $this->record->decrement('loyalty_points', $data['points']);
                    
                    // Create loyalty transaction record
                    $this->record->loyaltyTransactions()->create([
                        'transaction_type' => 'manual_deduct',
                        'points' => -$data['points'],
                        'description' => $data['reason'],
                        'created_by' => auth()->id(),
                    ]);

                    \Filament\Notifications\Notification::make()
                        ->success()
                        ->title('Loyalty points deducted')
                        ->body("Deducted {$data['points']} points from {$this->record->nama}")
                        ->send();
                })
                ->visible(fn () => $this->record->loyalty_points > 0),
        ];
    }
}
