<?php

namespace App\Filament\Pos\Resources\CustomerResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class LoyaltyTransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'loyaltyTransactions';

    protected static ?string $title = 'Loyalty Points History';

    protected static ?string $icon = 'heroicon-o-star';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('transaction_type')
                    ->label('Transaction Type')
                    ->options([
                        'earned' => 'Points Earned',
                        'redeemed' => 'Points Redeemed',
                        'manual_add' => 'Manual Addition',
                        'manual_deduct' => 'Manual Deduction',
                        'expired' => 'Points Expired',
                    ])
                    ->required(),

                Forms\Components\TextInput::make('points')
                    ->label('Points')
                    ->numeric()
                    ->required(),

                Forms\Components\Textarea::make('description')
                    ->label('Description')
                    ->maxLength(255),

                Forms\Components\TextInput::make('reference_number')
                    ->label('Reference Number')
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('description')
            ->columns([
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date')
                    ->dateTime('M j, Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('transaction_type')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'earned' => 'success',
                        'redeemed' => 'warning',
                        'manual_add' => 'info',
                        'manual_deduct' => 'danger',
                        'expired' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'earned' => 'Earned',
                        'redeemed' => 'Redeemed',
                        'manual_add' => 'Manual Add',
                        'manual_deduct' => 'Manual Deduct',
                        'expired' => 'Expired',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('points')
                    ->label('Points')
                    ->numeric()
                    ->alignEnd()
                    ->color(fn ($state): string => $state > 0 ? 'success' : 'danger')
                    ->formatStateUsing(fn ($state): string => $state > 0 ? "+{$state}" : (string) $state),

                Tables\Columns\TextColumn::make('description')
                    ->label('Description')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),

                Tables\Columns\TextColumn::make('reference_number')
                    ->label('Reference')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('createdBy.name')
                    ->label('Created By')
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('transaction_type')
                    ->options([
                        'earned' => 'Earned',
                        'redeemed' => 'Redeemed',
                        'manual_add' => 'Manual Add',
                        'manual_deduct' => 'Manual Deduct',
                        'expired' => 'Expired',
                    ]),

                Tables\Filters\Filter::make('points_range')
                    ->form([
                        Forms\Components\TextInput::make('min_points')
                            ->label('Minimum Points')
                            ->numeric(),
                        Forms\Components\TextInput::make('max_points')
                            ->label('Maximum Points')
                            ->numeric(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['min_points'],
                                fn (Builder $query, $value): Builder => $query->where('points', '>=', $value),
                            )
                            ->when(
                                $data['max_points'],
                                fn (Builder $query, $value): Builder => $query->where('points', '<=', $value),
                            );
                    }),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('From Date'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Until Date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Add Points Transaction')
                    ->icon('heroicon-o-plus')
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['created_by'] = auth()->id();
                        return $data;
                    })
                    ->after(function ($record): void {
                        // Update customer's loyalty points
                        $customer = $record->customer;
                        if ($record->points > 0) {
                            $customer->increment('loyalty_points', $record->points);
                        } else {
                            $customer->decrement('loyalty_points', abs($record->points));
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => in_array($record->transaction_type, ['manual_add', 'manual_deduct'])),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => in_array($record->transaction_type, ['manual_add', 'manual_deduct']))
                    ->after(function ($record): void {
                        // Reverse the points change
                        $customer = $record->customer;
                        if ($record->points > 0) {
                            $customer->decrement('loyalty_points', $record->points);
                        } else {
                            $customer->increment('loyalty_points', abs($record->points));
                        }
                    }),
            ])
            ->bulkActions([
                // No bulk actions for loyalty transactions
            ])
            ->defaultSort('created_at', 'desc')
            ->paginated([10, 25, 50]);
    }
}
