<?php

namespace App\Filament\Pos\Resources\CustomerResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PosTransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'posTransactions';

    protected static ?string $title = 'Transaction History';

    protected static ?string $icon = 'heroicon-o-shopping-cart';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('transaction_number')
                    ->label('Transaction Number')
                    ->disabled(),
                Forms\Components\DateTimePicker::make('transaction_date')
                    ->label('Transaction Date')
                    ->disabled(),
                Forms\Components\TextInput::make('net_amount')
                    ->label('Total Amount')
                    ->numeric()
                    ->prefix('Rp')
                    ->disabled(),
                Forms\Components\TextInput::make('payment_method')
                    ->label('Payment Method')
                    ->disabled(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('transaction_number')
            ->columns([
                Tables\Columns\TextColumn::make('transaction_number')
                    ->label('Transaction #')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('Date & Time')
                    ->dateTime('M j, Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Payment')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'cash' => 'success',
                        'card' => 'info',
                        'digital_wallet' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('total_amount')
                    ->label('Subtotal')
                    ->money('IDR')
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('discount_amount')
                    ->label('Discount')
                    ->money('IDR')
                    ->alignEnd()
                    ->color('danger'),

                Tables\Columns\TextColumn::make('net_amount')
                    ->label('Total')
                    ->money('IDR')
                    ->alignEnd()
                    ->weight('medium')
                    ->sortable(),

                Tables\Columns\TextColumn::make('loyalty_points_earned')
                    ->label('Points Earned')
                    ->numeric()
                    ->alignEnd()
                    ->badge()
                    ->color('success'),

                Tables\Columns\TextColumn::make('loyalty_points_used')
                    ->label('Points Used')
                    ->numeric()
                    ->alignEnd()
                    ->badge()
                    ->color('warning'),

                Tables\Columns\IconColumn::make('is_offline_transaction')
                    ->label('Sync Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-wifi')
                    ->falseIcon('heroicon-o-cloud')
                    ->trueColor('warning')
                    ->falseColor('success'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('payment_method')
                    ->options([
                        'cash' => 'Cash',
                        'card' => 'Card',
                        'digital_wallet' => 'Digital Wallet',
                    ]),

                Tables\Filters\Filter::make('transaction_date')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('From Date'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Until Date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('transaction_date', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('transaction_date', '<=', $date),
                            );
                    }),

                Tables\Filters\TernaryFilter::make('is_offline_transaction')
                    ->label('Sync Status')
                    ->placeholder('All transactions')
                    ->trueLabel('Offline transactions')
                    ->falseLabel('Online transactions'),
            ])
            ->headerActions([
                // No create action for transactions - they come from POS
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->url(fn ($record) => route('filament.pos.resources.pos-transactions.view', $record))
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                // No bulk actions for transactions
            ])
            ->defaultSort('transaction_date', 'desc')
            ->paginated([10, 25, 50]);
    }
}
