<?php

namespace App\Filament\Pos\Resources;

use App\Filament\Pos\Resources\OutletResource\Pages;
use App\Filament\Pos\Resources\OutletResource\RelationManagers;
use App\Models\Outlet;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class OutletResource extends Resource
{
    protected static ?string $model = Outlet::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-storefront';

    protected static ?string $navigationGroup = 'Outlet Management';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationLabel = 'Outlets';

    protected static ?string $modelLabel = 'Outlet';

    protected static ?string $pluralModelLabel = 'Outlets';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('code')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('type')
                    ->options([
                        'toko' => 'Toko',
                        'restoran' => 'Restoran',
                        'kafe' => 'Kafe',
                        'minimarket' => 'Minimarket',
                        'supermarket' => 'Supermarket',
                    ])
                    ->required(),
                Forms\Components\Select::make('category')
                    ->options([
                        'FnB' => 'Food & Beverage',
                        'VOO' => 'Viera Oleh-Oleh',
                        'Retail' => 'Retail',
                        'Service' => 'Service',
                    ])
                    ->required(),
                Forms\Components\Textarea::make('address')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('city')
                    ->maxLength(255),
                Forms\Components\TextInput::make('phone')
                    ->tel()
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->maxLength(255),
                Forms\Components\TextInput::make('manager_name')
                    ->maxLength(255),
                Forms\Components\DatePicker::make('opening_date'),
                Forms\Components\TimePicker::make('opening_time')
                    ->required()
                    ->default('08:00'),
                Forms\Components\TimePicker::make('closing_time')
                    ->required()
                    ->default('22:00'),
                Forms\Components\TextInput::make('latitude')
                    ->numeric(),
                Forms\Components\TextInput::make('longitude')
                    ->numeric(),
                Forms\Components\TextInput::make('delivery_radius')
                    ->required()
                    ->numeric()
                    ->default(5000),
                Forms\Components\TextInput::make('tax_rate')
                    ->required()
                    ->numeric()
                    ->default(11.00),
                Forms\Components\TextInput::make('service_charge_rate')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\Select::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'maintenance' => 'Under Maintenance',
                        'closed' => 'Closed',
                    ])
                    ->required()
                    ->default('active'),
                Forms\Components\Textarea::make('description')
                    ->columnSpanFull(),
                Forms\Components\Toggle::make('is_active')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) =>
                $query->select([
                    'id', 'name', 'address', 'city', 'phone', 'category',
                    'is_active', 'created_at'
                ])
            )
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Outlet Name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('code')
                    ->label('Code')
                    ->searchable()
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'toko' => 'success',
                        'restoran' => 'warning',
                        'kafe' => 'info',
                        'minimarket' => 'primary',
                        'supermarket' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('city')
                    ->label('City')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('manager_name')
                    ->label('Manager')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'gray',
                        'maintenance' => 'warning',
                        'closed' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'maintenance' => 'Maintenance',
                        'closed' => 'Closed',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('today_revenue')
                    ->label('Today Revenue')
                    ->getStateUsing(function (Outlet $record): string {
                        $revenue = $record->getTodayRevenue();
                        return 'Rp ' . number_format($revenue, 0, ',', '.');
                    })
                    ->alignEnd()
                    ->sortable(),

                Tables\Columns\TextColumn::make('today_transactions')
                    ->label('Transactions')
                    ->getStateUsing(function (Outlet $record): int {
                        return $record->getTodayTransactionCount();
                    })
                    ->alignEnd()
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'toko' => 'Toko',
                        'restoran' => 'Restoran',
                        'kafe' => 'Kafe',
                        'minimarket' => 'Minimarket',
                        'supermarket' => 'Supermarket',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'maintenance' => 'Under Maintenance',
                        'closed' => 'Closed',
                    ]),

                Tables\Filters\SelectFilter::make('category')
                    ->options([
                        'FnB' => 'Food & Beverage',
                        'VOO' => 'Viera Oleh-Oleh',
                        'Retail' => 'Retail',
                        'Service' => 'Service',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->placeholder('All outlets')
                    ->trueLabel('Active outlets')
                    ->falseLabel('Inactive outlets'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->defaultPaginationPageOption(25)
            ->paginationPageOptions([10, 25, 50, 100]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\AssignedUsersRelationManager::class,
            RelationManagers\PosTransactionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOutlets::route('/'),
            'create' => Pages\CreateOutlet::route('/create'),
            'edit' => Pages\EditOutlet::route('/{record}/edit'),
        ];
    }
}
