<?php

namespace App\Filament\Pos\Resources\OutletResource\Pages;

use App\Filament\Pos\Resources\OutletResource;
use App\Filament\Pos\Widgets\OutletAssignmentOverviewWidget;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditOutlet extends EditRecord
{
    protected static string $resource = OutletResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            OutletAssignmentOverviewWidget::class,
        ];
    }
}
