<?php

namespace App\Filament\Pos\Resources\OutletResource\RelationManagers;

use App\Models\User;
use App\Models\OutletUserAssignment;
use App\Services\PosCache;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class AssignedUsersRelationManager extends RelationManager
{
    protected static string $relationship = 'assignedUsers';

    protected static ?string $title = 'Assigned Users';

    protected static ?string $modelLabel = 'User Assignment';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('User')
                    ->options(PosCache::getUserOptionsWithJabatan())
                    ->searchable()
                    ->required()
                    ->preload(),
                
                Forms\Components\Select::make('role')
                    ->label('Role at Outlet')
                    ->options(PosCache::getRoleOptions())
                    ->required(),
                
                Forms\Components\Toggle::make('is_active')
                    ->label('Active')
                    ->default(true),
                
                Forms\Components\DatePicker::make('assigned_from')
                    ->label('Assigned From')
                    ->default(now())
                    ->required(),
                
                Forms\Components\DatePicker::make('assigned_until')
                    ->label('Assigned Until')
                    ->helperText('Leave empty for permanent assignment')
                    ->after('assigned_from'),
                
                Forms\Components\Textarea::make('notes')
                    ->label('Notes')
                    ->maxLength(1000)
                    ->rows(3),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('user.name')
            ->modifyQueryUsing(fn (Builder $query) =>
                $query->with(['userWithKaryawan.karyawan.jabatan'])
                    ->select([
                        'id', 'outlet_id', 'user_id', 'role', 'is_active',
                        'assigned_from', 'assigned_until', 'created_at', 'updated_at'
                    ])
            )
            ->columns([
                Tables\Columns\TextColumn::make('userWithKaryawan.name')
                    ->label('User')
                    ->searchable(['users.name'])
                    ->sortable()
                    ->description(fn ($record) => $record->userWithKaryawan->email ?? ''),

                Tables\Columns\TextColumn::make('user_jabatan')
                    ->label('Jabatan')
                    ->badge()
                    ->color('gray')
                    ->default('N/A')
                    ->getStateUsing(function ($record) {
                        return $record->userWithKaryawan?->karyawan?->jabatan?->nama_jabatan ?? 'N/A';
                    }),

                Tables\Columns\TextColumn::make('role')
                    ->label('Role')
                    ->badge()
                    ->color(fn ($record) => match($record->role) {
                        'kepala toko' => 'success',
                        'kasir' => 'primary',
                        'supervisor' => 'warning',
                        'staff' => 'gray',
                        default => 'gray',
                    })
                    ->searchable(),

                Tables\Columns\TextColumn::make('status_text')
                    ->label('Status')
                    ->badge()
                    ->color(function ($record) {
                        if (!$record->is_active) return 'danger';

                        $now = now();
                        $effective = true;

                        if ($record->assigned_from && $now->lt($record->assigned_from)) {
                            $effective = false;
                        }

                        if ($record->assigned_until && $now->gt($record->assigned_until)) {
                            $effective = false;
                        }

                        return $effective ? 'success' : 'warning';
                    })
                    ->getStateUsing(function ($record) {
                        if (!$record->is_active) return 'Inactive';

                        $now = now();
                        $effective = true;

                        if ($record->assigned_from && $now->lt($record->assigned_from)) {
                            $effective = false;
                        }

                        if ($record->assigned_until && $now->gt($record->assigned_until)) {
                            $effective = false;
                        }

                        return $effective ? 'Active' : 'Scheduled';
                    }),

                Tables\Columns\TextColumn::make('assigned_from')
                    ->label('From')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('assigned_until')
                    ->label('Until')
                    ->date()
                    ->placeholder('Permanent')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->options([
                        'kepala toko' => 'Kepala Toko',
                        'kasir' => 'Kasir',
                        'supervisor' => 'Supervisor',
                        'staff' => 'Staff',
                        'admin' => 'Admin',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),

                Tables\Filters\Filter::make('effective_now')
                    ->label('Currently Effective')
                    ->query(fn (Builder $query): Builder =>
                        $query->where('is_active', true)
                            ->where(function ($q) {
                                $q->whereNull('assigned_from')
                                  ->orWhere('assigned_from', '<=', now());
                            })
                            ->where(function ($q) {
                                $q->whereNull('assigned_until')
                                  ->orWhere('assigned_until', '>=', now());
                            })
                    ),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Assign User')
                    ->modalHeading('Assign User to Outlet')
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['outlet_id'] = $this->ownerRecord->id;
                        return $data;
                    }),

                Tables\Actions\Action::make('bulk_assign')
                    ->label('Bulk Assign Users')
                    ->icon('heroicon-o-users')
                    ->color('info')
                    ->form([
                        Forms\Components\Select::make('users')
                            ->label('Select Users')
                            ->options(PosCache::getUserOptionsWithJabatan())
                            ->multiple()
                            ->searchable()
                            ->preload()
                            ->required()
                            ->helperText('Select multiple users to assign to this outlet'),

                        Forms\Components\Select::make('role')
                            ->label('Role at Outlet')
                            ->options(PosCache::getRoleOptions())
                            ->required()
                            ->helperText('All selected users will be assigned this role'),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),

                        Forms\Components\DatePicker::make('assigned_from')
                            ->label('Assigned From')
                            ->default(now())
                            ->required(),

                        Forms\Components\DatePicker::make('assigned_until')
                            ->label('Assigned Until')
                            ->helperText('Leave empty for permanent assignment')
                            ->after('assigned_from'),

                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->helperText('These notes will be applied to all assignments')
                            ->maxLength(1000)
                            ->rows(3),

                        Forms\Components\Toggle::make('skip_existing')
                            ->label('Skip Existing Assignments')
                            ->default(true)
                            ->helperText('If enabled, users already assigned to this outlet will be skipped'),
                    ])
                    ->action(function (array $data) {
                        $created = 0;
                        $skipped = 0;

                        foreach ($data['users'] as $userId) {
                            if ($data['skip_existing'] ?? true) {
                                $existing = OutletUserAssignment::where('outlet_id', $this->ownerRecord->id)
                                    ->where('user_id', $userId)
                                    ->exists();

                                if ($existing) {
                                    $skipped++;
                                    continue;
                                }
                            }

                            OutletUserAssignment::create([
                                'outlet_id' => $this->ownerRecord->id,
                                'user_id' => $userId,
                                'role' => $data['role'],
                                'is_active' => $data['is_active'] ?? true,
                                'assigned_from' => $data['assigned_from'],
                                'assigned_until' => $data['assigned_until'] ?? null,
                                'notes' => $data['notes'] ?? null,
                            ]);

                            $created++;
                        }

                        Notification::make()
                            ->success()
                            ->title('Bulk Assignment Completed')
                            ->body("Assigned {$created} users to {$this->ownerRecord->name}" . ($skipped > 0 ? ", skipped {$skipped} existing" : ""))
                            ->send();
                    })
                    ->modalHeading('Bulk Assign Users to Outlet')
                    ->modalDescription(fn () => "Assign multiple users to {$this->ownerRecord->name} at once")
                    ->modalSubmitActionLabel('Assign Users')
                    ->requiresConfirmation(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->label('Remove Assignment'),
                
                Tables\Actions\Action::make('toggle_status')
                    ->label(fn ($record) => $record->is_active ? 'Deactivate' : 'Activate')
                    ->icon(fn ($record) => $record->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn ($record) => $record->is_active ? 'danger' : 'success')
                    ->action(function ($record) {
                        $record->update(['is_active' => !$record->is_active]);
                    })
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Remove Assignments'),

                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate Selected')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => true]);

                            Notification::make()
                                ->success()
                                ->title('Assignments Activated')
                                ->body('Selected assignments have been activated.')
                                ->send();
                        })
                        ->requiresConfirmation(),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate Selected')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => false]);

                            Notification::make()
                                ->success()
                                ->title('Assignments Deactivated')
                                ->body('Selected assignments have been deactivated.')
                                ->send();
                        })
                        ->requiresConfirmation(),

                    Tables\Actions\BulkAction::make('update_role')
                        ->label('Update Role')
                        ->icon('heroicon-o-user-circle')
                        ->color('warning')
                        ->form([
                            Forms\Components\Select::make('role')
                                ->label('New Role')
                                ->options([
                                    'kepala toko' => 'Kepala Toko',
                                    'kasir' => 'Kasir',
                                    'supervisor' => 'Supervisor',
                                    'staff' => 'Staff',
                                    'admin' => 'Admin',
                                ])
                                ->required(),
                        ])
                        ->action(function ($records, array $data) {
                            $records->each->update(['role' => $data['role']]);

                            Notification::make()
                                ->success()
                                ->title('Roles Updated')
                                ->body("Updated role to {$data['role']} for selected assignments.")
                                ->send();
                        })
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->defaultPaginationPageOption(25)
            ->paginationPageOptions([10, 25, 50, 100]);
    }

    public function getTabs(): array
    {
        // Cache the counts for better performance
        $outletId = $this->getOwnerRecord()->id;
        $counts = cache()->remember("outlet_assignment_counts_{$outletId}", 60, function () use ($outletId) {
            $base = OutletUserAssignment::where('outlet_id', $outletId);

            return [
                'all' => $base->count(),
                'active' => $base->where('is_active', true)->count(),
                'inactive' => $base->where('is_active', false)->count(),
                'kepala_toko' => $base->where('role', 'kepala toko')->count(),
                'kasir' => $base->where('role', 'kasir')->count(),
                'effective' => $base->where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('assigned_from')
                          ->orWhere('assigned_from', '<=', now());
                    })
                    ->where(function ($q) {
                        $q->whereNull('assigned_until')
                          ->orWhere('assigned_until', '>=', now());
                    })
                    ->count(),
            ];
        });

        return [
            'all' => Tab::make('All Assignments')
                ->badge($counts['all']),

            'active' => Tab::make('Active')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_active', true))
                ->badge($counts['active'])
                ->badgeColor('success'),

            'inactive' => Tab::make('Inactive')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_active', false))
                ->badge($counts['inactive'])
                ->badgeColor('danger'),

            'kepala_toko' => Tab::make('Kepala Toko')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('role', 'kepala toko'))
                ->badge($counts['kepala_toko'])
                ->badgeColor('success'),

            'kasir' => Tab::make('Kasir')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('role', 'kasir'))
                ->badge($counts['kasir'])
                ->badgeColor('primary'),

            'effective' => Tab::make('Currently Effective')
                ->modifyQueryUsing(fn (Builder $query) =>
                    $query->where('is_active', true)
                        ->where(function ($q) {
                            $q->whereNull('assigned_from')
                              ->orWhere('assigned_from', '<=', now());
                        })
                        ->where(function ($q) {
                            $q->whereNull('assigned_until')
                              ->orWhere('assigned_until', '>=', now());
                        })
                )
                ->badge($counts['effective'])
                ->badgeColor('warning'),
        ];
    }
}
