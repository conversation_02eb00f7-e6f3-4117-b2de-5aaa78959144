<?php

namespace App\Filament\Pos\Resources\PosTransactionResource\Pages;

use App\Filament\Pos\Resources\PosTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreatePosTransaction extends CreateRecord
{
    protected static string $resource = PosTransactionResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Transaction created')
            ->body('The transaction has been created successfully.');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Generate transaction number if not provided
        if (empty($data['transaction_number'])) {
            $data['transaction_number'] = $this->generateTransactionNumber();
        }

        // Set default values
        if (!isset($data['discount_amount'])) {
            $data['discount_amount'] = 0;
        }

        if (!isset($data['tax_amount'])) {
            $data['tax_amount'] = 0;
        }

        if (!isset($data['change_given'])) {
            $data['change_given'] = 0;
        }

        if (!isset($data['loyalty_points_used'])) {
            $data['loyalty_points_used'] = 0;
        }

        if (!isset($data['loyalty_points_earned'])) {
            $data['loyalty_points_earned'] = 0;
        }

        if (!isset($data['is_offline_transaction'])) {
            $data['is_offline_transaction'] = false;
        }

        return $data;
    }

    private function generateTransactionNumber(): string
    {
        $prefix = 'TRX';
        $date = now()->format('Ymd');
        
        // Get the last transaction number for today
        $lastTransaction = \App\Models\PosTransaction::whereDate('created_at', today())
            ->where('transaction_number', 'like', "{$prefix}{$date}%")
            ->orderBy('transaction_number', 'desc')
            ->first();

        if ($lastTransaction) {
            // Extract the sequence number and increment
            $lastSequence = (int) substr($lastTransaction->transaction_number, -4);
            $sequence = str_pad($lastSequence + 1, 4, '0', STR_PAD_LEFT);
        } else {
            // First transaction of the day
            $sequence = '0001';
        }

        return $prefix . $date . $sequence;
    }
}
