<?php

namespace App\Filament\Pos\Resources\PosTransactionResource\Pages;

use App\Filament\Pos\Resources\PosTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditPosTransaction extends EditRecord
{
    protected static string $resource = PosTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->icon('heroicon-o-eye'),
            Actions\DeleteAction::make()
                ->icon('heroicon-o-trash'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Transaction updated')
            ->body('The transaction has been updated successfully.');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Ensure required fields have default values
        if (!isset($data['discount_amount'])) {
            $data['discount_amount'] = 0;
        }

        if (!isset($data['tax_amount'])) {
            $data['tax_amount'] = 0;
        }

        if (!isset($data['change_given'])) {
            $data['change_given'] = 0;
        }

        if (!isset($data['loyalty_points_used'])) {
            $data['loyalty_points_used'] = 0;
        }

        if (!isset($data['loyalty_points_earned'])) {
            $data['loyalty_points_earned'] = 0;
        }

        return $data;
    }
}
