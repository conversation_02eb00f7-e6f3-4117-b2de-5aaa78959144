<?php

namespace App\Filament\Pos\Resources\PosTransactionResource\Pages;

use App\Filament\Pos\Resources\PosTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class ListPosTransactions extends ListRecords
{
    protected static string $resource = PosTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->icon('heroicon-o-plus'),
        ];
    }

    public function getTabs(): array
    {
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();

        return [
            'all' => Tab::make('All Transactions')
                ->badge($this->getModel()::count()),

            'today' => Tab::make('Today')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereDate('transaction_date', $today))
                ->badge($this->getModel()::whereDate('transaction_date', $today)->count()),

            'yesterday' => Tab::make('Yesterday')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereDate('transaction_date', $yesterday))
                ->badge($this->getModel()::whereDate('transaction_date', $yesterday)->count()),

            'this_week' => Tab::make('This Week')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('transaction_date', '>=', $thisWeek))
                ->badge($this->getModel()::where('transaction_date', '>=', $thisWeek)->count()),

            'this_month' => Tab::make('This Month')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('transaction_date', '>=', $thisMonth))
                ->badge($this->getModel()::where('transaction_date', '>=', $thisMonth)->count()),

            'cash' => Tab::make('Cash Payments')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('payment_method', 'cash'))
                ->badge($this->getModel()::where('payment_method', 'cash')->count()),

            'card' => Tab::make('Card Payments')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('payment_method', 'card'))
                ->badge($this->getModel()::where('payment_method', 'card')->count()),

            'offline' => Tab::make('Offline Transactions')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_offline_transaction', true))
                ->badge($this->getModel()::where('is_offline_transaction', true)->count()),
        ];
    }
}
