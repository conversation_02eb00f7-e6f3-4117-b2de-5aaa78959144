<?php

namespace App\Filament\Pos\Resources\PosTransactionResource\Pages;

use App\Filament\Pos\Resources\PosTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewPosTransaction extends ViewRecord
{
    protected static string $resource = PosTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->icon('heroicon-o-pencil'),
            
            Actions\DeleteAction::make()
                ->icon('heroicon-o-trash'),

            Actions\Action::make('print_receipt')
                ->label('Print Receipt')
                ->icon('heroicon-o-printer')
                ->color('info')
                ->action(function (): void {
                    // This would implement receipt printing functionality
                    \Filament\Notifications\Notification::make()
                        ->info()
                        ->title('Receipt printing')
                        ->body('Receipt printing functionality will be implemented')
                        ->send();
                }),

            Actions\Action::make('refund')
                ->label('Process Refund')
                ->icon('heroicon-o-arrow-uturn-left')
                ->color('danger')
                ->requiresConfirmation()
                ->modalDescription('Are you sure you want to process a refund for this transaction? This action cannot be undone.')
                ->form([
                    \Filament\Forms\Components\Select::make('refund_type')
                        ->label('Refund Type')
                        ->options([
                            'full' => 'Full Refund',
                            'partial' => 'Partial Refund',
                        ])
                        ->required()
                        ->reactive(),
                    
                    \Filament\Forms\Components\TextInput::make('refund_amount')
                        ->label('Refund Amount')
                        ->numeric()
                        ->prefix('Rp')
                        ->visible(fn ($get) => $get('refund_type') === 'partial')
                        ->required(fn ($get) => $get('refund_type') === 'partial')
                        ->maxValue(fn () => $this->record->net_amount),
                    
                    \Filament\Forms\Components\Textarea::make('refund_reason')
                        ->label('Refund Reason')
                        ->required()
                        ->maxLength(255),
                ])
                ->action(function (array $data): void {
                    $refundAmount = $data['refund_type'] === 'full' 
                        ? $this->record->net_amount 
                        : $data['refund_amount'];

                    // Create refund transaction (this would be implemented)
                    \Filament\Notifications\Notification::make()
                        ->success()
                        ->title('Refund processed')
                        ->body("Refund of Rp " . number_format($refundAmount, 0, ',', '.') . " has been processed")
                        ->send();
                }),

            Actions\Action::make('duplicate')
                ->label('Duplicate Transaction')
                ->icon('heroicon-o-document-duplicate')
                ->color('warning')
                ->requiresConfirmation()
                ->modalDescription('This will create a new transaction with the same items and customer.')
                ->action(function (): void {
                    // Create duplicate transaction
                    $newTransaction = $this->record->replicate();
                    $newTransaction->transaction_number = $this->generateTransactionNumber();
                    $newTransaction->transaction_date = now();
                    $newTransaction->save();

                    // Duplicate transaction items
                    foreach ($this->record->items as $item) {
                        $newItem = $item->replicate();
                        $newItem->pos_transaction_id = $newTransaction->id;
                        $newItem->save();
                    }

                    \Filament\Notifications\Notification::make()
                        ->success()
                        ->title('Transaction duplicated')
                        ->body("New transaction {$newTransaction->transaction_number} created")
                        ->actions([
                            \Filament\Notifications\Actions\Action::make('view')
                                ->button()
                                ->url(PosTransactionResource::getUrl('view', ['record' => $newTransaction])),
                        ])
                        ->send();
                }),

            Actions\Action::make('send_receipt')
                ->label('Send Receipt')
                ->icon('heroicon-o-envelope')
                ->color('success')
                ->visible(fn () => $this->record->customer && $this->record->customer->email)
                ->form([
                    \Filament\Forms\Components\TextInput::make('email')
                        ->label('Email Address')
                        ->email()
                        ->required()
                        ->default(fn () => $this->record->customer?->email),
                    
                    \Filament\Forms\Components\Textarea::make('message')
                        ->label('Additional Message')
                        ->placeholder('Optional message to include with the receipt'),
                ])
                ->action(function (array $data): void {
                    // Send receipt via email (this would be implemented)
                    \Filament\Notifications\Notification::make()
                        ->success()
                        ->title('Receipt sent')
                        ->body("Receipt has been sent to {$data['email']}")
                        ->send();
                }),
        ];
    }

    private function generateTransactionNumber(): string
    {
        $prefix = 'TRX';
        $date = now()->format('Ymd');
        
        // Get the last transaction number for today
        $lastTransaction = \App\Models\PosTransaction::whereDate('created_at', today())
            ->where('transaction_number', 'like', "{$prefix}{$date}%")
            ->orderBy('transaction_number', 'desc')
            ->first();

        if ($lastTransaction) {
            // Extract the sequence number and increment
            $lastSequence = (int) substr($lastTransaction->transaction_number, -4);
            $sequence = str_pad($lastSequence + 1, 4, '0', STR_PAD_LEFT);
        } else {
            // First transaction of the day
            $sequence = '0001';
        }

        return $prefix . $date . $sequence;
    }
}
