<?php

namespace App\Filament\Pos\Resources\PosTransactionResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PosTransactionItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'items';

    protected static ?string $title = 'Transaction Items';

    protected static ?string $icon = 'heroicon-o-shopping-bag';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('product_id')
                    ->label('Product')
                    ->relationship('product', 'name')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if ($state) {
                            $product = \App\Models\Product::find($state);
                            if ($product) {
                                $set('unit_price', $product->price);
                            }
                        }
                    }),

                Forms\Components\TextInput::make('quantity')
                    ->label('Quantity')
                    ->numeric()
                    ->required()
                    ->minValue(1)
                    ->default(1)
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $get, callable $set) {
                        $unitPrice = $get('unit_price') ?? 0;
                        $discount = $get('discount_per_item') ?? 0;
                        $total = ($unitPrice * $state) - ($discount * $state);
                        $set('total_price', $total);
                    }),

                Forms\Components\TextInput::make('unit_price')
                    ->label('Unit Price')
                    ->numeric()
                    ->prefix('Rp')
                    ->required()
                    ->minValue(0)
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $get, callable $set) {
                        $quantity = $get('quantity') ?? 1;
                        $discount = $get('discount_per_item') ?? 0;
                        $total = ($state * $quantity) - ($discount * $quantity);
                        $set('total_price', $total);
                    }),

                Forms\Components\TextInput::make('discount_per_item')
                    ->label('Discount per Item')
                    ->numeric()
                    ->prefix('Rp')
                    ->default(0)
                    ->minValue(0)
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $get, callable $set) {
                        $quantity = $get('quantity') ?? 1;
                        $unitPrice = $get('unit_price') ?? 0;
                        $total = ($unitPrice * $quantity) - ($state * $quantity);
                        $set('total_price', $total);
                    }),

                Forms\Components\TextInput::make('total_price')
                    ->label('Total Price')
                    ->numeric()
                    ->prefix('Rp')
                    ->disabled()
                    ->dehydrated(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('product.name')
            ->columns([
                Tables\Columns\TextColumn::make('product.name')
                    ->label('Product')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('product.sku')
                    ->label('SKU')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('quantity')
                    ->label('Qty')
                    ->numeric()
                    ->alignEnd()
                    ->sortable(),

                Tables\Columns\TextColumn::make('unit_price')
                    ->label('Unit Price')
                    ->money('IDR')
                    ->alignEnd()
                    ->sortable(),

                Tables\Columns\TextColumn::make('discount_per_item')
                    ->label('Discount/Item')
                    ->money('IDR')
                    ->alignEnd()
                    ->color('danger'),

                Tables\Columns\TextColumn::make('total_price')
                    ->label('Total')
                    ->money('IDR')
                    ->alignEnd()
                    ->weight('medium')
                    ->sortable(),

                Tables\Columns\TextColumn::make('product.stock_quantity')
                    ->label('Stock')
                    ->numeric()
                    ->alignEnd()
                    ->badge()
                    ->color(fn ($state): string => match (true) {
                        $state > 50 => 'success',
                        $state > 10 => 'warning',
                        $state > 0 => 'danger',
                        default => 'gray',
                    })
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('product_id')
                    ->label('Product')
                    ->relationship('product', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('quantity_range')
                    ->form([
                        Forms\Components\TextInput::make('min_qty')
                            ->label('Minimum Quantity')
                            ->numeric(),
                        Forms\Components\TextInput::make('max_qty')
                            ->label('Maximum Quantity')
                            ->numeric(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['min_qty'],
                                fn (Builder $query, $qty): Builder => $query->where('quantity', '>=', $qty),
                            )
                            ->when(
                                $data['max_qty'],
                                fn (Builder $query, $qty): Builder => $query->where('quantity', '<=', $qty),
                            );
                    }),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        // Calculate total price if not set
                        if (!isset($data['total_price'])) {
                            $quantity = $data['quantity'] ?? 1;
                            $unitPrice = $data['unit_price'] ?? 0;
                            $discount = $data['discount_per_item'] ?? 0;
                            $data['total_price'] = ($unitPrice * $quantity) - ($discount * $quantity);
                        }
                        return $data;
                    })
                    ->after(function ($record): void {
                        // Update product stock
                        $product = $record->product;
                        if ($product && config('pos.business.inventory.auto_deduct_stock', true)) {
                            $product->decrement('stock_quantity', $record->quantity);
                        }

                        // Recalculate transaction totals
                        $this->recalculateTransactionTotals();
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->after(function ($record): void {
                        // Recalculate transaction totals after edit
                        $this->recalculateTransactionTotals();
                    }),
                Tables\Actions\DeleteAction::make()
                    ->after(function ($record): void {
                        // Return stock to product
                        $product = $record->product;
                        if ($product && config('pos.business.inventory.auto_deduct_stock', true)) {
                            $product->increment('stock_quantity', $record->quantity);
                        }

                        // Recalculate transaction totals
                        $this->recalculateTransactionTotals();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->after(function ($records): void {
                            // Return stock for all deleted items
                            foreach ($records as $record) {
                                $product = $record->product;
                                if ($product && config('pos.business.inventory.auto_deduct_stock', true)) {
                                    $product->increment('stock_quantity', $record->quantity);
                                }
                            }

                            // Recalculate transaction totals
                            $this->recalculateTransactionTotals();
                        }),
                ]),
            ])
            ->defaultSort('id', 'asc');
    }

    private function recalculateTransactionTotals(): void
    {
        $transaction = $this->ownerRecord;
        $items = $transaction->items;

        $totalAmount = $items->sum('total_price');
        $transaction->update([
            'total_amount' => $totalAmount,
            'net_amount' => $totalAmount - $transaction->discount_amount + $transaction->tax_amount,
        ]);
    }
}
