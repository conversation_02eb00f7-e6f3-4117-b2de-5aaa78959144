<?php

namespace App\Filament\Pos\Resources;

use App\Filament\Pos\Resources\PriceListResource\Pages;
use App\Filament\Pos\Resources\PriceListResource\RelationManagers;
use App\Models\PriceList;
use App\Models\Product;
use App\Models\Outlet;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PriceListResource extends Resource
{
    protected static ?string $model = PriceList::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationLabel = 'Price Lists';

    protected static ?string $navigationGroup = 'Outlet Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->label('Price List Name'),

                        Forms\Components\TextInput::make('code')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->label('Price List Code')
                            ->helperText('Unique identifier for this price list'),

                        Forms\Components\Textarea::make('description')
                            ->maxLength(1000)
                            ->label('Description'),
                    ])->columns(2),

                Forms\Components\Section::make('Settings')
                    ->schema([
                        Forms\Components\Toggle::make('is_global')
                            ->label('Global Price List')
                            ->helperText('Global price lists serve as fallback for all outlets'),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ])->columns(2),

                Forms\Components\Section::make('Effective Period')
                    ->schema([
                        Forms\Components\DatePicker::make('effective_from')
                            ->label('Effective From')
                            ->helperText('Leave empty for no start date restriction'),

                        Forms\Components\DatePicker::make('effective_until')
                            ->label('Effective Until')
                            ->helperText('Leave empty for no end date restriction'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) =>
                $query->select([
                    'id', 'name', 'description', 'is_active', 'is_global',
                    'effective_from', 'effective_until', 'created_at'
                ])
            )
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->label('Name'),

                Tables\Columns\TextColumn::make('code')
                    ->searchable()
                    ->sortable()
                    ->label('Code'),

                Tables\Columns\IconColumn::make('is_global')
                    ->boolean()
                    ->label('Global'),

                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->label('Active'),

                Tables\Columns\TextColumn::make('total_products')
                    ->label('Products')
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('effective_period')
                    ->label('Effective Period'),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),

                Tables\Filters\TernaryFilter::make('is_global')
                    ->label('Global Price Lists'),
            ])
            ->actions([
                Tables\Actions\Action::make('duplicate')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('warning')
                    ->action(function (PriceList $record) {
                        $duplicate = $record->duplicate();

                        Notification::make()
                            ->title('Price List Duplicated')
                            ->body("Created duplicate: {$duplicate->name}")
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Duplicate Price List')
                    ->modalDescription('This will create a copy of the price list with all its products. The copy will be inactive by default.'),

                Tables\Actions\Action::make('add_all_products')
                    ->icon('heroicon-o-plus-circle')
                    ->color('success')
                    ->label('Add All Products')
                    ->form([
                        Forms\Components\Toggle::make('use_default_price')
                            ->label('Use Default Product Prices')
                            ->default(true)
                            ->helperText('Use each product\'s default price, or set all to 0'),

                        Forms\Components\TextInput::make('discount_percentage')
                            ->label('Discount Percentage')
                            ->numeric()
                            ->suffix('%')
                            ->default(0)
                            ->helperText('Apply discount to default prices (e.g., 10 for 10% off)'),
                    ])
                    ->action(function (PriceList $record, array $data) {
                        $useDefaultPrice = $data['use_default_price'] ?? true;
                        $discount = $data['discount_percentage'] ?? 0;

                        $added = $record->addAllProducts($useDefaultPrice, $discount);

                        Notification::make()
                            ->title('Products Added')
                            ->body("Added {$added} new products to the price list")
                            ->success()
                            ->send();
                    })
                    ->modalHeading('Add All Products')
                    ->modalDescription('This will add all active products that are not already in this price list.'),

                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->defaultPaginationPageOption(25)
            ->paginationPageOptions([10, 25, 50, 100]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\PriceListItemsRelationManager::class,
            RelationManagers\OutletsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPriceLists::route('/'),
            'create' => Pages\CreatePriceList::route('/create'),
            'edit' => Pages\EditPriceList::route('/{record}/edit'),
        ];
    }
}
