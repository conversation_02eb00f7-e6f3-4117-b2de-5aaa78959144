<?php

namespace App\Filament\Pos\Resources\PriceListResource\RelationManagers;

use Filament\Forms;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use App\Models\Outlet;

class OutletsRelationManager extends RelationManager
{
    protected static string $relationship = 'outlets';

    protected static ?string $title = 'Assigned Outlets';



    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Outlet Name')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('code')
                    ->label('Code')
                    ->searchable(),
                
                Tables\Columns\TextColumn::make('pivot.priority')
                    ->label('Priority')
                    ->badge()
                    ->color('primary')
                    ->default('1'),

                Tables\Columns\IconColumn::make('pivot.is_active')
                    ->label('Active')
                    ->boolean()
                    ->default(true),

                Tables\Columns\TextColumn::make('pivot.effective_from')
                    ->label('From')
                    ->date()
                    ->placeholder('No limit')
                    ->default(null),

                Tables\Columns\TextColumn::make('pivot.effective_until')
                    ->label('Until')
                    ->date()
                    ->placeholder('No limit')
                    ->default(null),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('pivot.is_active')
                    ->label('Active Status'),
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->label('Assign to Outlet')
                    ->form([
                        Forms\Components\Select::make('recordId')
                            ->label('Outlet')
                            ->options(Outlet::all()->pluck('name', 'id'))
                            ->searchable()
                            ->required(),

                        Forms\Components\TextInput::make('priority')
                            ->label('Priority')
                            ->numeric()
                            ->default(1)
                            ->helperText('1 = Highest priority')
                            ->required(),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),

                        Forms\Components\DatePicker::make('effective_from')
                            ->label('Effective From'),

                        Forms\Components\DatePicker::make('effective_until')
                            ->label('Effective Until'),
                    ])
                    ->preloadRecordSelect(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->form([
                        Forms\Components\TextInput::make('priority')
                            ->label('Priority')
                            ->numeric()
                            ->default(1)
                            ->helperText('1 = Highest priority')
                            ->required(),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),

                        Forms\Components\DatePicker::make('effective_from')
                            ->label('Effective From'),

                        Forms\Components\DatePicker::make('effective_until')
                            ->label('Effective Until'),
                    ]),
                Tables\Actions\DetachAction::make()
                    ->label('Remove'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make()
                        ->label('Remove Selected'),
                ]),
            ])
            ->defaultSort('name');
    }
}
