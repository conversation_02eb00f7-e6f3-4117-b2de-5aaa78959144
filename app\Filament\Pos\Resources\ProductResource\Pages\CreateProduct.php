<?php

namespace App\Filament\Pos\Resources\ProductResource\Pages;

use App\Filament\Pos\Resources\ProductResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateProduct extends CreateRecord
{
    protected static string $resource = ProductResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Product created')
            ->body('The product has been created successfully.');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Generate SKU if not provided
        if (empty($data['sku'])) {
            $data['sku'] = $this->generateSku($data['name']);
        }

        // Ensure stock quantity is set
        if (!isset($data['stock_quantity'])) {
            $data['stock_quantity'] = 0;
        }

        // Ensure is_active is set
        if (!isset($data['is_active'])) {
            $data['is_active'] = true;
        }

        // Ensure is_food_item is set
        if (!isset($data['is_food_item'])) {
            $data['is_food_item'] = false;
        }

        return $data;
    }

    private function generateSku(string $name): string
    {
        // Generate SKU from product name
        $sku = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $name), 0, 6));
        
        // Add random number to ensure uniqueness
        $sku .= str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        // Check if SKU already exists
        $counter = 1;
        $originalSku = $sku;
        while (\App\Models\Product::where('sku', $sku)->exists()) {
            $sku = $originalSku . '-' . $counter;
            $counter++;
        }
        
        return $sku;
    }
}
