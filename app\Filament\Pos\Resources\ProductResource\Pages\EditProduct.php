<?php

namespace App\Filament\Pos\Resources\ProductResource\Pages;

use App\Filament\Pos\Resources\ProductResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditProduct extends EditRecord
{
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->icon('heroicon-o-eye'),
            Actions\DeleteAction::make()
                ->icon('heroicon-o-trash'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Product updated')
            ->body('The product information has been updated successfully.');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Generate SKU if empty
        if (empty($data['sku'])) {
            $data['sku'] = $this->generateSku($data['name']);
        }

        return $data;
    }

    private function generateSku(string $name): string
    {
        // Generate SKU from product name
        $sku = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $name), 0, 6));
        
        // Add random number to ensure uniqueness
        $sku .= str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        // Check if SKU already exists (excluding current record)
        $counter = 1;
        $originalSku = $sku;
        while (\App\Models\Product::where('sku', $sku)->where('id', '!=', $this->getRecord()->id)->exists()) {
            $sku = $originalSku . '-' . $counter;
            $counter++;
        }
        
        return $sku;
    }
}
