<?php

namespace App\Filament\Pos\Resources\ProductResource\Pages;

use App\Filament\Pos\Resources\ProductResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListProducts extends ListRecords
{
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->icon('heroicon-o-plus'),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Products')
                ->badge($this->getModel()::count()),

            'active' => Tab::make('Active')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_active', true))
                ->badge($this->getModel()::where('is_active', true)->count()),

            'inactive' => Tab::make('Inactive')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_active', false))
                ->badge($this->getModel()::where('is_active', false)->count()),

            'in_stock' => Tab::make('In Stock')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('stock_quantity', '>', 0))
                ->badge($this->getModel()::where('stock_quantity', '>', 0)->count()),

            'low_stock' => Tab::make('Low Stock')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereBetween('stock_quantity', [1, 10]))
                ->badge($this->getModel()::whereBetween('stock_quantity', [1, 10])->count()),

            'out_of_stock' => Tab::make('Out of Stock')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('stock_quantity', 0))
                ->badge($this->getModel()::where('stock_quantity', 0)->count()),

            'food_items' => Tab::make('Food Items')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_food_item', true))
                ->badge($this->getModel()::where('is_food_item', true)->count()),
        ];
    }
}
