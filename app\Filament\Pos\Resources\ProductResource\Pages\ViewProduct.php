<?php

namespace App\Filament\Pos\Resources\ProductResource\Pages;

use App\Filament\Pos\Resources\ProductResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewProduct extends ViewRecord
{
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->icon('heroicon-o-pencil'),
            
            Actions\DeleteAction::make()
                ->icon('heroicon-o-trash'),

            Actions\Action::make('adjust_stock')
                ->label('Adjust Stock')
                ->icon('heroicon-o-cube')
                ->color('warning')
                ->form([
                    \Filament\Forms\Components\Select::make('action')
                        ->label('Action')
                        ->options([
                            'add' => 'Add Stock',
                            'subtract' => 'Subtract Stock',
                            'set' => 'Set Stock',
                        ])
                        ->required(),
                    \Filament\Forms\Components\TextInput::make('quantity')
                        ->label('Quantity')
                        ->numeric()
                        ->required()
                        ->minValue(0),
                    \Filament\Forms\Components\Textarea::make('reason')
                        ->label('Reason')
                        ->required()
                        ->maxLength(255),
                ])
                ->action(function (array $data): void {
                    $oldStock = $this->record->stock_quantity;
                    
                    match ($data['action']) {
                        'add' => $this->record->increment('stock_quantity', $data['quantity']),
                        'subtract' => $this->record->decrement('stock_quantity', $data['quantity']),
                        'set' => $this->record->update(['stock_quantity' => $data['quantity']]),
                    };

                    $newStock = $this->record->fresh()->stock_quantity;

                    \Filament\Notifications\Notification::make()
                        ->success()
                        ->title('Stock adjusted')
                        ->body("Stock changed from {$oldStock} to {$newStock}. Reason: {$data['reason']}")
                        ->send();
                }),

            Actions\Action::make('duplicate')
                ->label('Duplicate Product')
                ->icon('heroicon-o-document-duplicate')
                ->color('info')
                ->form([
                    \Filament\Forms\Components\TextInput::make('name')
                        ->label('New Product Name')
                        ->required()
                        ->default(fn () => $this->record->name . ' (Copy)'),
                    \Filament\Forms\Components\TextInput::make('sku')
                        ->label('New SKU')
                        ->required()
                        ->unique(\App\Models\Product::class, 'sku'),
                ])
                ->action(function (array $data): void {
                    $newProduct = $this->record->replicate();
                    $newProduct->name = $data['name'];
                    $newProduct->sku = $data['sku'];
                    $newProduct->stock_quantity = 0; // Reset stock for new product
                    $newProduct->save();

                    \Filament\Notifications\Notification::make()
                        ->success()
                        ->title('Product duplicated')
                        ->body("New product '{$data['name']}' created successfully")
                        ->actions([
                            \Filament\Notifications\Actions\Action::make('view')
                                ->button()
                                ->url(ProductResource::getUrl('view', ['record' => $newProduct])),
                        ])
                        ->send();
                }),

            Actions\Action::make('toggle_status')
                ->label(fn () => $this->record->is_active ? 'Deactivate' : 'Activate')
                ->icon(fn () => $this->record->is_active ? 'heroicon-o-eye-slash' : 'heroicon-o-eye')
                ->color(fn () => $this->record->is_active ? 'danger' : 'success')
                ->requiresConfirmation()
                ->action(function (): void {
                    $this->record->update(['is_active' => !$this->record->is_active]);
                    
                    $status = $this->record->is_active ? 'activated' : 'deactivated';
                    
                    \Filament\Notifications\Notification::make()
                        ->success()
                        ->title('Product status updated')
                        ->body("Product has been {$status}")
                        ->send();
                }),
        ];
    }
}
