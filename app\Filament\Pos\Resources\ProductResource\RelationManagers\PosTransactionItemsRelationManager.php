<?php

namespace App\Filament\Pos\Resources\ProductResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PosTransactionItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'posTransactionItems';

    protected static ?string $title = 'Sales History';

    protected static ?string $icon = 'heroicon-o-shopping-cart';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('quantity')
                    ->label('Quantity')
                    ->numeric()
                    ->disabled(),
                Forms\Components\TextInput::make('unit_price')
                    ->label('Unit Price')
                    ->numeric()
                    ->prefix('Rp')
                    ->disabled(),
                Forms\Components\TextInput::make('total_price')
                    ->label('Total Price')
                    ->numeric()
                    ->prefix('Rp')
                    ->disabled(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('posTransaction.transaction_number')
            ->columns([
                Tables\Columns\TextColumn::make('posTransaction.transaction_number')
                    ->label('Transaction #')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('posTransaction.transaction_date')
                    ->label('Date & Time')
                    ->dateTime('M j, Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('posTransaction.customer.nama')
                    ->label('Customer')
                    ->searchable()
                    ->placeholder('Walk-in Customer'),

                Tables\Columns\TextColumn::make('quantity')
                    ->label('Qty')
                    ->numeric()
                    ->alignEnd()
                    ->sortable(),

                Tables\Columns\TextColumn::make('unit_price')
                    ->label('Unit Price')
                    ->money('IDR')
                    ->alignEnd()
                    ->sortable(),

                Tables\Columns\TextColumn::make('discount_per_item')
                    ->label('Discount')
                    ->money('IDR')
                    ->alignEnd()
                    ->color('danger'),

                Tables\Columns\TextColumn::make('total_price')
                    ->label('Total')
                    ->money('IDR')
                    ->alignEnd()
                    ->weight('medium')
                    ->sortable(),

                Tables\Columns\TextColumn::make('posTransaction.payment_method')
                    ->label('Payment')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'cash' => 'success',
                        'card' => 'info',
                        'digital_wallet' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('posTransaction.is_offline_transaction')
                    ->label('Sync Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-wifi')
                    ->falseIcon('heroicon-o-cloud')
                    ->trueColor('warning')
                    ->falseColor('success'),
            ])
            ->filters([
                Tables\Filters\Filter::make('transaction_date')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('From Date'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Until Date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn (Builder $query, $date): Builder => $query->whereHas(
                                    'posTransaction',
                                    fn (Builder $query) => $query->whereDate('transaction_date', '>=', $date)
                                ),
                            )
                            ->when(
                                $data['until'],
                                fn (Builder $query, $date): Builder => $query->whereHas(
                                    'posTransaction',
                                    fn (Builder $query) => $query->whereDate('transaction_date', '<=', $date)
                                ),
                            );
                    }),

                Tables\Filters\SelectFilter::make('payment_method')
                    ->relationship('posTransaction', 'payment_method')
                    ->options([
                        'cash' => 'Cash',
                        'card' => 'Card',
                        'digital_wallet' => 'Digital Wallet',
                    ]),

                Tables\Filters\Filter::make('quantity_range')
                    ->form([
                        Forms\Components\TextInput::make('min_qty')
                            ->label('Minimum Quantity')
                            ->numeric(),
                        Forms\Components\TextInput::make('max_qty')
                            ->label('Maximum Quantity')
                            ->numeric(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['min_qty'],
                                fn (Builder $query, $qty): Builder => $query->where('quantity', '>=', $qty),
                            )
                            ->when(
                                $data['max_qty'],
                                fn (Builder $query, $qty): Builder => $query->where('quantity', '<=', $qty),
                            );
                    }),
            ])
            ->headerActions([
                // No create action for transaction items - they come from POS
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->url(fn ($record) => route('filament.pos.resources.pos-transactions.view', $record->posTransaction))
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                // No bulk actions for transaction items
            ])
            ->defaultSort('posTransaction.transaction_date', 'desc')
            ->paginated([10, 25, 50]);
    }

    public function isReadOnly(): bool
    {
        return true;
    }
}
