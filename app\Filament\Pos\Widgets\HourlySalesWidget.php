<?php

namespace App\Filament\Pos\Widgets;

use App\Models\PosTransaction;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class HourlySalesWidget extends ChartWidget
{
    protected static ?string $heading = 'Hourly Sales (per Bulan)';
   
    protected static ?string $pollingInterval = '30s';

    protected static ?string $height = '300px';
    protected int | string | array $columnSpan = [
        
        'xl' => 3
    ];

    protected function getData(): array
    {
        // Define the start and end of the current month
        $startOfMonth = Carbon::createFromDate(2025,7,1);
        $endOfMonth = Carbon::createFromDate(2025,8,30);

        // Query to get total sales per hour
        $salesData = PosTransaction::selectRaw('HOUR(transaction_date) as hour, SUM(net_amount) as total_sales')
            ->whereBetween('transaction_date', [$startOfMonth, $endOfMonth])
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();
        
        // Query to get total transaction count per hour
        $transactionData = PosTransaction::selectRaw('HOUR(transaction_date) as hour, COUNT(*) as total_transactions')
            ->whereBetween('transaction_date', [$startOfMonth, $endOfMonth])
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();

        $salesDataMap = $salesData->keyBy('hour');
        $transactionDataMap = $transactionData->keyBy('hour');

        $sales = [];
        $transactions = [];
        $labels = [];

        // Populate arrays with data, ensuring all 24 hours are present
        for ($hour = 0; $hour < 24; $hour++) {
            $sales[] = $salesDataMap->has($hour) ? $salesDataMap[$hour]['total_sales'] : 0;
            $transactions[] = $transactionDataMap->has($hour) ? $transactionDataMap[$hour]['total_transactions'] : 0;
            $labels[] = str_pad($hour, 2, '0', STR_PAD_LEFT) . ':00';
        }
    //    dd($transactions);
        return [
            'datasets' => [
                // [
                //     'label' => 'Total Sales (Rp)',
                //     'data' => $sales,
                //     'backgroundColor' => 'rgba(99, 102, 241, 0.1)',
                //     'borderColor' => 'rgba(99, 102, 241, 1)',
                //     'borderWidth' => 2,
                //     'fill' => true,
                //     'tension' => 0.4,
                //     'pointBackgroundColor' => 'rgba(99, 102, 241, 1)',
                //     'pointBorsderColor' => '#fff',
                //     'pointBorderWidth' => 2,
                //     'pointRadius' => 4,
                // ],
                    [
                        'label' => 'Total Transactions',
                        'data' => $transactions,
                        'backgroundColor' => 'rgba(255, 99, 132, 0.1)', // Example color for the second dataset
                        'borderColor' => 'rgba(255, 99, 132, 1)',
                        'borderWidth' => 2,
                        'fill' => true,
                        'tension' => 0.4,
                        'pointBackgroundColor' => 'rgba(255, 99, 132, 1)',
                        'pointBorderColor' => '#fff',
                        'pointBorderWidth' => 2,
                        'pointRadius' => 2,
                    ],
                ],
            'labels' =>$labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => false,
                ],
                'tooltip' => [
                    'callbacks' => [
                        // Customize the tooltip to show both sales and transaction count
                        'label' => 'function(context) {
                            let label = context.dataset.label || "";
                            if (label) {
                                label += ": ";
                            }
                            if (context.parsed.y !== null) {
                                if (label.includes("Sales")) {
                                    label += "Rp " + context.parsed.y.toLocaleString();
                                } else {
                                    label += context.parsed.y.toLocaleString();
                                }
                            }
                            return label;
                        }',
                    ],
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => 'function(value) { return "Rp " + value.toLocaleString(); }',
                    ],
                ],
                'x' => [
                    'grid' => [
                        'display' => true,
                    ],
                ],
            ],
            'interaction' => [
                'intersect' => false,
                'mode' => 'index',
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}