<?php

namespace App\Filament\Pos\Widgets;

use App\Models\Outlet;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class LocationPerformanceWidget extends BaseWidget
{
    protected static ?string $heading = 'Outlet Performance (Today)';

    protected static ?string $pollingInterval = '60s';

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Outlet::query()
                    ->where('is_active', true)
                    ->where('status', 'active')
                    ->withCount([
                        'karyawan as active_employees' => function ($query) {
                            $query->where('status_aktif', 1);
                        }
                    ])
                    ->orderByRaw('(
                        SELECT COALESCE(SUM(pos_transactions.net_amount), 0)
                        FROM pos_transactions
                        WHERE pos_transactions.outlet_id = outlets.id
                        AND DATE(pos_transactions.transaction_date) = CURDATE()
                    ) DESC')
            )
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Outlet')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('code')
                    ->label('Code')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'toko' => 'success',
                        'restoran' => 'warning',
                        'kafe' => 'info',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('city')
                    ->label('City')
                    ->searchable(),

                Tables\Columns\TextColumn::make('active_employees')
                    ->label('Staff')
                    ->alignEnd()
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('today_transactions')
                    ->label('Transactions')
                    ->getStateUsing(function (Outlet $record): int {
                        return $record->getTodayTransactionCount();
                    })
                    ->alignEnd()
                    ->badge()
                    ->color('success'),

                Tables\Columns\TextColumn::make('today_revenue')
                    ->label('Revenue')
                    ->getStateUsing(function (Outlet $record): string {
                        $revenue = $record->getTodayRevenue();
                        return 'Rp ' . number_format($revenue, 0, ',', '.');
                    })
                    ->alignEnd()
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('avg_transaction')
                    ->label('Avg/Transaction')
                    ->getStateUsing(function (Outlet $record): string {
                        $avg = $record->getAverageTransactionValue();
                        return 'Rp ' . number_format($avg, 0, ',', '.');
                    })
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('performance_score')
                    ->label('Score')
                    ->getStateUsing(function (Outlet $record): string {
                        // Simple performance score based on transactions and revenue
                        $transactions = $record->getTodayTransactionCount();
                        $score = min(100, $transactions * 2); // Max 100, 2 points per transaction
                        return $score . '%';
                    })
                    ->alignEnd()
                    ->badge()
                    ->color(function ($state): string {
                        $score = (int) str_replace('%', '', $state);
                        if ($score >= 80) {
                            return 'success';
                        } elseif ($score >= 60) {
                            return 'warning';
                        } elseif ($score >= 40) {
                            return 'danger';
                        } else {
                            return 'gray';
                        }
                    }),
            ])
            ->paginated(false);
    }
}
