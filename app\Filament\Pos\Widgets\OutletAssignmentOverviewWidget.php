<?php

namespace App\Filament\Pos\Widgets;

use App\Models\OutletUserAssignment;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Database\Eloquent\Model;

class OutletAssignmentOverviewWidget extends BaseWidget
{
    public ?Model $record = null;

    protected function getStats(): array
    {
        if (!$this->record) {
            return [];
        }

        $outletId = $this->record->id;

        // Use a single query with aggregation for better performance
        $stats = cache()->remember("outlet_assignment_stats_{$outletId}", 60, function () use ($outletId) {
            $assignments = OutletUserAssignment::where('outlet_id', $outletId)
                ->select(['is_active', 'role', 'assigned_from', 'assigned_until'])
                ->get();

            $now = now();
            $stats = [
                'total' => $assignments->count(),
                'active' => 0,
                'effective' => 0,
                'kepala_toko' => 0,
                'kasir' => 0,
            ];

            foreach ($assignments as $assignment) {
                if ($assignment->is_active) {
                    $stats['active']++;

                    // Check if currently effective
                    $effective = true;
                    if ($assignment->assigned_from && $now->lt($assignment->assigned_from)) {
                        $effective = false;
                    }
                    if ($assignment->assigned_until && $now->gt($assignment->assigned_until)) {
                        $effective = false;
                    }

                    if ($effective) {
                        $stats['effective']++;

                        // Count by role
                        if ($assignment->role === 'kepala toko') {
                            $stats['kepala_toko']++;
                        } elseif ($assignment->role === 'kasir') {
                            $stats['kasir']++;
                        }
                    }
                }
            }

            return $stats;
        });

        return [
            Stat::make('Total Assignments', $stats['total'])
                ->description('All user assignments')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('primary'),

            Stat::make('Active Assignments', $stats['active'])
                ->description('Currently active')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('Currently Effective', $stats['effective'])
                ->description('Working today')
                ->descriptionIcon('heroicon-m-clock')
                ->color($stats['effective'] > 0 ? 'success' : 'warning'),

            Stat::make('Kepala Toko', $stats['kepala_toko'])
                ->description('Store managers')
                ->descriptionIcon('heroicon-m-user-circle')
                ->color($stats['kepala_toko'] > 0 ? 'success' : 'danger'),

            Stat::make('Kasir', $stats['kasir'])
                ->description('Cashiers')
                ->descriptionIcon('heroicon-m-users')
                ->color($stats['kasir'] > 0 ? 'success' : 'warning'),
        ];
    }

    protected function getColumns(): int
    {
        return 5;
    }

    public static function canView(): bool
    {
        return true;
    }
}
