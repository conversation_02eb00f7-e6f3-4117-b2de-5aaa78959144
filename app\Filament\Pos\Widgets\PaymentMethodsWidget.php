<?php

namespace App\Filament\Pos\Widgets;

use App\Models\PosTransaction;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class PaymentMethodsWidget extends ChartWidget
{
    protected static ?string $heading = 'Payment Methods (This Month)';

    protected static ?string $pollingInterval = '60s';

    protected function getData(): array
    {
        $thisMonth = Carbon::now()->startOfMonth();
        
        // Get payment method data for this month
        $paymentData = PosTransaction::where('transaction_date', '>=', $thisMonth)
            ->selectRaw('payment_method, COUNT(*) as count, SUM(net_amount) as total')
            ->groupBy('payment_method')
            ->get();

        $labels = [];
        $data = [];
        $colors = [];

        $colorMap = [
            'cash' => '#10B981',      // Green
            'card' => '#3B82F6',      // Blue
            'digital_wallet' => '#F59E0B', // Yellow
            'bank_transfer' => '#8B5CF6', // Purple
        ];

        foreach ($paymentData as $payment) {
            $labels[] = match ($payment->payment_method) {
                'cash' => 'Cash',
                'card' => 'Card',
                'digital_wallet' => 'Digital Wallet',
                'bank_transfer' => 'Bank Transfer',
                default => ucfirst($payment->payment_method),
            };
            $data[] = $payment->count;
            $colors[] = $colorMap[$payment->payment_method] ?? '#6B7280';
        }

        return [
            'datasets' => [
                [
                    'label' => 'Transactions',
                    'data' => $data,
                    'backgroundColor' => $colors,
                    'borderColor' => $colors,
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            const label = context.label || "";
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return label + ": " + value + " (" + percentage + "%)";
                        }',
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}
