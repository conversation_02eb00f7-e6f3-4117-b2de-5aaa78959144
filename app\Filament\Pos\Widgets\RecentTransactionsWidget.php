<?php

namespace App\Filament\Pos\Widgets;

use App\Models\PosTransaction;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class RecentTransactionsWidget extends BaseWidget
{
    protected static ?string $heading = 'Recent Transactions';

    protected static ?string $pollingInterval = '30s';

    protected static ?string $maxHeight = '200px';    

    protected int | string | array $columnSpan = [
        'xl' => 3
    ];

    public function table(Table $table): Table
    {
        return $table
            ->query(
                PosTransaction::query()
                    ->with(['customer', 'user'])
                    ->latest('transaction_date')
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('transaction_number')
                    ->label('Transaction #')
                    ->searchable()
                    ->sortable()
                    ->weight('medium')
                    ->copyable(),

                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('Date & Time')
                    ->dateTime('M j, Y H:i')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('customer.nama')
                    ->label('Customer')
                    ->searchable()
                    ->placeholder('Walk-in Customer')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Cashier')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Payment')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'cash' => 'success',
                        'card' => 'info',
                        'digital_wallet' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('total_amount')
                    ->label('Subtotal')
                    ->money('IDR')
                    ->alignEnd()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('discount_amount')
                    ->label('Discount')
                    ->money('IDR')
                    ->alignEnd()
                    ->toggleable()
                    ->color('danger'),

                Tables\Columns\TextColumn::make('net_amount')
                    ->label('Total')
                    ->money('IDR')
                    ->alignEnd()
                    ->weight('medium')
                    ->sortable(),

                Tables\Columns\TextColumn::make('table_number')
                    ->label('Table')
                    ->badge()
                    ->placeholder('Takeaway')
                    ->toggleable(),

                Tables\Columns\IconColumn::make('is_offline_transaction')
                    ->label('Sync Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-wifi')
                    ->falseIcon('heroicon-o-cloud')
                    ->trueColor('warning')
                    ->falseColor('success')
                    ->tooltip(fn ($record) => $record->is_offline_transaction ? 'Offline Transaction' : 'Online Transaction'),
            ])
            ->defaultSort('transaction_date', 'desc')
            ->paginated(false)
            ->actions([
                Tables\Actions\Action::make('view')
                    ->icon('heroicon-m-eye')
                    ->url(fn (PosTransaction $record): string => route('filament.pos.resources.pos-transactions.view', $record))
                    ->openUrlInNewTab(),
            ]);
    }
}
