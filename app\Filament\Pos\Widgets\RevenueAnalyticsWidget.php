<?php

namespace App\Filament\Pos\Widgets;

use App\Models\PosTransaction;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class RevenueAnalyticsWidget extends ChartWidget
{
    protected static ?string $heading = 'Revenue Analytics (Last 7 Days)';

    protected static ?string $pollingInterval = '30s';

    protected static ?string $maxHeight = '150px';

    protected int | string | array $columnSpan = [
        
        'xl' => 6
    ];
    
    protected function getData(): array
    {
        $data = [];
        $labels = [];

        // Get last 7 days data
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $labels[] = $date->format('M j');
            
            $dailyRevenue = PosTransaction::whereDate('transaction_date', $date)
                ->sum('net_amount') ?? 0;
            
            $data[] = $dailyRevenue;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Daily Revenue',
                    'data' => $data,
                    'backgroundColor' => 'rgba(99, 102, 241, 0.1)',
                    'borderColor' => 'rgba(99, 102, 241, 1)',
                    'borderWidth' => 2,
                    'fill' => true,
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => 'function(value) { return "Rp " + value.toLocaleString(); }',
                    ],
                ],
            ],
            'interaction' => [
                'intersect' => false,
                'mode' => 'index',
            ],
            'elements' => [
                'point' => [
                    'radius' => 4,
                    'hoverRadius' => 6,
                ],
            ],
        ];
    }
}
