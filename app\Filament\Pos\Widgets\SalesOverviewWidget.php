<?php

namespace App\Filament\Pos\Widgets;

use App\Models\PosTransaction;
use App\Services\PosCache;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class SalesOverviewWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        // Use cached data for better performance
        $stats = cache()->remember('pos_sales_overview_stats', 60, function () {
            $today = Carbon::today();
            $yesterday = Carbon::yesterday();
            $thisMonth = Carbon::now()->startOfMonth();
            $lastMonth = Carbon::now()->subMonth()->startOfMonth();

            // Get all data in fewer queries
            $todayData = PosTransaction::whereDate('transaction_date', $today)
                ->selectRaw('COUNT(*) as count, SUM(net_amount) as total')
                ->first();

            $yesterdayData = PosTransaction::whereDate('transaction_date', $yesterday)
                ->selectRaw('COUNT(*) as count, SUM(net_amount) as total')
                ->first();

            $thisMonthData = PosTransaction::where('transaction_date', '>=', $thisMonth)
                ->selectRaw('COUNT(*) as count, SUM(net_amount) as total')
                ->first();

            $lastMonthData = PosTransaction::where('transaction_date', '>=', $lastMonth)
                ->where('transaction_date', '<', $thisMonth)
                ->selectRaw('COUNT(*) as count, SUM(net_amount) as total')
                ->first();

            return [
                'today_sales' => $todayData->total ?? 0,
                'yesterday_sales' => $yesterdayData->total ?? 0,
                'today_transactions' => $todayData->count ?? 0,
                'yesterday_transactions' => $yesterdayData->count ?? 0,
                'this_month_sales' => $thisMonthData->total ?? 0,
                'last_month_sales' => $lastMonthData->total ?? 0,
            ];
        });

        // Calculate percentage changes
        $todayChange = $stats['yesterday_sales'] > 0
            ? (($stats['today_sales'] - $stats['yesterday_sales']) / $stats['yesterday_sales']) * 100
            : 0;

        $monthlyChange = $stats['last_month_sales'] > 0
            ? (($stats['this_month_sales'] - $stats['last_month_sales']) / $stats['last_month_sales']) * 100
            : 0;

        $transactionChange = $stats['yesterday_transactions'] > 0
            ? (($stats['today_transactions'] - $stats['yesterday_transactions']) / $stats['yesterday_transactions']) * 100
            : 0;

        // Average transaction value
        $avgTransactionValue = $stats['today_transactions'] > 0
            ? $stats['today_sales'] / $stats['today_transactions']
            : 0;

        // Get last 7 days data for chart
        $chartData = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dailySales = PosTransaction::whereDate('transaction_date', $date)->sum('net_amount');
            $chartData[] = $dailySales / 1000; // Convert to thousands for better chart display
        }

        return [
            Stat::make('Today\'s Sales', 'Rp ' . number_format($stats['today_sales'], 0, ',', '.'))
                ->description($todayChange >= 0 ? '+' . number_format($todayChange, 1) . '% from yesterday' : number_format($todayChange, 1) . '% from yesterday')
                ->descriptionIcon($todayChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($todayChange >= 0 ? 'success' : 'danger')
                ->chart($chartData),

            Stat::make('Monthly Sales', 'Rp ' . number_format($stats['this_month_sales'], 0, ',', '.'))
                ->description($monthlyChange >= 0 ? '+' . number_format($monthlyChange, 1) . '% from last month' : number_format($monthlyChange, 1) . '% from last month')
                ->descriptionIcon($monthlyChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($monthlyChange >= 0 ? 'success' : 'danger'),

            Stat::make('Today\'s Transactions', $stats['today_transactions'])
                ->description($transactionChange >= 0 ? '+' . number_format($transactionChange, 1) . '% from yesterday' : number_format($transactionChange, 1) . '% from yesterday')
                ->descriptionIcon($transactionChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($transactionChange >= 0 ? 'success' : 'danger'),

            Stat::make('Avg. Transaction Value', 'Rp ' . number_format($avgTransactionValue, 0, ',', '.'))
                ->description('Average value per transaction today')
                ->descriptionIcon('heroicon-m-calculator')
                ->color('info'),
        ];
    }
}
