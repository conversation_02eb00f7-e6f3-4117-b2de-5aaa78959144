<?php

namespace App\Filament\Pos\Widgets;

use App\Models\PosTransactionItem;
use App\Models\Product;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class TopProductsWidget extends BaseWidget
{
    protected static ?string $heading = 'Top Selling Products (This Month)';

    protected static ?string $pollingInterval = '60s';

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                PosTransactionItem::query()
                    ->select([
                        'product_id',
                        \DB::raw('SUM(quantity) as total_quantity'),
                        \DB::raw('SUM(total_price) as total_revenue'),
                        \DB::raw('COUNT(*) as transaction_count')
                    ])
                    ->whereHas('posTransaction', function (Builder $query) {
                        $query->whereMonth('transaction_date', now()->month)
                              ->whereYear('transaction_date', now()->year);
                    })
                    ->groupBy('product_id')
                    ->orderBy('total_quantity', 'desc')
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('product.name')
                    ->label('Product Name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('product.sku')
                    ->label('SKU')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('total_quantity')
                    ->label('Qty Sold')
                    ->numeric()
                    ->sortable()
                    ->alignEnd()
                    ->badge()
                    ->color('success'),

                Tables\Columns\TextColumn::make('transaction_count')
                    ->label('Transactions')
                    ->numeric()
                    ->sortable()
                    ->alignEnd(),

                Tables\Columns\TextColumn::make('total_revenue')
                    ->label('Revenue')
                    ->money('IDR')
                    ->sortable()
                    ->alignEnd()
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('product.stock_quantity')
                    ->label('Current Stock')
                    ->numeric()
                    ->alignEnd()
                    ->badge()
                    ->color(fn ($record) => $record->product?->stock_quantity > 10 ? 'success' : ($record->product?->stock_quantity > 0 ? 'warning' : 'danger')),
            ])
            ->defaultSort('total_quantity', 'desc')
            ->paginated(false);
    }
}
