<?php

namespace App\Filament\Pos\Widgets;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class WelcomeWidget extends Widget
{
    protected static string $view = 'filament.pos.widgets.welcome-widget';

    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = -10;

    public function getViewData(): array
    {
        $user = Auth::user();
        $currentHour = now()->hour;
        
        // Determine greeting based on time
        $greeting = match (true) {
            $currentHour < 12 => 'Good Morning',
            $currentHour < 17 => 'Good Afternoon',
            default => 'Good Evening',
        };

        // Get today's quick stats
        $todayStats = [
            'transactions' => \App\Models\PosTransaction::whereMonth('transaction_date', now()->month)->count(),
            'revenue' => \App\Models\PosTransaction::whereMonth('transaction_date', now()->month)->sum('net_amount'),
            'customers' => \App\Models\PosTransaction::whereMonth('transaction_date', now()->month)->distinct('customer_id')->count('customer_id'),
        ];
        // dd($todayStats);
        return [
            'user' => $user,
            'greeting' => $greeting,
            'todayStats' => $todayStats,
        ];
    }
}
