<?php

namespace App\Filament\Resources\AbsensiResource\Pages;

use App\Filament\Resources\AbsensiResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Actions;
use App\Services\PermissionService;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class ViewAbsensi extends ViewRecord
{
    protected static string $resource = AbsensiResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load relasi yang diperlukan untuk toleransi
        $this->record->load(['toleranceApprovedBy', 'karyawan', 'jadwal.shift']);

        return $data;
    }

    protected function getHeaderActions(): array
    {
        return [
            // acc absensi
            Actions\Action::make('acc_absensi')
                ->label('ACC Absensi')
                ->icon('heroicon-o-check')
                ->color('success')
                ->requiresConfirmation()
                ->action(fn() => $this->record->update(
                    [
                        'approved_by' => auth()->id(),
                        'approved_at' => now()
                    ]
                )),

            // berikan toleransi
            Actions\Action::make('give_tolerance')
                ->label('Berikan Toleransi')
                ->icon('heroicon-o-clock')
                ->color('warning')
                ->visible(function () {
                    $hasPermission = PermissionService::canManageAbsensi($this->record);
                    return $hasPermission
                        && $this->record->status === 'terlambat'
                        && !($this->record->is_tolerance_given ?? false);
                })
                ->form([
                    \Filament\Forms\Components\Textarea::make('tolerance_reason')
                        ->label('Alasan Toleransi')
                        ->required()
                        ->placeholder('Masukkan alasan pemberian toleransi keterlambatan...')
                        ->rows(3),
                ])
                ->action(function (array $data) {
                    $this->record->giveTolerance($data['tolerance_reason'], Auth::id());

                    Notification::make()
                        ->title('Toleransi Berhasil Diberikan')
                        ->body('Toleransi keterlambatan untuk ' . $this->record->karyawan->nama_lengkap . ' telah diberikan.')
                        ->success()
                        ->send();

                    // Refresh the page to show updated information
                    $this->redirect(request()->header('Referer'));
                }),

            // hapus toleransi
            Actions\Action::make('remove_tolerance')
                ->label('Hapus Toleransi')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->visible(function () {
                    $hasPermission = PermissionService::canManageAbsensi($this->record);
                    return $hasPermission && ($this->record->is_tolerance_given ?? false);
                })
                ->requiresConfirmation()
                ->modalHeading('Hapus Toleransi')
                ->modalDescription('Apakah Anda yakin ingin menghapus toleransi keterlambatan ini?')
                ->action(function () {
                    $this->record->removeTolerance();

                    Notification::make()
                        ->title('Toleransi Berhasil Dihapus')
                        ->body('Toleransi keterlambatan untuk ' . $this->record->karyawan->nama_lengkap . ' telah dihapus.')
                        ->success()
                        ->send();

                    // Refresh the page to show updated information
                    $this->redirect(request()->header('Referer'));
                }),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Informasi Absensi')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('karyawan.nama_lengkap')
                                    ->label('Nama Karyawan'),
                                Infolists\Components\TextEntry::make('tanggal_absensi')
                                    ->label('Tanggal')
                                    ->date('d M Y'),
                                Infolists\Components\TextEntry::make('waktu_masuk')
                                    ->label('Jam Masuk')
                                    ->time('H:i'),
                                Infolists\Components\TextEntry::make('waktu_keluar')
                                    ->label('Jam Keluar')
                                    ->time('H:i')
                                    ->placeholder('-'),
                                Infolists\Components\TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn(string $state): string => match (strtolower($state)) {
                                        'hadir' => 'success',
                                        'terlambat' => 'warning',
                                        'izin' => 'info',
                                        'sakit' => 'info',
                                        'cuti' => 'primary',
                                        'alpha' => 'danger',
                                        default => 'gray',
                                    }),
                                Infolists\Components\TextEntry::make('jadwal.shift.nama_shift')
                                    ->label('Shift')
                                    ->formatStateUsing(function ($state) {
                                        if (!$state || !$this->record->jadwal || !$this->record->jadwal->shift) return '-';
                                        $shift = $this->record->jadwal->shift;
                                        return $state . ' (' . $shift->waktu_mulai->format('H:i') . ' - ' . $shift->waktu_selesai->format('H:i') . ')';
                                    })
                                    ->placeholder('-'),
                            ]),

                        Infolists\Components\TextEntry::make('keterangan')
                            ->label('Keterangan')
                            ->placeholder('-')
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Lokasi')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('lokasi_masuk')
                                    ->label('Lokasi Masuk')
                                    ->placeholder('-')
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) return '-';
                                        $coords = explode(',', $state);
                                        if (count($coords) === 2) {
                                            return "Lat: {$coords[0]}, Lng: {$coords[1]}";
                                        }
                                        return $state;
                                    }),
                                Infolists\Components\TextEntry::make('lokasi_keluar')
                                    ->label('Lokasi Keluar')
                                    ->placeholder('-')
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) return '-';
                                        $coords = explode(',', $state);
                                        if (count($coords) === 2) {
                                            return "Lat: {$coords[0]}, Lng: {$coords[1]}";
                                        }
                                        return $state;
                                    }),
                            ]),
                    ]),

                Infolists\Components\Section::make('Peta Lokasi')
                    ->schema([
                        Infolists\Components\TextEntry::make('maps_info')
                            ->label('Akses Peta Interaktif')
                            ->formatStateUsing(function ($record) {
                                $content = '<div style="background: #f0f9ff; padding: 16px; border-radius: 8px; border: 1px solid #0ea5e9;">';
                                $content .= '<h4 style="margin: 0 0 12px 0; font-weight: 600; color: #0c4a6e;">🗺️ Lokasi Absensi</h4>';

                                if ($record->latitude_masuk && $record->longitude_masuk) {
                                    $content .= '<div style="margin-bottom: 12px;">';
                                    $content .= '<strong style="color: #059669;">📍 Lokasi Masuk:</strong><br>';
                                    $content .= '<span style="font-family: monospace; background: #ecfdf5; padding: 4px 8px; border-radius: 4px; font-size: 14px;">';
                                    $content .= $record->latitude_masuk . ', ' . $record->longitude_masuk;
                                    $content .= '</span><br>';
                                    $content .= '<a href="https://www.google.com/maps?q=' . $record->latitude_masuk . ',' . $record->longitude_masuk . '" target="_blank" style="color: #2563eb; text-decoration: underline; font-size: 14px;">🗺️ Buka di Google Maps</a>';
                                    $content .= '</div>';
                                }

                                if ($record->latitude_keluar && $record->longitude_keluar) {
                                    $content .= '<div style="margin-bottom: 12px;">';
                                    $content .= '<strong style="color: #dc2626;">📍 Lokasi Keluar:</strong><br>';
                                    $content .= '<span style="font-family: monospace; background: #fef2f2; padding: 4px 8px; border-radius: 4px; font-size: 14px;">';
                                    $content .= $record->latitude_keluar . ', ' . $record->longitude_keluar;
                                    $content .= '</span><br>';
                                    $content .= '<a href="https://www.google.com/maps?q=' . $record->latitude_keluar . ',' . $record->longitude_keluar . '" target="_blank" style="color: #2563eb; text-decoration: underline; font-size: 14px;">🗺️ Buka di Google Maps</a>';
                                    $content .= '</div>';
                                }

                                $content .= '<p style="margin: 12px 0 0 0; padding: 8px; background: #eff6ff; border-radius: 4px; font-size: 13px; color: #1e40af;">';
                                $content .= '💡 <strong>Tips:</strong> Gunakan tombol "Lihat Lokasi" di halaman daftar absensi untuk melihat peta interaktif dengan detail lengkap.';
                                $content .= '</p>';
                                $content .= '</div>';

                                return new \Illuminate\Support\HtmlString($content);
                            })
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => ($record->latitude_masuk && $record->longitude_masuk) || ($record->latitude_keluar && $record->longitude_keluar)),

                Infolists\Components\Section::make('Foto Absensi')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\ImageEntry::make('foto_masuk')
                                    ->label('Foto Masuk')
                                    ->disk('public')
                                    ->height(200)
                                    ->width(200),

                                Infolists\Components\ImageEntry::make('foto_keluar')
                                    ->label('Foto Keluar')
                                    ->disk('public')
                                    ->height(200)
                                    ->width(200),
                            ]),
                    ]),

                Infolists\Components\Section::make('Metadata Foto')
                    ->schema([
                        Infolists\Components\TextEntry::make('metadata_info')
                            ->label('Informasi Metadata')
                            ->formatStateUsing(function ($record) {
                                $content = '';

                                // Metadata foto masuk
                                if ($record->metadata_foto_masuk) {
                                    $metadata = $record->metadata_foto_masuk;
                                    $content .= '<div style="background: #f0f9ff; padding: 16px; border-radius: 8px; border: 1px solid #0ea5e9; margin-bottom: 16px;">';
                                    $content .= '<h4 style="margin: 0 0 12px 0; font-weight: 600; color: #0c4a6e;">📸 Metadata Foto Masuk</h4>';

                                    if (isset($metadata['datetime_display'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>🕐 Waktu:</strong> ' . $metadata['datetime_display'] . '</div>';
                                    }
                                    if (isset($metadata['coordinates_display'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>📍 Koordinat:</strong> ' . $metadata['coordinates_display'] . '</div>';
                                    }
                                    if (isset($metadata['status_kehadiran'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>⏰ Status:</strong> ' . $metadata['status_kehadiran'] . '</div>';
                                    }
                                    if (isset($metadata['camera_info'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>📱 Device:</strong> ' . $metadata['camera_info'] . '</div>';
                                    }
                                    $content .= '</div>';
                                }

                                // Metadata foto keluar
                                if ($record->metadata_foto_keluar) {
                                    $metadata = $record->metadata_foto_keluar;
                                    $content .= '<div style="background: #fef2f2; padding: 16px; border-radius: 8px; border: 1px solid #f87171; margin-bottom: 16px;">';
                                    $content .= '<h4 style="margin: 0 0 12px 0; font-weight: 600; color: #7f1d1d;">🏠 Metadata Foto Keluar</h4>';

                                    if (isset($metadata['datetime_display'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>🕐 Waktu:</strong> ' . $metadata['datetime_display'] . '</div>';
                                    }
                                    if (isset($metadata['coordinates_display'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>📍 Koordinat:</strong> ' . $metadata['coordinates_display'] . '</div>';
                                    }
                                    if (isset($metadata['status_kehadiran'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>⏰ Status:</strong> ' . $metadata['status_kehadiran'] . '</div>';
                                    }
                                    if (isset($metadata['camera_info'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>📱 Device:</strong> ' . $metadata['camera_info'] . '</div>';
                                    }
                                    $content .= '</div>';
                                }

                                if (empty($content)) {
                                    $content = '<div style="color: #9ca3af; font-style: italic;">Tidak ada metadata foto tersedia</div>';
                                }

                                return new \Illuminate\Support\HtmlString($content);
                            })
                            ->html()
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => !empty($record->metadata_foto_masuk) || !empty($record->metadata_foto_keluar)),

                Infolists\Components\Section::make('Persetujuan')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('approvedBy.name')
                                    ->label('Disetujui Oleh')
                                    ->placeholder('Belum disetujui'),
                                Infolists\Components\TextEntry::make('approved_at')
                                    ->label('Tanggal Persetujuan')
                                    ->dateTime('d M Y H:i')
                                    ->placeholder('Belum disetujui'),
                            ]),
                    ]),

                Infolists\Components\Section::make('Toleransi Keterlambatan')
                    ->schema([
                        Infolists\Components\TextEntry::make('is_tolerance_given')
                            ->label('Status Toleransi')
                            ->formatStateUsing(function ($state) {
                                if ($state) {
                                    return '✅ Toleransi Diberikan';
                                }
                                return '❌ Tidak Ada Toleransi';
                            })
                            ->badge()
                            ->color(fn($state) => $state ? 'success' : 'gray'),

                        Infolists\Components\TextEntry::make('tolerance_reason')
                            ->label('Detail Toleransi')
                            ->formatStateUsing(function ($state, $record) {
                                if (!$record->is_tolerance_given) {
                                    return '-';
                                }

                                $content = '<div style="background: #f0f9ff; padding: 16px; border-radius: 8px; border: 1px solid #0ea5e9;">';
                                $content .= '<h4 style="margin: 0 0 12px 0; font-weight: 600; color: #0c4a6e;">🕐 Informasi Toleransi</h4>';

                                if ($record->tolerance_reason) {
                                    $content .= '<div style="margin-bottom: 12px;">';
                                    $content .= '<strong style="color: #059669;">📝 Alasan:</strong><br>';
                                    $content .= '<div style="background: #ecfdf5; padding: 8px 12px; border-radius: 4px; margin-top: 4px; border-left: 4px solid #10b981;">';
                                    $content .= htmlspecialchars($record->tolerance_reason);
                                    $content .= '</div>';
                                    $content .= '</div>';
                                }

                                if ($record->toleranceApprovedBy) {
                                    $content .= '<div style="margin-bottom: 12px;">';
                                    $content .= '<strong style="color: #7c3aed;">👤 Diberikan oleh:</strong> ';
                                    $content .= '<span style="background: #f3e8ff; padding: 4px 8px; border-radius: 4px; font-weight: 500;">';
                                    $content .= htmlspecialchars($record->toleranceApprovedBy->name);
                                    $content .= '</span>';
                                    $content .= '</div>';
                                }

                                if ($record->tolerance_approved_at) {
                                    $content .= '<div style="margin-bottom: 8px;">';
                                    $content .= '<strong style="color: #dc2626;">⏰ Waktu Pemberian:</strong> ';
                                    $content .= '<span style="background: #fef2f2; padding: 4px 8px; border-radius: 4px; font-family: monospace;">';
                                    $content .= $record->tolerance_approved_at->format('d M Y H:i:s');
                                    $content .= '</span>';
                                    $content .= '</div>';
                                }

                                $content .= '</div>';

                                return new \Illuminate\Support\HtmlString($content);
                            })
                            ->html()
                            ->visible(fn($record) => $record->is_tolerance_given)
                            ->columnSpanFull(),

                        Infolists\Components\TextEntry::make('status')
                            ->label('Informasi Keterlambatan')
                            ->formatStateUsing(function ($state, $record) {
                                if ($record->status !== 'terlambat') {
                                    return 'Status bukan terlambat';
                                }

                                $content = '<div style="background: #fef3c7; padding: 16px; border-radius: 8px; border: 1px solid #f59e0b;">';
                                $content .= '<h4 style="margin: 0 0 12px 0; font-weight: 600; color: #92400e;">⚠️ Detail Keterlambatan</h4>';

                                // Hitung menit terlambat
                                $menitTerlambat = 0;
                                if ($record->waktu_masuk && $record->jadwal && $record->jadwal->shift) {
                                    $shift = $record->jadwal->shift;
                                    $waktuMasukAktual = \Carbon\Carbon::parse($record->waktu_masuk);
                                    $tanggalWaktuMasuk = $waktuMasukAktual->copy()->startOfDay();
                                    $waktuMasukShift = $tanggalWaktuMasuk->copy()->setTimeFromTimeString($shift->waktu_mulai->format('H:i:s'));
                                    $toleranceMinutes = $shift->toleransi_keterlambatan ?? 0;
                                    $allowedEntry = $waktuMasukShift->copy()->addMinutes($toleranceMinutes);

                                    if ($waktuMasukAktual->greaterThan($allowedEntry)) {
                                        $menitTerlambat = $waktuMasukAktual->diffInMinutes($allowedEntry);
                                    }
                                }

                                $content .= '<div style="margin-bottom: 8px;">';
                                $content .= '<strong>⏱️ Menit Terlambat:</strong> ';
                                $content .= '<span style="background: #fef2f2; padding: 4px 8px; border-radius: 4px; font-weight: bold; color: #dc2626;">';
                                $content .= $menitTerlambat . ' menit';
                                $content .= '</span>';
                                $content .= '</div>';

                                if ($record->is_tolerance_given) {
                                    $content .= '<div style="background: #d1fae5; padding: 8px 12px; border-radius: 4px; border-left: 4px solid #10b981; margin-top: 12px;">';
                                    $content .= '<strong style="color: #065f46;">✅ Status:</strong> Keterlambatan telah diberikan toleransi';
                                    $content .= '</div>';
                                } else {
                                    $content .= '<div style="background: #fee2e2; padding: 8px 12px; border-radius: 4px; border-left: 4px solid #ef4444; margin-top: 12px;">';
                                    $content .= '<strong style="color: #991b1b;">❌ Status:</strong> Belum diberikan toleransi';
                                    $content .= '</div>';
                                }

                                $content .= '</div>';

                                return new \Illuminate\Support\HtmlString($content);
                            })
                            ->html()
                            ->visible(fn($record) => $record->status === 'terlambat')
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => $record->status === 'terlambat' || $record->is_tolerance_given),
            ]);
    }
}
