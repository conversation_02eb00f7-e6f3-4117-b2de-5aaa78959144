<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AturanKeterlambatanResource\Pages;
use App\Models\AturanKeterlambatan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Hidden;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Support\Facades\Auth;
use App\Traits\HasExportActions;
use App\Exports\AturanKeterlambatanExport;

class AturanKeterlambatanResource extends Resource
{
    use HasExportActions;

    protected static ?string $model = AturanKeterlambatan::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static ?string $navigationLabel = 'Aturan Keterlambatan';

    protected static ?string $modelLabel = 'Aturan Keterlambatan';

    protected static ?string $pluralModelLabel = 'Aturan Keterlambatan';

    protected static ?string $navigationGroup = 'Data Master';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Dasar')
                    ->schema([
                        TextInput::make('nama_aturan')
                            ->label('Nama Aturan')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Contoh: Keterlambatan 1-15 menit'),

                        TextInput::make('menit_dari')
                            ->label('Menit Dari')
                            ->required()
                            ->numeric()
                            ->minValue(0)
                            ->placeholder('0')
                            ->helperText('Batas bawah keterlambatan dalam menit'),

                        TextInput::make('menit_sampai')
                            ->label('Menit Sampai')
                            ->numeric()
                            ->minValue(1)
                            ->placeholder('Kosongkan jika tidak terbatas')
                            ->helperText('Batas atas keterlambatan dalam menit (kosongkan jika tidak terbatas)'),
                    ])->columns(3),

                Forms\Components\Section::make('Pengaturan Denda')
                    ->schema([
                        Select::make('jenis_denda')
                            ->label('Jenis Denda')
                            ->required()
                            ->options([
                                'nominal_tetap' => 'Nominal Tetap',
                                'per_menit' => 'Per Menit',
                                'persentase_gaji' => 'Persentase dari Gaji Pokok',
                            ])
                            ->default('nominal_tetap')
                            ->reactive()
                            ->afterStateUpdated(fn($state, callable $set) => [
                                $set('denda_nominal', $state === 'nominal_tetap' ? null : 0),
                                $set('denda_per_menit', $state === 'per_menit' ? null : 0),
                                $set('persentase_denda', $state === 'persentase_gaji' ? null : 0),
                            ]),

                        TextInput::make('denda_nominal')
                            ->label('Nominal Denda (Rp)')
                            ->numeric()
                            ->prefix('Rp')
                            ->visible(fn(callable $get) => $get('jenis_denda') === 'nominal_tetap')
                            ->required(fn(callable $get) => $get('jenis_denda') === 'nominal_tetap'),

                        TextInput::make('denda_per_menit')
                            ->label('Denda Per Menit (Rp)')
                            ->numeric()
                            ->prefix('Rp')
                            ->visible(fn(callable $get) => $get('jenis_denda') === 'per_menit')
                            ->required(fn(callable $get) => $get('jenis_denda') === 'per_menit'),

                        TextInput::make('persentase_denda')
                            ->label('Persentase Denda (%)')
                            ->numeric()
                            ->suffix('%')
                            ->step(0.01)
                            ->visible(fn(callable $get) => $get('jenis_denda') === 'persentase_gaji')
                            ->required(fn(callable $get) => $get('jenis_denda') === 'persentase_gaji'),
                    ])->columns(2),

                Forms\Components\Section::make('Pengaturan Lainnya')
                    ->schema([
                        Toggle::make('is_active')
                            ->label('Status Aktif')
                            ->default(true),

                        Textarea::make('keterangan')
                            ->label('Keterangan')
                            ->rows(2)
                            ->placeholder('Keterangan tambahan'),

                        Hidden::make('created_by')
                            ->default(Auth::id()),
                    ])->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('nama_aturan')
                    ->label('Nama Aturan')
                    ->searchable()
                    ->sortable()
                    ->wrap(),

                TextColumn::make('range_waktu')
                    ->label('Range Waktu')
                    ->sortable(query: function ($query, $direction) {
                        return $query->orderBy('menit_dari', $direction);
                    }),

                TextColumn::make('formatted_denda')
                    ->label('Denda')
                    ->sortable(query: function ($query, $direction) {
                        return $query->orderBy('denda_nominal', $direction);
                    }),

                IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->sortable(),

                TextColumn::make('creator.name')
                    ->label('Dibuat Oleh')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                TernaryFilter::make('is_active')
                    ->label('Status Aktif')
                    ->boolean()
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->headerActions([
                ...self::getExportActions(AturanKeterlambatanExport::class, 'Data AturanKeterlambatan'),
            ])
            ->defaultSort('menit_dari', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAturanKeterlambatans::route('/'),
            'create' => Pages\CreateAturanKeterlambatan::route('/create'),
            'view' => Pages\ViewAturanKeterlambatan::route('/{record}'),
            'edit' => Pages\EditAturanKeterlambatan::route('/{record}/edit'),
        ];
    }
}
