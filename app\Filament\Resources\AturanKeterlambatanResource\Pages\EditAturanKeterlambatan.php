<?php

namespace App\Filament\Resources\AturanKeterlambatanResource\Pages;

use App\Filament\Resources\AturanKeterlambatanResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAturanKeterlambatan extends EditRecord
{
    protected static string $resource = AturanKeterlambatanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
