<?php

namespace App\Filament\Resources\AturanKeterlambatanResource\Pages;

use App\Filament\Resources\AturanKeterlambatanResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAturanKeterlambatans extends ListRecords
{
    protected static string $resource = AturanKeterlambatanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
