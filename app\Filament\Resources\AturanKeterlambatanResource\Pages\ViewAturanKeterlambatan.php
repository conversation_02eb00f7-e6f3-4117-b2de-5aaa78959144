<?php

namespace App\Filament\Resources\AturanKeterlambatanResource\Pages;

use App\Filament\Resources\AturanKeterlambatanResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewAturanKeterlambatan extends ViewRecord
{
    protected static string $resource = AturanKeterlambatanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
