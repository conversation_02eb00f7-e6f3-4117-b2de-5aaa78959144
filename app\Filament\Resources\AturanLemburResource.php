<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AturanLemburResource\Pages;
use App\Models\AturanLembur;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class AturanLemburResource extends Resource
{
    protected static ?string $model = AturanLembur::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static ?string $navigationLabel = 'Aturan Lembur';

    protected static ?string $modelLabel = 'Aturan Lembur';

    protected static ?string $pluralModelLabel = 'Aturan Lembur';

    protected static ?string $navigationGroup = 'Manajemen Karyawan';

    protected static ?int $navigationSort = 8;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('Informasi Aturan Lembur')
                ->schema([
                    Forms\Components\TextInput::make('nama_jenis_lembur')
                        ->label('Nama Jenis Lembur')
                        ->required()
                        ->maxLength(255)
                        ->placeholder('Contoh: Lembur Hari Biasa - Jam 1'),

                    Forms\Components\Select::make('tipe_perhitungan')
                        ->label('Tipe Perhitungan')
                        ->options([
                            'per_jam' => 'Per Jam',
                            'per_hari' => 'Per Hari',
                        ])
                        ->required()
                        ->reactive()
                        ->afterStateUpdated(function ($state, callable $set) {
                            if ($state === 'per_hari') {
                                $set('jam_mulai', null);
                                $set('jam_selesai', null);
                            }
                        }),

                    Forms\Components\TextInput::make('urutan')
                        ->label('Urutan Prioritas')
                        ->numeric()
                        ->default(0)
                        ->helperText('Urutan prioritas aturan (semakin kecil semakin prioritas)'),
                ])
                ->columns(2),

            Forms\Components\Section::make('Pengaturan Jam')
                ->schema([
                    Forms\Components\TextInput::make('jam_mulai')
                        ->label('Jam Mulai')
                        ->numeric()
                        ->step(0.25)
                        ->minValue(0)
                        ->placeholder('1.00')
                        ->helperText('Jam mulai dalam format desimal (contoh: 1.5 = 1 jam 30 menit)')
                        ->visible(fn(callable $get) => $get('tipe_perhitungan') === 'per_jam'),

                    Forms\Components\TextInput::make('jam_selesai')
                        ->label('Jam Selesai')
                        ->numeric()
                        ->step(0.25)
                        ->minValue(0)
                        ->placeholder('2.00 (kosongkan untuk jam terbuka)')
                        ->helperText('Jam selesai dalam format desimal (kosongkan untuk jam terbuka)')
                        ->visible(fn(callable $get) => $get('tipe_perhitungan') === 'per_jam'),
                ])
                ->columns(2)
                ->visible(fn(callable $get) => $get('tipe_perhitungan') === 'per_jam'),

            Forms\Components\Section::make('Pengaturan Upah')
                ->schema([
                    Forms\Components\TextInput::make('multiplier')
                        ->label('Multiplier')
                        ->required()
                        ->numeric()
                        ->step(0.25)
                        ->minValue(0.25)
                        ->placeholder('1.5')
                        ->helperText('Pengali upah (contoh: 1.5 = 1.5x upah normal)'),

                    Forms\Components\TextInput::make('pembagi_upah_bulanan')
                        ->label('Pembagi Upah Bulanan')
                        ->required()
                        ->numeric()
                        ->default(30)
                        ->minValue(1)
                        ->helperText('Pembagi untuk menghitung upah harian (26 atau 30)'),
                ])
                ->columns(2),

            Forms\Components\Section::make('Pengaturan Lainnya')
                ->schema([
                    Forms\Components\Textarea::make('keterangan')
                        ->label('Keterangan')
                        ->placeholder('Keterangan tambahan tentang aturan lembur ini')
                        ->rows(3),

                    Forms\Components\Toggle::make('is_active')
                        ->label('Status Aktif')
                        ->default(true)
                        ->helperText('Aturan hanya akan berlaku jika status aktif'),
                ])
                ->columns(1),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('urutan')
                    ->label('Urutan')
                    ->sortable()
                    ->alignCenter()
                    ->width(80),

                Tables\Columns\TextColumn::make('nama_jenis_lembur')
                    ->label('Nama Jenis Lembur')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('tipe_perhitungan')
                    ->label('Tipe')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'per_jam' => 'info',
                        'per_hari' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'per_jam' => 'Per Jam',
                        'per_hari' => 'Per Hari',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('jam_range')
                    ->label('Range Jam')
                    ->placeholder('-'),

                Tables\Columns\TextColumn::make('formatted_multiplier')
                    ->label('Multiplier')
                    ->alignCenter()
                    ->weight('bold')
                    ->color('primary'),

                Tables\Columns\TextColumn::make('pembagi_upah_bulanan')
                    ->label('Pembagi Upah')
                    ->alignCenter()
                    ->formatStateUsing(fn($state) => $state . ' hari'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('tipe_perhitungan')
                    ->label('Tipe Perhitungan')
                    ->options([
                        'per_jam' => 'Per Jam',
                        'per_hari' => 'Per Hari',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif')
                    ->placeholder('Semua')
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->icon('heroicon-o-eye'),

                Tables\Actions\EditAction::make()
                    ->icon('heroicon-o-pencil-square'),

                Tables\Actions\DeleteAction::make()
                    ->icon('heroicon-o-trash'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('urutan', 'asc')
            ->emptyStateHeading('Belum ada aturan lembur')
            ->emptyStateDescription('Tambahkan aturan lembur dengan mengklik tombol "Tambah Aturan Lembur".')
            ->emptyStateIcon('heroicon-o-clock');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAturanLemburs::route('/'),
            'create' => Pages\CreateAturanLembur::route('/create'),
            'edit' => Pages\EditAturanLembur::route('/{record}/edit'),
        ];
    }
}
