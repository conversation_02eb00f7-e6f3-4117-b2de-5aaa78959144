<?php

namespace App\Filament\Resources\AturanLemburResource\Pages;

use App\Filament\Resources\AturanLemburResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAturanLemburs extends ListRecords
{
    protected static string $resource = AturanLemburResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
