<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CompanySettingsResource\Pages;
use App\Models\CompanySettings;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;

use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\TernaryFilter;

class CompanySettingsResource extends Resource
{
    protected static ?string $model = CompanySettings::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationLabel = 'Pengaturan Perusahaan';

    protected static ?string $modelLabel = 'Pengaturan Perusahaan';

    protected static ?string $pluralModelLabel = 'Pengaturan Perusahaan';

    protected static ?string $navigationGroup = 'Sistem';

    protected static ?int $navigationSort = 99;

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Special form for work period settings
                Forms\Components\Section::make('Pengaturan Periode Kerja')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                TextInput::make('work_period_start')
                                    ->label('Hari Pertama Kerja')
                                    ->helperText('Tanggal mulai periode kerja setiap bulan (1-31)')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(31)
                                    ->default(fn() => \App\Models\CompanySettings::get('work_period_start_date', 21))
                                    ->live()
                                    ->afterStateUpdated(function ($state) {
                                        \App\Models\CompanySettings::set('work_period_start_date', (int)$state, 'integer', 'Tanggal mulai periode kerja setiap bulan');
                                    }),

                                TextInput::make('work_period_end')
                                    ->label('Cut-off Date (Hari Terakhir)')
                                    ->helperText('Tanggal akhir periode kerja setiap bulan (1-31)')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(31)
                                    ->default(fn() => \App\Models\CompanySettings::get('work_period_end_date', 20))
                                    ->live()
                                    ->afterStateUpdated(function ($state) {
                                        \App\Models\CompanySettings::set('work_period_end_date', (int)$state, 'integer', 'Tanggal akhir periode kerja setiap bulan');
                                    }),
                            ]),

                        Forms\Components\Placeholder::make('period_preview')
                            ->label('Preview Periode Kerja')
                            ->content(function ($get) {
                                $startDate = $get('work_period_start') ?: \App\Models\CompanySettings::get('work_period_start_date', 21);
                                $endDate = $get('work_period_end') ?: \App\Models\CompanySettings::get('work_period_end_date', 20);

                                $now = now();
                                $currentStart = $now->day >= $startDate ? $now->copy()->day($startDate) : $now->copy()->subMonth()->day($startDate);
                                $currentEnd = $now->day >= $startDate ? $now->copy()->addMonth()->day($endDate) : $now->copy()->day($endDate);

                                return "**Periode Saat Ini:** " . $currentStart->format('d M Y') . " - " . $currentEnd->format('d M Y') . "\n\n" .
                                    "**Pengaturan:** Periode kerja dimulai tanggal **{$startDate}** dan berakhir tanggal **{$endDate}** bulan berikutnya\n\n" .
                                    "**Contoh:**\n" .
                                    "- Periode Juni: {$startDate} Juni - {$endDate} Juli\n" .
                                    "- Periode Juli: {$startDate} Juli - {$endDate} Agustus";
                            }),
                    ])
                    ->visible(fn($record) => $record === null || in_array($record?->key, ['work_period_start_date', 'work_period_end_date'])),

                // Regular form for other settings
                Forms\Components\Section::make('Pengaturan Lainnya')
                    ->schema([
                        TextInput::make('key')
                            ->label('Key')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->disabled(fn($record) => $record !== null),

                        Textarea::make('description')
                            ->label('Deskripsi')
                            ->rows(2)
                            ->maxLength(500),

                        Select::make('type')
                            ->label('Tipe Data')
                            ->required()
                            ->options([
                                'string' => 'String',
                                'integer' => 'Integer',
                                'float' => 'Float',
                                'boolean' => 'Boolean',
                                'array' => 'Array',
                                'json' => 'JSON',
                            ])
                            ->default('string')
                            ->live(),

                        Toggle::make('is_active')
                            ->label('Aktif')
                            ->default(true),

                        // Value fields based on type
                        TextInput::make('value')
                            ->label('Nilai')
                            ->visible(fn($get) => in_array($get('type'), ['string']))
                            ->maxLength(255),

                        TextInput::make('value')
                            ->label('Nilai')
                            ->visible(fn($get) => $get('type') === 'integer')
                            ->numeric()
                            ->integer(),

                        TextInput::make('value')
                            ->label('Nilai')
                            ->visible(fn($get) => $get('type') === 'float')
                            ->numeric(),

                        Toggle::make('value')
                            ->label('Nilai')
                            ->visible(fn($get) => $get('type') === 'boolean'),

                        Textarea::make('value')
                            ->label('Nilai (JSON)')
                            ->visible(fn($get) => in_array($get('type'), ['array', 'json']))
                            ->rows(4)
                            ->helperText('Masukkan data dalam format JSON yang valid'),
                    ])
                    ->visible(fn($record) => $record !== null && !in_array($record?->key, ['work_period_start_date', 'work_period_end_date'])),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('key')
                    ->label('Key')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                TextColumn::make('description')
                    ->label('Deskripsi')
                    ->searchable()
                    ->limit(50)
                    ->wrap(),

                TextColumn::make('type')
                    ->label('Tipe')
                    ->badge()
                    ->color(fn($state) => match ($state) {
                        'string' => 'primary',
                        'integer' => 'success',
                        'float' => 'warning',
                        'boolean' => 'info',
                        'array', 'json' => 'secondary',
                        default => 'gray',
                    }),

                TextColumn::make('value')
                    ->label('Nilai')
                    ->formatStateUsing(function ($state, $record) {
                        if ($record->type === 'boolean') {
                            return $state ? 'Ya' : 'Tidak';
                        }
                        if (in_array($record->type, ['array', 'json'])) {
                            return is_array($state) ? json_encode($state) : $state;
                        }
                        return $state;
                    })
                    ->limit(30)
                    ->copyable(),

                IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->sortable(),

                TextColumn::make('updated_at')
                    ->label('Diperbarui')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                TernaryFilter::make('is_active')
                    ->label('Status Aktif')
                    ->boolean()
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('preview_work_period')
                    ->label('Preview Periode')
                    ->icon('heroicon-o-calendar-days')
                    ->color('info')
                    ->visible(fn($record) => in_array($record->key, ['work_period_start_date', 'work_period_end_date']))
                    ->action(function ($record) {
                        $workPeriodInfo = \App\Models\CompanySettings::getWorkPeriodInfo();

                        \Filament\Notifications\Notification::make()
                            ->title('Preview Periode Kerja')
                            ->body(
                                "**Periode Saat Ini:** {$workPeriodInfo['period_label']}\n\n" .
                                    "**Pengaturan:** {$workPeriodInfo['description']}\n\n" .
                                    "**Hari Pertama Kerja:** {$workPeriodInfo['start_date']}\n" .
                                    "**Cut-off Date:** {$workPeriodInfo['end_date']}"
                            )
                            ->info()
                            ->persistent()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('key', 'asc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompanySettings::route('/'),
            'create' => Pages\CreateCompanySettings::route('/create'),
            'edit' => Pages\EditCompanySettings::route('/{record}/edit'),
        ];
    }
}
