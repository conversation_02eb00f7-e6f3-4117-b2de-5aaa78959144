<?php

namespace App\Filament\Resources\CutiIzinResource\Actions;

use App\Services\WhatsAppService;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Exception;

class WhatsAppCutiActions
{
    /**
     * Create approve action with WhatsApp notification
     */
    public static function approveWithWhatsApp(): Action
    {
        return Action::make('approve')
            ->label('Setujui')
            ->icon('heroicon-o-check')
            ->color('success')
            ->visible(function ($record) {
                $user = Auth::user();
                $allowedRoles = ['admin', 'manager', 'supervisor', 'keptok'];
                $allowedShieldRoles = ['manager_hrd', 'super_admin'];

                return (in_array($user->role, $allowedRoles) || $user->hasAnyRole($allowedShieldRoles))
                    && $record->status === 'pending';
            })
            ->action(function ($record) {
                // Update status
                $record->update([
                    'status' => 'approved',
                    'approved_by' => Auth::id(),
                    'approved_at' => now(),
                    'rejection_reason' => null,
                ]);

                // Send Filament notification
                Notification::make()
                    ->title('Permohonan berhasil disetujui')
                    ->body('Permohonan ' . $record->jenis_permohonan_label . ' dari ' . $record->karyawan->nama_lengkap . ' telah disetujui.')
                    ->success()
                    ->send();

                // Send WhatsApp notification
                self::sendWhatsAppNotification($record, 'approved');
            });
    }

    /**
     * Create reject action with WhatsApp notification
     */
    public static function rejectWithWhatsApp(): Action
    {
        return Action::make('reject')
            ->label('Tolak')
            ->icon('heroicon-o-x-mark')
            ->color('danger')
            ->visible(function ($record) {
                $user = Auth::user();
                $allowedRoles = ['admin', 'manager', 'supervisor', 'keptok'];
                $allowedShieldRoles = ['manager_hrd', 'super_admin'];

                return (in_array($user->role, $allowedRoles) || $user->hasAnyRole($allowedShieldRoles))
                    && $record->status === 'pending';
            })
            ->form([
                \Filament\Forms\Components\Textarea::make('rejection_reason')
                    ->label('Alasan Penolakan')
                    ->required()
                    ->maxLength(1000)
                    ->placeholder('Jelaskan alasan penolakan permohonan ini'),
            ])
            ->action(function ($record, array $data) {
                // Update status
                $record->update([
                    'status' => 'rejected',
                    'approved_by' => Auth::id(),
                    'approved_at' => now(),
                    'rejection_reason' => $data['rejection_reason'],
                ]);

                // Send Filament notification
                Notification::make()
                    ->title('Permohonan berhasil ditolak')
                    ->body('Permohonan ' . $record->jenis_permohonan_label . ' dari ' . $record->karyawan->nama_lengkap . ' telah ditolak.')
                    ->success()
                    ->send();

                // Send WhatsApp notification
                self::sendWhatsAppNotification($record, 'rejected', $data['rejection_reason']);
            });
    }

    /**
     * Send WhatsApp notification for cuti/izin status update
     */
    protected static function sendWhatsAppNotification($record, string $status, string $rejectionReason = ''): void
    {
        try {
            $whatsAppService = app(WhatsAppService::class);
            
            if (!$whatsAppService->isEnabled()) {
                return;
            }

            $karyawan = $record->karyawan;
            $phoneNumber = $karyawan->no_hp ?? null;

            if (empty($phoneNumber)) {
                return; // Skip if no phone number
            }

            // Create message based on status
            $message = self::createMessage($record, $status, $rejectionReason);

            // Send WhatsApp message
            $result = $whatsAppService->sendMessage($phoneNumber, $message);

            if ($result['success']) {
                Notification::make()
                    ->title('WhatsApp Terkirim')
                    ->body("Notifikasi WhatsApp berhasil dikirim ke {$karyawan->nama_lengkap}")
                    ->info()
                    ->send();
            }
        } catch (Exception $e) {
            // Log error but don't interrupt the main process
            \Illuminate\Support\Facades\Log::error('WhatsApp notification failed', [
                'record_id' => $record->id,
                'karyawan_id' => $record->karyawan_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Create WhatsApp message content
     */
    protected static function createMessage($record, string $status, string $rejectionReason = ''): string
    {
        $karyawan = $record->karyawan;
        $jenisPermohonan = $record->jenis_permohonan_label;
        $tanggalMulai = $record->tanggal_mulai->format('d/m/Y');
        $tanggalSelesai = $record->tanggal_selesai->format('d/m/Y');

        $baseMessage = "Halo {$karyawan->nama_lengkap},\n\n";
        $baseMessage .= "Permohonan {$jenisPermohonan} Anda untuk tanggal {$tanggalMulai}";
        
        if ($tanggalMulai !== $tanggalSelesai) {
            $baseMessage .= " s/d {$tanggalSelesai}";
        }

        if ($status === 'approved') {
            $baseMessage .= " telah *DISETUJUI*.\n\n";
            $baseMessage .= "Silakan koordinasi dengan atasan langsung Anda untuk pengaturan lebih lanjut.\n\n";
        } else {
            $baseMessage .= " telah *DITOLAK*.\n\n";
            if (!empty($rejectionReason)) {
                $baseMessage .= "Alasan: {$rejectionReason}\n\n";
            }
            $baseMessage .= "Silakan hubungi HRD untuk informasi lebih lanjut.\n\n";
        }

        $baseMessage .= "Terima kasih.\n";
        $baseMessage .= "Tim HRD";

        return $baseMessage;
    }

    /**
     * Create manual WhatsApp notification action
     */
    public static function manualWhatsAppAction(): Action
    {
        return Action::make('sendWhatsApp')
            ->label('Kirim WhatsApp')
            ->icon('heroicon-o-chat-bubble-left-right')
            ->color('info')
            ->form([
                \Filament\Forms\Components\Textarea::make('message')
                    ->label('Pesan WhatsApp')
                    ->required()
                    ->rows(5)
                    ->default(function ($record) {
                        $karyawan = $record->karyawan;
                        $jenisPermohonan = $record->jenis_permohonan_label;
                        $status = match($record->status) {
                            'pending' => 'sedang diproses',
                            'approved' => 'telah disetujui',
                            'rejected' => 'telah ditolak',
                            default => 'dalam status ' . $record->status
                        };

                        return "Halo {$karyawan->nama_lengkap},\n\nPermohonan {$jenisPermohonan} Anda {$status}.\n\nTerima kasih.\nTim HRD";
                    }),
            ])
            ->action(function ($record, array $data) {
                try {
                    $whatsAppService = app(WhatsAppService::class);
                    $karyawan = $record->karyawan;
                    $phoneNumber = $karyawan->no_hp ?? null;

                    if (empty($phoneNumber)) {
                        Notification::make()
                            ->title('Nomor WhatsApp Tidak Tersedia')
                            ->body("Karyawan {$karyawan->nama_lengkap} tidak memiliki nomor WhatsApp")
                            ->warning()
                            ->send();
                        return;
                    }

                    $result = $whatsAppService->sendMessage($phoneNumber, $data['message']);

                    if ($result['success']) {
                        Notification::make()
                            ->title('WhatsApp Terkirim')
                            ->body("Pesan berhasil dikirim ke {$karyawan->nama_lengkap}")
                            ->success()
                            ->send();
                    } else {
                        Notification::make()
                            ->title('Gagal Mengirim WhatsApp')
                            ->body($result['error'] ?? 'Terjadi kesalahan saat mengirim pesan')
                            ->danger()
                            ->send();
                    }
                } catch (Exception $e) {
                    Notification::make()
                        ->title('Error WhatsApp')
                        ->body('Terjadi kesalahan: ' . $e->getMessage())
                        ->danger()
                        ->send();
                }
            })
            ->visible(function ($record) {
                $whatsAppService = app(WhatsAppService::class);
                return $whatsAppService->isEnabled() && !empty($record->karyawan->no_hp);
            });
    }
}
