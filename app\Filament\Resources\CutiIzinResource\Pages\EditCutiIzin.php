<?php

namespace App\Filament\Resources\CutiIzinResource\Pages;

use App\Filament\Resources\CutiIzinResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCutiIzin extends EditRecord
{
    protected static string $resource = CutiIzinResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function getTitle(): string
    {
        return 'Edit Permohonan Cuti/Izin';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Validate dates and quota before updating
        $tempModel = new \App\Models\CutiIzin(array_merge($this->record->toArray(), $data));
        $tempModel->id = $this->record->id; // Set ID for update validation
        $errors = $tempModel->validateDates();

        if (!empty($errors)) {
            foreach ($errors as $error) {
                // Check if it's a warning (monthly quota) or blocking error
                if (str_contains($error, '⚠️ Peringatan:')) {
                    \Filament\Notifications\Notification::make()
                        ->title('Peringatan Kuota Bulanan')
                        ->body($error)
                        ->warning()
                        ->send();
                } else {
                    \Filament\Notifications\Notification::make()
                        ->title('Validasi Gagal')
                        ->body($error)
                        ->danger()
                        ->send();

                    $this->halt();
                }
            }
        }

        return $data;
    }
}
