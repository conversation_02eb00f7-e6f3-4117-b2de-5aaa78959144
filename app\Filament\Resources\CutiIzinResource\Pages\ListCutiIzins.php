<?php

namespace App\Filament\Resources\CutiIzinResource\Pages;

use App\Filament\Resources\CutiIzinResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCutiIzins extends ListRecords
{
    protected static string $resource = CutiIzinResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Tambah Permohonan')
                ->icon('heroicon-o-plus'),
        ];
    }

    public function getTitle(): string
    {
        return 'Manajemen Cuti dan Izin';
    }
}
