<?php

namespace App\Filament\Resources\CutiIzinResource\Pages;

use App\Filament\Resources\CutiIzinResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewCutiIzin extends ViewRecord
{
    protected static string $resource = CutiIzinResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // acc cuti
            Actions\Action::make('acc_cuti')
                ->label('ACC Cuti')
                ->icon('heroicon-o-check')
                ->color('success')
                ->visible(fn() => $this->record->status === 'pending')
                ->requiresConfirmation()
                ->action(fn() => $this->record->update(
                    [
                        'status' => 'approved',
                        'approved_by' => auth()->id(),
                        'approved_at' => now()
                    ]
                )),
            Actions\EditAction::make(),
        ];
    }

    public function getTitle(): string
    {
        return 'Detail Permohonan Cuti/Izin';
    }
}
