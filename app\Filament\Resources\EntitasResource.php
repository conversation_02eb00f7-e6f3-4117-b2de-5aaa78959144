<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EntitasResource\Pages;
use App\Models\Entitas;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Traits\HasExportActions;
use App\Exports\EntitasExport;

class EntitasResource extends Resource
{
    use HasExportActions;

    protected static ?string $model = Entitas::class;

    protected static ?string $navigationGroup = 'Data Master';

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Entitas')
                    ->schema([
                        TextInput::make('nama')
                            ->label('Nama Entitas')
                            ->required()
                            ->maxLength(100),

                        Textarea::make('alamat')
                            ->label('Alamat')
                            ->rows(3),

                        Textarea::make('keterangan')
                            ->label('Keterangan Tambahan')
                            ->rows(2),
                    ])
                    ->columns(1),

                Section::make('Geofencing untuk Absensi')
                    ->description('Atur lokasi dan radius untuk validasi absensi karyawan')
                    ->schema([
                        Toggle::make('enable_geofencing')
                            ->label('Aktifkan Geofencing')
                            ->helperText('Jika diaktifkan, karyawan hanya bisa absen dalam radius yang ditentukan')
                            ->default(true)
                            ->live(),

                        Grid::make(3)
                            ->schema([
                                TextInput::make('latitude')
                                    ->label('Latitude')
                                    ->numeric()
                                    ->step(0.00000000000000001)
                                    ->placeholder('Contoh: -6.2088')
                                    ->helperText('Koordinat latitude lokasi entitas')
                                    ->visible(fn($get) => $get('enable_geofencing')),

                                TextInput::make('longitude')
                                    ->label('Longitude')
                                    ->numeric()
                                    ->step(0.00000000000000001)
                                    ->placeholder('Contoh: 106.8456')
                                    ->helperText('Koordinat longitude lokasi entitas')
                                    ->visible(fn($get) => $get('enable_geofencing')),

                                TextInput::make('radius')
                                    ->label('Radius (meter)')
                                    ->numeric()
                                    ->default(100)
                                    ->minValue(10)
                                    ->maxValue(1000)
                                    ->suffix('meter')
                                    ->helperText('Jarak maksimal yang diperbolehkan untuk absensi')
                                    ->visible(fn($get) => $get('enable_geofencing')),
                            ]),

                        \Filament\Forms\Components\Actions::make([
                            \Filament\Forms\Components\Actions\Action::make('geocode_address')
                                ->label('📍 Dapatkan Koordinat dari Alamat')
                                ->color('info')
                                ->action(function ($set, $get) {
                                    $alamat = $get('../../alamat'); // Get alamat from parent section

                                    if (!$alamat) {
                                        \Filament\Notifications\Notification::make()
                                            ->title('Alamat Diperlukan')
                                            ->body('Mohon isi alamat terlebih dahulu untuk mendapatkan koordinat otomatis.')
                                            ->warning()
                                            ->send();
                                        return;
                                    }

                                    // Simple geocoding using Nominatim (OpenStreetMap)
                                    try {
                                        $encodedAddress = urlencode($alamat);
                                        $url = "https://nominatim.openstreetmap.org/search?format=json&q={$encodedAddress}&limit=1";

                                        $context = stream_context_create([
                                            'http' => [
                                                'header' => "User-Agent: Viera-Filament-App/1.0\r\n"
                                            ]
                                        ]);

                                        $response = file_get_contents($url, false, $context);
                                        $data = json_decode($response, true);

                                        if (!empty($data) && isset($data[0]['lat']) && isset($data[0]['lon'])) {
                                            $lat = round((float)$data[0]['lat'], 8);
                                            $lon = round((float)$data[0]['lon'], 8);

                                            $set('latitude', $lat);
                                            $set('longitude', $lon);

                                            \Filament\Notifications\Notification::make()
                                                ->title('Koordinat Berhasil Ditemukan!')
                                                ->body("Latitude: {$lat}, Longitude: {$lon}")
                                                ->success()
                                                ->send();
                                        } else {
                                            \Filament\Notifications\Notification::make()
                                                ->title('Koordinat Tidak Ditemukan')
                                                ->body('Tidak dapat menemukan koordinat untuk alamat tersebut. Mohon input manual.')
                                                ->warning()
                                                ->send();
                                        }
                                    } catch (\Exception $e) {
                                        \Filament\Notifications\Notification::make()
                                            ->title('Error Geocoding')
                                            ->body('Terjadi kesalahan saat mencari koordinat: ' . $e->getMessage())
                                            ->danger()
                                            ->send();
                                    }
                                })
                                ->visible(fn($get) => $get('enable_geofencing')),
                        ])
                            ->visible(fn($get) => $get('enable_geofencing')),

                        \Filament\Forms\Components\Placeholder::make('geofencing_info')
                            ->label('')
                            ->content('💡 Tips: Gunakan tombol "Dapatkan Koordinat dari Alamat" atau input manual dari Google Maps (klik kanan pada lokasi → pilih koordinat).')
                            ->visible(fn($get) => $get('enable_geofencing')),

                        \Filament\Forms\Components\Placeholder::make('map_preview')
                            ->label('Preview Lokasi')
                            ->content(function ($get) {
                                $latitude = $get('latitude');
                                $longitude = $get('longitude');
                                $radius = $get('radius') ?? 100;

                                if (!$latitude || !$longitude) {
                                    return new \Illuminate\Support\HtmlString('
                                        <div style="background: #f8fafc; border: 2px dashed #cbd5e1; border-radius: 8px; padding: 40px; text-align: center; color: #64748b;">
                                            <div style="font-size: 48px; margin-bottom: 16px;">🗺️</div>
                                            <div style="font-weight: 600; margin-bottom: 8px;">Preview Maps</div>
                                            <div style="font-size: 14px;">Masukkan latitude dan longitude untuk melihat preview lokasi</div>
                                        </div>
                                    ');
                                }

                                // Validate coordinates
                                if (
                                    !is_numeric($latitude) || !is_numeric($longitude) ||
                                    $latitude < -90 || $latitude > 90 ||
                                    $longitude < -180 || $longitude > 180
                                ) {
                                    return new \Illuminate\Support\HtmlString('
                                        <div style="background: #fef2f2; border: 2px solid #fecaca; border-radius: 8px; padding: 20px; text-align: center; color: #dc2626;">
                                            <div style="font-size: 24px; margin-bottom: 8px;">⚠️</div>
                                            <div style="font-weight: 600;">Koordinat Tidak Valid</div>
                                            <div style="font-size: 14px; margin-top: 4px;">
                                                Latitude: -90 to 90<br>
                                                Longitude: -180 to 180
                                            </div>
                                        </div>
                                    ');
                                }

                                $googleMapsUrl = "https://www.google.com/maps?q={$latitude},{$longitude}";
                                $openStreetMapUrl = "https://www.openstreetmap.org/export/embed.html?bbox=" .
                                    ($longitude - 0.005) . "%2C" . ($latitude - 0.005) . "%2C" .
                                    ($longitude + 0.005) . "%2C" . ($latitude + 0.005) .
                                    "&amp;layer=mapnik&amp;marker={$latitude}%2C{$longitude}";

                                return new \Illuminate\Support\HtmlString('
                                    <div style="border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden; background: white;">
                                        <!-- Map Header -->
                                        <div style="background: #f8fafc; padding: 12px 16px; border-bottom: 1px solid #e5e7eb;">
                                            <div style="display: flex; justify-content: between; align-items: center; flex-wrap: wrap; gap: 12px;">
                                                <div>
                                                    <div style="font-weight: 600; color: #1e293b; margin-bottom: 4px;">📍 Preview Lokasi</div>
                                                    <div style="font-size: 13px; color: #64748b; font-family: monospace;">
                                                        ' . $latitude . ', ' . $longitude . ' (Radius: ' . $radius . 'm)
                                                    </div>
                                                </div>
                                                <div style="display: flex; gap: 8px;">
                                                    <a href="' . $googleMapsUrl . '" target="_blank"
                                                       style="display: inline-flex; align-items: center; gap: 4px; padding: 6px 12px;
                                                              background: #3b82f6; color: white; text-decoration: none; border-radius: 6px;
                                                              font-size: 12px; font-weight: 500;">
                                                        🗺️ Google Maps
                                                    </a>
                                                    <button type="button" onclick="copyCoordinates(\'' . $latitude . ', ' . $longitude . '\')"
                                                            style="display: inline-flex; align-items: center; gap: 4px; padding: 6px 12px;
                                                                   background: #10b981; color: white; border: none; border-radius: 6px;
                                                                   font-size: 12px; font-weight: 500; cursor: pointer;">
                                                        📋 Copy
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Map Container -->
                                        <div style="position: relative; height: 300px;">
                                            <iframe src="' . $openStreetMapUrl . '"
                                                    style="width: 100%; height: 100%; border: none;"
                                                    allowfullscreen>
                                            </iframe>

                                            <!-- Radius Indicator -->
                                            <div style="position: absolute; top: 12px; left: 12px;
                                                        background: rgba(59, 130, 246, 0.9); color: white;
                                                        padding: 6px 10px; border-radius: 6px; font-size: 12px; font-weight: 500;">
                                                📏 Radius: ' . $radius . 'm
                                            </div>

                                            <!-- Coordinates Display -->
                                            <div style="position: absolute; bottom: 12px; left: 12px;
                                                        background: rgba(0, 0, 0, 0.8); color: white;
                                                        padding: 6px 10px; border-radius: 6px; font-size: 11px; font-family: monospace;">
                                                📍 ' . $latitude . ', ' . $longitude . '
                                            </div>
                                        </div>

                                        <!-- Map Footer -->
                                        <div style="background: #f8fafc; padding: 12px 16px; border-top: 1px solid #e5e7eb;">
                                            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 8px;">
                                                <div style="font-size: 12px; color: #64748b;">
                                                    🎯 Area absensi dalam radius ' . $radius . ' meter dari titik ini
                                                </div>
                                                <div style="font-size: 11px; color: #9ca3af;">
                                                    Powered by OpenStreetMap
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <script>
                                    function copyCoordinates(coords) {
                                        navigator.clipboard.writeText(coords).then(function() {
                                            // Create temporary notification
                                            const notification = document.createElement("div");
                                            notification.style.cssText = `
                                                position: fixed; top: 20px; right: 20px; z-index: 9999;
                                                background: #10b981; color: white; padding: 12px 16px;
                                                border-radius: 8px; font-size: 14px; font-weight: 500;
                                                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                                            `;
                                            notification.textContent = "📋 Koordinat berhasil dicopy!";
                                            document.body.appendChild(notification);

                                            setTimeout(() => {
                                                notification.remove();
                                            }, 2000);
                                        });
                                    }
                                    </script>
                                ');
                            })
                            ->visible(fn($get) => $get('enable_geofencing'))
                            ->columnSpanFull(),
                    ])
                    ->columns(1)
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('nama')
                    ->label('Nama Entitas')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('alamat')
                    ->label('Alamat')
                    ->limit(30)
                    ->tooltip(fn($record) => $record->alamat),

                TextColumn::make('coordinates')
                    ->label('Koordinat')
                    ->getStateUsing(fn($record) => $record->coordinates ?? '—')
                    ->copyable()
                    ->tooltip('Klik untuk copy koordinat'),

                TextColumn::make('radius')
                    ->label('Radius')
                    ->getStateUsing(fn($record) => $record->radius ? $record->radius . 'm' : '—')
                    ->alignCenter(),

                \Filament\Tables\Columns\IconColumn::make('enable_geofencing')
                    ->label('Geofencing')
                    ->boolean()
                    ->trueIcon('heroicon-o-shield-check')
                    ->falseIcon('heroicon-o-shield-exclamation')
                    ->trueColor('success')
                    ->falseColor('warning')
                    ->tooltip(fn($record) => $record->enable_geofencing ? 'Geofencing aktif' : 'Geofencing nonaktif'),

                TextColumn::make('karyawan_count')
                    ->label('Karyawan')
                    ->counts('karyawan')
                    ->badge()
                    ->color('info')
                    ->alignCenter(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->headerActions([
                ...self::getExportActions(EntitasExport::class, 'Data Entitas'),
            ])
            ->defaultSort('nama');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEntitas::route('/'),
        ];
    }
}
