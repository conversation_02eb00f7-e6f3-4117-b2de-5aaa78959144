<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ExpenseRequestResource\Pages;
use App\Filament\Resources\ExpenseRequestResource\RelationManagers;
use App\Models\ExpenseRequest;
use App\Models\Karyawan;
use App\Models\Entitas;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ExpenseRequestResource extends Resource
{
    protected static ?string $model = ExpenseRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'Expense Requests';

    protected static ?string $navigationGroup = 'Expense Management';

    protected static ?int $navigationSort = 2;

    // has access superadmin
    public static function canAccess(): bool
    {
        return auth()->user()->hasRole(['super_admin', 'direktur', 'manager_accounting']);
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Request Information')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('request_number')
                                    ->label('Request Number')
                                    ->disabled()
                                    ->dehydrated(false)
                                    ->placeholder('Auto-generated'),
                                Forms\Components\DatePicker::make('request_date')
                                    ->label('Request Date')
                                    ->required()
                                    ->default(now()),
                                Forms\Components\Select::make('status')
                                    ->label('Status')
                                    ->options([
                                        'Draft' => 'Draft',
                                        'Submitted' => 'Submitted',
                                        'Approved' => 'Approved',
                                        'Rejected' => 'Rejected',
                                        'Paid' => 'Paid',
                                        'Cancelled' => 'Cancelled',
                                    ])
                                    ->default('Draft')
                                    ->required(),
                            ]),
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Select::make('employee_id')
                                    ->label('Employee')
                                    ->options(Karyawan::all()->pluck('nama_lengkap', 'id'))
                                    ->required()
                                    ->searchable()
                                    ->preload(),
                                Forms\Components\Select::make('entitas_id')
                                    ->label('Entitas')
                                    ->options(Entitas::all()->pluck('nama', 'id'))
                                    ->required()
                                    ->searchable()
                                    ->preload(),
                                Forms\Components\Select::make('expense_type')
                                    ->label('Expense Type')
                                    ->options([
                                        'Petty_Cash' => 'Petty Cash',
                                        'Reimbursement' => 'Reimbursement',
                                        'Advance' => 'Advance Payment',
                                    ])
                                    ->required(),
                            ]),
                        Forms\Components\Textarea::make('purpose')
                            ->label('Purpose')
                            ->required()
                            ->rows(2)
                            ->columnSpanFull(),
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->rows(2)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Approval Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('total_amount')
                                    ->label('Total Amount')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->disabled()
                                    ->dehydrated(false),
                                Forms\Components\Select::make('approved_by')
                                    ->label('Approved By')
                                    ->relationship('approvedBy', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->disabled(),
                            ]),
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Rejection Reason')
                            ->rows(2)
                            ->columnSpanFull()
                            ->visible(fn ($record) => $record && $record->status === 'Rejected'),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('request_number')
                    ->label('Request #')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('request_date')
                    ->label('Date')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('employee.nama_lengkap')
                    ->label('Employee')
                    ->searchable(),
                Tables\Columns\TextColumn::make('entitas.nama')
                    ->label('Entitas')
                    ->searchable(),
                Tables\Columns\BadgeColumn::make('expense_type')
                    ->label('Type')
                    ->colors([
                        'info' => 'Petty_Cash',
                        'success' => 'Reimbursement',
                        'warning' => 'Advance',
                    ]),
                Tables\Columns\TextColumn::make('total_amount')
                    ->label('Amount')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status')
                    ->colors([
                        'gray' => 'Draft',
                        'warning' => 'Submitted',
                        'success' => ['Approved', 'Paid'],
                        'danger' => ['Rejected', 'Cancelled'],
                    ]),
                Tables\Columns\TextColumn::make('purpose')
                    ->label('Purpose')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'Draft' => 'Draft',
                        'Submitted' => 'Submitted',
                        'Approved' => 'Approved',
                        'Rejected' => 'Rejected',
                        'Paid' => 'Paid',
                        'Cancelled' => 'Cancelled',
                    ]),
                Tables\Filters\SelectFilter::make('expense_type')
                    ->options([
                        'Petty_Cash' => 'Petty Cash',
                        'Reimbursement' => 'Reimbursement',
                        'Advance' => 'Advance Payment',
                    ]),
                Tables\Filters\SelectFilter::make('employee')
                    ->relationship('employee', 'nama_lengkap'),
                Tables\Filters\SelectFilter::make('entitas')
                    ->relationship('entitas', 'nama'),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn (ExpenseRequest $record) => $record->isEditable()),
                Tables\Actions\Action::make('submit')
                    ->label('Submit')
                    ->icon('heroicon-o-paper-airplane')
                    ->color('warning')
                    ->visible(fn (ExpenseRequest $record) => $record->canBeSubmitted())
                    ->requiresConfirmation()
                    ->action(function (ExpenseRequest $record) {
                        $record->submit();
                    }),
                Tables\Actions\Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn (ExpenseRequest $record) => $record->canBeApproved())
                    ->requiresConfirmation()
                    ->action(function (ExpenseRequest $record) {
                        $record->approve();
                    }),
                Tables\Actions\Action::make('reject')
                    ->label('Reject')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->visible(fn (ExpenseRequest $record) => $record->canBeRejected())
                    ->form([
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Rejection Reason')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function (ExpenseRequest $record, array $data) {
                        $record->reject($data['rejection_reason']);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('request_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ExpenseRequestItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListExpenseRequests::route('/'),
            'create' => Pages\CreateExpenseRequest::route('/create'),
            'view' => Pages\ViewExpenseRequest::route('/{record}'),
            'edit' => Pages\EditExpenseRequest::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
