<?php

namespace App\Filament\Resources\ExpenseRequestResource\Pages;

use App\Filament\Resources\ExpenseRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Forms;

class ViewExpenseRequest extends ViewRecord
{
    protected static string $resource = ExpenseRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn () => $this->getRecord()->isEditable()),
            Actions\Action::make('submit')
                ->label('Submit')
                ->icon('heroicon-o-paper-airplane')
                ->color('warning')
                ->visible(fn () => $this->getRecord()->canBeSubmitted())
                ->requiresConfirmation()
                ->action(function () {
                    $record = $this->getRecord();
                    $record->submit();
                    
                    $this->refreshFormData(['status']);
                }),
            Actions\Action::make('approve')
                ->label('Approve')
                ->icon('heroicon-o-check')
                ->color('success')
                ->visible(fn () => $this->getRecord()->canBeApproved())
                ->requiresConfirmation()
                ->action(function () {
                    $record = $this->getRecord();
                    $record->approve();
                    
                    $this->refreshFormData(['status', 'approved_by', 'approved_at']);
                }),
            Actions\Action::make('reject')
                ->label('Reject')
                ->icon('heroicon-o-x-mark')
                ->color('danger')
                ->visible(fn () => $this->getRecord()->canBeRejected())
                ->form([
                    Forms\Components\Textarea::make('rejection_reason')
                        ->label('Rejection Reason')
                        ->required()
                        ->rows(3),
                ])
                ->action(function (array $data) {
                    $record = $this->getRecord();
                    $record->reject($data['rejection_reason']);
                    
                    $this->refreshFormData(['status', 'rejected_by', 'rejected_at', 'rejection_reason']);
                }),
        ];
    }
}
