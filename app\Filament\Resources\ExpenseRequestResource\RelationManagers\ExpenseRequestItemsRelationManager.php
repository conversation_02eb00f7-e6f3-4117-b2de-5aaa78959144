<?php

namespace App\Filament\Resources\ExpenseRequestResource\RelationManagers;

use App\Models\ExpenseCategory;
use App\Models\Akun;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ExpenseRequestItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'expenseRequestItems';

    protected static ?string $title = 'Expense Items';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('expense_category_id')
                    ->label('Expense Category')
                    ->options(ExpenseCategory::active()->get()->pluck('display_name', 'id'))
                    ->required()
                    ->searchable()
                    ->preload()
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if ($state) {
                            $category = ExpenseCategory::find($state);
                            if ($category) {
                                $set('account_id', $category->default_account_id);
                            }
                        }
                    }),
                Forms\Components\TextInput::make('description')
                    ->label('Description')
                    ->required()
                    ->maxLength(255)
                    ->columnSpanFull(),
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('amount')
                            ->label('Amount')
                            ->numeric()
                            ->required()
                            ->prefix('Rp')
                            ->minValue(1),
                        Forms\Components\DatePicker::make('expense_date')
                            ->label('Expense Date')
                            ->required()
                            ->default(now()),
                    ]),
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('receipt_number')
                            ->label('Receipt Number')
                            ->maxLength(255),
                        Forms\Components\Select::make('account_id')
                            ->label('Account')
                            ->options(Akun::where('kategori_akun', 'Beban')->pluck('nama_akun', 'id'))
                            ->required()
                            ->searchable()
                            ->preload(),
                    ]),
                Forms\Components\FileUpload::make('receipt_file_path')
                    ->label('Receipt File')
                    ->image()
                    ->directory('expense-receipts')
                    ->visibility('private')
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('notes')
                    ->label('Notes')
                    ->rows(2)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('description')
            ->columns([
                Tables\Columns\TextColumn::make('expenseCategory.name')
                    ->label('Category')
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')
                    ->label('Description')
                    ->searchable()
                    ->wrap(),
                Tables\Columns\TextColumn::make('amount')
                    ->label('Amount')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('expense_date')
                    ->label('Date')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('receipt_number')
                    ->label('Receipt #')
                    ->searchable(),
                Tables\Columns\BadgeColumn::make('receipt_status')
                    ->label('Receipt')
                    ->colors([
                        'success' => ['Complete', 'Available'],
                        'danger' => 'Missing',
                        'gray' => 'Not Required',
                    ]),
                Tables\Columns\TextColumn::make('account.nama_akun')
                    ->label('Account')
                    ->searchable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('expense_category')
                    ->relationship('expenseCategory', 'name'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->visible(fn () => $this->getOwnerRecord()->isEditable()),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->visible(fn () => $this->getOwnerRecord()->isEditable()),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn () => $this->getOwnerRecord()->isEditable()),
                Tables\Actions\Action::make('download_receipt')
                    ->label('Download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('info')
                    ->visible(fn ($record) => $record->hasReceipt())
                    ->url(fn ($record) => route('expense.receipt.download', $record->id))
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn () => $this->getOwnerRecord()->isEditable()),
                ]),
            ]);
    }
}
