<?php

namespace App\Filament\Resources\GoodsReceiptResource\Pages;

use App\Filament\Resources\GoodsReceiptResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewGoodsReceipt extends ViewRecord
{
    protected static string $resource = GoodsReceiptResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn () => $this->getRecord()->isEditable()),
            Actions\Action::make('complete')
                ->label('Complete')
                ->icon('heroicon-o-check')
                ->color('success')
                ->visible(fn () => $this->getRecord()->canBeCompleted())
                ->requiresConfirmation()
                ->action(function () {
                    $record = $this->getRecord();
                    $record->complete();
                    
                    $this->refreshFormData(['status']);
                }),
            Actions\Action::make('cancel')
                ->label('Cancel')
                ->icon('heroicon-o-x-mark')
                ->color('danger')
                ->visible(fn () => $this->getRecord()->canBeCancelled())
                ->requiresConfirmation()
                ->action(function () {
                    $record = $this->getRecord();
                    $record->status = 'Cancelled';
                    $record->save();
                    
                    $this->refreshFormData(['status']);
                }),
        ];
    }
}
