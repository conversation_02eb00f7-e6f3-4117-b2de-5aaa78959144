<?php

namespace App\Filament\Resources;

use App\Filament\Resources\InventoryStockResource\Pages;
use App\Models\InventoryStock;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class InventoryStockResource extends Resource
{
    protected static ?string $model = InventoryStock::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel = 'Stok Inventaris';

    protected static ?string $modelLabel = 'Stok Inventaris';

    protected static ?string $pluralModelLabel = 'Stok Inventaris';

    protected static ?string $navigationGroup = 'Inventaris';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('product_id')
                    ->label('Produk')
                    ->relationship('product', 'nama')
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\Select::make('warehouse_id')
                    ->label('Gudang')
                    ->relationship('warehouse', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\Select::make('entitas_id')
                    ->label('Entitas')
                    ->relationship('entitas', 'nama')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->helperText('Pilih entitas untuk inventaris ini'),
                Forms\Components\TextInput::make('quantity')
                    ->label('Jumlah Stok')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('average_cost')
                    ->label('Harga Rata-rata')
                    ->required()
                    ->numeric()
                    ->prefix('Rp')
                    ->default(0.00),
                Forms\Components\TextInput::make('total_value')
                    ->label('Total Nilai')
                    ->required()
                    ->numeric()
                    ->prefix('Rp')
                    ->default(0.00),
                Forms\Components\TextInput::make('minimum_stock')
                    ->label('Stok Minimum')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('maximum_stock')
                    ->label('Stok Maksimum')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\DatePicker::make('last_updated')
                    ->label('Terakhir Update'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('product.kode')
                    ->label('Kode Produk')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.nama')
                    ->label('Nama Produk')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('warehouse.name')
                    ->label('Gudang')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('entitas.nama')
                    ->label('Entitas')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('quantity')
                    ->label('Jumlah')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('average_cost')
                    ->label('Harga Rata-rata')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_value')
                    ->label('Total Nilai')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('minimum_stock')
                    ->label('Stok Min')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('maximum_stock')
                    ->label('Stok Max')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_updated')
                    ->label('Terakhir Update')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('entitas_id')
                    ->label('Entitas')
                    ->relationship('entitas', 'nama')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('warehouse_id')
                    ->label('Gudang')
                    ->relationship('warehouse', 'name')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInventoryStocks::route('/'),
            'create' => Pages\CreateInventoryStock::route('/create'),
            'edit' => Pages\EditInventoryStock::route('/{record}/edit'),
        ];
    }
}
