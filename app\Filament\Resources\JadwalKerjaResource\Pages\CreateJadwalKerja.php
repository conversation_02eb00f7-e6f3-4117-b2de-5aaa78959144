<?php

namespace App\Filament\Resources\JadwalKerjaResource\Pages;

use App\Filament\Resources\JadwalKerjaResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use App\Models\Schedule;

class CreateJadwalKerja extends CreateRecord
{
    protected static string $resource = JadwalKerjaResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function beforeCreate(): void
    {
        // Check if schedule already exists for this employee on this date
        $existingSchedule = Schedule::where('karyawan_id', $this->data['karyawan_id'])
            ->where('tanggal_jadwal', $this->data['tanggal_jadwal'])
            ->first();

        if ($existingSchedule) {
            Notification::make()
                ->title('Jadwal sudah ada')
                ->body('Karyawan sudah memiliki jadwal pada tanggal tersebut.')
                ->danger()
                ->send();

            $this->halt();
        }
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Jadwal kerja berhasil dibuat';
    }
}
