<?php

namespace App\Filament\Resources\JadwalKerjaResource\Pages;

use App\Filament\Resources\JadwalKerjaResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use App\Models\Schedule;

class EditJadwalKerja extends EditRecord
{
    protected static string $resource = JadwalKerjaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function beforeSave(): void
    {
        // Check if schedule already exists for this employee on this date (excluding current record)
        $existingSchedule = Schedule::where('karyawan_id', $this->data['karyawan_id'])
            ->where('tanggal_jadwal', $this->data['tanggal_jadwal'])
            ->where('id', '!=', $this->record->id)
            ->first();

        if ($existingSchedule) {
            Notification::make()
                ->title('J<PERSON>wal sudah ada')
                ->body('Karyawan sudah memiliki jadwal pada tanggal tersebut.')
                ->danger()
                ->send();

            $this->halt();
        }
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'Jadwal kerja berhasil diperbarui';
    }
}
