<?php

namespace App\Filament\Resources\JadwalKerjaResource\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Schedule;
use App\Models\Karyawan;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class JadwalKerjaOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $user = Auth::user();
        $today = Carbon::today();
        $thisWeek = [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()];
        $thisMonth = [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()];

        // Base query with supervisor filtering
        $baseQuery = Schedule::query();
        if ($user->role === 'supervisor') {
            $employeeIds = Karyawan::where('supervisor_id', $user->id)->pluck('id')->toArray();
            $baseQuery->whereIn('karyawan_id', $employeeIds);
        }

        // Today's schedules
        $todaySchedules = (clone $baseQuery)
            ->whereDate('tanggal_jadwal', $today)
            ->count();

        // This week's schedules
        $weekSchedules = (clone $baseQuery)
            ->whereBetween('tanggal_jadwal', $thisWeek)
            ->count();

        // This month's schedules
        $monthSchedules = (clone $baseQuery)
            ->whereBetween('tanggal_jadwal', $thisMonth)
            ->count();

        // Pending approvals
        $pendingApprovals = (clone $baseQuery)
            ->where('is_approved', false)
            ->count();

        // Active employees with schedules this week
        $activeEmployees = (clone $baseQuery)
            ->whereBetween('tanggal_jadwal', $thisWeek)
            ->distinct('karyawan_id')
            ->count('karyawan_id');

        return [
            Stat::make('Jadwal Hari Ini', $todaySchedules)
                ->description('Jadwal kerja untuk hari ini')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('primary'),

            Stat::make('Jadwal Minggu Ini', $weekSchedules)
                ->description('Total jadwal minggu ini')
                ->descriptionIcon('heroicon-m-calendar')
                ->color('success'),

            Stat::make('Jadwal Bulan Ini', $monthSchedules)
                ->description('Total jadwal bulan ini')
                ->descriptionIcon('heroicon-m-calendar-date-range')
                ->color('info'),

            Stat::make('Menunggu Persetujuan', $pendingApprovals)
                ->description('Jadwal belum disetujui')
                ->descriptionIcon('heroicon-m-clock')
                ->color($pendingApprovals > 0 ? 'warning' : 'success'),

            Stat::make('Karyawan Aktif', $activeEmployees)
                ->description('Karyawan dengan jadwal minggu ini')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),
        ];
    }
}
