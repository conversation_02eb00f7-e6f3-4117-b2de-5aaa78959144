<?php

namespace App\Filament\Resources\JadwalMasalResource\Pages;

use App\Filament\Resources\JadwalMasalResource;
use App\Services\PermissionService;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Filament\Notifications\Notification;

class CreateJadwalMasal extends CreateRecord
{
    protected static string $resource = JadwalMasalResource::class;

    protected array $karyawanData = [];

    protected function beforeCreate(): void
    {
        // Check if user has permission to manage jadwal
        $user = Auth::user();

        // Skip permission check for super admin and direktur
        if (!$user->hasRole(['super_admin', 'direktur', 'manager_hrd'])) {
            $accessibleIds = PermissionService::getAccessibleKaryawanIds('manage_jadwal');

            if (empty($accessibleIds)) {
                Notification::make()
                    ->title('Tidak Ada Permission')
                    ->body('Anda tidak memiliki permission untuk mengelola jadwal karyawan. Silakan hubungi administrator untuk mendapatkan akses yang diperlukan.')
                    ->warning()
                    ->persistent()
                    ->send();

                throw new \Exception('Tidak memiliki permission untuk mengelola jadwal karyawan.');
            }
        }

        // Validate karyawan selection
        $karyawanData = $this->data['karyawan'] ?? '[]';
        if (is_string($karyawanData)) {
            $karyawanData = json_decode($karyawanData, true) ?? [];
        }

        if (empty($karyawanData)) {
            Notification::make()
                ->title('Validasi Error')
                ->body('Pilih minimal satu karyawan untuk jadwal masal.')
                ->danger()
                ->send();

            throw new \Exception('Pilih minimal satu karyawan untuk jadwal masal.');
        }
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = Auth::id();
        $user = Auth::user();

        // Handle karyawan data - could be JSON string or array
        $karyawanData = $data['karyawan'] ?? [];
        if (is_string($karyawanData)) {
            $karyawanData = json_decode($karyawanData, true) ?? [];
        }

        // Store karyawan data temporarily for afterCreate
        $this->karyawanData = is_array($karyawanData) ? $karyawanData : [];

        // Debug log
        Log::info('Karyawan data before create:', [
            'raw' => $data['karyawan'] ?? 'not set',
            'processed' => $this->karyawanData
        ]);

        // Ensure entitas_id is set
        if (empty($data['entitas_id'])) {
            // Auto-set entitas_id berdasarkan role user jika belum diset
            if ($user->role === 'keptok' && $user->karyawan && $user->karyawan->id_entitas) {
                $data['entitas_id'] = $user->karyawan->id_entitas;
            } elseif (!empty($this->karyawanData)) {
                // Fallback: ambil entitas dari karyawan pertama yang dipilih
                $firstKaryawan = \App\Models\Karyawan::find($this->karyawanData[0]);
                if ($firstKaryawan && $firstKaryawan->id_entitas) {
                    $data['entitas_id'] = $firstKaryawan->id_entitas;
                }
            }
        }

        // Remove karyawan from main data as it's not a direct field
        unset($data['karyawan']);

        return $data;
    }

    protected function afterCreate(): void
    {
        // Attach selected karyawan to the jadwal masal
        if (!empty($this->karyawanData)) {
            $this->record->karyawan()->attach($this->karyawanData);

            Notification::make()
                ->title('Jadwal masal berhasil dibuat')
                ->body('Jadwal masal dengan ' . count($this->karyawanData) . ' karyawan berhasil dibuat. Klik "Generate Jadwal" untuk membuat jadwal individual.')
                ->success()
                ->duration(8000)
                ->send();
        } else {
            Notification::make()
                ->title('Jadwal masal dibuat tanpa karyawan')
                ->body('Jadwal masal berhasil dibuat, tapi tidak ada karyawan yang dipilih. Silakan edit jadwal untuk menambahkan karyawan.')
                ->warning()
                ->send();
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
