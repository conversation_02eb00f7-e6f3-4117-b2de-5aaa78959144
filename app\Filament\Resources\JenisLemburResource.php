<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JenisLemburResource\Pages;
use App\Filament\Resources\JenisLemburResource\RelationManagers;
use App\Models\JenisLembur;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class JenisLemburResource extends Resource
{
    protected static ?string $model = JenisLembur::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static ?string $navigationLabel = 'Jenis Lembur';

    protected static ?string $modelLabel = '<PERSON><PERSON> Lembur';

    protected static ?string $pluralModelLabel = 'Jenis Lembur';

    protected static ?string $navigationGroup = 'Manajemen Karyawan';

    protected static ?int $navigationSort = 7;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('Informasi Jenis Lembur')
                ->schema([
                    Forms\Components\TextInput::make('nama_jenis')
                        ->label('Nama <PERSON>is Lembur')
                        ->required()
                        ->maxLength(255)
                        ->placeholder('Contoh: Lembur Hari Biasa'),

                    Forms\Components\Select::make('tipe_perhitungan')
                        ->label('Tipe Perhitungan')
                        ->options([
                            'per_jam' => 'Per Jam',
                            'per_hari' => 'Per Hari',
                        ])
                        ->required()
                        ->helperText('Per Jam: Dihitung berdasarkan jam lembur dengan aturan bertingkat. Per Hari: Dihitung flat per hari.'),

                    Forms\Components\TextInput::make('pembagi_upah_bulanan')
                        ->label('Pembagi Upah Bulanan')
                        ->required()
                        ->numeric()
                        ->default(30)
                        ->minValue(1)
                        ->helperText('Pembagi untuk menghitung upah harian (26 atau 30 hari)'),

                    Forms\Components\TextInput::make('urutan')
                        ->label('Urutan Tampilan')
                        ->numeric()
                        ->default(0)
                        ->helperText('Urutan tampilan di dropdown (semakin kecil semakin atas)'),
                ])
                ->columns(2),

            Forms\Components\Section::make('Pengaturan Lainnya')
                ->schema([
                    Forms\Components\Textarea::make('keterangan')
                        ->label('Keterangan')
                        ->placeholder('Keterangan tentang jenis lembur ini')
                        ->rows(3),

                    Forms\Components\Toggle::make('is_active')
                        ->label('Status Aktif')
                        ->default(true)
                        ->helperText('Jenis lembur hanya akan tersedia jika status aktif'),
                ])
                ->columns(1),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('urutan')
                    ->label('Urutan')
                    ->sortable()
                    ->alignCenter()
                    ->width(80),

                Tables\Columns\TextColumn::make('nama_jenis')
                    ->label('Nama Jenis Lembur')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('tipe_perhitungan')
                    ->label('Tipe')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'per_jam' => 'info',
                        'per_hari' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'per_jam' => 'Per Jam',
                        'per_hari' => 'Per Hari',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('pembagi_upah_bulanan')
                    ->label('Pembagi Upah')
                    ->alignCenter()
                    ->formatStateUsing(fn($state) => $state . ' hari'),

                Tables\Columns\TextColumn::make('aturan_lembur_count')
                    ->label('Jumlah Aturan')
                    ->counts('aturanLembur')
                    ->alignCenter()
                    ->badge()
                    ->color('primary'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('tipe_perhitungan')
                    ->label('Tipe Perhitungan')
                    ->options([
                        'per_jam' => 'Per Jam',
                        'per_hari' => 'Per Hari',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif')
                    ->placeholder('Semua')
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->icon('heroicon-o-eye'),

                Tables\Actions\EditAction::make()
                    ->icon('heroicon-o-pencil-square'),

                Tables\Actions\DeleteAction::make()
                    ->icon('heroicon-o-trash'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('urutan', 'asc')
            ->emptyStateHeading('Belum ada jenis lembur')
            ->emptyStateDescription('Tambahkan jenis lembur dengan mengklik tombol "Tambah Jenis Lembur".')
            ->emptyStateIcon('heroicon-o-clock');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\AturanLemburRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJenisLemburs::route('/'),
            'create' => Pages\CreateJenisLembur::route('/create'),
            'edit' => Pages\EditJenisLembur::route('/{record}/edit'),
        ];
    }
}
