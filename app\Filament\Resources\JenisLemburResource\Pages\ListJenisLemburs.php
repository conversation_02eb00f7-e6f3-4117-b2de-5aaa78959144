<?php

namespace App\Filament\Resources\JenisLemburResource\Pages;

use App\Filament\Resources\JenisLemburResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListJenisLemburs extends ListRecords
{
    protected static string $resource = JenisLemburResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
