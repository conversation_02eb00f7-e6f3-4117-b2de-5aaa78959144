<?php

namespace App\Filament\Resources\JenisLemburResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class AturanLemburRelationManager extends RelationManager
{
    protected static string $relationship = 'aturanLembur';

    protected static ?string $title = 'Aturan Range Jam';

    protected static ?string $recordTitleAttribute = 'keterangan';

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('Pengaturan Range Jam')
                ->schema([
                    Forms\Components\TextInput::make('jam_mulai')
                        ->label('Jam Mulai')
                        ->numeric()
                        ->step(0.25)
                        ->minValue(0)
                        ->placeholder('1.00')
                        ->helperText('Jam mulai dalam format desimal (contoh: 1.5 = 1 jam 30 menit)')
                        ->visible(fn() => $this->getOwnerRecord()->tipe_perhitungan === 'per_jam'),

                    Forms\Components\TextInput::make('jam_selesai')
                        ->label('Jam Selesai')
                        ->numeric()
                        ->step(0.25)
                        ->minValue(0)
                        ->placeholder('2.00 (kosongkan untuk jam terbuka)')
                        ->helperText('Jam selesai dalam format desimal (kosongkan untuk jam terbuka)')
                        ->visible(fn() => $this->getOwnerRecord()->tipe_perhitungan === 'per_jam'),

                    Forms\Components\TextInput::make('multiplier')
                        ->label('Multiplier')
                        ->required()
                        ->numeric()
                        ->step(0.25)
                        ->minValue(0.25)
                        ->placeholder('1.5')
                        ->helperText('Pengali upah (contoh: 1.5 = 1.5x upah normal)'),

                    Forms\Components\TextInput::make('urutan')
                        ->label('Urutan Prioritas')
                        ->numeric()
                        ->default(0)
                        ->helperText('Urutan prioritas aturan (semakin kecil semakin prioritas)'),
                ])
                ->columns(2),

            Forms\Components\Section::make('Pengaturan Lainnya')
                ->schema([
                    Forms\Components\Textarea::make('keterangan')
                        ->label('Keterangan')
                        ->placeholder('Keterangan tentang aturan ini')
                        ->rows(3),

                    Forms\Components\Toggle::make('is_active')
                        ->label('Status Aktif')
                        ->default(true)
                        ->helperText('Aturan hanya akan berlaku jika status aktif'),
                ])
                ->columns(1),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('keterangan')
            ->columns([
                Tables\Columns\TextColumn::make('urutan')
                    ->label('Urutan')
                    ->sortable()
                    ->alignCenter()
                    ->width(80),

                Tables\Columns\TextColumn::make('jam_range')
                    ->label('Range Jam')
                    ->state(function ($record) {
                        if ($record->jenisLembur->tipe_perhitungan === 'per_hari') {
                            return 'Per Hari';
                        }

                        if ($record->jam_selesai) {
                            return "Jam {$record->jam_mulai} - {$record->jam_selesai}";
                        }

                        return "Jam {$record->jam_mulai}+";
                    }),

                Tables\Columns\TextColumn::make('multiplier')
                    ->label('Multiplier')
                    ->alignCenter()
                    ->weight('bold')
                    ->color('primary')
                    ->formatStateUsing(fn($state) => $state . 'x'),

                Tables\Columns\TextColumn::make('keterangan')
                    ->label('Keterangan')
                    ->limit(50)
                    ->placeholder('-'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->alignCenter(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif')
                    ->placeholder('Semua')
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Tambah Aturan')
                    ->icon('heroicon-o-plus'),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->icon('heroicon-o-pencil-square'),

                Tables\Actions\DeleteAction::make()
                    ->icon('heroicon-o-trash'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('urutan', 'asc')
            ->emptyStateHeading('Belum ada aturan')
            ->emptyStateDescription('Tambahkan aturan range jam untuk jenis lembur ini.')
            ->emptyStateIcon('heroicon-o-clock');
    }
}
