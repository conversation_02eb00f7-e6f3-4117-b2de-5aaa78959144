<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JenisPelanggaranResource\Pages;
use App\Models\JenisPelanggaran;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Hidden;
use Filament\Tables\Columns\TextColumn;

use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Support\Facades\Auth;
use App\Traits\HasExportActions;
use App\Exports\JenisPelanggaranExport;
use Filament\Notifications\Notification;

class JenisPelanggaranResource extends Resource
{
    use HasExportActions;

    protected static ?string $model = JenisPelanggaran::class;

    protected static ?string $navigationIcon = 'heroicon-o-exclamation-triangle';

    protected static ?string $navigationLabel = 'Jenis Pelanggaran';

    protected static ?string $modelLabel = 'Jenis Pelanggaran';

    protected static ?string $pluralModelLabel = 'Jenis Pelanggaran';

    protected static ?string $navigationGroup = 'Data Master';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Dasar')
                    ->schema([
                        TextInput::make('kode_pelanggaran')
                            ->label('Kode Pelanggaran')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(20)
                            ->placeholder('Contoh: PL001'),

                        TextInput::make('nama_pelanggaran')
                            ->label('Nama Pelanggaran')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Contoh: Terlambat Masuk Kerja'),

                        Textarea::make('deskripsi')
                            ->label('Deskripsi')
                            ->rows(3)
                            ->placeholder('Deskripsi detail pelanggaran'),

                        Select::make('kategori')
                            ->label('Kategori')
                            ->required()
                            ->options([
                                'ringan' => 'Ringan',
                                'sedang' => 'Sedang',
                                'berat' => 'Berat',
                            ])
                            ->default('ringan'),
                    ])->columns(2),

                Forms\Components\Section::make('Pengaturan Denda')
                    ->schema([
                        Select::make('jenis_denda')
                            ->label('Jenis Denda')
                            ->required()
                            ->options([
                                'nominal_tetap' => 'Nominal Tetap',
                                'persentase_gaji' => 'Persentase dari Gaji Pokok',
                            ])
                            ->default('nominal_tetap')
                            ->reactive()
                            ->afterStateUpdated(fn($state, callable $set) => [
                                $set('denda_nominal', $state === 'nominal_tetap' ? null : 0),
                                $set('persentase_denda', $state === 'persentase_gaji' ? null : 0),
                            ]),

                        TextInput::make('denda_nominal')
                            ->label('Nominal Denda (Rp)')
                            ->numeric()
                            ->prefix('Rp')
                            ->visible(fn(callable $get) => $get('jenis_denda') === 'nominal_tetap')
                            ->required(fn(callable $get) => $get('jenis_denda') === 'nominal_tetap')
                            ->minValue(0),

                        TextInput::make('persentase_denda')
                            ->label('Persentase Denda (%)')
                            ->numeric()
                            ->suffix('%')
                            ->step(0.01)
                            ->visible(fn(callable $get) => $get('jenis_denda') === 'persentase_gaji')
                            ->required(fn(callable $get) => $get('jenis_denda') === 'persentase_gaji')
                            ->minValue(0),
                    ])->columns(2),

                Forms\Components\Section::make('Pengaturan Lainnya')
                    ->schema([
                        Toggle::make('is_active')
                            ->label('Status Aktif')
                            ->default(true),

                        Textarea::make('keterangan')
                            ->label('Keterangan')
                            ->rows(2)
                            ->placeholder('Keterangan tambahan'),

                        Hidden::make('created_by')
                            ->default(Auth::id()),
                    ])->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('kode_pelanggaran')
                    ->label('Kode')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('nama_pelanggaran')
                    ->label('Nama Pelanggaran')
                    ->searchable()
                    ->sortable()
                    ->wrap(),

                TextColumn::make('kategori')
                    ->label('Kategori')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'ringan' => 'success',
                        'sedang' => 'warning',
                        'berat' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                TextColumn::make('formatted_denda')
                    ->label('Denda')
                    ->sortable(query: function ($query, $direction) {
                        return $query->orderBy('denda_nominal', $direction);
                    }),

                IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->sortable(),

                TextColumn::make('creator.name')
                    ->label('Dibuat Oleh')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                SelectFilter::make('kategori')
                    ->options([
                        'ringan' => 'Ringan',
                        'sedang' => 'Sedang',
                        'berat' => 'Berat',
                    ]),

                TernaryFilter::make('is_active')
                    ->label('Status Aktif')
                    ->boolean()
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->before(function (JenisPelanggaran $record) {
                        if ($record->pelanggarans()->count() > 0) {
                            Notification::make()
                                ->title('Tidak dapat menghapus jenis pelanggaran')
                                ->body('Jenis pelanggaran ini sudah digunakan dalam data pelanggaran dan tidak dapat dihapus.')
                                ->danger()
                                ->send();

                            // Stop the deletion process
                            return false;
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->before(function ($records) {
                            $usedRecords = [];
                            foreach ($records as $record) {
                                if ($record->pelanggarans()->count() > 0) {
                                    $usedRecords[] = $record->nama_pelanggaran;
                                }
                            }

                            if (!empty($usedRecords)) {
                                Notification::make()
                                    ->title('Tidak dapat menghapus beberapa jenis pelanggaran')
                                    ->body('Jenis pelanggaran berikut sudah digunakan dan tidak dapat dihapus: ' . implode(', ', $usedRecords))
                                    ->danger()
                                    ->send();

                                // Stop the deletion process
                                return false;
                            }
                        }),
                ]),
            ])
            ->headerActions([
                ...self::getExportActions(JenisPelanggaranExport::class, 'Data JenisPelanggaran'),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJenisPelanggarans::route('/'),
            'create' => Pages\CreateJenisPelanggaran::route('/create'),
            'view' => Pages\ViewJenisPelanggaran::route('/{record}'),
            'edit' => Pages\EditJenisPelanggaran::route('/{record}/edit'),
        ];
    }
}
