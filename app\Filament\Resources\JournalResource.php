<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JournalResource\Pages;
use App\Filament\Resources\JournalResource\RelationManagers;
use App\Models\Journal;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class JournalResource extends Resource
{
    protected static ?string $model = Journal::class;

    protected static ?string $navigationIcon = 'heroicon-o-book-open';

    protected static ?string $navigationLabel = 'Jurnal';

    protected static ?string $modelLabel = 'Jurnal';

    protected static ?string $pluralModelLabel = 'Jurnal';

    protected static ?string $navigationGroup = 'Akuntansi';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('journal_number')
                    ->label('Nomor Jurnal')
                    ->disabled()
                    ->dehydrated(false),
                Forms\Components\DatePicker::make('transaction_date')
                    ->label('Tanggal Transaksi')
                    ->required()
                    ->default(now()),
                Forms\Components\TextInput::make('reference_number')
                    ->label('Nomor Referensi')
                    ->maxLength(255),
                Forms\Components\Select::make('source_type')
                    ->label('Tipe Sumber')
                    ->options([
                        'Sale' => 'Penjualan',
                        'Purchase' => 'Pembelian',
                        'Payment' => 'Pembayaran',
                        'Receipt' => 'Penerimaan',
                        'ManualAdjust' => 'Penyesuaian Manual',
                    ])
                    ->native(false),
                Forms\Components\Textarea::make('description')
                    ->label('Deskripsi')
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\Select::make('status')
                    ->label('Status')
                    ->options([
                        'Draft' => 'Draft',
                        'Posted' => 'Posted',
                        'Cancelled' => 'Dibatalkan',
                        'Error' => 'Error',
                    ])
                    ->default('Draft')
                    ->native(false),
                Forms\Components\Repeater::make('journalEntries')
                    ->relationship()
                    ->schema([
                        Forms\Components\Select::make('account_id')
                            ->label('Akun')
                            ->relationship('account', 'nama_akun')
                            ->required()
                            ->searchable()
                            ->preload()
                            ->columnSpan(2),
                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->required()
                            ->columnSpan(2),
                        Forms\Components\TextInput::make('debit')
                            ->label('Debit')
                            ->numeric()
                            ->prefix('Rp')
                            ->default(0),
                        Forms\Components\TextInput::make('credit')
                            ->label('Credit')
                            ->numeric()
                            ->prefix('Rp')
                            ->default(0),
                        Forms\Components\TextInput::make('sort_order')
                            ->label('Urutan')
                            ->numeric()
                            ->default(0),
                    ])
                    ->columns(3)
                    ->defaultItems(2)
                    ->addActionLabel('Tambah Entri Jurnal')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('journal_number')
                    ->label('Nomor Jurnal')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('Tanggal')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reference_number')
                    ->label('Referensi')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('source_type')
                    ->label('Tipe Sumber')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Sale' => 'success',
                        'Purchase' => 'info',
                        'Payment' => 'warning',
                        'Receipt' => 'primary',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(50)
                    ->toggleable(),
                Tables\Columns\TextColumn::make('total_debit')
                    ->label('Total Debit')
                    ->money('IDR')
                    ->getStateUsing(fn($record) => $record->total_debit)
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_credit')
                    ->label('Total Credit')
                    ->money('IDR')
                    ->getStateUsing(fn($record) => $record->total_credit)
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Draft' => 'gray',
                        'Posted' => 'success',
                        'Cancelled' => 'danger',
                        'Error' => 'warning',
                        default => 'gray',
                    }),
                Tables\Columns\IconColumn::make('is_balanced')
                    ->label('Seimbang')
                    ->getStateUsing(fn($record) => $record->isBalanced())
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'Draft' => 'Draft',
                        'Posted' => 'Posted',
                        'Cancelled' => 'Dibatalkan',
                        'Error' => 'Error',
                    ]),
                Tables\Filters\SelectFilter::make('source_type')
                    ->label('Tipe Sumber')
                    ->options([
                        'Sale' => 'Penjualan',
                        'Purchase' => 'Pembelian',
                        'Payment' => 'Pembayaran',
                        'Receipt' => 'Penerimaan',
                        'ManualAdjust' => 'Penyesuaian Manual',
                    ]),
                Tables\Filters\Filter::make('transaction_date')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('transaction_date', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('transaction_date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('post')
                    ->label('Post Jurnal')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn($record) => $record->status === 'Draft' && $record->isBalanced())
                    ->requiresConfirmation()
                    ->modalHeading('Post Jurnal')
                    ->modalDescription('Apakah Anda yakin ingin memposting jurnal ini? Jurnal yang sudah diposting tidak dapat diubah.')
                    ->action(function ($record) {
                        if (!$record->isBalanced()) {
                            Notification::make()
                                ->title('Jurnal Tidak Seimbang')
                                ->body('Jurnal tidak dapat diposting karena total debit tidak sama dengan total credit.')
                                ->danger()
                                ->send();
                            return;
                        }

                        $record->update(['status' => 'Posted']);

                        Notification::make()
                            ->title('Jurnal Berhasil Diposting')
                            ->body('Jurnal ' . $record->journal_number . ' telah berhasil diposting.')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\Action::make('cancel')
                    ->label('Batalkan')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->visible(fn($record) => in_array($record->status, ['Draft', 'Posted']))
                    ->requiresConfirmation()
                    ->modalHeading('Batalkan Jurnal')
                    ->modalDescription('Apakah Anda yakin ingin membatalkan jurnal ini?')
                    ->action(function ($record) {
                        $record->update(['status' => 'Cancelled']);

                        Notification::make()
                            ->title('Jurnal Dibatalkan')
                            ->body('Jurnal ' . $record->journal_number . ' telah dibatalkan.')
                            ->warning()
                            ->send();
                    }),
                Tables\Actions\EditAction::make()
                    ->visible(fn($record) => $record->status === 'Draft'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJournals::route('/'),
            'create' => Pages\CreateJournal::route('/create'),
            'view' => Pages\ViewJournal::route('/{record}'),
            'edit' => Pages\EditJournal::route('/{record}/edit'),
        ];
    }
}
