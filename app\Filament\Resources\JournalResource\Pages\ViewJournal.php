<?php

namespace App\Filament\Resources\JournalResource\Pages;

use App\Filament\Resources\JournalResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewJournal extends ViewRecord
{
    protected static string $resource = JournalResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('post')
                ->label('Post Jurnal')
                ->icon('heroicon-o-check')
                ->color('success')
                ->visible(fn () => $this->record->status === 'Draft' && $this->record->isBalanced())
                ->requiresConfirmation()
                ->action(function () {
                    $this->record->update(['status' => 'Posted']);
                    $this->redirect($this->getResource()::getUrl('view', ['record' => $this->record]));
                }),
            Actions\Action::make('cancel')
                ->label('Batalkan')
                ->icon('heroicon-o-x-mark')
                ->color('danger')
                ->visible(fn () => in_array($this->record->status, ['Draft', 'Posted']))
                ->requiresConfirmation()
                ->action(function () {
                    $this->record->update(['status' => 'Cancelled']);
                    $this->redirect($this->getResource()::getUrl('view', ['record' => $this->record]));
                }),
            Actions\EditAction::make()
                ->visible(fn () => $this->record->status === 'Draft'),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Informasi Jurnal')
                    ->schema([
                        Infolists\Components\TextEntry::make('journal_number')
                            ->label('Nomor Jurnal'),
                        Infolists\Components\TextEntry::make('transaction_date')
                            ->label('Tanggal Transaksi')
                            ->date(),
                        Infolists\Components\TextEntry::make('reference_number')
                            ->label('Nomor Referensi'),
                        Infolists\Components\TextEntry::make('source_type')
                            ->label('Tipe Sumber')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'Sale' => 'success',
                                'Purchase' => 'info',
                                'Payment' => 'warning',
                                'Receipt' => 'primary',
                                default => 'gray',
                            }),
                        Infolists\Components\TextEntry::make('status')
                            ->label('Status')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'Draft' => 'gray',
                                'Posted' => 'success',
                                'Cancelled' => 'danger',
                                'Error' => 'warning',
                                default => 'gray',
                            }),
                        Infolists\Components\TextEntry::make('description')
                            ->label('Deskripsi')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                
                Infolists\Components\Section::make('Detail Entri Jurnal')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('journalEntries')
                            ->schema([
                                Infolists\Components\TextEntry::make('account.nama_akun')
                                    ->label('Akun'),
                                Infolists\Components\TextEntry::make('description')
                                    ->label('Deskripsi'),
                                Infolists\Components\TextEntry::make('debit')
                                    ->label('Debit')
                                    ->money('IDR')
                                    ->placeholder('-'),
                                Infolists\Components\TextEntry::make('credit')
                                    ->label('Credit')
                                    ->money('IDR')
                                    ->placeholder('-'),
                            ])
                            ->columns(4)
                            ->columnSpanFull(),
                    ]),
                
                Infolists\Components\Section::make('Ringkasan')
                    ->schema([
                        Infolists\Components\TextEntry::make('total_debit')
                            ->label('Total Debit')
                            ->money('IDR')
                            ->getStateUsing(fn ($record) => $record->total_debit),
                        Infolists\Components\TextEntry::make('total_credit')
                            ->label('Total Credit')
                            ->money('IDR')
                            ->getStateUsing(fn ($record) => $record->total_credit),
                        Infolists\Components\TextEntry::make('balance_status')
                            ->label('Status Keseimbangan')
                            ->getStateUsing(fn ($record) => $record->isBalanced() ? 'Seimbang' : 'Tidak Seimbang')
                            ->badge()
                            ->color(fn ($record) => $record->isBalanced() ? 'success' : 'danger'),
                        Infolists\Components\TextEntry::make('difference')
                            ->label('Selisih')
                            ->money('IDR')
                            ->getStateUsing(fn ($record) => abs($record->total_debit - $record->total_credit))
                            ->visible(fn ($record) => !$record->isBalanced()),
                    ])
                    ->columns(2),
            ]);
    }
}
