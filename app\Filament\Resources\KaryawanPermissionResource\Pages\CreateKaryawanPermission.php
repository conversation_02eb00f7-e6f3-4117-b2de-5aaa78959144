<?php

namespace App\Filament\Resources\KaryawanPermissionResource\Pages;

use App\Filament\Resources\KaryawanPermissionResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateKaryawanPermission extends CreateRecord
{
    protected static string $resource = KaryawanPermissionResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = Auth::id();

        // Handle scope values based on scope type
        switch ($data['scope_type']) {
            case 'entitas':
                $data['scope_values'] = $data['scope_values_entitas'] ?? null;
                break;

            case 'departemen':
                // Combine departemen from all entitas with entitas context
                $departemenWithEntitas = [];
                $entitas = \App\Models\Entitas::all();
                foreach ($entitas as $ent) {
                    $entDepartemen = $data["departemen_entitas_{$ent->id}"] ?? [];
                    if (is_array($entDepartemen) && !empty($entDepartemen)) {
                        foreach ($entDepartemen as $deptId) {
                            $departemenWithEntitas[] = [
                                'departemen_id' => $deptId,
                                'entitas_id' => $ent->id
                            ];
                        }
                    }
                }
                $data['scope_values'] = !empty($departemenWithEntitas) ? $departemenWithEntitas : null;
                break;

            case 'divisi':
                // Combine divisi from all departemen with departemen and entitas context
                $divisiWithContext = [];
                $entitas = \App\Models\Entitas::all();
                foreach ($entitas as $ent) {
                    // Get departemen yang ada di entitas ini
                    $departemenIds = \App\Models\Karyawan::where('id_entitas', $ent->id)
                        ->whereNotNull('id_departemen')
                        ->distinct()
                        ->pluck('id_departemen')
                        ->toArray();

                    $departemen = \App\Models\Departemen::whereIn('id', $departemenIds)->get();
                    foreach ($departemen as $dept) {
                        $fieldName = "divisi_dept_{$dept->id}_entitas_{$ent->id}";
                        $deptDivisi = $data[$fieldName] ?? [];

                        // Only process if this field exists and has values
                        if (isset($data[$fieldName]) && is_array($deptDivisi) && !empty($deptDivisi)) {
                            foreach ($deptDivisi as $divisiId) {
                                $divisiWithContext[] = [
                                    'divisi_id' => $divisiId,
                                    'departemen_id' => $dept->id,
                                    'entitas_id' => $ent->id
                                ];
                            }
                        }
                    }
                }
                $data['scope_values'] = !empty($divisiWithContext) ? $divisiWithContext : null;
                break;

            case 'custom':
                $data['scope_values'] = $data['scope_values_custom'] ?? null;
                break;

            case 'all':
            default:
                $data['scope_values'] = null;
                break;
        }

        // Remove temporary fields
        unset($data['scope_values_entitas']);
        unset($data['scope_values_departemen']);
        unset($data['scope_values_divisi']);
        unset($data['scope_values_custom']);

        // Remove individual entitas departemen fields
        $entitas = \App\Models\Entitas::all();
        foreach ($entitas as $ent) {
            unset($data["departemen_entitas_{$ent->id}"]);

            // Remove divisi fields for this entitas
            $departemenIds = \App\Models\Karyawan::where('id_entitas', $ent->id)
                ->whereNotNull('id_departemen')
                ->distinct()
                ->pluck('id_departemen')
                ->toArray();

            $departemen = \App\Models\Departemen::whereIn('id', $departemenIds)->get();
            foreach ($departemen as $dept) {
                unset($data["divisi_dept_{$dept->id}_entitas_{$ent->id}"]);
            }
        }

        // Remove individual department divisi fields (legacy cleanup)
        $departemen = \App\Models\Departemen::all();
        foreach ($departemen as $dept) {
            unset($data["divisi_dept_{$dept->id}"]);
            unset($data["divisi_entitas_{$dept->id}"]);
        }

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
