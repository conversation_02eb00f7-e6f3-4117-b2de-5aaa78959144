<?php

namespace App\Filament\Resources\KaryawanPermissionResource\Pages;

use App\Filament\Resources\KaryawanPermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use App\Models\KaryawanPermission;

class ListKaryawanPermissions extends ListRecords
{
    protected static string $resource = KaryawanPermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('Semua Permission')
                ->badge(KaryawanPermission::count()),
            
            'approve_cuti' => Tab::make('Approve Cuti')
                ->badge(KaryawanPermission::where('permission_type', 'approve_cuti')->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('permission_type', 'approve_cuti')),
            
            'view_absensi' => Tab::make('Lihat Absensi')
                ->badge(KaryawanPermission::where('permission_type', 'view_absensi')->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('permission_type', 'view_absensi')),
            
            'manage_jadwal' => Tab::make('Kelola Jadwal')
                ->badge(KaryawanPermission::where('permission_type', 'manage_jadwal')->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('permission_type', 'manage_jadwal')),
            
            'view_payroll' => Tab::make('Lihat Payroll')
                ->badge(KaryawanPermission::where('permission_type', 'view_payroll')->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('permission_type', 'view_payroll')),
            
            'manage_karyawan' => Tab::make('Kelola Karyawan')
                ->badge(KaryawanPermission::where('permission_type', 'manage_karyawan')->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('permission_type', 'manage_karyawan')),
            
            'inactive' => Tab::make('Tidak Aktif')
                ->badge(KaryawanPermission::where('is_active', false)->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_active', false)),
        ];
    }
}
