<?php

namespace App\Filament\Resources\KaryawanResource\Pages;

use App\Filament\Resources\KaryawanResource;
use App\Traits\HasWhatsAppNotifications;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListKaryawan extends ListRecords
{
    use HasWhatsAppNotifications;

    protected static string $resource = KaryawanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            
            // WhatsApp notification actions
            $this->whatsAppNotificationAction(
                phoneField: 'no_hp',
                defaultMessage: 'Halo {nama_lengkap}, ini adalah pesan dari sistem HR.'
            ),
            
            $this->bulkWhatsAppNotificationAction(
                phoneField: 'no_hp',
                nameField: 'nama_lengkap',
                defaultMessage: 'Halo {name}, ini adalah pengumuman penting dari HR. Terima kasih.'
            ),
        ];
    }
}
