<?php

namespace App\Filament\Resources\KaryawanResource\Pages;

use App\Filament\Resources\KaryawanResource;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\KaryawanResource\Widgets\KaryawanSummary;

class ListKaryawans extends ListRecords
{
    protected static string $resource = KaryawanResource::class;

    protected function getHeaderActions(): array
    {
        return [];
    }

    public function getTabs(): array
    {
        // BARIS 1: Status Karyawan + Jenis Kontrak
        $totalKaryawan = \App\Models\Karyawan::count();
        $karyawanAktif = \App\Models\Karyawan::where('status_aktif', 1)->count();
        $karyawanTidakAktif = \App\Models\Karyawan::where('status_aktif', 0)->count();

        $tabs = [
            'semua' => Tab::make('Semua')
                ->badge($totalKaryawan)
                ->badgeColor('primary'),

            'aktif' => Tab::make('Aktif')
                ->badge($karyawanAktif)
                ->badgeColor('success')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('status_aktif', 1)),

            'tidak_aktif' => Tab::make('Non-Aktif')
                ->badge($karyawanTidakAktif)
                ->badgeColor('danger')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('status_aktif', 0)),
        ];

        // Tambahkan tab untuk setiap jenis kontrak
        $jenisKontrak = [
            'PKWTT' => ['label' => '👔 Tetap', 'color' => 'success'],
            'PKWT' => ['label' => '📝 Kontrak', 'color' => 'warning'],
            'Probation' => ['label' => '🔄 Probation', 'color' => 'info'],
            'Freelance' => ['label' => '💼 Freelance', 'color' => 'gray'],
        ];

        foreach ($jenisKontrak as $jenis => $config) {
            $jumlahKaryawan = \App\Models\Karyawan::whereHas('riwayatKontrak', function ($query) use ($jenis) {
                $query->where('jenis_kontrak', $jenis)
                    ->where('is_active', 1);
            })->count();

            $tabs['kontrak_' . strtolower($jenis)] = Tab::make($config['label'])
                ->badge($jumlahKaryawan)
                ->badgeColor($config['color'])
                ->modifyQueryUsing(function (Builder $query) use ($jenis) {
                    return $query->whereHas('riwayatKontrak', function ($q) use ($jenis) {
                        $q->where('jenis_kontrak', $jenis)
                            ->where('is_active', 1);
                    });
                });
        }

        return $tabs;
    }

    protected function getHeaderWidgets(): array
    {
        return [
            KaryawanSummary::class,
            // Widgets moved to dedicated HR Dashboard
            // Access comprehensive analytics via: HR Dashboard in navigation
        ];
    }

    public function getTitle(): string
    {
        return 'Data Karyawan';
    }
}
