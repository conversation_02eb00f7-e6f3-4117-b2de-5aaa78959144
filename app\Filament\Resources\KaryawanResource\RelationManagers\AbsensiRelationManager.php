<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Resources\RelationManagers\RelationManager;
use App\Models\Schedule;
use Illuminate\Support\Facades\Auth;
use Filament\Notifications\Notification;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

class AbsensiRelationManager extends RelationManager
{
    protected static string $relationship = 'absensi';
    protected static ?string $recordTitleAttribute = 'tanggal_absensi';
    protected static ?string $title = 'Data Absensi';

    public function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            DatePicker::make('tanggal_absensi')
                ->label('Tanggal Absensi')
                ->required()
                ->default(now())
                ->reactive()
                ->afterStateUpdated(fn(callable $set) => $set('jadwal_id', null)),

            Select::make('jadwal_id')
                ->label('Jadwal')
                ->options(function (callable $get) {
                    $karyawanId = $this->getOwnerRecord()->id;
                    $tanggal = $get('tanggal_absensi');

                    if (!$tanggal) {
                        return [];
                    }

                    return Schedule::where('karyawan_id', $karyawanId)
                        ->where('tanggal_jadwal', $tanggal)
                        ->get()
                        ->mapWithKeys(function ($jadwal) {
                            $shift = $jadwal->shift ? " ({$jadwal->shift->nama_shift})" : '';
                            return [$jadwal->id => "Jadwal {$jadwal->tanggal_jadwal->format('d/m/Y')}{$shift}"];
                        });
                })
                ->searchable()
                ->placeholder('Pilih jadwal jika ada'),

            Select::make('status')
                ->label('Status Kehadiran')
                ->options(\App\Enums\AttendanceStatus::options())
                ->required()
                ->default(\App\Enums\AttendanceStatus::HADIR->value),

            TimePicker::make('waktu_masuk')
                ->label('Waktu Masuk')
                ->seconds(false),

            TimePicker::make('waktu_keluar')
                ->label('Waktu Keluar')
                ->seconds(false),

            Textarea::make('keterangan')
                ->label('Keterangan')
                ->maxLength(1000),

            FileUpload::make('foto_masuk')
                ->label('Foto Masuk')
                ->image()
                ->directory(config('app_constants.upload_directories.attendance_photos_in'))
                ->disk('public'),

            FileUpload::make('foto_keluar')
                ->label('Foto Keluar')
                ->image()
                ->directory(config('app_constants.upload_directories.attendance_photos_out'))
                ->disk('public'),

            TextInput::make('lokasi_masuk')
                ->label('Lokasi Masuk')
                ->placeholder('Koordinat GPS atau alamat'),

            TextInput::make('lokasi_keluar')
                ->label('Lokasi Keluar')
                ->placeholder('Koordinat GPS atau alamat'),
        ]);
    }

    public function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                TextColumn::make('tanggal_absensi')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable(),

                TextColumn::make('waktu_masuk')
                    ->label('Masuk')
                    ->time('H:i')
                    ->sortable(),

                TextColumn::make('waktu_keluar')
                    ->label('Keluar')
                    ->time('H:i')
                    ->sortable(),

                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'hadir' => 'success',
                        'terlambat' => 'warning',
                        'izin' => 'info',
                        'sakit' => 'info',
                        'cuti' => 'primary',
                        'alpha' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => ucfirst($state))
                    ->sortable(),

                TextColumn::make('approvedBy.name')
                    ->label('Disetujui Oleh')
                    ->sortable(),

                TextColumn::make('approved_at')
                    ->label('Disetujui Pada')
                    ->dateTime('d M Y H:i')
                    ->sortable(),

                ImageColumn::make('foto_masuk')
                    ->label('Foto Masuk')
                    ->circular()
                    ->toggleable(isToggledHiddenByDefault: true),

                ImageColumn::make('foto_keluar')
                    ->label('Foto Keluar')
                    ->circular()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'hadir' => 'Hadir',
                        'terlambat' => 'Terlambat',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                        'cuti' => 'Cuti',
                        'alpha' => 'Alpha',
                    ]),

                Filter::make('tanggal')
                    ->form([
                        DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_absensi', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_absensi', '<=', $date),
                            );
                    }),

                Filter::make('approved')
                    ->label('Disetujui')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('approved_at'))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('approve')
                    ->label('Setujui')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn($record) => (Auth::user()->role === 'supervisor' || Auth::user()->role === 'admin') && is_null($record->approved_at))
                    ->action(function ($record) {
                        $record->update([
                            'approved_by' => Auth::id(),
                            'approved_at' => now(),
                        ]);

                        Notification::make()
                            ->title('Absensi berhasil disetujui')
                            ->body('Absensi tanggal ' . $record->tanggal_absensi->format('d M Y') . ' telah disetujui.')
                            ->success()
                            ->send();
                    }),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data) {
                        // If the user is a supervisor, auto-approve the attendance
                        $user = Auth::user();
                        if ($user->role === 'supervisor' || $user->role === 'admin') {
                            $data['approved_by'] = $user->id;
                            $data['approved_at'] = now();
                        }

                        return $data;
                    }),
            ]);
    }
}
