<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Resources\RelationManagers\RelationManager;
use Filament\Forms;
use Filament\Tables;
use Illuminate\Support\Facades\Auth;

class DokumenRelationManager extends RelationManager
{
    protected static string $relationship = 'dokumens';

    protected static ?string $label = 'Dokumen';

    protected static ?string $recordTitleAttribute = 'nama_dokumen';

    public function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('nama_dokumen')
                ->label('Nama Dokumen')
                ->required(),

            Forms\Components\FileUpload::make('file_path')
                ->label('Upload Dokumen')
                ->required()
                ->directory('dokumen_karyawan')
                ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png'])
                ->maxSize(2048),

            Forms\Components\Hidden::make('created_by')
                ->default(Auth::id())
                ->disabled(),
        ]);
    }

    public function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nama_dokumen')->label('Nama Dokumen')->sortable(),
                Tables\Columns\TextColumn::make('file_path')
                    ->label('File')
                    ->url(fn($record) => asset('storage/' . $record->file_path))
                    ->openUrlInNewTab(),

                Tables\Columns\TextColumn::make('created_by')->label('Dibuat Oleh')->sortable(),
                Tables\Columns\TextColumn::make('created_at')->label('Diupload Pada')->dateTime(),
                Tables\Columns\TextColumn::make('deleted_at')->label('Dihapus Pada')->dateTime()->toggleable(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ]);
    }
}
