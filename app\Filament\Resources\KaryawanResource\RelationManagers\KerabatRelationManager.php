<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\RelationManagers\RelationManager;

class KerabatRelationManager extends RelationManager
{
    protected static string $relationship = 'kerabatDarurat';
    protected static ?string $title = 'Kerabat Darurat';

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('nama_kerabat')->required(),
            Forms\Components\TextInput::make('hubungan')->required(),
            Forms\Components\TextInput::make('no_hp_kerabat')->required(),
            Forms\Components\Textarea::make('alamat_kerabat')->nullable(),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('nama_kerabat'),
            Tables\Columns\TextColumn::make('hubungan'),
            Tables\Columns\TextColumn::make('no_hp_kerabat'),
        ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->headerActions([Tables\Actions\CreateAction::make()]);
    }
}
