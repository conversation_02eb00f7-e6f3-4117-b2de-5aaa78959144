<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Resources\RelationManagers\RelationManager;
use Filament\Forms;
use Filament\Tables;
use Illuminate\Support\Facades\Auth;
use App\Traits\HasReferenceData;

class KpiPenilaianRelationManager extends RelationManager
{
    use HasReferenceData;

    protected static string $relationship = 'kpiPenilaians';

    protected static ?string $label = 'KPI Penilaian';

    protected static ?string $recordTitleAttribute = 'periode';

    public function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('periode')
                ->label('Periode')
                ->required()
                ->placeholder('Misal: 2025-01')
                ->helperText('Format: YYYY-MM'),

            Forms\Components\TextInput::make('target_kpi')
                ->label('Target KPI (%)')
                ->numeric()
                ->step(0.01)
                ->required()
                ->suffix('%')
                ->minValue(0)
                ->maxValue(999.99),

            Forms\Components\TextInput::make('realisasi_kpi')
                ->label('Realisasi KPI (%)')
                ->numeric()
                ->step(0.01)
                ->required()
                ->suffix('%')
                ->minValue(0)
                ->maxValue(999.99),

            Forms\Components\Select::make('nilai_akhir')
                ->label('Nilai Akhir')
                ->options(static::getKpiNilaiAkhirOptions())
                ->nullable(),

            Forms\Components\Select::make('status_penilaian')
                ->label('Status Penilaian')
                ->options(static::getKpiStatusPenilaianOptions())
                ->default('Draft')
                ->required(),

            Forms\Components\Select::make('penilai_id')
                ->label('Penilai')
                ->relationship('penilai', 'name')
                ->searchable()
                ->nullable(),

            Forms\Components\DatePicker::make('tanggal_penilaian')
                ->label('Tanggal Penilaian')
                ->nullable(),

            Forms\Components\Textarea::make('keterangan')
                ->label('Keterangan')
                ->nullable()
                ->rows(3),

            Forms\Components\Hidden::make('created_by')
                ->default(Auth::id()),
        ]);
    }

    public function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('periode')
                    ->label('Periode')
                    ->sortable()
                    ->searchable()
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('target_kpi')
                    ->label('Target KPI')
                    ->numeric(decimalPlaces: 2)
                    ->suffix('%')
                    ->sortable(),

                Tables\Columns\TextColumn::make('realisasi_kpi')
                    ->label('Realisasi KPI')
                    ->numeric(decimalPlaces: 2)
                    ->suffix('%')
                    ->sortable()
                    ->color(
                        fn($state, $record) =>
                        $state >= $record->target_kpi ? 'success' : ($state >= ($record->target_kpi * 0.8) ? 'warning' : 'danger')
                    ),

                Tables\Columns\TextColumn::make('achievement_percentage')
                    ->label('Pencapaian')
                    ->getStateUsing(fn($record) => $record->achievement_percentage)
                    ->suffix('%')
                    ->badge()
                    ->color(
                        fn($state) =>
                        $state >= 100 ? 'success' : ($state >= 80 ? 'warning' : 'danger')
                    )
                    ->sortable(),

                Tables\Columns\TextColumn::make('nilai_akhir')
                    ->label('Nilai Akhir')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'A' => 'success',
                        'B' => 'info',
                        'C' => 'warning',
                        'D' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('status_penilaian')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Selesai' => 'success',
                        'Proses' => 'warning',
                        'Draft' => 'gray',
                        default => 'primary',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('penilai.name')
                    ->label('Penilai')
                    ->default('Belum ditentukan')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('tanggal_penilaian')
                    ->label('Tanggal Penilaian')
                    ->date('d M Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('keterangan')
                    ->label('Keterangan')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat Pada')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('nilai_akhir')
                    ->label('Nilai Akhir')
                    ->options(static::getKpiNilaiAkhirOptions()),

                Tables\Filters\SelectFilter::make('status_penilaian')
                    ->label('Status Penilaian')
                    ->options(static::getKpiStatusPenilaianOptions()),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Tambah Penilaian KPI'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('periode', 'desc')
            ->emptyStateHeading('Belum Ada Penilaian KPI')
            ->emptyStateDescription('Belum ada data penilaian KPI untuk karyawan ini.')
            ->emptyStateIcon('heroicon-o-chart-bar');
    }
}
