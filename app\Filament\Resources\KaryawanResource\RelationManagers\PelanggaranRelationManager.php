<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Resources\RelationManagers\RelationManager;
use Filament\Forms;
use Filament\Tables;
use Illuminate\Support\Facades\Auth;

class PelanggaranRelationManager extends RelationManager
{
    protected static string $relationship = 'pelanggarans';

    protected static ?string $title = 'Pelanggaran';

    protected static ?string $recordTitleAttribute = 'jenis';

    public function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('jenis_pelanggaran_id')
                ->label('Jenis Pelanggaran')
                ->relationship('jenisPelanggaran', 'nama_pelanggaran')
                ->searchable()
                ->preload()
                ->required()
                ->reactive()
                ->afterStateUpdated(function ($state, callable $set) {
                    if ($state) {
                        $jenisPelanggaran = \App\Models\JenisPelanggaran::find($state);
                        if ($jenisPelanggaran) {
                            $set('preview_denda', $jenisPelanggaran->formatted_denda);
                        }
                    }
                }),

            Forms\Components\Placeholder::make('preview_denda')
                ->label('Preview Denda')
                ->content(fn(callable $get) => $get('preview_denda') ?? '-')
                ->visible(fn(callable $get) => !empty($get('jenis_pelanggaran_id'))),

            Forms\Components\TextInput::make('jenis_manual')
                ->label('Jenis Pelanggaran (Manual)')
                ->helperText('Isi jika jenis pelanggaran tidak tersedia di dropdown')
                ->visible(fn(callable $get) => empty($get('jenis_pelanggaran_id'))),

            Forms\Components\DatePicker::make('tanggal')
                ->label('Tanggal Pelanggaran')
                ->required()
                ->displayFormat('d-m-Y')
                ->default(now()),

            Forms\Components\Textarea::make('keterangan')
                ->label('Keterangan')
                ->rows(3)
                ->placeholder('Keterangan detail pelanggaran'),

            Forms\Components\Hidden::make('created_by')
                ->default(Auth::id()),
        ]);
    }

    public function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('jenis')
                    ->label('Jenis Pelanggaran')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('jenisPelanggaran.kategori')
                    ->label('Kategori')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'ringan' => 'success',
                        'sedang' => 'warning',
                        'berat' => 'danger',
                        default => 'gray',
                    })
                    ->placeholder('-'),

                Tables\Columns\TextColumn::make('tanggal')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('formatted_denda')
                    ->label('Denda')
                    ->sortable(query: function ($query, $direction) {
                        return $query->orderBy('nominal_denda', $direction);
                    })
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('keterangan')
                    ->label('Keterangan')
                    ->limit(50)
                    ->placeholder('-'),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Dibuat Oleh')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat Pada')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis_pelanggaran_id')
                    ->label('Jenis Pelanggaran')
                    ->relationship('jenisPelanggaran', 'nama_pelanggaran'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->defaultSort('tanggal', 'desc');
    }
}
