<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Notifications\Notification;

class PendidikanRelationManager extends RelationManager
{
    protected static string $relationship = 'pendidikan';
    protected static ?string $title = 'Riwayat Pendidikan';

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Select::make('tingkat')
                ->label('Tingkat Pendidikan')
                ->options([
                    'SD' => 'SD',
                    'SMP' => 'SMP',
                    'SMA/SMK' => 'SMA/SMK',
                    'D1' => 'D1',
                    'D2' => 'D2',
                    'D3' => 'D3',
                    'S1' => 'S1',
                    'S2' => 'S2',
                    'S3' => 'S3',
                ])
                ->required()

                ->helperText('Setiap karyawan hanya boleh memiliki satu tingkat pendidikan yang sama.'),
            Forms\Components\TextInput::make('institusi')->required(),
            Forms\Components\TextInput::make('jurusan')->required(),
            // max this year
            Forms\Components\TextInput::make('tahun_lulus')->numeric()->nullable()->maxValue(date('Y')),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('tingkat'),
            Tables\Columns\TextColumn::make('institusi'),
            Tables\Columns\TextColumn::make('jurusan'),
            Tables\Columns\TextColumn::make('tahun_lulus'),
        ])
            ->defaultSort('tahun_lulus', 'desc')
            ->actions([
                Tables\Actions\EditAction::make()
                    ->before(function (array $data, $action) {
                        if (!$this->validateDuplicateEducation($data, $this->mountedTableActionRecord)) {
                            $action->halt();
                        }
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->before(function (array $data, $action) {
                        if (!$this->validateDuplicateEducation($data)) {
                            $action->halt();
                        }
                    })
            ]);
    }

    protected function validateDuplicateEducation(array $data, $excludeId = null): bool
    {
        $karyawanId = $this->getOwnerRecord()->id;

        $exists = \App\Models\PendidikanKaryawan::where('karyawan_id', $karyawanId)
            ->where('tingkat', $data['tingkat'])
            ->when($excludeId, function ($query) use ($excludeId) {
                return $query->where('id', '!=', $excludeId);
            })
            ->exists();

        if ($exists) {
            Notification::make()
                ->title('Tingkat Pendidikan Sudah Ada')
                ->body('Tingkat pendidikan ini sudah ada untuk karyawan ini. Silakan pilih tingkat pendidikan yang berbeda.')
                ->danger()
                ->send();

            return false;
        }

        return true;
    }
}
