<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class PotonganKaryawanRelationManager extends RelationManager
{
    protected static string $relationship = 'potonganKaryawan';

    protected static ?string $title = 'Potongan Karyawan';

    protected static ?string $modelLabel = 'Potongan';

    protected static ?string $pluralModelLabel = 'Potongan Karyawan';

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Select::make('jenis_potongan')
                ->label('Jenis Potongan')
                ->options([
                    'kasir' => 'Potongan Kasir',
                    'stok_opname' => 'Potongan Stok Opname',
                    'retur' => 'Potongan Retur',
                    'kasbon' => 'Potongan Kasbon',
                ])
                ->required()
                ->searchable()
                ->preload(),

            Forms\Components\TextInput::make('nominal')
                ->label('Nominal Potongan')
                ->required()
                ->numeric()
                ->prefix('Rp')
                ->minValue(0)
                ->step(1000)
                ->placeholder('0')
                ->formatStateUsing(fn($state) => $state ? number_format($state, 0, ',', '.') : '')
                ->dehydrateStateUsing(fn($state) => (float) str_replace(['.', ','], ['', '.'], $state)),

            Forms\Components\DatePicker::make('bulan_potongan')
                ->label('Tanggal Potongan')
                ->required()
                ->native(false)
                ->displayFormat('d M Y')
                ->helperText('Pilih tanggal untuk periode potongan'),

            Forms\Components\Textarea::make('keterangan')
                ->label('Keterangan')
                ->placeholder('Keterangan tambahan (opsional)')
                ->rows(3)
                ->columnSpanFull(),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('jenis_potongan')
            ->columns([
                Tables\Columns\TextColumn::make('jenis_potongan')
                    ->label('Jenis Potongan')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'kasir' => 'warning',
                        'stok_opname' => 'info',
                        'retur' => 'danger',
                        'kasbon' => 'secondary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'kasir' => 'Potongan Kasir',
                        'stok_opname' => 'Potongan Stok Opname',
                        'retur' => 'Potongan Retur',
                        'kasbon' => 'Potongan Kasbon',
                        default => $state,
                    })
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('nominal')
                    ->label('Nominal')
                    ->money('IDR')
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('bulan_potongan')
                    ->label('Tanggal Potongan')
                    ->date('d M Y')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('keterangan')
                    ->label('Keterangan')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->placeholder('-'),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Dibuat Oleh')
                    ->placeholder('-')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tanggal Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis_potongan')
                    ->label('Jenis Potongan')
                    ->options([
                        'kasir' => 'Potongan Kasir',
                        'stok_opname' => 'Potongan Stok Opname',
                        'retur' => 'Potongan Retur',
                        'kasbon' => 'Potongan Kasbon',
                    ]),

                Tables\Filters\Filter::make('tanggal_potongan')
                    ->form([
                        Forms\Components\DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal')
                            ->native(false),
                        Forms\Components\DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal')
                            ->native(false),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn($query, $date) => $query->whereDate('bulan_potongan', '>=', $date)
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn($query, $date) => $query->whereDate('bulan_potongan', '<=', $date)
                            );
                    }),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Tambah Potongan')
                    ->icon('heroicon-o-plus')
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['created_by'] = auth()->id();
                        return $data;
                    })
                    ->successNotification(
                        fn() => \Filament\Notifications\Notification::make()
                            ->success()
                            ->title('Potongan berhasil ditambahkan')
                            ->body('Data potongan karyawan telah disimpan.')
                    ),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->icon('heroicon-o-pencil-square')
                    ->successNotification(
                        fn() => \Filament\Notifications\Notification::make()
                            ->success()
                            ->title('Potongan berhasil diperbarui')
                            ->body('Data potongan karyawan telah diperbarui.')
                    ),

                Tables\Actions\DeleteAction::make()
                    ->icon('heroicon-o-trash')
                    ->successNotification(
                        fn() => \Filament\Notifications\Notification::make()
                            ->success()
                            ->title('Potongan berhasil dihapus')
                            ->body('Data potongan karyawan telah dihapus.')
                    ),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->successNotification(
                            fn() => \Filament\Notifications\Notification::make()
                                ->success()
                                ->title('Potongan berhasil dihapus')
                                ->body('Data potongan karyawan yang dipilih telah dihapus.')
                        ),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Belum ada data potongan')
            ->emptyStateDescription('Tambahkan potongan karyawan dengan mengklik tombol "Tambah Potongan".')
            ->emptyStateIcon('heroicon-o-currency-dollar');
    }
}
