<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\RelationManagers\RelationManager;

class ResignRelationManager extends RelationManager
{
    protected static string $relationship = 'resignLogs';
    protected static ?string $title = 'Resign Log';

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\DatePicker::make('tanggal_resign')->required(),
            Forms\Components\Textarea::make('alasan_resign')->required(),
            Forms\Components\Textarea::make('catatan')->nullable(),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('tanggal_resign')->date(),
            Tables\Columns\TextColumn::make('alasan_resign')->limit(30),
        ])
            ->defaultSort('tanggal_resign', 'desc')
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->headerActions([Tables\Actions\CreateAction::make()]);
    }
}
