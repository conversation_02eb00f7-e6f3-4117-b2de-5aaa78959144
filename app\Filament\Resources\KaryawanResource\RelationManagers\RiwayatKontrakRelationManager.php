<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;

class RiwayatKontrakRelationManager extends RelationManager
{
    protected static string $relationship = 'riwayatKontrak';
    protected static ?string $title = 'Riwayat Kontrak';

    public function form(Form $form): Form
    {
        return $form->schema([
            TextInput::make('no_kontrak')
                ->label('Nomor Kontrak')
                ->required()
                ->unique('riwayat_kontrak', 'no_kontrak', fn($record) => $record),

            Select::make('jenis_kontrak')
                ->label('Jenis Kontrak')
                ->options([
                    'PKWT' => 'PKWT',
                    'PKWTT' => 'PKWTT',
                    'Probation' => 'Probation',
                    'Freelance' => 'Freelance'
                ])
                ->required()
                ->live(),

            DatePicker::make('tgl_mulai')
                ->label('Tanggal Mulai')
                ->required(),

            DatePicker::make('tgl_selesai')
                ->label('Tanggal Berakhir Kontrak')
                ->displayFormat('d-m-Y')
                ->nullable()
                ->disabled(fn($get) => in_array($get('jenis_kontrak'), ['PKWTT']))
                ->placeholder(function ($get) {
                   if ($get('jenis_kontrak') === 'PKWTT') {
                        return 'Tidak Perlu Diisi';
                    } else {
                        return 'Pilih Tanggal';
                    }
                })
                ->after('tgl_mulai'),

            DatePicker::make('tanggal_mulai_kerja')
                ->label('Tanggal Mulai Kerja')
                ->displayFormat('d-m-Y')
                ->nullable()
                ->required(),

            Textarea::make('keterangan')
                ->label('Keterangan')
                ->nullable(),

            Toggle::make('is_active')
                ->label('Aktif?'),
        ]);
    }


    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('no_kontrak')
                    ->label('Nomor Kontrak')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('jenis_kontrak')->label('Jenis Kontrak'),

                TextColumn::make('tgl_mulai')->label('Mulai')->date('d M Y'),

                TextColumn::make('tgl_selesai')->label('Selesai')->date('d M Y'),

                TextColumn::make('tanggal_mulai_kerja')
                    ->label('Tgl. Mulai Kerja')
                    ->date('d M Y')
                    ->sortable(),

                IconColumn::make('is_active')->label('Aktif')->boolean(),
            ])
            ->defaultSort('tgl_mulai', 'desc')
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->headerActions([
                CreateAction::make(),
            ]);
    }
}
