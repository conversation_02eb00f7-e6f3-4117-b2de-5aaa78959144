<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Resources\RelationManagers\RelationManager;
use App\Models\Shift;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

class SchedulesRelationManager extends RelationManager
{
    protected static string $relationship = 'schedules';
    protected static ?string $recordTitleAttribute = 'tanggal_jadwal';
    protected static ?string $title = 'Jadwal Kerja';

    public function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            DatePicker::make('tanggal_jadwal')
                ->label('Tanggal Jadwal')
                ->required(),

            Select::make('shift_id')
                ->label('Shift')
                ->options(Shift::where('is_active', true)->pluck('nama_shift', 'id'))
                ->searchable()
                ->reactive()
                ->afterStateUpdated(function ($state, callable $set) {
                    if ($state) {
                        $shift = Shift::find($state);
                        if ($shift) {
                            $set('waktu_masuk', $shift->waktu_mulai);
                            $set('waktu_keluar', $shift->waktu_selesai);
                        }
                    }
                }),

            TimePicker::make('waktu_masuk')
                ->label('Waktu Masuk')
                ->required()
                ->seconds(false),

            TimePicker::make('waktu_keluar')
                ->label('Waktu Keluar')
                ->required()
                ->seconds(false),

            Select::make('status')
                ->label('Status')
                ->options([
                    'Hadir' => 'Hadir',
                    'Libur' => 'Libur',
                    'Cuti' => 'Cuti',
                    'Izin' => 'Izin',
                    'Sakit' => 'Sakit',
                ])
                ->default('Hadir')
                ->required(),

            TextInput::make('keterangan')
                ->label('Keterangan')
                ->maxLength(255),

            Toggle::make('is_approved')
                ->label('Disetujui')
                ->default(true)
                ->visible(fn () => Auth::user()->role === 'supervisor' || Auth::user()->role === 'admin'),
        ]);
    }

    public function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                TextColumn::make('tanggal_jadwal')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable(),

                TextColumn::make('shift.nama_shift')
                    ->label('Shift')
                    ->sortable(),

                TextColumn::make('waktu_masuk')
                    ->label('Masuk')
                    ->time('H:i')
                    ->sortable(),

                TextColumn::make('waktu_keluar')
                    ->label('Keluar')
                    ->time('H:i')
                    ->sortable(),

                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Hadir' => 'success',
                        'Libur' => 'info',
                        'Cuti' => 'warning',
                        'Izin' => 'warning',
                        'Sakit' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                IconColumn::make('is_approved')
                    ->label('Disetujui')
                    ->boolean()
                    ->sortable(),

                TextColumn::make('supervisor.name')
                    ->label('Dibuat Oleh')
                    ->sortable(),
            ])
            ->defaultSort('tanggal_jadwal', 'desc')
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'Hadir' => 'Hadir',
                        'Libur' => 'Libur',
                        'Cuti' => 'Cuti',
                        'Izin' => 'Izin',
                        'Sakit' => 'Sakit',
                    ]),

                Filter::make('tanggal')
                    ->form([
                        DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn (Builder $query, $date): Builder => $query->whereDate('tanggal_jadwal', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn (Builder $query, $date): Builder => $query->whereDate('tanggal_jadwal', '<=', $date),
                            );
                    }),

                Filter::make('is_approved')
                    ->label('Disetujui')
                    ->query(fn (Builder $query): Builder => $query->where('is_approved', true))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data) {
                        $user = Auth::user();
                        if ($user->role === 'supervisor' || $user->role === 'admin') {
                            $data['supervisor_id'] = $user->id;
                        }
                        return $data;
                    }),
            ]);
    }
}
