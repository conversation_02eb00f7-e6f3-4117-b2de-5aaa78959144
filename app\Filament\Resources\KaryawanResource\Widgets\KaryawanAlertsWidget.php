<?php

namespace App\Filament\Resources\KaryawanResource\Widgets;


use App\Models\Absensi;
use App\Models\Pelanggaran;
use App\Models\KpiPenilaian;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class KaryawanAlertsWidget extends BaseWidget
{
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 'full';

    protected function getStats(): array
    {
        return [


            // Poor Performance Alert
            Stat::make('Performance Rendah', $this->getPoorPerformanceCount())
                ->description('KPI < 70% bulan ini')
                ->descriptionIcon('heroicon-m-arrow-trending-down')
                ->color('danger'),

            // High Achievers
            Stat::make('Top Performers', $this->getTopPerformersCount())
                ->description('KPI > 90% bulan ini')
                ->descriptionIcon('heroicon-m-star')
                ->color('success'),

            // Frequent Late Arrivals
            Stat::make('Sering Terlambat', $this->getFrequentLateCount())
                ->description('Terlambat > 5x bulan ini')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            // Pending Approvals
            Stat::make('Pending Approval', $this->getPendingApprovalsCount())
                ->description('Absensi menunggu persetujuan')
                ->descriptionIcon('heroicon-m-clipboard-document-check')
                ->color('info'),
        ];
    }



    private function getPoorPerformanceCount(): int
    {
        return KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->where('realisasi_kpi', '<', 70)
            ->distinct('karyawan_id')
            ->count();
    }

    private function getTopPerformersCount(): int
    {
        return KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->where('realisasi_kpi', '>', 90)
            ->distinct('karyawan_id')
            ->count();
    }

    private function getFrequentLateCount(): int
    {
        return Absensi::selectRaw('karyawan_id, COUNT(*) as late_count')
            ->whereMonth('tanggal_absensi', now()->month)
            ->where('status', 'terlambat')
            ->groupBy('karyawan_id')
            ->havingRaw('late_count > 5')
            ->count();
    }

    private function getPendingApprovalsCount(): int
    {
        // Assuming there's an approval system for attendance
        // This is a placeholder - adjust based on your actual approval system
        return Absensi::whereMonth('tanggal_absensi', now()->month)
            ->whereIn('status', ['izin', 'sakit'])
            ->whereNull('approved_at') // Assuming there's an approved_at column
            ->count();
    }
}
