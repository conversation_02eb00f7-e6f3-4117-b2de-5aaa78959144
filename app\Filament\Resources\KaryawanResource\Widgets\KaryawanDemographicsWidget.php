<?php

namespace App\Filament\Resources\KaryawanResource\Widgets;

use App\Models\Karyawan;
use App\Models\Departemen;
use App\Models\Entitas;
use App\Models\PendidikanKaryawan;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class KaryawanDemographicsWidget extends ChartWidget
{
    protected static ?string $heading = 'Distribusi Karyawan';
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'gender';

    protected function getFilters(): ?array
    {
        return [
            'gender' => 'Jenis Kelamin',
            'department' => 'Departemen',
            'entity' => 'Entitas',
            'education' => 'Pendidikan',
            'age' => 'Kelompok Usia',
        ];
    }

    protected function getData(): array
    {
        $filter = $this->filter;

        switch ($filter) {
            case 'gender':
                return $this->getGenderData();
            case 'department':
                return $this->getDepartmentData();
            case 'entity':
                return $this->getEntityData();
            case 'education':
                return $this->getEducationData();
            case 'age':
                return $this->getAgeData();
            default:
                return $this->getGenderData();
        }
    }

    protected function getType(): string
    {
        return $this->filter === 'department' || $this->filter === 'age' ? 'bar' : 'doughnut';
    }

    private function getGenderData(): array
    {
        $maleCount = Karyawan::where('jenis_kelamin', 'Laki-laki')->count();
        $femaleCount = Karyawan::where('jenis_kelamin', 'Perempuan')->count();

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Karyawan',
                    'data' => [$maleCount, $femaleCount],
                    'backgroundColor' => [
                        'rgb(59, 130, 246)', // Blue for male
                        'rgb(236, 72, 153)', // Pink for female
                    ],
                ],
            ],
            'labels' => ['Laki-laki', 'Perempuan'],
        ];
    }

    private function getDepartmentData(): array
    {
        $departments = Departemen::withCount('karyawan')
            ->orderBy('karyawan_count', 'desc')
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Karyawan',
                    'data' => $departments->pluck('karyawan_count')->toArray(),
                    'backgroundColor' => [
                        'rgb(59, 130, 246)',
                        'rgb(16, 185, 129)',
                        'rgb(245, 158, 11)',
                        'rgb(239, 68, 68)',
                        'rgb(139, 92, 246)',
                        'rgb(236, 72, 153)',
                        'rgb(34, 197, 94)',
                        'rgb(251, 146, 60)',
                        'rgb(168, 85, 247)',
                        'rgb(14, 165, 233)',
                    ],
                ],
            ],
            'labels' => $departments->pluck('nama_departemen')->toArray(),
        ];
    }

    private function getEntityData(): array
    {
        $entities = Entitas::withCount('karyawan')
            ->orderBy('karyawan_count', 'desc')
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Karyawan',
                    'data' => $entities->pluck('karyawan_count')->toArray(),
                    'backgroundColor' => [
                        'rgb(59, 130, 246)',
                        'rgb(16, 185, 129)',
                        'rgb(245, 158, 11)',
                    ],
                ],
            ],
            'labels' => $entities->pluck('nama')->toArray(),
        ];
    }

    private function getEducationData(): array
    {
        $educationLevels = ['SMA', 'D3', 'S1', 'S2', 'S3'];
        $educationData = [];

        foreach ($educationLevels as $level) {
            $count = PendidikanKaryawan::where('tingkat', $level)->count();
            $educationData[] = $count;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Karyawan',
                    'data' => $educationData,
                    'backgroundColor' => [
                        'rgb(239, 68, 68)',   // Red for SMA
                        'rgb(245, 158, 11)',  // Yellow for D3
                        'rgb(34, 197, 94)',   // Green for S1
                        'rgb(59, 130, 246)',  // Blue for S2
                        'rgb(139, 92, 246)',  // Purple for S3
                    ],
                ],
            ],
            'labels' => $educationLevels,
        ];
    }

    private function getAgeData(): array
    {
        $ageGroups = [
            '20-30' => [20, 30],
            '31-40' => [31, 40],
            '41-50' => [41, 50],
            '51-60' => [51, 60],
            '60+' => [61, 100],
        ];

        $ageData = [];
        $labels = [];

        foreach ($ageGroups as $label => $range) {
            $startDate = Carbon::now()->subYears($range[1])->format('Y-m-d');
            $endDate = Carbon::now()->subYears($range[0])->format('Y-m-d');

            $count = Karyawan::whereBetween('tanggal_lahir', [$startDate, $endDate])->count();
            
            $ageData[] = $count;
            $labels[] = $label;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Karyawan',
                    'data' => $ageData,
                    'backgroundColor' => [
                        'rgb(34, 197, 94)',   // Green for 20-30
                        'rgb(59, 130, 246)',  // Blue for 31-40
                        'rgb(245, 158, 11)',  // Yellow for 41-50
                        'rgb(239, 68, 68)',   // Red for 51-60
                        'rgb(139, 92, 246)',  // Purple for 60+
                    ],
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'enabled' => true,
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}
