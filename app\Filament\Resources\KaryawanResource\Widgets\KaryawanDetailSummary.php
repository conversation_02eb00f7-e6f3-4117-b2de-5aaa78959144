<?php

namespace App\Filament\Resources\KaryawanResource\Widgets;

use App\Models\Karyawan;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class KaryawanDetailSummary extends BaseWidget
{
    public ?Karyawan $record = null;

    protected function getStats(): array
    {
        // Kontrak aktif terbaru
        $kontrak = $this->record?->riwayatKontrak()
            ->where('is_active', 1)
            ->latest('tgl_selesai')
            ->first();

        $nama = $this->record?->nama_lengkap ?? '-';
        $status = $this->record?->status_aktif ? '🟢 Aktif' : '🔴 Tidak Aktif';
        $nip = $this->record?->nip ?? '-';

        // Info kontrak
        if ($kontrak) {
            $tglSelesai = Carbon::parse($kontrak->tgl_selesai);
            $sisaHari = now()->diffInDays($tglSelesai, false);
            $labelSisa = $sisaHari >= 0 ? "$sisaHari Hari Lagi" : abs($sisaHari) . ' Hari Lewat';
            $warnaSisa = $sisaHari < 0 ? 'danger' : ($sisaHari <= 7 ? 'warning' : 'success');
            $deskripsiKontrak = 'Berakhir: ' . $tglSelesai->translatedFormat('d M Y');
        } else {
            $labelSisa = 'Tidak Ada';
            $warnaSisa = 'gray';
            $deskripsiKontrak = 'Belum ada kontrak aktif';
        }

        return [
            Stat::make('👤 Nama', $nama)->color('primary'),
            Stat::make('🪪 NIP', $nip)->color('gray'),
            Stat::make('📌 Status', $status)->color($this->record?->status_aktif ? 'success' : 'danger'),
            Stat::make('📅 Sisa Kontrak', $labelSisa)
                ->description($deskripsiKontrak)
                ->color($warnaSisa),
        ];
    }
}
