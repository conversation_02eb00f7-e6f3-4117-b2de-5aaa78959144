<?php

namespace App\Filament\Resources\KaryawanResource\Widgets;

use App\Models\KpiPenilaian;
use App\Models\Departemen;
use App\Models\Absensi;
use App\Models\Pelanggaran;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class KaryawanPerformanceWidget extends ChartWidget
{
    protected static ?string $heading = 'Performance & Trends';
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'kpi_monthly';

    protected function getFilters(): ?array
    {
        return [
            'kpi_monthly' => 'KPI Bulanan',
            'kpi_department' => 'KPI per Departemen',
            'attendance_trend' => 'Trend Kehadiran',
            'violation_trend' => 'Trend Pelanggaran',
        ];
    }

    protected function getData(): array
    {
        $filter = $this->filter;

        switch ($filter) {
            case 'kpi_monthly':
                return $this->getKpiMonthlyData();
            case 'kpi_department':
                return $this->getKpiDepartmentData();
            case 'attendance_trend':
                return $this->getAttendanceTrendData();
            case 'violation_trend':
                return $this->getViolationTrendData();
            default:
                return $this->getKpiMonthlyData();
        }
    }

    protected function getType(): string
    {
        return match ($this->filter) {
            'kpi_department' => 'radar',
            'kpi_monthly', 'attendance_trend', 'violation_trend' => 'line',
            default => 'line',
        };
    }

    private function getKpiMonthlyData(): array
    {
        $months = [];
        $kpiData = [];
        $targetData = [];

        // Get last 6 months
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthYear = $date->format('Y-m');
            $monthLabel = $date->format('M Y');

            $avgKpi = KpiPenilaian::where('periode', $monthYear)
                ->avg('realisasi_kpi');
            
            $avgTarget = KpiPenilaian::where('periode', $monthYear)
                ->avg('target_kpi');

            $months[] = $monthLabel;
            $kpiData[] = $avgKpi ? round($avgKpi, 1) : 0;
            $targetData[] = $avgTarget ? round($avgTarget, 1) : 0;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Realisasi KPI',
                    'data' => $kpiData,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Target KPI',
                    'data' => $targetData,
                    'borderColor' => 'rgb(239, 68, 68)',
                    'backgroundColor' => 'rgba(239, 68, 68, 0.1)',
                    'tension' => 0.4,
                    'borderDash' => [5, 5],
                ],
            ],
            'labels' => $months,
        ];
    }

    private function getKpiDepartmentData(): array
    {
        $departments = Departemen::with(['karyawan.kpiPenilaian' => function($query) {
            $query->whereMonth('tanggal_penilaian', now()->month);
        }])->get();

        $departmentNames = [];
        $kpiAverages = [];

        foreach ($departments as $department) {
            $kpiValues = $department->karyawan
                ->flatMap->kpiPenilaian
                ->pluck('realisasi_kpi');

            if ($kpiValues->count() > 0) {
                $departmentNames[] = $department->nama_departemen;
                $kpiAverages[] = round($kpiValues->avg(), 1);
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Rata-rata KPI',
                    'data' => $kpiAverages,
                    'backgroundColor' => 'rgba(59, 130, 246, 0.2)',
                    'borderColor' => 'rgb(59, 130, 246)',
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $departmentNames,
        ];
    }

    private function getAttendanceTrendData(): array
    {
        $weeks = [];
        $attendanceRates = [];

        // Get last 8 weeks
        for ($i = 7; $i >= 0; $i--) {
            $startOfWeek = Carbon::now()->subWeeks($i)->startOfWeek();
            $endOfWeek = Carbon::now()->subWeeks($i)->endOfWeek();
            
            $totalAbsensi = Absensi::whereBetween('tanggal_absensi', [
                $startOfWeek->format('Y-m-d'),
                $endOfWeek->format('Y-m-d')
            ])->count();

            $hadirCount = Absensi::whereBetween('tanggal_absensi', [
                $startOfWeek->format('Y-m-d'),
                $endOfWeek->format('Y-m-d')
            ])->whereIn('status', ['hadir', 'terlambat'])->count();

            $rate = $totalAbsensi > 0 ? ($hadirCount / $totalAbsensi) * 100 : 0;

            $weeks[] = 'Week ' . ($i + 1);
            $attendanceRates[] = round($rate, 1);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Tingkat Kehadiran (%)',
                    'data' => $attendanceRates,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'tension' => 0.4,
                    'fill' => true,
                ],
            ],
            'labels' => $weeks,
        ];
    }

    private function getViolationTrendData(): array
    {
        $months = [];
        $violationCounts = [];

        // Get last 6 months
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthLabel = $date->format('M Y');

            $count = Pelanggaran::whereYear('tanggal', $date->year)
                ->whereMonth('tanggal', $date->month)
                ->count();

            $months[] = $monthLabel;
            $violationCounts[] = $count;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Pelanggaran',
                    'data' => $violationCounts,
                    'borderColor' => 'rgb(239, 68, 68)',
                    'backgroundColor' => 'rgba(239, 68, 68, 0.1)',
                    'tension' => 0.4,
                    'fill' => true,
                ],
            ],
            'labels' => $months,
        ];
    }

    protected function getOptions(): array
    {
        $baseOptions = [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'enabled' => true,
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];

        if ($this->filter === 'kpi_department') {
            $baseOptions['scales'] = [
                'r' => [
                    'beginAtZero' => true,
                    'max' => 100,
                    'ticks' => [
                        'stepSize' => 20,
                    ],
                ],
            ];
        } else {
            $baseOptions['scales'] = [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => "function(value) { return value + '%'; }",
                    ],
                ],
            ];
        }

        return $baseOptions;
    }
}
