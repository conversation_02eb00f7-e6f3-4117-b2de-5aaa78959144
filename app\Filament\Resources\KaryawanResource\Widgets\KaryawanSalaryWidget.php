<?php

namespace App\Filament\Resources\KaryawanResource\Widgets;

use App\Models\PenggajianKaryawan;
use App\Models\Departemen;
use App\Models\Jabatan;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class KaryawanSalaryWidget extends ChartWidget
{
    protected static ?string $heading = 'Analisis Gaj<PERSON> & Kompensasi';
    protected static ?int $sort = 5;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'salary_range';

    protected function getFilters(): ?array
    {
        return [
            'salary_range' => 'Distribusi Gaji',
            'department_salary' => 'Gaji per Departemen',
            'position_salary' => 'Gaji per Jabatan',
            'salary_trend' => 'Trend Gaji Bulanan',
        ];
    }

    protected function getData(): array
    {
        $filter = $this->filter;

        switch ($filter) {
            case 'salary_range':
                return $this->getSalaryRangeData();
            case 'department_salary':
                return $this->getDepartmentSalaryData();
            case 'position_salary':
                return $this->getPositionSalaryData();
            case 'salary_trend':
                return $this->getSalaryTrendData();
            default:
                return $this->getSalaryRangeData();
        }
    }

    protected function getType(): string
    {
        return match ($this->filter) {
            'salary_trend' => 'line',
            'department_salary', 'position_salary' => 'bar',
            default => 'doughnut',
        };
    }

    private function getSalaryRangeData(): array
    {
        $ranges = [
            '< 5M' => [0, 5000000],
            '5M - 10M' => [5000000, 10000000],
            '10M - 15M' => [10000000, 15000000],
            '15M - 20M' => [15000000, 20000000],
            '> 20M' => [20000000, 999999999],
        ];

        $rangeData = [];
        $labels = [];

        foreach ($ranges as $label => $range) {
            $count = PenggajianKaryawan::whereBetween('gaji_pokok', $range)
                ->distinct('karyawan_id')
                ->count();
            
            $rangeData[] = $count;
            $labels[] = $label;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Karyawan',
                    'data' => $rangeData,
                    'backgroundColor' => [
                        'rgb(239, 68, 68)',   // Red for < 5M
                        'rgb(245, 158, 11)',  // Yellow for 5M-10M
                        'rgb(34, 197, 94)',   // Green for 10M-15M
                        'rgb(59, 130, 246)',  // Blue for 15M-20M
                        'rgb(139, 92, 246)',  // Purple for > 20M
                    ],
                ],
            ],
            'labels' => $labels,
        ];
    }

    private function getDepartmentSalaryData(): array
    {
        $departments = Departemen::with(['karyawan.penggajian' => function($query) {
            $query->latest('periode_gaji');
        }])->get();

        $departmentNames = [];
        $avgSalaries = [];

        foreach ($departments as $department) {
            $salaries = $department->karyawan
                ->map(function($karyawan) {
                    return $karyawan->penggajian->first()?->gaji_pokok ?? 0;
                })
                ->filter(fn($salary) => $salary > 0);

            if ($salaries->count() > 0) {
                $departmentNames[] = $department->nama_departemen;
                $avgSalaries[] = round($salaries->avg() / 1000000, 1); // Convert to millions
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Rata-rata Gaji (Juta)',
                    'data' => $avgSalaries,
                    'backgroundColor' => [
                        'rgb(59, 130, 246)',
                        'rgb(16, 185, 129)',
                        'rgb(245, 158, 11)',
                        'rgb(239, 68, 68)',
                        'rgb(139, 92, 246)',
                        'rgb(236, 72, 153)',
                        'rgb(34, 197, 94)',
                        'rgb(251, 146, 60)',
                        'rgb(168, 85, 247)',
                        'rgb(14, 165, 233)',
                    ],
                ],
            ],
            'labels' => $departmentNames,
        ];
    }

    private function getPositionSalaryData(): array
    {
        $positions = Jabatan::with(['karyawan.penggajian' => function($query) {
            $query->latest('periode_gaji');
        }])->get();

        $positionNames = [];
        $avgSalaries = [];

        foreach ($positions as $position) {
            $salaries = $position->karyawan
                ->map(function($karyawan) {
                    return $karyawan->penggajian->first()?->gaji_pokok ?? 0;
                })
                ->filter(fn($salary) => $salary > 0);

            if ($salaries->count() > 0) {
                $positionNames[] = $position->nama_jabatan;
                $avgSalaries[] = round($salaries->avg() / 1000000, 1); // Convert to millions
            }
        }

        // Sort by salary descending
        $combined = collect(array_combine($positionNames, $avgSalaries))
            ->sortDesc()
            ->take(10); // Top 10 positions

        return [
            'datasets' => [
                [
                    'label' => 'Rata-rata Gaji (Juta)',
                    'data' => $combined->values()->toArray(),
                    'backgroundColor' => array_slice([
                        'rgb(59, 130, 246)',
                        'rgb(16, 185, 129)',
                        'rgb(245, 158, 11)',
                        'rgb(239, 68, 68)',
                        'rgb(139, 92, 246)',
                        'rgb(236, 72, 153)',
                        'rgb(34, 197, 94)',
                        'rgb(251, 146, 60)',
                        'rgb(168, 85, 247)',
                        'rgb(14, 165, 233)',
                    ], 0, $combined->count()),
                ],
            ],
            'labels' => $combined->keys()->toArray(),
        ];
    }

    private function getSalaryTrendData(): array
    {
        $months = [];
        $avgSalaries = [];

        // Get last 6 months
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthYear = $date->format('Y-m');
            $monthLabel = $date->format('M Y');

            $avgSalary = PenggajianKaryawan::where('periode_gaji', $monthYear)
                ->avg('gaji_pokok');

            $months[] = $monthLabel;
            $avgSalaries[] = $avgSalary ? round($avgSalary / 1000000, 1) : 0; // Convert to millions
        }

        return [
            'datasets' => [
                [
                    'label' => 'Rata-rata Gaji (Juta)',
                    'data' => $avgSalaries,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'tension' => 0.4,
                    'fill' => true,
                ],
            ],
            'labels' => $months,
        ];
    }

    protected function getOptions(): array
    {
        $baseOptions = [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'enabled' => true,
                    'callbacks' => [
                        'label' => "function(context) {
                            if (context.chart.config.type === 'doughnut') {
                                return context.label + ': ' + context.parsed + ' karyawan';
                            } else {
                                return context.dataset.label + ': Rp ' + context.parsed + ' juta';
                            }
                        }",
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];

        if ($this->filter !== 'salary_range') {
            $baseOptions['scales'] = [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => "function(value) { return 'Rp ' + value + 'M'; }",
                    ],
                ],
            ];
        }

        return $baseOptions;
    }
}
