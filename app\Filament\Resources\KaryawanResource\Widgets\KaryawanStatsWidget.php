<?php

namespace App\Filament\Resources\KaryawanResource\Widgets;

use App\Models\Karyawan;
use App\Models\KpiPenilaian;
use App\Models\Absensi;
use App\Models\PenggajianKaryawan;
use App\Models\Pelanggaran;
use App\Models\RiwayatKontrak;
use App\Models\KaryawanBpjs;
use App\Models\MutasiPromosiDemosi;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class KaryawanStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            // Total Karyawan Aktif
            Stat::make('Total Karyawan Aktif', $this->getTotalActiveEmployees())
                ->description('Karyawan dengan status aktif')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),

            // Average KPI Achievement
            Stat::make('Rata-rata Achievement KPI', $this->getAverageKpiAchievement())
                ->description($this->getKpiTrend())
                ->descriptionIcon($this->getKpiTrendIcon())
                ->color($this->getKpiTrendColor()),

            // Attendance Rate This Month
            Stat::make('Tingkat Kehadiran', $this->getAttendanceRate())
                ->description('Bulan ' . now()->format('F Y'))
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('success'),

            // Average Salary
            Stat::make('Rata-rata Gaji', $this->getAverageSalary())
                ->description('Gaji pokok semua karyawan')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('warning'),

            // Violations This Month
            Stat::make('Pelanggaran Bulan Ini', $this->getViolationsThisMonth())
                ->description($this->getViolationTrend())
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($this->getViolationColor()),

            // BPJS Coverage
            Stat::make('Coverage BPJS', $this->getBpjsCoverage())
                ->description($this->getBpjsDescription())
                ->descriptionIcon('heroicon-m-shield-check')
                ->color('info'),
        ];
    }

    private function getTotalActiveEmployees(): int
    {
        return Karyawan::where('status_aktif', true)->count();
    }

    private function getAverageKpiAchievement(): string
    {
        $avgKpi = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->avg('realisasi_kpi');

        return $avgKpi ? number_format($avgKpi, 1) . '%' : 'N/A';
    }

    private function getKpiTrend(): string
    {
        $currentMonth = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->avg('realisasi_kpi');

        $lastMonth = KpiPenilaian::whereMonth('tanggal_penilaian', now()->subMonth()->month)
            ->avg('realisasi_kpi');

        if (!$currentMonth || !$lastMonth) {
            return 'Data tidak cukup';
        }

        $diff = $currentMonth - $lastMonth;
        $percentage = abs($diff);

        if ($diff > 0) {
            return "Naik {$percentage}% dari bulan lalu";
        } elseif ($diff < 0) {
            return "Turun {$percentage}% dari bulan lalu";
        } else {
            return "Sama dengan bulan lalu";
        }
    }

    private function getKpiTrendIcon(): string
    {
        $currentMonth = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->avg('realisasi_kpi');

        $lastMonth = KpiPenilaian::whereMonth('tanggal_penilaian', now()->subMonth()->month)
            ->avg('realisasi_kpi');

        if (!$currentMonth || !$lastMonth) {
            return 'heroicon-m-minus';
        }

        return $currentMonth > $lastMonth ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down';
    }

    private function getKpiTrendColor(): string
    {
        $currentMonth = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->avg('realisasi_kpi');

        $lastMonth = KpiPenilaian::whereMonth('tanggal_penilaian', now()->subMonth()->month)
            ->avg('realisasi_kpi');

        if (!$currentMonth || !$lastMonth) {
            return 'gray';
        }

        return $currentMonth >= $lastMonth ? 'success' : 'danger';
    }

    private function getAttendanceRate(): string
    {
        $totalAbsensi = Absensi::whereMonth('tanggal_absensi', now()->month)->count();

        if ($totalAbsensi === 0) {
            return 'N/A';
        }

        $hadirCount = Absensi::whereMonth('tanggal_absensi', now()->month)
            ->whereIn('status', ['hadir', 'terlambat'])
            ->count();

        $rate = ($hadirCount / $totalAbsensi) * 100;
        return number_format($rate, 1) . '%';
    }

    private function getAverageSalary(): string
    {
        $avgSalary = PenggajianKaryawan::whereMonth('periode_gaji', now()->format('Y-m'))
            ->avg('gaji_pokok');

        return $avgSalary ? 'Rp ' . number_format($avgSalary, 0, ',', '.') : 'N/A';
    }

    private function getViolationsThisMonth(): int
    {
        return Pelanggaran::whereMonth('tanggal', now()->month)->count();
    }

    private function getViolationTrend(): string
    {
        $currentMonth = Pelanggaran::whereMonth('tanggal', now()->month)->count();
        $lastMonth = Pelanggaran::whereMonth('tanggal', now()->subMonth()->month)->count();

        $diff = $currentMonth - $lastMonth;

        if ($diff > 0) {
            return "Naik {$diff} dari bulan lalu";
        } elseif ($diff < 0) {
            return "Turun " . abs($diff) . " dari bulan lalu";
        } else {
            return "Sama dengan bulan lalu";
        }
    }

    private function getViolationColor(): string
    {
        $count = $this->getViolationsThisMonth();

        if ($count === 0) {
            return 'success';
        } elseif ($count <= 5) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    private function getBpjsCoverage(): string
    {
        $totalKaryawan = Karyawan::where('status_aktif', true)->count();
        $bpjsCount = KaryawanBpjs::count();

        if ($totalKaryawan === 0) {
            return 'N/A';
        }

        $coverage = ($bpjsCount / $totalKaryawan) * 100;
        return number_format($coverage, 1) . '%';
    }

    private function getBpjsDescription(): string
    {
        $totalKaryawan = Karyawan::where('status_aktif', true)->count();
        $bpjsCount = KaryawanBpjs::count();

        return "{$bpjsCount} dari {$totalKaryawan} karyawan";
    }
}
