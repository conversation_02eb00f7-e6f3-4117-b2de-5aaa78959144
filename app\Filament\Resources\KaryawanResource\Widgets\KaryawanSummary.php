<?php

namespace App\Filament\Resources\KaryawanResource\Widgets;

use App\Models\Karyawan;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class KaryawanSummary extends BaseWidget
{
    protected int | string | array $columnSpan = 'full';

    protected function getStats(): array
    {
        return [
            Stat::make('Total Karyawan', Karyawan::count())
                ->description('Jumlah seluruh data karyawan')
                ->color('primary'),

            Stat::make('Kontrak < 30 Hari', $this->getJumlahKontrakHampirHabis())
                ->description('Sisa kontrak kurang dari 30 hari')
                ->color('warning')
                ->url(route('filament.admin.resources.karyawans.index', [
                    'tableFilters[kontrak_mau_habis][isActive]' => '1',
                ])),

            Stat::make('Ulang Tahun Bulan Ini', $this->getJumlahKaryawanUlangTahun())
                ->description('Karyawan berulang tahun bulan ini')
                ->color('info')
                ->url(route('filament.admin.resources.karyawans.index', [
                    'tableFilters[ulang_tahun_bulan_ini][isActive]' => '1',
                ])),

            Stat::make('Belum Ada Data Gaji', $this->getJumlahKaryawanBelumAdaGaji())
                ->description('Karyawan yang belum memiliki data gaji')
                ->color('warning')
                ->url(route('filament.admin.resources.karyawans.index', [
                    'tableFilters[belum_ada_gaji][isActive]' => '1',
                ])),

            Stat::make('Belum Ada Data Pendidikan', $this->getJumlahKaryawanBelumAdaPendidikan())
                ->description('Karyawan yang belum memiliki data pendidikan')
                ->color('danger')
                ->url(route('filament.admin.resources.karyawans.index', [
                    'tableFilters[belum_ada_pendidikan][isActive]' => '1',
                ])),

            Stat::make('Belum Setup BPJS', $this->getJumlahKaryawanBelumSetupBpjs())
                ->description('Karyawan yang belum di-setup BPJS')
                ->color('warning')
                ->url(route('filament.admin.resources.karyawans.index', [
                    'tableFilters[belum_setup_bpjs][isActive]' => '1',
                ])),
        ];
    }

    /**
     * Hitung jumlah kontrak hampir habis
     */
    protected function getJumlahKontrakHampirHabis(): int
    {
        return Karyawan::whereHas('riwayatKontrak', function ($q) {
            $q->where('is_active', 1)
                ->whereDate('tgl_selesai', '<=', now()->copy()->addDays(30));
        })->count();
    }

    /**
     * Hitung jumlah karyawan berulang tahun bulan ini
     */
    protected function getJumlahKaryawanUlangTahun(): int
    {
        $bulanIni = now()->month;

        return Karyawan::whereMonth('tanggal_lahir', $bulanIni)
            ->where('status_aktif', 1)
            ->count();
    }

    /**
     * Hitung jumlah karyawan yang belum ada data gaji
     */
    protected function getJumlahKaryawanBelumAdaGaji(): int
    {
        return Karyawan::whereDoesntHave('penggajian')
            ->where('status_aktif', 1)
            ->count();
    }

    /**
     * Hitung jumlah karyawan yang belum ada data pendidikan
     */
    protected function getJumlahKaryawanBelumAdaPendidikan(): int
    {
        return Karyawan::whereDoesntHave('pendidikan')
            ->where('status_aktif', 1)
            ->count();
    }

    /**
     * Hitung jumlah karyawan yang belum setup BPJS
     */
    protected function getJumlahKaryawanBelumSetupBpjs(): int
    {
        return Karyawan::whereDoesntHave('bpjs')
            ->where('status_aktif', 1)
            ->count();
    }
}
