<?php

namespace App\Filament\Resources\KaryawanResource\Widgets;

use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class KontrakAktifSummary extends BaseWidget
{
    public ?\App\Models\Karyawan $record = null;

    protected function getStats(): array
    {
        $kontrak = $this->record?->riwayatKontrak()
            ->where('is_active', 1)
            ->latest('tgl_selesai')
            ->first();

        if (! $kontrak) {
            return [
                Stat::make('Sisa Kontrak', 'Tidak Ada')->color('gray'),
            ];
        }

        $tglSelesai = Carbon::parse($kontrak->tgl_selesai);
        $sisaHari = now()->diffInDays($tglSelesai, false);

        return [
            Stat::make('Sisa Kontrak', $sisaHari >= 0 ? "$sisaHari Hari Lagi" : abs($sisaHari) . ' <PERSON>')
                ->description('Berakhir: ' . $tglSelesai->format('d M Y'))
                ->color(
                    $sisaHari < 0 ? 'danger' : ($sisaHari <= 7 ? 'warning' :
                            'success')
                ),
        ];
    }
}
