<?php

namespace App\Filament\Resources;

use App\Filament\Resources\KekuranganGajiResource\Pages;
use App\Models\KekuranganGaji;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class KekuranganGajiResource extends Resource
{
    protected static ?string $model = KekuranganGaji::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationLabel = 'Kekurangan Gaji';

    protected static ?string $modelLabel = 'Kekurangan Gaji';

    protected static ?string $pluralModelLabel = 'Kekurangan Gaji';

    protected static ?string $navigationGroup = 'Payroll Management';

    protected static ?int $navigationSort = 6;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('Informasi Kekurangan Gaji')
                ->schema([
                    Forms\Components\Select::make('karyawan_id')
                        ->label('Karyawan')
                        ->relationship('karyawan', 'nama_lengkap')
                        ->searchable(['nama_lengkap', 'nip'])
                        ->preload()
                        ->required()
                        ->getOptionLabelFromRecordUsing(fn($record) => "{$record->nama_lengkap} ({$record->nip})"),

                    Forms\Components\DatePicker::make('periode_kekurangan')
                        ->label('Periode Kekurangan')
                        ->required()
                        ->displayFormat('F Y')
                        ->format('Y-m-01')
                        ->helperText('Pilih bulan dan tahun periode kekurangan gaji'),

                    Forms\Components\TextInput::make('nominal_kekurangan')
                        ->label('Nominal Kekurangan')
                        ->required()
                        ->numeric()
                        ->prefix('Rp')
                        ->minValue(1)
                        ->formatStateUsing(fn($state) => $state ? number_format($state, 0, ',', '.') : '')
                        ->dehydrateStateUsing(fn($state) => (float) str_replace(['.', ','], ['', '.'], $state)),

                    Forms\Components\Textarea::make('keterangan')
                        ->label('Keterangan')
                        ->required()
                        ->placeholder('Jelaskan alasan kekurangan gaji...')
                        ->rows(3),
                ])
                ->columns(2),

            Forms\Components\Section::make('Status & Persetujuan')
                ->schema([
                    Forms\Components\Select::make('status')
                        ->label('Status')
                        ->options([
                            'pending' => 'Menunggu Persetujuan',
                            'approved' => 'Disetujui',
                            'paid' => 'Sudah Dibayar',
                        ])
                        ->default('pending')
                        ->required()
                        ->reactive(),

                    Forms\Components\DatePicker::make('tanggal_pembayaran')
                        ->label('Tanggal Pembayaran')
                        ->visible(fn(callable $get) => $get('status') === 'paid')
                        ->required(fn(callable $get) => $get('status') === 'paid'),

                    Forms\Components\Hidden::make('created_by')
                        ->default(auth()->id()),
                ])
                ->columns(2),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('karyawan.nama_lengkap')
                    ->label('Nama Karyawan')
                    ->searchable(['karyawan.nama_lengkap', 'karyawan.nip'])
                    ->sortable()
                    ->formatStateUsing(fn($record) => "{$record->karyawan->nama_lengkap} ({$record->karyawan->nip})")
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('karyawan.departemen.nama_departemen')
                    ->label('Departemen')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('formatted_periode')
                    ->label('Periode Kekurangan')
                    ->sortable('periode_kekurangan')
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('formatted_nominal')
                    ->label('Nominal')
                    ->alignCenter()
                    ->weight('bold')
                    ->color('danger'),

                Tables\Columns\TextColumn::make('status_label')
                    ->label('Status')
                    ->badge()
                    ->color(fn($record) => $record->status_color)
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('tanggal_pembayaran')
                    ->label('Tgl Pembayaran')
                    ->date('d M Y')
                    ->placeholder('-')
                    ->alignCenter()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Dibuat Oleh')
                    ->placeholder('-')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'pending' => 'Menunggu Persetujuan',
                        'approved' => 'Disetujui',
                        'paid' => 'Sudah Dibayar',
                    ]),

                Tables\Filters\Filter::make('periode_kekurangan')
                    ->form([
                        Forms\Components\DatePicker::make('dari_periode')
                            ->label('Dari Periode'),
                        Forms\Components\DatePicker::make('sampai_periode')
                            ->label('Sampai Periode'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_periode'],
                                fn(Builder $query, $date): Builder => $query->whereDate('periode_kekurangan', '>=', $date),
                            )
                            ->when(
                                $data['sampai_periode'],
                                fn(Builder $query, $date): Builder => $query->whereDate('periode_kekurangan', '<=', $date),
                            );
                    }),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->icon('heroicon-o-eye'),

                Tables\Actions\EditAction::make()
                    ->icon('heroicon-o-pencil-square'),

                Tables\Actions\Action::make('approve')
                    ->label('Setujui')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn($record) => $record->status === 'pending')
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $record->update([
                            'status' => 'approved',
                            'approved_by' => auth()->id(),
                            'approved_at' => now(),
                        ]);
                    }),

                Tables\Actions\Action::make('mark_paid')
                    ->label('Tandai Dibayar')
                    ->icon('heroicon-o-banknotes')
                    ->color('info')
                    ->visible(fn($record) => $record->status === 'approved')
                    ->form([
                        Forms\Components\DatePicker::make('tanggal_pembayaran')
                            ->label('Tanggal Pembayaran')
                            ->required()
                            ->default(now()),
                    ])
                    ->action(function ($record, array $data) {
                        $record->update([
                            'status' => 'paid',
                            'tanggal_pembayaran' => $data['tanggal_pembayaran'],
                        ]);
                    }),

                Tables\Actions\DeleteAction::make()
                    ->icon('heroicon-o-trash'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Belum ada data kekurangan gaji')
            ->emptyStateDescription('Tambahkan data kekurangan gaji dengan mengklik tombol "Tambah Kekurangan Gaji".')
            ->emptyStateIcon('heroicon-o-banknotes');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKekuranganGajis::route('/'),
            'create' => Pages\CreateKekuranganGaji::route('/create'),
            'edit' => Pages\EditKekuranganGaji::route('/{record}/edit'),
        ];
    }
}
