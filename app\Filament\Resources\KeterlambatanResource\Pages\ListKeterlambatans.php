<?php

namespace App\Filament\Resources\KeterlambatanResource\Pages;

use App\Filament\Resources\KeterlambatanResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class ListKeterlambatans extends ListRecords
{
    protected static string $resource = KeterlambatanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('export')
                ->label('Export Excel')
                ->icon('heroicon-o-document-arrow-down')
                ->color('success')
                ->action(function () {
                    // TODO: Implement export functionality
                    $this->notify('success', 'Export akan segera tersedia');
                }),
        ];
    }

    public function getTabs(): array
    {
        return [
            'semua' => Tab::make('Semua Keterlambatan')
                ->badge(fn() => $this->getTabBadgeCount()),

            'hari_ini' => Tab::make('Hari Ini')
                ->modifyQueryUsing(fn(Builder $query) => $query->whereDate('tanggal_absensi', Carbon::today()))
                ->badge(fn() => $this->getTabBadgeCount(Carbon::today())),

            'minggu_ini' => Tab::make('Minggu Ini')
                ->modifyQueryUsing(fn(Builder $query) => $query->whereBetween('tanggal_absensi', [
                    Carbon::now()->startOfWeek(),
                    Carbon::now()->endOfWeek()
                ]))
                ->badge(fn() => $this->getTabBadgeCount(Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek())),

            'bulan_ini' => Tab::make('Bulan Ini')
                ->modifyQueryUsing(fn(Builder $query) => $query->whereBetween('tanggal_absensi', [
                    Carbon::now()->startOfMonth(),
                    Carbon::now()->endOfMonth()
                ]))
                ->badge(fn() => $this->getTabBadgeCount(Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth())),
        ];
    }

    protected function getTabBadgeCount($startDate = null, $endDate = null): int
    {
        $query = static::getResource()::getEloquentQuery();

        if ($startDate && $endDate) {
            $query->whereBetween('tanggal_absensi', [$startDate, $endDate]);
        } elseif ($startDate) {
            $query->whereDate('tanggal_absensi', $startDate);
        }

        return $query->count();
    }
}
