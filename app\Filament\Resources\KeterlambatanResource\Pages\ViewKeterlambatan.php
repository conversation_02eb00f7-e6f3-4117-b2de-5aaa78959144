<?php

namespace App\Filament\Resources\KeterlambatanResource\Pages;

use App\Filament\Resources\KeterlambatanResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Carbon\Carbon;

class ViewKeterlambatan extends ViewRecord
{
    protected static string $resource = KeterlambatanResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Informasi Karyawan')
                    ->schema([
                        Infolists\Components\TextEntry::make('karyawan.nama_lengkap')
                            ->label('<PERSON>a <PERSON>wan'),
                        Infolists\Components\TextEntry::make('karyawan.nip')
                            ->label('NIP'),
                        Infolists\Components\TextEntry::make('karyawan.entitas.nama')
                            ->label('Entitas'),
                        Infolists\Components\TextEntry::make('karyawan.departemen.nama_departemen')
                            ->label('Departemen'),
                        Infolists\Components\TextEntry::make('karyawan.divisi.nama_divisi')
                            ->label('Divisi'),
                        Infolists\Components\TextEntry::make('karyawan.jabatan.nama_jabatan')
                            ->label('Jabatan'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Detail Absensi')
                    ->schema([
                        Infolists\Components\TextEntry::make('tanggal_absensi')
                            ->label('Tanggal')
                            ->date('d F Y'),
                        Infolists\Components\TextEntry::make('jadwal.shift.nama_shift')
                            ->label('Shift'),
                        Infolists\Components\TextEntry::make('periode')
                            ->label('Periode')
                            ->formatStateUsing(fn($state) => $state ? "Periode {$state}" : 'Shift Normal'),
                        Infolists\Components\TextEntry::make('waktu_masuk')
                            ->label('Waktu Masuk Aktual')
                            ->time('H:i:s'),
                        Infolists\Components\TextEntry::make('jadwal_waktu_masuk')
                            ->label('Jadwal Waktu Masuk')
                            ->getStateUsing(function ($record) {
                                if (!$record->jadwal || !$record->jadwal->shift) {
                                    return '-';
                                }
                                
                                $shift = $record->jadwal->shift;
                                if ($shift->is_split_shift && $record->periode) {
                                    if (method_exists($shift, 'getWorkPeriods')) {
                                        $periods = $shift->getWorkPeriods();
                                        foreach ($periods as $period) {
                                            if ($period['periode'] == $record->periode) {
                                                return Carbon::parse($period['waktu_mulai'])->format('H:i:s');
                                            }
                                        }
                                    }
                                }
                                
                                return $shift->waktu_mulai ? $shift->waktu_mulai->format('H:i:s') : '-';
                            }),
                        Infolists\Components\TextEntry::make('menit_terlambat')
                            ->label('Keterlambatan')
                            ->getStateUsing(function ($record) {
                                if (!$record->waktu_masuk || !$record->jadwal) {
                                    return '0 menit';
                                }

                                $shift = $record->jadwal->shift;
                                if (!$shift) {
                                    return '0 menit';
                                }

                                try {
                                    $waktuMasukAktual = Carbon::parse($record->waktu_masuk);

                                    // Handle split shift
                                    if ($shift->is_split_shift ?? false) {
                                        if (method_exists($shift, 'getCurrentPeriod') && method_exists($shift, 'getWorkPeriods')) {
                                            $currentPeriod = $shift->getCurrentPeriod($waktuMasukAktual->format('H:i:s'));
                                            $periods = $shift->getWorkPeriods();

                                            foreach ($periods as $period) {
                                                if ($period['periode'] == $currentPeriod) {
                                                    $shiftStart = Carbon::parse($period['waktu_mulai']);
                                                    $toleranceMinutes = $period['toleransi_keterlambatan'] ?? 0;
                                                    $allowedEntry = $shiftStart->copy()->addMinutes($toleranceMinutes);

                                                    if ($waktuMasukAktual->greaterThan($allowedEntry)) {
                                                        $minutes = $waktuMasukAktual->diffInMinutes($allowedEntry);
                                                        return $minutes . ' menit';
                                                    }
                                                    return '0 menit (Tepat waktu)';
                                                }
                                            }
                                        }
                                    } else {
                                        // Regular shift
                                        $tanggalWaktuMasuk = $waktuMasukAktual->copy()->startOfDay();
                                        $waktuMasukShift = $tanggalWaktuMasuk->copy()->setTimeFromTimeString($shift->waktu_mulai->format('H:i:s'));
                                        $toleranceMinutes = $shift->toleransi_keterlambatan ?? 0;
                                        $allowedEntry = $waktuMasukShift->copy()->addMinutes($toleranceMinutes);

                                        if ($waktuMasukAktual->greaterThan($allowedEntry)) {
                                            $minutes = $waktuMasukAktual->diffInMinutes($allowedEntry);
                                            return $minutes . ' menit';
                                        }
                                    }

                                    return '0 menit (Tepat waktu)';
                                } catch (\Exception $e) {
                                    return '0 menit';
                                }
                            })
                            ->badge()
                            ->color('danger'),
                        Infolists\Components\TextEntry::make('status')
                            ->label('Status Kehadiran')
                            ->badge()
                            ->color(fn(string $state): string => \App\Enums\AttendanceStatus::from($state)->badgeColor())
                            ->formatStateUsing(fn(string $state): string => \App\Enums\AttendanceStatus::from($state)->label()),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Informasi Lokasi')
                    ->schema([
                        Infolists\Components\TextEntry::make('lokasi_masuk')
                            ->label('Lokasi Absen Masuk'),
                        Infolists\Components\TextEntry::make('latitude_masuk')
                            ->label('Latitude'),
                        Infolists\Components\TextEntry::make('longitude_masuk')
                            ->label('Longitude'),
                    ])
                    ->columns(3)
                    ->visible(fn($record) => $record->lokasi_masuk || $record->latitude_masuk),

                Infolists\Components\Section::make('Keterangan')
                    ->schema([
                        Infolists\Components\TextEntry::make('keterangan')
                            ->label('Keterangan')
                            ->placeholder('Tidak ada keterangan'),
                    ])
                    ->visible(fn($record) => $record->keterangan),

                Infolists\Components\Section::make('Foto Absensi')
                    ->schema([
                        Infolists\Components\ImageEntry::make('foto_masuk')
                            ->label('Foto Masuk')
                            ->disk('public')
                            ->height(200),
                    ])
                    ->visible(fn($record) => $record->foto_masuk),
            ]);
    }
}
