<?php

namespace App\Filament\Resources\KeyResultResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Spatie\Activitylog\Models\Activity;

class ActivityLogsRelationManager extends RelationManager
{
    protected static string $relationship = 'activities';

    protected static ?string $title = 'Log Aktivitas';

    protected static ?string $modelLabel = 'Log Aktivitas';

    protected static ?string $pluralModelLabel = 'Log Aktivitas';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Activity logs are read-only, no form needed
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('description')
            ->columns([
                Tables\Columns\TextColumn::make('event')
                    ->label('Event')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'created' => 'success',
                        'updated' => 'warning',
                        'deleted' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'created' => 'Dibuat',
                        'updated' => 'Diupdate',
                        'deleted' => 'Dihapus',
                        default => ucfirst($state),
                    }),

                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->searchable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('causer.name')
                    ->label('Oleh')
                    ->searchable()
                    ->default('System'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Waktu')
                    ->dateTime('d M Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('properties')
                    ->label('Perubahan')
                    ->formatStateUsing(function ($state) {
                        if (!$state || !is_array($state)) {
                            return '-';
                        }

                        $changes = [];
                        if (isset($state['attributes']) && isset($state['old'])) {
                            foreach ($state['attributes'] as $key => $newValue) {
                                $oldValue = $state['old'][$key] ?? null;
                                if ($oldValue != $newValue) {
                                    $changes[] = "{$key}: {$oldValue} → {$newValue}";
                                }
                            }
                        }

                        return implode(', ', $changes) ?: '-';
                    })
                    ->limit(100)
                    ->tooltip(function ($state) {
                        if (!$state || !is_array($state)) {
                            return null;
                        }

                        $details = [];
                        if (isset($state['attributes']) && isset($state['old'])) {
                            foreach ($state['attributes'] as $key => $newValue) {
                                $oldValue = $state['old'][$key] ?? null;
                                if ($oldValue != $newValue) {
                                    $details[] = "• {$key}: {$oldValue} → {$newValue}";
                                }
                            }
                        }

                        return implode("\n", $details);
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('event')
                    ->label('Event')
                    ->options([
                        'created' => 'Dibuat',
                        'updated' => 'Diupdate',
                        'deleted' => 'Dihapus',
                    ]),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->headerActions([
                // No create action - logs are auto-generated
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Log Aktivitas')
                    ->modalContent(function (Activity $record) {
                        $properties = $record->properties?->toArray() ?? [];
                        
                        return view('filament.components.activity-log-detail', [
                            'record' => $record,
                            'properties' => $properties,
                        ]);
                    }),
            ])
            ->bulkActions([
                // No bulk actions for logs
            ])
            ->defaultSort('created_at', 'desc')
            ->poll('30s'); // Auto-refresh every 30 seconds
    }
}
