<?php

namespace App\Filament\Resources\LemburResource\Pages;

use App\Filament\Resources\LemburResource;
use Filament\Resources\Pages\CreateRecord;

class CreateLembur extends CreateRecord
{
    protected static string $resource = LemburResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = auth()->id();
        
        return $data;
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Data lembur berhasil ditambahkan';
    }
}
