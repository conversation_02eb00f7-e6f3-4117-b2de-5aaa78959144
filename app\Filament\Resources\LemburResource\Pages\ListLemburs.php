<?php

namespace App\Filament\Resources\LemburResource\Pages;

use App\Filament\Resources\LemburResource;
use App\Filament\Resources\LemburResource\Widgets\LemburStatsWidget;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListLemburs extends ListRecords
{
    protected static string $resource = LemburResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Tambah Lembur')
                ->icon('heroicon-o-plus'),

            Actions\Action::make('statistik')
                ->label('Lihat Statistik')
                ->icon('heroicon-o-chart-bar')
                ->color('info')
                ->modalHeading('Statistik Lembur')
                ->modalContent(function () {
                    $stats = LemburResource::getStatistik();
                    return view('filament.resources.lembur.statistik-modal', $stats);
                })
                ->modalWidth('5xl'),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            LemburStatsWidget::class,
        ];
    }
}
