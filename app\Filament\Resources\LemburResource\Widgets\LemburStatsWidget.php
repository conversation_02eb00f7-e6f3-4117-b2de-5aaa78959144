<?php

namespace App\Filament\Resources\LemburResource\Widgets;

use App\Models\Lembur;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class LemburStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $currentMonth = now()->month;
        $currentYear = now()->year;
        $lastMonth = now()->subMonth()->month;
        $lastMonthYear = now()->subMonth()->year;

        // Get accessible karyawan IDs based on permission
        $accessibleIds = \App\Services\PermissionService::getAccessibleKaryawanIds('manage_overtime');

        // Data bulan ini
        $totalJamBulanIni = Lembur::whereMonth('tanggal', $currentMonth)
            ->whereYear('tanggal', $currentYear)
            ->whereIn('karyawan_id', $accessibleIds)
            ->sum('jumlah_jam');

        $totalRecordBulanIni = Lembur::whereMonth('tanggal', $currentMonth)
            ->whereYear('tanggal', $currentYear)
            ->whereIn('karyawan_id', $accessibleIds)
            ->count();

        // Data bulan lalu untuk perbandingan
        $totalJamBulanLalu = Lembur::whereMonth('tanggal', $lastMonth)
            ->whereYear('tanggal', $lastMonthYear)
            ->whereIn('karyawan_id', $accessibleIds)
            ->sum('jumlah_jam');

        $totalRecordBulanLalu = Lembur::whereMonth('tanggal', $lastMonth)
            ->whereYear('tanggal', $lastMonthYear)
            ->whereIn('karyawan_id', $accessibleIds)
            ->count();

        // Hitung persentase perubahan
        $perubahanJam = $totalJamBulanLalu > 0 
            ? (($totalJamBulanIni - $totalJamBulanLalu) / $totalJamBulanLalu) * 100 
            : 0;

        $perubahanRecord = $totalRecordBulanLalu > 0 
            ? (($totalRecordBulanIni - $totalRecordBulanLalu) / $totalRecordBulanLalu) * 100 
            : 0;

        // Rata-rata jam per hari kerja (asumsi 22 hari kerja per bulan)
        $rataRataJamPerHari = $totalJamBulanIni / max(now()->day, 1);

        // Karyawan dengan lembur terbanyak bulan ini
        $karyawanTerbanyak = Lembur::with('karyawan')
            ->whereMonth('tanggal', $currentMonth)
            ->whereYear('tanggal', $currentYear)
            ->whereIn('karyawan_id', $accessibleIds)
            ->selectRaw('karyawan_id, SUM(jumlah_jam) as total_jam')
            ->groupBy('karyawan_id')
            ->orderByDesc('total_jam')
            ->first();

        return [
            Stat::make('Total Jam Lembur Bulan Ini', number_format($totalJamBulanIni, 1) . ' jam')
                ->description($perubahanJam >= 0 ? 'Naik ' . number_format(abs($perubahanJam), 1) . '% dari bulan lalu' : 'Turun ' . number_format(abs($perubahanJam), 1) . '% dari bulan lalu')
                ->descriptionIcon($perubahanJam >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($perubahanJam >= 0 ? 'success' : 'danger')
                ->chart([
                    $totalJamBulanLalu ?: 0,
                    $totalJamBulanIni ?: 0,
                ]),

            Stat::make('Total Record Lembur', $totalRecordBulanIni . ' record')
                ->description($perubahanRecord >= 0 ? 'Naik ' . number_format(abs($perubahanRecord), 1) . '% dari bulan lalu' : 'Turun ' . number_format(abs($perubahanRecord), 1) . '% dari bulan lalu')
                ->descriptionIcon($perubahanRecord >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($perubahanRecord >= 0 ? 'info' : 'warning'),

            Stat::make('Rata-rata Jam per Hari', number_format($rataRataJamPerHari, 1) . ' jam')
                ->description('Berdasarkan hari berjalan bulan ini')
                ->descriptionIcon('heroicon-m-clock')
                ->color('primary'),

            Stat::make('Karyawan Lembur Terbanyak', $karyawanTerbanyak ? $karyawanTerbanyak->karyawan->nama_lengkap : 'Belum ada data')
                ->description($karyawanTerbanyak ? number_format($karyawanTerbanyak->total_jam, 1) . ' jam bulan ini' : 'Tidak ada lembur bulan ini')
                ->descriptionIcon('heroicon-m-user')
                ->color('warning'),
        ];
    }

    protected function getColumns(): int
    {
        return 4;
    }
}
