<?php

namespace App\Filament\Resources\ObjectiveResource\Pages;

use App\Filament\Resources\ObjectiveResource;
use Filament\Resources\Pages\CreateRecord;

class CreateObjective extends CreateRecord
{
    protected static string $resource = ObjectiveResource::class;

    protected function handleRecordCreation(array $data): \Illuminate\Database\Eloquent\Model
    {
        // Handle new tasks separately
        $newTasks = $data['new_tasks'] ?? [];
        unset($data['new_tasks']);

        // Create the main record
        $record = static::getModel()::create($data);

        // Create new tasks and connect them to objective
        if (!empty($newTasks)) {
            $syncData = [];
            foreach ($newTasks as $taskData) {
                $contributionPercentage = $taskData['contribution_percentage'] ?? 100;
                unset($taskData['contribution_percentage']);

                // Set created_by for the task
                $taskData['created_by'] = auth()->id();

                // Create the task
                $task = \App\Models\Task::create($taskData);

                // Add to sync data
                $syncData[$task->id] = [
                    'contribution_percentage' => $contributionPercentage
                ];
            }

            // Sync tasks with objective
            $record->tasks()->sync($syncData);
        }

        return $record;
    }
}
