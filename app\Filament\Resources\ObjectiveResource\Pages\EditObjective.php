<?php

namespace App\Filament\Resources\ObjectiveResource\Pages;

use App\Filament\Resources\ObjectiveResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditObjective extends EditRecord
{
    protected static string $resource = ObjectiveResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load existing tasks as new_tasks format for editing
        $existingTasks = $this->record->tasks()
            ->withPivot('contribution_percentage')
            ->get()
            ->map(function ($task) {
                return [
                    'id' => $task->id, // Keep task ID for updates
                    'name' => $task->name,
                    'description' => $task->description,
                    'project_id' => $task->project_id,
                    'assigned_to' => $task->assigned_to,
                    'start_date' => $task->start_date?->format('Y-m-d'),
                    'due_date' => $task->due_date?->format('Y-m-d'),
                    'status' => $task->status,
                    'contribution_percentage' => $task->pivot->contribution_percentage,
                ];
            })
            ->toArray();

        $data['new_tasks'] = $existingTasks;

        // Also load existing task connections for the separate section
        $existingConnections = $this->record->tasks()
            ->withPivot('contribution_percentage')
            ->get()
            ->map(function ($task) {
                return [
                    'task_id' => $task->id,
                    'contribution_percentage' => $task->pivot->contribution_percentage,
                ];
            })
            ->toArray();

        $data['existing_task_connections'] = $existingConnections;

        return $data;
    }

    protected function handleRecordUpdate(\Illuminate\Database\Eloquent\Model $record, array $data): \Illuminate\Database\Eloquent\Model
    {
        // Handle new tasks and existing task connections separately
        $newTasks = $data['new_tasks'] ?? [];
        $existingTaskConnections = $data['existing_task_connections'] ?? [];
        unset($data['new_tasks'], $data['existing_task_connections']);

        // Update the main record
        $record->update($data);

        // Handle tasks - update existing or create new
        $syncData = [];

        // Process new tasks (created from form)
        foreach ($newTasks as $taskData) {
            $contributionPercentage = $taskData['contribution_percentage'] ?? 100;
            unset($taskData['contribution_percentage']);

            if (isset($taskData['id']) && $taskData['id']) {
                // Update existing task
                $task = \App\Models\Task::find($taskData['id']);
                if ($task) {
                    unset($taskData['id']); // Remove ID from update data
                    $task->update($taskData);
                    $syncData[$task->id] = [
                        'contribution_percentage' => $contributionPercentage
                    ];
                }
            } else {
                // Create new task
                unset($taskData['id']); // Remove null ID
                $taskData['created_by'] = auth()->id();
                $task = \App\Models\Task::create($taskData);
                $syncData[$task->id] = [
                    'contribution_percentage' => $contributionPercentage
                ];
            }
        }

        // Process existing task connections (just linking existing tasks)
        foreach ($existingTaskConnections as $connection) {
            if (isset($connection['task_id']) && isset($connection['contribution_percentage'])) {
                $syncData[$connection['task_id']] = [
                    'contribution_percentage' => $connection['contribution_percentage']
                ];
            }
        }

        // Sync all tasks with objective
        $record->tasks()->sync($syncData);

        return $record;
    }
}
