<?php

namespace App\Filament\Resources\ObjectiveResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

use Filament\Support\Enums\FontWeight;
use Illuminate\Support\Facades\Auth;
use Filament\Notifications\Notification;

class KeyResultsRelationManager extends RelationManager
{
    protected static string $relationship = 'keyResults';

    protected static ?string $title = 'Key Results';

    protected static ?string $modelLabel = 'Key Result';

    protected static ?string $pluralModelLabel = 'Key Results';

    // Allow create actions in view mode
    public function canCreate(): bool
    {
        return true;
    }

    // Override to allow actions in view mode
    public function isReadOnly(): bool
    {
        return false; // Allow create/edit/delete actions even in view mode
    }

    // Override to show header actions in view mode
    protected function getTableHeaderActions(): array
    {
        return [
            Tables\Actions\CreateAction::make()
                ->label('Tambah Key Result')
                ->icon('heroicon-o-plus')
                ->color('primary')
                ->mutateFormDataUsing(function (array $data): array {
                    $data['created_by'] = Auth::id();
                    return $data;
                })
                ->after(function ($record) {
                    // Update progress after creating key result
                    $record->updateProgress();
                }),
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Key Result')
                    ->schema([
                        Forms\Components\TextInput::make('nama_key_result')
                            ->label('Nama Key Result')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('deskripsi')
                            ->label('Deskripsi')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('tipe_metrik')
                                    ->label('Tipe Metrik')
                                    ->options([
                                        'number' => 'Angka',
                                        'percentage' => 'Persentase',
                                        'currency' => 'Mata Uang',
                                        'boolean' => 'Ya/Tidak',
                                    ])
                                    ->required()
                                    ->default('number')
                                    ->native(false)
                                    ->reactive(),

                                Forms\Components\TextInput::make('unit_measurement')
                                    ->label('Satuan Pengukuran')
                                    ->placeholder('contoh: unit, orang, hari')
                                    ->hidden(fn(callable $get) => in_array($get('tipe_metrik'), ['percentage', 'boolean'])),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('target_value')
                                    ->label('Target Value')
                                    ->required()
                                    ->numeric()
                                    ->minValue(0)
                                    ->step(0.01)
                                    ->suffix(fn(callable $get) => match ($get('tipe_metrik')) {
                                        'percentage' => '%',
                                        'currency' => 'Rp',
                                        default => $get('unit_measurement') ?? '',
                                    }),

                                Forms\Components\TextInput::make('current_value')
                                    ->label('Current Value')
                                    ->numeric()
                                    ->minValue(0)
                                    ->step(0.01)
                                    ->default(0)
                                    ->suffix(fn(callable $get) => match ($get('tipe_metrik')) {
                                        'percentage' => '%',
                                        'currency' => 'Rp',
                                        default => $get('unit_measurement') ?? '',
                                    })
                                    ->reactive()
                                    ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                        $target = $get('target_value');
                                        if ($target > 0) {
                                            $progress = min(100, round(($state / $target) * 100));
                                            $set('progress_percentage', $progress);
                                        }
                                    }),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('due_date')
                                    ->label('Tanggal Target')
                                    ->after('today'),

                                Forms\Components\Select::make('status')
                                    ->label('Status')
                                    ->options([
                                        'not_started' => 'Belum Dimulai',
                                        'in_progress' => 'Sedang Berjalan',
                                        'completed' => 'Selesai',
                                        'at_risk' => 'Berisiko',
                                    ])
                                    ->required()
                                    ->default('not_started')
                                    ->native(false),
                            ]),

                        Forms\Components\TextInput::make('progress_percentage')
                            ->label('Progress (%)')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->default(0)
                            ->suffix('%')
                            ->disabled(),

                        Forms\Components\TextInput::make('weight')
                            ->label('Weight (Bobot)')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(100)
                            ->default(1)
                            ->helperText('Bobot untuk menghitung progress objective (1-100)')
                            ->rules([
                                new \App\Rules\ValidKeyResultWeight($this->getOwnerRecord()->id ?? null)
                            ]),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Milestones & Tracking')
                    ->description('Milestone dan tracking progress')
                    ->schema([
                        Forms\Components\Repeater::make('milestones')
                            ->label('Milestones')
                            ->schema([
                                Forms\Components\TextInput::make('title')
                                    ->label('Milestone')
                                    ->required(),
                                Forms\Components\TextInput::make('target_value')
                                    ->label('Target Value')
                                    ->numeric(),
                                Forms\Components\DatePicker::make('due_date')
                                    ->label('Target Date'),
                                Forms\Components\Toggle::make('completed')
                                    ->label('Completed')
                                    ->default(false),
                            ])
                            ->columns(4)
                            ->collapsible()
                            ->defaultItems(0)
                            ->addActionLabel('Tambah Milestone'),
                    ])
                    ->columns(1),

                Forms\Components\Hidden::make('created_by')
                    ->default(Auth::id()),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('nama_key_result')
            ->columns([
                Tables\Columns\TextColumn::make('nama_key_result')
                    ->label('Nama Key Result')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Medium)
                    ->wrap(),

                Tables\Columns\TextColumn::make('tipe_metrik')
                    ->label('Tipe')
                    ->badge()
                    ->color('info')
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'number' => 'Angka',
                        'percentage' => 'Persentase',
                        'currency' => 'Mata Uang',
                        'boolean' => 'Ya/Tidak',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('current_value')
                    ->label('Current')
                    ->formatStateUsing(function ($record) {
                        return match ($record->tipe_metrik) {
                            'percentage' => number_format($record->current_value, 1) . '%',
                            'currency' => 'Rp ' . number_format($record->current_value, 0, ',', '.'),
                            'boolean' => $record->current_value >= 1 ? 'Ya' : 'Tidak',
                            default => number_format($record->current_value, 2) . ($record->unit_measurement ? ' ' . $record->unit_measurement : ''),
                        };
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('target_value')
                    ->label('Target')
                    ->formatStateUsing(function ($record) {
                        return match ($record->tipe_metrik) {
                            'percentage' => number_format($record->target_value, 1) . '%',
                            'currency' => 'Rp ' . number_format($record->target_value, 0, ',', '.'),
                            'boolean' => $record->target_value >= 1 ? 'Ya' : 'Tidak',
                            default => number_format($record->target_value, 2) . ($record->unit_measurement ? ' ' . $record->unit_measurement : ''),
                        };
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('progress_percentage')
                    ->label('Progress')
                    ->formatStateUsing(fn($state) => $state . '%')
                    ->color(fn($state) => match (true) {
                        $state >= 100 => 'success',
                        $state >= 80 => 'info',
                        $state >= 60 => 'warning',
                        $state >= 40 => 'primary',
                        default => 'danger',
                    })
                    ->badge()
                    ->sortable(),

                Tables\Columns\TextColumn::make('weight')
                    ->label('Weight')
                    ->badge()
                    ->color('info')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('milestones')
                    ->label('Milestones')
                    ->getStateUsing(fn($record) => is_array($record->milestones) ? count($record->milestones) : 0)
                    ->formatStateUsing(fn($state) => $state . ' milestone' . ($state != 1 ? 's' : ''))
                    ->badge()
                    ->color('secondary')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'not_started' => 'gray',
                        'in_progress' => 'warning',
                        'completed' => 'success',
                        'at_risk' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'not_started' => 'Belum Dimulai',
                        'in_progress' => 'Sedang Berjalan',
                        'completed' => 'Selesai',
                        'at_risk' => 'Berisiko',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('due_date')
                    ->label('Target Date')
                    ->date('d M Y')
                    ->sortable()
                    ->color(fn($record) => $record->is_overdue ? 'danger' : 'primary')
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'not_started' => 'Belum Dimulai',
                        'in_progress' => 'Sedang Berjalan',
                        'completed' => 'Selesai',
                        'at_risk' => 'Berisiko',
                    ]),

                Tables\Filters\SelectFilter::make('tipe_metrik')
                    ->label('Tipe Metrik')
                    ->options([
                        'number' => 'Angka',
                        'percentage' => 'Persentase',
                        'currency' => 'Mata Uang',
                        'boolean' => 'Ya/Tidak',
                    ]),
            ])
            ->headerActions($this->getTableHeaderActions())
            ->actions([
                Tables\Actions\Action::make('update_progress')
                    ->label('Update Progress')
                    ->icon('heroicon-o-arrow-path')
                    ->color('info')
                    ->modalHeading('Update Progress Key Result')
                    ->modalDescription(fn($record) => 'Update nilai saat ini untuk: ' . $record->nama_key_result)
                    ->form([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('current_value')
                                    ->label('Nilai Saat Ini')
                                    ->numeric()
                                    ->step(0.01)
                                    ->required()
                                    ->suffix(fn($record) => match ($record->tipe_metrik) {
                                        'percentage' => '%',
                                        'currency' => 'Rp',
                                        default => $record->unit_measurement ?? '',
                                    })
                                    ->helperText(fn($record) => 'Target: ' . number_format($record->target_value, 2) .
                                        match ($record->tipe_metrik) {
                                            'percentage' => '%',
                                            'currency' => ' Rupiah',
                                            default => ($record->unit_measurement ? ' ' . $record->unit_measurement : ''),
                                        })
                                    ->live()
                                    ->afterStateUpdated(function (callable $set, $state, $record) {
                                        if ($record && $record->target_value > 0) {
                                            $progress = min(100, round(($state / $record->target_value) * 100));
                                            $set('progress_percentage', $progress);
                                        }
                                    }),

                                Forms\Components\TextInput::make('progress_percentage')
                                    ->label('Progress (%)')
                                    ->numeric()
                                    ->disabled()
                                    ->suffix('%')
                                    ->helperText('Progress akan dihitung otomatis berdasarkan nilai saat ini'),
                            ]),

                        Forms\Components\Textarea::make('update_note')
                            ->label('Catatan Update (Opsional)')
                            ->rows(3)
                            ->placeholder('Tambahkan catatan tentang update progress ini...'),
                    ])
                    ->fillForm(fn($record) => [
                        'current_value' => $record->current_value,
                        'progress_percentage' => $record->progress_percentage,
                    ])
                    ->action(function ($record, array $data) {
                        $oldValue = $record->current_value;
                        $oldProgress = $record->progress_percentage;

                        // Update current value
                        $record->update([
                            'current_value' => $data['current_value'],
                            'last_updated_at' => now(),
                        ]);

                        // Recalculate progress
                        $newProgress = $record->updateProgress();

                        // Log the manual progress update with additional context
                        activity('manual_update')
                            ->performedOn($record)
                            ->causedBy(auth()->user())
                            ->event('manual_progress_update')
                            ->withProperties([
                                'old_values' => [
                                    'current_value' => $oldValue,
                                    'progress_percentage' => $oldProgress,
                                ],
                                'new_values' => [
                                    'current_value' => $data['current_value'],
                                    'progress_percentage' => $newProgress,
                                ],
                                'update_note' => $data['update_note'] ?? null,
                                'update_type' => 'manual_progress_update',
                            ])
                            ->log('Progress key result diupdate secara manual');

                        Notification::make()
                            ->title('Progress berhasil diupdate!')
                            ->body("Progress berubah dari {$oldProgress}% menjadi {$newProgress}%. Nilai saat ini: " . number_format($data['current_value'], 2))
                            ->success()
                            ->send();
                    }),

                Tables\Actions\EditAction::make()
                    ->label('Edit')
                    ->after(function ($record) {
                        // Update progress after editing
                        $record->updateProgress();
                    }),

                Tables\Actions\Action::make('view_logs')
                    ->label('Lihat Log')
                    ->icon('heroicon-o-clipboard-document-list')
                    ->color('info')
                    ->modalHeading(fn($record) => 'Log Aktivitas: ' . $record->nama_key_result)
                    ->modalContent(function ($record) {
                        $activities = $record->activities()
                            ->with('causer')
                            ->orderBy('created_at', 'desc')
                            ->get();

                        return view('filament.components.key-result-activity-logs', [
                            'activities' => $activities,
                            'keyResult' => $record,
                        ]);
                    })
                    ->modalWidth('5xl'),

                Tables\Actions\DeleteAction::make()
                    ->label('Hapus')
                    ->after(function () {
                        // Update objective progress after deleting key result
                        $this->getOwnerRecord()->calculateProgress();
                        $this->getOwnerRecord()->updateStatus();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Hapus Terpilih')
                        ->after(function () {
                            // Update objective progress after bulk delete
                            $this->getOwnerRecord()->calculateProgress();
                            $this->getOwnerRecord()->updateStatus();
                        }),
                ]),
            ])
            ->defaultSort('key_results.created_at', 'desc')
            ->striped();
    }
}
