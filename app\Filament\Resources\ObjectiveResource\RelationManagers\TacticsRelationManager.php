<?php

namespace App\Filament\Resources\ObjectiveResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Support\Enums\FontWeight;
use Illuminate\Support\Facades\Auth;


class TacticsRelationManager extends RelationManager
{
    protected static string $relationship = 'tactics';

    protected static ?string $title = 'Tactics';
    protected static ?string $modelLabel = 'Tactic';
    protected static ?string $pluralModelLabel = 'Tactics';

    // Allow create actions in view mode
    public function canCreate(): bool
    {
        return true;
    }

    // Override to allow actions in view mode
    public function isReadOnly(): bool
    {
        return false; // Allow create/edit/delete actions even in view mode
    }

    // Override to show header actions in view mode
    protected function getTableHeaderActions(): array
    {
        return [
            Tables\Actions\CreateAction::make()
                ->label('Tambah Tactic Baru')
                ->icon('heroicon-o-plus')
                ->color('primary')
                ->mutateFormDataUsing(function (array $data): array {
                    $data['created_by'] = Auth::id();
                    return $data;
                }),
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Tactic')
                    ->schema([
                        Forms\Components\TextInput::make('nama_tactic')
                            ->label('Judul Tactic')
                            ->placeholder('Contoh: Implementasi sistem feedback pelanggan')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('deskripsi')
                            ->label('Deskripsi')
                            ->placeholder('Jelaskan secara lengkap yang akan dilakukan...')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Select::make('jenis_tactic')
                                    ->label('Jenis Tactic')
                                    ->options([
                                        'Tindakan langsung' => 'Tindakan langsung',
                                        'Penelitian' => 'Penelitian',
                                        'Pengembangan' => 'Pengembangan',
                                        'Pemasaran' => 'Pemasaran',
                                        'Operasional' => 'Operasional',
                                        'Keuangan' => 'Keuangan',
                                    ])
                                    ->searchable()
                                    ->placeholder('Pilih jenis tactic'),

                                Forms\Components\Select::make('priority')
                                    ->label('Prioritas')
                                    ->options([
                                        'low' => 'Rendah',
                                        'medium' => 'Sedang',
                                        'high' => 'Tinggi',
                                        'critical' => 'Kritis',
                                    ])
                                    ->required()
                                    ->default('medium')
                                    ->native(false),

                                Forms\Components\Select::make('status')
                                    ->label('Status')
                                    ->options([
                                        'planned' => 'Direncanakan',
                                        'in_progress' => 'Sedang Berjalan',
                                        'completed' => 'Selesai',
                                        'blocked' => 'Terblokir',
                                    ])
                                    ->required()
                                    ->default('planned')
                                    ->native(false),
                            ]),

                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Select::make('pemilik')
                                    ->label('Pemilik')
                                    ->options(\App\Models\User::pluck('name', 'id'))
                                    ->searchable()
                                    ->placeholder('Pilih pemilik tactic'),

                                Forms\Components\DatePicker::make('tanggal_mulai')
                                    ->label('Tanggal Mulai'),

                                Forms\Components\DatePicker::make('tanggal_target')
                                    ->label('Tanggal Target'),
                            ]),

                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('estimasi_effort')
                                    ->label('Estimasi Effort (Jam)')
                                    ->numeric()
                                    ->placeholder('Perkiraan waktu yang dibutuhkan')
                                    ->suffix('jam'),

                                Forms\Components\Select::make('skor_dampak')
                                    ->label('Skor Dampak')
                                    ->options([
                                        1 => '1 - Sangat Rendah',
                                        2 => '2 - Rendah',
                                        3 => '3 - Cukup Rendah',
                                        4 => '4 - Sedang',
                                        5 => '5 - Cukup Tinggi',
                                        6 => '6 - Tinggi',
                                        7 => '7 - Sangat Tinggi',
                                        8 => '8 - Ekstrem Tinggi',
                                        9 => '9 - Luar Biasa',
                                        10 => '10 - Revolusioner',
                                    ])
                                    ->placeholder('Pilih skor dampak (1-10)'),

                                Forms\Components\TextInput::make('progress_percentage')
                                    ->label('Progress (%)')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(100)
                                    ->default(0)
                                    ->suffix('%'),
                            ]),

                        Forms\Components\Textarea::make('sumber_daya')
                            ->label('Sumber Daya yang Dibutuhkan')
                            ->placeholder('Tambahkan sumber daya yang dibutuhkan')
                            ->rows(2),

                        Forms\Components\Textarea::make('kriteria_keberhasilan')
                            ->label('Kriteria Keberhasilan')
                            ->placeholder('Tambahkan kriteria keberhasilan')
                            ->rows(2),

                        Forms\Components\Textarea::make('dependensi')
                            ->label('Dependensi')
                            ->placeholder('Tambahkan dependensi')
                            ->rows(2),
                    ])
                    ->columns(1),

                Forms\Components\Hidden::make('created_by')
                    ->default(Auth::id()),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('nama_tactic')
            ->columns([
                Tables\Columns\TextColumn::make('nama_tactic')
                    ->label('Nama Tactic')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Medium)
                    ->wrap(),

                Tables\Columns\TextColumn::make('jenis_tactic')
                    ->label('Jenis Tactic')
                    ->badge()
                    ->color('secondary')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('priority')
                    ->label('Prioritas')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'critical' => 'danger',
                        'high' => 'warning',
                        'medium' => 'info',
                        'low' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'low' => 'Rendah',
                        'medium' => 'Sedang',
                        'high' => 'Tinggi',
                        'critical' => 'Kritis',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'planned' => 'gray',
                        'in_progress' => 'warning',
                        'completed' => 'success',
                        'blocked' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'planned' => 'Direncanakan',
                        'in_progress' => 'Sedang Berjalan',
                        'completed' => 'Selesai',
                        'blocked' => 'Terblokir',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('progress_percentage')
                    ->label('Progress')
                    ->formatStateUsing(fn($state) => $state . '%')
                    ->color(fn($state) => match (true) {
                        $state >= 100 => 'success',
                        $state >= 80 => 'info',
                        $state >= 50 => 'warning',
                        default => 'gray',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('skor_dampak')
                    ->label('Skor Dampak')
                    ->badge()
                    ->color(fn($state) => match (true) {
                        $state >= 8 => 'success',
                        $state >= 6 => 'warning',
                        $state >= 4 => 'info',
                        default => 'gray',
                    })
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('priority')
                    ->options([
                        'low' => 'Rendah',
                        'medium' => 'Sedang',
                        'high' => 'Tinggi',
                        'critical' => 'Kritis',
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'planned' => 'Direncanakan',
                        'in_progress' => 'Sedang Berjalan',
                        'completed' => 'Selesai',
                        'blocked' => 'Terblokir',
                    ]),
            ])
            ->headerActions($this->getTableHeaderActions())
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Edit'),

                Tables\Actions\DeleteAction::make()
                    ->label('Hapus'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Hapus Terpilih'),
                ]),
            ])
            ->defaultSort('tactics.created_at', 'desc')
            ->striped();
    }
}
