<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OkrPeriodResource\Pages;
use App\Models\OkrPeriod;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Filters\SelectFilter;

class OkrPeriodResource extends Resource
{
    protected static ?string $model = OkrPeriod::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar';

    protected static ?string $navigationGroup = 'OKR Management';

    protected static ?string $navigationLabel = 'OKR Periods';

    protected static ?string $modelLabel = 'OKR Period';

    protected static ?string $pluralModelLabel = 'OKR Periods';

    protected static ?int $navigationSort = 3;

    // Access control
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return in_array($user->role, ['admin', 'supervisor']);
    }

    private static function getQuarterData(int $year, int $quarter): array
    {
        return match ($quarter) {
            1 => [
                'start' => \Carbon\Carbon::create($year, 1, 1),
                'end' => \Carbon\Carbon::create($year, 3, 31),
            ],
            2 => [
                'start' => \Carbon\Carbon::create($year, 4, 1),
                'end' => \Carbon\Carbon::create($year, 6, 30),
            ],
            3 => [
                'start' => \Carbon\Carbon::create($year, 7, 1),
                'end' => \Carbon\Carbon::create($year, 9, 30),
            ],
            4 => [
                'start' => \Carbon\Carbon::create($year, 10, 1),
                'end' => \Carbon\Carbon::create($year, 12, 31),
            ],
            default => throw new \InvalidArgumentException('Invalid quarter: ' . $quarter),
        };
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Periode OKR')
                    ->schema([
                        Forms\Components\TextInput::make('nama_periode')
                            ->label('Nama Periode')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('deskripsi')
                            ->label('Deskripsi')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Select::make('tipe_periode')
                            ->label('Tipe Periode')
                            ->options([
                                'quarterly' => 'Kuartalan',
                                'yearly' => 'Tahunan',
                                'monthly' => 'Bulanan',
                                'custom' => 'Kustom',
                            ])
                            ->required()
                            ->default('quarterly')
                            ->native(false)
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                if ($state === 'quarterly') {
                                    $year = $get('tahun') ?? now()->year;
                                    $quarter = $get('quarter') ?? 1;
                                    $quarterData = self::getQuarterData($year, $quarter);
                                    $set('tanggal_mulai', $quarterData['start']->format('Y-m-d'));
                                    $set('tanggal_selesai', $quarterData['end']->format('Y-m-d'));
                                    $set('nama_periode', "Q{$quarter} {$year}");
                                } elseif ($state === 'yearly') {
                                    $year = $get('tahun') ?? now()->year;
                                    $set('tanggal_mulai', "{$year}-01-01");
                                    $set('tanggal_selesai', "{$year}-12-31");
                                    $set('nama_periode', "Tahun {$year}");
                                }
                            }),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('tahun')
                                    ->label('Tahun')
                                    ->required()
                                    ->numeric()
                                    ->minValue(2020)
                                    ->maxValue(2030)
                                    ->default(now()->year)
                                    ->reactive()
                                    ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                        $tipe = $get('tipe_periode');
                                        if ($tipe === 'quarterly') {
                                            $quarter = $get('quarter') ?? 1;
                                            $quarterData = self::getQuarterData($state, $quarter);
                                            $set('tanggal_mulai', $quarterData['start']->format('Y-m-d'));
                                            $set('tanggal_selesai', $quarterData['end']->format('Y-m-d'));
                                            $set('nama_periode', "Q{$quarter} {$state}");
                                        } elseif ($tipe === 'yearly') {
                                            $set('tanggal_mulai', "{$state}-01-01");
                                            $set('tanggal_selesai', "{$state}-12-31");
                                            $set('nama_periode', "Tahun {$state}");
                                        }
                                    }),

                                Forms\Components\Select::make('quarter')
                                    ->label('Quarter')
                                    ->options([
                                        1 => 'Q1 (Jan-Mar)',
                                        2 => 'Q2 (Apr-Jun)',
                                        3 => 'Q3 (Jul-Sep)',
                                        4 => 'Q4 (Oct-Dec)',
                                    ])
                                    ->default(1)
                                    ->native(false)
                                    ->visible(fn(callable $get) => $get('tipe_periode') === 'quarterly')
                                    ->reactive()
                                    ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                        $year = $get('tahun') ?? now()->year;
                                        $quarterData = self::getQuarterData($year, $state);
                                        $set('tanggal_mulai', $quarterData['start']->format('Y-m-d'));
                                        $set('tanggal_selesai', $quarterData['end']->format('Y-m-d'));
                                        $set('nama_periode', "Q{$state} {$year}");
                                    }),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('tanggal_mulai')
                                    ->label('Tanggal Mulai')
                                    ->required(),

                                Forms\Components\DatePicker::make('tanggal_selesai')
                                    ->label('Tanggal Selesai')
                                    ->required()
                                    ->after('tanggal_mulai'),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('status')
                                    ->label('Status')
                                    ->options([
                                        'draft' => 'Draft',
                                        'active' => 'Aktif',
                                        'completed' => 'Selesai',
                                        'archived' => 'Diarsipkan',
                                    ])
                                    ->required()
                                    ->default('draft')
                                    ->native(false),

                                Forms\Components\Toggle::make('is_active')
                                    ->label('Periode Aktif')
                                    ->helperText('Hanya satu periode yang dapat aktif pada satu waktu')
                                    ->default(false),
                            ]),
                    ])
                    ->columns(2),

                Forms\Components\Hidden::make('created_by')
                    ->default(Auth::id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nama_periode')
                    ->searchable(),
                Tables\Columns\TextColumn::make('tipe_periode'),
                Tables\Columns\TextColumn::make('tahun')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('quarter')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('tanggal_mulai')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('tanggal_selesai')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status'),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOkrPeriods::route('/'),
            'create' => Pages\CreateOkrPeriod::route('/create'),
            'edit' => Pages\EditOkrPeriod::route('/{record}/edit'),
        ];
    }
}
