<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PayrollComponentResource\Pages;
use App\Models\PayrollComponent;
use App\Models\Akun;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PayrollComponentResource extends Resource
{
    protected static ?string $model = PayrollComponent::class;

    protected static ?string $navigationIcon = 'heroicon-o-puzzle-piece';

    protected static ?string $navigationLabel = 'Payroll Components';

    protected static ?string $navigationGroup = 'Enhanced Payroll';

    protected static ?int $navigationSort = 1;

    // has access superadmin
    public static function canAccess(): bool
    {
        return auth()->user()->hasRole(['super_admin']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('component_code')
                                    ->label('Component Code')
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true)
                                    ->placeholder('GAPOK, TUNJAB, PPH21, etc.'),
                                Forms\Components\TextInput::make('component_name')
                                    ->label('Component Name')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('Gaji Pokok, Tunjangan Jabatan, etc.'),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('component_type')
                                    ->label('Component Type')
                                    ->options([
                                        'Earning' => 'Earning (Pendapatan)',
                                        'Deduction' => 'Deduction (Potongan)',
                                    ])
                                    ->required(),
                                Forms\Components\Select::make('calculation_type')
                                    ->label('Calculation Type')
                                    ->options([
                                        'Fixed' => 'Fixed Amount',
                                        'Percentage' => 'Percentage',
                                        'Formula' => 'Formula',
                                    ])
                                    ->required()
                                    ->reactive(),
                            ]),
                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Calculation Settings')
                    ->schema([
                        Forms\Components\TextInput::make('fixed_amount')
                            ->label('Fixed Amount')
                            ->numeric()
                            ->prefix('Rp')
                            ->visible(fn (callable $get) => $get('calculation_type') === 'Fixed'),
                        Forms\Components\TextInput::make('percentage_rate')
                            ->label('Percentage Rate')
                            ->numeric()
                            ->suffix('%')
                            ->minValue(0)
                            ->maxValue(100)
                            ->visible(fn (callable $get) => $get('calculation_type') === 'Percentage'),
                        Forms\Components\Textarea::make('calculation_formula')
                            ->label('Calculation Formula')
                            ->rows(3)
                            ->placeholder('BASE_SALARY * 0.1 or (BASIC_SALARY / WORKING_DAYS) * ALPHA_DAYS')
                            ->helperText('Available variables: BASE_SALARY, BASIC_SALARY, WORKING_DAYS, ATTENDANCE_DAYS')
                            ->visible(fn (callable $get) => $get('calculation_type') === 'Formula')
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Forms\Components\Section::make('Account & Tax Settings')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('account_id')
                                    ->label('Account')
                                    ->options(Akun::all()->pluck('nama_akun', 'id'))
                                    ->required()
                                    ->searchable()
                                    ->preload(),
                                Forms\Components\Toggle::make('is_taxable')
                                    ->label('Taxable')
                                    ->default(true)
                                    ->helperText('Whether this component is subject to income tax'),
                            ]),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true)
                            ->helperText('Whether this component is available for use'),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('component_code')
                    ->label('Code')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('component_name')
                    ->label('Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('component_type')
                    ->label('Type')
                    ->colors([
                        'success' => 'Earning',
                        'danger' => 'Deduction',
                    ]),
                Tables\Columns\BadgeColumn::make('calculation_type')
                    ->label('Calculation')
                    ->colors([
                        'info' => 'Fixed',
                        'warning' => 'Percentage',
                        'primary' => 'Formula',
                    ]),
                Tables\Columns\TextColumn::make('fixed_amount')
                    ->label('Fixed Amount')
                    ->money('IDR')
                    ->placeholder('N/A'),
                Tables\Columns\TextColumn::make('percentage_rate')
                    ->label('Rate')
                    ->suffix('%')
                    ->placeholder('N/A'),
                Tables\Columns\TextColumn::make('account.nama_akun')
                    ->label('Account')
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_taxable')
                    ->label('Taxable')
                    ->boolean(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('usage_count')
                    ->label('Usage')
                    ->alignCenter(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('component_type')
                    ->options([
                        'Earning' => 'Earning',
                        'Deduction' => 'Deduction',
                    ]),
                Tables\Filters\SelectFilter::make('calculation_type')
                    ->options([
                        'Fixed' => 'Fixed',
                        'Percentage' => 'Percentage',
                        'Formula' => 'Formula',
                    ]),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->boolean()
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only')
                    ->native(false),
                Tables\Filters\TernaryFilter::make('is_taxable')
                    ->label('Taxable')
                    ->boolean()
                    ->trueLabel('Taxable only')
                    ->falseLabel('Non-taxable only')
                    ->native(false),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn (PayrollComponent $record) => $record->canBeDeleted()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('component_code');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayrollComponents::route('/'),
            'create' => Pages\CreatePayrollComponent::route('/create'),
            'view' => Pages\ViewPayrollComponent::route('/{record}'),
            'edit' => Pages\EditPayrollComponent::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
