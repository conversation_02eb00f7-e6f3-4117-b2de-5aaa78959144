<?php

namespace App\Filament\Resources\PayrollComponentResource\Pages;

use App\Filament\Resources\PayrollComponentResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreatePayrollComponent extends CreateRecord
{
    protected static string $resource = PayrollComponentResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = auth()->id();
        
        return $data;
    }
}
