<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PayrollPeriodResource\Pages;
use App\Models\PayrollPeriod;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Hidden;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Support\Facades\Auth;

class PayrollPeriodResource extends Resource
{
    protected static ?string $model = PayrollPeriod::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static ?string $navigationLabel = 'Periode Payroll';

    protected static ?string $modelLabel = 'Periode Payroll';

    protected static ?string $pluralModelLabel = 'Periode Payroll';

    protected static ?string $navigationGroup = 'Payroll Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Periode')
                    ->schema([
                        TextInput::make('nama_periode')
                            ->label('Nama Periode')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Contoh: Payroll Januari 2025'),

                        DatePicker::make('tanggal_mulai')
                            ->label('Tanggal Mulai')
                            ->required()
                            ->displayFormat('d/m/Y')
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                if ($state && !$get('tanggal_selesai')) {
                                    $endDate = \Carbon\Carbon::parse($state)->endOfMonth();
                                    $set('tanggal_selesai', $endDate->format('Y-m-d'));
                                    $set('tanggal_cutoff', $endDate->format('Y-m-d'));
                                }
                            }),

                        DatePicker::make('tanggal_selesai')
                            ->label('Tanggal Selesai')
                            ->required()
                            ->displayFormat('d/m/Y')
                            ->afterOrEqual('tanggal_mulai'),

                        DatePicker::make('tanggal_cutoff')
                            ->label('Tanggal Cutoff')
                            ->required()
                            ->displayFormat('d/m/Y')
                            ->helperText('Tanggal batas untuk perhitungan absensi dan pelanggaran'),
                    ])->columns(2),

                Forms\Components\Section::make('Status dan Keterangan')
                    ->schema([
                        Select::make('status')
                            ->label('Status')
                            ->options([
                                'draft' => 'Draft',
                                'processing' => 'Processing',
                                'completed' => 'Completed',
                                'cancelled' => 'Cancelled',
                            ])
                            ->default('draft')
                            ->disabled(fn($record) => $record && $record->status !== 'draft'),

                        Textarea::make('keterangan')
                            ->label('Keterangan')
                            ->rows(3)
                            ->placeholder('Keterangan periode payroll'),

                        Hidden::make('created_by')
                            ->default(Auth::id()),
                    ])->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('kode_periode')
                    ->label('Kode Periode')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('nama_periode')
                    ->label('Nama Periode')
                    ->searchable()
                    ->sortable()
                    ->wrap(),

                TextColumn::make('formatted_periode')
                    ->label('Periode')
                    ->sortable(query: function ($query, $direction) {
                        return $query->orderBy('tanggal_mulai', $direction);
                    }),

                TextColumn::make('tanggal_cutoff')
                    ->label('Cutoff')
                    ->date('d M Y')
                    ->sortable(),

                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'draft' => 'gray',
                        'processing' => 'warning',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                TextColumn::make('total_karyawan')
                    ->label('Total Karyawan')
                    ->numeric()
                    ->sortable(),

                TextColumn::make('total_payroll')
                    ->label('Total Payroll')
                    ->money('IDR')
                    ->sortable(),

                TextColumn::make('creator.name')
                    ->label('Dibuat Oleh')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'processing' => 'Processing',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn($record) => $record->status === 'draft'),
                Tables\Actions\Action::make('process')
                    ->label('Proses Payroll')
                    ->icon('heroicon-o-play')
                    ->color('warning')
                    ->visible(fn($record) => $record->status === 'draft')
                    ->form([
                        Forms\Components\CheckboxList::make('karyawan_ids')
                            ->label('Pilih Karyawan')
                            ->options(function () {
                                return \App\Models\Karyawan::where('status_aktif', true)
                                    ->whereHas('penggajian')
                                    ->with(['jabatan', 'penggajian' => function ($q) {
                                        $q->latest();
                                    }])
                                    ->get()
                                    ->mapWithKeys(function ($karyawan) {
                                        $gaji = $karyawan->penggajian->first();
                                        $gajiInfo = $gaji ? 'Rp ' . number_format($gaji->gaji_pokok, 0, ',', '.') : 'Belum ada gaji';

                                        return [
                                            $karyawan->id => "{$karyawan->nama_lengkap} - {$karyawan->jabatan?->nama_jabatan} ({$gajiInfo})"
                                        ];
                                    })
                                    ->toArray();
                            })
                            ->searchable()
                            ->bulkToggleable()
                            ->required()
                            ->default(function () {
                                return \App\Models\Karyawan::where('status_aktif', true)
                                    ->whereHas('penggajian')
                                    ->pluck('id')
                                    ->toArray();
                            }),
                    ])
                    ->action(function ($record, array $data) {
                        if (empty($data['karyawan_ids'])) {
                            \Filament\Notifications\Notification::make()
                                ->title('Tidak ada karyawan dipilih')
                                ->body('Pilih minimal satu karyawan untuk diproses.')
                                ->warning()
                                ->send();
                            return;
                        }

                        try {
                            \Illuminate\Support\Facades\DB::beginTransaction();

                            // Update status periode menjadi processing
                            $record->startProcessing(auth()->id());

                            // Proses payroll menggunakan service
                            $payrollService = new \App\Services\PayrollService();
                            $payrollService->generatePayroll($record, $data['karyawan_ids']);

                            // Update status periode menjadi completed
                            $record->completeProcessing();

                            \Illuminate\Support\Facades\DB::commit();

                            \Filament\Notifications\Notification::make()
                                ->title('Payroll berhasil diproses')
                                ->body('Payroll untuk ' . count($data['karyawan_ids']) . ' karyawan telah berhasil diproses.')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            \Illuminate\Support\Facades\DB::rollback();

                            \Filament\Notifications\Notification::make()
                                ->title('Gagal memproses payroll')
                                ->body('Terjadi kesalahan: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Proses Payroll')
                    ->modalDescription('Pilih karyawan yang akan diproses payrollnya untuk periode ini.')
                    ->modalSubmitActionLabel('Proses Payroll'),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn($record) => $record->status === 'draft'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn($records) => $records && $records->every(fn($record) => $record->status === 'draft')),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayrollPeriods::route('/'),
            'create' => Pages\CreatePayrollPeriod::route('/create'),
            'view' => Pages\ViewPayrollPeriod::route('/{record}'),
            'edit' => Pages\EditPayrollPeriod::route('/{record}/edit'),
        ];
    }
}
