<?php

namespace App\Filament\Resources\PayrollPeriodResource\Pages;

use App\Filament\Resources\PayrollPeriodResource;
use App\Models\Karyawan;
use App\Models\PayrollPeriod;
use App\Services\PayrollService;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Actions;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Section;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;

class ProcessPayrollPeriod extends EditRecord
{
    protected static string $resource = PayrollPeriodResource::class;

    protected static string $view = 'filament.resources.payroll-period-resource.pages.process-payroll-period';

    public ?array $data = [];

    public function mount(int | string $record): void
    {
        parent::mount($record);

        // Cek apakah periode bisa diproses
        if (!$this->record->canBeProcessed()) {
            Notification::make()
                ->title('Periode tidak dapat diproses')
                ->body('Periode payroll ini sudah diproses atau dibatalkan.')
                ->danger()
                ->send();

            $this->redirect(PayrollPeriodResource::getUrl('index'));
            return;
        }

        $this->form->fill([
            'karyawan_ids' => Karyawan::where('status_aktif', true)
                ->whereHas('penggajian')
                ->pluck('id')
                ->toArray(),
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Pilih Karyawan untuk Diproses')
                    ->description('Pilih karyawan yang akan diproses payrollnya untuk periode ini.')
                    ->schema([
                        CheckboxList::make('karyawan_ids')
                            ->label('Karyawan')
                            ->options(function () {
                                return Karyawan::where('status_aktif', true)
                                    ->whereHas('penggajian')
                                    ->with(['jabatan', 'departemen', 'penggajian' => function ($q) {
                                        $q->latest();
                                    }])
                                    ->get()
                                    ->mapWithKeys(function ($karyawan) {
                                        $gaji = $karyawan->penggajian->first();
                                        $gajiInfo = $gaji ? 'Rp ' . number_format($gaji->gaji_pokok, 0, ',', '.') : 'Belum ada gaji';

                                        return [
                                            $karyawan->id => "{$karyawan->nama_lengkap} - {$karyawan->jabatan?->nama_jabatan} ({$gajiInfo})"
                                        ];
                                    })
                                    ->toArray();
                            })
                            ->searchable()
                            ->bulkToggleable()
                            ->required()
                            ->columns(1),
                    ]),
            ])
            ->statePath('data');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('process')
                ->label('Proses Payroll')
                ->icon('heroicon-o-play')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading('Konfirmasi Proses Payroll')
                ->modalDescription('Apakah Anda yakin ingin memproses payroll untuk periode ini? Proses ini akan menghitung semua komponen gaji, potongan keterlambatan, dan pelanggaran.')
                ->modalSubmitActionLabel('Ya, Proses Payroll')
                ->action('processPayroll'),

            Actions\Action::make('back')
                ->label('Kembali')
                ->icon('heroicon-o-arrow-left')
                ->color('gray')
                ->url(fn() => PayrollPeriodResource::getUrl('index')),
        ];
    }

    public function processPayroll(): void
    {
        $data = $this->form->getState();

        if (empty($data['karyawan_ids'])) {
            Notification::make()
                ->title('Tidak ada karyawan dipilih')
                ->body('Pilih minimal satu karyawan untuk diproses.')
                ->warning()
                ->send();
            return;
        }

        try {
            DB::beginTransaction();

            // Update status periode menjadi processing
            $this->record->startProcessing(auth()->id());

            // Proses payroll menggunakan service
            $payrollService = new PayrollService();
            $payrollService->generatePayroll($this->record, $data['karyawan_ids']);

            // Update status periode menjadi completed
            $this->record->completeProcessing();

            DB::commit();

            Notification::make()
                ->title('Payroll berhasil diproses')
                ->body('Payroll untuk ' . count($data['karyawan_ids']) . ' karyawan telah berhasil diproses.')
                ->success()
                ->send();

            $this->redirect(PayrollPeriodResource::getUrl('view', ['record' => $this->record]));
        } catch (\Exception $e) {
            DB::rollback();

            Notification::make()
                ->title('Gagal memproses payroll')
                ->body('Terjadi kesalahan: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function getTitle(): string
    {
        return 'Proses Payroll - ' . $this->record->nama_periode;
    }
}
