<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PayrollTransactionResource\Pages;
use App\Filament\Resources\PayrollTransactionResource\RelationManagers;
use App\Models\PayrollTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use App\Traits\HasExportActions;
use App\Exports\PayrollTransactionExport;

class PayrollTransactionResource extends Resource
{
    use HasExportActions;

    protected static ?string $model = PayrollTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationLabel = 'Transaksi Payroll';

    protected static ?string $modelLabel = 'Transaksi Payroll';

    protected static ?string $pluralModelLabel = 'Transaksi Payroll';

    protected static ?string $navigationGroup = 'Payroll Management';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form hanya untuk view, tidak untuk create/edit
                Forms\Components\Placeholder::make('info')
                    ->label('')
                    ->content('Transaksi payroll tidak dapat dibuat atau diedit secara manual. Gunakan fitur "Proses Payroll" pada periode payroll.'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('no_payroll')
                    ->label('No. Payroll')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('payrollPeriod.nama_periode')
                    ->label('Periode')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('karyawan.nama_lengkap')
                    ->label('Karyawan')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('karyawan.jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('total_gaji_kotor')
                    ->label('Gaji Kotor')
                    ->money('IDR')
                    ->sortable(),

                TextColumn::make('total_potongan')
                    ->label('Total Potongan')
                    ->money('IDR')
                    ->sortable(),

                TextColumn::make('take_home_pay')
                    ->label('Take Home Pay')
                    ->money('IDR')
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'draft' => 'gray',
                        'approved' => 'success',
                        'paid' => 'primary',
                        'cancelled' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                TextColumn::make('total_hari_hadir')
                    ->label('Hari Hadir')
                    ->numeric()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('total_menit_terlambat')
                    ->label('Menit Terlambat')
                    ->numeric()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('total_pelanggaran')
                    ->label('Pelanggaran')
                    ->numeric()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                SelectFilter::make('payroll_period_id')
                    ->label('Periode Payroll')
                    ->relationship('payrollPeriod', 'nama_periode')
                    ->searchable(),

                SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'approved' => 'Approved',
                        'paid' => 'Paid',
                        'cancelled' => 'Cancelled',
                    ]),

                Filter::make('take_home_pay')
                    ->form([
                        Forms\Components\TextInput::make('min_amount')
                            ->label('Minimal Take Home Pay')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('max_amount')
                            ->label('Maksimal Take Home Pay')
                            ->numeric()
                            ->prefix('Rp'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['min_amount'],
                                fn(Builder $query, $amount): Builder => $query->where('take_home_pay', '>=', $amount),
                            )
                            ->when(
                                $data['max_amount'],
                                fn(Builder $query, $amount): Builder => $query->where('take_home_pay', '<=', $amount),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn($record) => $record->status === 'draft')
                    ->requiresConfirmation()
                    ->action(fn($record) => $record->approve(auth()->id())),
                Tables\Actions\Action::make('mark_paid')
                    ->label('Mark as Paid')
                    ->icon('heroicon-o-banknotes')
                    ->color('primary')
                    ->visible(fn($record) => $record->status === 'approved')
                    ->requiresConfirmation()
                    ->action(fn($record) => $record->markAsPaid()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('bulk_approve')
                        ->label('Approve Selected')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                if ($record->status === 'draft') {
                                    $record->approve(auth()->id());
                                }
                            }
                        }),
                ]),
            ])
            ->headerActions([
                ...self::getExportActions(PayrollTransactionExport::class, 'Data PayrollTransaction'),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\PayrollDeductionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayrollTransactions::route('/'),
            'view' => Pages\ViewPayrollTransaction::route('/{record}'),
        ];
    }

    public static function canCreate(): bool
    {
        return false; // Tidak bisa create manual
    }

    public static function canEdit($record): bool
    {
        return false; // Tidak bisa edit manual
    }
}
