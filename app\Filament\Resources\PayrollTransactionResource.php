<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PayrollTransactionResource\Pages;
use App\Filament\Resources\PayrollTransactionResource\RelationManagers;
use App\Models\PayrollTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use App\Traits\HasExportActions;
use App\Exports\PayrollTransactionExport;

class PayrollTransactionResource extends Resource
{
    use HasExportActions;

    protected static ?string $model = PayrollTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationLabel = 'Transaksi Payroll';

    protected static ?string $modelLabel = 'Transaksi Payroll';

    protected static ?string $pluralModelLabel = 'Transaksi Payroll';

    protected static ?string $navigationGroup = 'Payroll Management';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Payroll')
                    ->schema([
                        Forms\Components\TextInput::make('no_payroll')
                            ->label('No. Payroll')
                            ->disabled(),
                        Forms\Components\Select::make('payroll_period_id')
                            ->label('Periode Payroll')
                            ->relationship('payrollPeriod', 'nama_periode')
                            ->disabled(),
                        Forms\Components\Select::make('karyawan_id')
                            ->label('Karyawan')
                            ->relationship('karyawan', 'nama_lengkap')
                            ->disabled(),
                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'draft' => 'Draft',
                                'approved' => 'Approved',
                                'paid' => 'Paid',
                                'cancelled' => 'Cancelled',
                            ])
                            ->disabled(),
                    ])->columns(2),

                Forms\Components\Section::make('Komponen Gaji')
                    ->schema([
                        Forms\Components\TextInput::make('gaji_pokok')
                            ->label('Gaji Pokok')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('tunjangan_jabatan')
                            ->label('Tunjangan Jabatan')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('tunjangan_umum')
                            ->label('Tunjangan Umum')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('tunjangan_sembako')
                            ->label('Tunjangan Sembako')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('lembur_biasa')
                            ->label('Lembur Biasa')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('lembur_tanggal_merah')
                            ->label('Lembur Tanggal Merah')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('lembur_tambah_hk')
                            ->label('Lembur Tambah HK')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('kekurangan_gaji_bulan_sebelum')
                            ->label('Kekurangan Gaji Bulan Sebelum')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('claim_sakit_dengan_surat')
                            ->label('Claim Sakit dengan Surat')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('pesangon')
                            ->label('Pesangon')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('insentif')
                            ->label('Insentif')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('total_gaji_kotor')
                            ->label('Total Gaji Kotor')
                            ->numeric()
                            ->prefix('Rp')
                            ->disabled(),
                    ])->columns(3),

                Forms\Components\Section::make('Potongan')
                    ->schema([
                        Forms\Components\TextInput::make('potongan_bpjs_kesehatan')
                            ->label('Potongan BPJS Kesehatan')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('potongan_bpjs_tk')
                            ->label('Potongan BPJS TK')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('potongan_keterlambatan')
                            ->label('Potongan Keterlambatan')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('potongan_pelanggaran')
                            ->label('Potongan Pelanggaran')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('potongan_lainnya')
                            ->label('Potongan Lainnya')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('total_potongan')
                            ->label('Total Potongan')
                            ->numeric()
                            ->prefix('Rp')
                            ->disabled(),
                    ])->columns(3),

                Forms\Components\Section::make('Data Absensi')
                    ->schema([
                        Forms\Components\TextInput::make('total_hari_kerja')
                            ->label('Total Hari Kerja')
                            ->numeric()
                            ->disabled(),
                        Forms\Components\TextInput::make('total_hari_hadir')
                            ->label('Total Hari Hadir')
                            ->numeric()
                            ->disabled(),
                        Forms\Components\TextInput::make('total_menit_terlambat')
                            ->label('Total Menit Terlambat')
                            ->numeric()
                            ->disabled(),
                        Forms\Components\TextInput::make('total_pelanggaran')
                            ->label('Total Pelanggaran')
                            ->numeric()
                            ->disabled(),
                        Forms\Components\TextInput::make('sakit_dengan_surat')
                            ->label('Sakit dengan Surat')
                            ->numeric()
                            ->suffix('hari')
                            ->disabled(),
                        Forms\Components\TextInput::make('sakit_tanpa_surat')
                            ->label('Sakit tanpa Surat')
                            ->numeric()
                            ->suffix('hari')
                            ->disabled(),
                        Forms\Components\TextInput::make('izin')
                            ->label('Izin')
                            ->numeric()
                            ->suffix('hari')
                            ->disabled(),
                        Forms\Components\TextInput::make('ambil_cuti')
                            ->label('Ambil Cuti')
                            ->numeric()
                            ->suffix('hari')
                            ->disabled(),
                        Forms\Components\TextInput::make('sisa_cuti')
                            ->label('Sisa Cuti')
                            ->numeric()
                            ->suffix('hari')
                            ->disabled(),
                    ])->columns(3),

                Forms\Components\Section::make('Total')
                    ->schema([
                        Forms\Components\TextInput::make('take_home_pay')
                            ->label('Take Home Pay')
                            ->numeric()
                            ->prefix('Rp')
                            ->disabled(),
                        Forms\Components\Textarea::make('keterangan')
                            ->label('Keterangan')
                            ->rows(3),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('no_payroll')
                    ->label('No. Payroll')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('payrollPeriod.nama_periode')
                    ->label('Periode')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('karyawan.nama_lengkap')
                    ->label('Karyawan')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('karyawan.jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('total_gaji_kotor')
                    ->label('Gaji Kotor')
                    ->money('IDR')
                    ->sortable(),

                TextColumn::make('total_potongan')
                    ->label('Total Potongan')
                    ->money('IDR')
                    ->sortable(),

                TextColumn::make('take_home_pay')
                    ->label('Take Home Pay')
                    ->money('IDR')
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'draft' => 'gray',
                        'approved' => 'success',
                        'paid' => 'primary',
                        'cancelled' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                TextColumn::make('total_hari_hadir')
                    ->label('Hari Hadir')
                    ->numeric()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('total_menit_terlambat')
                    ->label('Menit Terlambat')
                    ->numeric()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('total_pelanggaran')
                    ->label('Pelanggaran')
                    ->numeric()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                SelectFilter::make('payroll_period_id')
                    ->label('Periode Payroll')
                    ->relationship('payrollPeriod', 'nama_periode')
                    ->searchable(),

                SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'approved' => 'Approved',
                        'paid' => 'Paid',
                        'cancelled' => 'Cancelled',
                    ]),

                Filter::make('take_home_pay')
                    ->form([
                        Forms\Components\TextInput::make('min_amount')
                            ->label('Minimal Take Home Pay')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('max_amount')
                            ->label('Maksimal Take Home Pay')
                            ->numeric()
                            ->prefix('Rp'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['min_amount'],
                                fn(Builder $query, $amount): Builder => $query->where('take_home_pay', '>=', $amount),
                            )
                            ->when(
                                $data['max_amount'],
                                fn(Builder $query, $amount): Builder => $query->where('take_home_pay', '<=', $amount),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn($record) => $record->status === 'draft'),
                Tables\Actions\Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn($record) => $record->status === 'draft')
                    ->requiresConfirmation()
                    ->action(fn($record) => $record->approve(auth()->id())),
                Tables\Actions\Action::make('mark_paid')
                    ->label('Mark as Paid')
                    ->icon('heroicon-o-banknotes')
                    ->color('primary')
                    ->visible(fn($record) => $record->status === 'approved')
                    ->requiresConfirmation()
                    ->action(fn($record) => $record->markAsPaid()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('bulk_approve')
                        ->label('Approve Selected')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                if ($record->status === 'draft') {
                                    $record->approve(auth()->id());
                                }
                            }
                        }),
                ]),
            ])
            ->headerActions([
                Tables\Actions\Action::make('export_excel')
                    ->label('Export Excel')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->form([
                        Forms\Components\Select::make('payroll_period_id')
                            ->label('Pilih Periode Payroll')
                            ->options(\App\Models\PayrollPeriod::pluck('nama_periode', 'id'))
                            ->placeholder('Semua Periode')
                            ->helperText('Kosongkan untuk export semua periode'),
                    ])
                    ->action(function (array $data) {
                        try {
                            $periodId = $data['payroll_period_id'] ?? null;
                            $periodName = $periodId ?
                                \App\Models\PayrollPeriod::find($periodId)->nama_periode :
                                'Semua_Periode';

                            $fileName = 'payroll_transaction_' .
                                \Illuminate\Support\Str::slug($periodName) . '_' .
                                now()->format('Y-m-d_H-i-s') . '.xlsx';

                            return response()->streamDownload(function () use ($periodId) {
                                echo \Maatwebsite\Excel\Facades\Excel::raw(
                                    new PayrollTransactionExport($periodId),
                                    \Maatwebsite\Excel\Excel::XLSX
                                );
                            }, $fileName);
                        } catch (\Exception $e) {
                            \Filament\Notifications\Notification::make()
                                ->title('Export Excel Error')
                                ->body('Terjadi kesalahan saat export Excel: ' . $e->getMessage())
                                ->danger()
                                ->send();

                            return null;
                        }
                    }),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\PayrollDeductionsRelationManager::class,
            RelationManagers\AbsensiRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayrollTransactions::route('/'),
            'view' => Pages\ViewPayrollTransaction::route('/{record}'),
            'edit' => Pages\EditPayrollTransaction::route('/{record}/edit'),
        ];
    }

    public static function canCreate(): bool
    {
        return false; // Tidak bisa create manual
    }

    public static function canEdit($record): bool
    {
        return $record->status === 'draft'; // Hanya bisa edit jika status draft
    }
}
