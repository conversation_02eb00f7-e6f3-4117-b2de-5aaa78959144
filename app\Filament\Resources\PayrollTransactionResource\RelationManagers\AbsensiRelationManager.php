<?php

namespace App\Filament\Resources\PayrollTransactionResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\BadgeColumn;
use App\Services\PermissionService;

class AbsensiRelationManager extends RelationManager
{
    protected static string $relationship = 'absensiRecords';

    protected static ?string $title = 'Data Absensi';

    protected static ?string $recordTitleAttribute = 'tanggal_absensi';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Absensi')
                    ->schema([
                        Forms\Components\DatePicker::make('tanggal_absensi')
                            ->label('Tanggal Absensi')
                            ->required()
                            ->disabled(),
                        
                        Forms\Components\TimePicker::make('waktu_masuk')
                            ->label('Waktu Masuk')
                            ->disabled(),
                        
                        Forms\Components\TimePicker::make('waktu_keluar')
                            ->label('Waktu Keluar')
                            ->disabled(),
                        
                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'hadir' => 'Hadir',
                                'terlambat' => 'Terlambat',
                                'alpha' => 'Alpha',
                                'sakit' => 'Sakit',
                                'izin' => 'Izin',
                                'cuti' => 'Cuti',
                            ])
                            ->disabled(),
                    ])->columns(2),

                Forms\Components\Section::make('Toleransi Keterlambatan')
                    ->schema([
                        Forms\Components\Toggle::make('is_tolerance_given')
                            ->label('Berikan Toleransi')
                            ->helperText('Aktifkan untuk memberikan toleransi keterlambatan')
                            ->reactive()
                            ->visible(function ($record) {
                                return $record && $record->status === 'terlambat' && 
                                       PermissionService::canManageAbsensi($record);
                            }),

                        Forms\Components\Textarea::make('tolerance_reason')
                            ->label('Alasan Toleransi')
                            ->placeholder('Masukkan alasan pemberian toleransi keterlambatan...')
                            ->rows(3)
                            ->required()
                            ->visible(fn($get) => $get('is_tolerance_given'))
                            ->columnSpanFull(),

                        Forms\Components\Placeholder::make('tolerance_info')
                            ->label('Informasi Toleransi')
                            ->content(function ($record) {
                                if (!$record || !$record->is_tolerance_given) {
                                    return 'Belum ada toleransi yang diberikan.';
                                }
                                
                                $info = "Alasan: {$record->tolerance_reason}\n";
                                $info .= "Diberikan oleh: " . ($record->toleranceApprovedBy->name ?? 'Unknown') . "\n";
                                $info .= "Pada: " . $record->tolerance_approved_at->format('d M Y H:i');
                                
                                return $info;
                            })
                            ->visible(fn($record) => $record && $record->is_tolerance_given)
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => $record && $record->status === 'terlambat'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('tanggal_absensi')
            ->columns([
                TextColumn::make('tanggal_absensi')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable(),

                TextColumn::make('waktu_masuk')
                    ->label('Waktu Masuk')
                    ->time('H:i')
                    ->placeholder('-'),

                TextColumn::make('waktu_keluar')
                    ->label('Waktu Keluar')
                    ->time('H:i')
                    ->placeholder('-'),

                BadgeColumn::make('status')
                    ->label('Status')
                    ->colors([
                        'success' => 'hadir',
                        'warning' => 'terlambat',
                        'danger' => 'alpha',
                        'info' => ['sakit', 'izin', 'cuti'],
                    ]),

                IconColumn::make('is_tolerance_given')
                    ->label('Toleransi')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('gray')
                    ->tooltip(fn($record) => $record->is_tolerance_given ? 
                        "Toleransi: {$record->tolerance_reason}" : 'Tidak ada toleransi'),

                TextColumn::make('jadwal.shift.nama_shift')
                    ->label('Shift')
                    ->placeholder('-'),

                TextColumn::make('jadwal.entitas.nama')
                    ->label('Lokasi')
                    ->placeholder('-'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'hadir' => 'Hadir',
                        'terlambat' => 'Terlambat',
                        'alpha' => 'Alpha',
                        'sakit' => 'Sakit',
                        'izin' => 'Izin',
                        'cuti' => 'Cuti',
                    ]),

                Tables\Filters\TernaryFilter::make('is_tolerance_given')
                    ->label('Status Toleransi')
                    ->placeholder('Semua')
                    ->trueLabel('Sudah diberi toleransi')
                    ->falseLabel('Belum diberi toleransi'),
            ])
            ->headerActions([
                // No create action - absensi dibuat otomatis
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn($record) => $record->status === 'terlambat' && 
                        PermissionService::canManageAbsensi($record))
                    ->mutateFormDataUsing(function (array $data, $record): array {
                        if ($data['is_tolerance_given'] && $record->tolerance_approved_by) {
                            $data['tolerance_approved_by'] = auth()->id();
                            $data['tolerance_approved_at'] = now();
                        }
                        return $data;
                    }),
            ])
            ->bulkActions([
                // No bulk actions
            ])
            ->defaultSort('tanggal_absensi', 'desc')
            ->emptyStateHeading('Tidak Ada Data Absensi')
            ->emptyStateDescription('Tidak ada data absensi untuk periode payroll ini.')
            ->emptyStateIcon('heroicon-o-calendar');
    }

    public function isReadOnly(): bool
    {
        return false; // Allow viewing and tolerance management
    }
}
