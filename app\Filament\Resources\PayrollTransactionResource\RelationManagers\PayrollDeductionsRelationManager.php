<?php

namespace App\Filament\Resources\PayrollTransactionResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;


class PayrollDeductionsRelationManager extends RelationManager
{
    protected static string $relationship = 'payrollDeductions';

    protected static ?string $title = 'Detail Potongan';

    protected static ?string $recordTitleAttribute = 'deskripsi';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Potongan')
                    ->schema([
                        Forms\Components\Select::make('jenis_potongan')
                            ->label('Jenis <PERSON>tongan')
                            ->options([
                                'bpjs_kesehatan' => 'BPJS Kesehatan',
                                'bpjs_tk' => 'BPJS Tenaga Kerja',
                                'keterlambatan' => 'Keterlambatan',
                                'pelanggaran' => 'Pelanggaran',
                                'kasir' => 'Potongan Kasir',
                                'stok_opname' => 'Potongan Stok Opname',
                                'retur' => 'Potongan Retur',
                                'kasbon' => 'Potongan Kasbon',
                                'sakit_tanpa_surat' => 'Sakit Tanpa Surat',
                                'alpha' => 'Alpha/Tidak Hadir',
                                'cuti_melebihi_kuota' => 'Cuti Melebihi Kuota',
                                'lainnya' => 'Lainnya',
                            ])
                            ->required()
                            ->disabled(fn($record) => $record && in_array($record->jenis_potongan, ['bpjs_kesehatan', 'bpjs_tk', 'keterlambatan', 'pelanggaran'])),

                        Forms\Components\TextInput::make('kode_referensi')
                            ->label('Kode Referensi')
                            ->helperText('ID referensi untuk potongan (opsional)')
                            ->disabled(fn($record) => $record && in_array($record->jenis_potongan, ['bpjs_kesehatan', 'bpjs_tk', 'keterlambatan', 'pelanggaran'])),
                    ])->columns(2),

                Forms\Components\Section::make('Detail Potongan')
                    ->schema([
                        Forms\Components\Textarea::make('deskripsi')
                            ->label('Deskripsi')
                            ->required()
                            ->rows(2),

                        Forms\Components\TextInput::make('nominal')
                            ->label('Nominal')
                            ->numeric()
                            ->prefix('Rp')
                            ->required(),

                        Forms\Components\DatePicker::make('tanggal_kejadian')
                            ->label('Tanggal Kejadian')
                            ->required(),

                        Forms\Components\Textarea::make('keterangan')
                            ->label('Keterangan Tambahan')
                            ->rows(2),
                    ])->columns(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('deskripsi')
            ->columns([
                TextColumn::make('jenis_label')
                    ->label('Jenis Potongan')
                    ->badge()
                    ->color(fn(string $state): string => match (true) {
                        in_array($state, ['BPJS Kesehatan', 'BPJS Tenaga Kerja', 'Potongan Stok Opname']) => 'info',
                        in_array($state, ['Keterlambatan', 'Potongan Kasir', 'Cuti Melebihi Kuota']) => 'warning',
                        in_array($state, ['Pelanggaran', 'Potongan Retur', 'Sakit Tanpa Surat', 'Alpha/Tidak Hadir']) => 'danger',
                        in_array($state, ['Potongan Kasbon', 'Lainnya']) => 'secondary',
                        default => 'gray',
                    }),

                TextColumn::make('deskripsi')
                    ->label('Deskripsi')
                    ->wrap()
                    ->formatStateUsing(function ($state, $record) {
                        // Tambahkan info toleransi untuk potongan keterlambatan
                        if ($record->jenis_potongan === 'keterlambatan' && $record->kode_referensi) {
                            $absensi = \App\Models\Absensi::find($record->kode_referensi);
                            if ($absensi && $absensi->is_tolerance_given) {
                                return $state . ' (TOLERANSI DIBERIKAN - Potongan tidak berlaku)';
                            }
                        }
                        return $state;
                    })
                    ->color(function ($record) {
                        if ($record->jenis_potongan === 'keterlambatan' && $record->kode_referensi) {
                            $absensi = \App\Models\Absensi::find($record->kode_referensi);
                            if ($absensi && $absensi->is_tolerance_given) {
                                return 'success';
                            }
                        }
                        return null;
                    }),

                TextColumn::make('tanggal_kejadian')
                    ->label('Tanggal Kejadian')
                    ->date('d M Y')
                    ->sortable()
                    ->placeholder('-'),

                TextColumn::make('formatted_nominal')
                    ->label('Nominal')
                    ->sortable(query: function ($query, $direction) {
                        return $query->orderBy('nominal', $direction);
                    })
                    ->weight('bold'),

                TextColumn::make('keterangan')
                    ->label('Keterangan')
                    ->limit(50)
                    ->placeholder('-')
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis_potongan')
                    ->label('Jenis Potongan')
                    ->options([
                        'bpjs_kesehatan' => 'BPJS Kesehatan',
                        'bpjs_tk' => 'BPJS Tenaga Kerja',
                        'keterlambatan' => 'Keterlambatan',
                        'pelanggaran' => 'Pelanggaran',
                        'kasir' => 'Potongan Kasir',
                        'stok_opname' => 'Potongan Stok Opname',
                        'retur' => 'Potongan Retur',
                        'kasbon' => 'Potongan Kasbon',
                        'sakit_tanpa_surat' => 'Sakit Tanpa Surat',
                        'alpha' => 'Alpha/Tidak Hadir',
                        'cuti_melebihi_kuota' => 'Cuti Melebihi Kuota',
                        'lainnya' => 'Lainnya',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->visible(fn() => $this->getOwnerRecord()->status === 'draft')
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['payroll_transaction_id'] = $this->getOwnerRecord()->id;
                        return $data;
                    })
                    ->after(function () {
                        $this->recalculatePayrollTotals();
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn() => $this->getOwnerRecord()->status === 'draft')
                    ->after(function () {
                        $this->recalculatePayrollTotals();
                    }),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn($record) => $this->getOwnerRecord()->status === 'draft' &&
                        !in_array($record->jenis_potongan, ['bpjs_kesehatan', 'bpjs_tk', 'keterlambatan', 'pelanggaran']))
                    ->after(function () {
                        $this->recalculatePayrollTotals();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn() => $this->getOwnerRecord()->status === 'draft'),
                ]),
            ])
            ->defaultSort('created_at', 'asc')
            ->emptyStateHeading('Tidak Ada Potongan')
            ->emptyStateDescription('Tidak ada detail potongan untuk transaksi payroll ini.')
            ->emptyStateIcon('heroicon-o-banknotes');
    }

    public function isReadOnly(): bool
    {
        return $this->getOwnerRecord()->status !== 'draft';
    }

    /**
     * Recalculate payroll totals after deduction changes
     */
    protected function recalculatePayrollTotals(): void
    {
        $payroll = $this->getOwnerRecord();

        // Hitung ulang total potongan dari detail potongan
        $totalPotonganDetail = $payroll->payrollDeductions()
            ->whereNotIn('jenis_potongan', ['bpjs_kesehatan', 'bpjs_tk'])
            ->sum('nominal');

        // Update total potongan lainnya
        $payroll->update([
            'potongan_lainnya' => $totalPotonganDetail,
            'total_potongan' => $payroll->potongan_bpjs_kesehatan +
                $payroll->potongan_bpjs_tk +
                $payroll->potongan_keterlambatan +
                $payroll->potongan_pelanggaran +
                $totalPotonganDetail,
            'take_home_pay' => $payroll->total_gaji_kotor -
                ($payroll->potongan_bpjs_kesehatan +
                    $payroll->potongan_bpjs_tk +
                    $payroll->potongan_keterlambatan +
                    $payroll->potongan_pelanggaran +
                    $totalPotonganDetail),
        ]);

        \Filament\Notifications\Notification::make()
            ->title('Total Payroll Diperbarui')
            ->body('Total potongan dan take home pay telah dihitung ulang.')
            ->success()
            ->send();
    }
}
