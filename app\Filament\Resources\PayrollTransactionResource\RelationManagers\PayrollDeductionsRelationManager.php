<?php

namespace App\Filament\Resources\PayrollTransactionResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;


class PayrollDeductionsRelationManager extends RelationManager
{
    protected static string $relationship = 'payrollDeductions';

    protected static ?string $title = 'Detail Potongan';

    protected static ?string $recordTitleAttribute = 'deskripsi';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Read-only form
                Forms\Components\Placeholder::make('info')
                    ->label('')
                    ->content('Detail potongan tidak dapat diedit secara manual.'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('deskripsi')
            ->columns([
                TextColumn::make('jenis_label')
                    ->label('<PERSON><PERSON>')
                    ->badge()
                    ->color(fn(string $state): string => match (true) {
                        in_array($state, ['BPJS Kesehatan', 'BPJS Tenaga Kerja', 'Potongan Stok Opname']) => 'info',
                        in_array($state, ['Keterlambatan', 'Potongan Kasir', 'Cuti Melebihi Kuota']) => 'warning',
                        in_array($state, ['Pelanggaran', 'Potongan Retur', 'Sakit Tanpa Surat', 'Alpha/Tidak Hadir']) => 'danger',
                        in_array($state, ['Potongan Kasbon', 'Lainnya']) => 'secondary',
                        default => 'gray',
                    }),

                TextColumn::make('deskripsi')
                    ->label('Deskripsi')
                    ->wrap(),

                TextColumn::make('tanggal_kejadian')
                    ->label('Tanggal Kejadian')
                    ->date('d M Y')
                    ->sortable()
                    ->placeholder('-'),

                TextColumn::make('formatted_nominal')
                    ->label('Nominal')
                    ->sortable(query: function ($query, $direction) {
                        return $query->orderBy('nominal', $direction);
                    })
                    ->weight('bold'),

                TextColumn::make('keterangan')
                    ->label('Keterangan')
                    ->limit(50)
                    ->placeholder('-')
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis_potongan')
                    ->label('Jenis Potongan')
                    ->options([
                        'bpjs_kesehatan' => 'BPJS Kesehatan',
                        'bpjs_tk' => 'BPJS Tenaga Kerja',
                        'keterlambatan' => 'Keterlambatan',
                        'pelanggaran' => 'Pelanggaran',
                        'kasir' => 'Potongan Kasir',
                        'stok_opname' => 'Potongan Stok Opname',
                        'retur' => 'Potongan Retur',
                        'kasbon' => 'Potongan Kasbon',
                        'sakit_tanpa_surat' => 'Sakit Tanpa Surat',
                        'alpha' => 'Alpha/Tidak Hadir',
                        'cuti_melebihi_kuota' => 'Cuti Melebihi Kuota',
                        'lainnya' => 'Lainnya',
                    ]),
            ])
            ->headerActions([
                // No create action
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // No bulk actions
            ])
            ->defaultSort('created_at', 'asc')
            ->emptyStateHeading('Tidak Ada Potongan')
            ->emptyStateDescription('Tidak ada detail potongan untuk transaksi payroll ini.')
            ->emptyStateIcon('heroicon-o-banknotes');
    }

    public function isReadOnly(): bool
    {
        return true;
    }
}
