<?php

namespace App\Filament\Resources\PesangonResource\Pages;

use App\Filament\Resources\PesangonResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPesangons extends ListRecords
{
    protected static string $resource = PesangonResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
