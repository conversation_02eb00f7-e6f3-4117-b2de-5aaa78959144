<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PettyCashFundResource\Pages;
use App\Filament\Resources\PettyCashFundResource\RelationManagers;
use App\Models\PettyCashFund;
use App\Models\Entitas;
use App\Models\Karyawan;
use App\Models\Akun;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PettyCashFundResource extends Resource
{
    protected static ?string $model = PettyCashFund::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationLabel = 'Petty Cash Funds';

    protected static ?string $navigationGroup = 'Expense Management';

    protected static ?int $navigationSort = 3;


    // has access superadmin
    public static function canAccess(): bool
    {
        return auth()->user()->hasRole(['super_admin', 'direktur', 'manager_accounting']);
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Fund Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('fund_name')
                                    ->label('Fund Name')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('fund_code')
                                    ->label('Fund Code')
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('entitas_id')
                                    ->label('Entitas')
                                    ->options(Entitas::all()->pluck('nama', 'id'))
                                    ->required()
                                    ->searchable()
                                    ->preload(),
                                Forms\Components\Select::make('custodian_employee_id')
                                    ->label('Custodian Employee')
                                    ->options(Karyawan::all()->pluck('nama_lengkap', 'id'))
                                    ->required()
                                    ->searchable()
                                    ->preload(),
                            ]),
                        Forms\Components\Select::make('cash_account_id')
                            ->label('Cash Account')
                            ->options(Akun::where('kategori_akun', 'Aset')
                                        ->where('nama_akun', 'like', '%kas%')
                                        ->pluck('nama_akun', 'id'))
                            ->required()
                            ->searchable()
                            ->preload(),
                    ]),

                Forms\Components\Section::make('Fund Amounts')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('initial_amount')
                                    ->label('Initial Amount')
                                    ->numeric()
                                    ->required()
                                    ->prefix('Rp')
                                    ->minValue(0),
                                Forms\Components\TextInput::make('current_balance')
                                    ->label('Current Balance')
                                    ->numeric()
                                    ->required()
                                    ->prefix('Rp')
                                    ->minValue(0),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('maximum_amount')
                                    ->label('Maximum Amount')
                                    ->numeric()
                                    ->required()
                                    ->prefix('Rp')
                                    ->minValue(0),
                                Forms\Components\TextInput::make('minimum_balance')
                                    ->label('Minimum Balance')
                                    ->numeric()
                                    ->required()
                                    ->prefix('Rp')
                                    ->default(0)
                                    ->minValue(0),
                            ]),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('fund_code')
                    ->label('Code')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fund_name')
                    ->label('Fund Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('entitas.nama')
                    ->label('Entitas')
                    ->searchable(),
                Tables\Columns\TextColumn::make('custodianEmployee.nama_lengkap')
                    ->label('Custodian')
                    ->searchable(),
                Tables\Columns\TextColumn::make('current_balance')
                    ->label('Current Balance')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('maximum_amount')
                    ->label('Max Amount')
                    ->money('IDR'),
                Tables\Columns\BadgeColumn::make('balance_status')
                    ->label('Status')
                    ->colors([
                        'danger' => 'Low',
                        'warning' => 'High',
                        'success' => 'Normal',
                    ]),
                Tables\Columns\TextColumn::make('balance_percentage')
                    ->label('Usage')
                    ->suffix('%')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->boolean()
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only')
                    ->native(false),
                Tables\Filters\SelectFilter::make('entitas')
                    ->relationship('entitas', 'nama'),
                Tables\Filters\SelectFilter::make('balance_status')
                    ->options([
                        'Low' => 'Low Balance',
                        'Normal' => 'Normal',
                        'High' => 'High Balance',
                    ]),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('replenish')
                    ->label('Replenish')
                    ->icon('heroicon-o-plus')
                    ->color('success')
                    ->visible(fn (PettyCashFund $record) => $record->needsReplenishment())
                    ->form([
                        Forms\Components\TextInput::make('amount')
                            ->label('Replenishment Amount')
                            ->numeric()
                            ->required()
                            ->prefix('Rp')
                            ->default(fn (PettyCashFund $record) => $record->replenishment_amount),
                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->default('Petty cash replenishment')
                            ->rows(2),
                    ])
                    ->action(function (PettyCashFund $record, array $data) {
                        $record->replenishment($data['amount'], $data['description']);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('fund_name');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\PettyCashTransactionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPettyCashFunds::route('/'),
            'create' => Pages\CreatePettyCashFund::route('/create'),
            'view' => Pages\ViewPettyCashFund::route('/{record}'),
            'edit' => Pages\EditPettyCashFund::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
