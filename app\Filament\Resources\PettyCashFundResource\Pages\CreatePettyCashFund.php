<?php

namespace App\Filament\Resources\PettyCashFundResource\Pages;

use App\Filament\Resources\PettyCashFundResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreatePettyCashFund extends CreateRecord
{
    protected static string $resource = PettyCashFundResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = auth()->id();
        
        return $data;
    }
}
