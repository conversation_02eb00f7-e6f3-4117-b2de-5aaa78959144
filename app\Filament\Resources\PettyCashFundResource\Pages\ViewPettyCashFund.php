<?php

namespace App\Filament\Resources\PettyCashFundResource\Pages;

use App\Filament\Resources\PettyCashFundResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Forms;

class ViewPettyCashFund extends ViewRecord
{
    protected static string $resource = PettyCashFundResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('replenish')
                ->label('Replenish Fund')
                ->icon('heroicon-o-plus')
                ->color('success')
                ->visible(fn () => $this->getRecord()->needsReplenishment())
                ->form([
                    Forms\Components\TextInput::make('amount')
                        ->label('Replenishment Amount')
                        ->numeric()
                        ->required()
                        ->prefix('Rp')
                        ->default(fn () => $this->getRecord()->replenishment_amount),
                    Forms\Components\Textarea::make('description')
                        ->label('Description')
                        ->default('Petty cash replenishment')
                        ->rows(2),
                ])
                ->action(function (array $data) {
                    $record = $this->getRecord();
                    $record->replenishment($data['amount'], $data['description']);
                    
                    $this->refreshFormData(['current_balance']);
                }),
            Actions\Action::make('disbursement')
                ->label('Quick Disbursement')
                ->icon('heroicon-o-minus')
                ->color('warning')
                ->visible(fn () => $this->getRecord()->is_active)
                ->form([
                    Forms\Components\TextInput::make('amount')
                        ->label('Disbursement Amount')
                        ->numeric()
                        ->required()
                        ->prefix('Rp')
                        ->maxValue(fn () => $this->getRecord()->current_balance),
                    Forms\Components\TextInput::make('recipient_name')
                        ->label('Recipient Name')
                        ->required(),
                    Forms\Components\Textarea::make('description')
                        ->label('Description')
                        ->required()
                        ->rows(2),
                ])
                ->action(function (array $data) {
                    $record = $this->getRecord();
                    $record->disbursement($data['amount'], $data['description']);
                    
                    $this->refreshFormData(['current_balance']);
                }),
        ];
    }
}
