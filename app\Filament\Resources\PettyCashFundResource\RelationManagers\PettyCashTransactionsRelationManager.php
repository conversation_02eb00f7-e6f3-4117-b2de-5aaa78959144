<?php

namespace App\Filament\Resources\PettyCashFundResource\RelationManagers;

use App\Models\ExpenseCategory;
use App\Models\Akun;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PettyCashTransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'pettyCashTransactions';

    protected static ?string $title = 'Petty Cash Transactions';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('transaction_type')
                    ->label('Transaction Type')
                    ->options([
                        'Disbursement' => 'Disbursement',
                        'Replenishment' => 'Replenishment',
                    ])
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if ($state === 'Replenishment') {
                            $set('expense_category_id', null);
                            $set('recipient_name', null);
                        }
                    }),
                Forms\Components\DatePicker::make('transaction_date')
                    ->label('Transaction Date')
                    ->required()
                    ->default(now()),
                Forms\Components\TextInput::make('amount')
                    ->label('Amount')
                    ->numeric()
                    ->required()
                    ->prefix('Rp')
                    ->minValue(1),
                Forms\Components\TextInput::make('description')
                    ->label('Description')
                    ->required()
                    ->maxLength(255)
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('recipient_name')
                    ->label('Recipient Name')
                    ->maxLength(255)
                    ->visible(fn (callable $get) => $get('transaction_type') === 'Disbursement'),
                Forms\Components\TextInput::make('receipt_number')
                    ->label('Receipt Number')
                    ->maxLength(255),
                Forms\Components\Select::make('expense_category_id')
                    ->label('Expense Category')
                    ->options(ExpenseCategory::active()->get()->pluck('display_name', 'id'))
                    ->searchable()
                    ->preload()
                    ->visible(fn (callable $get) => $get('transaction_type') === 'Disbursement')
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if ($state) {
                            $category = ExpenseCategory::find($state);
                            if ($category) {
                                $set('account_id', $category->default_account_id);
                            }
                        }
                    }),
                Forms\Components\Select::make('account_id')
                    ->label('Account')
                    ->options(Akun::where('kategori_akun', 'Beban')->pluck('nama_akun', 'id'))
                    ->required()
                    ->searchable()
                    ->preload(),
                Forms\Components\Select::make('status')
                    ->label('Status')
                    ->options([
                        'Draft' => 'Draft',
                        'Completed' => 'Completed',
                        'Cancelled' => 'Cancelled',
                    ])
                    ->default('Draft')
                    ->required(),
                Forms\Components\Textarea::make('notes')
                    ->label('Notes')
                    ->rows(2)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('transaction_number')
            ->columns([
                Tables\Columns\TextColumn::make('transaction_number')
                    ->label('Transaction #')
                    ->searchable(),
                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('Date')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('transaction_type')
                    ->label('Type')
                    ->colors([
                        'danger' => 'Disbursement',
                        'success' => 'Replenishment',
                    ]),
                Tables\Columns\TextColumn::make('amount_with_sign')
                    ->label('Amount')
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->label('Description')
                    ->limit(30),
                Tables\Columns\TextColumn::make('recipient_name')
                    ->label('Recipient')
                    ->limit(20),
                Tables\Columns\TextColumn::make('category_name')
                    ->label('Category'),
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status')
                    ->colors([
                        'gray' => 'Draft',
                        'success' => 'Completed',
                        'danger' => 'Cancelled',
                    ]),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('transaction_type')
                    ->options([
                        'Disbursement' => 'Disbursement',
                        'Replenishment' => 'Replenishment',
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'Draft' => 'Draft',
                        'Completed' => 'Completed',
                        'Cancelled' => 'Cancelled',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->visible(fn () => $this->getOwnerRecord()->is_active),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => $record->isEditable()),
                Tables\Actions\Action::make('complete')
                    ->label('Complete')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn ($record) => $record->canBeCompleted())
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $record->complete();
                    }),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => $record->isEditable()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('transaction_date', 'desc');
    }
}
