<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PostingRuleResource\Pages;
use App\Filament\Resources\PostingRuleResource\RelationManagers;
use App\Models\PostingRule;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PostingRuleResource extends Resource
{
    protected static ?string $model = PostingRule::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationLabel = 'Aturan Posting';

    protected static ?string $modelLabel = 'Aturan Posting';

    protected static ?string $pluralModelLabel = 'Aturan Posting';

    protected static ?string $navigationGroup = 'Akuntansi';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Dasar')
                    ->schema([
                        Forms\Components\TextInput::make('rule_name')
                            ->label('Nama Aturan')
                            ->required()
                            ->maxLength(255)
                            ->columnSpan(2),
                        Forms\Components\Select::make('source_type')
                            ->label('Tipe Sumber')
                            ->required()
                            ->options([
                                'Sale' => 'Penjualan',
                                'Purchase' => 'Pembelian',
                                'Payment' => 'Pembayaran',
                                'Receipt' => 'Penerimaan',
                                'ManualAdjust' => 'Penyesuaian Manual',
                                'Expense' => 'Beban',
                                'Revenue' => 'Pendapatan',
                            ])
                            ->native(false),
                        Forms\Components\TextInput::make('priority')
                            ->label('Prioritas')
                            ->required()
                            ->numeric()
                            ->default(0)
                            ->helperText('Semakin kecil angka, semakin tinggi prioritas'),
                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Kondisi Pemicu')
                    ->schema([
                        Forms\Components\KeyValue::make('trigger_condition')
                            ->label('Kondisi Pemicu')
                            ->keyLabel('Field')
                            ->valueLabel('Nilai')
                            ->helperText('Contoh: payment_method = Cash, total_amount > 1000000')
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Detail Entri Jurnal')
                    ->schema([
                        Forms\Components\Repeater::make('postingRuleEntries')
                            ->relationship()
                            ->schema([
                                Forms\Components\Select::make('account_id')
                                    ->label('Akun')
                                    ->relationship('account', 'nama_akun')
                                    ->required()
                                    ->searchable()
                                    ->preload()
                                    ->columnSpan(2),
                                Forms\Components\Select::make('dc_type')
                                    ->label('Tipe')
                                    ->required()
                                    ->options([
                                        'Debit' => 'Debit',
                                        'Credit' => 'Credit',
                                    ])
                                    ->native(false),
                                Forms\Components\Select::make('amount_type')
                                    ->label('Tipe Jumlah')
                                    ->required()
                                    ->options([
                                        'Fixed' => 'Jumlah Tetap',
                                        'SourceValue' => 'Nilai dari Source',
                                        'Calculated' => 'Perhitungan',
                                    ])
                                    ->native(false)
                                    ->reactive(),
                                Forms\Components\TextInput::make('fixed_amount')
                                    ->label('Jumlah Tetap')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->visible(fn($get) => $get('amount_type') === 'Fixed'),
                                Forms\Components\TextInput::make('source_property')
                                    ->label('Property Source')
                                    ->helperText('Contoh: total_amount, subtotal, tax_amount')
                                    ->visible(fn($get) => $get('amount_type') === 'SourceValue'),
                                Forms\Components\Textarea::make('calculation_expression')
                                    ->label('Ekspresi Perhitungan')
                                    ->helperText('Contoh: source.total_amount * 0.11')
                                    ->visible(fn($get) => $get('amount_type') === 'Calculated')
                                    ->columnSpan(2),
                                Forms\Components\TextInput::make('description_template')
                                    ->label('Template Deskripsi')
                                    ->helperText('Gunakan {source.property} untuk placeholder')
                                    ->columnSpan(2),
                                Forms\Components\TextInput::make('sort_order')
                                    ->label('Urutan')
                                    ->numeric()
                                    ->default(0),
                            ])
                            ->columns(3)
                            ->defaultItems(2)
                            ->addActionLabel('Tambah Entri Jurnal')
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Status')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Aktif')
                            ->default(true),
                        Forms\Components\Hidden::make('created_by')
                            ->default(auth()->id()),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('rule_name')
                    ->label('Nama Aturan')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('source_type')
                    ->label('Tipe Sumber')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Sale' => 'success',
                        'Purchase' => 'info',
                        'Payment' => 'warning',
                        'Receipt' => 'primary',
                        default => 'gray',
                    })
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('priority')
                    ->label('Prioritas')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('postingRuleEntries_count')
                    ->label('Jumlah Entri')
                    ->counts('postingRuleEntries')
                    ->badge()
                    ->color('info'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('source_type')
                    ->label('Tipe Sumber')
                    ->options([
                        'Sale' => 'Penjualan',
                        'Purchase' => 'Pembelian',
                        'Payment' => 'Pembayaran',
                        'Receipt' => 'Penerimaan',
                        'ManualAdjust' => 'Penyesuaian Manual',
                        'Expense' => 'Beban',
                        'Revenue' => 'Pendapatan',
                    ]),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status')
                    ->placeholder('Semua')
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPostingRules::route('/'),
            'create' => Pages\CreatePostingRule::route('/create'),
            'edit' => Pages\EditPostingRule::route('/{record}/edit'),
        ];
    }
}
