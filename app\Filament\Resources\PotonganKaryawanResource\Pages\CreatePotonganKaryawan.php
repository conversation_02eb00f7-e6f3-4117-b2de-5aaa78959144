<?php

namespace App\Filament\Resources\PotonganKaryawanResource\Pages;

use App\Filament\Resources\PotonganKaryawanResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreatePotonganKaryawan extends CreateRecord
{
    protected static string $resource = PotonganKaryawanResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = auth()->id();
        return $data;
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Potongan karyawan berhasil ditambahkan')
            ->body('Data potongan karyawan telah disimpan.');
    }

    protected function handleRecordCreation(array $data): \Illuminate\Database\Eloquent\Model
    {
        // Validasi unik untuk kombinasi karyawan_id, jenis_potongan, dan bulan_potongan
        $existing = \App\Models\PotonganKaryawan::where('karyawan_id', $data['karyawan_id'])
            ->where('jenis_potongan', $data['jenis_potongan'])
            ->where('bulan_potongan', $data['bulan_potongan'])
            ->first();

        if ($existing) {
            Notification::make()
                ->danger()
                ->title('Potongan sudah ada')
                ->body('Potongan dengan jenis yang sama untuk karyawan dan bulan ini sudah ada.')
                ->persistent()
                ->send();

            $this->halt();
        }

        return static::getModel()::create($data);
    }
}
