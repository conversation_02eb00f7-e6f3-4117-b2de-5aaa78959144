<?php

namespace App\Filament\Resources\PotonganKaryawanResource\Pages;

use App\Filament\Resources\PotonganKaryawanResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditPotonganKaryawan extends EditRecord
{
    protected static string $resource = PotonganKaryawanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Potongan karyawan berhasil diperbarui')
            ->body('Data potongan karyawan telah diperbarui.');
    }

    protected function handleRecordUpdate(\Illuminate\Database\Eloquent\Model $record, array $data): \Illuminate\Database\Eloquent\Model
    {
        // Validasi unik untuk kombinasi karyawan_id, jenis_potongan, dan bulan_potongan (kecuali record saat ini)
        $existing = \App\Models\PotonganKaryawan::where('karyawan_id', $data['karyawan_id'])
            ->where('jenis_potongan', $data['jenis_potongan'])
            ->where('bulan_potongan', $data['bulan_potongan'])
            ->where('id', '!=', $record->id)
            ->first();

        if ($existing) {
            Notification::make()
                ->danger()
                ->title('Potongan sudah ada')
                ->body('Potongan dengan jenis yang sama untuk karyawan dan bulan ini sudah ada.')
                ->persistent()
                ->send();

            $this->halt();
        }

        $record->update($data);
        return $record;
    }
}
