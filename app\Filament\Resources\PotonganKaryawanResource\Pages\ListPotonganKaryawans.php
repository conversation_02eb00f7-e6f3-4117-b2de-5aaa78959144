<?php

namespace App\Filament\Resources\PotonganKaryawanResource\Pages;

use App\Filament\Resources\PotonganKaryawanResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPotonganKaryawans extends ListRecords
{
    protected static string $resource = PotonganKaryawanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
