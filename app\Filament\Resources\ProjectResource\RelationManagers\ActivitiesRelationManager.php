<?php

namespace App\Filament\Resources\ProjectResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class ActivitiesRelationManager extends RelationManager
{
    protected static string $relationship = 'activities';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('description')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('description')
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Pengguna')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('activity_type')
                    ->label('Jenis Aktivitas')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'task_created' => 'success',
                        'task_updated' => 'warning',
                        'task_completed' => 'info',
                        'task_deleted' => 'danger',
                        'comment_added' => 'gray',
                        'comment_replied' => 'gray',
                        'comment_deleted' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'task_created' => 'Task Dibuat',
                        'task_updated' => 'Task Diupdate',
                        'task_completed' => 'Task Selesai',
                        'task_deleted' => 'Task Dihapus',
                        'comment_added' => 'Komentar Ditambah',
                        'comment_replied' => 'Komentar Dibalas',
                        'comment_deleted' => 'Komentar Dihapus',
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('subject_type')
                    ->label('Objek')
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'App\\Models\\Task' => 'Task',
                        'App\\Models\\TaskComment' => 'Komentar',
                        default => class_basename($state),
                    }),
                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(80)
                    ->searchable()
                    ->wrap(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Waktu')
                    ->dateTime()
                    ->sortable()
                    ->since(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('activity_type')
                    ->label('Jenis Aktivitas')
                    ->options([
                        'task_created' => 'Task Dibuat',
                        'task_updated' => 'Task Diupdate',
                        'task_completed' => 'Task Selesai',
                        'task_deleted' => 'Task Dihapus',
                        'comment_added' => 'Komentar Ditambah',
                        'comment_replied' => 'Komentar Dibalas',
                        'comment_deleted' => 'Komentar Dihapus',
                    ]),
                Tables\Filters\SelectFilter::make('subject_type')
                    ->label('Objek')
                    ->options([
                        'App\\Models\\Task' => 'Task',
                        'App\\Models\\TaskComment' => 'Komentar',
                    ]),
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('Pengguna')
                    ->relationship('user', 'name'),
            ])
            ->headerActions([
                // No create action - activities are auto-generated
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalContent(function ($record) {
                        $properties = $record->properties ?? [];
                        $content = "**Deskripsi:** {$record->description}\n\n";

                        if (!empty($properties)) {
                            $content .= "**Detail Perubahan:**\n";
                            foreach ($properties as $key => $value) {
                                $content .= "- **{$key}:** {$value}\n";
                            }
                        }

                        return view('filament.components.activity-detail', [
                            'content' => $content,
                            'record' => $record
                        ]);
                    }),
            ])
            ->bulkActions([
                // No bulk actions for activities
            ])
            ->defaultSort('created_at', 'desc')
            ->poll('30s'); // Auto-refresh every 30 seconds
    }
}
