<?php

namespace App\Filament\Resources\ProjectResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MembersRelationManager extends RelationManager
{
    protected static string $relationship = 'members';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('Karyawan')
                    ->options(function () {
                        return \App\Models\User::whereHas('karyawan', function ($query) {
                            $query->where('status_aktif', true);
                        })
                            ->with(['karyawan.jabatan'])
                            ->get()
                            ->mapWithKeys(function ($user) {
                                $karyawan = $user->karyawan;
                                if ($karyawan) {
                                    $jabatan = $karyawan->jabatan ? " - {$karyawan->jabatan->nama_jabatan}" : "";
                                    return [$user->id => "{$karyawan->nama_lengkap}{$jabatan}"];
                                }
                                return [$user->id => $user->name];
                            });
                    })
                    ->required()
                    ->searchable()
                    ->preload(),
                Forms\Components\Select::make('role')
                    ->label('Role')
                    ->options([
                        'project_manager' => 'Project Manager',
                        'tech_lead' => 'Tech Lead',
                        'senior_developer' => 'Senior Developer',
                        'developer' => 'Developer',
                        'junior_developer' => 'Junior Developer',
                        'designer' => 'Designer',
                        'qa_tester' => 'QA Tester',
                        'business_analyst' => 'Business Analyst',
                    ])
                    ->required()
                    ->default('developer'),
                Forms\Components\TextInput::make('hourly_rate')
                    ->label('Tarif per Jam')
                    ->numeric()
                    ->prefix('Rp')
                    ->default(50000),
                Forms\Components\TextInput::make('capacity_hours_per_week')
                    ->label('Kapasitas Jam per Minggu')
                    ->numeric()
                    ->default(40)
                    ->suffix('jam'),
                Forms\Components\DateTimePicker::make('joined_at')
                    ->label('Tanggal Bergabung')
                    ->default(now())
                    ->native(false),
                Forms\Components\Toggle::make('is_active')
                    ->label('Aktif')
                    ->default(true),
            ])
            ->columns(2);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('nama_lengkap')
            ->modifyQueryUsing(fn(Builder $query) => $query->with(['karyawan.jabatan']))
            ->columns([
                Tables\Columns\TextColumn::make('karyawan.nama_lengkap')
                    ->label('Nama Karyawan')
                    ->searchable()
                    ->default('N/A'),
                Tables\Columns\TextColumn::make('email')
                    ->label('Email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('karyawan.jabatan.nama_jabatan')
                    ->label('Posisi')
                    ->searchable()
                    ->default('N/A'),

                Tables\Columns\IconColumn::make('pivot.is_active')
                    ->label('Aktif')
                    ->boolean(),

            ])
            ->filters([
                Tables\Filters\SelectFilter::make('pivot.role')
                    ->label('Role')
                    ->options([
                        'project_manager' => 'Project Manager',
                        'tech_lead' => 'Tech Lead',
                        'senior_developer' => 'Senior Developer',
                        'developer' => 'Developer',
                        'junior_developer' => 'Junior Developer',
                        'designer' => 'Designer',
                        'qa_tester' => 'QA Tester',
                        'business_analyst' => 'Business Analyst',
                    ]),
                Tables\Filters\TernaryFilter::make('pivot.is_active')
                    ->label('Status Aktif'),
            ])
            ->headerActions([
                Tables\Actions\Action::make('add_member')
                    ->label('Tambah Member')
                    ->icon('heroicon-o-plus')
                    ->form([
                        Forms\Components\Select::make('user_id')
                            ->label('Karyawan')
                            ->options(function () {
                                return \App\Models\User::whereHas('karyawan', function ($query) {
                                    $query->where('status_aktif', true);
                                })
                                    ->with(['karyawan.jabatan'])
                                    ->get()
                                    ->mapWithKeys(function ($user) {
                                        $karyawan = $user->karyawan;
                                        if ($karyawan) {
                                            $jabatan = $karyawan->jabatan ? " - {$karyawan->jabatan->nama_jabatan}" : "";
                                            return [$user->id => "{$karyawan->nama_lengkap}{$jabatan}"];
                                        }
                                        return [$user->id => $user->name];
                                    });
                            })
                            ->required()
                            ->searchable()
                            ->preload(),



                        // Forms\Components\Select::make('role')
                        //     ->label('Role')
                        //     ->options([
                        //         'project_manager' => 'Project Manager',
                        //         'tech_lead' => 'Tech Lead',
                        //         'senior_developer' => 'Senior Developer',
                        //         'developer' => 'Developer',
                        //         'junior_developer' => 'Junior Developer',
                        //         'designer' => 'Designer',
                        //         'qa_tester' => 'QA Tester',
                        //         'business_analyst' => 'Business Analyst',
                        //     ])
                        //     ->required()
                        //     ->default('developer'),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Aktif')
                            ->default(true),
                    ])
                    ->action(function (array $data) {
                        $project = $this->getOwnerRecord();

                        // Check if employee is already a member
                        if ($project->members()->where('user_id', $data['user_id'])->exists()) {
                            \Filament\Notifications\Notification::make()
                                ->title('Error')
                                ->body('Karyawan sudah menjadi member proyek ini.')
                                ->danger()
                                ->send();
                            return;
                        }

                        // Attach employee to project
                        $project->members()->attach($data['user_id'], [

                            'is_active' => $data['is_active'],
                            'added_by' => auth()->id() ?? 1,
                        ]);

                        \Filament\Notifications\Notification::make()
                            ->title('Berhasil')
                            ->body('Member berhasil ditambahkan ke proyek.')
                            ->success()
                            ->send();
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('edit_member')
                    ->label('Edit')
                    ->icon('heroicon-o-pencil')
                    ->form([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Aktif'),
                    ])
                    ->fillForm(function ($record) {
                        return [
                            'is_active' => $record->pivot->is_active,
                        ];
                    })
                    ->action(function (array $data, $record) {
                        $project = $this->getOwnerRecord();

                        $project->members()->updateExistingPivot($record->id, [
                            'is_active' => $data['is_active'],
                        ]);

                        \Filament\Notifications\Notification::make()
                            ->title('Berhasil')
                            ->body('Data member berhasil diupdate.')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\Action::make('remove_member')
                    ->label('Hapus')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $project = $this->getOwnerRecord();
                        $project->members()->detach($record->id);

                        \Filament\Notifications\Notification::make()
                            ->title('Berhasil')
                            ->body('Member berhasil dihapus dari proyek.')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('remove_members')
                        ->label('Hapus Members')
                        ->icon('heroicon-o-trash')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->action(function ($records) {
                            $project = $this->getOwnerRecord();
                            $employeeIds = $records->pluck('id')->toArray();
                            $project->members()->detach($employeeIds);

                            \Filament\Notifications\Notification::make()
                                ->title('Berhasil')
                                ->body(count($employeeIds) . ' member berhasil dihapus dari proyek.')
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }
}
