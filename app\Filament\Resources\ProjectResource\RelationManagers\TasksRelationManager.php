<?php

namespace App\Filament\Resources\ProjectResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TasksRelationManager extends RelationManager
{
    protected static string $relationship = 'tasks';

    protected static ?string $title = 'Tugas';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Nama Tugas')
                    ->required()
                    ->maxLength(255)
                    ->columnSpanFull(),
                Forms\Components\RichEditor::make('description')
                    ->label('Deskripsi')
                    ->fileAttachmentsDirectory('images/attachments')
                    ->columnSpanFull(),
                Forms\Components\Select::make('assigned_to')
                    ->label('Ditugaskan Kepada')
                    ->options(function () {
                        $project = $this->getOwnerRecord();
                        return $project->members()
                            ->wherePivot('is_active', true)
                            ->with('karyawan')
                            ->get()
                            ->mapWithKeys(function ($member) {
                                $name = $member->karyawan ? $member->karyawan->nama_lengkap : $member->name;
                                return [$member->id => $name];
                            })
                            ->toArray();
                    })
                    ->searchable()
                    ->preload(),
                Forms\Components\DatePicker::make('start_date')
                    ->label('Tanggal Mulai')
                    ->after('today'),
                Forms\Components\DatePicker::make('due_date')
                    ->label('Tanggal Deadline')
                    ->after('start_date'),
                Forms\Components\Select::make('status')
                    ->label('Status')
                    ->options([
                        'todo' => 'To Do',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                    ])
                    ->required()
                    ->default('todo')
                    ->native(false),
            ])
            ->columns(2);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nama Task')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('assignedUser.name')
                    ->label('Ditugaskan Kepada')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_date')
                    ->label('Tanggal Mulai')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('due_date')
                    ->label('Deadline')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'todo' => 'gray',
                        'in_progress' => 'warning',
                        'completed' => 'success',
                        default => 'gray',
                    })
                    ->icon(fn(string $state): string => match ($state) {
                        'todo' => 'heroicon-o-clock',
                        'in_progress' => 'heroicon-o-play',
                        'completed' => 'heroicon-o-check-circle',
                        default => 'heroicon-o-question-mark-circle',
                    }),
                Tables\Columns\TextColumn::make('progress_percentage')
                    ->label('Progress')
                    ->suffix('%')
                    ->badge()
                    ->color(fn(int $state): string => match (true) {
                        $state >= 80 => 'success',
                        $state >= 50 => 'warning',
                        default => 'danger',
                    }),
                Tables\Columns\TextColumn::make('total_hours')
                    ->label('Total Jam')
                    ->suffix(' jam')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'todo' => 'To Do',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                    ]),
                Tables\Filters\SelectFilter::make('assigned_to')
                    ->label('Ditugaskan Kepada')
                    ->options(function () {
                        $project = $this->getOwnerRecord();
                        return $project->members()
                            ->wherePivot('is_active', true)
                            ->with('karyawan')
                            ->get()
                            ->mapWithKeys(function ($member) {
                                $name = $member->karyawan ? $member->karyawan->nama_lengkap : $member->name;
                                return [$member->id => $name];
                            })
                            ->toArray();
                    }),
            ])
            ->headerActions([
                Tables\Actions\Action::make('tambah_task')
                    ->label('Tambah Tugas')
                    ->icon('heroicon-o-plus')
                    ->form([
                        Forms\Components\TextInput::make('name')
                            ->label('Nama Task')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),
                        Forms\Components\RichEditor::make('description')
                            ->label('Deskripsi')
                            ->required()
                            ->fileAttachmentsDirectory('images/attachments')
                            ->columnSpanFull(),
                        Forms\Components\Select::make('assigned_to')
                            ->label('Ditugaskan Kepada')
                            ->options(function () {
                                $project = $this->getOwnerRecord();
                                return $project->members()
                                    ->wherePivot('is_active', true)
                                    ->with('karyawan')
                                    ->get()
                                    ->mapWithKeys(function ($member) {
                                        $name = $member->karyawan ? $member->karyawan->nama_lengkap : $member->name;
                                        return [$member->id => $name];
                                    })
                                    ->toArray();
                            })
                            ->searchable()
                            ->preload(),
                        Forms\Components\DatePicker::make('start_date')
                            ->label('Tanggal Mulai')
                            ->required()
                            ->default(now()),
                        Forms\Components\DatePicker::make('due_date')
                            ->label('Tanggal Deadline')
                            ->required()
                            ->default(now()->addDays(7))
                            ->after('start_date'),
                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'todo' => 'To Do',
                                'in_progress' => 'In Progress',
                                'completed' => 'Completed',
                            ])
                            ->required()
                            ->default('todo')
                            ->native(false),
                    ])
                    ->action(function (array $data) {
                        $data['created_by'] = auth()->id() ?? 1;
                        $record = $this->ownerRecord->tasks()->create($data);

                        // Log activity
                        $userName = auth()->user()?->name ?? 'System';
                        \App\Models\ProjectActivity::log(
                            'task_created',
                            $record,
                            $userName . ' created task: ' . $record->name
                        );
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('view_details')
                    ->label('Detail')
                    ->icon('heroicon-o-eye')
                    ->url(fn($record): string => url("/admin/tasks/{$record->id}")),

                // Hanya gunakan satu action edit
                Tables\Actions\Action::make('edit')
                    ->label('Edit')
                    ->icon('heroicon-o-pencil')
                    ->modalHeading('Edit Task')
                    ->mountUsing(fn(Forms\Form $form, $record) => $form->fill($record->toArray()))
                    ->form([
                        Forms\Components\TextInput::make('name')
                            ->label('Nama Task')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),
                        Forms\Components\RichEditor::make('description')
                            ->label('Deskripsi')
                            ->required()
                            ->fileAttachmentsDirectory('images/attachments')
                            ->columnSpanFull(),
                        Forms\Components\Select::make('assigned_to')
                            ->label('Ditugaskan Kepada')
                            ->options(function () {
                                $project = $this->getOwnerRecord();
                                return $project->members()
                                    ->wherePivot('is_active', true)
                                    ->with('karyawan')
                                    ->get()
                                    ->mapWithKeys(function ($member) {
                                        $name = $member->karyawan ? $member->karyawan->nama_lengkap : $member->name;
                                        return [$member->id => $name];
                                    })
                                    ->toArray();
                            })
                            ->searchable()
                            ->preload(),
                        Forms\Components\DatePicker::make('start_date')
                            ->label('Tanggal Mulai')
                            ->required(),
                        Forms\Components\DatePicker::make('due_date')
                            ->label('Tanggal Deadline')
                            ->required()
                            ->after('start_date'),
                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'todo' => 'To Do',
                                'in_progress' => 'In Progress',
                                'completed' => 'Completed',
                            ])
                            ->required()
                            ->native(false),
                    ])
                    ->action(function (array $data, $record) {
                        // Simpan status lama untuk pengecekan perubahan
                        $oldStatus = $record->status;

                        // Update record
                        $record->update($data);

                        // Log activity untuk perubahan status
                        if (isset($data['status']) && $oldStatus !== $data['status']) {
                            $userName = auth()->user()?->name ?? 'System';
                            \App\Models\ProjectActivity::log(
                                'task_updated',
                                $record,
                                $userName . " changed task status from {$oldStatus} to {$data['status']}",
                                [
                                    'old_status' => $oldStatus,
                                    'new_status' => $data['status'],
                                ]
                            );
                        }
                    }),

                Tables\Actions\DeleteAction::make()
                    ->after(function ($record) {
                        $userName = auth()->user()?->name ?? 'System';
                        \App\Models\ProjectActivity::log(
                            'task_deleted',
                            $record,
                            $userName . ' deleted task: ' . $record->name
                        );
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
