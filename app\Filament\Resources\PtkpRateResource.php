<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PtkpRateResource\Pages;
use App\Models\PtkpRate;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PtkpRateResource extends Resource
{
    protected static ?string $model = PtkpRate::class;

    protected static ?string $navigationIcon = 'heroicon-o-identification';

    protected static ?string $navigationLabel = 'PTKP Rates';

    protected static ?string $navigationGroup = 'Enhanced Payroll';

    protected static ?int $navigationSort = 3;

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole(['super_admin']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('PTKP Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('status_code')
                                    ->label('Status Code')
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true)
                                    ->placeholder('TK/0, K/1, K/I/2, etc.'),
                                Forms\Components\TextInput::make('status_description')
                                    ->label('Status Description')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('Tidak Kawin, Tanpa Tanggungan'),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('annual_amount')
                                    ->label('Annual Amount')
                                    ->numeric()
                                    ->required()
                                    ->prefix('Rp')
                                    ->minValue(0),
                                Forms\Components\TextInput::make('monthly_amount')
                                    ->label('Monthly Amount')
                                    ->numeric()
                                    ->required()
                                    ->prefix('Rp')
                                    ->minValue(0),
                            ]),
                    ]),

                Forms\Components\Section::make('Effective Period')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('effective_date')
                                    ->label('Effective Date')
                                    ->required()
                                    ->default(now()),
                                Forms\Components\DatePicker::make('end_date')
                                    ->label('End Date')
                                    ->placeholder('Leave empty for ongoing')
                                    ->after('effective_date'),
                            ]),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('status_code')
                    ->label('Status Code')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status_description')
                    ->label('Description')
                    ->searchable()
                    ->wrap(),
                Tables\Columns\BadgeColumn::make('status_category')
                    ->label('Category')
                    ->colors([
                        'info' => 'Tidak Kawin',
                        'success' => 'Kawin',
                        'warning' => 'Kawin (Istri Digabung)',
                    ]),
                Tables\Columns\TextColumn::make('dependents_count')
                    ->label('Dependents')
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('annual_amount')
                    ->label('Annual Amount')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('monthly_amount')
                    ->label('Monthly Amount')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('effective_date')
                    ->label('Effective Date')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_date')
                    ->label('End Date')
                    ->date('d/m/Y')
                    ->placeholder('Ongoing'),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status_category')
                    ->options([
                        'Tidak Kawin' => 'Tidak Kawin',
                        'Kawin' => 'Kawin',
                        'Kawin (Istri Digabung)' => 'Kawin (Istri Digabung)',
                    ]),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->boolean()
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only')
                    ->native(false),
                Tables\Filters\Filter::make('effective_now')
                    ->label('Effective Now')
                    ->query(fn (Builder $query): Builder =>
                        $query->where('effective_date', '<=', now())
                              ->where(function ($q) {
                                  $q->whereNull('end_date')
                                    ->orWhere('end_date', '>=', now());
                              })
                    ),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('status_code');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPtkpRates::route('/'),
            'create' => Pages\CreatePtkpRate::route('/create'),
            'view' => Pages\ViewPtkpRate::route('/{record}'),
            'edit' => Pages\EditPtkpRate::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
