<?php

namespace App\Filament\Resources\PtkpRateResource\Pages;

use App\Filament\Resources\PtkpRateResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreatePtkpRate extends CreateRecord
{
    protected static string $resource = PtkpRateResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = auth()->id();
        
        return $data;
    }
}
