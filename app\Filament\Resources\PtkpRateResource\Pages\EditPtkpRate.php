<?php

namespace App\Filament\Resources\PtkpRateResource\Pages;

use App\Filament\Resources\PtkpRateResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPtkpRate extends EditRecord
{
    protected static string $resource = PtkpRateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
