<?php

namespace App\Filament\Resources\PtkpRateResource\Pages;

use App\Filament\Resources\PtkpRateResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPtkpRates extends ListRecords
{
    protected static string $resource = PtkpRateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
