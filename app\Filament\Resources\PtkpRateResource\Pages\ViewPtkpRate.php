<?php

namespace App\Filament\Resources\PtkpRateResource\Pages;

use App\Filament\Resources\PtkpRateResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewPtkpRate extends ViewRecord
{
    protected static string $resource = PtkpRateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
