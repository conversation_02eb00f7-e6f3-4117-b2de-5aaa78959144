<?php

namespace App\Filament\Resources\PurchaseOrderResource\Pages;

use App\Filament\Resources\PurchaseOrderResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewPurchaseOrder extends ViewRecord
{
    protected static string $resource = PurchaseOrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn () => $this->getRecord()->isEditable()),
            Actions\Action::make('approve')
                ->label('Approve')
                ->icon('heroicon-o-check')
                ->color('success')
                ->visible(fn () => $this->getRecord()->canBeApproved())
                ->requiresConfirmation()
                ->action(function () {
                    $record = $this->getRecord();
                    $record->status = 'Approved';
                    $record->approved_by = auth()->id();
                    $record->approved_at = now();
                    $record->save();
                    
                    $this->refreshFormData(['status']);
                }),
            Actions\Action::make('cancel')
                ->label('Cancel')
                ->icon('heroicon-o-x-mark')
                ->color('danger')
                ->visible(fn () => $this->getRecord()->canBeCancelled())
                ->requiresConfirmation()
                ->action(function () {
                    $record = $this->getRecord();
                    $record->status = 'Cancelled';
                    $record->save();
                    
                    $this->refreshFormData(['status']);
                }),
        ];
    }
}
