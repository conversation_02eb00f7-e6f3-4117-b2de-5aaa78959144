<?php

namespace App\Filament\Resources\PurchaseOrderResource\RelationManagers;

use App\Models\Produk;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PurchaseOrderItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'purchaseOrderItems';

    protected static ?string $title = 'Items Purchase Order';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('product_id')
                    ->label('Produk')
                    ->options(Produk::all()->pluck('nama', 'id'))
                    ->required()
                    ->searchable()
                    ->preload()
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if ($state) {
                            $product = Produk::find($state);
                            if ($product) {
                                $set('unit_price', $product->unit_cost ?? 0);
                            }
                        }
                    }),
                Forms\Components\TextInput::make('quantity_ordered')
                    ->label('Jumlah Dipesan')
                    ->numeric()
                    ->required()
                    ->minValue(1)
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $get, callable $set) {
                        $unitPrice = $get('unit_price') ?? 0;
                        $set('total_price', $state * $unitPrice);
                    }),
                Forms\Components\TextInput::make('unit_price')
                    ->label('Harga Satuan')
                    ->numeric()
                    ->required()
                    ->prefix('Rp')
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $get, callable $set) {
                        $quantity = $get('quantity_ordered') ?? 0;
                        $set('total_price', $quantity * $state);
                    }),
                Forms\Components\TextInput::make('total_price')
                    ->label('Total Harga')
                    ->numeric()
                    ->prefix('Rp')
                    ->disabled()
                    ->dehydrated(false),
                Forms\Components\Textarea::make('notes')
                    ->label('Catatan')
                    ->rows(2)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('product.nama')
            ->columns([
                Tables\Columns\TextColumn::make('product.kode')
                    ->label('Kode Produk')
                    ->searchable(),
                Tables\Columns\TextColumn::make('product.nama')
                    ->label('Nama Produk')
                    ->searchable()
                    ->wrap(),
                Tables\Columns\TextColumn::make('quantity_ordered')
                    ->label('Qty Dipesan')
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('quantity_received')
                    ->label('Qty Diterima')
                    ->alignCenter()
                    ->color(fn ($record) => $record->quantity_received > 0 ? 'success' : 'gray'),
                Tables\Columns\TextColumn::make('remaining_quantity')
                    ->label('Sisa')
                    ->alignCenter()
                    ->color(fn ($record) => $record->remaining_quantity > 0 ? 'warning' : 'success'),
                Tables\Columns\TextColumn::make('unit_price')
                    ->label('Harga Satuan')
                    ->money('IDR'),
                Tables\Columns\TextColumn::make('total_price')
                    ->label('Total')
                    ->money('IDR'),
                Tables\Columns\BadgeColumn::make('receipt_status')
                    ->label('Status Penerimaan')
                    ->colors([
                        'gray' => 'Not Received',
                        'warning' => 'Partially Received',
                        'success' => 'Fully Received',
                    ]),
                Tables\Columns\TextColumn::make('receipt_progress')
                    ->label('Progress')
                    ->suffix('%'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->visible(fn () => $this->getOwnerRecord()->isEditable()),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->visible(fn () => $this->getOwnerRecord()->isEditable()),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn () => $this->getOwnerRecord()->isEditable()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn () => $this->getOwnerRecord()->isEditable()),
                ]),
            ]);
    }
}
