<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SalesTransactionResource\Pages;
use App\Filament\Resources\SalesTransactionResource\RelationManagers;
use App\Models\SalesTransaction;
use App\Jobs\ProcessSalesImport;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SalesTransactionResource extends Resource
{
    protected static ?string $model = SalesTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-cart';

    protected static ?string $navigationLabel = 'Transaksi Penjualan';

    protected static ?string $modelLabel = 'Transaksi Penjualan';

    protected static ?string $pluralModelLabel = 'Transaksi Penjualan';

    protected static ?string $navigationGroup = 'Penjualan';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Transaksi')
                    ->schema([
                        Forms\Components\Select::make('entitas_id')
                            ->label('Entitas')
                            ->relationship('entitas', 'nama')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->helperText('Pilih entitas untuk transaksi ini')
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('transaction_code')
                            ->label('Kode Transaksi')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\DatePicker::make('transaction_date')
                            ->label('Tanggal Transaksi')
                            ->required()
                            ->default(now()),
                        Forms\Components\TextInput::make('customer_name')
                            ->label('Nama Customer')
                            ->maxLength(255),
                        Forms\Components\Select::make('payment_method')
                            ->label('Metode Pembayaran')
                            ->options([
                                'Cash' => 'Tunai',
                                'Credit' => 'Kredit',
                                'Transfer' => 'Transfer',
                                'Debit' => 'Debit',
                            ])
                            ->default('Cash')
                            ->native(false),
                        Forms\Components\TextInput::make('subtotal')
                            ->label('Subtotal')
                            ->numeric()
                            ->prefix('Rp')
                            ->disabled()
                            ->afterStateHydrated(function ($component, $state, $record) {
                                if ($record) {
                                    $subtotal = $record->saleItems()->sum('total_price');
                                    $component->state($subtotal);
                                }
                            }),
                        Forms\Components\TextInput::make('tax_amount')
                            ->label('Pajak')
                            ->numeric()
                            ->prefix('Rp')
                            ->default(0),
                        Forms\Components\TextInput::make('discount_amount')
                            ->label('Diskon')
                            ->numeric()
                            ->prefix('Rp')
                            ->default(0),
                        Forms\Components\TextInput::make('total_amount')
                            ->label('Total')
                            ->numeric()
                            ->prefix('Rp')
                            ->disabled()
                            ->afterStateHydrated(function ($component, $state, $record) {
                                if ($record) {
                                    $subtotal = $record->saleItems()->sum('total_price');
                                    $taxAmount = $record->tax_amount ?: 0;
                                    $discountAmount = $record->discount_amount ?: 0;
                                    $total = $subtotal + $taxAmount - $discountAmount;
                                    $component->state($total);
                                }
                            }),
                        Forms\Components\Textarea::make('notes')
                            ->label('Catatan')
                            ->columnSpanFull(),
                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'Draft' => 'Draft',
                                'Completed' => 'Selesai',
                                'Cancelled' => 'Dibatalkan',
                            ])
                            ->default('Completed')
                            ->native(false),
                    ]),


            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('transaction_code')
                    ->label('Kode Transaksi')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('Tanggal')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('customer_name')
                    ->label('Customer')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('entitas.nama')
                    ->label('Entitas')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Pembayaran')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Cash' => 'success',
                        'Credit' => 'warning',
                        'Transfer' => 'info',
                        'Debit' => 'primary',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('total_amount')
                    ->label('Total')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Draft' => 'gray',
                        'Completed' => 'success',
                        'Cancelled' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('saleItems_count')
                    ->label('Jumlah Item')
                    ->counts('saleItems')
                    ->badge()
                    ->color('info'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('entitas_id')
                    ->label('Entitas')
                    ->relationship('entitas', 'nama')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('payment_method')
                    ->label('Metode Pembayaran')
                    ->options([
                        'Cash' => 'Tunai',
                        'Credit' => 'Kredit',
                        'Transfer' => 'Transfer',
                        'Debit' => 'Debit',
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'Draft' => 'Draft',
                        'Completed' => 'Selesai',
                        'Cancelled' => 'Dibatalkan',
                    ]),
                Tables\Filters\Filter::make('transaction_date')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('transaction_date', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('transaction_date', '<=', $date),
                            );
                    }),
            ])
            ->headerActions([
                Tables\Actions\Action::make('import')
                    ->label('Import Data Penjualan')
                    ->icon('heroicon-o-arrow-up-tray')
                    ->color('success')
                    ->form([
                        Forms\Components\FileUpload::make('file')
                            ->label('File Excel')
                            ->acceptedFileTypes(['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'])
                            ->required()
                            ->helperText('Format: transaction_code, transaction_date, product_code, quantity, unit_price, unit_cost, payment_method, customer_name, tax_amount, discount_amount'),
                    ])
                    ->action(function (array $data) {
                        $filePath = $data['file'];

                        // Dispatch job untuk memproses import
                        ProcessSalesImport::dispatch($filePath, auth()->id());

                        Notification::make()
                            ->title('Import Dimulai')
                            ->body('File sedang diproses. Anda akan mendapat notifikasi setelah selesai.')
                            ->success()
                            ->send();
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\SaleItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSalesTransactions::route('/'),
            'create' => Pages\CreateSalesTransaction::route('/create'),
            'edit' => Pages\EditSalesTransaction::route('/{record}/edit'),
        ];
    }
}
