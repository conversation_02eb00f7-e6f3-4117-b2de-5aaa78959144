<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ShiftResource\Pages;
use App\Models\Shift;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Traits\HasExportActions;
use App\Exports\ShiftExport;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\TrashedFilter;
use Illuminate\Support\Facades\Auth;

class ShiftResource extends Resource
{
    use HasExportActions;

    protected static ?string $model = Shift::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';
    protected static ?string $navigationGroup = 'Jadwal & Absensi';
    protected static ?string $navigationLabel = 'Shift Kerja';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('nama_shift')
                    ->label('Nama Shift')
                    ->required()
                    ->maxLength(255),

                Toggle::make('is_split_shift')
                    ->label('Split Shift')
                    ->helperText('Aktifkan jika shift terbagi menjadi dua periode dengan break panjang')
                    ->reactive()
                    ->default(false),

                // Periode 1 (Regular/First Period)
                Section::make('Periode 1')
                    ->schema([
                        TimePicker::make('waktu_mulai')
                            ->label('Waktu Mulai Periode 1')
                            ->required()
                            ->seconds(false),

                        TimePicker::make('waktu_selesai')
                            ->label('Waktu Selesai Periode 1')
                            ->required()
                            ->seconds(false),

                        TextInput::make('toleransi_keterlambatan')
                            ->label('Toleransi Keterlambatan Periode 1 (menit)')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->maxValue(120),
                    ])
                    ->columns(3),

                // Periode 2 (Only for Split Shift)
                Section::make('Periode 2')
                    ->schema([
                        TimePicker::make('waktu_mulai_periode2')
                            ->label('Waktu Mulai Periode 2')
                            ->required()
                            ->seconds(false),

                        TimePicker::make('waktu_selesai_periode2')
                            ->label('Waktu Selesai Periode 2')
                            ->required()
                            ->seconds(false),

                        TextInput::make('toleransi_keterlambatan_periode2')
                            ->label('Toleransi Keterlambatan Periode 2 (menit)')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->maxValue(120)
                            ->helperText('Kosongkan untuk menggunakan toleransi periode 1'),
                    ])
                    ->columns(3)
                    ->visible(fn(callable $get) => $get('is_split_shift')),

                Textarea::make('keterangan')
                    ->label('Keterangan')
                    ->maxLength(1000)
                    ->columnSpanFull()
                    ->placeholder('Contoh: Shift pagi-sore dengan break 6 jam (08:00-12:00 & 18:00-22:00)'),

                Toggle::make('is_active')
                    ->label('Status Aktif')
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('nama_shift')
                    ->label('Nama Shift')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('shift_type')
                    ->label('Tipe Shift')
                    ->badge()
                    ->getStateUsing(fn(Shift $record) => $record->is_split_shift ? 'Split Shift' : 'Regular')
                    ->color(fn(string $state): string => match ($state) {
                        'Split Shift' => 'warning',
                        'Regular' => 'success',
                    }),

                TextColumn::make('schedule_display')
                    ->label('Jadwal Kerja')
                    ->getStateUsing(function (Shift $record) {
                        if ($record->is_split_shift) {
                            return sprintf(
                                'P1: %s-%s | P2: %s-%s',
                                $record->waktu_mulai->format('H:i'),
                                $record->waktu_selesai->format('H:i'),
                                $record->waktu_mulai_periode2?->format('H:i') ?? '--',
                                $record->waktu_selesai_periode2?->format('H:i') ?? '--'
                            );
                        }
                        return sprintf(
                            '%s - %s',
                            $record->waktu_mulai->format('H:i'),
                            $record->waktu_selesai->format('H:i')
                        );
                    })
                    ->wrap(),

                TextColumn::make('toleransi_keterlambatan')
                    ->label('Toleransi (menit)')
                    ->numeric()
                    ->sortable(),

                ToggleColumn::make('is_active')
                    ->label('Status Aktif')
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('Dibuat Pada')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Diperbarui Pada')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShifts::route('/'),
            'create' => Pages\CreateShift::route('/create'),
            'edit' => Pages\EditShift::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_active', true)->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }
}
