<?php

namespace App\Filament\Resources\SopDokumenResource\Pages;

use App\Filament\Resources\SopDokumenResource;
use Filament\Resources\Pages\CreateRecord;

class CreateSopDokumen extends CreateRecord
{
    protected static string $resource = SopDokumenResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'SOP berhasil dibuat';
    }
}
