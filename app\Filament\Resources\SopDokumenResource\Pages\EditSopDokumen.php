<?php

namespace App\Filament\Resources\SopDokumenResource\Pages;

use App\Filament\Resources\SopDokumenResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditSopDokumen extends EditRecord
{
    protected static string $resource = SopDokumenResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'SOP berhasil diperbarui';
    }
}
