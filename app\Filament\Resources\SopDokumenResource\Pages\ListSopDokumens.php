<?php

namespace App\Filament\Resources\SopDokumenResource\Pages;

use App\Filament\Resources\SopDokumenResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListSopDokumens extends ListRecords
{
    protected static string $resource = SopDokumenResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Tambah SOP')
                ->icon('heroicon-o-plus'),
        ];
    }

    public function getTabs(): array
    {
        return [
            'semua' => Tab::make('Semua SOP')
                ->icon('heroicon-o-document-text'),

            'sop_berlaku' => Tab::make('SOP Berlaku')
                ->icon('heroicon-o-check-circle')
                ->modifyQueryUsing(fn(Builder $query) => $query->aktif()->berlaku()),

            'departemen' => Tab::make('Departemen')
                ->icon('heroicon-o-building-office-2')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('scope_type', 'departemen')),

            'divisi' => Tab::make('Divisi')
                ->icon('heroicon-o-user-group')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('scope_type', 'divisi')),
        ];
    }
}
