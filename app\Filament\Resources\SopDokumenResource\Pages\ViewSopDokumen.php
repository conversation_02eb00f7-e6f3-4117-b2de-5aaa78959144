<?php

namespace App\Filament\Resources\SopDokumenResource\Pages;

use App\Filament\Resources\SopDokumenResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewSopDokumen extends ViewRecord
{
    protected static string $resource = SopDokumenResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
            
            Actions\Action::make('download')
                ->label('Download PDF')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('primary')
                ->url(fn (): string => asset('storage/' . $this->record->file_path))
                ->openUrlInNewTab(),
        ];
    }
}
