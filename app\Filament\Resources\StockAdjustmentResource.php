<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StockAdjustmentResource\Pages;
use App\Filament\Resources\StockAdjustmentResource\RelationManagers;
use App\Models\StockAdjustment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class StockAdjustmentResource extends Resource
{
    protected static ?string $model = StockAdjustment::class;

    protected static ?string $navigationIcon = 'heroicon-o-adjustments-horizontal';

    protected static ?string $navigationLabel = 'Penyesuaian Stok';

    protected static ?string $modelLabel = 'Penyesuaian Stok';

    protected static ?string $pluralModelLabel = 'Penyesuaian Stok';

    protected static ?string $navigationGroup = 'Inventaris';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Penyesuaian')
                    ->schema([
                        Forms\Components\DatePicker::make('adjustment_date')
                            ->label('Tanggal Penyesuaian')
                            ->required()
                            ->default(now()),
                        Forms\Components\Select::make('adjustment_type')
                            ->label('Jenis Penyesuaian')
                            ->options(StockAdjustment::ADJUSTMENT_TYPES)
                            ->required(),
                        Forms\Components\Select::make('adjustment_reason')
                            ->label('Alasan Penyesuaian')
                            ->options(StockAdjustment::ADJUSTMENT_REASONS)
                            ->required(),
                        Forms\Components\Select::make('entitas_id')
                            ->label('Entitas')
                            ->relationship('entitas', 'nama')
                            ->required(),
                        Forms\Components\Select::make('warehouse_id')
                            ->label('Gudang')
                            ->relationship('warehouse', 'name')
                            ->required(),
                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->required()
                            ->columnSpanFull(),
                        Forms\Components\Textarea::make('notes')
                            ->label('Catatan')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('adjustment_number')
                    ->label('Nomor Penyesuaian')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('adjustment_date')
                    ->label('Tanggal')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('adjustment_type')
                    ->label('Jenis')
                    ->formatStateUsing(fn($state) => StockAdjustment::ADJUSTMENT_TYPES[$state] ?? $state)
                    ->badge()
                    ->color(fn($state) => $state === 'Increase' ? 'success' : 'danger'),
                Tables\Columns\TextColumn::make('adjustment_reason')
                    ->label('Alasan')
                    ->formatStateUsing(fn($state) => StockAdjustment::ADJUSTMENT_REASONS[$state] ?? $state),
                Tables\Columns\TextColumn::make('entitas.nama')
                    ->label('Entitas')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('warehouse.name')
                    ->label('Gudang')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('stockAdjustmentItems_count')
                    ->label('Jumlah Item')
                    ->counts('stockAdjustmentItems')
                    ->numeric(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn($record) => $record->status_color),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'Draft' => 'Draft',
                        'Approved' => 'Disetujui',
                        'Cancelled' => 'Dibatalkan',
                    ]),
                Tables\Filters\SelectFilter::make('adjustment_type')
                    ->label('Jenis Penyesuaian')
                    ->options(StockAdjustment::ADJUSTMENT_TYPES),
                Tables\Filters\SelectFilter::make('entitas_id')
                    ->label('Entitas')
                    ->relationship('entitas', 'nama'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn($record) => $record->canBeEdited()),
                Tables\Actions\Action::make('approve')
                    ->label('Setujui')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn($record) => $record->canBeApproved())
                    ->requiresConfirmation()
                    ->action(fn($record) => $record->approve()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('adjustment_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStockAdjustments::route('/'),
            'create' => Pages\CreateStockAdjustment::route('/create'),
            'edit' => Pages\EditStockAdjustment::route('/{record}/edit'),
        ];
    }
}
