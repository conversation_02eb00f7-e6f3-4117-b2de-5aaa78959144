<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StockMovementResource\Pages;
use App\Filament\Resources\StockMovementResource\RelationManagers;
use App\Models\StockMovement;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class StockMovementResource extends Resource
{
    protected static ?string $model = StockMovement::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrows-right-left';

    protected static ?string $navigationLabel = 'Pergerakan Stok';

    protected static ?string $modelLabel = 'Pergerakan Stok';

    protected static ?string $pluralModelLabel = 'Pergerakan Stok';

    protected static ?string $navigationGroup = 'Inventaris';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('movement_number')
                    ->label('Nomor Pergerakan')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('movement_date')
                    ->label('Tanggal')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('movement_type')
                    ->label('Jenis Pergerakan')
                    ->formatStateUsing(fn($state) => StockMovement::MOVEMENT_TYPES[$state] ?? $state)
                    ->colors([
                        'success' => ['Purchase_Receipt', 'Transfer_In', 'Adjustment_In', 'Opening_Balance', 'Production_In'],
                        'danger' => ['Sales_Issue', 'Transfer_Out', 'Adjustment_Out', 'Production_Out'],
                    ]),
                Tables\Columns\TextColumn::make('product.nama')
                    ->label('Produk')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('entitas.nama')
                    ->label('Entitas')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('quantity')
                    ->label('Jumlah')
                    ->numeric()
                    ->sortable()
                    ->formatStateUsing(
                        fn($record) => ($record->movement_direction === 'IN' ? '+' : '-') . number_format($record->quantity)
                    )
                    ->color(fn($record) => $record->movement_direction === 'IN' ? 'success' : 'danger'),
                Tables\Columns\TextColumn::make('unit_cost')
                    ->label('Harga Satuan')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_value')
                    ->label('Total Nilai')
                    ->money('IDR')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('movement_type')
                    ->label('Jenis Pergerakan')
                    ->options(StockMovement::MOVEMENT_TYPES),
                Tables\Filters\SelectFilter::make('entitas_id')
                    ->label('Entitas')
                    ->relationship('entitas', 'nama'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Stock movements are audit trail - no delete
                ]),
            ])
            ->defaultSort('movement_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStockMovements::route('/'),
        ];
    }
}
