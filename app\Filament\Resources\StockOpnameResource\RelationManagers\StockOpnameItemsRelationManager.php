<?php

namespace App\Filament\Resources\StockOpnameResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class StockOpnameItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'stockOpnameItems';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('product_id')
                    ->label('Produk')
                    ->relationship('product', 'nama')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->getOptionLabelFromRecordUsing(fn($record) => "{$record->kode} - {$record->nama}"),
                Forms\Components\TextInput::make('physical_quantity')
                    ->label('Jumlah Fisik')
                    ->numeric()
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                        $systemQty = $get('system_quantity') ?? 0;
                        $variance = $state - $systemQty;
                        $set('variance_quantity', $variance);

                        $unitCost = $get('unit_cost') ?? 0;
                        $set('variance_value', $variance * $unitCost);

                        if ($variance > 0) {
                            $set('variance_type', 'Surplus');
                        } elseif ($variance < 0) {
                            $set('variance_type', 'Shortage');
                        } else {
                            $set('variance_type', 'Match');
                        }
                    }),
                Forms\Components\TextInput::make('system_quantity')
                    ->label('Jumlah Sistem')
                    ->numeric()
                    ->disabled()
                    ->dehydrated(),
                Forms\Components\TextInput::make('variance_quantity')
                    ->label('Selisih')
                    ->numeric()
                    ->disabled()
                    ->dehydrated(),
                Forms\Components\Select::make('variance_type')
                    ->label('Jenis Selisih')
                    ->options([
                        'Match' => 'Sesuai',
                        'Surplus' => 'Lebih',
                        'Shortage' => 'Kurang',
                    ])
                    ->disabled()
                    ->dehydrated(),
                Forms\Components\TextInput::make('unit_cost')
                    ->label('Harga Satuan')
                    ->numeric()
                    ->prefix('Rp')
                    ->disabled()
                    ->dehydrated(),
                Forms\Components\TextInput::make('variance_value')
                    ->label('Nilai Selisih')
                    ->numeric()
                    ->prefix('Rp')
                    ->disabled()
                    ->dehydrated(),
                Forms\Components\Textarea::make('notes')
                    ->label('Catatan')
                    ->columnSpanFull(),
                Forms\Components\Toggle::make('is_counted')
                    ->label('Sudah Dihitung')
                    ->default(true),
            ])
            ->columns(2);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('product.nama')
            ->columns([
                Tables\Columns\TextColumn::make('product.kode')
                    ->label('Kode Produk')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.nama')
                    ->label('Nama Produk')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('system_quantity')
                    ->label('Qty Sistem')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('physical_quantity')
                    ->label('Qty Fisik')
                    ->numeric()
                    ->placeholder('Belum dihitung'),
                Tables\Columns\TextColumn::make('variance_quantity')
                    ->label('Selisih')
                    ->numeric()
                    ->color(fn($record) => $record->variance_color)
                    ->formatStateUsing(function ($state, $record) {
                        if ($state == 0) return '0';
                        $prefix = $state > 0 ? '+' : '';
                        return $prefix . number_format($state);
                    }),
                Tables\Columns\TextColumn::make('variance_type')
                    ->label('Jenis')
                    ->badge()
                    ->color(fn($state) => match ($state) {
                        'Surplus' => 'success',
                        'Shortage' => 'danger',
                        'Match' => 'gray',
                        default => 'gray'
                    })
                    ->formatStateUsing(fn($state) => match ($state) {
                        'Surplus' => 'Lebih',
                        'Shortage' => 'Kurang',
                        'Match' => 'Sesuai',
                        default => $state
                    }),
                Tables\Columns\TextColumn::make('variance_value')
                    ->label('Nilai Selisih')
                    ->money('IDR')
                    ->color(fn($record) => $record->variance_color),
                Tables\Columns\IconColumn::make('is_counted')
                    ->label('Dihitung')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('variance_type')
                    ->label('Jenis Selisih')
                    ->options([
                        'Match' => 'Sesuai',
                        'Surplus' => 'Lebih',
                        'Shortage' => 'Kurang',
                    ]),
                Tables\Filters\TernaryFilter::make('is_counted')
                    ->label('Status Counting'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Tambah Produk')
                    ->mutateFormDataUsing(function (array $data): array {
                        // Auto-load system quantity and unit cost
                        $stockOpname = $this->getOwnerRecord();
                        $currentStock = \App\Models\InventoryStock::where('product_id', $data['product_id'])
                            ->where('warehouse_id', $stockOpname->warehouse_id)
                            ->where('entitas_id', $stockOpname->entitas_id)
                            ->first();

                        $data['system_quantity'] = $currentStock ? $currentStock->quantity : 0;
                        $data['unit_cost'] = $currentStock ? $currentStock->average_cost : 0;
                        $data['counted_by'] = auth()->id();
                        $data['counted_at'] = now();

                        return $data;
                    }),
                Tables\Actions\Action::make('load_all_products')
                    ->label('Muat Semua Produk')
                    ->icon('heroicon-o-plus-circle')
                    ->color('success')
                    ->action(function () {
                        $this->loadAllProducts();
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Muat Semua Produk')
                    ->modalDescription('Apakah Anda yakin ingin memuat semua produk yang ada di gudang ini untuk stock opname?'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('mark_counted')
                    ->label('Hitung')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn($record) => !$record->is_counted)
                    ->form([
                        Forms\Components\TextInput::make('physical_quantity')
                            ->label('Jumlah Fisik')
                            ->numeric()
                            ->required(),
                        Forms\Components\Textarea::make('notes')
                            ->label('Catatan'),
                    ])
                    ->action(function ($record, array $data) {
                        $record->markAsCounted($data['physical_quantity'], auth()->id(), $data['notes'] ?? null);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('product.nama');
    }

    public function loadAllProducts()
    {
        $stockOpname = $this->getOwnerRecord();

        // Get all products that have stock in this warehouse/entitas
        $stocks = \App\Models\InventoryStock::where('warehouse_id', $stockOpname->warehouse_id)
            ->where('entitas_id', $stockOpname->entitas_id)
            ->where('quantity', '>', 0)
            ->with('product')
            ->get();

        foreach ($stocks as $stock) {
            // Check if product already exists in opname
            $existingItem = $stockOpname->stockOpnameItems()
                ->where('product_id', $stock->product_id)
                ->first();

            if (!$existingItem) {
                $stockOpname->stockOpnameItems()->create([
                    'product_id' => $stock->product_id,
                    'system_quantity' => $stock->quantity,
                    'unit_cost' => $stock->average_cost,
                    'counted_by' => auth()->id(),
                ]);
            }
        }

        // Refresh the table
        $this->dispatch('$refresh');

        // Show notification
        \Filament\Notifications\Notification::make()
            ->title('Produk berhasil dimuat')
            ->body('Semua produk dengan stok > 0 telah ditambahkan ke stock opname.')
            ->success()
            ->send();
    }
}
