<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StrukturOrganisasiResource\Pages;
use App\Models\Entitas;
use App\Models\Departemen;
use App\Models\Divisi;
use App\Models\Jabatan;
use App\Models\Karyawan;
use App\Exports\StrukturOrganisasiExport;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Facades\Excel;

class StrukturOrganisasiResource extends Resource
{
    protected static ?string $model = Entitas::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';
    protected static ?string $navigationGroup = 'Data Master';
    protected static ?string $navigationLabel = 'Struktur Organisasi';
    protected static ?string $label = 'Struktur Organisasi';
    protected static ?string $pluralLabel = 'Struktur Organisasi';

    public static function form(Form $form): Form
    {
        return $form->schema([
            // Form tidak diperlukan karena ini hanya untuk viewing
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('nama')
                    ->label('Entitas')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->color('primary'),

                TextColumn::make('alamat')
                    ->label('Alamat')
                    ->searchable()
                    ->limit(50),

                TextColumn::make('total_karyawan')
                    ->label('Total Karyawan')
                    ->getStateUsing(function (Entitas $record) {
                        return $record->karyawan()->where('status_aktif', true)->count();
                    })
                    ->badge()
                    ->color('success'),

                TextColumn::make('departemen_count')
                    ->label('Departemen')
                    ->getStateUsing(function (Entitas $record) {
                        return Departemen::whereHas('karyawan', function ($query) use ($record) {
                            $query->where('id_entitas', $record->id)
                                ->where('status_aktif', true);
                        })->count();
                    })
                    ->badge()
                    ->color('info'),

                TextColumn::make('divisi_count')
                    ->label('Divisi')
                    ->getStateUsing(function (Entitas $record) {
                        return Divisi::whereHas('karyawan', function ($query) use ($record) {
                            $query->where('id_entitas', $record->id)
                                ->where('status_aktif', true);
                        })->count();
                    })
                    ->badge()
                    ->color('warning'),

                TextColumn::make('jabatan_count')
                    ->label('Jabatan')
                    ->getStateUsing(function (Entitas $record) {
                        return Jabatan::whereHas('karyawan', function ($query) use ($record) {
                            $query->where('id_entitas', $record->id)
                                ->where('status_aktif', true);
                        })->count();
                    })
                    ->badge()
                    ->color('gray'),
            ])
            ->filters([
                SelectFilter::make('has_geofencing')
                    ->label('Geofencing')
                    ->options([
                        '1' => 'Aktif',
                        '0' => 'Tidak Aktif',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (isset($data['value'])) {
                            return $query->where('enable_geofencing', $data['value']);
                        }
                        return $query;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('Lihat Detail')
                    ->icon('heroicon-o-eye'),

                Tables\Actions\Action::make('export')
                    ->label('Export Excel')
                    ->icon('heroicon-o-document-arrow-down')
                    ->color('success')
                    ->action(function (Entitas $record) {
                        return Excel::download(
                            new StrukturOrganisasiExport($record->id),
                            'struktur-organisasi-' . str_replace(' ', '-', strtolower($record->nama)) . '.xlsx'
                        );
                    }),
            ])
            ->bulkActions([
                // Tidak ada bulk actions
            ])
            ->defaultSort('nama')
            ->striped()
            ->paginated([10, 25, 50]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStrukturOrganisasi::route('/'),
            'view' => Pages\ViewStrukturOrganisasi::route('/{record}'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit($record): bool
    {
        return false;
    }

    public static function canDelete($record): bool
    {
        return false;
    }
}
