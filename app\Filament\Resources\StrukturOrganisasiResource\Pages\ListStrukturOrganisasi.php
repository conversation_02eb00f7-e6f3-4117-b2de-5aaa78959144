<?php

namespace App\Filament\Resources\StrukturOrganisasiResource\Pages;

use App\Filament\Resources\StrukturOrganisasiResource;
use App\Filament\Resources\StrukturOrganisasiResource\Widgets\OrganisasiStatsWidget;
use App\Exports\StrukturOrganisasiExport;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Maatwebsite\Excel\Facades\Excel;

class ListStrukturOrganisasi extends ListRecords
{
    protected static string $resource = StrukturOrganisasiResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('export_all')
                ->label('Export Semua Data')
                ->icon('heroicon-o-document-arrow-down')
                ->color('success')
                ->action(function () {
                    return Excel::download(
                        new StrukturOrganisasiExport(),
                        'struktur-organisasi-lengkap-' . date('Y-m-d') . '.xlsx'
                    );
                }),

            Actions\Action::make('refresh')
                ->label('Refresh Data')
                ->icon('heroicon-o-arrow-path')
                ->action(function () {
                    $this->redirect(request()->header('Referer'));
                }),
        ];
    }

    public function getTitle(): string
    {
        return 'Struktur Organisasi';
    }

    public function getSubheading(): ?string
    {
        return 'Tampilan hierarki organisasi berdasarkan entitas, departemen, divisi, dan jabatan';
    }

    protected function getHeaderWidgets(): array
    {
        return [
            OrganisasiStatsWidget::class,
        ];
    }
}
