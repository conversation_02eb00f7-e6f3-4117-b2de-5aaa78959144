<?php

namespace App\Filament\Resources\StrukturOrganisasiResource\Pages;

use App\Filament\Resources\StrukturOrganisasiResource;
use App\Models\Entitas;
use App\Models\Departemen;
use App\Models\Divisi;
use App\Models\Jabatan;
use App\Models\Karyawan;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\ViewEntry;
use Filament\Support\Enums\FontWeight;

class ViewStrukturOrganisasi extends ViewRecord
{
    protected static string $resource = StrukturOrganisasiResource::class;

    // public function getMaxContentWidth(): ?string
    // {
    //     return 'full';
    // }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('back')
                ->label('Kembali')
                ->icon('heroicon-o-arrow-left')
                ->url(StrukturOrganisasiResource::getUrl('index')),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Entitas')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('nama')
                                    ->label('Nama Entitas')
                                    ->weight(FontWeight::Bold)
                                    ->size('lg'),

                                TextEntry::make('alamat')
                                    ->label('Alamat'),

                                TextEntry::make('coordinates')
                                    ->label('Koordinat')
                                    ->getStateUsing(function (Entitas $record) {
                                        if ($record->latitude && $record->longitude) {
                                            return "{$record->latitude}, {$record->longitude}";
                                        }
                                        return 'Tidak tersedia';
                                    }),

                                TextEntry::make('enable_geofencing')
                                    ->label('Geofencing')
                                    ->badge()
                                    ->getStateUsing(function (Entitas $record) {
                                        return $record->enable_geofencing ? 'Aktif' : 'Tidak Aktif';
                                    })
                                    ->color(fn(string $state): string => $state === 'Aktif' ? 'success' : 'gray'),
                            ]),
                    ])
                    ->collapsible(),

                Section::make('Statistik Organisasi')
                    ->schema([
                        Grid::make(4)
                            ->schema([
                                TextEntry::make('total_karyawan')
                                    ->label('Total Karyawan')
                                    ->getStateUsing(function (Entitas $record) {
                                        return $record->karyawan()->where('status_aktif', true)->count();
                                    })
                                    ->badge()
                                    ->color('success'),

                                TextEntry::make('total_departemen')
                                    ->label('Total Departemen')
                                    ->getStateUsing(function (Entitas $record) {
                                        return Departemen::whereHas('karyawan', function ($query) use ($record) {
                                            $query->where('id_entitas', $record->id)
                                                ->where('status_aktif', true);
                                        })->count();
                                    })
                                    ->badge()
                                    ->color('info'),

                                TextEntry::make('total_divisi')
                                    ->label('Total Divisi')
                                    ->getStateUsing(function (Entitas $record) {
                                        return Divisi::whereHas('karyawan', function ($query) use ($record) {
                                            $query->where('id_entitas', $record->id)
                                                ->where('status_aktif', true);
                                        })->count();
                                    })
                                    ->badge()
                                    ->color('warning'),

                                TextEntry::make('total_jabatan')
                                    ->label('Total Jabatan')
                                    ->getStateUsing(function (Entitas $record) {
                                        return Jabatan::whereHas('karyawan', function ($query) use ($record) {
                                            $query->where('id_entitas', $record->id)
                                                ->where('status_aktif', true);
                                        })->count();
                                    })
                                    ->badge()
                                    ->color('gray'),
                            ]),
                    ])
                    ->collapsible(),

                ViewEntry::make('struktur_organisasi')
                    ->label('')
                    ->view('filament.pages.struktur-organisasi-detail')
                    ->viewData([
                        'entitas' => $this->record,
                    ])->columnSpan(2),

                // ViewEntry::make('struktur_organisasi2')
                //     ->label('')
                //     ->view('filament.pages.struktur-organisasi-canvas')
                //     ->viewData([
                //         'entitas' => $this->record,
                //     ])->columnSpan(2),
            ]);
    }

    public function getTitle(): string
    {
        return 'Struktur Organisasi: ' . $this->record->nama;
    }
}
