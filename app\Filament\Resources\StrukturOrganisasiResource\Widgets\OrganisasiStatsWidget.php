<?php

namespace App\Filament\Resources\StrukturOrganisasiResource\Widgets;

use App\Models\Entitas;
use App\Models\Departemen;
use App\Models\Divisi;
use App\Models\Jabatan;
use App\Models\Karyawan;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class OrganisasiStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalEntitas = Entitas::count();
        $totalDepartemen = Departemen::count();
        $totalDivisi = Divisi::count();
        $totalJabatan = Jabatan::count();
        $totalKaryawan = Karyawan::where('status_aktif', true)->count();
        $totalKaryawanNonAktif = Karyawan::where('status_aktif', false)->count();

        return [
            Stat::make('Total Entitas', $totalEntitas)
                ->description('Lokasi kerja')
                ->descriptionIcon('heroicon-m-building-office-2')
                ->color('primary'),

            Stat::make('Total Departemen', $totalDepartemen)
                ->description('Unit departemen')
                ->descriptionIcon('heroicon-m-briefcase')
                ->color('info'),

            Stat::make('Total Divisi', $totalDivisi)
                ->description('Unit divisi')
                ->descriptionIcon('heroicon-m-squares-2x2')
                ->color('warning'),

            Stat::make('Total Jabatan', $totalJabatan)
                ->description('Posisi jabatan')
                ->descriptionIcon('heroicon-m-check-badge')
                ->color('gray'),

            Stat::make('Karyawan Aktif', $totalKaryawan)
                ->description('Karyawan yang masih aktif')
                ->descriptionIcon('heroicon-m-users')
                ->color('success'),

            Stat::make('Karyawan Non-Aktif', $totalKaryawanNonAktif)
                ->description('Karyawan yang sudah tidak aktif')
                ->descriptionIcon('heroicon-m-user-minus')
                ->color('danger'),
        ];
    }
}
