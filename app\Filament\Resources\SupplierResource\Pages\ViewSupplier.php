<?php

namespace App\Filament\Resources\SupplierResource\Pages;

use App\Filament\Resources\SupplierResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewSupplier extends ViewRecord
{
    protected static string $resource = SupplierResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Informasi Supplier')
                    ->schema([
                        Infolists\Components\TextEntry::make('nama')
                            ->label('Nama Supplier'),
                        Infolists\Components\TextEntry::make('nama_perusahaan')
                            ->label('Nama Perusahaan'),
                        Infolists\Components\TextEntry::make('email')
                            ->label('Email')
                            ->copyable(),
                        Infolists\Components\TextEntry::make('nomor_handphone')
                            ->label('Nomor Handphone')
                            ->copyable(),
                        Infolists\Components\TextEntry::make('npwp')
                            ->label('NPWP')
                            ->copyable(),
                        Infolists\Components\TextEntry::make('alamat')
                            ->label('Alamat')
                            ->columnSpanFull(),
                        Infolists\Components\TextEntry::make('info_lainnya')
                            ->label('Informasi Lainnya')
                            ->columnSpanFull()
                            ->placeholder('Tidak ada informasi tambahan'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Informasi Bank')
                    ->schema([
                        Infolists\Components\TextEntry::make('akun_bank')
                            ->label('Akun Bank'),
                        Infolists\Components\TextEntry::make('nama_bank')
                            ->label('Nama Bank'),
                        Infolists\Components\TextEntry::make('kantor_cabang_bank')
                            ->label('Kantor Cabang Bank'),
                        Infolists\Components\TextEntry::make('nomor_rekening')
                            ->label('Nomor Rekening')
                            ->copyable(),
                        Infolists\Components\TextEntry::make('pemegang_akun_bank')
                            ->label('Pemegang Akun Bank'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Pengaturan Akuntansi')
                    ->schema([
                        Infolists\Components\TextEntry::make('akunHutang.nama_akun')
                            ->label('Akun Hutang'),
                        Infolists\Components\TextEntry::make('syarat_pembayaran_utama')
                            ->label('Syarat Pembayaran Utama'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Informasi Sistem')
                    ->schema([
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Dibuat Pada')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Diperbarui Pada')
                            ->dateTime(),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }
}
