<?php

namespace App\Filament\Resources\TaskResource\Pages;

use App\Filament\Resources\TaskResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Notifications\Notification;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components;
use Livewire\Attributes\On;

class ViewTask extends ViewRecord
{
    protected static string $resource = TaskResource::class;

    protected function getHeaderActions(): array
    {
        return [
            $this->editAction(),
            $this->markCompleteAction(),
            $this->startTaskAction(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Components\Section::make('Task Information')
                    ->schema([
                        Components\Grid::make(2)
                            ->schema([
                                Components\TextEntry::make('name')
                                    ->label('Task Name')
                                    ->size('lg')
                                    ->weight('bold'),
                                Components\TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn(string $state): string => match ($state) {
                                        'todo' => 'gray',
                                        'in_progress' => 'warning',
                                        'completed' => 'success',
                                        default => 'gray',
                                    }),
                                Components\TextEntry::make('project.name')
                                    ->label('Project'),
                                Components\TextEntry::make('assignedUser.name')
                                    ->label('Assigned To'),
                                Components\TextEntry::make('start_date')
                                    ->label('Start Date')
                                    ->date(),
                                Components\TextEntry::make('due_date')
                                    ->label('Due Date')
                                    ->date(),
                                Components\TextEntry::make('created_at')
                                    ->label('Created')
                                    ->dateTime(),
                            ]),
                        Components\TextEntry::make('formatted_description')
                            ->label('Description')
                            ->html()
                            ->columnSpanFull(),
                    ]),

                // Enhanced Comments Section
                // Components\Section::make('Comments & Discussions')
                //     ->schema([
                //         Components\View::make('filament.task-comments')
                //             ->viewData(['task' => $this->record]),
                //     ])
                //     ->collapsible()
                //     ->persistCollapsed()
                //     ->id('comments-section'),
            ]);
    }

    public function editAction(): Actions\Action
    {
        return Actions\Action::make('edit')
            ->label('Edit Task')
            ->icon('heroicon-o-pencil')
            ->color('primary')
            ->url(fn() => TaskResource::getUrl('edit', ['record' => $this->record]));
    }

    public function markCompleteAction(): Actions\Action
    {
        return Actions\Action::make('mark_complete')
            ->label('Mark Complete')
            ->icon('heroicon-o-check-circle')
            ->color('success')
            ->requiresConfirmation()
            ->modalHeading('Mark Task as Complete')
            ->modalDescription('Are you sure you want to mark this task as completed?')
            ->action(function () {
                $this->record->update(['status' => 'completed']);

                Notification::make()
                    ->title('Task Completed')
                    ->body('Task has been marked as completed successfully.')
                    ->success()
                    ->send();

                $this->redirect(TaskResource::getUrl('view', ['record' => $this->record]));
            });
    }

    public function startTaskAction(): Actions\Action
    {
        return Actions\Action::make('start_task')
            ->label('Start Task')
            ->icon('heroicon-o-play')
            ->color('warning')
            ->action(function () {
                $this->record->update(['status' => 'in_progress']);

                Notification::make()
                    ->title('Task Started')
                    ->body('Task has been moved to In Progress.')
                    ->success()
                    ->send();

                $this->redirect(TaskResource::getUrl('view', ['record' => $this->record]));
            });
    }
}
