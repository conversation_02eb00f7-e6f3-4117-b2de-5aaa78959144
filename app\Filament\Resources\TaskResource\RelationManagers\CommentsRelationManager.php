<?php

namespace App\Filament\Resources\TaskResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use App\Filament\Forms\Components\MentionTextarea;
use Filament\Tables\Actions;

class CommentsRelationManager extends RelationManager
{
    protected static string $relationship = 'comments';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                MentionTextarea::make('comment')
                    ->label('Komentar')
                    ->placeholder('Tulis komentar... Gunakan @username untuk mention seseorang')
                    ->required()
                    ->rows(4)
                    ->contextType('task')
                    ->contextId(fn() => $this->getOwnerRecord()->id)
                    ->projectId(fn() => $this->getOwnerRecord()->project_id)
                    ->columnSpanFull(),
                Forms\Components\FileUpload::make('attachments')
                    ->label('Lampiran')
                    ->multiple()
                    ->directory('task-comments')
                    ->acceptedFileTypes(['image/*', 'application/pdf', '.doc', '.docx', '.xls', '.xlsx'])
                    ->maxSize(5120) // 5MB
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('comment')
            ->modifyQueryUsing(fn($query) => $query->with(['user', 'replies.user'])->withCount('replies'))
            ->columns([
                Tables\Columns\Layout\Stack::make([
                    // Main comment
                    Tables\Columns\Layout\Split::make([
                        Tables\Columns\Layout\Stack::make([
                            Tables\Columns\TextColumn::make('user.name')
                                ->label('User')
                                ->weight('bold')
                                ->size('sm')
                                ->color('primary'),
                            Tables\Columns\TextColumn::make('formatted_comment')
                                ->label('Comment')
                                ->html()
                                ->wrap()
                                ->searchable(['comment']),
                        ])->grow(),
                        Tables\Columns\Layout\Stack::make([
                            Tables\Columns\TextColumn::make('created_at')
                                ->label('Time')
                                ->since()
                                ->size('sm')
                                ->color('gray'),
                            Tables\Columns\TextColumn::make('attachments')
                                ->label('Attachments')
                                ->formatStateUsing(function ($state) {
                                    if (!$state || !is_array($state)) {
                                        return null;
                                    }
                                    return count($state) . ' file(s)';
                                })
                                ->badge()
                                ->color('info')
                                ->size('sm'),
                        ])->alignment('end')->grow(false),
                    ]),

                    // Replies section
                    Tables\Columns\ViewColumn::make('replies')
                        ->view('filament.components.comment-replies')
                        ->visible(fn($record) => ($record->replies_count ?? 0) > 0),

                    Tables\Columns\TextColumn::make('replies_count')
                        ->counts('replies')
                        ->label('Replies')
                        ->badge()
                        ->color('gray')
                        ->size('sm')
                        ->visible(fn($record) => ($record->replies_count ?? 0) > 0),
                ])->space(2),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('Pengguna')
                    ->relationship('user', 'name'),
            ])
            ->headerActions([
                //
                Tables\Actions\Action::make('add_comment')
                    ->label('Tambah Komentar')
                    ->icon('heroicon-o-chat-bubble-left-right')
                    ->form([
                        MentionTextarea::make('comment')
                            ->label('Komentar')
                            ->placeholder('Tulis komentar... Gunakan @username untuk mention seseorang')
                            ->required()
                            ->rows(4)
                            ->contextType('task')
                            ->contextId(fn() => $this->getOwnerRecord()->id)
                            ->projectId(fn() => $this->getOwnerRecord()->project_id)
                            ->columnSpanFull(),
                        Forms\Components\FileUpload::make('attachments')
                            ->label('Lampiran')
                            ->multiple()
                            ->directory('task-comments')
                            ->acceptedFileTypes(['image/*', 'application/pdf', '.doc', '.docx', '.xls', '.xlsx'])
                            ->maxSize(5120) // 5MB
                            ->columnSpanFull(),
                    ])
                    ->action(function (array $data) {
                        $comment = $this->getOwnerRecord()->comments()->create([
                            'user_id' => auth()->id() ?? 1,
                            'comment' => $data['comment'],
                            'attachments' => $data['attachments'] ?? null,
                        ]);

                        // Process mentions in the comment
                        $comment->processMentions();

                        $userName = auth()->user()?->name ?? 'System';
                        \App\Models\ProjectActivity::log(
                            'comment_added',
                            $comment,
                            $userName . ' added a comment to task: ' . $this->getOwnerRecord()->name
                        );

                        // Refresh the table to show the new comment
                        $this->dispatch('refreshTable');
                    })
                    ->after(function () {
                        // Send notification
                        \Filament\Notifications\Notification::make()
                            ->title('Comment Added')
                            ->body('Your comment has been added successfully.')
                            ->success()
                            ->send();
                    }),
                // Tables\Actions\CreateAction::make()
                //     ->label('Tambah Komentar')
                //     ->mutateFormDataUsing(function (array $data): array {
                //         $data['user_id'] = auth()->id() ?? 1;
                //         return $data;
                //     })
                //     ->after(function ($record) {
                //         // Process mentions in the comment
                //         $record->processMentions();

                //         $userName = auth()->user()?->name ?? 'System';
                //         \App\Models\ProjectActivity::log(
                //             'comment_added',
                //             $record,
                //             $userName . ' added a comment to task: ' . $this->getOwnerRecord()->name
                //         );
                //     }),
            ])
            ->actions([
                Tables\Actions\Action::make('reply')
                    ->label('Balas')
                    ->icon('heroicon-o-chat-bubble-left-right')
                    ->form([
                        MentionTextarea::make('comment')
                            ->label('Balasan')
                            ->placeholder('Tulis balasan... Gunakan @username untuk mention seseorang')
                            ->required()
                            ->rows(3)
                            ->contextType('task')
                            ->contextId(fn() => $this->getOwnerRecord()->id)
                            ->projectId(fn() => $this->getOwnerRecord()->project_id),
                        Forms\Components\FileUpload::make('attachments')
                            ->label('Lampiran')
                            ->multiple()
                            ->directory('task-comments')
                            ->acceptedFileTypes(['image/*', 'application/pdf', '.doc', '.docx'])
                            ->maxSize(5120),
                    ])
                    ->action(function (array $data, $record) {
                        $reply = $record->replies()->create([
                            'task_id' => $record->task_id,
                            'user_id' => auth()->id() ?? 1,
                            'comment' => $data['comment'],
                            'attachments' => $data['attachments'] ?? null,
                        ]);

                        // Process mentions in the reply
                        $reply->processMentions();

                        $userName = auth()->user()?->name ?? 'System';
                        \App\Models\ProjectActivity::log(
                            'comment_replied',
                            $reply,
                            $userName . ' replied to a comment in task: ' . $this->getOwnerRecord()->name
                        );

                        // Refresh the table to show the new reply
                        $this->dispatch('refreshTable');
                    })
                    ->after(function () {
                        // Send notification
                        \Filament\Notifications\Notification::make()
                            ->title('Reply Added')
                            ->body('Your reply has been added successfully.')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\EditAction::make()
                    ->visible(fn($record) => $record->user_id === (auth()->id() ?? 0)),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn($record) => $record->user_id === (auth()->id() ?? 0))
                    ->after(function ($record) {
                        $userName = auth()->user()?->name ?? 'System';
                        \App\Models\ProjectActivity::log(
                            'comment_deleted',
                            $record,
                            $userName . ' deleted a comment from task: ' . $this->getOwnerRecord()->name
                        );
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn() => auth()->user()?->hasRole('admin') ?? false),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
