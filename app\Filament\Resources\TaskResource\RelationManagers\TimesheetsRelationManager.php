<?php

namespace App\Filament\Resources\TaskResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TimesheetsRelationManager extends RelationManager
{
    protected static string $relationship = 'timesheets';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\DatePicker::make('date')
                    ->label('Tanggal')
                    ->required()
                    ->default(now()),
                Forms\Components\TextInput::make('hours')
                    ->label('Jam Kerja')
                    ->numeric()
                    ->required()
                    ->step(0.25)
                    ->minValue(0.25)
                    ->maxValue(24)
                    ->suffix('jam'),
                Forms\Components\Textarea::make('description')
                    ->label('Deskripsi Pekerjaan')
                    ->required()
                    ->rows(3)
                    ->columnSpanFull(),
            ])
            ->columns(2);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('date')
            ->columns([
                Tables\Columns\TextColumn::make('date')
                    ->label('Tanggal')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('hours')
                    ->label('Jam Kerja')
                    ->suffix(' jam')
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('date_range')
                    ->form([
                        Forms\Components\DatePicker::make('date_from')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('date_until')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['date_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('date', '>=', $date),
                            )
                            ->when(
                                $data['date_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('date', '<=', $date),
                            );
                    }),
            ])
            ->headerActions([
                Tables\Actions\Action::make('add_timesheet')
                    ->label('Tambah Timesheet')
                    ->icon('heroicon-o-clock')
                    ->form([
                        Forms\Components\DatePicker::make('date')
                            ->label('Tanggal')
                            ->required()
                            ->default(now()),
                        Forms\Components\TextInput::make('hours')
                            ->label('Jam Kerja')
                            ->numeric()
                            ->required()
                            ->step(0.25)
                            ->minValue(0.25)
                            ->maxValue(24)
                            ->suffix('jam'),
                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi Pekerjaan')
                            ->required()
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->action(function (array $data) {
                        $task = $this->getOwnerRecord();

                        $timesheet = $task->timesheets()->create([
                            'user_id' => $task->assigned_to, // Otomatis menggunakan assigned_to dari task
                            'date' => $data['date'],
                            'hours' => $data['hours'],
                            'description' => $data['description'],
                        ]);

                        // Log activity
                        $userName = auth()->user()?->name ?? 'System';
                        \App\Models\ProjectActivity::log(
                            'timesheet_added',
                            $timesheet,
                            $userName . ' added timesheet for task: ' . $task->name
                        );

                        // Refresh the table
                        $this->dispatch('refreshTable');
                    })
                    ->after(function () {
                        // Send notification
                        \Filament\Notifications\Notification::make()
                            ->title('Timesheet Added')
                            ->body('Timesheet has been added successfully.')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\CreateAction::make()
                    ->label('Tambah Timesheet (Default)')
                    ->visible(false) // Hide default action, keep for fallback
                    ->mutateFormDataUsing(function (array $data): array {
                        $task = $this->getOwnerRecord();
                        $data['user_id'] = $task->assigned_to; // Otomatis set ke assigned_to
                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('date', 'desc');
    }
}
