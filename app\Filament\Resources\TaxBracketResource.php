<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TaxBracketResource\Pages;
use App\Models\TaxBracket;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TaxBracketResource extends Resource
{
    protected static ?string $model = TaxBracket::class;

    protected static ?string $navigationIcon = 'heroicon-o-calculator';

    protected static ?string $navigationLabel = 'Tax Brackets';

    protected static ?string $navigationGroup = 'Enhanced Payroll';

    protected static ?int $navigationSort = 2;

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole(['super_admin']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Tax Bracket Information')
                    ->schema([
                        Forms\Components\TextInput::make('bracket_name')
                            ->label('Bracket Name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Tarif 1, Tarif 2, etc.'),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('min_income')
                                    ->label('Minimum Income')
                                    ->numeric()
                                    ->required()
                                    ->prefix('Rp')
                                    ->minValue(0),
                                Forms\Components\TextInput::make('max_income')
                                    ->label('Maximum Income')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->placeholder('Leave empty for unlimited')
                                    ->minValue(0),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('tax_rate')
                                    ->label('Tax Rate')
                                    ->numeric()
                                    ->required()
                                    ->suffix('%')
                                    ->minValue(0)
                                    ->maxValue(100),
                                Forms\Components\TextInput::make('cumulative_tax')
                                    ->label('Cumulative Tax')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->default(0)
                                    ->helperText('Tax amount from previous brackets'),
                            ]),
                    ]),

                Forms\Components\Section::make('Effective Period')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('effective_date')
                                    ->label('Effective Date')
                                    ->required()
                                    ->default(now()),
                                Forms\Components\DatePicker::make('end_date')
                                    ->label('End Date')
                                    ->placeholder('Leave empty for ongoing')
                                    ->after('effective_date'),
                            ]),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('bracket_name')
                    ->label('Bracket')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('income_range')
                    ->label('Income Range')
                    ->searchable(),
                Tables\Columns\TextColumn::make('tax_rate')
                    ->label('Tax Rate')
                    ->suffix('%')
                    ->sortable(),
                Tables\Columns\TextColumn::make('cumulative_tax')
                    ->label('Cumulative Tax')
                    ->money('IDR'),
                Tables\Columns\TextColumn::make('effective_date')
                    ->label('Effective Date')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_date')
                    ->label('End Date')
                    ->date('d/m/Y')
                    ->placeholder('Ongoing'),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->boolean()
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only')
                    ->native(false),
                Tables\Filters\Filter::make('effective_now')
                    ->label('Effective Now')
                    ->query(fn (Builder $query): Builder =>
                        $query->where('effective_date', '<=', now())
                              ->where(function ($q) {
                                  $q->whereNull('end_date')
                                    ->orWhere('end_date', '>=', now());
                              })
                    ),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('min_income');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTaxBrackets::route('/'),
            'create' => Pages\CreateTaxBracket::route('/create'),
            'view' => Pages\ViewTaxBracket::route('/{record}'),
            'edit' => Pages\EditTaxBracket::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
