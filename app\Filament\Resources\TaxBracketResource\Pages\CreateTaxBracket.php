<?php

namespace App\Filament\Resources\TaxBracketResource\Pages;

use App\Filament\Resources\TaxBracketResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateTaxBracket extends CreateRecord
{
    protected static string $resource = TaxBracketResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = auth()->id();
        
        return $data;
    }
}
