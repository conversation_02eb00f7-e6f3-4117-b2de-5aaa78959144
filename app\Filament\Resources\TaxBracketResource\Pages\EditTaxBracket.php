<?php

namespace App\Filament\Resources\TaxBracketResource\Pages;

use App\Filament\Resources\TaxBracketResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTaxBracket extends EditRecord
{
    protected static string $resource = TaxBracketResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
