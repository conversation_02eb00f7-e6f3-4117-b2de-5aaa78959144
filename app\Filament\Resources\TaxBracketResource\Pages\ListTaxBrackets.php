<?php

namespace App\Filament\Resources\TaxBracketResource\Pages;

use App\Filament\Resources\TaxBracketResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTaxBrackets extends ListRecords
{
    protected static string $resource = TaxBracketResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
