<?php

namespace App\Filament\Resources\TaxBracketResource\Pages;

use App\Filament\Resources\TaxBracketResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewTaxBracket extends ViewRecord
{
    protected static string $resource = TaxBracketResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
