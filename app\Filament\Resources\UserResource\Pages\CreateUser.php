<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Resources\Pages\CreateRecord;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;

    protected function afterCreate(): void
    {
        // Handle Shield role assignment after user creation
        UserResource::handleShieldRoles($this->record, $this->data);
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index'); // Kembali ke halaman tabel setelah menambahkan data
    }
}
