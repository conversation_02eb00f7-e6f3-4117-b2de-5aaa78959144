<?php

namespace App\Filament\Resources\UserResource\Widgets;

use App\Models\User;
use App\Models\Karyawan;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class UserStatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total User', $this->getTotalUsers())
                ->description('Jumlah seluruh user dalam sistem')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary')
                ->url('/admin/user-list'),

            Stat::make('Total Karyawan', $this->getTotalKaryawan())
                ->description('Jumlah seluruh data karyawan')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('success')
                ->url('/admin/karyawans'),

            Stat::make('User Tanpa Karyawan', $this->getUsersWithoutKaryawan())
                ->description('User yang belum terkait dengan karyawan')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('warning')
                ->url('/admin/user-list?tableFilters[karyawan_status][value]=unlinked'),

            Stat::make('Karyawan Tanpa User', $this->getKaryawanWithoutUser())
                ->description('Karyawan yang belum terkait dengan user')
                ->descriptionIcon('heroicon-m-user-minus')
                ->color('danger')
                ->url('/admin/karyawans?tableFilters[user_status][value]=unlinked'),
        ];
    }

    private function getTotalUsers(): int
    {
        return User::count();
    }

    private function getTotalKaryawan(): int
    {
        return Karyawan::count();
    }

    private function getUsersWithoutKaryawan(): int
    {
        return User::whereDoesntHave('karyawan')->count();
    }

    private function getKaryawanWithoutUser(): int
    {
        return Karyawan::whereNull('id_user')->count();
    }
}
