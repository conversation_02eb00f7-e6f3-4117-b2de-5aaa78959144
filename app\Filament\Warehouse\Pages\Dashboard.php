<?php

namespace App\Filament\Warehouse\Pages;

use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static string $view = 'filament-panels::pages.dashboard';

    protected static ?string $title = 'Warehouse Dashboard';

    protected static ?int $navigationSort = -2;

    public function getWidgets(): array
    {
        return [
            \App\Filament\Warehouse\Widgets\TotalProductsWidget::class,
            \App\Filament\Warehouse\Widgets\ActiveWarehousesWidget::class,
            \App\Filament\Warehouse\Widgets\LowStockAlertsWidget::class,
            \App\Filament\Warehouse\Widgets\PendingOrdersWidget::class,
            \App\Filament\Warehouse\Widgets\StockMovementChart::class,
        ];
    }

    public function getColumns(): int | string | array
    {
        return [
            'md' => 2,
            'xl' => 4,
        ];
    }
}
