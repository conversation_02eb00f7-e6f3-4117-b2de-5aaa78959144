<?php

namespace App\Filament\Warehouse\Pages;

use App\Models\InventoryStock;
use App\Models\Product;
use App\Models\Category;
use App\Models\Warehouse;
use Filament\Forms;
use Filament\Pages\Page;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Database\Eloquent\Builder;
use Filament\Actions;

class StockReport extends Page implements HasTable
{
    use InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';

    protected static string $view = 'filament.warehouse.pages.stock-report';

    protected static ?string $title = 'Stock Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 1;

    public $filters = [
        'warehouse_id' => null,
        'category_id' => null,
        'stock_status' => null,
        'date_from' => null,
        'date_to' => null,
    ];

    public function mount(): void
    {
        $this->filters['date_from'] = now()->startOfMonth()->format('Y-m-d');
        $this->filters['date_to'] = now()->format('Y-m-d');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('export')
                ->label('Export CSV')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->action(function () {
                    return $this->exportToCsv();
                }),
            Actions\Action::make('print')
                ->label('Print Report')
                ->icon('heroicon-o-printer')
                ->color('gray')
                ->url(fn () => route('warehouse.reports.stock.print', $this->filters))
                ->openUrlInNewTab(),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                Tables\Columns\TextColumn::make('product.kode')
                    ->label('Product Code')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.nama')
                    ->label('Product Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.kategori.nama')
                    ->label('Category')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('warehouse.name')
                    ->label('Warehouse')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('quantity')
                    ->label('Total Qty')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('available_quantity')
                    ->label('Available')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('on_hold_quantity')
                    ->label('On Hold')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reserved_quantity')
                    ->label('Reserved')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('minimum_stock')
                    ->label('Min Stock')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('average_cost')
                    ->label('Avg Cost')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_value')
                    ->label('Total Value')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('stock_status')
                    ->label('Status')
                    ->getStateUsing(function (InventoryStock $record) {
                        if ($record->quantity <= 0) {
                            return 'Out of Stock';
                        } elseif ($record->available_quantity <= $record->minimum_stock && $record->minimum_stock > 0) {
                            return 'Low Stock';
                        } elseif ($record->available_quantity <= $record->reorder_point && $record->reorder_point > 0) {
                            return 'Reorder Required';
                        } elseif ($record->quantity >= $record->maximum_stock && $record->maximum_stock > 0) {
                            return 'Over Stock';
                        } else {
                            return 'Normal';
                        }
                    })
                    ->colors([
                        'success' => 'Normal',
                        'warning' => 'Low Stock',
                        'danger' => ['Out of Stock', 'Reorder Required'],
                        'info' => 'Over Stock',
                    ]),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('warehouse_id')
                    ->label('Warehouse')
                    ->options(Warehouse::where('is_active', true)->pluck('name', 'id'))
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('category_id')
                    ->label('Category')
                    ->options(Category::pluck('name', 'id'))
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('stock_status')
                    ->label('Stock Status')
                    ->options([
                        'normal' => 'Normal',
                        'low' => 'Low Stock',
                        'reorder' => 'Reorder Required',
                        'overstock' => 'Over Stock',
                        'out_of_stock' => 'Out of Stock',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\Action::make('filter')
                    ->label('Advanced Filters')
                    ->icon('heroicon-o-funnel')
                    ->form([
                        Forms\Components\DatePicker::make('date_from')
                            ->label('From Date')
                            ->default($this->filters['date_from']),
                        Forms\Components\DatePicker::make('date_to')
                            ->label('To Date')
                            ->default($this->filters['date_to']),
                        Forms\Components\Select::make('warehouse_id')
                            ->label('Warehouse')
                            ->options(Warehouse::where('is_active', true)->pluck('name', 'id'))
                            ->searchable()
                            ->preload(),
                        Forms\Components\Select::make('category_id')
                            ->label('Category')
                            ->options(Category::pluck('name', 'id'))
                            ->searchable()
                            ->preload(),
                    ])
                    ->action(function (array $data) {
                        $this->filters = array_merge($this->filters, $data);
                        $this->resetTable();
                    }),
            ])
            ->defaultSort('product.nama')
            ->striped()
            ->paginated([25, 50, 100]);
    }

    protected function getTableQuery(): Builder
    {
        $query = InventoryStock::query()
            ->with(['product.kategori', 'warehouse', 'entitas']);

        // Apply filters
        if ($this->filters['warehouse_id']) {
            $query->where('warehouse_id', $this->filters['warehouse_id']);
        }

        if ($this->filters['category_id']) {
            $query->whereHas('product', function ($q) {
                $q->where('category_id', $this->filters['category_id']);
            });
        }

        if ($this->filters['stock_status']) {
            switch ($this->filters['stock_status']) {
                case 'low':
                    $query->whereColumn('available_quantity', '<=', 'minimum_stock')
                          ->where('minimum_stock', '>', 0);
                    break;
                case 'reorder':
                    $query->whereColumn('available_quantity', '<=', 'reorder_point')
                          ->where('reorder_point', '>', 0);
                    break;
                case 'overstock':
                    $query->whereColumn('quantity', '>=', 'maximum_stock')
                          ->where('maximum_stock', '>', 0);
                    break;
                case 'out_of_stock':
                    $query->where('quantity', '<=', 0);
                    break;
                case 'normal':
                    $query->where('quantity', '>', 0)
                          ->where(function ($q) {
                              $q->whereColumn('available_quantity', '>', 'minimum_stock')
                                ->orWhere('minimum_stock', '=', 0);
                          });
                    break;
            }
        }

        return $query;
    }

    protected function exportToCsv()
    {
        $data = $this->getTableQuery()->get();
        
        $filename = 'stock_report_' . now()->format('Y_m_d_H_i_s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Product Code',
                'Product Name',
                'Category',
                'Warehouse',
                'Total Qty',
                'Available',
                'On Hold',
                'Reserved',
                'Min Stock',
                'Avg Cost',
                'Total Value',
                'Status'
            ]);

            // CSV data
            foreach ($data as $record) {
                $status = 'Normal';
                if ($record->quantity <= 0) {
                    $status = 'Out of Stock';
                } elseif ($record->available_quantity <= $record->minimum_stock && $record->minimum_stock > 0) {
                    $status = 'Low Stock';
                } elseif ($record->available_quantity <= $record->reorder_point && $record->reorder_point > 0) {
                    $status = 'Reorder Required';
                } elseif ($record->quantity >= $record->maximum_stock && $record->maximum_stock > 0) {
                    $status = 'Over Stock';
                }

                fputcsv($file, [
                    $record->product->kode,
                    $record->product->nama,
                    $record->product->kategori->nama ?? '',
                    $record->warehouse->name,
                    $record->quantity,
                    $record->available_quantity,
                    $record->on_hold_quantity,
                    $record->reserved_quantity,
                    $record->minimum_stock,
                    $record->average_cost,
                    $record->total_value,
                    $status,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
