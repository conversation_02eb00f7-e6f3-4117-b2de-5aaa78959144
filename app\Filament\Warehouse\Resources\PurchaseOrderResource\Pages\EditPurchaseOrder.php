<?php

namespace App\Filament\Warehouse\Resources\PurchaseOrderResource\Pages;

use App\Filament\Warehouse\Resources\PurchaseOrderResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPurchaseOrder extends EditRecord
{
    protected static string $resource = PurchaseOrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Calculate totals
        $subtotal = 0;
        if (isset($data['purchaseOrderItems'])) {
            foreach ($data['purchaseOrderItems'] as $item) {
                $subtotal += $item['total_price'] ?? 0;
            }
        }
        
        $data['subtotal'] = $subtotal;
        $data['total_amount'] = $subtotal + ($data['tax_amount'] ?? 0) - ($data['discount_amount'] ?? 0);
        
        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
