<?php

namespace App\Filament\Warehouse\Resources\SalesOrderResource\Pages;

use App\Filament\Warehouse\Resources\SalesOrderResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateSalesOrder extends CreateRecord
{
    protected static string $resource = SalesOrderResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = auth()->id();
        
        // Calculate totals
        $subtotal = 0;
        if (isset($data['salesOrderItems'])) {
            foreach ($data['salesOrderItems'] as $item) {
                $subtotal += $item['total_price'] ?? 0;
            }
        }
        
        $data['subtotal'] = $subtotal;
        $data['total_amount'] = $subtotal + ($data['tax_amount'] ?? 0) - ($data['discount_amount'] ?? 0);
        
        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
