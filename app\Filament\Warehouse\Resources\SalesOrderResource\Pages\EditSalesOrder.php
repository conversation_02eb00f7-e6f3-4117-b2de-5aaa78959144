<?php

namespace App\Filament\Warehouse\Resources\SalesOrderResource\Pages;

use App\Filament\Warehouse\Resources\SalesOrderResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditSalesOrder extends EditRecord
{
    protected static string $resource = SalesOrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Calculate totals
        $subtotal = 0;
        if (isset($data['salesOrderItems'])) {
            foreach ($data['salesOrderItems'] as $item) {
                $subtotal += $item['total_price'] ?? 0;
            }
        }
        
        $data['subtotal'] = $subtotal;
        $data['total_amount'] = $subtotal + ($data['tax_amount'] ?? 0) - ($data['discount_amount'] ?? 0);
        
        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
