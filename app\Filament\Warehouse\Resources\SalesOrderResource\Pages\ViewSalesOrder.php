<?php

namespace App\Filament\Warehouse\Resources\SalesOrderResource\Pages;

use App\Filament\Warehouse\Resources\SalesOrderResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewSalesOrder extends ViewRecord
{
    protected static string $resource = SalesOrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('approve')
                ->label('Approve')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn ($record) => $record->canBeApproved())
                ->action(function ($record) {
                    $record->status = 'Approved';
                    $record->approved_by = auth()->id();
                    $record->approved_at = now();
                    $record->save();
                    
                    $this->notify('success', 'Sales Order approved successfully');
                }),
            Actions\Action::make('cancel')
                ->label('Cancel')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->requiresConfirmation()
                ->visible(fn ($record) => $record->canBeCancelled())
                ->action(function ($record) {
                    $record->status = 'Cancelled';
                    $record->save();
                    
                    $this->notify('success', 'Sales Order cancelled successfully');
                }),
        ];
    }
}
