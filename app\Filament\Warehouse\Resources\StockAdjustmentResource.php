<?php

namespace App\Filament\Warehouse\Resources;

use App\Filament\Warehouse\Resources\StockAdjustmentResource\Pages;
use App\Models\StockAdjustment;
use App\Models\Warehouse;
use App\Models\Entitas;
use App\Models\Produk;
use App\Models\InventoryStock;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class StockAdjustmentResource extends Resource
{
    protected static ?string $model = StockAdjustment::class;

    protected static ?string $navigationIcon = 'heroicon-o-adjustments-horizontal';

    protected static ?string $navigationGroup = 'Inventory Management';

    protected static ?int $navigationSort = 2;

    protected static ?string $navigationLabel = 'Stock Adjustment';

    protected static ?string $modelLabel = 'Stock Adjustment';

    protected static ?string $pluralModelLabel = 'Stock Adjustments';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Adjustment Information')
                    ->schema([
                        Forms\Components\TextInput::make('adjustment_number')
                            ->label('Adjustment Number')
                            ->disabled()
                            ->dehydrated(false)
                            ->placeholder('Auto-generated'),
                        Forms\Components\DatePicker::make('adjustment_date')
                            ->label('Adjustment Date')
                            ->required()
                            ->default(now()),
                        Forms\Components\Select::make('adjustment_type')
                            ->label('Adjustment Type')
                            ->options([
                                'Increase' => 'Increase',
                                'Decrease' => 'Decrease',
                            ])
                            ->required(),
                        Forms\Components\TextInput::make('adjustment_reason')
                            ->label('Adjustment Reason')
                            ->required()
                            ->placeholder('e.g., Damaged goods, Found items, Stock count discrepancy'),
                        Forms\Components\Select::make('warehouse_id')
                            ->label('Warehouse')
                            ->options(Warehouse::where('is_active', true)->pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\Select::make('entitas_id')
                            ->label('Entitas')
                            ->options(Entitas::where('is_active', true)->pluck('nama', 'id'))
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'Draft' => 'Draft',
                                'Submitted' => 'Submitted',
                                'Approved' => 'Approved',
                                'Cancelled' => 'Cancelled',
                            ])
                            ->default('Draft')
                            ->required(),
                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ])
                    ->columns(3),
                Forms\Components\Section::make('Adjustment Items')
                    ->schema([
                        Forms\Components\Repeater::make('stockAdjustmentItems')
                            ->label('Items')
                            ->relationship()
                            ->schema([
                                Forms\Components\Select::make('product_id')
                                    ->label('Product')
                                    ->options(Produk::with('kategori')->get()->pluck('nama', 'id'))
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, Forms\Set $set, Forms\Get $get) {
                                        if ($state) {
                                            $warehouseId = $get('../../warehouse_id');
                                            $entitasId = $get('../../entitas_id');
                                            
                                            if ($warehouseId && $entitasId) {
                                                $stock = InventoryStock::where('product_id', $state)
                                                    ->where('warehouse_id', $warehouseId)
                                                    ->where('entitas_id', $entitasId)
                                                    ->first();
                                                
                                                if ($stock) {
                                                    $set('system_quantity', $stock->quantity);
                                                    $set('unit_cost', $stock->average_cost);
                                                } else {
                                                    $set('system_quantity', 0);
                                                    $product = Produk::find($state);
                                                    $set('unit_cost', $product ? $product->unit_cost : 0);
                                                }
                                            }
                                        }
                                    }),
                                Forms\Components\TextInput::make('system_quantity')
                                    ->label('System Qty')
                                    ->numeric()
                                    ->disabled()
                                    ->dehydrated(),
                                Forms\Components\TextInput::make('physical_quantity')
                                    ->label('Physical Qty')
                                    ->numeric()
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, Forms\Get $get, Forms\Set $set) {
                                        $systemQty = $get('system_quantity') ?? 0;
                                        $adjustment = $state - $systemQty;
                                        $set('quantity_adjustment', $adjustment);
                                        
                                        $unitCost = $get('unit_cost') ?? 0;
                                        $set('total_adjustment_value', $adjustment * $unitCost);
                                    }),
                                Forms\Components\TextInput::make('quantity_adjustment')
                                    ->label('Adjustment')
                                    ->numeric()
                                    ->disabled()
                                    ->dehydrated()
                                    ->formatStateUsing(function ($state) {
                                        return ($state > 0 ? '+' : '') . $state;
                                    }),
                                Forms\Components\TextInput::make('unit_cost')
                                    ->label('Unit Cost')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, Forms\Get $get, Forms\Set $set) {
                                        $adjustment = $get('quantity_adjustment') ?? 0;
                                        $set('total_adjustment_value', $adjustment * $state);
                                    }),
                                Forms\Components\TextInput::make('total_adjustment_value')
                                    ->label('Total Value')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->disabled()
                                    ->dehydrated(),
                                Forms\Components\Textarea::make('item_notes')
                                    ->label('Item Notes')
                                    ->maxLength(500),
                            ])
                            ->columns(7)
                            ->defaultItems(1)
                            ->addActionLabel('Add Item')
                            ->reorderableWithButtons()
                            ->collapsible(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('adjustment_number')
                    ->label('Adjustment #')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('adjustment_date')
                    ->label('Date')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('adjustment_type')
                    ->label('Type')
                    ->colors([
                        'success' => 'Increase',
                        'danger' => 'Decrease',
                    ]),
                Tables\Columns\TextColumn::make('adjustment_reason')
                    ->label('Reason')
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('warehouse.name')
                    ->label('Warehouse')
                    ->searchable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status')
                    ->colors([
                        'secondary' => 'Draft',
                        'warning' => 'Submitted',
                        'success' => 'Approved',
                        'danger' => 'Cancelled',
                    ]),
                Tables\Columns\TextColumn::make('stockAdjustmentItems_count')
                    ->label('Items')
                    ->counts('stockAdjustmentItems')
                    ->badge(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('adjustment_type')
                    ->label('Type')
                    ->options([
                        'Increase' => 'Increase',
                        'Decrease' => 'Decrease',
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'Draft' => 'Draft',
                        'Submitted' => 'Submitted',
                        'Approved' => 'Approved',
                        'Cancelled' => 'Cancelled',
                    ]),
                Tables\Filters\SelectFilter::make('warehouse_id')
                    ->label('Warehouse')
                    ->options(Warehouse::pluck('name', 'id'))
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => $record->status === 'Draft'),
                Tables\Actions\Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->requiresConfirmation()
                    ->visible(fn ($record) => $record->status === 'Submitted')
                    ->action(function ($record) {
                        $record->status = 'Approved';
                        $record->approved_by = auth()->id();
                        $record->approved_at = now();
                        $record->save();
                        
                        // Process the adjustment
                        $record->processAdjustment();
                    }),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => $record->status === 'Draft'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('adjustment_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStockAdjustments::route('/'),
            'create' => Pages\CreateStockAdjustment::route('/create'),
            'view' => Pages\ViewStockAdjustment::route('/{record}'),
            'edit' => Pages\EditStockAdjustment::route('/{record}/edit'),
        ];
    }
}
