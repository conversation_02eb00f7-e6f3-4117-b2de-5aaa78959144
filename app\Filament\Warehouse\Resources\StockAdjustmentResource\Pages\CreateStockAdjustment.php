<?php

namespace App\Filament\Warehouse\Resources\StockAdjustmentResource\Pages;

use App\Filament\Warehouse\Resources\StockAdjustmentResource;
use App\Models\StockAdjustment;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateStockAdjustment extends CreateRecord
{
    protected static string $resource = StockAdjustmentResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = auth()->id();
        
        // Auto-generate adjustment number if not provided
        if (empty($data['adjustment_number'])) {
            $data['adjustment_number'] = StockAdjustment::generateAdjustmentNumber();
        }
        
        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
