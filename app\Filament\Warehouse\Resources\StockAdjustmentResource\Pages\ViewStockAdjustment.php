<?php

namespace App\Filament\Warehouse\Resources\StockAdjustmentResource\Pages;

use App\Filament\Warehouse\Resources\StockAdjustmentResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewStockAdjustment extends ViewRecord
{
    protected static string $resource = StockAdjustmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn ($record) => $record->status === 'Draft'),
            Actions\Action::make('submit')
                ->label('Submit for Approval')
                ->icon('heroicon-o-paper-airplane')
                ->color('warning')
                ->requiresConfirmation()
                ->visible(fn ($record) => $record->status === 'Draft')
                ->action(function ($record) {
                    $record->status = 'Submitted';
                    $record->save();
                    
                    $this->notify('success', 'Stock Adjustment submitted for approval');
                }),
            Actions\Action::make('approve')
                ->label('Approve')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn ($record) => $record->status === 'Submitted')
                ->action(function ($record) {
                    $record->status = 'Approved';
                    $record->approved_by = auth()->id();
                    $record->approved_at = now();
                    $record->save();
                    
                    // Process the adjustment
                    $record->processAdjustment();
                    
                    $this->notify('success', 'Stock Adjustment approved and processed');
                }),
            Actions\Action::make('cancel')
                ->label('Cancel')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->requiresConfirmation()
                ->visible(fn ($record) => in_array($record->status, ['Draft', 'Submitted']))
                ->action(function ($record) {
                    $record->status = 'Cancelled';
                    $record->save();
                    
                    $this->notify('success', 'Stock Adjustment cancelled');
                }),
        ];
    }
}
