<?php

namespace App\Filament\Warehouse\Resources\StockMovementResource\Pages;

use App\Filament\Warehouse\Resources\StockMovementResource;
use App\Models\InventoryStock;
use App\Models\StockMovement;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateStockMovement extends CreateRecord
{
    protected static string $resource = StockMovementResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = auth()->id();
        
        // Auto-generate movement number if not provided
        if (empty($data['movement_number'])) {
            $data['movement_number'] = StockMovement::generateMovementNumber();
        }
        
        return $data;
    }

    protected function afterCreate(): void
    {
        $record = $this->record;
        
        // Update inventory stock if this is an adjustment
        if (in_array($record->movement_type, ['Adjustment_In', 'Adjustment_Out'])) {
            $this->updateInventoryStock($record);
        }
    }

    protected function updateInventoryStock(StockMovement $movement): void
    {
        $inventoryStock = InventoryStock::where('product_id', $movement->product_id)
            ->where('warehouse_id', $movement->warehouse_id)
            ->where('entitas_id', $movement->entitas_id)
            ->first();

        if (!$inventoryStock) {
            // Create new inventory stock record if it doesn't exist
            InventoryStock::create([
                'product_id' => $movement->product_id,
                'warehouse_id' => $movement->warehouse_id,
                'entitas_id' => $movement->entitas_id,
                'quantity' => max(0, $movement->quantity),
                'available_quantity' => max(0, $movement->quantity),
                'on_hold_quantity' => 0,
                'reserved_quantity' => 0,
                'average_cost' => $movement->unit_cost,
                'total_value' => max(0, $movement->quantity) * $movement->unit_cost,
                'minimum_stock' => 0,
                'maximum_stock' => 0,
                'last_movement_at' => $movement->movement_date,
                'last_movement_type' => $movement->movement_type,
            ]);
        } else {
            // Update existing inventory stock
            $newQuantity = max(0, $inventoryStock->quantity + $movement->quantity);
            $newAvailableQuantity = max(0, $inventoryStock->available_quantity + $movement->quantity);
            
            $inventoryStock->quantity = $newQuantity;
            $inventoryStock->available_quantity = $newAvailableQuantity;
            $inventoryStock->last_movement_at = $movement->movement_date;
            $inventoryStock->last_movement_type = $movement->movement_type;
            $inventoryStock->updateTotalValue();
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
