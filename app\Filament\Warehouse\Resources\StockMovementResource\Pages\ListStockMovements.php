<?php

namespace App\Filament\Warehouse\Resources\StockMovementResource\Pages;

use App\Filament\Warehouse\Resources\StockMovementResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListStockMovements extends ListRecords
{
    protected static string $resource = StockMovementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Create Adjustment'),
        ];
    }
}
