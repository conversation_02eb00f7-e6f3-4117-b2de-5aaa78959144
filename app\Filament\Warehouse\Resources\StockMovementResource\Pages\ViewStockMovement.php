<?php

namespace App\Filament\Warehouse\Resources\StockMovementResource\Pages;

use App\Filament\Warehouse\Resources\StockMovementResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewStockMovement extends ViewRecord
{
    protected static string $resource = StockMovementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn ($record) => in_array($record->movement_type, ['Adjustment_In', 'Adjustment_Out'])),
        ];
    }
}
