<?php

namespace App\Filament\Warehouse\Widgets;

use App\Models\Warehouse;
use App\Models\InventoryStock;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ActiveWarehousesWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalWarehouses = Warehouse::count();
        $activeWarehouses = Warehouse::where('is_active', true)->count();
        $totalStockValue = InventoryStock::sum('total_value');

        return [
            Stat::make('Total Warehouses', $totalWarehouses)
                ->description('All warehouses')
                ->descriptionIcon('heroicon-m-building-storefront')
                ->color('primary'),
            
            Stat::make('Active Warehouses', $activeWarehouses)
                ->description('Currently active')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),
            
            Stat::make('Total Stock Value', 'Rp ' . number_format($totalStockValue, 0, ',', '.'))
                ->description('Total inventory value')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('warning'),
        ];
    }
}
