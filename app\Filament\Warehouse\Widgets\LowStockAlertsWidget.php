<?php

namespace App\Filament\Warehouse\Widgets;

use App\Models\InventoryStock;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class LowStockAlertsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $lowStockCount = InventoryStock::whereColumn('available_quantity', '<=', 'minimum_stock')
            ->where('minimum_stock', '>', 0)
            ->count();
        
        $reorderRequiredCount = InventoryStock::whereColumn('available_quantity', '<=', 'reorder_point')
            ->where('reorder_point', '>', 0)
            ->count();
        
        $overStockCount = InventoryStock::whereColumn('quantity', '>=', 'maximum_stock')
            ->where('maximum_stock', '>', 0)
            ->count();

        return [
            Stat::make('Low Stock Items', $lowStockCount)
                ->description('Below minimum stock')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('danger'),
            
            Stat::make('Reorder Required', $reorderRequiredCount)
                ->description('Below reorder point')
                ->descriptionIcon('heroicon-m-arrow-path')
                ->color('warning'),
            
            Stat::make('Overstock Items', $overStockCount)
                ->description('Above maximum stock')
                ->descriptionIcon('heroicon-m-arrow-up-circle')
                ->color('info'),
        ];
    }
}
