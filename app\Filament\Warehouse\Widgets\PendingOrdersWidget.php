<?php

namespace App\Filament\Warehouse\Widgets;

use App\Models\PurchaseOrder;
use App\Models\SalesOrder;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class PendingOrdersWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $pendingPurchaseOrders = PurchaseOrder::whereIn('status', ['Draft', 'Submitted'])->count();
        $pendingSalesOrders = SalesOrder::whereIn('status', ['Draft', 'Submitted'])->count();
        $approvedPurchaseOrders = PurchaseOrder::where('status', 'Approved')->count();
        $approvedSalesOrders = SalesOrder::where('status', 'Approved')->count();

        return [
            Stat::make('Pending Purchase Orders', $pendingPurchaseOrders)
                ->description('Awaiting approval')
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('warning'),
            
            Stat::make('Pending Sales Orders', $pendingSalesOrders)
                ->description('Awaiting approval')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('warning'),
            
            Stat::make('Approved Purchase Orders', $approvedPurchaseOrders)
                ->description('Ready for receipt')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),
            
            Stat::make('Approved Sales Orders', $approvedSalesOrders)
                ->description('Ready for issue')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),
        ];
    }
}
