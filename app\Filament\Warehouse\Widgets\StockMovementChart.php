<?php

namespace App\Filament\Warehouse\Widgets;

use App\Models\StockMovement;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class StockMovementChart extends ChartWidget
{
    protected static ?string $heading = 'Stock Movement Trend (Last 30 Days)';

    protected static ?int $sort = 5;

    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        $endDate = Carbon::now();
        $startDate = $endDate->copy()->subDays(29);

        $dates = [];
        $incomingData = [];
        $outgoingData = [];

        for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
            $dates[] = $date->format('M d');
            
            $incoming = StockMovement::whereDate('movement_date', $date)
                ->whereIn('movement_type', ['Purchase_Receipt', 'Transfer_In', 'Adjustment_In', 'Production_In'])
                ->sum('quantity');
            
            $outgoing = abs(StockMovement::whereDate('movement_date', $date)
                ->whereIn('movement_type', ['Sales_Issue', 'Transfer_Out', 'Adjustment_Out', 'Production_Out'])
                ->sum('quantity'));
            
            $incomingData[] = $incoming;
            $outgoingData[] = $outgoing;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Incoming Stock',
                    'data' => $incomingData,
                    'backgroundColor' => 'rgba(34, 197, 94, 0.2)',
                    'borderColor' => 'rgba(34, 197, 94, 1)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
                [
                    'label' => 'Outgoing Stock',
                    'data' => $outgoingData,
                    'backgroundColor' => 'rgba(239, 68, 68, 0.2)',
                    'borderColor' => 'rgba(239, 68, 68, 1)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
            ],
            'labels' => $dates,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                ],
            ],
        ];
    }
}
