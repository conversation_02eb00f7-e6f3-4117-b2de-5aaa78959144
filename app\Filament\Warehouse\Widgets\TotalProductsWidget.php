<?php

namespace App\Filament\Warehouse\Widgets;

use App\Models\Product;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class TotalProductsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalProducts = Product::count();
        $activeProducts = Product::where('stock_quantity', '>', 0)->count();
        $outOfStockProducts = Product::where('stock_quantity', '<=', 0)->count();

        return [
            Stat::make('Total Products', $totalProducts)
                ->description('Total products in system')
                ->descriptionIcon('heroicon-m-cube')
                ->color('primary'),
            
            Stat::make('Active Products', $activeProducts)
                ->description('Products with stock')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),
            
            Stat::make('Out of Stock', $outOfStockProducts)
                ->description('Products without stock')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('danger'),
        ];
    }
}
