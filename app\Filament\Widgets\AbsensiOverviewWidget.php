<?php

namespace App\Filament\Widgets;

use App\Models\Absensi;
use App\Models\Karyawan;
use App\Models\Schedule;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class AbsensiOverviewWidget extends BaseWidget
{
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;
    protected static ?int $sort = 1;
    protected int | string | array $columnSpan = 'full';

    protected function getStats(): array
    {
        return [
            // Today's Attendance
            Stat::make('Kehadiran Hari Ini', $this->getTodayAttendance())
                ->description($this->getTodayAttendanceDescription())
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('primary'),

            // This Week Attendance Rate
            Stat::make('Tingkat Kehadiran Minggu Ini', $this->getWeeklyAttendanceRate())
                ->description($this->getWeeklyTrend())
                ->descriptionIcon($this->getWeeklyTrendIcon())
                ->color($this->getWeeklyTrendColor()),

            // This Month Attendance Rate
            Stat::make('Tingkat Kehadiran Bulan Ini', $this->getMonthlyAttendanceRate())
                ->description('Bulan ' . now()->format('F Y'))
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('success'),

            // Late Arrivals Today
            Stat::make('Terlambat Hari Ini', $this->getTodayLateArrivals())
                ->description($this->getLateArrivalsDescription())
                ->descriptionIcon('heroicon-m-clock')
                ->color($this->getLateArrivalsColor()),

            // Absent Today
            Stat::make('Tidak Hadir Hari Ini', $this->getTodayAbsent())
                ->description($this->getAbsentDescription())
                ->descriptionIcon('heroicon-m-x-circle')
                ->color('danger'),

            // On Leave Today
            Stat::make('Izin/Cuti Hari Ini', $this->getTodayOnLeave())
                ->description('Izin, Sakit, Cuti')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('warning'),

            // Early Departures
            Stat::make('Pulang Cepat Minggu Ini', $this->getEarlyDepartures())
                ->description('Pulang sebelum jam kerja selesai')
                ->descriptionIcon('heroicon-m-arrow-right-on-rectangle')
                ->color('warning'),

            // Perfect Attendance This Month
            Stat::make('Perfect Attendance', $this->getPerfectAttendance())
                ->description('Tidak pernah terlambat/absent bulan ini')
                ->descriptionIcon('heroicon-m-star')
                ->color('success'),
        ];
    }

    private function getTodayAttendance(): string
    {
        $totalScheduled = Schedule::whereDate('tanggal_jadwal', today())->count();
        $totalPresent = Absensi::whereDate('tanggal_absensi', today())
            ->whereIn('status', ['hadir', 'terlambat'])
            ->count();

        return "{$totalPresent}/{$totalScheduled}";
    }

    private function getTodayAttendanceDescription(): string
    {
        $totalScheduled = Schedule::whereDate('tanggal_jadwal', today())->count();
        $totalPresent = Absensi::whereDate('tanggal_absensi', today())
            ->whereIn('status', ['hadir', 'terlambat'])
            ->count();

        if ($totalScheduled === 0) {
            return 'Tidak ada jadwal hari ini';
        }

        $percentage = round(($totalPresent / $totalScheduled) * 100, 1);
        return "{$percentage}% dari yang dijadwalkan";
    }

    private function getWeeklyAttendanceRate(): string
    {
        $startOfWeek = now()->startOfWeek();
        $endOfWeek = now()->endOfWeek();

        $totalAbsensi = Absensi::whereBetween('tanggal_absensi', [
            $startOfWeek->format('Y-m-d'),
            $endOfWeek->format('Y-m-d')
        ])->count();

        $presentCount = Absensi::whereBetween('tanggal_absensi', [
            $startOfWeek->format('Y-m-d'),
            $endOfWeek->format('Y-m-d')
        ])->whereIn('status', ['hadir', 'terlambat'])->count();

        if ($totalAbsensi === 0) {
            return 'N/A';
        }

        $rate = ($presentCount / $totalAbsensi) * 100;
        return number_format($rate, 1) . '%';
    }

    private function getWeeklyTrend(): string
    {
        $thisWeek = $this->getWeekAttendanceRate(now()->startOfWeek(), now()->endOfWeek());
        $lastWeek = $this->getWeekAttendanceRate(
            now()->subWeek()->startOfWeek(),
            now()->subWeek()->endOfWeek()
        );

        if ($thisWeek === 0 || $lastWeek === 0) {
            return 'Data tidak cukup';
        }

        $diff = round($thisWeek - $lastWeek, 1);

        if ($diff > 0) {
            return "Naik {$diff}% dari minggu lalu";
        } elseif ($diff < 0) {
            return "Turun " . abs($diff) . "% dari minggu lalu";
        } else {
            return "Sama dengan minggu lalu";
        }
    }

    private function getWeeklyTrendIcon(): string
    {
        $thisWeek = $this->getWeekAttendanceRate(now()->startOfWeek(), now()->endOfWeek());
        $lastWeek = $this->getWeekAttendanceRate(
            now()->subWeek()->startOfWeek(),
            now()->subWeek()->endOfWeek()
        );

        if ($thisWeek === 0 || $lastWeek === 0) {
            return 'heroicon-m-minus';
        }

        return $thisWeek >= $lastWeek ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down';
    }

    private function getWeeklyTrendColor(): string
    {
        $thisWeek = $this->getWeekAttendanceRate(now()->startOfWeek(), now()->endOfWeek());
        $lastWeek = $this->getWeekAttendanceRate(
            now()->subWeek()->startOfWeek(),
            now()->subWeek()->endOfWeek()
        );

        if ($thisWeek === 0 || $lastWeek === 0) {
            return 'gray';
        }

        return $thisWeek >= $lastWeek ? 'success' : 'danger';
    }

    private function getWeekAttendanceRate($startDate, $endDate): float
    {
        $totalAbsensi = Absensi::whereBetween('tanggal_absensi', [
            $startDate->format('Y-m-d'),
            $endDate->format('Y-m-d')
        ])->count();

        $presentCount = Absensi::whereBetween('tanggal_absensi', [
            $startDate->format('Y-m-d'),
            $endDate->format('Y-m-d')
        ])->whereIn('status', ['hadir', 'terlambat'])->count();

        if ($totalAbsensi === 0) {
            return 0;
        }

        return ($presentCount / $totalAbsensi) * 100;
    }

    private function getMonthlyAttendanceRate(): string
    {
        $totalAbsensi = Absensi::whereMonth('tanggal_absensi', now()->month)->count();
        $presentCount = Absensi::whereMonth('tanggal_absensi', now()->month)
            ->whereIn('status', ['hadir', 'terlambat'])
            ->count();

        if ($totalAbsensi === 0) {
            return 'N/A';
        }

        $rate = ($presentCount / $totalAbsensi) * 100;
        return number_format($rate, 1) . '%';
    }

    private function getTodayLateArrivals(): int
    {
        return Absensi::whereDate('tanggal_absensi', today())
            ->where('status', 'terlambat')
            ->count();
    }

    private function getLateArrivalsDescription(): string
    {
        $todayLate = $this->getTodayLateArrivals();
        $yesterdayLate = Absensi::whereDate('tanggal_absensi', now()->subDay())
            ->where('status', 'terlambat')
            ->count();

        $diff = $todayLate - $yesterdayLate;

        if ($diff > 0) {
            return "Naik {$diff} dari kemarin";
        } elseif ($diff < 0) {
            return "Turun " . abs($diff) . " dari kemarin";
        } else {
            return "Sama dengan kemarin";
        }
    }

    private function getLateArrivalsColor(): string
    {
        $count = $this->getTodayLateArrivals();

        if ($count === 0) {
            return 'success';
        } elseif ($count <= 5) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    private function getTodayAbsent(): int
    {
        return Absensi::whereDate('tanggal_absensi', today())
            ->where('status', 'alpha')
            ->count();
    }

    private function getAbsentDescription(): string
    {
        $totalScheduled = Schedule::whereDate('tanggal_jadwal', today())->count();
        $totalAbsent = $this->getTodayAbsent();

        if ($totalScheduled === 0) {
            return 'Tidak ada jadwal';
        }

        $percentage = round(($totalAbsent / $totalScheduled) * 100, 1);
        return "{$percentage}% dari yang dijadwalkan";
    }

    private function getTodayOnLeave(): int
    {
        return Absensi::whereDate('tanggal_absensi', today())
            ->whereIn('status', ['izin', 'sakit', 'cuti'])
            ->count();
    }

    private function getEarlyDepartures(): int
    {
        $startOfWeek = now()->startOfWeek();
        $endOfWeek = now()->endOfWeek();

        // Assuming we track early departures (you might need to add this field)
        // For now, let's use a placeholder calculation
        return Absensi::whereBetween('tanggal_absensi', [
            $startOfWeek->format('Y-m-d'),
            $endOfWeek->format('Y-m-d')
        ])
            ->whereNotNull('waktu_keluar')
            ->whereTime('waktu_keluar', '<', '17:00:00') // Assuming 5 PM is normal end time
            ->count();
    }

    private function getPerfectAttendance(): int
    {
        return Karyawan::whereHas('absensi', function ($query) {
            $query->whereMonth('tanggal_absensi', now()->month)
                ->where('status', 'hadir');
        })
            ->whereDoesntHave('absensi', function ($query) {
                $query->whereMonth('tanggal_absensi', now()->month)
                    ->whereIn('status', ['terlambat', 'alpha', 'izin', 'sakit']);
            })
            ->count();
    }
}
