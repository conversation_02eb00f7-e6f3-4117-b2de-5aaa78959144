<?php

namespace App\Filament\Widgets;

use App\Models\Absensi;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class AbsensiStatusWidget extends ChartWidget
{
    protected static ?string $heading = 'Distribusi Status Absensi';
    protected static ?int $sort = 5;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'this_month';

    protected function getFilters(): ?array
    {
        return [
            'this_month' => 'Bulan Ini',
            'last_month' => 'Bulan Lalu',
            'this_week' => 'Minggu Ini',
            'today' => 'Hari Ini',
        ];
    }

    protected function getData(): array
    {
        return match ($this->filter) {
            'this_month' => $this->getThisMonthData(),
            'last_month' => $this->getLastMonthData(),
            'this_week' => $this->getThisWeekData(),
            'today' => $this->getTodayData(),
            default => $this->getThisMonthData(),
        };
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    private function getThisMonthData(): array
    {
        return $this->getStatusData(
            now()->startOfMonth(),
            now()->endOfMonth()
        );
    }

    private function getLastMonthData(): array
    {
        return $this->getStatusData(
            now()->subMonth()->startOfMonth(),
            now()->subMonth()->endOfMonth()
        );
    }

    private function getThisWeekData(): array
    {
        return $this->getStatusData(
            now()->startOfWeek(),
            now()->endOfWeek()
        );
    }

    private function getTodayData(): array
    {
        return $this->getStatusData(
            now()->startOfDay(),
            now()->endOfDay()
        );
    }

    private function getStatusData(Carbon $startDate, Carbon $endDate): array
    {
        $statuses = [
            'hadir' => 'Hadir',
            'terlambat' => 'Terlambat',
            'izin' => 'Izin',
            'sakit' => 'Sakit',
            'cuti' => 'Cuti',
            'alpha' => 'Alpha',
        ];

        $statusData = [];
        $statusLabels = [];
        $colors = [
            'hadir' => 'rgb(34, 197, 94)',      // Green
            'terlambat' => 'rgb(245, 158, 11)', // Yellow
            'izin' => 'rgb(59, 130, 246)',      // Blue
            'sakit' => 'rgb(139, 92, 246)',     // Purple
            'cuti' => 'rgb(236, 72, 153)',      // Pink
            'alpha' => 'rgb(239, 68, 68)',      // Red
        ];

        $backgroundColors = [];

        foreach ($statuses as $status => $label) {
            $count = Absensi::whereBetween('tanggal_absensi', [
                $startDate->format('Y-m-d'),
                $endDate->format('Y-m-d')
            ])
            ->where('status', $status)
            ->count();

            if ($count > 0) {
                $statusData[] = $count;
                $statusLabels[] = $label;
                $backgroundColors[] = $colors[$status];
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Absensi',
                    'data' => $statusData,
                    'backgroundColor' => $backgroundColors,
                    'borderWidth' => 2,
                    'borderColor' => '#ffffff',
                ],
            ],
            'labels' => $statusLabels,
        ];
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'enabled' => true,
                    'callbacks' => [
                        'label' => "function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }",
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
            'cutout' => '50%', // Makes it a doughnut instead of pie
        ];
    }
}
