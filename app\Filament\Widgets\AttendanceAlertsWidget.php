<?php

namespace App\Filament\Widgets;

use App\Models\Absensi;
use App\Models\Karyawan;
use App\Models\Schedule;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class AttendanceAlertsWidget extends BaseWidget
{
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;
    //protected static ?string $heading = 'Alert & Notifikasi Absensi';
    protected static ?int $sort = 6;
    protected int | string | array $columnSpan = 'full';

    protected function getStats(): array
    {
        return [
            // Employees Not Checked In Today
            Stat::make('Belum Absen Masuk', $this->getNotCheckedInToday())
                ->description('Karyawan yang belum absen masuk hari ini')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($this->getNotCheckedInColor()),

            // Employees Not Checked Out Yesterday
            Stat::make('Belum Absen Keluar', $this->getNotCheckedOutYesterday())
                ->description('Kemarin belum absen keluar')
                ->descriptionIcon('heroicon-m-arrow-right-on-rectangle')
                ->color('warning'),

            // Consecutive Absences
            Stat::make('Absen Berturut-turut', $this->getConsecutiveAbsences())
                ->description('Alpha > 3 hari berturut-turut')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color('danger'),

            // Frequent Late This Month
            Stat::make('Sering Terlambat', $this->getFrequentLateThisMonth())
                ->description('Terlambat > 5x bulan ini')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            // Low Attendance Rate
            Stat::make('Kehadiran Rendah', $this->getLowAttendanceEmployees())
                ->description('Kehadiran < 80% bulan ini')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('danger'),

            // Pending Leave Requests
            Stat::make('Izin Pending', $this->getPendingLeaveRequests())
                ->description('Menunggu persetujuan')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('info'),
        ];
    }

    private function getNotCheckedInToday(): int
    {
        $scheduledToday = Schedule::whereDate('tanggal_jadwal', today())
            ->pluck('karyawan_id')
            ->unique();

        $checkedInToday = Absensi::whereDate('tanggal_absensi', today())
            ->whereNotNull('waktu_masuk')
            ->pluck('karyawan_id')
            ->unique();

        return $scheduledToday->diff($checkedInToday)->count();
    }

    private function getNotCheckedInColor(): string
    {
        $count = $this->getNotCheckedInToday();
        $currentHour = now()->hour;

        // More urgent as the day progresses
        if ($currentHour < 9) {
            return 'info'; // Still early
        } elseif ($currentHour < 12) {
            return $count > 0 ? 'warning' : 'success';
        } else {
            return $count > 0 ? 'danger' : 'success';
        }
    }

    private function getNotCheckedOutYesterday(): int
    {
        return Absensi::whereDate('tanggal_absensi', now()->subDay())
            ->whereNotNull('waktu_masuk')
            ->whereNull('waktu_keluar')
            ->count();
    }

    private function getConsecutiveAbsences(): int
    {
        // This is a simplified version - you might need more complex logic
        // to track actual consecutive absences
        $recentAbsences = Absensi::where('status', 'alpha')
            ->whereBetween('tanggal_absensi', [
                now()->subDays(7)->format('Y-m-d'),
                now()->format('Y-m-d')
            ])
            ->selectRaw('karyawan_id, COUNT(*) as absence_count')
            ->groupBy('karyawan_id')
            ->havingRaw('absence_count >= 3')
            ->count();

        return $recentAbsences;
    }

    private function getFrequentLateThisMonth(): int
    {
        return Absensi::selectRaw('karyawan_id, COUNT(*) as late_count')
            ->where('status', 'terlambat')
            ->whereMonth('tanggal_absensi', now()->month)
            ->groupBy('karyawan_id')
            ->havingRaw('late_count > 5')
            ->count();
    }

    private function getLowAttendanceEmployees(): int
    {
        $activeEmployees = Karyawan::where('status_aktif', true)->get();
        $lowAttendanceCount = 0;

        foreach ($activeEmployees as $employee) {
            $totalAbsensi = Absensi::where('karyawan_id', $employee->id)
                ->whereMonth('tanggal_absensi', now()->month)
                ->count();

            $presentCount = Absensi::where('karyawan_id', $employee->id)
                ->whereMonth('tanggal_absensi', now()->month)
                ->whereIn('status', ['hadir', 'terlambat'])
                ->count();

            if ($totalAbsensi > 0) {
                $attendanceRate = ($presentCount / $totalAbsensi) * 100;
                if ($attendanceRate < 80) {
                    $lowAttendanceCount++;
                }
            }
        }

        return $lowAttendanceCount;
    }

    private function getPendingLeaveRequests(): int
    {
        // Assuming leave requests are stored as absensi with certain statuses
        // and have an approval field - adjust based on your actual structure
        return Absensi::whereIn('status', ['izin', 'sakit', 'cuti'])
            ->whereMonth('tanggal_absensi', now()->month)
            ->whereNull('approved_at') // Assuming there's an approval field
            ->count();
    }
}
