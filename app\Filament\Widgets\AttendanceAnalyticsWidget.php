<?php

namespace App\Filament\Widgets;

use App\Models\Absensi;
use App\Models\Departemen;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class AttendanceAnalyticsWidget extends ChartWidget
{
    protected static ?string $heading = 'Analisis Kehadiran & Absensi';
    protected static ?int $sort = 6;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'attendance_trend';

    protected function getFilters(): ?array
    {
        return [
            'attendance_trend' => 'Trend Kehadiran Mingguan',
            'attendance_status' => 'Status Kehadiran Bulan Ini',
            'department_attendance' => 'Kehadiran per Departemen',
            'monthly_comparison' => 'Perbandingan Bulanan',
        ];
    }

    protected function getData(): array
    {
        return match ($this->filter) {
            'attendance_trend' => $this->getAttendanceTrendData(),
            'attendance_status' => $this->getAttendanceStatusData(),
            'department_attendance' => $this->getDepartmentAttendanceData(),
            'monthly_comparison' => $this->getMonthlyComparisonData(),
            default => $this->getAttendanceTrendData(),
        };
    }

    protected function getType(): string
    {
        return match ($this->filter) {
            'attendance_status' => 'doughnut',
            'department_attendance' => 'bar',
            default => 'line',
        };
    }

    private function getAttendanceTrendData(): array
    {
        $weeks = [];
        $attendanceRates = [];

        // Get last 8 weeks
        for ($i = 7; $i >= 0; $i--) {
            $startOfWeek = Carbon::now()->subWeeks($i)->startOfWeek();
            $endOfWeek = Carbon::now()->subWeeks($i)->endOfWeek();

            $totalAbsensi = Absensi::whereBetween('tanggal_absensi', [
                $startOfWeek->format('Y-m-d'),
                $endOfWeek->format('Y-m-d')
            ])->count();

            $hadirCount = Absensi::whereBetween('tanggal_absensi', [
                $startOfWeek->format('Y-m-d'),
                $endOfWeek->format('Y-m-d')
            ])->whereIn('status', ['hadir', 'terlambat'])->count();

            $rate = $totalAbsensi > 0 ? ($hadirCount / $totalAbsensi) * 100 : 0;

            $weeks[] = 'Week ' . (8 - $i);
            $attendanceRates[] = round($rate, 1);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Tingkat Kehadiran (%)',
                    'data' => $attendanceRates,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'tension' => 0.4,
                    'fill' => true,
                ],
            ],
            'labels' => $weeks,
        ];
    }

    private function getAttendanceStatusData(): array
    {
        $statuses = ['hadir', 'terlambat', 'izin', 'sakit', 'alpha'];
        $statusData = [];
        $statusLabels = [];

        foreach ($statuses as $status) {
            $count = Absensi::whereMonth('tanggal_absensi', now()->month)
                ->where('status', $status)
                ->count();

            if ($count > 0) {
                $statusData[] = $count;
                $statusLabels[] = ucfirst($status);
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Absensi',
                    'data' => $statusData,
                    'backgroundColor' => [
                        'rgb(34, 197, 94)',   // Green for hadir
                        'rgb(245, 158, 11)',  // Yellow for terlambat
                        'rgb(59, 130, 246)',  // Blue for izin
                        'rgb(139, 92, 246)',  // Purple for sakit
                        'rgb(239, 68, 68)',   // Red for alpha
                    ],
                ],
            ],
            'labels' => $statusLabels,
        ];
    }

    private function getDepartmentAttendanceData(): array
    {
        $departments = Departemen::with(['karyawan.absensi' => function ($query) {
            $query->whereMonth('tanggal_absensi', now()->month);
        }])->get();

        $departmentNames = [];
        $attendanceRates = [];

        foreach ($departments as $department) {
            $totalAbsensi = $department->karyawan
                ->flatMap->absensi
                ->count();

            $hadirCount = $department->karyawan
                ->flatMap->absensi
                ->whereIn('status', ['hadir', 'terlambat'])
                ->count();

            if ($totalAbsensi > 0) {
                $rate = ($hadirCount / $totalAbsensi) * 100;
                $departmentNames[] = $department->nama_departemen;
                $attendanceRates[] = round($rate, 1);
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Tingkat Kehadiran (%)',
                    'data' => $attendanceRates,
                    'backgroundColor' => [
                        'rgb(59, 130, 246)',
                        'rgb(16, 185, 129)',
                        'rgb(245, 158, 11)',
                        'rgb(239, 68, 68)',
                        'rgb(139, 92, 246)',
                        'rgb(236, 72, 153)',
                        'rgb(34, 197, 94)',
                        'rgb(251, 146, 60)',
                        'rgb(168, 85, 247)',
                        'rgb(14, 165, 233)',
                    ],
                ],
            ],
            'labels' => $departmentNames,
        ];
    }

    private function getMonthlyComparisonData(): array
    {
        $months = [];
        $attendanceRates = [];
        $lateRates = [];

        // Get last 6 months
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthLabel = $date->format('M Y');

            $totalAbsensi = Absensi::whereYear('tanggal_absensi', $date->year)
                ->whereMonth('tanggal_absensi', $date->month)
                ->count();

            $hadirCount = Absensi::whereYear('tanggal_absensi', $date->year)
                ->whereMonth('tanggal_absensi', $date->month)
                ->whereIn('status', ['hadir', 'terlambat'])
                ->count();

            $lateCount = Absensi::whereYear('tanggal_absensi', $date->year)
                ->whereMonth('tanggal_absensi', $date->month)
                ->where('status', 'terlambat')
                ->count();

            $attendanceRate = $totalAbsensi > 0 ? ($hadirCount / $totalAbsensi) * 100 : 0;
            $lateRate = $totalAbsensi > 0 ? ($lateCount / $totalAbsensi) * 100 : 0;

            $months[] = $monthLabel;
            $attendanceRates[] = round($attendanceRate, 1);
            $lateRates[] = round($lateRate, 1);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Tingkat Kehadiran (%)',
                    'data' => $attendanceRates,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Tingkat Keterlambatan (%)',
                    'data' => $lateRates,
                    'borderColor' => 'rgb(245, 158, 11)',
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                    'tension' => 0.4,
                ],
            ],
            'labels' => $months,
        ];
    }

    protected function getOptions(): array
    {
        $baseOptions = [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'enabled' => true,
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];

        if ($this->filter !== 'attendance_status') {
            $baseOptions['scales'] = [
                'y' => [
                    'beginAtZero' => true,
                    'max' => 100,
                    'ticks' => [
                        'callback' => "function(value) { return value + '%'; }",
                    ],
                ],
            ];
        }

        return $baseOptions;
    }
}
