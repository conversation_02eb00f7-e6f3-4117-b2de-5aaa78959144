<?php

namespace App\Filament\Widgets;

use App\Models\Departemen;
use App\Models\Absensi;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class AttendanceByDepartmentWidget extends ChartWidget
{
    protected static ?string $heading = 'Kehadiran per Departemen';
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'this_month';

    protected function getFilters(): ?array
    {
        return [
            'this_month' => 'Bulan Ini',
            'last_month' => 'Bulan Lalu',
            'this_week' => 'Minggu Ini',
            'today' => 'Hari Ini',
        ];
    }

    protected function getData(): array
    {
        return match ($this->filter) {
            'this_month' => $this->getThisMonthData(),
            'last_month' => $this->getLastMonthData(),
            'this_week' => $this->getThisWeekData(),
            'today' => $this->getTodayData(),
            default => $this->getThisMonthData(),
        };
    }

    protected function getType(): string
    {
        return 'bar';
    }

    private function getThisMonthData(): array
    {
        return $this->getDepartmentData(
            now()->startOfMonth(),
            now()->endOfMonth()
        );
    }

    private function getLastMonthData(): array
    {
        return $this->getDepartmentData(
            now()->subMonth()->startOfMonth(),
            now()->subMonth()->endOfMonth()
        );
    }

    private function getThisWeekData(): array
    {
        return $this->getDepartmentData(
            now()->startOfWeek(),
            now()->endOfWeek()
        );
    }

    private function getTodayData(): array
    {
        return $this->getDepartmentData(
            now()->startOfDay(),
            now()->endOfDay()
        );
    }

    private function getDepartmentData(Carbon $startDate, Carbon $endDate): array
    {
        $departments = Departemen::with(['karyawan.absensi' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('tanggal_absensi', [
                $startDate->format('Y-m-d'),
                $endDate->format('Y-m-d')
            ]);
        }])->get();

        $departmentNames = [];
        $attendanceRates = [];
        $lateRates = [];
        $absentRates = [];

        foreach ($departments as $department) {
            $allAbsensi = $department->karyawan->flatMap->absensi;
            
            if ($allAbsensi->count() > 0) {
                $totalAbsensi = $allAbsensi->count();
                $hadirCount = $allAbsensi->whereIn('status', ['hadir', 'terlambat'])->count();
                $lateCount = $allAbsensi->where('status', 'terlambat')->count();
                $absentCount = $allAbsensi->where('status', 'alpha')->count();

                $attendanceRate = ($hadirCount / $totalAbsensi) * 100;
                $lateRate = ($lateCount / $totalAbsensi) * 100;
                $absentRate = ($absentCount / $totalAbsensi) * 100;

                $departmentNames[] = $department->nama_departemen;
                $attendanceRates[] = round($attendanceRate, 1);
                $lateRates[] = round($lateRate, 1);
                $absentRates[] = round($absentRate, 1);
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Tingkat Kehadiran (%)',
                    'data' => $attendanceRates,
                    'backgroundColor' => 'rgba(34, 197, 94, 0.8)',
                    'borderColor' => 'rgb(34, 197, 94)',
                    'borderWidth' => 1,
                ],
                [
                    'label' => 'Tingkat Keterlambatan (%)',
                    'data' => $lateRates,
                    'backgroundColor' => 'rgba(245, 158, 11, 0.8)',
                    'borderColor' => 'rgb(245, 158, 11)',
                    'borderWidth' => 1,
                ],
                [
                    'label' => 'Tingkat Absen (%)',
                    'data' => $absentRates,
                    'backgroundColor' => 'rgba(239, 68, 68, 0.8)',
                    'borderColor' => 'rgb(239, 68, 68)',
                    'borderWidth' => 1,
                ],
            ],
            'labels' => $departmentNames,
        ];
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'enabled' => true,
                    'callbacks' => [
                        'label' => "function(context) {
                            return context.dataset.label + ': ' + context.parsed.y + '%';
                        }",
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'max' => 100,
                    'stacked' => false,
                    'ticks' => [
                        'callback' => "function(value) { return value + '%'; }",
                    ],
                ],
                'x' => [
                    'stacked' => false,
                ],
            ],
        ];
    }
}
