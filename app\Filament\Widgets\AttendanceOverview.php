<?php

namespace App\Filament\Widgets;

use App\Models\Absensi;
use App\Models\Karyawan;
use App\Models\Schedule;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class AttendanceOverview extends BaseWidget
{
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;

    protected function getStats(): array
    {
        $user = Auth::user();
        $today = now()->format('Y-m-d');

        // Get supervised employee IDs
        $employeeIds = [];
        if ($user->role === 'supervisor') {
            $employeeIds = Karyawan::where('supervisor_id', $user->id)->pluck('id')->toArray();
        } else {
            // Admin can see all employees
            $employeeIds = Karyawan::where('status_aktif', 1)->pluck('id')->toArray();
        }

        // Count today's schedules
        $schedulesCount = Schedule::whereIn('karyawan_id', $employeeIds)
            ->where('tanggal_jadwal', $today)
            ->count();

        // Count today's attendance
        $attendanceCount = Absensi::whereIn('karyawan_id', $employeeIds)
            ->where('tanggal_absensi', $today)
            ->count();

        // Count late employees
        $lateCount = Absensi::whereIn('karyawan_id', $employeeIds)
            ->where('tanggal_absensi', $today)
            ->where('status', 'terlambat')
            ->count();

        // Count absent employees
        $absentCount = $schedulesCount - $attendanceCount;
        if ($absentCount < 0) $absentCount = 0;

        return [
            Stat::make('Jadwal Hari Ini', $schedulesCount)
                ->description('Jumlah karyawan yang dijadwalkan hari ini')
                ->color('primary'),

            Stat::make('Sudah Absen', $attendanceCount)
                ->description('Jumlah karyawan yang sudah absen hari ini')
                ->color('success'),

            Stat::make('Terlambat', $lateCount)
                ->description('Jumlah karyawan yang terlambat hari ini')
                ->color('warning'),

            Stat::make('Belum Absen', $absentCount)
                ->description('Jumlah karyawan yang belum absen hari ini')
                ->color('danger'),
        ];
    }
}
