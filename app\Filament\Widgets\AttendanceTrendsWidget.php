<?php

namespace App\Filament\Widgets;

use App\Models\Absensi;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class AttendanceTrendsWidget extends ChartWidget
{
    protected static ?string $heading = 'Trend Kehadiran';
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'weekly';

    protected function getFilters(): ?array
    {
        return [
            'weekly' => 'Mingguan (8 Minggu)',
            'monthly' => 'Bulanan (6 Bulan)',
            'daily' => '<PERSON>an (30 Hari)',
        ];
    }

    protected function getData(): array
    {
        return match ($this->filter) {
            'weekly' => $this->getWeeklyData(),
            'monthly' => $this->getMonthlyData(),
            'daily' => $this->getDailyData(),
            default => $this->getWeeklyData(),
        };
    }

    protected function getType(): string
    {
        return 'line';
    }

    private function getWeeklyData(): array
    {
        $weeks = [];
        $attendanceRates = [];
        $lateRates = [];

        // Get last 8 weeks
        for ($i = 7; $i >= 0; $i--) {
            $startOfWeek = Carbon::now()->subWeeks($i)->startOfWeek();
            $endOfWeek = Carbon::now()->subWeeks($i)->endOfWeek();
            
            $totalAbsensi = Absensi::whereBetween('tanggal_absensi', [
                $startOfWeek->format('Y-m-d'),
                $endOfWeek->format('Y-m-d')
            ])->count();

            $hadirCount = Absensi::whereBetween('tanggal_absensi', [
                $startOfWeek->format('Y-m-d'),
                $endOfWeek->format('Y-m-d')
            ])->whereIn('status', ['hadir', 'terlambat'])->count();

            $lateCount = Absensi::whereBetween('tanggal_absensi', [
                $startOfWeek->format('Y-m-d'),
                $endOfWeek->format('Y-m-d')
            ])->where('status', 'terlambat')->count();

            $attendanceRate = $totalAbsensi > 0 ? ($hadirCount / $totalAbsensi) * 100 : 0;
            $lateRate = $totalAbsensi > 0 ? ($lateCount / $totalAbsensi) * 100 : 0;

            $weeks[] = 'Week ' . (8 - $i);
            $attendanceRates[] = round($attendanceRate, 1);
            $lateRates[] = round($lateRate, 1);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Tingkat Kehadiran (%)',
                    'data' => $attendanceRates,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'tension' => 0.4,
                    'fill' => true,
                ],
                [
                    'label' => 'Tingkat Keterlambatan (%)',
                    'data' => $lateRates,
                    'borderColor' => 'rgb(245, 158, 11)',
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                    'tension' => 0.4,
                ],
            ],
            'labels' => $weeks,
        ];
    }

    private function getMonthlyData(): array
    {
        $months = [];
        $attendanceRates = [];
        $lateRates = [];

        // Get last 6 months
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthLabel = $date->format('M Y');

            $totalAbsensi = Absensi::whereYear('tanggal_absensi', $date->year)
                ->whereMonth('tanggal_absensi', $date->month)
                ->count();

            $hadirCount = Absensi::whereYear('tanggal_absensi', $date->year)
                ->whereMonth('tanggal_absensi', $date->month)
                ->whereIn('status', ['hadir', 'terlambat'])
                ->count();

            $lateCount = Absensi::whereYear('tanggal_absensi', $date->year)
                ->whereMonth('tanggal_absensi', $date->month)
                ->where('status', 'terlambat')
                ->count();

            $attendanceRate = $totalAbsensi > 0 ? ($hadirCount / $totalAbsensi) * 100 : 0;
            $lateRate = $totalAbsensi > 0 ? ($lateCount / $totalAbsensi) * 100 : 0;

            $months[] = $monthLabel;
            $attendanceRates[] = round($attendanceRate, 1);
            $lateRates[] = round($lateRate, 1);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Tingkat Kehadiran (%)',
                    'data' => $attendanceRates,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'tension' => 0.4,
                    'fill' => true,
                ],
                [
                    'label' => 'Tingkat Keterlambatan (%)',
                    'data' => $lateRates,
                    'borderColor' => 'rgb(245, 158, 11)',
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                    'tension' => 0.4,
                ],
            ],
            'labels' => $months,
        ];
    }

    private function getDailyData(): array
    {
        $days = [];
        $attendanceRates = [];
        $lateRates = [];

        // Get last 30 days
        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dayLabel = $date->format('M j');

            $totalAbsensi = Absensi::whereDate('tanggal_absensi', $date->format('Y-m-d'))->count();

            $hadirCount = Absensi::whereDate('tanggal_absensi', $date->format('Y-m-d'))
                ->whereIn('status', ['hadir', 'terlambat'])
                ->count();

            $lateCount = Absensi::whereDate('tanggal_absensi', $date->format('Y-m-d'))
                ->where('status', 'terlambat')
                ->count();

            $attendanceRate = $totalAbsensi > 0 ? ($hadirCount / $totalAbsensi) * 100 : 0;
            $lateRate = $totalAbsensi > 0 ? ($lateCount / $totalAbsensi) * 100 : 0;

            $days[] = $dayLabel;
            $attendanceRates[] = round($attendanceRate, 1);
            $lateRates[] = round($lateRate, 1);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Tingkat Kehadiran (%)',
                    'data' => $attendanceRates,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'tension' => 0.4,
                    'fill' => true,
                ],
                [
                    'label' => 'Tingkat Keterlambatan (%)',
                    'data' => $lateRates,
                    'borderColor' => 'rgb(245, 158, 11)',
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                    'tension' => 0.4,
                ],
            ],
            'labels' => $days,
        ];
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'enabled' => true,
                    'callbacks' => [
                        'label' => "function(context) {
                            return context.dataset.label + ': ' + context.parsed.y + '%';
                        }",
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'max' => 100,
                    'ticks' => [
                        'callback' => "function(value) { return value + '%'; }",
                    ],
                ],
            ],
        ];
    }
}
