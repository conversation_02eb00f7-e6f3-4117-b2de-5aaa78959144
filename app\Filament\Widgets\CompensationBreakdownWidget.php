<?php

namespace App\Filament\Widgets;

use App\Models\PenggajianKaryawan;
use Filament\Widgets\ChartWidget;

class CompensationBreakdownWidget extends ChartWidget
{
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;
    protected static ?string $heading = 'Breakdown Kompensasi';
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'allowances';

    protected function getFilters(): ?array
    {
        return [
            'allowances' => 'Tunjangan',
            'deductions' => 'Potongan',
            'net_vs_gross' => '<PERSON><PERSON><PERSON> vs <PERSON><PERSON>',
        ];
    }

    protected function getData(): array
    {
        return match ($this->filter) {
            'allowances' => $this->getAllowancesData(),
            'deductions' => $this->getDeductionsData(),
            'net_vs_gross' => $this->getNetVsGrossData(),
            default => $this->getAllowancesData(),
        };
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    private function getAllowancesData(): array
    {
        $currentMonth = now()->format('Y-m');

        $allowances = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->selectRaw('
                SUM(tunjangan_jabatan) as total_tunjangan_jabatan,
                SUM(tunjangan_umum) as total_tunjangan_umum,
                SUM(tunjangan_sembako) as total_tunjangan_sembako
            ')
            ->first();

        if (!$allowances) {
            return [
                'datasets' => [['data' => [], 'backgroundColor' => []]],
                'labels' => [],
            ];
        }

        $data = [
            round($allowances->total_tunjangan_jabatan / 1000000, 1),
            round($allowances->total_tunjangan_umum / 1000000, 1),
            round($allowances->total_tunjangan_sembako / 1000000, 1),
        ];

        return [
            'datasets' => [
                [
                    'label' => 'Total Tunjangan (Juta)',
                    'data' => $data,
                    'backgroundColor' => [
                        'rgb(34, 197, 94)',   // Green for position allowance
                        'rgb(59, 130, 246)',  // Blue for general allowance
                        'rgb(245, 158, 11)',  // Yellow for food allowance
                    ],
                ],
            ],
            'labels' => ['Tunjangan Jabatan', 'Tunjangan Umum', 'Tunjangan Sembako'],
        ];
    }

    private function getDeductionsData(): array
    {
        $currentMonth = now()->format('Y-m');

        $deductions = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->selectRaw('
                SUM(bpjs_kesehatan_dipotong + bpjs_tk_dipotong) as total_potongan_bpjs,
                SUM(potongan_lainnya) as total_potongan_lain
            ')
            ->first();

        if (!$deductions) {
            return [
                'datasets' => [['data' => [], 'backgroundColor' => []]],
                'labels' => [],
            ];
        }

        $data = [
            round($deductions->total_potongan_bpjs / 1000000, 1),
            round($deductions->total_potongan_lain / 1000000, 1),
        ];

        return [
            'datasets' => [
                [
                    'label' => 'Total Potongan (Juta)',
                    'data' => $data,
                    'backgroundColor' => [
                        'rgb(239, 68, 68)',   // Red for BPJS
                        'rgb(139, 92, 246)',  // Purple for others
                    ],
                ],
            ],
            'labels' => ['Potongan BPJS', 'Potongan Lainnya'],
        ];
    }

    private function getNetVsGrossData(): array
    {
        $currentMonth = now()->format('Y-m');

        $totals = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->selectRaw('
                SUM(gaji_pokok + tunjangan_jabatan + tunjangan_umum + tunjangan_sembako) as total_gross,
                SUM(take_home_pay) as total_net
            ')
            ->first();

        if (!$totals) {
            return [
                'datasets' => [['data' => [], 'backgroundColor' => []]],
                'labels' => [],
            ];
        }

        $totalDeductions = $totals->total_gross - $totals->total_net;

        $data = [
            round($totals->total_net / 1000000, 1),
            round($totalDeductions / 1000000, 1),
        ];

        return [
            'datasets' => [
                [
                    'label' => 'Total Gaji (Juta)',
                    'data' => $data,
                    'backgroundColor' => [
                        'rgb(34, 197, 94)',   // Green for net salary
                        'rgb(239, 68, 68)',   // Red for deductions
                    ],
                ],
            ],
            'labels' => ['Gaji Bersih', 'Total Potongan'],
        ];
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'enabled' => true,
                    'callbacks' => [
                        'label' => "function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': Rp ' + context.parsed + ' juta (' + percentage + '%)';
                        }",
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
            'cutout' => '50%',
        ];
    }
}
