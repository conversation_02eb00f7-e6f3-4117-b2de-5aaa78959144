<?php

namespace App\Filament\Widgets;

use App\Models\Project;
use App\Models\Task;

use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class EnhancedProjectStats extends BaseWidget
{
    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        // Project statistics
        $totalProjects = Project::count();
        $activeProjects = Project::where('status', 'active')->count();
        $completedProjects = Project::where('status', 'completed')->count();
        $overdueProjects = Project::where('end_date', '<', now())
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->count();

        // Task statistics
        $totalTasks = Task::count();
        $completedTasks = Task::where('status', 'completed')->count();
        $inProgressTasks = Task::where('status', 'in_progress')->count();
        $overdueTasks = Task::where('due_date', '<', now())
            ->whereNotIn('status', ['completed'])
            ->count();



        // Team statistics
        $activeTeamMembers = User::whereHas('assignedTasks', function ($query) {
            $query->whereIn('status', ['todo', 'in_progress']);
        })->count();

        // Calculate percentages
        $projectCompletionRate = $totalProjects > 0 ? round(($completedProjects / $totalProjects) * 100) : 0;
        $taskCompletionRate = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100) : 0;

        // Generate trend data (last 7 days)
        $projectTrend = $this->getProjectTrend();
        $taskTrend = $this->getTaskTrend();

        return [
            Stat::make('Total Kegiatan', $totalProjects)
                ->description($this->getProjectDescription($activeProjects, $completedProjects, $overdueProjects))
                ->descriptionIcon($overdueProjects > 0 ? 'heroicon-m-exclamation-triangle' : 'heroicon-m-briefcase')
                ->color($overdueProjects > 0 ? 'warning' : 'primary')
                ->chart($projectTrend),

            Stat::make('Progress Kegiatan', "{$projectCompletionRate}%")
                ->description($overdueProjects > 0 ? "{$overdueProjects} Kegiatan terlambat" : "Semua proyek on track")
                ->descriptionIcon($overdueProjects > 0 ? 'heroicon-m-clock' : 'heroicon-m-check-circle')
                ->color($overdueProjects > 0 ? 'danger' : 'success'),

            Stat::make('Progress Tugas', "{$taskCompletionRate}%")
                ->description("{$completedTasks} dari {$totalTasks} tugas selesai")
                ->descriptionIcon('heroicon-m-list-bullet')
                ->color($this->getTaskColor($overdueTasks, $taskCompletionRate))
                ->chart($taskTrend),

            Stat::make('Tim Aktif', $activeTeamMembers)
                ->description("{$inProgressTasks} tugas sedang dikerjakan")
                ->descriptionIcon('heroicon-m-users')
                ->color('info'),


            Stat::make('Produktivitas Tim', $this->getProductivityScore())
                ->description($this->getProductivityDescription())
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color($this->getProductivityColor()),
        ];
    }

    private function getProjectDescription($active, $completed, $overdue): string
    {
        $parts = [];
        if ($active > 0) $parts[] = "{$active} aktif";
        if ($completed > 0) $parts[] = "{$completed} selesai";
        if ($overdue > 0) $parts[] = "{$overdue} terlambat";

        return implode(', ', $parts) ?: 'total kegiatan';
    }

    private function getTaskColor($overdueTasks, $completionRate): string
    {
        if ($overdueTasks > 0) return 'danger';
        if ($completionRate >= 80) return 'success';
        if ($completionRate >= 60) return 'warning';
        return 'gray';
    }

    private function getProductivityScore(): string
    {
        $completedTasksThisWeek = Task::where('status', 'completed')
            ->where('updated_at', '>=', now()->startOfWeek())
            ->count();

        $score = ($completedTasksThisWeek * 2);
        return (string) $score;
    }

    private function getProductivityDescription(): string
    {
        $score = (int) $this->getProductivityScore();

        if ($score >= 20) return 'Produktivitas sangat tinggi';
        if ($score >= 15) return 'Produktivitas tinggi';
        if ($score >= 10) return 'Produktivitas sedang';
        if ($score >= 5) return 'Produktivitas rendah';
        return 'Perlu peningkatan produktivitas';
    }

    private function getProductivityColor(): string
    {
        $score = (int) $this->getProductivityScore();

        if ($score >= 20) return 'success';
        if ($score >= 15) return 'primary';
        if ($score >= 10) return 'warning';
        return 'danger';
    }

    private function getProjectTrend(): array
    {
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $count = Project::whereDate('created_at', $date)->count();
            $data[] = $count;
        }
        return $data;
    }

    private function getTaskTrend(): array
    {
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $count = Task::where('status', 'completed')
                ->whereDate('updated_at', $date)
                ->count();
            $data[] = $count;
        }
        return $data;
    }
}
