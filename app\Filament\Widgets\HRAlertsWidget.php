<?php

namespace App\Filament\Widgets;

use App\Models\Karyawan;
use App\Models\KpiPenilaian;
use App\Models\Absensi;
use App\Models\Pelanggaran;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class HrAlertsWidget extends BaseWidget
{
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;
    //protected static ?string $heading = 'HR Alerts & Notifications';
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 'full';

    protected function getStats(): array
    {
        return [
            // Poor Performance Alert
            Stat::make('Performance Rendah', $this->getPoorPerformanceCount())
                ->description('KPI < 70% bulan ini')
                ->descriptionIcon('heroicon-m-arrow-trending-down')
                ->color('danger'),

            // High Achievers
            Stat::make('Top Performers', $this->getTopPerformersCount())
                ->description('KPI > 90% bulan ini')
                ->descriptionIcon('heroicon-m-star')
                ->color('success'),

            // Frequent Late Arrivals
            Stat::make('Sering Terlambat', $this->getFrequentLateCount())
                ->description('Terlambat > 5x bulan ini')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            // Recent Violations
            Stat::make('Pelanggaran Bulan Ini', $this->getViolationsThisMonth())
                ->description($this->getViolationTrend())
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($this->getViolationColor()),

            // Employees Without BPJS
            Stat::make('Tanpa BPJS', $this->getEmployeesWithoutBpjs())
                ->description('Karyawan belum terdaftar BPJS')
                ->descriptionIcon('heroicon-m-shield-exclamation')
                ->color('warning'),

            // Pending KPI Assessments
            Stat::make('KPI Belum Dinilai', $this->getPendingKpiCount())
                ->description('Bulan ini belum ada penilaian')
                ->descriptionIcon('heroicon-m-clipboard-document-list')
                ->color('info'),
        ];
    }

    private function getPoorPerformanceCount(): int
    {
        return KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->where('realisasi_kpi', '<', 70)
            ->distinct('karyawan_id')
            ->count();
    }

    private function getTopPerformersCount(): int
    {
        return KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->where('realisasi_kpi', '>', 90)
            ->distinct('karyawan_id')
            ->count();
    }

    private function getFrequentLateCount(): int
    {
        return Absensi::selectRaw('karyawan_id, COUNT(*) as late_count')
            ->whereMonth('tanggal_absensi', now()->month)
            ->where('status', 'terlambat')
            ->groupBy('karyawan_id')
            ->havingRaw('late_count > 5')
            ->count();
    }

    private function getViolationsThisMonth(): int
    {
        return Pelanggaran::whereMonth('tanggal', now()->month)->count();
    }

    private function getViolationTrend(): string
    {
        $currentMonth = Pelanggaran::whereMonth('tanggal', now()->month)->count();
        $lastMonth = Pelanggaran::whereMonth('tanggal', now()->subMonth()->month)->count();

        $diff = $currentMonth - $lastMonth;

        if ($diff > 0) {
            return "Naik {$diff} dari bulan lalu";
        } elseif ($diff < 0) {
            return "Turun " . abs($diff) . " dari bulan lalu";
        } else {
            return "Sama dengan bulan lalu";
        }
    }

    private function getViolationColor(): string
    {
        $count = $this->getViolationsThisMonth();

        if ($count === 0) {
            return 'success';
        } elseif ($count <= 5) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    private function getEmployeesWithoutBpjs(): int
    {
        $totalActive = Karyawan::where('status_aktif', true)->count();
        $withBpjs = Karyawan::where('status_aktif', true)
            ->whereHas('bpjs')
            ->count();

        return $totalActive - $withBpjs;
    }

    private function getPendingKpiCount(): int
    {
        $activeEmployees = Karyawan::where('status_aktif', true)->pluck('id');
        $assessedEmployees = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->pluck('karyawan_id')
            ->unique();

        return $activeEmployees->diff($assessedEmployees)->count();
    }
}
