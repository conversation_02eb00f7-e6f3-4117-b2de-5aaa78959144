<?php

namespace App\Filament\Widgets;

use App\Models\Karyawan;
use App\Models\Absensi;
use App\Models\KpiPenilaian;
use App\Filament\Resources\KaryawanResource;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Livewire\Attributes\On;
use Carbon\Carbon;

class HrStatsWidget extends BaseWidget
{
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;
    protected static ?int $sort = 1;
    protected int | string | array $columnSpan = 'full';

    public $filters = [];

    public function mount(): void
    {
        $this->filters = session('dashboard_filters', [
            'date_range' => 'this_month',
            'start_date' => now()->startOfMonth(),
            'end_date' => now()->endOfMonth(),
        ]);
    }

    #[On('filtersUpdated')]
    public function updateFilters($filters): void
    {
        $this->filters = $filters;
        $this->dispatch('$refresh');
    }

    #[On('updateCharts')]
    public function refreshWidget(): void
    {
        $this->filters = session('dashboard_filters', $this->filters);
        $this->dispatch('$refresh');
    }

    protected function getStats(): array
    {
        return [
            Stat::make('Total Karyawan', $this->getTotalEmployees())
                ->description('Karyawan aktif')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),

            Stat::make('Rata-rata KPI', $this->getAverageKpiAchievement())
                ->description('Bulan ini')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('success'),

            Stat::make('Tingkat Kehadiran', $this->getAttendanceRate())
                ->description('Bulan ini')
                ->descriptionIcon('heroicon-m-clock')
                ->color('info'),

            Stat::make('Karyawan Baru', $this->getNewHiresCount())
                ->description('Bulan ini')
                ->descriptionIcon('heroicon-m-user-plus')
                ->color('warning'),
        ];
    }

    private function getTotalEmployees(): int
    {
        return Karyawan::where('status_aktif', true)->count();
    }

    private function getAverageKpiAchievement(): string
    {
        $dateRange = $this->getFilteredDateRange();

        $avgKpi = KpiPenilaian::whereBetween('tanggal_penilaian', [
            $dateRange['start']->format('Y-m-d'),
            $dateRange['end']->format('Y-m-d')
        ])
            ->avg('realisasi_kpi');

        return $avgKpi ? number_format($avgKpi, 1) . '%' : 'N/A';
    }

    private function getAttendanceRate(): string
    {
        $dateRange = $this->getFilteredDateRange();

        $totalAbsensi = Absensi::whereBetween('tanggal_absensi', [
            $dateRange['start']->format('Y-m-d'),
            $dateRange['end']->format('Y-m-d')
        ])->count();

        if ($totalAbsensi === 0) {
            return 'N/A';
        }

        $hadirCount = Absensi::whereBetween('tanggal_absensi', [
            $dateRange['start']->format('Y-m-d'),
            $dateRange['end']->format('Y-m-d')
        ])
            ->whereIn('status', ['hadir', 'terlambat'])
            ->count();

        $rate = ($hadirCount / $totalAbsensi) * 100;
        return number_format($rate, 1) . '%';
    }

    private function getNewHiresCount(): int
    {
        $dateRange = $this->getFilteredDateRange();

        return Karyawan::whereBetween('created_at', [
            $dateRange['start']->format('Y-m-d'),
            $dateRange['end']->format('Y-m-d')
        ])->count();
    }

    private function getFilteredDateRange(): array
    {
        if (empty($this->filters)) {
            return [
                'start' => now()->startOfMonth(),
                'end' => now()->endOfMonth(),
            ];
        }

        if (isset($this->filters['start_date']) && isset($this->filters['end_date'])) {
            return [
                'start' => Carbon::parse($this->filters['start_date']),
                'end' => Carbon::parse($this->filters['end_date']),
            ];
        }

        // Fallback to current month
        return [
            'start' => now()->startOfMonth(),
            'end' => now()->endOfMonth(),
        ];
    }
}
