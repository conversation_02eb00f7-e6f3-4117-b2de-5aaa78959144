<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\InventoryStock;
use App\Models\StockMovement;
use App\Models\StockAdjustment;
use App\Models\Produk;
use Carbon\Carbon;

class InventoryStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalProducts = Produk::count();
        $totalStockValue = InventoryStock::sum('total_value');
        $lowStockCount = InventoryStock::lowStock()->count();
        $todayMovements = StockMovement::whereDate('movement_date', Carbon::today())->count();
        $pendingAdjustments = StockAdjustment::where('status', 'Draft')->count();

        return [
            Stat::make('Total Produk', number_format($totalProducts))
                ->description('Jumlah produk terdaftar')
                ->descriptionIcon('heroicon-m-cube')
                ->color('primary'),

            Stat::make('Nilai Total Stok', 'Rp ' . number_format($totalStockValue, 0, ',', '.'))
                ->description('Total nilai persediaan')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),

            Stat::make('Stok Rendah', number_format($lowStockCount))
                ->description('Produk dengan stok di bawah minimum')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($lowStockCount > 0 ? 'danger' : 'success'),

            Stat::make('Pergerakan Hari Ini', number_format($todayMovements))
                ->description('Transaksi stok hari ini')
                ->descriptionIcon('heroicon-m-arrows-right-left')
                ->color('info'),

            Stat::make('Penyesuaian Pending', number_format($pendingAdjustments))
                ->description('Menunggu persetujuan')
                ->descriptionIcon('heroicon-m-clock')
                ->color($pendingAdjustments > 0 ? 'warning' : 'success'),
        ];
    }
}
