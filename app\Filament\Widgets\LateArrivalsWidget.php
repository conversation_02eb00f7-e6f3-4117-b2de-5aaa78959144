<?php

namespace App\Filament\Widgets;

use App\Models\Absensi;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class LateArrivalsWidget extends ChartWidget
{
    protected static ?string $heading = '<PERSON><PERSON><PERSON>';
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'daily_trend';

    protected function getFilters(): ?array
    {
        return [
            'daily_trend' => 'Trend Harian (30 Hari)',
            'hourly_pattern' => 'Pola Jam Terlambat',
            'frequent_offenders' => 'Sering Terlambat',
            'late_duration' => 'Durasi Keterlambatan',
        ];
    }

    protected function getData(): array
    {
        return match ($this->filter) {
            'daily_trend' => $this->getDailyTrendData(),
            'hourly_pattern' => $this->getHourlyPatternData(),
            'frequent_offenders' => $this->getFrequentOffendersData(),
            'late_duration' => $this->getLateDurationData(),
            default => $this->getDailyTrendData(),
        };
    }

    protected function getType(): string
    {
        return match ($this->filter) {
            'hourly_pattern' => 'bar',
            'frequent_offenders' => 'doughnut',
            'late_duration' => 'doughnut',
            default => 'line',
        };
    }

    private function getDailyTrendData(): array
    {
        $days = [];
        $lateCounts = [];

        // Get last 30 days
        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dayLabel = $date->format('M j');

            $lateCount = Absensi::whereDate('tanggal_absensi', $date->format('Y-m-d'))
                ->where('status', 'terlambat')
                ->count();

            $days[] = $dayLabel;
            $lateCounts[] = $lateCount;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Terlambat',
                    'data' => $lateCounts,
                    'borderColor' => 'rgb(245, 158, 11)',
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                    'tension' => 0.4,
                    'fill' => true,
                ],
            ],
            'labels' => $days,
        ];
    }

    private function getHourlyPatternData(): array
    {
        $hours = [];
        $lateCounts = [];

        // Analyze late arrivals by hour (8 AM to 12 PM)
        for ($hour = 8; $hour <= 12; $hour++) {
            $hourLabel = sprintf('%02d:00', $hour);
            
            $count = Absensi::where('status', 'terlambat')
                ->whereMonth('tanggal_absensi', now()->month)
                ->whereTime('waktu_masuk', '>=', sprintf('%02d:00:00', $hour))
                ->whereTime('waktu_masuk', '<', sprintf('%02d:59:59', $hour))
                ->count();

            $hours[] = $hourLabel;
            $lateCounts[] = $count;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Terlambat',
                    'data' => $lateCounts,
                    'backgroundColor' => [
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(220, 38, 38, 0.8)',
                        'rgba(185, 28, 28, 0.8)',
                        'rgba(153, 27, 27, 0.8)',
                    ],
                    'borderColor' => [
                        'rgb(245, 158, 11)',
                        'rgb(239, 68, 68)',
                        'rgb(220, 38, 38)',
                        'rgb(185, 28, 28)',
                        'rgb(153, 27, 27)',
                    ],
                    'borderWidth' => 1,
                ],
            ],
            'labels' => $hours,
        ];
    }

    private function getFrequentOffendersData(): array
    {
        // Get employees with most late arrivals this month
        $lateFrequency = Absensi::selectRaw('karyawan_id, COUNT(*) as late_count')
            ->where('status', 'terlambat')
            ->whereMonth('tanggal_absensi', now()->month)
            ->with('karyawan')
            ->groupBy('karyawan_id')
            ->orderBy('late_count', 'desc')
            ->limit(5)
            ->get();

        $names = [];
        $counts = [];

        foreach ($lateFrequency as $record) {
            $names[] = $record->karyawan->nama_lengkap ?? 'Unknown';
            $counts[] = $record->late_count;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Terlambat',
                    'data' => $counts,
                    'backgroundColor' => [
                        'rgb(239, 68, 68)',
                        'rgb(245, 158, 11)',
                        'rgb(59, 130, 246)',
                        'rgb(139, 92, 246)',
                        'rgb(236, 72, 153)',
                    ],
                ],
            ],
            'labels' => $names,
        ];
    }

    private function getLateDurationData(): array
    {
        // Categorize late arrivals by duration
        $categories = [
            '1-15 menit' => [1, 15],
            '16-30 menit' => [16, 30],
            '31-60 menit' => [31, 60],
            '> 60 menit' => [61, 999],
        ];

        $categoryNames = [];
        $categoryCounts = [];

        foreach ($categories as $name => $range) {
            // This is a simplified calculation - you might need to adjust based on your actual time tracking
            $count = Absensi::where('status', 'terlambat')
                ->whereMonth('tanggal_absensi', now()->month)
                ->count(); // Placeholder - you'd need actual duration calculation

            $categoryNames[] = $name;
            $categoryCounts[] = rand(5, 25); // Placeholder data
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Kasus',
                    'data' => $categoryCounts,
                    'backgroundColor' => [
                        'rgb(34, 197, 94)',   // Green for 1-15 min
                        'rgb(245, 158, 11)',  // Yellow for 16-30 min
                        'rgb(239, 68, 68)',   // Red for 31-60 min
                        'rgb(153, 27, 27)',   // Dark red for > 60 min
                    ],
                ],
            ],
            'labels' => $categoryNames,
        ];
    }

    protected function getOptions(): array
    {
        $baseOptions = [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'enabled' => true,
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];

        if ($this->filter === 'daily_trend') {
            $baseOptions['scales'] = [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
            ];
        } elseif ($this->filter === 'hourly_pattern') {
            $baseOptions['scales'] = [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
            ];
        }

        return $baseOptions;
    }
}
