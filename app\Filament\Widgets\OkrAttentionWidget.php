<?php

namespace App\Filament\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use App\Models\Objective;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;

class OkrAttentionWidget extends BaseWidget
{
    protected static ?int $sort = 2;

    protected static ?string $heading = 'Objectives Membutuhkan Perhatian';

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                Tables\Columns\TextColumn::make('nama_objective')
                    ->label('Objective')
                    ->searchable()
                    ->sortable()
                    ->weight('medium')
                    ->wrap()
                    ->limit(50),

                Tables\Columns\TextColumn::make('owner.name')
                    ->label('Owner')
                    ->sortable(),

                Tables\Columns\TextColumn::make('progress_percentage')
                    ->label('Progress')
                    ->formatStateUsing(fn($state) => $state . '%')
                    ->color(fn($state) => match (true) {
                        $state >= 80 => 'success',
                        $state >= 60 => 'warning',
                        $state >= 40 => 'info',
                        default => 'danger',
                    })
                    ->badge()
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'draft' => 'gray',
                        'active' => 'success',
                        'completed' => 'primary',
                        'cancelled' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'draft' => 'Draft',
                        'active' => 'Aktif',
                        'completed' => 'Selesai',
                        'cancelled' => 'Dibatalkan',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('target_completion')
                    ->label('Target')
                    ->date('d M Y')
                    ->sortable()
                    ->color(fn($record) => $record->is_overdue ? 'danger' : 'primary'),

                Tables\Columns\TextColumn::make('attention_reason')
                    ->label('Alasan')
                    ->getStateUsing(function ($record) {
                        $reasons = [];

                        if ($record->is_overdue) {
                            $reasons[] = 'Overdue';
                        }

                        if ($record->progress_percentage < 50 && $record->target_completion && $record->target_completion->diffInDays(now()) <= 14) {
                            $reasons[] = 'At Risk';
                        }

                        if ($record->updated_at < now()->subWeeks(2)) {
                            $reasons[] = 'Stalled';
                        }

                        if ($record->keyResults()->where('status', 'at_risk')->count() > 0) {
                            $reasons[] = 'Key Results at Risk';
                        }

                        if ($record->tactics()->where('status', 'blocked')->count() > 0) {
                            $reasons[] = 'Blocked Tactics';
                        }

                        return implode(', ', $reasons);
                    })
                    ->badge()
                    ->color('warning'),

                Tables\Columns\TextColumn::make('days_remaining')
                    ->label('Days Left')
                    ->getStateUsing(fn($record) => $record->days_remaining)
                    ->formatStateUsing(function ($state) {
                        if ($state === null) return '-';
                        if ($state < 0) return abs($state) . ' days overdue';
                        return $state . ' days left';
                    })
                    ->color(fn($state) => match (true) {
                        $state === null => 'gray',
                        $state < 0 => 'danger',
                        $state <= 7 => 'warning',
                        $state <= 30 => 'info',
                        default => 'success',
                    })
                    ->badge(),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('Lihat')
                    ->icon('heroicon-o-eye')
                    ->url(fn($record) => route('filament.admin.resources.objectives.edit', $record))
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('update_progress')
                    ->label('Update Progress')
                    ->icon('heroicon-o-arrow-path')
                    ->color('info')
                    ->action(function ($record) {
                        $record->calculateProgress();
                        $record->updateStatus();
                        $this->dispatch('$refresh');
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Update Progress')
                    ->modalDescription('Apakah Anda yakin ingin mengupdate progress objective ini?'),
            ])
            ->defaultSort('target_completion', 'asc')
            ->striped()
            ->paginated([5, 10, 25])
            ->poll('30s'); // Auto refresh every 30 seconds
    }

    protected function getTableQuery(): Builder
    {
        $user = Auth::user();

        return Objective::query()
            ->with(['owner', 'keyResults', 'tactics', 'departemen'])
            ->when(!in_array($user->role, ['admin', 'supervisor']), function ($query) use ($user) {
                $query->where('owner_id', $user->id);
            })
            ->where(function ($query) {
                // Objectives that need attention
                $query->where('target_completion', '<', now()) // Overdue
                    ->orWhere(function ($q) {
                        // At risk: low progress with approaching deadline
                        $q->where('progress_percentage', '<', 50)
                            ->where('target_completion', '<=', now()->addWeeks(2))
                            ->where('status', '!=', 'completed');
                    })
                    ->orWhere('updated_at', '<', now()->subWeeks(2)) // Stalled
                    ->orWhereHas('keyResults', function ($q) {
                        $q->where('status', 'at_risk');
                    })
                    ->orWhereHas('tactics', function ($q) {
                        $q->where('status', 'blocked');
                    });
            })
            ->where('status', '!=', 'completed'); // Exclude completed objectives
    }

    public static function canView(): bool
    {
        $user = auth()->user();
        return in_array($user->role, ['admin', 'supervisor']);
    }
}
