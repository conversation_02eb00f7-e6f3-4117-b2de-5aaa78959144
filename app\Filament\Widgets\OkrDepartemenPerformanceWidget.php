<?php

namespace App\Filament\Widgets;

use Filament\Widgets\ChartWidget;
use App\Models\Departemen;

class OkrDepartemenPerformanceWidget extends ChartWidget
{
    protected static ?string $heading = 'Performa Objective per Departemen';

    protected static ?int $sort = 2;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $maxHeight = '400px';

    public ?string $filter = 'all';

    protected function getData(): array
    {
        // Get departemen with their objectives performance
        $departemenData = Departemen::select('id', 'nama_departemen')
            ->withCount(['objectives' => function ($query) {
                $query->whereNotNull('departemen_id')
                    ->when($this->filter !== 'all', function ($q) {
                        $q->where('status', $this->filter);
                    });
            }])
            ->with(['objectives' => function ($query) {
                $query->select('id', 'departemen_id', 'progress_percentage', 'status')
                    ->whereNotNull('departemen_id')
                    ->when($this->filter !== 'all', function ($q) {
                        $q->where('status', $this->filter);
                    });
            }])
            ->having('objectives_count', '>', 0)
            ->orderBy('nama_departemen')
            ->get();

        $labels = [];
        $progressData = [];
        $completedData = [];
        $activeData = [];
        $colors = [
            'rgba(59, 130, 246, 0.8)',   // Blue
            'rgba(16, 185, 129, 0.8)',   // Green
            'rgba(245, 158, 11, 0.8)',   // Yellow
            'rgba(239, 68, 68, 0.8)',    // Red
            'rgba(139, 92, 246, 0.8)',   // Purple
            'rgba(236, 72, 153, 0.8)',   // Pink
            'rgba(14, 165, 233, 0.8)',   // Sky
            'rgba(34, 197, 94, 0.8)',    // Emerald
        ];

        foreach ($departemenData as $departemen) {
            $labels[] = $departemen->nama_departemen;

            // Calculate average progress
            $avgProgress = $departemen->objectives->avg('progress_percentage') ?? 0;
            $progressData[] = round($avgProgress, 1);

            // Count completed and active objectives
            $completedCount = $departemen->objectives->where('status', 'completed')->count();
            $activeCount = $departemen->objectives->where('status', 'active')->count();

            $completedData[] = $completedCount;
            $activeData[] = $activeCount;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Progress Rata-rata (%)',
                    'data' => $progressData,
                    'backgroundColor' => array_slice($colors, 0, count($labels)),
                    'borderColor' => array_map(fn($color) => str_replace('0.8', '1', $color), array_slice($colors, 0, count($labels))),
                    'borderWidth' => 2,
                    'type' => 'bar',
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'Objectives Selesai',
                    'data' => $completedData,
                    'backgroundColor' => 'rgba(16, 185, 129, 0.6)',
                    'borderColor' => 'rgba(16, 185, 129, 1)',
                    'borderWidth' => 2,
                    'type' => 'line',
                    'yAxisID' => 'y1',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Objectives Aktif',
                    'data' => $activeData,
                    'backgroundColor' => 'rgba(245, 158, 11, 0.6)',
                    'borderColor' => 'rgba(245, 158, 11, 1)',
                    'borderWidth' => 2,
                    'type' => 'line',
                    'yAxisID' => 'y1',
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
            'scales' => [
                'x' => [
                    'display' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Departemen',
                    ],
                ],
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
                    'title' => [
                        'display' => true,
                        'text' => 'Progress (%)',
                    ],
                    'min' => 0,
                    'max' => 100,
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
                    'title' => [
                        'display' => true,
                        'text' => 'Jumlah Objectives',
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                ],
            ],
            'interaction' => [
                'mode' => 'index',
                'intersect' => false,
            ],
        ];
    }

    protected function getFilters(): ?array
    {
        return [
            'all' => 'Semua Status',
            'active' => 'Aktif',
            'completed' => 'Selesai',
            'draft' => 'Draft',
        ];
    }
}
