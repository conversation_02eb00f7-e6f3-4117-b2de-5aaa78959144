<?php

namespace App\Filament\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use App\Models\Departemen;
use Illuminate\Database\Eloquent\Builder;

class OkrDepartemenTableWidget extends BaseWidget
{
    protected static ?string $heading = 'Detail Performa per Departemen';

    protected static ?int $sort = 5;

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Departemen::query()
                    ->withCount([
                        'objectives',
                        'objectives as active_objectives_count' => function (Builder $query) {
                            $query->where('status', 'active');
                        },
                        'objectives as completed_objectives_count' => function (Builder $query) {
                            $query->where('status', 'completed');
                        },
                        'objectives as draft_objectives_count' => function (Builder $query) {
                            $query->where('status', 'draft');
                        },
                    ])
                    ->withAvg('objectives', 'progress_percentage')
                    ->having('objectives_count', '>', 0)
            )
            ->columns([
                Tables\Columns\TextColumn::make('nama_departemen')
                    ->label('Departemen')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('objectives_count')
                    ->label('Total Objectives')
                    ->alignCenter()
                    ->sortable()
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('active_objectives_count')
                    ->label('Aktif')
                    ->alignCenter()
                    ->sortable()
                    ->badge()
                    ->color('warning'),

                Tables\Columns\TextColumn::make('completed_objectives_count')
                    ->label('Selesai')
                    ->alignCenter()
                    ->sortable()
                    ->badge()
                    ->color('success'),

                Tables\Columns\TextColumn::make('draft_objectives_count')
                    ->label('Draft')
                    ->alignCenter()
                    ->sortable()
                    ->badge()
                    ->color('gray'),

                Tables\Columns\TextColumn::make('objectives_avg_progress_percentage')
                    ->label('Progress Rata-rata')
                    ->alignCenter()
                    ->sortable()
                    ->formatStateUsing(fn ($state) => $state ? round($state, 1) . '%' : '0%')
                    ->badge()
                    ->color(fn ($state) => match (true) {
                        $state >= 80 => 'success',
                        $state >= 60 => 'warning',
                        $state >= 40 => 'info',
                        default => 'danger',
                    }),

                Tables\Columns\TextColumn::make('completion_rate')
                    ->label('Tingkat Penyelesaian')
                    ->alignCenter()
                    ->state(function ($record) {
                        if ($record->objectives_count == 0) return 0;
                        return round(($record->completed_objectives_count / $record->objectives_count) * 100, 1);
                    })
                    ->formatStateUsing(fn ($state) => $state . '%')
                    ->badge()
                    ->color(fn ($state) => match (true) {
                        $state >= 80 => 'success',
                        $state >= 60 => 'warning',
                        $state >= 40 => 'info',
                        default => 'danger',
                    }),

                Tables\Columns\TextColumn::make('performance_indicator')
                    ->label('Indikator Performa')
                    ->alignCenter()
                    ->state(function ($record) {
                        $avgProgress = $record->objectives_avg_progress_percentage ?? 0;
                        $completionRate = $record->objectives_count > 0 
                            ? ($record->completed_objectives_count / $record->objectives_count) * 100 
                            : 0;
                        
                        $score = ($avgProgress * 0.6) + ($completionRate * 0.4);
                        
                        return match (true) {
                            $score >= 85 => 'Excellent',
                            $score >= 70 => 'Good',
                            $score >= 55 => 'Average',
                            $score >= 40 => 'Below Average',
                            default => 'Poor',
                        };
                    })
                    ->badge()
                    ->color(fn ($state) => match ($state) {
                        'Excellent' => 'success',
                        'Good' => 'info',
                        'Average' => 'warning',
                        'Below Average' => 'danger',
                        'Poor' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->defaultSort('objectives_avg_progress_percentage', 'desc')
            ->filters([
                Tables\Filters\Filter::make('high_performance')
                    ->label('High Performance (>80%)')
                    ->query(fn (Builder $query): Builder => $query->having('objectives_avg_progress_percentage', '>', 80)),
                
                Tables\Filters\Filter::make('needs_attention')
                    ->label('Needs Attention (<50%)')
                    ->query(fn (Builder $query): Builder => $query->having('objectives_avg_progress_percentage', '<', 50)),
                
                Tables\Filters\Filter::make('most_active')
                    ->label('Most Active (>5 objectives)')
                    ->query(fn (Builder $query): Builder => $query->having('objectives_count', '>', 5)),
            ])
            ->actions([
                Tables\Actions\Action::make('view_objectives')
                    ->label('Lihat Objectives')
                    ->icon('heroicon-o-eye')
                    ->url(fn ($record) => route('filament.admin.resources.objectives.index', [
                        'tableFilters[departemen_id][value]' => $record->id
                    ]))
                    ->openUrlInNewTab(),
            ])
            ->emptyStateHeading('Tidak ada data departemen')
            ->emptyStateDescription('Belum ada departemen yang memiliki objectives.')
            ->emptyStateIcon('heroicon-o-building-office');
    }
}
