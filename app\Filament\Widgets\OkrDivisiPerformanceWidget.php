<?php

namespace App\Filament\Widgets;

use Filament\Widgets\ChartWidget;
use App\Models\Divisi;

class OkrDivisiPerformanceWidget extends ChartWidget
{
    protected static ?string $heading = 'Performa Objective per Divisi';

    protected static ?int $sort = 3;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $maxHeight = '400px';

    public ?string $filter = 'all';

    protected function getData(): array
    {
        // Get divisi with their objectives performance
        $divisiData = Divisi::select('id', 'nama_divisi', 'departemen_id')
            ->with(['departemen:id,nama_departemen'])
            ->withCount(['objectives' => function ($query) {
                $query->whereNotNull('divisi_id')
                    ->when($this->filter !== 'all', function ($q) {
                        $q->where('status', $this->filter);
                    });
            }])
            ->with(['objectives' => function ($query) {
                $query->select('id', 'divisi_id', 'progress_percentage', 'status')
                    ->whereNotNull('divisi_id')
                    ->when($this->filter !== 'all', function ($q) {
                        $q->where('status', $this->filter);
                    });
            }])
            ->having('objectives_count', '>', 0)
            ->orderBy('nama_divisi')
            ->get();

        $labels = [];
        $progressData = [];
        $objectiveCountData = [];
        $backgroundColors = [];

        // Color mapping for departments
        $departmentColors = [
            'Technology' => 'rgba(59, 130, 246, 0.8)',      // Blue
            'Human Resources' => 'rgba(16, 185, 129, 0.8)', // Green
            'Marketing' => 'rgba(245, 158, 11, 0.8)',       // Yellow
            'Finance' => 'rgba(239, 68, 68, 0.8)',          // Red
            'Operations' => 'rgba(139, 92, 246, 0.8)',      // Purple
        ];

        foreach ($divisiData as $divisi) {
            $departemenName = $divisi->departemen ? $divisi->departemen->nama_departemen : 'Unknown';
            $labels[] = $divisi->nama_divisi . "\n(" . $departemenName . ")";

            // Calculate average progress
            $avgProgress = $divisi->objectives->avg('progress_percentage') ?? 0;
            $progressData[] = round($avgProgress, 1);

            // Count total objectives
            $objectiveCountData[] = $divisi->objectives->count();

            // Set color based on department
            $backgroundColors[] = $departmentColors[$departemenName] ?? 'rgba(107, 114, 128, 0.8)';
        }

        return [
            'datasets' => [
                [
                    'label' => 'Progress Rata-rata (%)',
                    'data' => $progressData,
                    'backgroundColor' => $backgroundColors,
                    'borderColor' => array_map(fn($color) => str_replace('0.8', '1', $color), $backgroundColors),
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'right',
                    'labels' => [
                        'usePointStyle' => true,
                        'padding' => 20,
                        'font' => [
                            'size' => 12,
                        ],
                    ],
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            return context.label + ": " + context.parsed + "%";
                        }',
                    ],
                ],
            ],
            'cutout' => '50%',
        ];
    }

    protected function getFilters(): ?array
    {
        return [
            'all' => 'Semua Status',
            'active' => 'Aktif',
            'completed' => 'Selesai',
            'draft' => 'Draft',
        ];
    }
}
