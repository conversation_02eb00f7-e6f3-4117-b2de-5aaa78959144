<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;
use App\Models\Departemen;
use App\Models\Divisi;

class OkrLeaderboardWidget extends Widget
{
    protected static string $view = 'filament.widgets.okr-leaderboard';

    protected static ?int $sort = 7;

    protected int | string | array $columnSpan = 'full';

    public function getViewData(): array
    {
        // Get departemen leaderboard
        $departemenLeaderboard = Departemen::select('id', 'nama_departemen')
            ->withCount([
                'objectives',
                'objectives as completed_objectives_count' => function ($query) {
                    $query->where('status', 'completed');
                }
            ])
            ->withAvg('objectives', 'progress_percentage')
            ->having('objectives_count', '>', 0)
            ->get()
            ->map(function ($dept) {
                $completionRate = $dept->objectives_count > 0
                    ? round(($dept->completed_objectives_count / $dept->objectives_count) * 100, 1)
                    : 0;

                $avgProgress = round($dept->objectives_avg_progress_percentage ?? 0, 1);

                // Calculate performance score (60% progress + 40% completion rate)
                $performanceScore = round(($avgProgress * 0.6) + ($completionRate * 0.4), 1);

                return [
                    'nama' => $dept->nama_departemen,
                    'objectives_count' => $dept->objectives_count,
                    'completed_count' => $dept->completed_objectives_count,
                    'avg_progress' => $avgProgress,
                    'completion_rate' => $completionRate,
                    'performance_score' => $performanceScore,
                    'rank' => 0, // Will be set after sorting
                ];
            })
            ->sortByDesc('performance_score')
            ->values()
            ->map(function ($item, $index) {
                $item['rank'] = $index + 1;
                return $item;
            })
            ->take(5);

        // Get divisi leaderboard
        $divisiLeaderboard = Divisi::select('id', 'nama_divisi', 'departemen_id')
            ->with('departemen:id,nama_departemen')
            ->withCount([
                'objectives',
                'objectives as completed_objectives_count' => function ($query) {
                    $query->where('status', 'completed');
                }
            ])
            ->withAvg('objectives', 'progress_percentage')
            ->having('objectives_count', '>', 0)
            ->get()
            ->map(function ($divisi) {
                $completionRate = $divisi->objectives_count > 0
                    ? round(($divisi->completed_objectives_count / $divisi->objectives_count) * 100, 1)
                    : 0;

                $avgProgress = round($divisi->objectives_avg_progress_percentage ?? 0, 1);

                // Calculate performance score (60% progress + 40% completion rate)
                $performanceScore = round(($avgProgress * 0.6) + ($completionRate * 0.4), 1);

                return [
                    'nama' => $divisi->nama_divisi,
                    'departemen' => $divisi->departemen ? $divisi->departemen->nama_departemen : 'Unknown',
                    'objectives_count' => $divisi->objectives_count,
                    'completed_count' => $divisi->completed_objectives_count,
                    'avg_progress' => $avgProgress,
                    'completion_rate' => $completionRate,
                    'performance_score' => $performanceScore,
                    'rank' => 0, // Will be set after sorting
                ];
            })
            ->sortByDesc('performance_score')
            ->values()
            ->map(function ($item, $index) {
                $item['rank'] = $index + 1;
                return $item;
            })
            ->take(5);

        return [
            'departemenLeaderboard' => $departemenLeaderboard,
            'divisiLeaderboard' => $divisiLeaderboard,
        ];
    }

    public static function canView(): bool
    {
        $user = auth()->user();
        return in_array($user->role, ['admin', 'supervisor']);
    }
}
