<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Objective;
use App\Models\Departemen;
use App\Models\Divisi;

class OkrPerformanceComparisonWidget extends BaseWidget
{
    protected static ?int $sort = 4;

    protected int | string | array $columnSpan = 'full';

    protected function getStats(): array
    {
        // Get top performing department
        $topDepartemen = Departemen::select('id', 'nama_departemen')
            ->withAvg('objectives', 'progress_percentage')
            ->withCount('objectives')
            ->having('objectives_count', '>', 0)
            ->orderByDesc('objectives_avg_progress_percentage')
            ->first();

        // Get lowest performing department
        $lowestDepartemen = Departemen::select('id', 'nama_departemen')
            ->withAvg('objectives', 'progress_percentage')
            ->withCount('objectives')
            ->having('objectives_count', '>', 0)
            ->orderBy('objectives_avg_progress_percentage')
            ->first();

        // Get top performing divisi
        $topDivisi = Divisi::select('id', 'nama_divisi')
            ->with('departemen:id,nama_departemen')
            ->withAvg('objectives', 'progress_percentage')
            ->withCount('objectives')
            ->having('objectives_count', '>', 0)
            ->orderByDesc('objectives_avg_progress_percentage')
            ->first();

        // Get most active department (most objectives)
        $mostActiveDepartemen = Departemen::select('id', 'nama_departemen')
            ->withCount('objectives')
            ->orderByDesc('objectives_count')
            ->first();

        // Calculate completion rates by department
        $departemenCompletionRates = Departemen::select('id', 'nama_departemen')
            ->withCount([
                'objectives',
                'objectives as completed_objectives_count' => function ($query) {
                    $query->where('status', 'completed');
                }
            ])
            ->having('objectives_count', '>', 0)
            ->get()
            ->map(function ($dept) {
                $completionRate = $dept->objectives_count > 0
                    ? round(($dept->completed_objectives_count / $dept->objectives_count) * 100)
                    : 0;
                return [
                    'nama' => $dept->nama_departemen,
                    'rate' => $completionRate,
                    'completed' => $dept->completed_objectives_count,
                    'total' => $dept->objectives_count,
                ];
            })
            ->sortByDesc('rate');

        $topCompletionDept = $departemenCompletionRates->first();

        return [
            Stat::make('Top Performer (Departemen)', $topDepartemen?->nama_departemen ?? 'N/A')
                ->description($topDepartemen ? round($topDepartemen->objectives_avg_progress_percentage, 1) . '% progress rata-rata' : 'Tidak ada data')
                ->descriptionIcon('heroicon-m-trophy')
                ->color('success')
                ->chart($this->getDepartemenProgressChart()),

            Stat::make('Needs Attention (Departemen)', $lowestDepartemen?->nama_departemen ?? 'N/A')
                ->description($lowestDepartemen ? round($lowestDepartemen->objectives_avg_progress_percentage, 1) . '% progress rata-rata' : 'Tidak ada data')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('danger'),

            Stat::make('Top Performer (Divisi)', $topDivisi?->nama_divisi ?? 'N/A')
                ->description($topDivisi ?
                    round($topDivisi->objectives_avg_progress_percentage, 1) . '% (' . ($topDivisi->departemen?->nama_departemen ?? 'Unknown') . ')'
                    : 'Tidak ada data')
                ->descriptionIcon('heroicon-m-star')
                ->color('warning'),

            Stat::make('Most Active (Departemen)', $mostActiveDepartemen?->nama_departemen ?? 'N/A')
                ->description($mostActiveDepartemen ? $mostActiveDepartemen->objectives_count . ' objectives' : 'Tidak ada data')
                ->descriptionIcon('heroicon-m-fire')
                ->color('info'),

            Stat::make('Best Completion Rate', $topCompletionDept['nama'] ?? 'N/A')
                ->description($topCompletionDept ?
                    $topCompletionDept['rate'] . '% (' . $topCompletionDept['completed'] . '/' . $topCompletionDept['total'] . ')'
                    : 'Tidak ada data')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('Total Departments Active', Departemen::has('objectives')->count())
                ->description('Departemen dengan objectives aktif')
                ->descriptionIcon('heroicon-m-building-office')
                ->color('primary'),
        ];
    }

    protected function getDepartemenProgressChart(): array
    {
        // Get progress trend for top department over last 7 days
        $topDepartemen = Departemen::withAvg('objectives', 'progress_percentage')
            ->withCount('objectives')
            ->having('objectives_count', '>', 0)
            ->orderByDesc('objectives_avg_progress_percentage')
            ->first();

        if (!$topDepartemen) {
            return [0, 0, 0, 0, 0, 0, 0];
        }

        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $avgProgress = Objective::where('departemen_id', $topDepartemen->id)
                ->whereDate('updated_at', '<=', $date)
                ->avg('progress_percentage') ?? 0;
            $data[] = round($avgProgress);
        }

        return $data;
    }
}
