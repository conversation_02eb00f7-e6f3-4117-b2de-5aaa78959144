<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Objective;
use App\Models\KeyResult;
use App\Models\Tactic;
use App\Models\Task;

class OkrPerformanceWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $objectives = Objective::query();
        $keyResults = KeyResult::query();
        $tactics = Tactic::query();
        $tasks = Task::whereHas('objectives');

        // Overall Progress
        $avgProgress = $objectives->avg('progress_percentage') ?? 0;
        
        // Completion Rates
        $completedObjectives = $objectives->where('status', 'completed')->count();
        $totalObjectives = $objectives->count();
        $completionRate = $totalObjectives > 0 ? round(($completedObjectives / $totalObjectives) * 100) : 0;

        // At Risk Items
        $atRiskObjectives = $objectives->where('progress_percentage', '<', 50)
                                     ->where('status', '!=', 'completed')
                                     ->count();
        
        $atRiskKeyResults = $keyResults->where('status', 'at_risk')->count();

        // Task Completion
        $completedTasks = $tasks->where('status', 'completed')->count();
        $totalTasks = $tasks->count();
        $taskCompletionRate = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100) : 0;

        return [
            Stat::make('Overall Progress', round($avgProgress) . '%')
                ->description('Average progress across all objectives')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color($avgProgress >= 80 ? 'success' : ($avgProgress >= 60 ? 'warning' : 'danger'))
                ->chart($this->getProgressChart()),

            Stat::make('Completion Rate', $completionRate . '%')
                ->description($completedObjectives . ' of ' . $totalObjectives . ' objectives completed')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color($completionRate >= 80 ? 'success' : ($completionRate >= 60 ? 'warning' : 'danger')),

            Stat::make('At Risk Items', $atRiskObjectives + $atRiskKeyResults)
                ->description('Objectives and Key Results needing attention')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($atRiskObjectives + $atRiskKeyResults > 0 ? 'danger' : 'success'),

            Stat::make('Task Completion', $taskCompletionRate . '%')
                ->description($completedTasks . ' of ' . $totalTasks . ' tasks completed')
                ->descriptionIcon('heroicon-m-list-bullet')
                ->color($taskCompletionRate >= 80 ? 'success' : ($taskCompletionRate >= 60 ? 'warning' : 'danger')),
        ];
    }

    protected function getProgressChart(): array
    {
        // Get progress data for the last 7 days
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $avgProgress = Objective::whereDate('updated_at', '<=', $date)
                                  ->avg('progress_percentage') ?? 0;
            $data[] = round($avgProgress);
        }
        
        return $data;
    }

    protected static ?int $sort = 1;
}
