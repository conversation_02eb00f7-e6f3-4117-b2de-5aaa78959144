<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;
use App\Models\Objective;
use App\Models\KeyResult;
use App\Models\Tactic;
use Illuminate\Support\Facades\Auth;

class OkrQuickActionsWidget extends Widget
{
    protected static string $view = 'filament.widgets.okr-quick-actions';
    
    protected static ?int $sort = 3;
    
    protected int | string | array $columnSpan = 'full';

    public function getViewData(): array
    {
        $user = Auth::user();
        
        // Get recent activities
        $recentObjectives = Objective::when(!in_array($user->role, ['admin', 'supervisor']), function ($query) use ($user) {
                $query->where('owner_id', $user->id);
            })
            ->orderBy('updated_at', 'desc')
            ->limit(5)
            ->get();

        // Get objectives needing attention
        $needsAttention = Objective::when(!in_array($user->role, ['admin', 'supervisor']), function ($query) use ($user) {
                $query->where('owner_id', $user->id);
            })
            ->where(function ($query) {
                $query->where('target_completion', '<', now())
                    ->orWhere(function ($q) {
                        $q->where('progress_percentage', '<', 50)
                          ->where('target_completion', '<=', now()->addWeeks(2));
                    });
            })
            ->where('status', '!=', 'completed')
            ->limit(3)
            ->get();

        // Get quick stats
        $quickStats = [
            'objectives_due_this_week' => Objective::when(!in_array($user->role, ['admin', 'supervisor']), function ($query) use ($user) {
                    $query->where('owner_id', $user->id);
                })
                ->whereBetween('target_completion', [now()->startOfWeek(), now()->endOfWeek()])
                ->where('status', '!=', 'completed')
                ->count(),
                
            'key_results_at_risk' => KeyResult::whereHas('objective', function ($query) use ($user) {
                    $query->when(!in_array($user->role, ['admin', 'supervisor']), function ($q) use ($user) {
                        $q->where('owner_id', $user->id);
                    });
                })
                ->where('status', 'at_risk')
                ->count(),
                
            'blocked_tactics' => Tactic::whereHas('objective', function ($query) use ($user) {
                    $query->when(!in_array($user->role, ['admin', 'supervisor']), function ($q) use ($user) {
                        $q->where('owner_id', $user->id);
                    });
                })
                ->where('status', 'blocked')
                ->count(),
        ];

        return [
            'recentObjectives' => $recentObjectives,
            'needsAttention' => $needsAttention,
            'quickStats' => $quickStats,
        ];
    }

    public static function canView(): bool
    {
        $user = auth()->user();
        return in_array($user->role, ['admin', 'supervisor']);
    }
}
