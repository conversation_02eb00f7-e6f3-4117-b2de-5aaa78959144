<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Objective;
use App\Models\KeyResult;
use App\Models\Tactic;
use Illuminate\Support\Facades\Auth;

class OkrStatsWidget extends BaseWidget
{
    protected static ?int $sort = 1;

    protected static bool $isLazy = false;

    protected function getStats(): array
    {
        $user = Auth::user();

        // Get objectives data based on user role
        $objectives = Objective::when(!in_array($user->role, ['admin', 'supervisor']), function ($query) use ($user) {
            $query->where('owner_id', $user->id);
        })->get();

        $totalObjectives = $objectives->count();
        $activeObjectives = $objectives->where('status', 'active')->count();
        $completedObjectives = $objectives->where('status', 'completed')->count();
        $overallProgress = $objectives->avg('progress_percentage') ?? 0;

        // Get key results data
        $keyResults = KeyResult::whereIn('objective_id', $objectives->pluck('id'))->get();
        $atRiskKeyResults = $keyResults->where('status', 'at_risk')->count();
        $completedKeyResults = $keyResults->where('status', 'completed')->count();

        // Get tactics data
        $tactics = Tactic::whereIn('objective_id', $objectives->pluck('id'))->get();
        $blockedTactics = $tactics->where('status', 'blocked')->count();
        $completedTactics = $tactics->where('status', 'completed')->count();

        // Calculate trends (compare with last month)
        $lastMonthObjectives = Objective::where('created_at', '>=', now()->subMonth())
            ->when(!in_array($user->role, ['admin', 'supervisor']), function ($query) use ($user) {
                $query->where('owner_id', $user->id);
            })->count();

        $objectiveTrend = $lastMonthObjectives > 0 ? '+' . $lastMonthObjectives : null;

        return [
            Stat::make('Total Objectives', $totalObjectives)
                ->description($activeObjectives . ' aktif, ' . $completedObjectives . ' selesai')
                ->descriptionIcon('heroicon-m-flag')
                ->chart($this->getObjectiveChart($objectives))
                ->color('primary')
                ->extraAttributes([
                    'class' => 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors',
                ]),

            Stat::make('Overall Progress', round($overallProgress) . '%')
                ->description($this->getProgressDescription($overallProgress))
                ->descriptionIcon($this->getProgressIcon($overallProgress))
                ->chart($this->getProgressChart($objectives))
                ->color($this->getProgressColor($overallProgress))
                ->extraAttributes([
                    'class' => 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors',
                ]),

            Stat::make('Key Results', $keyResults->count())
                ->description($completedKeyResults . ' selesai' . ($atRiskKeyResults > 0 ? ', ' . $atRiskKeyResults . ' berisiko' : ''))
                ->descriptionIcon($atRiskKeyResults > 0 ? 'heroicon-m-exclamation-triangle' : 'heroicon-m-key')
                ->chart($this->getKeyResultChart($keyResults))
                ->color($atRiskKeyResults > 0 ? 'warning' : 'info')
                ->extraAttributes([
                    'class' => 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors',
                ]),

            Stat::make('Tactics', $tactics->count())
                ->description($completedTactics . ' selesai' . ($blockedTactics > 0 ? ', ' . $blockedTactics . ' terblokir' : ''))
                ->descriptionIcon($blockedTactics > 0 ? 'heroicon-m-x-circle' : 'heroicon-m-puzzle-piece')
                ->chart($this->getTacticChart($tactics))
                ->color($blockedTactics > 0 ? 'danger' : 'success')
                ->extraAttributes([
                    'class' => 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors',
                ]),
        ];
    }

    private function getObjectiveChart($objectives): array
    {
        // Generate simple chart data for objectives over time
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $count = $objectives->where('created_at', '<=', $date)->count();
            $data[] = $count;
        }
        return $data;
    }

    private function getProgressChart($objectives): array
    {
        // Generate progress chart data
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $avgProgress = $objectives->where('updated_at', '<=', $date)->avg('progress_percentage') ?? 0;
            $data[] = round($avgProgress);
        }
        return $data;
    }

    private function getKeyResultChart($keyResults): array
    {
        // Generate key result status distribution
        $completed = $keyResults->where('status', 'completed')->count();
        $inProgress = $keyResults->where('status', 'in_progress')->count();
        $notStarted = $keyResults->where('status', 'not_started')->count();
        $atRisk = $keyResults->where('status', 'at_risk')->count();

        return [$completed, $inProgress, $notStarted, $atRisk];
    }

    private function getTacticChart($tactics): array
    {
        // Generate tactic status distribution
        $completed = $tactics->where('status', 'completed')->count();
        $inProgress = $tactics->where('status', 'in_progress')->count();
        $planned = $tactics->where('status', 'planned')->count();
        $blocked = $tactics->where('status', 'blocked')->count();

        return [$completed, $inProgress, $planned, $blocked];
    }

    private function getProgressDescription(float $progress): string
    {
        return match (true) {
            $progress >= 90 => 'Excellent progress!',
            $progress >= 75 => 'Great progress!',
            $progress >= 60 => 'Good progress',
            $progress >= 40 => 'Making progress',
            $progress >= 20 => 'Slow progress',
            default => 'Needs attention',
        };
    }

    private function getProgressIcon(float $progress): string
    {
        return match (true) {
            $progress >= 80 => 'heroicon-m-arrow-trending-up',
            $progress >= 60 => 'heroicon-m-arrow-right',
            $progress >= 40 => 'heroicon-m-minus',
            default => 'heroicon-m-arrow-trending-down',
        };
    }

    private function getProgressColor(float $progress): string
    {
        return match (true) {
            $progress >= 80 => 'success',
            $progress >= 60 => 'info',
            $progress >= 40 => 'warning',
            default => 'danger',
        };
    }

    public static function canView(): bool
    {
        $user = auth()->user();
        return in_array($user->role, ['admin', 'supervisor']);
    }
}
