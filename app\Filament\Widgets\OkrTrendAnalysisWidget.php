<?php

namespace App\Filament\Widgets;

use Filament\Widgets\ChartWidget;
use App\Models\Objective;
use App\Models\Departemen;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class OkrTrendAnalysisWidget extends ChartWidget
{
    protected static ?string $heading = 'Tren Performa OKR (30 Hari <PERSON>)';

    protected static ?int $sort = 6;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $maxHeight = '400px';

    public ?string $filter = 'progress';

    protected function getData(): array
    {
        $days = 30;
        $dates = [];
        $overallData = [];
        $departemenData = [];

        // Generate dates for the last 30 days
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dates[] = $date->format('M d');

            if ($this->filter === 'progress') {
                // Overall progress trend
                $avgProgress = Objective::whereDate('updated_at', '<=', $date)
                    ->avg('progress_percentage') ?? 0;
                $overallData[] = round($avgProgress, 1);
            } else {
                // Completion trend
                $completedCount = Objective::where('status', 'completed')
                    ->whereDate('updated_at', '<=', $date)
                    ->count();
                $overallData[] = $completedCount;
            }
        }

        // Get top 3 departments for comparison
        $topDepartemen = Departemen::withAvg('objectives', 'progress_percentage')
            ->withCount('objectives')
            ->having('objectives_count', '>', 0)
            ->orderByDesc('objectives_avg_progress_percentage')
            ->take(3)
            ->get();

        $datasets = [
            [
                'label' => $this->filter === 'progress' ? 'Overall Progress (%)' : 'Total Completed',
                'data' => $overallData,
                'borderColor' => 'rgba(59, 130, 246, 1)',
                'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                'tension' => 0.4,
                'fill' => true,
            ]
        ];

        $colors = [
            'rgba(16, 185, 129, 1)',   // Green
            'rgba(245, 158, 11, 1)',   // Yellow
            'rgba(239, 68, 68, 1)',    // Red
        ];

        foreach ($topDepartemen as $index => $departemen) {
            $deptData = [];
            
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i);
                
                if ($this->filter === 'progress') {
                    $avgProgress = Objective::where('departemen_id', $departemen->id)
                        ->whereDate('updated_at', '<=', $date)
                        ->avg('progress_percentage') ?? 0;
                    $deptData[] = round($avgProgress, 1);
                } else {
                    $completedCount = Objective::where('departemen_id', $departemen->id)
                        ->where('status', 'completed')
                        ->whereDate('updated_at', '<=', $date)
                        ->count();
                    $deptData[] = $completedCount;
                }
            }

            $datasets[] = [
                'label' => $departemen->nama_departemen,
                'data' => $deptData,
                'borderColor' => $colors[$index] ?? 'rgba(107, 114, 128, 1)',
                'backgroundColor' => 'transparent',
                'tension' => 0.4,
                'borderWidth' => 2,
            ];
        }

        return [
            'datasets' => $datasets,
            'labels' => $dates,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
            'scales' => [
                'x' => [
                    'display' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Tanggal',
                    ],
                ],
                'y' => [
                    'display' => true,
                    'title' => [
                        'display' => true,
                        'text' => $this->filter === 'progress' ? 'Progress (%)' : 'Jumlah Completed',
                    ],
                    'beginAtZero' => true,
                    'max' => $this->filter === 'progress' ? 100 : null,
                ],
            ],
            'interaction' => [
                'mode' => 'index',
                'intersect' => false,
            ],
        ];
    }

    protected function getFilters(): ?array
    {
        return [
            'progress' => 'Progress Trend',
            'completion' => 'Completion Trend',
        ];
    }
}
