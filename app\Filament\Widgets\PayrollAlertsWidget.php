<?php

namespace App\Filament\Widgets;

use App\Models\PenggajianK<PERSON>awan;
use App\Models\Karyawan;
use App\Models\KaryawanBpjs;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class PayrollAlertsWidget extends BaseWidget
{
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;
    //protected static ?string $heading = 'Alert & Notifikasi Payroll';
    protected static ?int $sort = 5;
    protected int | string | array $columnSpan = 'full';

    protected function getStats(): array
    {
        return [
            // Employees Without Payroll This Month
            Stat::make('Belum Ada Payroll', $this->getEmployeesWithoutPayroll())
                ->description('Karyawan belum diproses payroll bulan ini')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($this->getPayrollProcessingColor()),

            // Salary Anomalies
            Stat::make('Anomali Gaji', $this->getSalaryAnomalies())
                ->description('Gaji di luar rentang normal')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('warning'),

            // Missing BPJS Data
            Stat::make('Data BPJS Kosong', $this->getMissingBpjsData())
                ->description('Karyawan tanpa data BPJS')
                ->descriptionIcon('heroicon-m-shield-exclamation')
                ->color('danger'),

            // High Deductions
            Stat::make('Potongan Tinggi', $this->getHighDeductions())
                ->description('Potongan > 30% dari gaji kotor')
                ->descriptionIcon('heroicon-m-minus-circle')
                ->color('warning'),

            // Payroll Budget Alert
            Stat::make('Budget Alert', $this->getBudgetAlert())
                ->description($this->getBudgetDescription())
                ->descriptionIcon('heroicon-m-banknotes')
                ->color($this->getBudgetColor()),

            // Pending Approvals
            Stat::make('Pending Approval', $this->getPendingApprovals())
                ->description('Payroll menunggu persetujuan')
                ->descriptionIcon('heroicon-m-clock')
                ->color('info'),
        ];
    }

    private function getEmployeesWithoutPayroll(): int
    {
        $currentMonth = now()->format('Y-m');
        $processedEmployees = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->pluck('karyawan_id')
            ->unique();

        $totalActiveEmployees = Karyawan::where('status_aktif', true)->count();

        return $totalActiveEmployees - $processedEmployees->count();
    }

    private function getPayrollProcessingColor(): string
    {
        $count = $this->getEmployeesWithoutPayroll();

        if ($count === 0) {
            return 'success';
        } elseif ($count <= 10) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    private function getSalaryAnomalies(): int
    {
        $currentMonth = now()->format('Y-m');

        // Calculate average salary
        $avgSalary = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->avg('gaji_pokok');

        if (!$avgSalary) {
            return 0;
        }

        // Define anomaly as salary > 3x average or < 0.3x average
        $upperLimit = $avgSalary * 3;
        $lowerLimit = $avgSalary * 0.3;

        return PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->where(function ($query) use ($upperLimit, $lowerLimit) {
                $query->where('gaji_pokok', '>', $upperLimit)
                    ->orWhere('gaji_pokok', '<', $lowerLimit);
            })
            ->count();
    }

    private function getMissingBpjsData(): int
    {
        $activeEmployees = Karyawan::where('status_aktif', true)->count();
        $employeesWithBpjs = KaryawanBpjs::distinct('karyawan_id')->count();

        return $activeEmployees - $employeesWithBpjs;
    }

    private function getHighDeductions(): int
    {
        $currentMonth = now()->format('Y-m');

        return PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->whereRaw('(bpjs_kesehatan_dipotong + bpjs_tk_dipotong + potongan_lainnya) > (gaji_pokok + tunjangan_jabatan + tunjangan_umum + tunjangan_sembako) * 0.3')
            ->count();
    }

    private function getBudgetAlert(): string
    {
        $currentMonth = now()->format('Y-m');
        $lastMonth = now()->subMonth()->format('Y-m');

        $currentTotal = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->sum('take_home_pay');

        $lastTotal = PenggajianKaryawan::where('periode_gaji', $lastMonth)
            ->sum('take_home_pay');

        if ($lastTotal == 0) {
            return 'N/A';
        }

        $percentageChange = (($currentTotal - $lastTotal) / $lastTotal) * 100;

        if ($percentageChange > 0) {
            return '+' . number_format($percentageChange, 1) . '%';
        } else {
            return number_format($percentageChange, 1) . '%';
        }
    }

    private function getBudgetDescription(): string
    {
        $currentMonth = now()->format('Y-m');
        $lastMonth = now()->subMonth()->format('Y-m');

        $currentTotal = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->sum('take_home_pay');

        $lastTotal = PenggajianKaryawan::where('periode_gaji', $lastMonth)
            ->sum('take_home_pay');

        if ($lastTotal == 0) {
            return 'Data bulan lalu tidak tersedia';
        }

        $percentageChange = (($currentTotal - $lastTotal) / $lastTotal) * 100;

        if ($percentageChange > 10) {
            return 'Peningkatan signifikan dari bulan lalu';
        } elseif ($percentageChange > 0) {
            return 'Naik dari bulan lalu';
        } elseif ($percentageChange < -10) {
            return 'Penurunan signifikan dari bulan lalu';
        } else {
            return 'Turun dari bulan lalu';
        }
    }

    private function getBudgetColor(): string
    {
        $currentMonth = now()->format('Y-m');
        $lastMonth = now()->subMonth()->format('Y-m');

        $currentTotal = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->sum('take_home_pay');

        $lastTotal = PenggajianKaryawan::where('periode_gaji', $lastMonth)
            ->sum('take_home_pay');

        if ($lastTotal == 0) {
            return 'gray';
        }

        $percentageChange = (($currentTotal - $lastTotal) / $lastTotal) * 100;

        if ($percentageChange > 15) {
            return 'danger'; // High increase
        } elseif ($percentageChange > 5) {
            return 'warning'; // Moderate increase
        } elseif ($percentageChange < -15) {
            return 'danger'; // High decrease
        } else {
            return 'success'; // Normal range
        }
    }

    private function getPendingApprovals(): int
    {
        // Since there's no approval system yet, return 0
        // This can be updated when approval system is implemented
        return 0;
    }
}
