<?php

namespace App\Filament\Widgets;

use App\Models\Departemen;
use App\Models\PenggajianKaryawan;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class PayrollByDepartmentWidget extends ChartWidget
{
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;
    protected static ?string $heading = 'Payroll per Departemen';
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'this_month';

    protected function getFilters(): ?array
    {
        return [
            'this_month' => 'Bulan Ini',
            'last_month' => 'Bulan Lalu',
            'comparison' => 'Perbandingan 2 Bulan',
        ];
    }

    protected function getData(): array
    {
        return match ($this->filter) {
            'this_month' => $this->getThisMonthData(),
            'last_month' => $this->getLastMonthData(),
            'comparison' => $this->getComparisonData(),
            default => $this->getThisMonthData(),
        };
    }

    protected function getType(): string
    {
        return $this->filter === 'comparison' ? 'bar' : 'doughnut';
    }

    private function getThisMonthData(): array
    {
        $currentMonth = now()->format('Y-m');
        return $this->getDepartmentPayrollData($currentMonth);
    }

    private function getLastMonthData(): array
    {
        $lastMonth = now()->subMonth()->format('Y-m');
        return $this->getDepartmentPayrollData($lastMonth);
    }

    private function getDepartmentPayrollData(string $period): array
    {
        $departments = Departemen::with(['karyawan.penggajian' => function ($query) use ($period) {
            $query->where('periode_gaji', $period);
        }])->get();

        $departmentNames = [];
        $payrollAmounts = [];
        $colors = [
            'rgb(59, 130, 246)',
            'rgb(16, 185, 129)',
            'rgb(245, 158, 11)',
            'rgb(239, 68, 68)',
            'rgb(139, 92, 246)',
            'rgb(236, 72, 153)',
            'rgb(34, 197, 94)',
            'rgb(251, 146, 60)',
            'rgb(168, 85, 247)',
            'rgb(14, 165, 233)',
        ];

        foreach ($departments as $department) {
            $totalPayroll = $department->karyawan
                ->flatMap->penggajian
                ->sum('take_home_pay');

            if ($totalPayroll > 0) {
                $departmentNames[] = $department->nama_departemen;
                $payrollAmounts[] = round($totalPayroll / 1000000, 1); // Convert to millions
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Total Payroll (Juta)',
                    'data' => $payrollAmounts,
                    'backgroundColor' => array_slice($colors, 0, count($payrollAmounts)),
                ],
            ],
            'labels' => $departmentNames,
        ];
    }

    private function getComparisonData(): array
    {
        $currentMonth = now()->format('Y-m');
        $lastMonth = now()->subMonth()->format('Y-m');

        $departments = Departemen::with([
            'karyawan.penggajian' => function ($query) use ($currentMonth, $lastMonth) {
                $query->whereIn('periode_gaji', [$currentMonth, $lastMonth]);
            }
        ])->get();

        $departmentNames = [];
        $currentMonthData = [];
        $lastMonthData = [];

        foreach ($departments as $department) {
            $currentMonthPayroll = $department->karyawan
                ->flatMap->penggajian
                ->where('periode_gaji', $currentMonth)
                ->sum('take_home_pay');

            $lastMonthPayroll = $department->karyawan
                ->flatMap->penggajian
                ->where('periode_gaji', $lastMonth)
                ->sum('take_home_pay');

            if ($currentMonthPayroll > 0 || $lastMonthPayroll > 0) {
                $departmentNames[] = $department->nama_departemen;
                $currentMonthData[] = round($currentMonthPayroll / 1000000, 1);
                $lastMonthData[] = round($lastMonthPayroll / 1000000, 1);
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Bulan Ini (Juta)',
                    'data' => $currentMonthData,
                    'backgroundColor' => 'rgba(59, 130, 246, 0.8)',
                    'borderColor' => 'rgb(59, 130, 246)',
                    'borderWidth' => 1,
                ],
                [
                    'label' => 'Bulan Lalu (Juta)',
                    'data' => $lastMonthData,
                    'backgroundColor' => 'rgba(239, 68, 68, 0.8)',
                    'borderColor' => 'rgb(239, 68, 68)',
                    'borderWidth' => 1,
                ],
            ],
            'labels' => $departmentNames,
        ];
    }

    protected function getOptions(): array
    {
        $baseOptions = [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'enabled' => true,
                    'callbacks' => [
                        'label' => "function(context) {
                            return context.dataset.label + ': Rp ' + context.parsed + ' juta';
                        }",
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];

        if ($this->filter === 'comparison') {
            $baseOptions['scales'] = [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => "function(value) { return 'Rp ' + value + 'M'; }",
                    ],
                ],
            ];
        }

        return $baseOptions;
    }
}
