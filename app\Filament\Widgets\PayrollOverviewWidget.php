<?php

namespace App\Filament\Widgets;

use App\Models\PenggajianKaryawan;
use App\Models\Karyawan;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;
use Livewire\Attributes\On;

class PayrollOverviewWidget extends BaseWidget
{
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;
    protected static ?int $sort = 1;
    protected int | string | array $columnSpan = 'full';

    public $filters = [];

    public function mount(): void
    {
        $this->filters = session('dashboard_filters', [
            'date_range' => 'this_month',
            'start_date' => now()->startOfMonth(),
            'end_date' => now()->endOfMonth(),
        ]);
    }

    #[On('filtersUpdated')]
    public function updateFilters($filters): void
    {
        $this->filters = $filters;
        $this->dispatch('$refresh');
    }

    #[On('updateCharts')]
    public function refreshWidget(): void
    {
        $this->filters = session('dashboard_filters', $this->filters);
        $this->dispatch('$refresh');
    }

    protected function getStats(): array
    {
        return [
            // Total Payroll This Month
            Stat::make('Total Payroll Bulan Ini', $this->getTotalPayrollThisMonth())
                ->description($this->getPayrollTrend())
                ->descriptionIcon($this->getPayrollTrendIcon())
                ->color($this->getPayrollTrendColor()),

            // Average Salary
            Stat::make('Rata-rata Gaji', $this->getAverageSalary())
                ->description('Gaji pokok karyawan')
                ->descriptionIcon('heroicon-m-calculator')
                ->color('primary'),

            // Highest Salary
            Stat::make('Gaji Tertinggi', $this->getHighestSalary())
                ->description('Gaji pokok tertinggi')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('success'),

            // Lowest Salary
            Stat::make('Gaji Terendah', $this->getLowestSalary())
                ->description('Gaji pokok terendah')
                ->descriptionIcon('heroicon-m-arrow-trending-down')
                ->color('warning'),

            // Total Allowances
            Stat::make('Total Tunjangan', $this->getTotalAllowances())
                ->description('Semua jenis tunjangan bulan ini')
                ->descriptionIcon('heroicon-m-plus-circle')
                ->color('info'),

            // Total Deductions
            Stat::make('Total Potongan', $this->getTotalDeductions())
                ->description('BPJS, pajak, dll bulan ini')
                ->descriptionIcon('heroicon-m-minus-circle')
                ->color('danger'),

            // Employees Processed
            Stat::make('Karyawan Diproses', $this->getEmployeesProcessed())
                ->description($this->getProcessedDescription())
                ->descriptionIcon('heroicon-m-check-circle')
                ->color($this->getProcessedColor()),

            // Pending Payroll
            Stat::make('Payroll Pending', $this->getPendingPayroll())
                ->description('Belum diproses bulan ini')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),
        ];
    }

    private function getTotalPayrollThisMonth(): string
    {
        $dateRange = $this->getFilteredDateRange();

        $total = PenggajianKaryawan::whereBetween('created_at', [
            $dateRange['start']->format('Y-m-d'),
            $dateRange['end']->format('Y-m-d')
        ])
            ->sum('take_home_pay');

        return 'Rp ' . number_format($total, 0, ',', '.');
    }

    private function getPayrollTrend(): string
    {
        $currentMonth = now()->format('Y-m');
        $lastMonth = now()->subMonth()->format('Y-m');

        $currentTotal = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->sum('take_home_pay');

        $lastTotal = PenggajianKaryawan::where('periode_gaji', $lastMonth)
            ->sum('take_home_pay');

        if ($lastTotal == 0) {
            return 'Data bulan lalu tidak tersedia';
        }

        $percentageChange = (($currentTotal - $lastTotal) / $lastTotal) * 100;

        if ($percentageChange > 0) {
            return 'Naik ' . number_format($percentageChange, 1) . '% dari bulan lalu';
        } elseif ($percentageChange < 0) {
            return 'Turun ' . number_format(abs($percentageChange), 1) . '% dari bulan lalu';
        } else {
            return 'Sama dengan bulan lalu';
        }
    }

    private function getPayrollTrendIcon(): string
    {
        $currentMonth = now()->format('Y-m');
        $lastMonth = now()->subMonth()->format('Y-m');

        $currentTotal = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->sum('take_home_pay');

        $lastTotal = PenggajianKaryawan::where('periode_gaji', $lastMonth)
            ->sum('take_home_pay');

        if ($lastTotal == 0) {
            return 'heroicon-m-minus';
        }

        return $currentTotal >= $lastTotal ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down';
    }

    private function getPayrollTrendColor(): string
    {
        $currentMonth = now()->format('Y-m');
        $lastMonth = now()->subMonth()->format('Y-m');

        $currentTotal = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->sum('take_home_pay');

        $lastTotal = PenggajianKaryawan::where('periode_gaji', $lastMonth)
            ->sum('take_home_pay');

        if ($lastTotal == 0) {
            return 'gray';
        }

        return $currentTotal >= $lastTotal ? 'success' : 'danger';
    }

    private function getAverageSalary(): string
    {
        $currentMonth = now()->format('Y-m');
        $average = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->avg('gaji_pokok');

        return $average ? 'Rp ' . number_format($average, 0, ',', '.') : 'N/A';
    }

    private function getHighestSalary(): string
    {
        $currentMonth = now()->format('Y-m');
        $highest = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->max('gaji_pokok');

        return $highest ? 'Rp ' . number_format($highest, 0, ',', '.') : 'N/A';
    }

    private function getLowestSalary(): string
    {
        $currentMonth = now()->format('Y-m');
        $lowest = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->min('gaji_pokok');

        return $lowest ? 'Rp ' . number_format($lowest, 0, ',', '.') : 'N/A';
    }

    private function getTotalAllowances(): string
    {
        $currentMonth = now()->format('Y-m');
        $total = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->selectRaw('SUM(tunjangan_jabatan + tunjangan_umum + tunjangan_sembako) as total_allowances')
            ->value('total_allowances');

        return 'Rp ' . number_format($total ?? 0, 0, ',', '.');
    }

    private function getTotalDeductions(): string
    {
        $currentMonth = now()->format('Y-m');
        $total = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->selectRaw('SUM(bpjs_kesehatan_dipotong + bpjs_tk_dipotong + potongan_lainnya) as total_deductions')
            ->value('total_deductions');

        return 'Rp ' . number_format($total ?? 0, 0, ',', '.');
    }

    private function getEmployeesProcessed(): string
    {
        $currentMonth = now()->format('Y-m');
        $processed = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->distinct('karyawan_id')
            ->count();

        $totalActive = Karyawan::where('status_aktif', true)->count();

        return "{$processed}/{$totalActive}";
    }

    private function getProcessedDescription(): string
    {
        $currentMonth = now()->format('Y-m');
        $processed = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->distinct('karyawan_id')
            ->count();

        $totalActive = Karyawan::where('status_aktif', true)->count();

        if ($totalActive == 0) {
            return 'Tidak ada karyawan aktif';
        }

        $percentage = round(($processed / $totalActive) * 100, 1);
        return "{$percentage}% dari total karyawan";
    }

    private function getProcessedColor(): string
    {
        $currentMonth = now()->format('Y-m');
        $processed = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->distinct('karyawan_id')
            ->count();

        $totalActive = Karyawan::where('status_aktif', true)->count();

        if ($totalActive == 0) {
            return 'gray';
        }

        $percentage = ($processed / $totalActive) * 100;

        if ($percentage == 100) {
            return 'success';
        } elseif ($percentage >= 80) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    private function getPendingPayroll(): int
    {
        $dateRange = $this->getFilteredDateRange();

        $processed = PenggajianKaryawan::whereBetween('created_at', [
            $dateRange['start']->format('Y-m-d'),
            $dateRange['end']->format('Y-m-d')
        ])
            ->pluck('karyawan_id')
            ->unique();

        $totalActive = Karyawan::where('status_aktif', true)->count();

        return $totalActive - $processed->count();
    }

    private function getFilteredDateRange(): array
    {
        if (empty($this->filters)) {
            return [
                'start' => now()->startOfMonth(),
                'end' => now()->endOfMonth(),
            ];
        }

        if (isset($this->filters['start_date']) && isset($this->filters['end_date'])) {
            return [
                'start' => Carbon::parse($this->filters['start_date']),
                'end' => Carbon::parse($this->filters['end_date']),
            ];
        }

        // Fallback to current month
        return [
            'start' => now()->startOfMonth(),
            'end' => now()->endOfMonth(),
        ];
    }
}
