<?php

namespace App\Filament\Widgets;

use App\Models\PenggajianKaryawan;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class PayrollTrendsWidget extends ChartWidget
{
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;
    protected static ?string $heading = 'Trend Gaji & Kompensasi';
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'monthly_trend';

    protected function getFilters(): ?array
    {
        return [
            'monthly_trend' => 'Trend Bulanan (6 Bulan)',
            'yearly_comparison' => 'Perbandingan Tahunan',
            'component_breakdown' => 'Breakdown Komponen Gaji',
        ];
    }

    protected function getData(): array
    {
        return match ($this->filter) {
            'monthly_trend' => $this->getMonthlyTrendData(),
            'yearly_comparison' => $this->getYearlyComparisonData(),
            'component_breakdown' => $this->getComponentBreakdownData(),
            default => $this->getMonthlyTrendData(),
        };
    }

    protected function getType(): string
    {
        return match ($this->filter) {
            'component_breakdown' => 'doughnut',
            default => 'line',
        };
    }

    private function getMonthlyTrendData(): array
    {
        $months = [];
        $avgSalaries = [];
        $totalPayroll = [];

        // Get last 6 months
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthYear = $date->format('Y-m');
            $monthLabel = $date->format('M Y');

            $avgSalary = PenggajianKaryawan::where('periode_gaji', $monthYear)
                ->avg('gaji_pokok');

            $totalPayrollAmount = PenggajianKaryawan::where('periode_gaji', $monthYear)
                ->sum('take_home_pay');

            $months[] = $monthLabel;
            $avgSalaries[] = $avgSalary ? round($avgSalary / 1000000, 1) : 0; // Convert to millions
            $totalPayroll[] = $totalPayrollAmount ? round($totalPayrollAmount / 1000000, 1) : 0;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Rata-rata Gaji Pokok (Juta)',
                    'data' => $avgSalaries,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Total Payroll (Juta)',
                    'data' => $totalPayroll,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'tension' => 0.4,
                ],
            ],
            'labels' => $months,
        ];
    }

    private function getYearlyComparisonData(): array
    {
        $currentYear = now()->year;
        $lastYear = $currentYear - 1;

        $months = [];
        $currentYearData = [];
        $lastYearData = [];

        for ($month = 1; $month <= 12; $month++) {
            $monthLabel = Carbon::create($currentYear, $month, 1)->format('M');

            $currentYearAvg = PenggajianKaryawan::where('periode_gaji', sprintf('%d-%02d', $currentYear, $month))
                ->avg('gaji_pokok');

            $lastYearAvg = PenggajianKaryawan::where('periode_gaji', sprintf('%d-%02d', $lastYear, $month))
                ->avg('gaji_pokok');

            $months[] = $monthLabel;
            $currentYearData[] = $currentYearAvg ? round($currentYearAvg / 1000000, 1) : 0;
            $lastYearData[] = $lastYearAvg ? round($lastYearAvg / 1000000, 1) : 0;
        }

        return [
            'datasets' => [
                [
                    'label' => $currentYear,
                    'data' => $currentYearData,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => $lastYear,
                    'data' => $lastYearData,
                    'borderColor' => 'rgb(239, 68, 68)',
                    'backgroundColor' => 'rgba(239, 68, 68, 0.1)',
                    'tension' => 0.4,
                ],
            ],
            'labels' => $months,
        ];
    }

    private function getComponentBreakdownData(): array
    {
        $currentMonth = now()->format('Y-m');

        $components = PenggajianKaryawan::where('periode_gaji', $currentMonth)
            ->selectRaw('
                AVG(gaji_pokok) as avg_gaji_pokok,
                AVG(tunjangan_jabatan) as avg_tunjangan_jabatan,
                AVG(tunjangan_umum) as avg_tunjangan_umum,
                AVG(tunjangan_sembako) as avg_tunjangan_sembako
            ')
            ->first();

        if (!$components) {
            return [
                'datasets' => [['data' => [], 'backgroundColor' => []]],
                'labels' => [],
            ];
        }

        $data = [
            round($components->avg_gaji_pokok / 1000000, 1),
            round($components->avg_tunjangan_jabatan / 1000000, 1),
            round($components->avg_tunjangan_umum / 1000000, 1),
            round($components->avg_tunjangan_sembako / 1000000, 1),
        ];

        return [
            'datasets' => [
                [
                    'label' => 'Rata-rata Komponen (Juta)',
                    'data' => $data,
                    'backgroundColor' => [
                        'rgb(59, 130, 246)',  // Blue for basic salary
                        'rgb(34, 197, 94)',   // Green for position allowance
                        'rgb(245, 158, 11)',  // Yellow for general allowance
                        'rgb(139, 92, 246)',  // Purple for food allowance
                    ],
                ],
            ],
            'labels' => ['Gaji Pokok', 'Tunjangan Jabatan', 'Tunjangan Umum', 'Tunjangan Sembako'],
        ];
    }

    protected function getOptions(): array
    {
        $baseOptions = [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'enabled' => true,
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];

        if ($this->filter !== 'component_breakdown') {
            $baseOptions['scales'] = [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => "function(value) { return 'Rp ' + value + 'M'; }",
                    ],
                ],
            ];
        }

        return $baseOptions;
    }
}
