<?php

namespace App\Filament\Widgets;

use App\Models\KpiPenilaian;
use App\Models\Departemen;
use App\Models\Absensi;
use App\Models\Pelanggaran;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class PerformanceAnalyticsWidget extends ChartWidget
{
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;
    protected static ?string $heading = 'Analisis Performa & KPI';
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'kpi_monthly';

    protected function getFilters(): ?array
    {
        return [
            'kpi_monthly' => 'Trend KPI Bulanan',
            'kpi_department' => 'KPI per Departemen',
            'performance_distribution' => 'Distribusi Performa',
            'violation_trend' => 'Trend Pelanggaran',
        ];
    }

    protected function getData(): array
    {
        return match ($this->filter) {
            'kpi_monthly' => $this->getKpiMonthlyData(),
            'kpi_department' => $this->getKpiDepartmentData(),
            'performance_distribution' => $this->getPerformanceDistributionData(),
            'violation_trend' => $this->getViolationTrendData(),
            default => $this->getKpiMonthlyData(),
        };
    }

    protected function getType(): string
    {
        return match ($this->filter) {
            'kpi_department' => 'radar',
            'performance_distribution' => 'doughnut',
            default => 'line',
        };
    }

    private function getKpiMonthlyData(): array
    {
        $months = [];
        $kpiData = [];
        $targetData = [];

        // Get last 6 months
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthYear = $date->format('Y-m');
            $monthLabel = $date->format('M Y');

            $avgKpi = KpiPenilaian::where('periode', $monthYear)
                ->avg('realisasi_kpi');

            $avgTarget = KpiPenilaian::where('periode', $monthYear)
                ->avg('target_kpi');

            $months[] = $monthLabel;
            $kpiData[] = $avgKpi ? round($avgKpi, 1) : 0;
            $targetData[] = $avgTarget ? round($avgTarget, 1) : 0;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Realisasi KPI',
                    'data' => $kpiData,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Target KPI',
                    'data' => $targetData,
                    'borderColor' => 'rgb(239, 68, 68)',
                    'backgroundColor' => 'rgba(239, 68, 68, 0.1)',
                    'tension' => 0.4,
                    'borderDash' => [5, 5],
                ],
            ],
            'labels' => $months,
        ];
    }

    private function getKpiDepartmentData(): array
    {
        $departments = Departemen::with(['karyawan.kpiPenilaian' => function ($query) {
            $query->whereMonth('tanggal_penilaian', now()->month);
        }])->get();

        $departmentNames = [];
        $kpiAverages = [];

        foreach ($departments as $department) {
            $kpiValues = $department->karyawan
                ->flatMap->kpiPenilaian
                ->pluck('realisasi_kpi');

            if ($kpiValues->count() > 0) {
                $departmentNames[] = $department->nama_departemen;
                $kpiAverages[] = round($kpiValues->avg(), 1);
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Rata-rata KPI',
                    'data' => $kpiAverages,
                    'backgroundColor' => 'rgba(59, 130, 246, 0.2)',
                    'borderColor' => 'rgb(59, 130, 246)',
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $departmentNames,
        ];
    }

    private function getPerformanceDistributionData(): array
    {
        $grades = ['A', 'B', 'C', 'D'];
        $gradeData = [];

        foreach ($grades as $grade) {
            $count = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
                ->where('nilai_akhir', $grade)
                ->distinct('karyawan_id')
                ->count();
            $gradeData[] = $count;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Karyawan',
                    'data' => $gradeData,
                    'backgroundColor' => [
                        'rgb(34, 197, 94)',   // Green for A
                        'rgb(59, 130, 246)',  // Blue for B
                        'rgb(245, 158, 11)',  // Yellow for C
                        'rgb(239, 68, 68)',   // Red for D
                    ],
                ],
            ],
            'labels' => $grades,
        ];
    }

    private function getViolationTrendData(): array
    {
        $months = [];
        $violationCounts = [];

        // Get last 6 months
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthLabel = $date->format('M Y');

            $count = Pelanggaran::whereYear('tanggal', $date->year)
                ->whereMonth('tanggal', $date->month)
                ->count();

            $months[] = $monthLabel;
            $violationCounts[] = $count;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Pelanggaran',
                    'data' => $violationCounts,
                    'borderColor' => 'rgb(239, 68, 68)',
                    'backgroundColor' => 'rgba(239, 68, 68, 0.1)',
                    'tension' => 0.4,
                    'fill' => true,
                ],
            ],
            'labels' => $months,
        ];
    }

    protected function getOptions(): array
    {
        $baseOptions = [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'enabled' => true,
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];

        if ($this->filter === 'kpi_department') {
            $baseOptions['scales'] = [
                'r' => [
                    'beginAtZero' => true,
                    'max' => 100,
                    'ticks' => [
                        'stepSize' => 20,
                    ],
                ],
            ];
        } elseif ($this->filter !== 'performance_distribution') {
            $baseOptions['scales'] = [
                'y' => [
                    'beginAtZero' => true,
                ],
            ];
        }

        return $baseOptions;
    }
}
