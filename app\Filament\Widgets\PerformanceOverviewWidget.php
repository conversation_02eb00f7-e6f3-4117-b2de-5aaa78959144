<?php

namespace App\Filament\Widgets;

use App\Models\KpiPenilaian;
use App\Models\Karyawan;
use App\Models\Pelanggaran;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Livewire\Attributes\On;
use Carbon\Carbon;

class PerformanceOverviewWidget extends BaseWidget
{
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;
    protected static ?int $sort = 1;
    protected int | string | array $columnSpan = 'full';

    public $filters = [];

    public function mount(): void
    {
        $this->filters = session('dashboard_filters', [
            'date_range' => 'this_month',
            'start_date' => now()->startOfMonth(),
            'end_date' => now()->endOfMonth(),
        ]);
    }

    #[On('filtersUpdated')]
    public function updateFilters($filters): void
    {
        $this->filters = $filters;
        $this->dispatch('$refresh');
    }

    #[On('updateCharts')]
    public function refreshWidget(): void
    {
        $this->filters = session('dashboard_filters', $this->filters);
        $this->dispatch('$refresh');
    }

    protected function getStats(): array
    {
        return [
            // Average KPI This Month
            Stat::make('Rata-rata KPI Bulan Ini', $this->getAverageKpiThisMonth())
                ->description($this->getKpiTrend())
                ->descriptionIcon($this->getKpiTrendIcon())
                ->color($this->getKpiTrendColor()),

            // Top Performers
            Stat::make('Top Performers (A)', $this->getTopPerformers())
                ->description('Karyawan dengan nilai A')
                ->descriptionIcon('heroicon-m-star')
                ->color('success'),

            // Poor Performers
            Stat::make('Performance Rendah (D)', $this->getPoorPerformers())
                ->description('Karyawan dengan nilai D')
                ->descriptionIcon('heroicon-m-arrow-trending-down')
                ->color('danger'),

            // KPI Achievement Rate
            Stat::make('Tingkat Pencapaian KPI', $this->getKpiAchievementRate())
                ->description('Target tercapai bulan ini')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color($this->getAchievementColor()),

            // Employees Assessed
            Stat::make('Karyawan Dinilai', $this->getEmployeesAssessed())
                ->description($this->getAssessmentDescription())
                ->descriptionIcon('heroicon-m-clipboard-document-check')
                ->color($this->getAssessmentColor()),

            // Violations This Month
            Stat::make('Pelanggaran Bulan Ini', $this->getViolationsThisMonth())
                ->description($this->getViolationTrend())
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($this->getViolationColor()),

            // Performance Improvement
            Stat::make('Peningkatan Performa', $this->getPerformanceImprovement())
                ->description('Karyawan yang meningkat dari bulan lalu')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('success'),

            // Pending Assessments
            Stat::make('Penilaian Pending', $this->getPendingAssessments())
                ->description('Belum dinilai bulan ini')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),
        ];
    }

    private function getAverageKpiThisMonth(): string
    {
        $dateRange = $this->getFilteredDateRange();

        $avgKpi = KpiPenilaian::whereBetween('tanggal_penilaian', [
            $dateRange['start']->format('Y-m-d'),
            $dateRange['end']->format('Y-m-d')
        ])
            ->avg('realisasi_kpi');

        return $avgKpi ? number_format($avgKpi, 1) . '%' : 'N/A';
    }

    private function getKpiTrend(): string
    {
        $currentMonth = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->avg('realisasi_kpi');

        $lastMonth = KpiPenilaian::whereMonth('tanggal_penilaian', now()->subMonth()->month)
            ->avg('realisasi_kpi');

        if (!$currentMonth || !$lastMonth) {
            return 'Data tidak cukup';
        }

        $diff = round($currentMonth - $lastMonth, 1);

        if ($diff > 0) {
            return "Naik {$diff}% dari bulan lalu";
        } elseif ($diff < 0) {
            return "Turun " . abs($diff) . "% dari bulan lalu";
        } else {
            return "Sama dengan bulan lalu";
        }
    }

    private function getKpiTrendIcon(): string
    {
        $currentMonth = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->avg('realisasi_kpi');

        $lastMonth = KpiPenilaian::whereMonth('tanggal_penilaian', now()->subMonth()->month)
            ->avg('realisasi_kpi');

        if (!$currentMonth || !$lastMonth) {
            return 'heroicon-m-minus';
        }

        return $currentMonth >= $lastMonth ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down';
    }

    private function getKpiTrendColor(): string
    {
        $currentMonth = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->avg('realisasi_kpi');

        $lastMonth = KpiPenilaian::whereMonth('tanggal_penilaian', now()->subMonth()->month)
            ->avg('realisasi_kpi');

        if (!$currentMonth || !$lastMonth) {
            return 'gray';
        }

        return $currentMonth >= $lastMonth ? 'success' : 'danger';
    }

    private function getTopPerformers(): int
    {
        return KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->where('nilai_akhir', 'A')
            ->distinct('karyawan_id')
            ->count();
    }

    private function getPoorPerformers(): int
    {
        return KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->where('nilai_akhir', 'D')
            ->distinct('karyawan_id')
            ->count();
    }

    private function getKpiAchievementRate(): string
    {
        $totalAssessments = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)->count();
        $achievedTargets = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->whereRaw('realisasi_kpi >= target_kpi')
            ->count();

        if ($totalAssessments === 0) {
            return 'N/A';
        }

        $rate = ($achievedTargets / $totalAssessments) * 100;
        return number_format($rate, 1) . '%';
    }

    private function getAchievementColor(): string
    {
        $totalAssessments = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)->count();
        $achievedTargets = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->whereRaw('realisasi_kpi >= target_kpi')
            ->count();

        if ($totalAssessments === 0) {
            return 'gray';
        }

        $rate = ($achievedTargets / $totalAssessments) * 100;

        if ($rate >= 80) {
            return 'success';
        } elseif ($rate >= 60) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    private function getEmployeesAssessed(): string
    {
        $assessed = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->distinct('karyawan_id')
            ->count();

        $totalActive = Karyawan::where('status_aktif', true)->count();

        return "{$assessed}/{$totalActive}";
    }

    private function getAssessmentDescription(): string
    {
        $assessed = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->distinct('karyawan_id')
            ->count();

        $totalActive = Karyawan::where('status_aktif', true)->count();

        if ($totalActive === 0) {
            return 'Tidak ada karyawan aktif';
        }

        $percentage = round(($assessed / $totalActive) * 100, 1);
        return "{$percentage}% dari total karyawan";
    }

    private function getAssessmentColor(): string
    {
        $assessed = KpiPenilaian::whereMonth('tanggal_penilaian', now()->month)
            ->distinct('karyawan_id')
            ->count();

        $totalActive = Karyawan::where('status_aktif', true)->count();

        if ($totalActive === 0) {
            return 'gray';
        }

        $percentage = ($assessed / $totalActive) * 100;

        if ($percentage >= 90) {
            return 'success';
        } elseif ($percentage >= 70) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    private function getViolationsThisMonth(): int
    {
        return Pelanggaran::whereMonth('tanggal', now()->month)->count();
    }

    private function getViolationTrend(): string
    {
        $currentMonth = Pelanggaran::whereMonth('tanggal', now()->month)->count();
        $lastMonth = Pelanggaran::whereMonth('tanggal', now()->subMonth()->month)->count();

        $diff = $currentMonth - $lastMonth;

        if ($diff > 0) {
            return "Naik {$diff} dari bulan lalu";
        } elseif ($diff < 0) {
            return "Turun " . abs($diff) . " dari bulan lalu";
        } else {
            return "Sama dengan bulan lalu";
        }
    }

    private function getViolationColor(): string
    {
        $count = $this->getViolationsThisMonth();

        if ($count === 0) {
            return 'success';
        } elseif ($count <= 5) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    private function getPerformanceImprovement(): int
    {
        // Get employees who improved from last month
        $currentMonth = now()->month;
        $lastMonth = now()->subMonth()->month;

        $improvements = KpiPenilaian::selectRaw('karyawan_id, AVG(realisasi_kpi) as current_avg')
            ->whereMonth('tanggal_penilaian', $currentMonth)
            ->groupBy('karyawan_id')
            ->get()
            ->filter(function ($current) use ($lastMonth) {
                $lastAvg = KpiPenilaian::where('karyawan_id', $current->karyawan_id)
                    ->whereMonth('tanggal_penilaian', $lastMonth)
                    ->avg('realisasi_kpi');

                return $lastAvg && $current->current_avg > $lastAvg;
            });

        return $improvements->count();
    }

    private function getPendingAssessments(): int
    {
        $dateRange = $this->getFilteredDateRange();

        $assessed = KpiPenilaian::whereBetween('tanggal_penilaian', [
            $dateRange['start']->format('Y-m-d'),
            $dateRange['end']->format('Y-m-d')
        ])
            ->pluck('karyawan_id')
            ->unique();

        $totalActive = Karyawan::where('status_aktif', true)->count();

        return $totalActive - $assessed->count();
    }

    private function getFilteredDateRange(): array
    {
        if (empty($this->filters)) {
            return [
                'start' => now()->startOfMonth(),
                'end' => now()->endOfMonth(),
            ];
        }

        if (isset($this->filters['start_date']) && isset($this->filters['end_date'])) {
            return [
                'start' => Carbon::parse($this->filters['start_date']),
                'end' => Carbon::parse($this->filters['end_date']),
            ];
        }

        // Fallback to current month
        return [
            'start' => now()->startOfMonth(),
            'end' => now()->endOfMonth(),
        ];
    }
}
