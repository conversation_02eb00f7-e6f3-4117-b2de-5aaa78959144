<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\KaryawanPermission;
use App\Models\Karyawan;

class PermissionOverview extends BaseWidget
{
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';

    protected function getStats(): array
    {
        return [
            Stat::make('Total Permission Aktif', $this->getTotalActivePermissions())
                ->description('Permission yang sedang aktif')
                ->descriptionIcon('heroicon-m-key')
                ->color('success')
                ->url(route('filament.admin.resources.karyawan-permissions.index')),

            Stat::make('Karyawan dengan Permission', $this->getKaryawanWithPermissions())
                ->description('Karyawan yang memiliki permission khusus')
                ->descriptionIcon('heroicon-m-users')
                ->color('info')
                ->url(route('filament.admin.resources.karyawan-permissions.index')),

            Stat::make('Approve Cuti', $this->getPermissionCount('approve_cuti'))
                ->description('Karyawan yang bisa approve cuti')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('warning')
                ->url(route('filament.admin.resources.karyawan-permissions.index', ['activeTab' => 'approve_cuti'])),

            Stat::make('Kelola Jadwal', $this->getPermissionCount('manage_jadwal'))
                ->description('Karyawan yang bisa kelola jadwal')
                ->descriptionIcon('heroicon-m-clock')
                ->color('primary')
                ->url(route('filament.admin.resources.karyawan-permissions.index', ['activeTab' => 'manage_jadwal'])),

            Stat::make('Lihat Absensi', $this->getPermissionCount('view_absensi'))
                ->description('Karyawan yang bisa lihat absensi')
                ->descriptionIcon('heroicon-m-eye')
                ->color('gray')
                ->url(route('filament.admin.resources.karyawan-permissions.index', ['activeTab' => 'view_absensi'])),

            Stat::make('Kelola Karyawan', $this->getPermissionCount('manage_karyawan'))
                ->description('Karyawan yang bisa kelola data karyawan')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('danger')
                ->url(route('filament.admin.resources.karyawan-permissions.index', ['activeTab' => 'manage_karyawan'])),
        ];
    }

    protected function getTotalActivePermissions(): int
    {
        return KaryawanPermission::where('is_active', true)->count();
    }

    protected function getKaryawanWithPermissions(): int
    {
        return KaryawanPermission::where('is_active', true)
            ->distinct('karyawan_id')
            ->count();
    }

    protected function getPermissionCount(string $permissionType): int
    {
        return KaryawanPermission::where('permission_type', $permissionType)
            ->where('is_active', true)
            ->count();
    }

    public function getHeading(): string
    {
        return 'Permission Management Overview';
    }
}
