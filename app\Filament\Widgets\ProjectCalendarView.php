<?php

namespace App\Filament\Widgets;

use App\Models\Project;
use App\Models\Task;
use Filament\Widgets\Widget;
use Carbon\Carbon;

class ProjectCalendarView extends Widget
{
    protected static string $view = 'filament.widgets.project-calendar-view';

    protected int | string | array $columnSpan = 'full';

    public string $currentMonth;
    public int $currentYear;
    public bool $isVisible = false;

    protected $listeners = ['view-switched' => 'handleViewSwitch'];

    public function mount(): void
    {
        $this->currentMonth = now()->format('m');
        $this->currentYear = now()->year;
    }

    public function getViewData(): array
    {
        $startOfMonth = Carbon::createFromDate($this->currentYear, $this->currentMonth, 1)->startOfMonth();
        $endOfMonth = $startOfMonth->copy()->endOfMonth();

        // Get projects that overlap with current month
        $projects = Project::with(['tasks'])
            ->where(function ($query) use ($startOfMonth, $endOfMonth) {
                $query->whereBetween('start_date', [$startOfMonth, $endOfMonth])
                    ->orWhereBetween('end_date', [$startOfMonth, $endOfMonth])
                    ->orWhere(function ($q) use ($startOfMonth, $endOfMonth) {
                        $q->where('start_date', '<=', $startOfMonth)
                            ->where('end_date', '>=', $endOfMonth);
                    });
            })
            ->orderBy('start_date')
            ->get();

        // Get tasks that have due dates in current month
        $tasks = Task::with(['project'])
            ->whereBetween('due_date', [$startOfMonth, $endOfMonth])
            ->orderBy('due_date')
            ->get();

        // Generate calendar data
        $calendarData = $this->generateCalendarData($startOfMonth, $endOfMonth, $projects, $tasks);

        return [
            'calendarData' => $calendarData,
            'currentMonth' => $startOfMonth->format('F Y'),
            'projects' => $projects,
            'tasks' => $tasks,
        ];
    }

    protected function generateCalendarData($startOfMonth, $endOfMonth, $projects, $tasks): array
    {
        $calendar = [];

        // Start from the first day of the week containing the first day of the month
        $startDate = $startOfMonth->copy()->startOfWeek();
        $endDate = $endOfMonth->copy()->endOfWeek();

        $current = $startDate->copy();
        $weekNumber = 0;

        while ($current <= $endDate) {
            $week = [];

            for ($i = 0; $i < 7; $i++) {
                $dayData = [
                    'date' => $current->copy(),
                    'day' => $current->day,
                    'isCurrentMonth' => $current->month == $startOfMonth->month,
                    'isToday' => $current->isToday(),
                    'projects' => [],
                    'tasks' => [],
                ];

                // Add projects for this day
                foreach ($projects as $project) {
                    $projectStart = Carbon::parse($project->start_date);
                    $projectEnd = Carbon::parse($project->end_date);

                    if ($current->between($projectStart, $projectEnd)) {
                        $dayData['projects'][] = [
                            'id' => $project->id,
                            'name' => $project->name,
                            'status' => $project->status,
                            'isStart' => $current->isSameDay($projectStart),
                            'isEnd' => $current->isSameDay($projectEnd),
                            'progress' => $project->progress_percentage ?? 0,
                        ];
                    }
                }

                // Add tasks for this day
                foreach ($tasks as $task) {
                    $taskDue = Carbon::parse($task->due_date);

                    if ($current->isSameDay($taskDue)) {
                        $dayData['tasks'][] = [
                            'id' => $task->id,
                            'name' => $task->name,
                            'status' => $task->status,
                            'project_name' => $task->project->name ?? 'No Project',
                            'is_overdue' => $taskDue->isPast() && $task->status !== 'completed',
                        ];
                    }
                }

                $week[] = $dayData;
                $current->addDay();
            }

            $calendar[] = $week;
            $weekNumber++;
        }

        return $calendar;
    }

    public function previousMonth(): void
    {
        $date = Carbon::createFromDate($this->currentYear, $this->currentMonth, 1)->subMonth();
        $this->currentMonth = $date->format('m');
        $this->currentYear = $date->year;
    }

    public function nextMonth(): void
    {
        $date = Carbon::createFromDate($this->currentYear, $this->currentMonth, 1)->addMonth();
        $this->currentMonth = $date->format('m');
        $this->currentYear = $date->year;
    }

    public function goToToday(): void
    {
        $this->currentMonth = now()->format('m');
        $this->currentYear = now()->year;
    }

    public function handleViewSwitch(string $view): void
    {
        $this->isVisible = $view === 'calendar';
    }
}
