<?php

namespace App\Filament\Widgets;

use App\Models\Project;
use App\Models\Task;
use Filament\Widgets\Widget;
use Carbon\Carbon;

class ProjectGanttChart extends Widget
{
    protected static string $view = 'filament.widgets.project-gantt-chart';

    protected int | string | array $columnSpan = 'full';

    public ?int $selectedProjectId = null;

    public bool $isVisible = true;

    protected $listeners = ['view-switched' => 'handleViewSwitch'];

    public function mount(?int $projectId = null): void
    {
        $this->selectedProjectId = $projectId;
    }

    public function getViewData(): array
    {
        $query = Project::with(['tasks' => function ($query) {
            $query->orderBy('start_date')->orderBy('due_date');
        }]);

        if ($this->selectedProjectId) {
            $query->where('id', $this->selectedProjectId);
        }

        $projects = $query->orderBy('start_date')->get();

        // Calculate timeline data
        $timelineData = $this->calculateTimelineData($projects);

        return [
            'projects' => $projects,
            'timelineData' => $timelineData,
            'selectedProjectId' => $this->selectedProjectId,
        ];
    }

    protected function calculateTimelineData($projects): array
    {
        if ($projects->isEmpty()) {
            return [
                'startDate' => now(),
                'endDate' => now()->addDays(30),
                'totalDays' => 30,
                'months' => [],
                'projectsData' => [],
            ];
        }

        // Find overall start and end dates from projects and tasks
        $projectStartDate = $projects->min('start_date');
        $projectEndDate = $projects->max('end_date');

        // Also consider task dates
        $taskStartDates = [];
        $taskEndDates = [];

        foreach ($projects as $project) {
            foreach ($project->tasks as $task) {
                if ($task->start_date) {
                    $taskStartDates[] = $task->start_date;
                }
                if ($task->due_date) {
                    $taskEndDates[] = $task->due_date;
                }
            }
        }

        $allStartDates = array_filter(array_merge([$projectStartDate], $taskStartDates));
        $allEndDates = array_filter(array_merge([$projectEndDate], $taskEndDates));

        $startDate = $allStartDates ? Carbon::parse(min($allStartDates)) : now();
        $endDate = $allEndDates ? Carbon::parse(max($allEndDates)) : now()->addDays(30);

        // Add some padding
        $startDate = $startDate->subDays(7);
        $endDate = $endDate->addDays(7);

        $totalDays = $startDate->diffInDays($endDate);

        // Generate months for header
        $months = [];
        $current = $startDate->copy()->startOfMonth();
        while ($current <= $endDate) {
            $months[] = [
                'name' => $current->format('M Y'),
                'days' => $current->daysInMonth,
                'startDay' => max(1, $startDate->diffInDays($current) + 1),
            ];
            $current->addMonth();
        }

        // Process projects data
        $projectsData = [];
        foreach ($projects as $project) {
            $projectStart = Carbon::parse($project->start_date);
            $projectEnd = Carbon::parse($project->end_date);

            $startOffset = $startDate->diffInDays($projectStart);
            $duration = $projectStart->diffInDays($projectEnd);

            // Process tasks
            $tasksData = [];
            foreach ($project->tasks as $task) {
                // Use start_date if available, otherwise fall back to created_at
                $taskStart = $task->start_date ? Carbon::parse($task->start_date) : Carbon::parse($task->created_at);
                $taskEnd = $task->due_date ? Carbon::parse($task->due_date) : $taskStart->copy()->addDays(1);

                $taskStartOffset = $startDate->diffInDays($taskStart);
                $taskDuration = $taskStart->diffInDays($taskEnd);

                $tasksData[] = [
                    'id' => $task->id,
                    'name' => $task->name,
                    'startOffset' => max(0, $taskStartOffset),
                    'duration' => max(1, $taskDuration),
                    'status' => $task->status,
                    'progress' => $this->calculateTaskProgress($task),
                    'start_date' => $task->start_date ? $task->start_date->format('M d, Y') : null,
                    'due_date' => $task->due_date ? $task->due_date->format('M d, Y') : null,
                ];
            }

            $projectsData[] = [
                'id' => $project->id,
                'name' => $project->name,
                'startOffset' => max(0, $startOffset),
                'duration' => max(1, $duration),
                'status' => $project->status,
                'progress' => $project->progress_percentage ?? 0,
                'tasks' => $tasksData,
            ];
        }

        return [
            'startDate' => $startDate,
            'endDate' => $endDate,
            'totalDays' => $totalDays,
            'months' => $months,
            'projectsData' => $projectsData,
        ];
    }

    protected function calculateTaskProgress($task): int
    {
        // Calculate task progress based on status
        return match ($task->status) {
            'completed' => 100,
            'in_progress' => 50,
            'todo' => 0,
            default => 0,
        };
    }

    public function selectProject(?int $projectId): void
    {
        $this->selectedProjectId = $projectId;
    }

    public function handleViewSwitch(string $view): void
    {
        $this->isVisible = $view === 'gantt';
    }
}
