<?php

namespace App\Filament\Widgets;

use App\Models\Project;
use Filament\Widgets\Widget;

class ProjectHealthOverview extends Widget
{
    protected static string $view = 'filament.widgets.project-health-overview';

    protected int | string | array $columnSpan = 'full';

    public function getViewData(): array
    {
        $projects = Project::with(['tasks', 'customer'])
            ->whereIn('status', ['active', 'planning'])
            ->get();

        $healthData = $projects->map(function ($project) {
            $health = $project->project_health;
            $progress = $project->progress_percentage;
            $tasksCount = $project->tasks->count();
            $completedTasks = $project->tasks->where('status', 'completed')->count();

            // Calculate days remaining
            $daysRemaining = $project->end_date ? now()->diffInDays($project->end_date, false) : null;

            return [
                'id' => $project->id,
                'name' => $project->name,
                'customer' => $project->customer->name ?? 'N/A',
                'health' => $health,
                'progress' => $progress,
                'tasks_total' => $tasksCount,
                'tasks_completed' => $completedTasks,
                'days_remaining' => $daysRemaining,
                'start_date' => $project->start_date,
                'end_date' => $project->end_date,
                'status' => $project->status,
                'health_color' => $this->getHealthColor($health),
                'health_icon' => $this->getHealthIcon($health),
                'progress_color' => $this->getProgressColor($progress),
                'urgency_level' => $this->getUrgencyLevel($daysRemaining, $progress),
            ];
        })->sortBy(function ($project) {
            // Sort by urgency: overdue first, then by days remaining
            if ($project['days_remaining'] < 0) return -1000 + $project['days_remaining'];
            return $project['days_remaining'] ?? 1000;
        });

        // Calculate summary statistics
        $healthSummary = [
            'on_track' => $healthData->where('health', 'on_track')->count(),
            'at_risk' => $healthData->where('health', 'at_risk')->count(),
            'behind' => $healthData->where('health', 'behind')->count(),
            'overdue' => $healthData->where('health', 'overdue')->count(),
            'completed' => $healthData->where('health', 'completed')->count(),
        ];

        return [
            'projects' => $healthData,
            'healthSummary' => $healthSummary,
            'totalProjects' => $healthData->count(),
        ];
    }

    private function getHealthColor(string $health): string
    {
        return match ($health) {
            'on_track' => 'success',
            'at_risk' => 'warning',
            'behind' => 'danger',
            'overdue' => 'danger',
            'completed' => 'primary',
            default => 'gray',
        };
    }

    private function getHealthIcon(string $health): string
    {
        return match ($health) {
            'on_track' => 'heroicon-m-check-circle',
            'at_risk' => 'heroicon-m-exclamation-triangle',
            'behind' => 'heroicon-m-clock',
            'overdue' => 'heroicon-m-x-circle',
            'completed' => 'heroicon-m-check-badge',
            default => 'heroicon-m-question-mark-circle',
        };
    }

    private function getProgressColor(int $progress): string
    {
        if ($progress >= 80) return 'bg-green-500';
        if ($progress >= 60) return 'bg-blue-500';
        if ($progress >= 40) return 'bg-yellow-500';
        if ($progress >= 20) return 'bg-orange-500';
        return 'bg-red-500';
    }

    private function getUrgencyLevel(?int $daysRemaining, int $progress): string
    {
        if ($daysRemaining === null) return 'unknown';
        if ($daysRemaining < 0) return 'overdue';
        if ($daysRemaining <= 3 && $progress < 80) return 'critical';
        if ($daysRemaining <= 7 && $progress < 60) return 'high';
        if ($daysRemaining <= 14 && $progress < 40) return 'medium';
        return 'low';
    }
}
