<?php

namespace App\Filament\Widgets;

use App\Models\Project;
use App\Models\Task;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ProjectManagementWidget extends BaseWidget
{
    protected static ?int $sort = 2;

    protected function getStats(): array
    {
        $totalProjects = Project::count();
        $activeProjects = Project::where('status', 'active')->count();
        $completedProjects = Project::where('status', 'completed')->count();
        $overdueProjects = Project::where('end_date', '<', now())
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->count();

        $totalTasks = Task::count();
        $completedTasks = Task::where('status', 'completed')->count();
        $inProgressTasks = Task::where('status', 'in_progress')->count();
        $overdueTasks = Task::where('due_date', '<', now())
            ->where('status', '!=', 'completed')
            ->count();

        $myTasks = Task::where('assigned_to', auth()->id())->count();
        $myCompletedTasks = Task::where('assigned_to', auth()->id())
            ->where('status', 'completed')
            ->count();

        return [
            Stat::make('Total Proyek', $totalProjects)
                ->description('Semua proyek dalam sistem')
                ->descriptionIcon('heroicon-m-briefcase')
                ->color('primary'),

            Stat::make('Proyek Aktif', $activeProjects)
                ->description('Proyek yang sedang berjalan')
                ->descriptionIcon('heroicon-m-play')
                ->color('success'),

            Stat::make('Proyek Selesai', $completedProjects)
                ->description('Proyek yang telah diselesaikan')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('Proyek Terlambat', $overdueProjects)
                ->description('Proyek yang melewati deadline')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('danger'),

            Stat::make('Total Tasks', $totalTasks)
                ->description('Semua tasks dalam sistem')
                ->descriptionIcon('heroicon-m-clipboard-document-list')
                ->color('primary'),

            Stat::make('Tasks Selesai', $completedTasks)
                ->description("{$completedTasks} dari {$totalTasks} tasks")
                ->descriptionIcon('heroicon-m-check')
                ->color('success'),

            Stat::make('Tasks Berjalan', $inProgressTasks)
                ->description('Tasks yang sedang dikerjakan')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('Tasks Terlambat', $overdueTasks)
                ->description('Tasks yang melewati deadline')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('danger'),

            Stat::make('Task Saya', $myTasks)
                ->description('Tasks yang ditugaskan kepada saya')
                ->descriptionIcon('heroicon-m-user')
                ->color('info'),

            Stat::make('Task Saya Selesai', $myCompletedTasks)
                ->description("{$myCompletedTasks} dari {$myTasks} tasks saya")
                ->descriptionIcon('heroicon-m-check-badge')
                ->color('success'),
        ];
    }
}
