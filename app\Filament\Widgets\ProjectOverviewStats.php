<?php

namespace App\Filament\Widgets;

use App\Models\Project;
use App\Models\Task;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ProjectOverviewStats extends BaseWidget
{
    protected function getStats(): array
    {
        $totalProjects = Project::count();
        $activeProjects = Project::where('status', 'active')->count();
        $completedProjects = Project::where('status', 'completed')->count();
        $overdueProjects = Project::where('end_date', '<', now())
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->count();

        $totalTasks = Task::count();
        $completedTasks = Task::where('status', 'completed')->count();
        $overdueTasks = Task::where('due_date', '<', now())
            ->where('status', '!=', 'completed')
            ->count();

        // Calculate completion rates
        $projectCompletionRate = $totalProjects > 0 ? round(($completedProjects / $totalProjects) * 100) : 0;
        $taskCompletionRate = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100) : 0;
        return [
            Stat::make('Total Kegiatan', $totalProjects)
                ->description("{$activeProjects} aktif, {$completedProjects} selesai")
                ->descriptionIcon('heroicon-m-briefcase')
                ->color('primary')
                ->chart([7, 2, 10, 3, 15, 4, 17]),

            Stat::make('Progress Kegiatan', "{$projectCompletionRate}%")
                ->description($overdueProjects > 0 ? "{$overdueProjects} kegiatan terlambat" : "Semua kegiatan on track")
                ->descriptionIcon($overdueProjects > 0 ? 'heroicon-m-exclamation-triangle' : 'heroicon-m-check-circle')
                ->color($overdueProjects > 0 ? 'danger' : 'success'),

            Stat::make('Progress Tugas', "{$taskCompletionRate}%")
                ->description("{$completedTasks} dari {$totalTasks} tugas selesai")
                ->descriptionIcon('heroicon-m-list-bullet')
                ->color($overdueTasks > 0 ? 'warning' : 'success')
                ->chart([3, 5, 8, 12, 15, 18, 20]),

        ];
    }
}
