<?php

namespace App\Filament\Widgets;

use App\Models\Project;
use App\Models\User;
use Filament\Widgets\Widget;

class ProjectResourceAllocation extends Widget
{
    protected static string $view = 'filament.widgets.project-resource-allocation';

    protected static ?string $heading = 'Alokasi Sumber Daya';

    protected static ?int $sort = 6;

    protected int | string | array $columnSpan = 'full';

    public function getViewData(): array
    {
        // Get active projects with team allocation
        $projects = Project::where('status', 'active')
            ->with(['members' => function ($query) {
                $query->wherePivot('is_active', true)->with('karyawan');
            }])
            ->withCount([
                'members as active_members_count' => function ($query) {
                    $query->where('project_members.is_active', true);
                },
                'tasks as total_tasks_count',
                'tasks as completed_tasks_count' => function ($query) {
                    $query->where('status', 'completed');
                },
                'tasks as in_progress_tasks_count' => function ($query) {
                    $query->where('status', 'in_progress');
                }
            ])
            ->get()
            ->map(function ($project) {
                $totalCapacity = $project->members->sum('pivot.capacity_hours_per_week');
                $utilizationRate = $project->in_progress_tasks_count > 0 && $totalCapacity > 0
                    ? min(100, ($project->in_progress_tasks_count * 10 / $totalCapacity) * 100)
                    : 0;

                return [
                    'id' => $project->id,
                    'name' => $project->name,
                    'active_members_count' => $project->active_members_count,
                    'total_capacity' => $totalCapacity,
                    'utilization_rate' => round($utilizationRate, 1),
                    'total_tasks' => $project->total_tasks_count,
                    'completed_tasks' => $project->completed_tasks_count,
                    'in_progress_tasks' => $project->in_progress_tasks_count,
                    'progress_percentage' => $project->total_tasks_count > 0
                        ? round(($project->completed_tasks_count / $project->total_tasks_count) * 100, 1)
                        : 0,
                    'members' => $project->members->map(function ($member) {
                        // Debug: Check if karyawan relationship exists
                        $displayName = $member->name; // Default fallback

                        if ($member->karyawan && $member->karyawan->nama_lengkap) {
                            $displayName = $member->karyawan->nama_lengkap;
                        }

                        return [
                            'name' => $displayName,
                            'role' => $member->pivot->role,
                            'capacity' => $member->pivot->capacity_hours_per_week,
                            'hourly_rate' => $member->pivot->hourly_rate,
                        ];
                    }),
                ];
            });

        // Get team members workload
        $teamWorkload = User::whereHas('assignedTasks', function ($query) {
            $query->whereIn('status', ['todo', 'in_progress']);
        })
            ->with('karyawan')
            ->withCount([
                'assignedTasks as active_tasks_count' => function ($query) {
                    $query->whereIn('status', ['todo', 'in_progress']);
                },
                'assignedTasks as overdue_tasks_count' => function ($query) {
                    $query->where('due_date', '<', now())
                        ->where('status', '!=', 'completed');
                }
            ])
            ->get()
            ->map(function ($user) {
                $workloadLevel = $this->calculateWorkloadLevel($user->active_tasks_count);

                return [
                    'name' => $user->karyawan ? $user->karyawan->nama_lengkap : $user->name,
                    'active_tasks' => $user->active_tasks_count,
                    'overdue_tasks' => $user->overdue_tasks_count,
                    'workload_level' => $workloadLevel,
                    'workload_color' => $this->getWorkloadColor($workloadLevel),
                ];
            })
            ->sortByDesc('active_tasks');

        return [
            'projects' => $projects,
            'teamWorkload' => $teamWorkload,
            'totalActiveProjects' => $projects->count(),
            'averageUtilization' => $projects->avg('utilization_rate'),
            'totalTeamMembers' => $teamWorkload->count(),
            'overloadedMembers' => $teamWorkload->where('workload_level', 'overloaded')->count(),
        ];
    }

    private function calculateWorkloadLevel($activeTasks): string
    {
        if ($activeTasks >= 8) return 'overloaded';
        if ($activeTasks >= 5) return 'high';
        if ($activeTasks >= 3) return 'medium';
        if ($activeTasks >= 1) return 'low';
        return 'idle';
    }

    private function getWorkloadColor($level): string
    {
        return match ($level) {
            'overloaded' => 'danger',
            'high' => 'warning',
            'medium' => 'primary',
            'low' => 'success',
            'idle' => 'gray',
            default => 'gray',
        };
    }
}
