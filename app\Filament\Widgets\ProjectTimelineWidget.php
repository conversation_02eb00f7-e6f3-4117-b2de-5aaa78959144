<?php

namespace App\Filament\Widgets;

use App\Models\Project;
use Filament\Widgets\Widget;
use Carbon\Carbon;

class ProjectTimelineWidget extends Widget
{
    protected static string $view = 'filament.widgets.project-timeline-widget';

    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = 3;

    public function getViewData(): array
    {
        $projects = Project::with(['tasks', 'customer'])
            ->whereIn('status', ['active', 'planning'])
            ->orderBy('start_date')
            ->get();

        $timelineData = $this->calculateTimelineData($projects);

        return [
            'projects' => $projects,
            'timelineData' => $timelineData,
            'currentDate' => now(),
        ];
    }

    private function calculateTimelineData($projects): array
    {
        if ($projects->isEmpty()) {
            return [
                'startDate' => now(),
                'endDate' => now()->addDays(30),
                'totalDays' => 30,
                'months' => [],
                'projectsData' => [],
            ];
        }

        // Find the overall timeline range
        $startDate = $projects->min('start_date');
        $endDate = $projects->max('end_date');

        // Extend range if needed
        $startDate = Carbon::parse($startDate)->subDays(7);
        $endDate = Carbon::parse($endDate)->addDays(7);

        // Ensure minimum 3 months view
        if ($startDate->diffInDays($endDate) < 90) {
            $endDate = $startDate->copy()->addDays(90);
        }

        $totalDays = $startDate->diffInDays($endDate);

        // Generate months for header
        $months = [];
        $currentMonth = $startDate->copy()->startOfMonth();
        while ($currentMonth->lte($endDate)) {
            $monthStart = max($currentMonth->diffInDays($startDate), 0);
            $monthEnd = min($currentMonth->copy()->endOfMonth()->diffInDays($startDate), $totalDays);
            $monthWidth = max($monthEnd - $monthStart, 0);

            if ($monthWidth > 0) {
                $months[] = [
                    'name' => $currentMonth->format('M Y'),
                    'start' => $monthStart,
                    'width' => $monthWidth,
                    'widthPercent' => ($monthWidth / $totalDays) * 100,
                ];
            }

            $currentMonth->addMonth();
        }

        // Calculate project timeline data
        $projectsData = $projects->map(function ($project) use ($startDate, $totalDays) {
            $projectStart = Carbon::parse($project->start_date);
            $projectEnd = Carbon::parse($project->end_date);

            $startOffset = max($projectStart->diffInDays($startDate), 0);
            $duration = $projectStart->diffInDays($projectEnd);
            $widthPercent = ($duration / $totalDays) * 100;

            // Calculate current progress position
            $progress = $project->progress_percentage;
            $progressWidth = ($widthPercent * $progress) / 100;

            // Determine if project is overdue
            $isOverdue = $projectEnd->isPast() && $project->status !== 'completed';
            $daysRemaining = now()->diffInDays($projectEnd, false);

            return [
                'id' => $project->id,
                'name' => $project->name,
                'customer' => $project->customer->name ?? 'N/A',
                'start_date' => $projectStart,
                'end_date' => $projectEnd,
                'progress' => $progress,
                'status' => $project->status,
                'health' => $project->project_health,
                'startOffset' => $startOffset,
                'startPercent' => ($startOffset / $totalDays) * 100,
                'widthPercent' => $widthPercent,
                'progressWidth' => $progressWidth,
                'isOverdue' => $isOverdue,
                'daysRemaining' => $daysRemaining,
                'color' => $this->getProjectColor($project->project_health),
                'tasks_count' => $project->tasks->count(),
                'completed_tasks' => $project->tasks->where('status', 'completed')->count(),
            ];
        });

        return [
            'startDate' => $startDate,
            'endDate' => $endDate,
            'totalDays' => $totalDays,
            'months' => $months,
            'projectsData' => $projectsData,
        ];
    }

    private function getProjectColor(string $health): array
    {
        return match ($health) {
            'on_track' => [
                'bg' => 'bg-green-500',
                'progress' => 'bg-green-600',
                'border' => 'border-green-600',
            ],
            'at_risk' => [
                'bg' => 'bg-yellow-500',
                'progress' => 'bg-yellow-600',
                'border' => 'border-yellow-600',
            ],
            'behind' => [
                'bg' => 'bg-orange-500',
                'progress' => 'bg-orange-600',
                'border' => 'border-orange-600',
            ],
            'overdue' => [
                'bg' => 'bg-red-500',
                'progress' => 'bg-red-600',
                'border' => 'border-red-600',
            ],
            'completed' => [
                'bg' => 'bg-blue-500',
                'progress' => 'bg-blue-600',
                'border' => 'border-blue-600',
            ],
            default => [
                'bg' => 'bg-gray-500',
                'progress' => 'bg-gray-600',
                'border' => 'border-gray-600',
            ],
        };
    }
}
