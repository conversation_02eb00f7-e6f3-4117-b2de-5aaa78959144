<?php

namespace App\Filament\Widgets;

use App\Models\Absensi;
use App\Models\Karyawan;
use App\Models\PenggajianKaryawan;
use App\Models\KpiPenilaian;
use App\Traits\HasWidgetFilters;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class QuickStatsWidget extends BaseWidget
{
    use HasWidgetFilters;

    protected static ?int $sort = 2;

    public static function canView(): bool
    {
        return auth()->user()->hasAnyRole(['super_admin', 'direktur']);
    }

    protected function getStats(): array
    {
        // Get current filter from session
        $filters = session('dashboard_filters', ['date_range' => 'this_month']);
        $dateRange = $this->getDateRangeFromFilters($filters);

        return [
            // Late Arrivals in Period
            Stat::make('Terlambat Periode', $this->getLateArrivalsInPeriod($dateRange))
                ->description($this->getLateArrivalsDescription($dateRange))
                ->descriptionIcon('heroicon-m-clock')
                ->color($this->getLateArrivalsColor($dateRange))
                ->url('/admin/absensi-dashboard'),

            // Attendance Rate in Period
            Stat::make('Tingkat Kehadiran', $this->getAttendanceRateInPeriod($dateRange))
                ->description('Periode: ' . $this->getFilterLabelFromRange($dateRange))
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('success')
                ->url('/admin/absensi-dashboard'),

            // Average KPI in Period
            Stat::make('Rata-rata KPI', $this->getAverageKpiInPeriod($dateRange))
                ->description('Performance karyawan')
                ->descriptionIcon('heroicon-m-star')
                ->color('primary')
                ->url('/admin/performance-dashboard'),

            // Payroll in Period
            Stat::make('Payroll Periode', $this->getPayrollInPeriod($dateRange))
                ->description($this->getPayrollDescription($dateRange))
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('warning')
                ->url('/admin/payroll-dashboard'),
        ];
    }

    private function getLateArrivalsInPeriod($dateRange): int
    {
        return Absensi::with(['karyawan', 'jadwal'])
            ->whereBetween('tanggal_absensi', [$dateRange['start'], $dateRange['end']])
            ->where('status', 'terlambat')
            ->count();
    }

    private function getLateArrivalsDescription($dateRange): string
    {
        $currentCount = $this->getLateArrivalsInPeriod($dateRange);
        $totalDays = $dateRange['start']->diffInDays($dateRange['end']) + 1;

        if ($totalDays === 1) {
            return "Hari ini";
        } else {
            $avgPerDay = $totalDays > 0 ? round($currentCount / $totalDays, 1) : 0;
            return "Rata-rata {$avgPerDay} per hari";
        }
    }

    private function getLateArrivalsColor($dateRange): string
    {
        $count = $this->getLateArrivalsInPeriod($dateRange);
        $totalDays = $dateRange['start']->diffInDays($dateRange['end']) + 1;
        $avgPerDay = $totalDays > 0 ? $count / $totalDays : 0;

        if ($avgPerDay === 0) {
            return 'success';
        } elseif ($avgPerDay <= 2) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    private function getAttendanceRateInPeriod($dateRange): string
    {
        $totalAbsensi = Absensi::with(['karyawan', 'jadwal'])
            ->whereBetween('tanggal_absensi', [$dateRange['start'], $dateRange['end']])
            ->count();
        $presentCount = Absensi::with(['karyawan', 'jadwal'])
            ->whereBetween('tanggal_absensi', [$dateRange['start'], $dateRange['end']])
            ->whereIn('status', ['hadir', 'terlambat'])
            ->count();

        if ($totalAbsensi === 0) {
            return 'N/A';
        }

        $rate = ($presentCount / $totalAbsensi) * 100;
        return number_format($rate, 1) . '%';
    }

    private function getAverageKpiInPeriod($dateRange): string
    {
        $avgKpi = KpiPenilaian::with(['karyawan'])
            ->whereBetween('tanggal_penilaian', [$dateRange['start'], $dateRange['end']])
            ->avg('realisasi_kpi');

        return $avgKpi ? number_format($avgKpi, 1) . '%' : 'N/A';
    }

    private function getPayrollInPeriod($dateRange): string
    {
        $processed = PenggajianKaryawan::with(['karyawan'])
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->distinct('karyawan_id')
            ->count();

        $totalActive = Karyawan::where('status_aktif', true)->count();

        return "{$processed}/{$totalActive}";
    }

    private function getPayrollDescription($dateRange): string
    {
        $processed = PenggajianKaryawan::with(['karyawan'])
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->distinct('karyawan_id')
            ->count();

        $totalActive = Karyawan::where('status_aktif', true)->count();

        if ($totalActive === 0) {
            return 'Tidak ada karyawan aktif';
        }

        $percentage = $totalActive > 0 ? round(($processed / $totalActive) * 100, 1) : 0;
        return "{$percentage}% karyawan diproses";
    }

    private function getDateRangeFromFilters($filters): array
    {
        $dateRange = $filters['date_range'] ?? 'this_month';

        switch ($dateRange) {
            case 'today':
                return [
                    'start' => now()->startOfDay(),
                    'end' => now()->endOfDay(),
                ];
            case 'yesterday':
                return [
                    'start' => now()->subDay()->startOfDay(),
                    'end' => now()->subDay()->endOfDay(),
                ];
            case 'this_week':
                return [
                    'start' => now()->startOfWeek(),
                    'end' => now()->endOfWeek(),
                ];
            case 'last_week':
                return [
                    'start' => now()->subWeek()->startOfWeek(),
                    'end' => now()->subWeek()->endOfWeek(),
                ];
            case 'this_month':
                return [
                    'start' => now()->startOfMonth(),
                    'end' => now()->endOfMonth(),
                ];
            case 'last_month':
                return [
                    'start' => now()->subMonth()->startOfMonth(),
                    'end' => now()->subMonth()->endOfMonth(),
                ];
            case 'this_quarter':
                return [
                    'start' => now()->startOfQuarter(),
                    'end' => now()->endOfQuarter(),
                ];
            case 'last_quarter':
                return [
                    'start' => now()->subQuarter()->startOfQuarter(),
                    'end' => now()->subQuarter()->endOfQuarter(),
                ];
            case 'this_year':
                return [
                    'start' => now()->startOfYear(),
                    'end' => now()->endOfYear(),
                ];
            case 'last_year':
                return [
                    'start' => now()->subYear()->startOfYear(),
                    'end' => now()->subYear()->endOfYear(),
                ];
            case 'custom':
                return [
                    'start' => $filters['start_date'] ? \Carbon\Carbon::parse($filters['start_date']) : now()->startOfMonth(),
                    'end' => $filters['end_date'] ? \Carbon\Carbon::parse($filters['end_date']) : now()->endOfMonth(),
                ];
            case 'monthly':
                $month = $filters['month'] ?? now()->month;
                $year = $filters['year'] ?? now()->year;
                return [
                    'start' => \Carbon\Carbon::create($year, $month, 1)->startOfMonth(),
                    'end' => \Carbon\Carbon::create($year, $month, 1)->endOfMonth(),
                ];
            default:
                return [
                    'start' => now()->startOfMonth(),
                    'end' => now()->endOfMonth(),
                ];
        }
    }

    private function getFilterLabelFromRange($dateRange): string
    {
        if ($dateRange['start']->isSameDay($dateRange['end'])) {
            return $dateRange['start']->format('d M Y');
        }

        if ($dateRange['start']->isSameMonth($dateRange['end'])) {
            return $dateRange['start']->format('M Y');
        }

        return $dateRange['start']->format('d M Y') . ' - ' . $dateRange['end']->format('d M Y');
    }
}
