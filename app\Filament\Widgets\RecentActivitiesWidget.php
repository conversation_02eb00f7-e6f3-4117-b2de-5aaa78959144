<?php

namespace App\Filament\Widgets;

use App\Models\Absensi;
use App\Models\Karyawan;
use App\Models\User;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Livewire\Attributes\On;
use Carbon\Carbon;

class RecentActivitiesWidget extends BaseWidget
{
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $heading = 'Aktivitas Terbaru';

    public $filters = [];
    public $dateRange = [];

    public function mount(): void
    {
        // Initialize filters from session
        $this->filters = session('dashboard_filters', [
            'period_type' => 'this_month',
            'start_date' => null,
            'end_date' => null,
        ]);
        $this->updateDateRange();
    }

    #[On('filtersUpdated')]
    public function updateFilters($filters): void
    {
        $this->filters = $filters;
        $this->updateDateRange();
        $this->dispatch('$refresh');
    }

    #[On('updateCharts')]
    public function refreshWidget(): void
    {
        $this->filters = session('dashboard_filters', $this->filters);
        $this->updateDateRange();
        $this->dispatch('$refresh');
    }

    public static function canView(): bool
    {
        return auth()->user()->hasAnyRole(['super_admin', 'direktur']);
    }

    protected function updateDateRange(): void
    {
        // Simple date range calculation
        $periodType = $this->filters['period_type'] ?? 'this_month';

        switch ($periodType) {
            case 'this_month':
                $this->dateRange = [
                    'start' => now()->startOfMonth(),
                    'end' => now()->endOfMonth(),
                ];
                break;
            case 'last_month':
                $this->dateRange = [
                    'start' => now()->subMonth()->startOfMonth(),
                    'end' => now()->subMonth()->endOfMonth(),
                ];
                break;
            case 'this_week':
                $this->dateRange = [
                    'start' => now()->startOfWeek(),
                    'end' => now()->endOfWeek(),
                ];
                break;
            case 'last_week':
                $this->dateRange = [
                    'start' => now()->subWeek()->startOfWeek(),
                    'end' => now()->subWeek()->endOfWeek(),
                ];
                break;
            default:
                $this->dateRange = [
                    'start' => now()->startOfMonth(),
                    'end' => now()->endOfMonth(),
                ];
        }

        // Handle custom date range
        if (
            isset($this->filters['start_date']) && isset($this->filters['end_date'])
            && $this->filters['start_date'] && $this->filters['end_date']
        ) {
            $this->dateRange = [
                'start' => Carbon::parse($this->filters['start_date']),
                'end' => Carbon::parse($this->filters['end_date']),
            ];
        }
    }

    protected function getDateRangeForQuery(): array
    {
        // Pastikan date range selalu ada
        if (empty($this->dateRange)) {
            $this->updateDateRange();
        }

        // Pastikan format date yang konsisten untuk query
        return [
            'start' => $this->dateRange['start']->format('Y-m-d'),
            'end' => $this->dateRange['end']->format('Y-m-d'),
        ];
    }

    protected function getDateRangeLabel(): string
    {
        if (empty($this->dateRange)) {
            $this->updateDateRange();
        }

        if ($this->dateRange['start']->isSameDay($this->dateRange['end'])) {
            return $this->dateRange['start']->format('d M Y');
        }

        if ($this->dateRange['start']->isSameMonth($this->dateRange['end'])) {
            return $this->dateRange['start']->format('M Y');
        }

        return $this->dateRange['start']->format('d M Y') . ' - ' . $this->dateRange['end']->format('d M Y');
    }

    public function table(Table $table): Table
    {
        // Get date range for filtering - HANYA DATA SESUAI FILTER
        $dateRange = $this->getDateRangeForQuery();

        return $table
            ->query(
                Absensi::query()
                    ->with([
                        'karyawan:id,nama_lengkap,nip,id_departemen',
                        'karyawan.departemen:id,nama_departemen',
                        'jadwal.shift:id,nama_shift',
                        'jadwal.entitas:id,nama'
                    ])
                    ->whereBetween('tanggal_absensi', [$dateRange['start'], $dateRange['end']])
                    ->whereNotNull('tanggal_absensi')
                    ->latest('tanggal_absensi')
                    ->latest('created_at')
            )
            ->heading('Aktivitas Terbaru - ' . $this->getDateRangeLabel())
            ->description('Data absensi periode: ' . $this->getDateRangeLabel() . ' (' . $this->getFilteredRecordsCount() . ' records)')
            ->columns([
                Tables\Columns\TextColumn::make('karyawan.nama_lengkap')
                    ->label('Nama Karyawan')
                    ->searchable()
                    ->sortable()
                    ->description(
                        fn(Absensi $record): string =>
                        $record->karyawan?->departemen?->nama_departemen ?? 'Tidak ada departemen'
                    ),

                Tables\Columns\TextColumn::make('tanggal_absensi')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable()
                    ->description(function (Absensi $record): string {
                        try {
                            return $record->tanggal_absensi ?
                                Carbon::parse($record->tanggal_absensi)->diffForHumans() :
                                '-';
                        } catch (\Exception) {
                            return '-';
                        }
                    }),

                Tables\Columns\TextColumn::make('waktu_masuk')
                    ->label('Waktu Masuk')
                    ->time('H:i')
                    ->placeholder('-')
                    ->description(
                        fn(Absensi $record): string =>
                        $record->waktu_masuk ? 'Masuk' : 'Belum masuk'
                    ),

                Tables\Columns\TextColumn::make('waktu_keluar')
                    ->label('Waktu Keluar')
                    ->time('H:i')
                    ->placeholder('-')
                    ->description(
                        fn(Absensi $record): string =>
                        $record->waktu_keluar ? 'Keluar' : 'Belum keluar'
                    ),

                Tables\Columns\TextColumn::make('durasi_kerja')
                    ->label('Durasi')
                    ->getStateUsing(function (Absensi $record): string {
                        if (!$record->waktu_masuk || !$record->waktu_keluar) {
                            return '-';
                        }

                        try {
                            // Ambil hanya waktu (HH:MM:SS atau HH:MM)
                            $waktuMasuk = $record->waktu_masuk;
                            $waktuKeluar = $record->waktu_keluar;

                            // Parse waktu masuk
                            if (strlen($waktuMasuk) == 5) { // HH:MM
                                $waktuMasuk .= ':00';
                            }
                            if (strlen($waktuKeluar) == 5) { // HH:MM
                                $waktuKeluar .= ':00';
                            }

                            $masuk = Carbon::createFromFormat('H:i:s', $waktuMasuk);
                            $keluar = Carbon::createFromFormat('H:i:s', $waktuKeluar);

                            $durasi = $masuk->diffInMinutes($keluar);

                            // Handle jika keluar lebih awal dari masuk (lintas hari)
                            if ($durasi < 0) {
                                $durasi = (24 * 60) + $durasi;
                            }

                            $jam = intval($durasi / 60);
                            $menit = $durasi % 60;

                            return "{$jam}j {$menit}m";
                        } catch (\Exception) {
                            return '-';
                        }
                    })
                    ->placeholder('-'),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'hadir' => 'success',
                        'terlambat' => 'warning',
                        'alpha' => 'danger',
                        'tidak_hadir' => 'danger',
                        'izin' => 'info',
                        'sakit' => 'gray',
                        'cuti' => 'primary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => ucfirst($state)),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dicatat')
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'hadir' => 'Hadir',
                        'terlambat' => 'Terlambat',
                        'alpha' => 'Alpha',
                        'tidak_hadir' => 'Tidak Hadir',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                        'cuti' => 'Cuti',
                    ])
                    ->multiple(),

                Tables\Filters\Filter::make('today')
                    ->label('Hari Ini')
                    ->query(fn($query) => $query->whereDate('tanggal_absensi', today()))
                    ->toggle(),

                Tables\Filters\Filter::make('has_time')
                    ->label('Ada Waktu')
                    ->query(fn($query) => $query->whereNotNull('waktu_masuk'))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('Detail')
                    ->icon('heroicon-m-eye')
                    ->url(fn(Absensi $record): string => "/admin/absensis/{$record->id}")
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('employee')
                    ->label('Karyawan')
                    ->icon('heroicon-m-user')
                    ->url(fn(Absensi $record): string => "/admin/karyawans/{$record->karyawan_id}")
                    ->openUrlInNewTab()
                    ->visible(fn(Absensi $record): bool => $record->karyawan_id !== null),
            ])
            ->defaultSort('tanggal_absensi', 'desc')
            ->paginated([10, 25, 50])
            ->defaultPaginationPageOption(10);
    }

    protected function getFilteredRecordsCount(): int
    {
        $dateRange = $this->getDateRangeForQuery();

        return Absensi::whereBetween('tanggal_absensi', [$dateRange['start'], $dateRange['end']])
            ->whereNotNull('tanggal_absensi')
            ->count();
    }
}
