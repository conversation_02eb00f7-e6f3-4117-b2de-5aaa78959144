<?php

namespace App\Filament\Widgets;

use App\Models\ProjectActivity;
use Filament\Widgets\Widget;

class RecentProjectActivity extends Widget
{
    protected static string $view = 'filament.widgets.recent-project-activity';

    protected static ?string $heading = 'Aktivitas Terbaru';

    protected static ?int $sort = 5;

    protected int | string | array $columnSpan = 'full';

    public function getViewData(): array
    {
        $activities = ProjectActivity::with(['user', 'project', 'subject'])
            ->latest()
            ->take(20)
            ->get()
            ->map(function ($activity) {
                return [
                    'id' => $activity->id,
                    'user_name' => $activity->user?->name ?? 'System',
                    'project_name' => $activity->project?->name ?? 'Unknown Project',
                    'activity_type' => $activity->activity_type,
                    'description' => $activity->description,
                    'subject_type' => class_basename($activity->subject_type),
                    'subject_id' => $activity->subject_id,
                    'created_at' => $activity->created_at,
                    'time_ago' => $activity->created_at->diffForHumans(),
                    'icon' => $this->getActivityIcon($activity->activity_type),
                    'color' => $this->getActivityColor($activity->activity_type),
                ];
            });

        return [
            'activities' => $activities,
        ];
    }

    private function getActivityIcon($activityType): string
    {
        return match ($activityType) {
            'task_created' => 'heroicon-o-plus-circle',
            'task_updated' => 'heroicon-o-pencil-square',
            'task_completed' => 'heroicon-o-check-circle',
            'comment_added' => 'heroicon-o-chat-bubble-left',
            'member_added' => 'heroicon-o-user-plus',
            'member_removed' => 'heroicon-o-user-minus',
            'project_created' => 'heroicon-o-briefcase',
            'project_updated' => 'heroicon-o-cog-6-tooth',
            'ticket_created' => 'heroicon-o-ticket',
            'ticket_updated' => 'heroicon-o-arrow-path',
            default => 'heroicon-o-information-circle',
        };
    }

    private function getActivityColor($activityType): string
    {
        return match ($activityType) {
            'task_created', 'project_created', 'ticket_created' => 'success',
            'task_updated', 'project_updated', 'ticket_updated' => 'warning',
            'task_completed' => 'primary',
            'comment_added' => 'info',
            'member_added' => 'success',
            'member_removed' => 'danger',
            default => 'gray',
        };
    }
}
