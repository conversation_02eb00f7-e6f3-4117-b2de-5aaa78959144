<?php

namespace App\Filament\Widgets;

use App\Models\PenggajianKaryawan;
use App\Models\Departemen;
use App\Models\Jabatan;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class SalaryAnalyticsWidget extends ChartWidget
{
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;
    protected static ?string $heading = 'Analisis Gaji & Kompensasi';
    protected static ?int $sort = 5;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'salary_range';

    protected function getFilters(): ?array
    {
        return [
            'salary_range' => 'Distribusi Range Gaji',
            'department_salary' => 'Rata-rata Gaji per Departemen',
            'position_salary' => 'Gaji per Jabatan (Top 10)',
            'salary_trend' => 'Trend Gaji B<PERSON>nan',
            'compensation_breakdown' => 'Breakdown Kompensasi',
        ];
    }

    protected function getData(): array
    {
        return match ($this->filter) {
            'salary_range' => $this->getSalaryRangeData(),
            'department_salary' => $this->getDepartmentSalaryData(),
            'position_salary' => $this->getPositionSalaryData(),
            'salary_trend' => $this->getSalaryTrendData(),
            'compensation_breakdown' => $this->getCompensationBreakdownData(),
            default => $this->getSalaryRangeData(),
        };
    }

    protected function getType(): string
    {
        return match ($this->filter) {
            'salary_trend' => 'line',
            'department_salary', 'position_salary' => 'bar',
            default => 'doughnut',
        };
    }

    private function getSalaryRangeData(): array
    {
        $ranges = [
            '< 5M' => [0, 5000000],
            '5M - 10M' => [5000000, 10000000],
            '10M - 15M' => [10000000, 15000000],
            '15M - 20M' => [15000000, 20000000],
            '> 20M' => [20000000, 999999999],
        ];

        $rangeData = [];
        $labels = [];

        foreach ($ranges as $label => $range) {
            $count = PenggajianKaryawan::whereBetween('gaji_pokok', $range)
                ->distinct('karyawan_id')
                ->count();

            $rangeData[] = $count;
            $labels[] = $label;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Karyawan',
                    'data' => $rangeData,
                    'backgroundColor' => [
                        'rgb(239, 68, 68)',   // Red for < 5M
                        'rgb(245, 158, 11)',  // Yellow for 5M-10M
                        'rgb(34, 197, 94)',   // Green for 10M-15M
                        'rgb(59, 130, 246)',  // Blue for 15M-20M
                        'rgb(139, 92, 246)',  // Purple for > 20M
                    ],
                ],
            ],
            'labels' => $labels,
        ];
    }

    private function getDepartmentSalaryData(): array
    {
        $departments = Departemen::with(['karyawan.penggajian' => function ($query) {
            $query->latest('periode_gaji');
        }])->get();

        $departmentNames = [];
        $avgSalaries = [];

        foreach ($departments as $department) {
            $salaries = $department->karyawan
                ->map(function ($karyawan) {
                    return $karyawan->penggajian->first()?->gaji_pokok ?? 0;
                })
                ->filter(fn($salary) => $salary > 0);

            if ($salaries->count() > 0) {
                $departmentNames[] = $department->nama_departemen;
                $avgSalaries[] = round($salaries->avg() / 1000000, 1); // Convert to millions
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Rata-rata Gaji (Juta)',
                    'data' => $avgSalaries,
                    'backgroundColor' => [
                        'rgb(59, 130, 246)',
                        'rgb(16, 185, 129)',
                        'rgb(245, 158, 11)',
                        'rgb(239, 68, 68)',
                        'rgb(139, 92, 246)',
                        'rgb(236, 72, 153)',
                        'rgb(34, 197, 94)',
                        'rgb(251, 146, 60)',
                        'rgb(168, 85, 247)',
                        'rgb(14, 165, 233)',
                    ],
                ],
            ],
            'labels' => $departmentNames,
        ];
    }

    private function getPositionSalaryData(): array
    {
        $positions = Jabatan::with(['karyawan.penggajian' => function ($query) {
            $query->latest('periode_gaji');
        }])->get();

        $positionSalaries = [];

        foreach ($positions as $position) {
            $salaries = $position->karyawan
                ->map(function ($karyawan) {
                    return $karyawan->penggajian->first()?->gaji_pokok ?? 0;
                })
                ->filter(fn($salary) => $salary > 0);

            if ($salaries->count() > 0) {
                $positionSalaries[$position->nama_jabatan] = $salaries->avg();
            }
        }

        // Sort by salary descending and take top 10
        $topPositions = collect($positionSalaries)
            ->sortDesc()
            ->take(10)
            ->map(fn($salary) => round($salary / 1000000, 1)); // Convert to millions

        return [
            'datasets' => [
                [
                    'label' => 'Rata-rata Gaji (Juta)',
                    'data' => $topPositions->values()->toArray(),
                    'backgroundColor' => array_slice([
                        'rgb(59, 130, 246)',
                        'rgb(16, 185, 129)',
                        'rgb(245, 158, 11)',
                        'rgb(239, 68, 68)',
                        'rgb(139, 92, 246)',
                        'rgb(236, 72, 153)',
                        'rgb(34, 197, 94)',
                        'rgb(251, 146, 60)',
                        'rgb(168, 85, 247)',
                        'rgb(14, 165, 233)',
                    ], 0, $topPositions->count()),
                ],
            ],
            'labels' => $topPositions->keys()->toArray(),
        ];
    }

    private function getSalaryTrendData(): array
    {
        $months = [];
        $avgSalaries = [];

        // Get last 6 months
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthYear = $date->format('Y-m');
            $monthLabel = $date->format('M Y');

            $avgSalary = PenggajianKaryawan::where('periode_gaji', $monthYear)
                ->avg('gaji_pokok');

            $months[] = $monthLabel;
            $avgSalaries[] = $avgSalary ? round($avgSalary / 1000000, 1) : 0; // Convert to millions
        }

        return [
            'datasets' => [
                [
                    'label' => 'Rata-rata Gaji (Juta)',
                    'data' => $avgSalaries,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'tension' => 0.4,
                    'fill' => true,
                ],
            ],
            'labels' => $months,
        ];
    }

    private function getCompensationBreakdownData(): array
    {
        $latestPeriod = PenggajianKaryawan::latest('periode_gaji')->value('periode_gaji');

        $compensation = PenggajianKaryawan::where('periode_gaji', $latestPeriod)
            ->selectRaw('
                AVG(gaji_pokok) as avg_gaji_pokok,
                AVG(tunjangan_jabatan) as avg_tunjangan_jabatan,
                AVG(tunjangan_umum) as avg_tunjangan_umum,
                AVG(tunjangan_sembako) as avg_tunjangan_sembako
            ')
            ->first();

        $data = [
            round($compensation->avg_gaji_pokok / 1000000, 1),
            round($compensation->avg_tunjangan_jabatan / 1000000, 1),
            round($compensation->avg_tunjangan_umum / 1000000, 1),
            round($compensation->avg_tunjangan_sembako / 1000000, 1),
        ];

        return [
            'datasets' => [
                [
                    'label' => 'Rata-rata Kompensasi (Juta)',
                    'data' => $data,
                    'backgroundColor' => [
                        'rgb(59, 130, 246)',  // Blue for basic salary
                        'rgb(34, 197, 94)',   // Green for position allowance
                        'rgb(245, 158, 11)',  // Yellow for general allowance
                        'rgb(139, 92, 246)',  // Purple for food allowance
                    ],
                ],
            ],
            'labels' => ['Gaji Pokok', 'Tunjangan Jabatan', 'Tunjangan Umum', 'Tunjangan Sembako'],
        ];
    }

    protected function getOptions(): array
    {
        $baseOptions = [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'enabled' => true,
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];

        if (in_array($this->filter, ['department_salary', 'position_salary', 'salary_trend'])) {
            $baseOptions['scales'] = [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => "function(value) { return 'Rp ' + value + 'M'; }",
                    ],
                ],
            ];
        }

        return $baseOptions;
    }
}
