<?php

namespace App\Filament\Widgets;

use App\Models\SopDokumen;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class SopOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $totalSop = SopDokumen::count();
        $sopAktif = SopDokumen::where('status', 'aktif')->count();
        $sopBerlaku = SopDokumen::aktif()->berlaku()->count();
        $sopDepartemen = SopDokumen::where('scope_type', 'departemen')->where('status', 'aktif')->count();
        $sopDivisi = SopDokumen::where('scope_type', 'divisi')->where('status', 'aktif')->count();

        return [
            Stat::make('Total SOP', $totalSop)
                ->description('Total dokumen SOP')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('primary'),

            Stat::make('SOP Aktif', $sopAktif)
                ->description('SOP dengan status aktif')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('SOP Berlaku', $sopBerlaku)
                ->description('SOP yang sedang berlaku')
                ->descriptionIcon('heroicon-m-clock')
                ->color('info'),

            Stat::make('SOP Departemen', $sopDepartemen)
                ->description('SOP untuk departemen')
                ->descriptionIcon('heroicon-m-building-office-2')
                ->color('warning'),

            Stat::make('SOP Divisi', $sopDivisi)
                ->description('SOP untuk divisi')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('danger'),
        ];
    }
}
