<?php

namespace App\Filament\Widgets;

use App\Models\Absensi;
use App\Models\Schedule;
use App\Models\SopDokumen;
use App\Models\Karyawan;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Livewire\Attributes\On;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class SystemOverviewWidget extends BaseWidget
{
    public $filters = [];
    public $dateRange = [];

    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;
    protected static ?int $sort = 1;

    public function mount(): void
    {
        // Initialize filters from session
        $this->filters = session('dashboard_filters', [
            'period_type' => 'this_month',
            'start_date' => null,
            'end_date' => null,
        ]);
        $this->updateDateRange();
    }

    #[On('filtersUpdated')]
    public function updateFilters($filters): void
    {
        $this->filters = $filters;
        $this->updateDateRange();
        $this->dispatch('$refresh');
    }

    #[On('updateCharts')]
    public function refreshWidget(): void
    {
        $this->filters = session('dashboard_filters', $this->filters);
        $this->updateDateRange();
        $this->dispatch('$refresh');
    }

    // hasacces
    public static function canView(): bool
    {
        return auth()->user()->hasAnyRole(['super_admin', 'direktur']);
    }

    protected function updateDateRange(): void
    {
        // Simple date range calculation
        $periodType = $this->filters['period_type'] ?? 'this_month';

        switch ($periodType) {
            case 'this_month':
                $this->dateRange = [
                    'start' => now()->startOfMonth(),
                    'end' => now()->endOfMonth(),
                ];
                break;
            case 'last_month':
                $this->dateRange = [
                    'start' => now()->subMonth()->startOfMonth(),
                    'end' => now()->subMonth()->endOfMonth(),
                ];
                break;
            case 'this_week':
                $this->dateRange = [
                    'start' => now()->startOfWeek(),
                    'end' => now()->endOfWeek(),
                ];
                break;
            case 'last_week':
                $this->dateRange = [
                    'start' => now()->subWeek()->startOfWeek(),
                    'end' => now()->subWeek()->endOfWeek(),
                ];
                break;
            default:
                $this->dateRange = [
                    'start' => now()->startOfMonth(),
                    'end' => now()->endOfMonth(),
                ];
        }

        // Handle custom date range
        if (isset($this->filters['start_date']) && isset($this->filters['end_date'])
            && $this->filters['start_date'] && $this->filters['end_date']) {
            $this->dateRange = [
                'start' => Carbon::parse($this->filters['start_date']),
                'end' => Carbon::parse($this->filters['end_date']),
            ];
        }
    }

    protected function getStats(): array
    {
        // Get date range for query
        $dateRange = $this->getDateRangeForQuery();

        return [
            // Total Karyawan Aktif - STATIC (tidak terpengaruh filter)
            Stat::make('Total Karyawan Aktif', $this->getTotalActiveEmployees())
                ->description('Karyawan dengan status aktif')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary')
                ->url('/admin/karyawans'),

            // Total Users - STATIC (tidak terpengaruh filter)
            Stat::make('Total Users', $this->getTotalUsers())
                ->description('Admin, Supervisor, Karyawan')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('gray')
                ->url('/admin/users'),

            // Kehadiran Periode - RESPONSIF FILTER
            Stat::make('Kehadiran Periode', $this->getAttendanceInPeriod($dateRange))
                ->description($this->getAttendanceDescription($dateRange))
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('success')
                ->url('/admin/absensis'),

            // SOP Aktif - STATIC (tidak terpengaruh filter)
            Stat::make('SOP Aktif', $this->getActiveSops())
                ->description('Dokumen SOP yang berlaku')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('warning')
                ->url('/admin/sop-dokumens'),

            // Terlambat Periode - RESPONSIF FILTER
            Stat::make('Terlambat Periode', $this->getLateAttendanceInPeriod($dateRange))
                ->description($this->getLateAttendanceDescription())
                ->descriptionIcon('heroicon-m-clock')
                ->color('danger'),

            // Tingkat Kehadiran - RESPONSIF FILTER
            Stat::make('Tingkat Kehadiran', $this->getAttendanceRate($dateRange))
                ->description($this->getAttendanceRateDescription($dateRange))
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color($this->getAttendanceRateColor($dateRange)),

            // Rata-rata KPI - RESPONSIF FILTER
            Stat::make('Rata-rata KPI', $this->getAverageKPI($dateRange))
                ->description($this->getAverageKPIDescription())
                ->descriptionIcon('heroicon-m-star')
                ->color($this->getAverageKPIColor($dateRange)),

            // Payroll Periode - RESPONSIF FILTER
            Stat::make('Payroll Periode', $this->getPayrollInPeriod($dateRange))
                ->description($this->getPayrollDescription($dateRange))
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('info'),
        ];
    }

    private function getAttendanceInPeriod($dateRange): string
    {
        // Jika tidak ada filter date range, tampilkan data bulan ini
        if (empty($this->dateRange)) {
            $start = now()->startOfMonth()->format('Y-m-d');
            $end = now()->endOfMonth()->format('Y-m-d');
        } else {
            $start = $dateRange['start'];
            $end = $dateRange['end'];
        }

        $totalScheduled = Schedule::whereBetween('tanggal_jadwal', [$start, $end])->count();
        $totalPresent = Absensi::with(['karyawan', 'jadwal'])
            ->whereBetween('tanggal_absensi', [$start, $end])
            ->whereIn('status', ['hadir', 'terlambat'])
            ->count();

        return "{$totalPresent}/{$totalScheduled}";
    }

    private function getAttendanceDescription($dateRange): string
    {
        // Jika tidak ada filter date range, tampilkan data bulan ini
        if (empty($this->dateRange)) {
            $start = now()->startOfMonth()->format('Y-m-d');
            $end = now()->endOfMonth()->format('Y-m-d');
            $periodDesc = 'bulan ini';
        } else {
            $start = $dateRange['start'];
            $end = $dateRange['end'];
            $periodDesc = $this->getDateRangeLabel();
        }

        $totalScheduled = Schedule::whereBetween('tanggal_jadwal', [$start, $end])->count();
        $totalPresent = Absensi::with(['karyawan', 'jadwal'])
            ->whereBetween('tanggal_absensi', [$start, $end])
            ->whereIn('status', ['hadir', 'terlambat'])
            ->count();

        if ($totalScheduled === 0) {
            return "Tidak ada jadwal pada {$periodDesc}";
        }

        $percentage = round(($totalPresent / $totalScheduled) * 100, 1);
        return "{$percentage}% hadir pada {$periodDesc}";
    }

    private function getTotalActiveEmployees(): int
    {
        return Karyawan::where('status_aktif', true)->count();
    }

    private function getTotalUsers(): int
    {
        return User::count();
    }

    private function getActiveSops(): int
    {
        return SopDokumen::where('status', 'aktif')->count();
    }

    private function getAttendanceRate($dateRange): string
    {
        // Jika tidak ada filter date range, tampilkan data bulan ini
        if (empty($this->dateRange)) {
            $start = now()->startOfMonth()->format('Y-m-d');
            $end = now()->endOfMonth()->format('Y-m-d');
        } else {
            $start = $dateRange['start'];
            $end = $dateRange['end'];
        }

        $totalScheduled = Schedule::whereBetween('tanggal_jadwal', [$start, $end])->count();
        $totalPresent = Absensi::with(['karyawan', 'jadwal'])
            ->whereBetween('tanggal_absensi', [$start, $end])
            ->whereIn('status', ['hadir', 'terlambat'])
            ->count();

        if ($totalScheduled === 0) {
            return '0%';
        }

        $percentage = round(($totalPresent / $totalScheduled) * 100, 1);
        return "{$percentage}%";
    }

    private function getAttendanceRateDescription($dateRange): string
    {
        // Jika tidak ada filter date range, tampilkan data bulan ini
        if (empty($this->dateRange)) {
            $start = now()->startOfMonth()->format('Y-m-d');
            $end = now()->endOfMonth()->format('Y-m-d');
            $periodDesc = 'bulan ini';
        } else {
            $start = $dateRange['start'];
            $end = $dateRange['end'];
            $periodDesc = $this->getDateRangeLabel();
        }

        $totalAbsent = Absensi::whereBetween('tanggal_absensi', [$start, $end])
            ->whereIn('status', ['tidak_hadir', 'sakit', 'izin'])
            ->count();

        return $totalAbsent > 0
            ? "{$totalAbsent} tidak hadir pada {$periodDesc}"
            : "Semua hadir pada {$periodDesc}";
    }

    private function getAttendanceRateColor($dateRange): string
    {
        // Jika tidak ada filter date range, tampilkan data bulan ini
        if (empty($this->dateRange)) {
            $start = now()->startOfMonth()->format('Y-m-d');
            $end = now()->endOfMonth()->format('Y-m-d');
        } else {
            $start = $dateRange['start'];
            $end = $dateRange['end'];
        }

        $totalScheduled = Schedule::whereBetween('tanggal_jadwal', [$start, $end])->count();
        $totalPresent = Absensi::with(['karyawan', 'jadwal'])
            ->whereBetween('tanggal_absensi', [$start, $end])
            ->whereIn('status', ['hadir', 'terlambat'])
            ->count();

        if ($totalScheduled === 0) {
            return 'gray';
        }

        $percentage = ($totalPresent / $totalScheduled) * 100;

        if ($percentage >= 90) {
            return 'success';
        } elseif ($percentage >= 75) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    // Method untuk Terlambat Periode
    private function getLateAttendanceInPeriod($dateRange): string
    {
        // Jika tidak ada filter date range, tampilkan data bulan ini
        if (empty($this->dateRange)) {
            $start = now()->startOfMonth()->format('Y-m-d');
            $end = now()->endOfMonth()->format('Y-m-d');
        } else {
            $start = $dateRange['start'];
            $end = $dateRange['end'];
        }

        $lateCount = Absensi::whereBetween('tanggal_absensi', [$start, $end])
            ->where('status', 'terlambat')
            ->count();

        return number_format($lateCount);
    }

    private function getLateAttendanceDescription(): string
    {
        // Jika tidak ada filter date range, tampilkan data bulan ini
        if (empty($this->dateRange)) {
            $periodDesc = 'bulan ini';
        } else {
            $periodDesc = $this->getDateRangeLabel();
        }

        return "Karyawan terlambat pada {$periodDesc}";
    }

    // Method untuk Rata-rata KPI
    private function getAverageKPI($dateRange): string
    {
        // Jika tidak ada filter date range, tampilkan data bulan ini
        if (empty($this->dateRange)) {
            $start = now()->startOfMonth()->format('Y-m-d');
            $end = now()->endOfMonth()->format('Y-m-d');
        } else {
            $start = $dateRange['start'];
            $end = $dateRange['end'];
        }

        // Cek apakah tabel KpiPenilaian ada
        if (!Schema::hasTable('kpi_penilaians')) {
            return 'N/A';
        }

        $avgKpi = DB::table('kpi_penilaians')
            ->whereBetween('tanggal_penilaian', [$start, $end])
            ->avg('realisasi_kpi');

        return $avgKpi ? number_format($avgKpi, 1) . '%' : 'N/A';
    }

    private function getAverageKPIDescription(): string
    {
        // Jika tidak ada filter date range, tampilkan data bulan ini
        if (empty($this->dateRange)) {
            $periodDesc = 'bulan ini';
        } else {
            $periodDesc = $this->getDateRangeLabel();
        }

        return "Rata-rata pencapaian pada {$periodDesc}";
    }

    private function getAverageKPIColor($dateRange): string
    {
        // Jika tidak ada filter date range, tampilkan data bulan ini
        if (empty($this->dateRange)) {
            $start = now()->startOfMonth()->format('Y-m-d');
            $end = now()->endOfMonth()->format('Y-m-d');
        } else {
            $start = $dateRange['start'];
            $end = $dateRange['end'];
        }

        // Cek apakah tabel KpiPenilaian ada
        if (!Schema::hasTable('kpi_penilaians')) {
            return 'gray';
        }

        $avgKpi = DB::table('kpi_penilaians')
            ->whereBetween('tanggal_penilaian', [$start, $end])
            ->avg('realisasi_kpi');

        if (!$avgKpi) {
            return 'gray';
        }

        if ($avgKpi >= 90) {
            return 'success';
        } elseif ($avgKpi >= 75) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    // Method untuk Payroll Periode
    private function getPayrollInPeriod($dateRange): string
    {
        // Jika tidak ada filter date range, tampilkan data bulan ini
        if (empty($this->dateRange)) {
            $start = now()->startOfMonth()->format('Y-m-d');
            $end = now()->endOfMonth()->format('Y-m-d');
        } else {
            $start = $dateRange['start'];
            $end = $dateRange['end'];
        }

        // Cek apakah tabel penggajian_karyawans ada
        if (!Schema::hasTable('penggajian_karyawans')) {
            return 'Rp 0';
        }

        $totalPayroll = DB::table('penggajian_karyawans')
            ->whereBetween('periode_gaji', [$start, $end])
            ->sum('total_gaji');

        return 'Rp ' . number_format($totalPayroll, 0, ',', '.');
    }

    private function getPayrollDescription($dateRange): string
    {
        // Jika tidak ada filter date range, tampilkan data bulan ini
        if (empty($this->dateRange)) {
            $start = now()->startOfMonth()->format('Y-m-d');
            $end = now()->endOfMonth()->format('Y-m-d');
            $periodDesc = 'bulan ini';
        } else {
            $start = $dateRange['start'];
            $end = $dateRange['end'];
            $periodDesc = $this->getDateRangeLabel();
        }

        // Cek apakah tabel penggajian_karyawans ada
        if (!Schema::hasTable('penggajian_karyawans')) {
            return "Data payroll tidak tersedia";
        }

        $employeeCount = DB::table('penggajian_karyawans')
            ->whereBetween('periode_gaji', [$start, $end])
            ->distinct('karyawan_id')
            ->count('karyawan_id');

        return $employeeCount > 0
            ? "{$employeeCount} karyawan pada {$periodDesc}"
            : "Belum ada payroll pada {$periodDesc}";
    }

    protected function getDateRangeForQuery(): array
    {
        if (empty($this->dateRange)) {
            $this->updateDateRange();
        }

        return [
            'start' => $this->dateRange['start']->format('Y-m-d'),
            'end' => $this->dateRange['end']->format('Y-m-d'),
        ];
    }

    protected function getDateRangeLabel(): string
    {
        if (empty($this->dateRange)) {
            $this->updateDateRange();
        }

        if ($this->dateRange['start']->isSameDay($this->dateRange['end'])) {
            return $this->dateRange['start']->format('d M Y');
        }

        if ($this->dateRange['start']->isSameMonth($this->dateRange['end'])) {
            return $this->dateRange['start']->format('M Y');
        }

        return $this->dateRange['start']->format('d M Y') . ' - ' . $this->dateRange['end']->format('d M Y');
    }
}
