<?php

namespace App\Filament\Widgets;

use App\Models\User;
use App\Models\Task;
use Filament\Widgets\ChartWidget;

class TeamPerformanceChart extends ChartWidget
{
    protected static ?string $heading = 'Performa Tim (30 <PERSON>)';

    protected static ?int $sort = 3;

    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        // Get team members with task assignments
        $teamMembers = User::whereHas('assignedTasks')
            ->withCount([
                'assignedTasks as total_tasks',
                'assignedTasks as completed_tasks' => function ($query) {
                    $query->where('status', 'completed')
                        ->where('updated_at', '>=', now()->subDays(30));
                },
                'assignedTasks as in_progress_tasks' => function ($query) {
                    $query->where('status', 'in_progress');
                },
                'assignedTasks as overdue_tasks' => function ($query) {
                    $query->where('due_date', '<', now())
                        ->where('status', '!=', 'completed');
                }
            ])
            ->take(10)
            ->get();

        $labels = $teamMembers->pluck('name')->toArray();
        $completedTasks = $teamMembers->pluck('completed_tasks')->toArray();
        $inProgressTasks = $teamMembers->pluck('in_progress_tasks')->toArray();
        $overdueTasks = $teamMembers->pluck('overdue_tasks')->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'Tugas Selesai',
                    'data' => $completedTasks,
                    'backgroundColor' => '#10B981',
                    'borderColor' => '#059669',
                ],
                [
                    'label' => 'Tugas Berjalan',
                    'data' => $inProgressTasks,
                    'backgroundColor' => '#F59E0B',
                    'borderColor' => '#D97706',
                ],
                [
                    'label' => 'Tugas Terlambat',
                    'data' => $overdueTasks,
                    'backgroundColor' => '#EF4444',
                    'borderColor' => '#DC2626',
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
            'interaction' => [
                'mode' => 'nearest',
                'axis' => 'x',
                'intersect' => false,
            ],
        ];
    }
}
