<?php

namespace App\Helpers;

class AppConstants
{
    /**
     * Get user role options
     */
    public static function getUserRoles(): array
    {
        return config('app_constants.user_roles', []);
    }

    /**
     * Get attendance status options
     */
    public static function getAttendanceStatus(): array
    {
        return config('app_constants.attendance_status', []);
    }

    /**
     * Get schedule status options
     */
    public static function getScheduleStatus(): array
    {
        return config('app_constants.schedule_status', []);
    }

    /**
     * Get gender options
     */
    public static function getGenderOptions(): array
    {
        return config('app_constants.gender_options', []);
    }

    /**
     * Get marital status options
     */
    public static function getMaritalStatus(): array
    {
        return config('app_constants.marital_status', []);
    }

    /**
     * Get religion options
     */
    public static function getReligionOptions(): array
    {
        return config('app_constants.religion_options', []);
    }

    /**
     * Get blood type options
     */
    public static function getBloodTypes(): array
    {
        return config('app_constants.blood_types', []);
    }

    /**
     * Get upload directory for specific type
     */
    public static function getUploadDirectory(string $type): string
    {
        return config("app_constants.upload_directories.{$type}", 'uploads');
    }

    /**
     * Get navigation group name
     */
    public static function getNavigationGroup(string $group): string
    {
        return config("app_constants.navigation_groups.{$group}", ucfirst($group));
    }

    /**
     * Get badge color for status
     */
    public static function getBadgeColor(string $type, string $status): string
    {
        return config("app_constants.badge_colors.{$type}.{$status}", 'gray');
    }

    /**
     * Get pagination settings
     */
    public static function getPaginationSettings(): array
    {
        return config('app_constants.pagination', []);
    }

    /**
     * Get date format
     */
    public static function getDateFormat(string $type = 'display'): string
    {
        return config("app_constants.date_formats.{$type}", 'Y-m-d');
    }

    /**
     * Get number format settings
     */
    public static function getNumberFormat(string $type): array
    {
        return config("app_constants.number_formats.{$type}", []);
    }

    /**
     * Get system limit
     */
    public static function getLimit(string $type): int
    {
        return config("app_constants.limits.{$type}", 0);
    }

    /**
     * Get default value
     */
    public static function getDefault(string $key): mixed
    {
        return config("app_constants.defaults.{$key}");
    }

    /**
     * Get validation rule
     */
    public static function getValidationRule(string $rule): mixed
    {
        return config("app_constants.validation.{$rule}");
    }

    /**
     * Format currency
     */
    public static function formatCurrency(float $amount): string
    {
        $format = self::getNumberFormat('currency');
        return $format['prefix'] . number_format(
            $amount,
            $format['decimals'],
            $format['decimal_separator'],
            $format['thousands_separator']
        );
    }

    /**
     * Format percentage
     */
    public static function formatPercentage(float $value): string
    {
        $format = self::getNumberFormat('percentage');
        return number_format($value, $format['decimals']) . $format['suffix'];
    }

    /**
     * Check if file size is within limit
     */
    public static function isFileSizeValid(int $sizeInKb, string $type = 'max_file_size'): bool
    {
        return $sizeInKb <= self::getLimit($type);
    }

    /**
     * Get all constants for JavaScript
     */
    public static function getJavaScriptConstants(): array
    {
        return [
            'userRoles' => self::getUserRoles(),
            'attendanceStatus' => self::getAttendanceStatus(),
            'scheduleStatus' => self::getScheduleStatus(),
            'genderOptions' => self::getGenderOptions(),
            'maritalStatus' => self::getMaritalStatus(),
            'religionOptions' => self::getReligionOptions(),
            'bloodTypes' => self::getBloodTypes(),
            'limits' => config('app_constants.limits', []),
            'dateFormats' => config('app_constants.date_formats', []),
        ];
    }
}
