<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class PosAuthController extends Controller
{
    /**
     * Login and create API token for POS system
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
            'device_name' => 'required|string',
            // 'location_id' => 'nullable|exists:entitas,id',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        // Check if user has permission to access POS
        if (!$this->canAccessPos($user)) {
            return response()->json([
                'message' => 'User does not have permission to access POS system.'
            ], Response::HTTP_FORBIDDEN);
        }

        // Create token with specific abilities for POS
        $token = $user->createToken($request->device_name, [
            'pos:read',
            'pos:write',
            'pos:transactions',
            'pos:products',
            'pos:customers',
            'pos:sync'
        ]);

        // Get user's assigned outlets (handle gracefully if relationship doesn't exist)
        $userOutlets = collect();
        try {
            if (method_exists($user, 'outlets')) {
                $userOutlets = $user->outlets()->wherePivot('is_active', true)->get();
            }
        } catch (\Exception $e) {
            // Log error but don't fail login
            \Log::warning('Failed to load user outlets during login', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }

        return response()->json([
            'token' => $token->plainTextToken,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'karyawan' => $user->karyawan ? [
                    'id' => $user->karyawan->id,
                    'nama_lengkap' => $user->karyawan->nama_lengkap,
                    'entitas_id' => $user->karyawan->id_entitas,
                    'jabatan' => $user->karyawan->jabatan->nama_jabatan ?? null,
                    'entitas_name' => $user->karyawan->entitas->nama ?? null,
                ] : null,
                'outlets' => $userOutlets->map(function ($outlet) {
                    return [
                        'id' => $outlet->id,
                        'name' => $outlet->name,
                        'code' => $outlet->code ?? null,
                        'categories' => $outlet->category ?? null,
                        'role' => $outlet->pivot->role ?? null,
                        'is_active' => $outlet->pivot->is_active ?? true,
                    ];
                }),
            ],
            'abilities' => $token->accessToken->abilities,
            'expires_at' => null, // Tokens don't expire by default
        ]);
    }

    /**
     * Logout and revoke current token
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'message' => 'Token revoked successfully.'
        ]);
    }

    /**
     * Logout from all devices
     */
    public function logoutAll(Request $request)
    {
        $request->user()->tokens()->delete();

        return response()->json([
            'message' => 'All tokens revoked successfully.'
        ]);
    }

    /**
     * Get current user information
     */
    public function user(Request $request)
    {
        $user = $request->user();
        
        return response()->json([
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'karyawan' => $user->karyawan ? [
                    'id' => $user->karyawan->id,
                    'nama_lengkap' => $user->karyawan->nama_lengkap,
                    'entitas_id' => $user->karyawan->id_entitas,
                    'entitas_name' => $user->karyawan->entitas->nama ?? null,
                ] : null,
            ],
            'abilities' => $request->user()->currentAccessToken()->abilities ?? [],
        ]);
    }

    /**
     * Refresh token (create new token and revoke old one)
     */
    public function refresh(Request $request)
    {
        $request->validate([
            'device_name' => 'required|string',
        ]);

        $user = $request->user();
        $oldToken = $request->user()->currentAccessToken();

        // Create new token with same abilities
        $newToken = $user->createToken($request->device_name, $oldToken->abilities);

        // Revoke old token
        $oldToken->delete();

        return response()->json([
            'token' => $newToken->plainTextToken,
            'message' => 'Token refreshed successfully.'
        ]);
    }

    /**
     * Get user's active tokens
     */
    public function tokens(Request $request)
    {
        $tokens = $request->user()->tokens()->get(['id', 'name', 'abilities', 'last_used_at', 'created_at']);

        return response()->json([
            'tokens' => $tokens
        ]);
    }

    /**
     * Revoke specific token
     */
    public function revokeToken(Request $request, $tokenId)
    {
        $token = $request->user()->tokens()->where('id', $tokenId)->first();

        if (!$token) {
            return response()->json([
                'message' => 'Token not found.'
            ], Response::HTTP_NOT_FOUND);
        }

        $token->delete();

        return response()->json([
            'message' => 'Token revoked successfully.'
        ]);
    }

    /**
     * Check if user can access POS system
     */
    private function canAccessPos(User $user): bool
    {
        // Allow admin, supervisor, manager roles
        if (in_array($user->role, ['admin', 'supervisor', 'manager'])) {
            return true;
        }

        // Allow users with specific Shield roles
        if ($user->hasAnyRole(['super_admin', 'manager_hrd', 'kepala_toko', 'keptok'])) {
            return true;
        }

        // Allow karyawan with specific permissions
        if ($user->role === 'karyawan' && $user->karyawan) {
            // Add specific logic for karyawan POS access if needed
            return true;
        }

        return false;
    }
}
