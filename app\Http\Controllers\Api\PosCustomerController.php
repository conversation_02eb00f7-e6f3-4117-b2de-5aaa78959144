<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;

class PosCustomerController extends Controller
{
    /**
     * Get all customers for POS system
     */
    public function index(Request $request)
    {
        $query = Customer::where('is_active', true);

        // Search by name, email, or phone
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('nama', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('telepon', 'like', "%{$search}%");
            });
        }

        // Filter by segment
        if ($request->has('segment')) {
            $query->where('segment', $request->segment);
        }

        // Filter by loyalty points range
        if ($request->has('min_points')) {
            $query->where('loyalty_points', '>=', $request->min_points);
        }

        if ($request->has('max_points')) {
            $query->where('loyalty_points', '<=', $request->max_points);
        }

        $customers = $query->get([
            'id', 'nama', 'email', 'telepon', 'loyalty_points', 'segment'
        ]);

        return response()->json([
            'success' => true,
            'data' => $customers,
            'meta' => [
                'total' => $customers->count(),
                'last_updated' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * Get single customer details
     */
    public function show($id)
    {
        $customer = Customer::find($id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found'
            ], Response::HTTP_NOT_FOUND);
        }

        // Get customer transaction summary
        $transactionSummary = [
            'total_transactions' => $customer->posTransactions()->count(),
            'total_spent' => $customer->posTransactions()->sum('net_amount'),
            'last_transaction' => $customer->posTransactions()->latest('transaction_date')->first()?->transaction_date,
        ];

        return response()->json([
            'success' => true,
            'data' => array_merge($customer->toArray(), [
                'transaction_summary' => $transactionSummary
            ])
        ]);
    }

    /**
     * Create new customer
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'nama' => 'required|string|max:255',
            'email' => 'nullable|email|unique:customers,email',
            'telepon' => 'nullable|string|max:20',
            'tanggal_lahir' => 'nullable|date',
            'jenis_kelamin' => 'nullable|in:L,P',
            'alamat' => 'nullable|string|max:500',
            'segment' => 'nullable|in:top_spenders,frequent_buyers,lapsed_customers,product_specific_buyers,new_customers',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        try {
            $customer = Customer::create(array_merge($request->all(), [
                'loyalty_points' => 0,
                'is_active' => true,
                'segment' => $request->segment ?? 'new_customers',
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Customer created successfully',
                'data' => $customer
            ], Response::HTTP_CREATED);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create customer',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update customer information
     */
    public function update(Request $request, $id)
    {
        $customer = Customer::find($id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found'
            ], Response::HTTP_NOT_FOUND);
        }

        $validator = Validator::make($request->all(), [
            'nama' => 'sometimes|required|string|max:255',
            'email' => 'nullable|email|unique:customers,email,' . $id,
            'telepon' => 'nullable|string|max:20',
            'tanggal_lahir' => 'nullable|date',
            'jenis_kelamin' => 'nullable|in:L,P',
            'alamat' => 'nullable|string|max:500',
            'segment' => 'nullable|in:top_spenders,frequent_buyers,lapsed_customers,product_specific_buyers,new_customers',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        try {
            $customer->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Customer updated successfully',
                'data' => $customer->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update customer',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update customer loyalty points
     */
    public function updateLoyaltyPoints(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'points' => 'required|integer',
            'action' => 'required|in:add,subtract,set',
            'transaction_number' => 'nullable|string',
            'description' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $customer = Customer::find($id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found'
            ], Response::HTTP_NOT_FOUND);
        }

        try {
            $oldPoints = $customer->loyalty_points;

            match ($request->action) {
                'add' => $customer->increment('loyalty_points', $request->points),
                'subtract' => $customer->decrement('loyalty_points', max(0, $request->points)),
                'set' => $customer->update(['loyalty_points' => max(0, $request->points)]),
            };

            $newPoints = $customer->fresh()->loyalty_points;

            // Create loyalty transaction record
            $customer->loyaltyTransactions()->create([
                'transaction_type' => $request->action === 'add' ? 'earned' : 'redeemed',
                'points' => $request->action === 'subtract' ? -$request->points : $request->points,
                'description' => $request->description ?? "Points {$request->action} via POS",
                'reference_number' => $request->transaction_number,
                'created_by' => $request->user()->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Loyalty points updated successfully',
                'data' => [
                    'customer_id' => $customer->id,
                    'old_points' => $oldPoints,
                    'new_points' => $newPoints,
                    'action' => $request->action,
                    'points_changed' => $request->points,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update loyalty points',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get customer transaction history
     */
    public function transactions(Request $request, $id)
    {
        $customer = Customer::find($id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found'
            ], Response::HTTP_NOT_FOUND);
        }

        $query = $customer->posTransactions()->with('items.product');

        // Filter by date range
        if ($request->has('from_date')) {
            $query->whereDate('transaction_date', '>=', $request->from_date);
        }

        if ($request->has('to_date')) {
            $query->whereDate('transaction_date', '<=', $request->to_date);
        }

        // Limit results
        $limit = $request->get('limit', 50);
        $transactions = $query->latest('transaction_date')->limit($limit)->get();

        return response()->json([
            'success' => true,
            'data' => $transactions,
            'meta' => [
                'customer_id' => $customer->id,
                'customer_name' => $customer->nama,
                'total_transactions' => $customer->posTransactions()->count(),
                'total_spent' => $customer->posTransactions()->sum('net_amount'),
                'limit' => $limit,
            ]
        ]);
    }

    /**
     * Search customers by phone or email
     */
    public function search(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:3',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $query = $request->query;
        
        $customers = Customer::where('is_active', true)
            ->where(function ($q) use ($query) {
                $q->where('nama', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%")
                  ->orWhere('telepon', 'like', "%{$query}%");
            })
            ->limit(20)
            ->get(['id', 'nama', 'email', 'telepon', 'loyalty_points']);

        return response()->json([
            'success' => true,
            'data' => $customers,
            'meta' => [
                'query' => $query,
                'count' => $customers->count(),
            ]
        ]);
    }

    /**
     * Get customer segments summary
     */
    public function segments()
    {
        $segments = Customer::where('is_active', true)
            ->selectRaw('segment, COUNT(*) as count, AVG(loyalty_points) as avg_points')
            ->groupBy('segment')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $segments,
            'meta' => [
                'total_customers' => Customer::where('is_active', true)->count(),
                'last_updated' => now()->toISOString(),
            ]
        ]);
    }
}
