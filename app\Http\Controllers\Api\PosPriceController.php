<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\PriceList;
use App\Models\Outlet;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;

class PosPriceController extends Controller
{
    /**
     * Get products with outlet-specific pricing
     */
    public function getProductPrices(Request $request)
    {
        $request->validate([
            'outlet_id' => 'required|exists:outlets,id',
            'product_ids' => 'nullable|array',
            'product_ids.*' => 'exists:products,id',
        ]);

        $outletId = $request->outlet_id;
        $productIds = $request->product_ids;

        // Create cache key
        $cacheKey = "outlet_prices_{$outletId}_" . md5(serialize($productIds));

        $result = Cache::remember($cacheKey, 300, function () use ($outletId, $productIds) {
            $query = Product::where('is_active', true);

            if ($productIds) {
                $query->whereIn('id', $productIds);
            }

            $products = $query->get();
            $outlet = Outlet::find($outletId);

            $productsWithPrices = $products->map(function ($product) use ($outlet) {
                $price = $outlet->getProductPrice($product->id);
                $costPrice = $outlet->getProductCostPrice($product->id);

                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'barcode' => $product->barcode,
                    'price' => $price,
                    'cost_price' => $costPrice,
                    'default_price' => $product->price,
                    'stock_quantity' => $product->stock_quantity,
                    'is_food_item' => $product->is_food_item,
                    'description' => $product->description,
                    'price_source' => $this->getPriceSource($outlet, $product->id),
                ];
            });

            return $productsWithPrices;
        });

        return response()->json([
            'success' => true,
            'data' => $result,
            'meta' => [
                'outlet_id' => $outletId,
                'total' => $result->count(),
                'last_updated' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * Get price for a specific product at an outlet
     */
    public function getProductPrice(Request $request, $productId)
    {
        $request->validate([
            'outlet_id' => 'required|exists:outlets,id',
        ]);

        $outlet = Outlet::find($request->outlet_id);
        $product = Product::find($productId);

        if (!$product) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found'
            ], Response::HTTP_NOT_FOUND);
        }

        $price = $outlet->getProductPrice($productId);
        $costPrice = $outlet->getProductCostPrice($productId);

        return response()->json([
            'success' => true,
            'data' => [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'sku' => $product->sku,
                'price' => $price,
                'cost_price' => $costPrice,
                'default_price' => $product->price,
                'price_source' => $this->getPriceSource($outlet, $productId),
                'outlet_id' => $outlet->id,
                'outlet_name' => $outlet->name,
            ]
        ]);
    }

    /**
     * Get all price lists assigned to an outlet
     */
    public function getOutletPriceLists(Request $request, $outletId)
    {
        $outlet = Outlet::find($outletId);

        if (!$outlet) {
            return response()->json([
                'success' => false,
                'message' => 'Outlet not found'
            ], Response::HTTP_NOT_FOUND);
        }

        $priceLists = $outlet->priceLists()
            ->where('is_active', true)
            ->orderBy('outlet_price_lists.priority', 'asc')
            ->get(['price_lists.*', 'outlet_price_lists.priority', 'outlet_price_lists.is_active']);

        return response()->json([
            'success' => true,
            'data' => $priceLists,
            'meta' => [
                'outlet_id' => $outlet->id,
                'outlet_name' => $outlet->name,
                'total_price_lists' => $priceLists->count(),
            ]
        ]);
    }

    /**
     * Get price comparison for a product across different outlets
     */
    public function getProductPriceComparison(Request $request, $productId)
    {
        $product = Product::find($productId);

        if (!$product) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found'
            ], Response::HTTP_NOT_FOUND);
        }

        $outlets = Outlet::where('is_active', true)->get();
        $comparison = [];

        foreach ($outlets as $outlet) {
            $price = $outlet->getProductPrice($productId);
            $comparison[] = [
                'outlet_id' => $outlet->id,
                'outlet_name' => $outlet->name,
                'price' => $price,
                'price_source' => $this->getPriceSource($outlet, $productId),
            ];
        }

        return response()->json([
            'success' => true,
            'data' => [
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'default_price' => $product->price,
                ],
                'outlet_prices' => $comparison,
            ]
        ]);
    }

    /**
     * Bulk get product prices for multiple products
     */
    public function bulkGetProductPrices(Request $request)
    {
        $request->validate([
            'outlet_id' => 'required|exists:outlets,id',
            'product_ids' => 'required|array|max:100',
            'product_ids.*' => 'exists:products,id',
        ]);

        return $this->getProductPrices($request);
    }

    /**
     * Debug price resolution for a specific product and outlet
     */
    public function debugPriceResolution(Request $request, $productId, $outletId)
    {
        $outlet = Outlet::find($outletId);
        $product = Product::find($productId);

        if (!$outlet || !$product) {
            return response()->json([
                'success' => false,
                'message' => 'Outlet or product not found'
            ], Response::HTTP_NOT_FOUND);
        }

        // Get price lists for this outlet
        $priceLists = $outlet->priceLists()
            ->where('is_active', true)
            ->orderBy('outlet_price_lists.priority', 'asc')
            ->get();

        $debugInfo = [
            'outlet' => [
                'id' => $outlet->id,
                'name' => $outlet->name,
            ],
            'product' => [
                'id' => $product->id,
                'name' => $product->name,
                'default_price' => $product->price,
            ],
            'price_lists' => [],
            'resolution_steps' => [],
        ];

        foreach ($priceLists as $priceList) {
            $item = $priceList->items()->where('product_id', $productId)->first();
            $debugInfo['price_lists'][] = [
                'id' => $priceList->id,
                'name' => $priceList->name,
                'priority' => $priceList->pivot->priority,
                'has_product' => $item ? true : false,
                'price' => $item ? $item->price : null,
            ];

            if ($item) {
                $debugInfo['resolution_steps'][] = "Found in price list '{$priceList->name}' (priority {$priceList->pivot->priority}): {$item->price}";
                break;
            } else {
                $debugInfo['resolution_steps'][] = "Not found in price list '{$priceList->name}' (priority {$priceList->pivot->priority})";
            }
        }

        $finalPrice = $outlet->getProductPrice($productId);
        $debugInfo['final_price'] = $finalPrice;
        $debugInfo['price_source'] = $this->getPriceSource($outlet, $productId);

        return response()->json([
            'success' => true,
            'debug_info' => $debugInfo,
        ]);
    }

    /**
     * Validate outlet configuration for pricing
     */
    public function validateOutletConfiguration(Request $request, $outletId)
    {
        $outlet = Outlet::find($outletId);

        if (!$outlet) {
            return response()->json([
                'success' => false,
                'message' => 'Outlet not found'
            ], Response::HTTP_NOT_FOUND);
        }

        $priceLists = $outlet->priceLists()->where('is_active', true)->count();
        $globalPriceList = PriceList::where('is_global', true)->where('is_active', true)->first();

        $validation = [
            'outlet_id' => $outlet->id,
            'outlet_name' => $outlet->name,
            'has_price_lists' => $priceLists > 0,
            'price_lists_count' => $priceLists,
            'has_global_fallback' => $globalPriceList ? true : false,
            'global_price_list' => $globalPriceList ? [
                'id' => $globalPriceList->id,
                'name' => $globalPriceList->name,
            ] : null,
            'is_valid' => $priceLists > 0 || $globalPriceList,
        ];

        return response()->json([
            'success' => true,
            'validation' => $validation,
        ]);
    }

    /**
     * Get the source of price for debugging
     */
    private function getPriceSource(Outlet $outlet, $productId)
    {
        $priceLists = $outlet->priceLists()
            ->where('is_active', true)
            ->orderBy('outlet_price_lists.priority', 'asc')
            ->get();

        foreach ($priceLists as $priceList) {
            $item = $priceList->items()->where('product_id', $productId)->first();
            if ($item) {
                return "Price List: {$priceList->name} (Priority {$priceList->pivot->priority})";
            }
        }

        $globalPriceList = PriceList::where('is_global', true)->where('is_active', true)->first();
        if ($globalPriceList) {
            $item = $globalPriceList->items()->where('product_id', $productId)->first();
            if ($item) {
                return "Global Price List: {$globalPriceList->name}";
            }
        }

        return "Product Default Price";
    }
}
