<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class PosProductController extends Controller
{
    /**
     * Get all products for POS system with outlet-specific pricing
     */
    public function index(Request $request)
    {
        // Validate outlet_id parameter (optional for backward compatibility)
        $request->validate([
            'outlet_id' => 'nullable|exists:outlets,id',
        ]);

        $outletId = $request->outlet_id;
        $outlet = $outletId ? \App\Models\Outlet::find($outletId) : null;

        $query = Product::with(['category'])
            ->where('is_active', true);

        // Filter by category
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Search by name or SKU
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%");
            });
        }

        // Filter by stock availability
        if ($request->has('in_stock') && $request->in_stock) {
            $query->where('stock_quantity', '>', 0);
        }

        // Filter by food items
        if ($request->has('food_only') && $request->food_only) {
            $query->where('is_food_item', true);
        }

        $products = $query->get([
            'id', 'name', 'sku', 'barcode', 'price', 'cost_price',
            'category_id', 'stock_quantity', 'is_food_item', 'description'
        ]);

        // Apply outlet-specific pricing if outlet is provided
        $productsWithOutletPricing = $outlet ? $products->map(function ($product) use ($outlet) {
            $outletPrice = $outlet->getProductPrice($product->id);
            $outletCostPrice = $outlet->getProductCostPrice($product->id);

            $product->outlet_price = $outletPrice;
            $product->outlet_cost_price = $outletCostPrice;
            $product->default_price = $product->price;
            $product->price = $outletPrice; // Override price with outlet-specific price

            return $product;
        }) : $products;

        return response()->json([
            'success' => true,
            'data' => $productsWithOutletPricing,
            'meta' => [
                'outlet_id' => $outletId,
                'outlet_name' => $outlet ? $outlet->name : null,
                'total' => $productsWithOutletPricing->count(),
                'last_updated' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * Get single product details
     */
    public function show($id)
    {
        $product = Product::with(['category'])->find($id);

        if (!$product) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found'
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'success' => true,
            'data' => $product
        ]);
    }

    /**
     * Update product stock
     */
    public function updateStock(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'quantity' => 'required|integer',
            'action' => 'required|in:add,subtract,set',
            'reason' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $product = Product::find($id);

        if (!$product) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found'
            ], Response::HTTP_NOT_FOUND);
        }

        $oldStock = $product->stock_quantity;

        try {
            match ($request->action) {
                'add' => $product->increment('stock_quantity', $request->quantity),
                'subtract' => $product->decrement('stock_quantity', $request->quantity),
                'set' => $product->update(['stock_quantity' => $request->quantity]),
            };

            $newStock = $product->fresh()->stock_quantity;

            // Log stock movement (if you have a stock movement model)
            // StockMovement::create([...]);

            return response()->json([
                'success' => true,
                'message' => 'Stock updated successfully',
                'data' => [
                    'product_id' => $product->id,
                    'old_stock' => $oldStock,
                    'new_stock' => $newStock,
                    'action' => $request->action,
                    'quantity' => $request->quantity,
                    'reason' => $request->reason,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update stock',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get product categories
     */
    public function categories(Request $request)
    {
        $query = Category::query();

        // Search by name
        if ($request->has('search')) {
            $query->where('name', 'like', "%{$request->search}%");
        }

        // Filter categories with products only
        if ($request->has('with_products') && $request->with_products) {
            $query->has('products');
        }

        $categories = $query->withCount('products')->get();

        return response()->json([
            'success' => true,
            'data' => $categories,
            'meta' => [
                'total' => $categories->count(),
                'last_updated' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * Check product availability
     */
    public function checkAvailability(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'products' => 'required|array',
            'products.*.id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $availability = [];
        $allAvailable = true;

        foreach ($request->products as $item) {
            $product = Product::find($item['id']);
            $isAvailable = $product && $product->is_active && $product->stock_quantity >= $item['quantity'];
            
            if (!$isAvailable) {
                $allAvailable = false;
            }

            $availability[] = [
                'product_id' => $item['id'],
                'requested_quantity' => $item['quantity'],
                'available_stock' => $product ? $product->stock_quantity : 0,
                'is_available' => $isAvailable,
                'product_name' => $product ? $product->name : 'Unknown',
                'is_active' => $product ? $product->is_active : false,
            ];
        }

        return response()->json([
            'success' => true,
            'all_available' => $allAvailable,
            'products' => $availability,
        ]);
    }

    /**
     * Get low stock products
     */
    public function lowStock(Request $request)
    {
        $threshold = $request->get('threshold', 10);

        $products = Product::with('category')
            ->where('is_active', true)
            ->where('stock_quantity', '<=', $threshold)
            ->where('stock_quantity', '>', 0)
            ->orderBy('stock_quantity', 'asc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $products,
            'meta' => [
                'threshold' => $threshold,
                'count' => $products->count(),
                'last_updated' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * Get out of stock products
     */
    public function outOfStock(Request $request)
    {
        $products = Product::with('category')
            ->where('is_active', true)
            ->where('stock_quantity', 0)
            ->orderBy('name', 'asc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $products,
            'meta' => [
                'count' => $products->count(),
                'last_updated' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * Bulk update product prices
     */
    public function bulkUpdatePrices(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'products' => 'required|array',
            'products.*.id' => 'required|exists:products,id',
            'products.*.price' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $updated = [];
        $failed = [];

        foreach ($request->products as $item) {
            try {
                $product = Product::find($item['id']);
                $oldPrice = $product->price;
                $product->update(['price' => $item['price']]);

                $updated[] = [
                    'product_id' => $product->id,
                    'name' => $product->name,
                    'old_price' => $oldPrice,
                    'new_price' => $item['price'],
                ];
            } catch (\Exception $e) {
                $failed[] = [
                    'product_id' => $item['id'],
                    'error' => $e->getMessage(),
                ];
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Bulk price update completed',
            'updated' => $updated,
            'failed' => $failed,
            'summary' => [
                'updated_count' => count($updated),
                'failed_count' => count($failed),
            ]
        ]);
    }
}
