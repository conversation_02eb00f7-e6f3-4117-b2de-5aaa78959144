<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PosTransaction;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Outlet;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Carbon\Carbon;

class PosReportController extends Controller
{
    /**
     * Get sales summary report
     */
    public function salesSummary(Request $request)
    {
        $fromDate = $request->get('from_date', Carbon::today()->toDateString());
        $toDate = $request->get('to_date', Carbon::today()->toDateString());
        $locationId = $request->get('location_id');

        $query = PosTransaction::whereBetween('transaction_date', [$fromDate, $toDate]);

        if ($locationId) {
            $query->where('outlet_id', $locationId);
        }

        $transactions = $query->get();

        $summary = [
            'period' => [
                'from' => $fromDate,
                'to' => $toDate,
            ],
            'totals' => [
                'transactions' => $transactions->count(),
                'gross_sales' => $transactions->sum('total_amount'),
                'discounts' => $transactions->sum('discount_amount'),
                'taxes' => $transactions->sum('tax_amount'),
                'net_sales' => $transactions->sum('net_amount'),
            ],
            'payment_methods' => $transactions->groupBy('payment_method')->map(function ($group, $method) {
                return [
                    'method' => $method,
                    'count' => $group->count(),
                    'amount' => $group->sum('net_amount'),
                ];
            })->values(),
            'hourly_breakdown' => $this->getHourlyBreakdown($transactions),
            'daily_breakdown' => $this->getDailyBreakdown($fromDate, $toDate, $locationId),
        ];

        return response()->json([
            'success' => true,
            'data' => $summary
        ]);
    }

    /**
     * Get top selling products report
     */
    public function topProducts(Request $request)
    {
        $fromDate = $request->get('from_date', Carbon::today()->toDateString());
        $toDate = $request->get('to_date', Carbon::today()->toDateString());
        $limit = $request->get('limit', 10);
        $locationId = $request->get('location_id');

        $query = \DB::table('pos_transaction_items')
            ->join('pos_transactions', 'pos_transaction_items.pos_transaction_id', '=', 'pos_transactions.id')
            ->join('products', 'pos_transaction_items.product_id', '=', 'products.id')
            ->whereBetween('pos_transactions.transaction_date', [$fromDate, $toDate]);

        if ($locationId) {
            $query->where('pos_transactions.outlet_id', $locationId);
        }

        $topProducts = $query
            ->select([
                'products.id',
                'products.name',
                'products.sku',
                \DB::raw('SUM(pos_transaction_items.quantity) as total_quantity'),
                \DB::raw('SUM(pos_transaction_items.total_price) as total_revenue'),
                \DB::raw('COUNT(DISTINCT pos_transactions.id) as transaction_count'),
                \DB::raw('AVG(pos_transaction_items.unit_price) as avg_price')
            ])
            ->groupBy('products.id', 'products.name', 'products.sku')
            ->orderBy('total_quantity', 'desc')
            ->limit($limit)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $topProducts,
            'meta' => [
                'period' => ['from' => $fromDate, 'to' => $toDate],
                'limit' => $limit,
                'location_id' => $locationId,
            ]
        ]);
    }

    /**
     * Get customer analytics report
     */
    public function customerAnalytics(Request $request)
    {
        $fromDate = $request->get('from_date', Carbon::today()->startOfMonth()->toDateString());
        $toDate = $request->get('to_date', Carbon::today()->toDateString());

        $customerStats = [
            'total_customers' => Customer::where('is_active', true)->count(),
            'new_customers' => Customer::whereBetween('created_at', [$fromDate, $toDate])->count(),
            'active_customers' => Customer::whereHas('posTransactions', function ($query) use ($fromDate, $toDate) {
                $query->whereBetween('transaction_date', [$fromDate, $toDate]);
            })->count(),
            'segments' => Customer::where('is_active', true)
                ->selectRaw('segment, COUNT(*) as count')
                ->groupBy('segment')
                ->get(),
            'top_customers' => Customer::with(['posTransactions' => function ($query) use ($fromDate, $toDate) {
                $query->whereBetween('transaction_date', [$fromDate, $toDate]);
            }])
                ->whereHas('posTransactions', function ($query) use ($fromDate, $toDate) {
                    $query->whereBetween('transaction_date', [$fromDate, $toDate]);
                })
                ->get()
                ->map(function ($customer) {
                    return [
                        'id' => $customer->id,
                        'name' => $customer->nama,
                        'transactions' => $customer->posTransactions->count(),
                        'total_spent' => $customer->posTransactions->sum('net_amount'),
                        'loyalty_points' => $customer->loyalty_points,
                    ];
                })
                ->sortByDesc('total_spent')
                ->take(10)
                ->values(),
        ];

        return response()->json([
            'success' => true,
            'data' => $customerStats,
            'meta' => [
                'period' => ['from' => $fromDate, 'to' => $toDate],
            ]
        ]);
    }

    /**
     * Get outlet performance report
     */
    public function locationPerformance(Request $request)
    {
        $fromDate = $request->get('from_date', Carbon::today()->toDateString());
        $toDate = $request->get('to_date', Carbon::today()->toDateString());

        $outlets = Outlet::where('is_active', true)
            ->where('status', 'active')
            ->get()
            ->map(function ($outlet) use ($fromDate, $toDate) {
                $transactions = PosTransaction::where('outlet_id', $outlet->id)
                    ->whereBetween('transaction_date', [$fromDate, $toDate])
                    ->get();

                return [
                    'id' => $outlet->id,
                    'name' => $outlet->name,
                    'code' => $outlet->code,
                    'type' => $outlet->type,
                    'city' => $outlet->city,
                    'transactions' => $transactions->count(),
                    'revenue' => $transactions->sum('net_amount'),
                    'avg_transaction' => $transactions->count() > 0 ? $transactions->sum('net_amount') / $transactions->count() : 0,
                    'active_staff' => $outlet->karyawan()->where('status_aktif', 1)->count(),
                ];
            })
            ->sortByDesc('revenue')
            ->values();

        return response()->json([
            'success' => true,
            'data' => $outlets,
            'meta' => [
                'period' => ['from' => $fromDate, 'to' => $toDate],
                'total_outlets' => $outlets->count(),
            ]
        ]);
    }

    /**
     * Get inventory report
     */
    public function inventoryReport(Request $request)
    {
        $lowStockThreshold = $request->get('low_stock_threshold', 10);

        $inventory = [
            'total_products' => Product::where('is_active', true)->count(),
            'in_stock' => Product::where('is_active', true)->where('stock_quantity', '>', 0)->count(),
            'low_stock' => Product::where('is_active', true)
                ->where('stock_quantity', '>', 0)
                ->where('stock_quantity', '<=', $lowStockThreshold)
                ->count(),
            'out_of_stock' => Product::where('is_active', true)->where('stock_quantity', 0)->count(),
            'total_value' => Product::where('is_active', true)
                ->selectRaw('SUM(price * stock_quantity) as total')
                ->value('total') ?? 0,
            'low_stock_products' => Product::with('category')
                ->where('is_active', true)
                ->where('stock_quantity', '>', 0)
                ->where('stock_quantity', '<=', $lowStockThreshold)
                ->orderBy('stock_quantity', 'asc')
                ->get(['id', 'name', 'sku', 'stock_quantity', 'price', 'category_id']),
            'out_of_stock_products' => Product::with('category')
                ->where('is_active', true)
                ->where('stock_quantity', 0)
                ->orderBy('name', 'asc')
                ->get(['id', 'name', 'sku', 'stock_quantity', 'price', 'category_id']),
        ];

        return response()->json([
            'success' => true,
            'data' => $inventory,
            'meta' => [
                'low_stock_threshold' => $lowStockThreshold,
                'generated_at' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * Get financial summary report
     */
    public function financialSummary(Request $request)
    {
        $fromDate = $request->get('from_date', Carbon::today()->startOfMonth()->toDateString());
        $toDate = $request->get('to_date', Carbon::today()->toDateString());

        $transactions = PosTransaction::whereBetween('transaction_date', [$fromDate, $toDate])->get();

        $financial = [
            'period' => ['from' => $fromDate, 'to' => $toDate],
            'revenue' => [
                'gross_sales' => $transactions->sum('total_amount'),
                'discounts' => $transactions->sum('discount_amount'),
                'taxes' => $transactions->sum('tax_amount'),
                'net_sales' => $transactions->sum('net_amount'),
            ],
            'transactions' => [
                'total_count' => $transactions->count(),
                'avg_value' => $transactions->count() > 0 ? $transactions->sum('net_amount') / $transactions->count() : 0,
                'largest_transaction' => $transactions->max('net_amount') ?? 0,
                'smallest_transaction' => $transactions->min('net_amount') ?? 0,
            ],
            'payment_breakdown' => $transactions->groupBy('payment_method')->map(function ($group, $method) {
                return [
                    'method' => $method,
                    'count' => $group->count(),
                    'amount' => $group->sum('net_amount'),
                    'percentage' => $group->sum('net_amount') / $transactions->sum('net_amount') * 100,
                ];
            })->values(),
            'daily_totals' => $this->getDailyBreakdown($fromDate, $toDate),
        ];

        return response()->json([
            'success' => true,
            'data' => $financial
        ]);
    }

    /**
     * Get hourly breakdown for transactions
     */
    private function getHourlyBreakdown($transactions)
    {
        $hourly = [];
        for ($hour = 0; $hour < 24; $hour++) {
            $hourlyTransactions = $transactions->filter(function ($transaction) use ($hour) {
                return Carbon::parse($transaction->transaction_date)->hour === $hour;
            });

            $hourly[] = [
                'hour' => sprintf('%02d:00', $hour),
                'transactions' => $hourlyTransactions->count(),
                'revenue' => $hourlyTransactions->sum('net_amount'),
            ];
        }

        return $hourly;
    }

    /**
     * Get daily breakdown for date range
     */
    private function getDailyBreakdown($fromDate, $toDate, $locationId = null)
    {
        $start = Carbon::parse($fromDate);
        $end = Carbon::parse($toDate);
        $daily = [];

        while ($start <= $end) {
            $query = PosTransaction::whereDate('transaction_date', $start);

            if ($locationId) {
                $query->where('outlet_id', $locationId);
            }

            $dayTransactions = $query->get();

            $daily[] = [
                'date' => $start->toDateString(),
                'day_name' => $start->format('l'),
                'transactions' => $dayTransactions->count(),
                'revenue' => $dayTransactions->sum('net_amount'),
            ];

            $start->addDay();
        }

        return $daily;
    }
}
