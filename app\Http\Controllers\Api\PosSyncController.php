<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PosTransaction;
use App\Models\PosTransactionItem;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Entitas;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Carbon\Carbon;

class PosSyncController extends Controller
{
    /**
     * Sync transactions from POS to backoffice
     */
    public function syncTransactions(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'transactions' => 'required|array',
            'transactions.*.transaction_number' => 'required|string|unique:pos_transactions,transaction_number',
            'transactions.*.customer_id' => 'nullable|exists:customers,id',
            'transactions.*.transaction_date' => 'required|date',
            'transactions.*.total_amount' => 'required|numeric|min:0',
            'transactions.*.discount_amount' => 'nullable|numeric|min:0',
            'transactions.*.tax_amount' => 'nullable|numeric|min:0',
            'transactions.*.net_amount' => 'required|numeric|min:0',
            'transactions.*.payment_method' => 'required|string',
            'transactions.*.amount_paid' => 'required|numeric|min:0',
            'transactions.*.change_given' => 'nullable|numeric|min:0',
            'transactions.*.table_number' => 'nullable|string',
            'transactions.*.items' => 'required|array|min:1',
            'transactions.*.items.*.product_id' => 'required|exists:products,id',
            'transactions.*.items.*.quantity' => 'required|integer|min:1',
            'transactions.*.items.*.unit_price' => 'required|numeric|min:0',
            'transactions.*.items.*.discount_per_item' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $syncedTransactions = [];
        $failedTransactions = [];

        DB::beginTransaction();
        try {
            foreach ($request->transactions as $transactionData) {
                try {
                    // Create transaction
                    $transaction = PosTransaction::create([
                        'transaction_number' => $transactionData['transaction_number'],
                        'customer_id' => $transactionData['customer_id'] ?? null,
                        'user_id' => $request->user()->id,
                        'transaction_date' => $transactionData['transaction_date'],
                        'total_amount' => $transactionData['total_amount'],
                        'discount_amount' => $transactionData['discount_amount'] ?? 0,
                        'tax_amount' => $transactionData['tax_amount'] ?? 0,
                        'net_amount' => $transactionData['net_amount'],
                        'payment_method' => $transactionData['payment_method'],
                        'amount_paid' => $transactionData['amount_paid'],
                        'change_given' => $transactionData['change_given'] ?? 0,
                        'table_number' => $transactionData['table_number'] ?? null,
                        'is_offline_transaction' => true,
                        'synced_at' => now(),
                    ]);

                    // Create transaction items
                    foreach ($transactionData['items'] as $itemData) {
                        PosTransactionItem::create([
                            'pos_transaction_id' => $transaction->id,
                            'product_id' => $itemData['product_id'],
                            'quantity' => $itemData['quantity'],
                            'unit_price' => $itemData['unit_price'],
                            'discount_per_item' => $itemData['discount_per_item'] ?? 0,
                        ]);

                        // Update product stock
                        $product = Product::find($itemData['product_id']);
                        if ($product) {
                            $product->decrement('stock_quantity', $itemData['quantity']);
                        }
                    }

                    $syncedTransactions[] = [
                        'transaction_number' => $transaction->transaction_number,
                        'id' => $transaction->id,
                        'status' => 'synced'
                    ];

                } catch (\Exception $e) {
                    $failedTransactions[] = [
                        'transaction_number' => $transactionData['transaction_number'],
                        'error' => $e->getMessage()
                    ];
                }
            }

            DB::commit();

            return response()->json([
                'message' => 'Sync completed',
                'synced_count' => count($syncedTransactions),
                'failed_count' => count($failedTransactions),
                'synced_transactions' => $syncedTransactions,
                'failed_transactions' => $failedTransactions,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Sync failed',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get products for POS system
     */
    public function getProducts(Request $request)
    {
        // Validate outlet_id parameter
        $request->validate([
            'outlet_id' => 'nullable|exists:outlets,id',
        ]);

        // Create cache key based on request parameters
        $cacheKey = 'pos_sync_products_' . md5(serialize($request->all()));

        // Cache for 5 minutes to reduce database load
        $result = \Cache::remember($cacheKey, 300, function () use ($request) {
            $outletId = $request->outlet_id;
            $outlet = $outletId ? \App\Models\Outlet::find($outletId) : null;

            $query = Product::with('category')
                ->where('is_active', true);

            // Filter by category if provided
            if ($request->has('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            // Search by name or SKU
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('sku', 'like', "%{$search}%");
                });
            }

            // Filter by stock availability
            if ($request->has('in_stock') && $request->in_stock) {
                $query->where('stock_quantity', '>', 0);
            }

            // Add pagination support
            $perPage = $request->get('per_page', 100); // Default 100 items per page
            $page = $request->get('page', 1);

            if ($request->has('paginate') && $request->paginate) {
                $products = $query->paginate($perPage, ['id', 'name', 'sku', 'price', 'cost_price', 'category_id', 'stock_quantity', 'is_food_item']);

                // Apply outlet-specific pricing if outlet is provided
                $productsWithPricing = $outlet ?
                    collect($products->items())->map(function ($product) use ($outlet) {
                        $product->outlet_price = $outlet->getProductPrice($product->id);
                        $product->outlet_cost_price = $outlet->getProductCostPrice($product->id);
                        $product->default_price = $product->price;
                        $product->price = $product->outlet_price; // Override with outlet price
                        return $product;
                    })->toArray() : $products->items();

                return [
                    'products' => $productsWithPricing,
                    'pagination' => [
                        'current_page' => $products->currentPage(),
                        'last_page' => $products->lastPage(),
                        'per_page' => $products->perPage(),
                        'total' => $products->total(),
                        'has_more_pages' => $products->hasMorePages(),
                    ],
                    'outlet_id' => $outletId,
                    'outlet_name' => $outlet ? $outlet->name : null,
                    'last_updated' => now()->toISOString(),
                ];
            } else {
                // Return all products (existing behavior)
                $products = $query->get(['id', 'name', 'sku', 'price', 'cost_price', 'category_id', 'stock_quantity', 'is_food_item']);

                // Apply outlet-specific pricing if outlet is provided
                $productsWithPricing = $outlet ?
                    $products->map(function ($product) use ($outlet) {
                        $product->outlet_price = $outlet->getProductPrice($product->id);
                        $product->outlet_cost_price = $outlet->getProductCostPrice($product->id);
                        $product->default_price = $product->price;
                        $product->price = $product->outlet_price; // Override with outlet price
                        return $product;
                    }) : $products;

                return [
                    'products' => $productsWithPricing,
                    'total_count' => $productsWithPricing->count(),
                    'outlet_id' => $outletId,
                    'outlet_name' => $outlet ? $outlet->name : null,
                    'last_updated' => now()->toISOString(),
                ];
            }
        });

        return response()->json($result);
    }

    /**
     * Get customers for POS system
     */
    public function getCustomers(Request $request)
    {
        $query = Customer::where('is_active', true);

        // Search by name, email, or phone
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('nama', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('telepon', 'like', "%{$search}%");
            });
        }

        $customers = $query->get(['id', 'nama', 'email', 'telepon', 'loyalty_points']);

        return response()->json([
            'customers' => $customers,
            'last_updated' => now()->toISOString(),
        ]);
    }

    /**
     * Update customer loyalty points
     */
    public function updateLoyaltyPoints(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|exists:customers,id',
            'points' => 'required|integer',
            'transaction_number' => 'required|string',
            'type' => 'required|in:earned,redeemed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        try {
            $customer = Customer::find($request->customer_id);
            
            if ($request->type === 'earned') {
                $customer->increment('loyalty_points', $request->points);
            } else {
                $customer->decrement('loyalty_points', $request->points);
            }

            return response()->json([
                'message' => 'Loyalty points updated successfully',
                'customer' => [
                    'id' => $customer->id,
                    'nama' => $customer->nama,
                    'loyalty_points' => $customer->loyalty_points,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update loyalty points',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get sync status and statistics
     */
    public function getSyncStatus(Request $request)
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();

        $stats = [
            'total_transactions' => PosTransaction::count(),
            'offline_transactions' => PosTransaction::where('is_offline_transaction', true)->count(),
            'today_transactions' => PosTransaction::whereDate('transaction_date', $today)->count(),
            'month_transactions' => PosTransaction::where('transaction_date', '>=', $thisMonth)->count(),
            'last_sync' => PosTransaction::where('is_offline_transaction', true)
                ->latest('synced_at')
                ->value('synced_at'),
            'pending_sync_count' => 0, // This would come from POS system
        ];

        // Add rate limit debugging info
        $user = $request->user();
        $ip = $request->ip();

        $rateLimitInfo = [
            'user_id' => $user?->id,
            'ip_address' => $ip,
            'api_rate_limit_remaining' => \RateLimiter::remaining('api:' . ($user?->id ?: $ip), 60),
            'pos_sync_rate_limit_remaining' => \RateLimiter::remaining('pos_sync:' . ($user?->id ?: $ip), 300),
        ];

        return response()->json([
            'sync_status' => 'online',
            'statistics' => $stats,
            'rate_limit_debug' => $rateLimitInfo,
            'server_time' => now()->toISOString(),
        ]);
    }
}
