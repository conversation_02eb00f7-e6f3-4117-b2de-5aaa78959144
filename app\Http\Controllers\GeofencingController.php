<?php

namespace App\Http\Controllers;

use App\Models\Karyawan;
use App\Services\GeofencingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class GeofencingController extends Controller
{
    /**
     * Validate attendance location for current employee
     */
    public function validateAttendanceLocation(Request $request): JsonResponse
    {
        try {
            // Validate request
            $request->validate([
                'latitude' => 'required|numeric|between:-90,90',
                'longitude' => 'required|numeric|between:-180,180',
            ]);

            $user = Auth::user();
            $karyawan = Karyawan::where('id_user', $user->id)->first();

            if (!$karyawan) {
                return response()->json([
                    'allowed' => false,
                    'message' => 'Data karyawan tidak ditemukan',
                    'error' => true
                ], 404);
            }

            // Validate geofencing
            $validation = GeofencingService::validateAttendanceLocation(
                $karyawan,
                $request->latitude,
                $request->longitude
            );

            // Log validation attempt
            Log::info('Geofencing validation attempt:', [
                'karyawan_id' => $karyawan->id,
                'user_id' => $user->id,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'result' => $validation
            ]);

            return response()->json($validation);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'allowed' => false,
                'message' => 'Data koordinat tidak valid',
                'errors' => $e->errors(),
                'error' => true
            ], 422);

        } catch (\Exception $e) {
            Log::error('Geofencing validation error:', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'allowed' => false,
                'message' => 'Terjadi kesalahan saat memvalidasi lokasi',
                'error' => true
            ], 500);
        }
    }

    /**
     * Get workplace location information for current employee
     */
    public function getWorkplaceLocationInfo(): JsonResponse
    {
        try {
            $user = Auth::user();
            $karyawan = Karyawan::where('id_user', $user->id)->first();

            if (!$karyawan) {
                return response()->json([
                    'has_location' => false,
                    'message' => 'Data karyawan tidak ditemukan'
                ], 404);
            }

            $locationInfo = GeofencingService::getAttendanceLocationInfo($karyawan);

            return response()->json($locationInfo);

        } catch (\Exception $e) {
            Log::error('Get workplace location info error:', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'has_location' => false,
                'message' => 'Terjadi kesalahan saat mengambil informasi lokasi'
            ], 500);
        }
    }
}
