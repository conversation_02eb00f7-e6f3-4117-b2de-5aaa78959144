<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Karyawan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Filament\Notifications\Notification;

class UserKaryawanController extends Controller
{
    /**
     * Link a user to a karyawan
     */
    public function linkUserToKaryawan(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'karyawan_id' => 'required|exists:karyawan,id',
        ]);

        try {
            DB::beginTransaction();

            $user = User::with('karyawan')->findOrFail($request->user_id);
            $karyawan = Karyawan::with('user')->findOrFail($request->karyawan_id);

            // Check if karyawan already has a user
            if ($karyawan->id_user && $karyawan->id_user != $user->id) {
                $existingUser = User::with('karyawan')->find($karyawan->id_user);
                if ($existingUser) {
                    return response()->json([
                        'success' => false,
                        'message' => "Karyawan sudah terkait dengan pengguna {$existingUser->name}",
                    ], 422);
                }
            }

            // Update karyawan with user_id
            $karyawan->update(['id_user' => $user->id]);

            // Update user role if needed
            if ($user->role !== 'karyawan') {
                $user->update(['role' => 'karyawan']);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Berhasil mengaitkan pengguna dengan karyawan',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Gagal mengaitkan pengguna dengan karyawan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Unlink a user from a karyawan
     */
    public function unlinkUserFromKaryawan(Request $request)
    {
        $request->validate([
            'karyawan_id' => 'required|exists:karyawan,id',
        ]);

        try {
            DB::beginTransaction();

            $karyawan = Karyawan::with('user')->findOrFail($request->karyawan_id);

            // Check if karyawan has a user
            if (!$karyawan->id_user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Karyawan tidak terkait dengan pengguna manapun',
                ], 422);
            }

            // Update karyawan to remove user_id
            $karyawan->update(['id_user' => null]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Berhasil memutuskan kaitan pengguna dengan karyawan',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Gagal memutuskan kaitan pengguna dengan karyawan: ' . $e->getMessage(),
            ], 500);
        }
    }
}
