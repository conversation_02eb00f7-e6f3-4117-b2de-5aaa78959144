<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureAdminOrSupervisorRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();



        // Check if user is authenticated
        if (!$user) {
            return redirect()->route('filament.admin.auth.login');
        }

        // Check if user has karyawan
        if ($user->karyawan()->exists()) {
            return redirect()->route('filament.karyawan.pages.dashboard');
        }


        // // Check if user has admin or supervisor role

        // if (!$hasAdminAccess) {
        //     // If user is karyawan, redirect to karyawan panel
        //     if ($user->role === 'karyawan') {
        //         return redirect()->route('filament.karyawan.pages.dashboard');
        //     }

        //     // For other roles or no role, logout and redirect to login
        //     Auth::logout();
        //     $request->session()->invalidate();
        //     $request->session()->regenerateToken();

        //     return redirect()->route('filament.admin.auth.login')
        //         ->with('error', 'Anda tidak memiliki akses ke halaman admin.');
        // }

        return $next($request);
    }
}
