<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class HandleSessionExpired
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if the session has expired
        if (!$request->session()->has('_token')) {
            // Clear any existing authentication
            Auth::logout();

            // Regenerate the session
            $request->session()->regenerate();

            // If it's an AJAX request, return a JSON response
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'message' => 'Session expired. Please refresh the page and login again.',
                    'session_expired' => true,
                ], 401);
            }

            // For regular requests, redirect to login page with a message
            return redirect()->route('filament.admin.auth.login')
                ->with('message', 'Session expired. Please login again.');
        }

        return $next($request);
    }
}
