<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class LivewireMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Jika ini adalah request Livewire, pastikan method-nya POST
        if ($request->is('livewire/update') && $request->isMethod('GET')) {
            // Konversi GET request ke POST request
            $request->setMethod('POST');
        }

        return $next($request);
    }
}
