<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class PosApiRateLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $type = 'general'): Response
    {
        $key = $this->resolveRequestSignature($request, $type);
        $maxAttempts = $this->getMaxAttempts($type);
        $decayMinutes = $this->getDecayMinutes($type);

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            return response()->json([
                'success' => false,
                'message' => 'Too many requests. Please try again later.',
                'retry_after' => RateLimiter::availableIn($key),
            ], Response::HTTP_TOO_MANY_REQUESTS);
        }

        RateLimiter::hit($key, $decayMinutes * 60);

        $response = $next($request);

        // Add rate limit headers
        $response->headers->add([
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => RateLimiter::remaining($key, $maxAttempts),
            'X-RateLimit-Reset' => RateLimiter::availableIn($key),
        ]);

        return $response;
    }

    /**
     * Resolve the rate limiting signature for the request.
     */
    protected function resolveRequestSignature(Request $request, string $type): string
    {
        $user = $request->user();
        $ip = $request->ip();
        
        if ($user) {
            return "pos_api_{$type}:{$user->id}";
        }

        return "pos_api_{$type}:{$ip}";
    }

    /**
     * Get the maximum number of attempts for the given type.
     */
    protected function getMaxAttempts(string $type): int
    {
        return match ($type) {
            'auth' => config('pos.api.rate_limit.auth', 10),
            'sync' => config('pos.api.rate_limit.sync', 100),
            'general' => config('pos.api.rate_limit.general', 60),
            default => 60,
        };
    }

    /**
     * Get the decay time in minutes for the given type.
     */
    protected function getDecayMinutes(string $type): int
    {
        return match ($type) {
            'auth' => 1,
            'sync' => 1,
            'general' => 1,
            default => 1,
        };
    }
}
