<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ValidateOutletAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip validation if no outlet_id is provided
        if (!$request->has('outlet_id') && !$request->route('outletId')) {
            return $next($request);
        }

        // Get outlet_id from request parameter or route parameter
        $outletId = $request->input('outlet_id') ?? $request->route('outletId');

        // Skip validation if user is not authenticated
        if (!$request->user()) {
            return $next($request);
        }

        $user = $request->user();

        // Allow admin and super admin to access any outlet
        if ($user->role === 'admin' || $user->hasRole('super_admin')) {
            return $next($request);
        }

        // Check if user has access to the requested outlet
        if (!$user->hasAccessToOutlet($outletId)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have access to this outlet.',
                'error_code' => 'OUTLET_ACCESS_DENIED',
                'outlet_id' => $outletId,
                'user_outlets' => $user->activeOutlets()->pluck('id')->toArray(),
            ], Response::HTTP_FORBIDDEN);
        }

        // Add outlet information to request for use in controllers
        $outlet = $user->activeOutlets()->where('outlets.id', $outletId)->first();
        if ($outlet) {
            $request->merge([
                'user_outlet_role' => $outlet->pivot->role,
                'user_outlet_access' => true,
            ]);
        }

        return $next($request);
    }
}
