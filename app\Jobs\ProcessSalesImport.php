<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Imports\SalesDataImport;
use App\Models\User;
use Maatwebsite\Excel\Facades\Excel;
use Filament\Notifications\Notification;
use Exception;

class ProcessSalesImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $filePath;
    protected $warehouseId;
    protected $userId;

    public $timeout = 300; // 5 minutes timeout

    /**
     * Create a new job instance.
     */
    public function __construct(string $filePath, int $warehouseId, int $userId)
    {
        $this->filePath = $filePath;
        $this->warehouseId = $warehouseId;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting sales import process', [
                'file_path' => $this->filePath,
                'warehouse_id' => $this->warehouseId,
                'user_id' => $this->userId
            ]);

            // Check if file exists
            if (!Storage::exists($this->filePath)) {
                throw new Exception('File tidak ditemukan: ' . $this->filePath);
            }

            // Get file path
            $fullPath = Storage::path($this->filePath);

            // Create import instance with warehouse ID
            $import = new SalesDataImport($this->warehouseId, $this->userId);

            // Import the Excel file
            Excel::import($import, $fullPath);

            // Get import results
            $results = $import->getResults();

            // Send success notification
            $this->sendSuccessNotification($results);

            // Clean up the uploaded file
            if (Storage::exists($this->filePath)) {
                Storage::delete($this->filePath);
            }

            Log::info('Sales import completed successfully', $results);
        } catch (Exception $e) {
            Log::error('Sales import failed', [
                'file_path' => $this->filePath,
                'warehouse_id' => $this->warehouseId,
                'user_id' => $this->userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->sendErrorNotification($e->getMessage());

            // Clean up file on error
            if (Storage::exists($this->filePath)) {
                Storage::delete($this->filePath);
            }

            throw $e;
        }
    }

    /**
     * Send success notification to user
     */
    private function sendSuccessNotification(array $results): void
    {
        $user = User::find($this->userId);

        if ($user) {
            Notification::make()
                ->title('Import Penjualan Berhasil')
                ->body(sprintf(
                    'Import selesai! %d transaksi berhasil diproses, %d jurnal dibuat.',
                    $results['transactions_created'] ?? 0,
                    $results['journals_created'] ?? 0
                ))
                ->success()
                ->sendToDatabase($user);
        }
    }

    /**
     * Send error notification to user
     */
    private function sendErrorNotification(string $errorMessage): void
    {
        $user = User::find($this->userId);

        if ($user) {
            Notification::make()
                ->title('Import Penjualan Gagal')
                ->body('Terjadi kesalahan saat memproses file: ' . $errorMessage)
                ->danger()
                ->sendToDatabase($user);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('Sales import job failed', [
            'file_path' => $this->filePath,
            'warehouse_id' => $this->warehouseId,
            'user_id' => $this->userId,
            'error' => $exception->getMessage()
        ]);

        $this->sendErrorNotification($exception->getMessage());

        // Clean up the uploaded file even on failure
        if (Storage::exists($this->filePath)) {
            Storage::delete($this->filePath);
        }
    }
}
