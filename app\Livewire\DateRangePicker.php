<?php

namespace App\Livewire;

use Livewire\Component;
use Carbon\Carbon;

class DateRangePicker extends Component
{
    public $startDate = null;
    public $endDate = null;
    public $timeStart = '00:00';
    public $timeEnd = '23:59';
    public $placeholder = 'Pilih rentang tanggal';
    public $isOpen = false;
    public $selectedPreset = null;
    
    public $presets = [
        'today' => 'Hari Ini',
        'yesterday' => 'Kemarin',
        '7days' => '7 Hari Terakhir',
        'thisWeek' => 'Minggu Ini',
        'lastWeek' => 'Minggu Lalu',
        '30days' => '30 Hari Terakhir',
        'thisMonth' => 'Bulan Ini',
        'lastMonth' => 'Bulan Lalu'
    ];

    public function mount($startDate = null, $endDate = null, $placeholder = 'Pilih rentang tanggal')
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->placeholder = $placeholder;
    }

    public function toggleDropdown()
    {
        $this->isOpen = !$this->isOpen;
    }

    public function closeDropdown()
    {
        $this->isOpen = false;
    }

    public function selectPreset($presetKey)
    {
        $this->selectedPreset = $presetKey;
        $today = Carbon::today();
        
        switch ($presetKey) {
            case 'today':
                $this->startDate = $today->format('Y-m-d');
                $this->endDate = $today->format('Y-m-d');
                break;
            case 'yesterday':
                $yesterday = $today->copy()->subDay();
                $this->startDate = $yesterday->format('Y-m-d');
                $this->endDate = $yesterday->format('Y-m-d');
                break;
            case '7days':
                $this->startDate = $today->copy()->subDays(6)->format('Y-m-d');
                $this->endDate = $today->format('Y-m-d');
                break;
            case 'thisWeek':
                $this->startDate = $today->copy()->startOfWeek()->format('Y-m-d');
                $this->endDate = $today->format('Y-m-d');
                break;
            case 'lastWeek':
                $lastWeekStart = $today->copy()->subWeek()->startOfWeek();
                $lastWeekEnd = $today->copy()->subWeek()->endOfWeek();
                $this->startDate = $lastWeekStart->format('Y-m-d');
                $this->endDate = $lastWeekEnd->format('Y-m-d');
                break;
            case '30days':
                $this->startDate = $today->copy()->subDays(29)->format('Y-m-d');
                $this->endDate = $today->format('Y-m-d');
                break;
            case 'thisMonth':
                $this->startDate = $today->copy()->startOfMonth()->format('Y-m-d');
                $this->endDate = $today->format('Y-m-d');
                break;
            case 'lastMonth':
                $lastMonthStart = $today->copy()->subMonth()->startOfMonth();
                $lastMonthEnd = $today->copy()->subMonth()->endOfMonth();
                $this->startDate = $lastMonthStart->format('Y-m-d');
                $this->endDate = $lastMonthEnd->format('Y-m-d');
                break;
        }
    }

    public function process()
    {
        if (!$this->startDate || !$this->endDate) {
            return;
        }

        $this->dispatch('daterange-selected', [
            'start' => $this->startDate,
            'end' => $this->endDate,
            'timeStart' => $this->timeStart,
            'timeEnd' => $this->timeEnd
        ]);

        $this->isOpen = false;
    }

    public function cancel()
    {
        $this->isOpen = false;
        $this->selectedPreset = null;
    }

    public function getDisplayTextProperty()
    {
        if (!$this->startDate || !$this->endDate) {
            return $this->placeholder;
        }

        if ($this->selectedPreset && isset($this->presets[$this->selectedPreset])) {
            return $this->presets[$this->selectedPreset];
        }

        $start = Carbon::parse($this->startDate)->format('d/m/Y');
        $end = Carbon::parse($this->endDate)->format('d/m/Y');
        
        return "{$start} - {$end}";
    }

    public function render()
    {
        return view('livewire.date-range-picker');
    }
}
