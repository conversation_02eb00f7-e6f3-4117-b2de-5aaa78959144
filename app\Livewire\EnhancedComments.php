<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Task;
use App\Models\TaskComment;
use App\Models\CommentReaction;
use App\Models\TeamMention;
use App\Models\TeamActivityFeed;
use App\Models\User;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class EnhancedComments extends Component
{
    use WithFileUploads;

    public Task $task;
    public $comments = [];
    public $newComment = '';
    public $replyingTo = null;
    public $replyComment = '';
    public $editingComment = null;
    public $editComment = '';
    public $attachments = [];
    public $showEmojiPicker = [];
    public $mentionSuggestions = [];
    public $showMentions = false;

    protected $listeners = [
        'commentAdded' => 'loadComments',
        'reactionToggled' => 'loadComments',
    ];

    public function mount(Task $task)
    {
        $this->task = $task;
        $this->loadComments();
    }

    public function loadComments()
    {
        $this->comments = $this->task->comments()
            ->with([
                'user',
                'replies.user',
                'reactions.user',
                'mentions.mentionedUser',
                'fileShares'
            ])
            ->whereNull('parent_id') // Only load top-level comments
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($comment) {
                return [
                    'id' => $comment->id,
                    'comment' => $comment->comment,
                    'formatted_comment' => $comment->formatted_comment,
                    'body_parsed' => $comment->body_parsed,
                    'user' => $comment->user,
                    'created_at' => $comment->created_at,
                    'is_edited' => $comment->is_edited,
                    'edited_at' => $comment->edited_at,
                    'replies' => $comment->replies->map(function ($reply) {
                        return [
                            'id' => $reply->id,
                            'comment' => $reply->comment,
                            'formatted_comment' => $reply->formatted_comment,
                            'body_parsed' => $reply->body_parsed,
                            'user' => $reply->user,
                            'created_at' => $reply->created_at,
                            'is_edited' => $reply->is_edited,
                            'edited_at' => $reply->edited_at,
                            'reactions' => $reply->getReactionSummary(),
                            'user_reaction' => $reply->getUserReaction(auth()->user()),
                            'mentions' => $reply->mentions,
                        ];
                    }),
                    'reactions' => $comment->getReactionSummary(),
                    'user_reaction' => $comment->getUserReaction(auth()->user()),
                    'mentions' => $comment->mentions,
                    'mentioned_users' => $comment->getMentioned(),
                    'attachments' => $comment->attachments ?? [],
                    'file_shares' => $comment->fileShares,
                ];
            })
            ->toArray();
    }

    public function addComment()
    {
        $this->validate([
            'newComment' => 'required|string|min:1',
            'attachments.*' => 'file|max:5120', // 5MB max
        ]);

        $comment = TaskComment::create([
            'task_id' => $this->task->id,
            'user_id' => auth()->id(),
            'comment' => $this->newComment,
            'attachments' => $this->processAttachments(),
        ]);

        // Process mentions
        $comment->processMentions();

        // Log activity
        TeamActivityFeed::logCommentAdded($comment);

        $this->newComment = '';
        $this->attachments = [];
        $this->loadComments();

        $this->dispatch('commentAdded');

        session()->flash('message', 'Comment added successfully!');
    }

    public function addReply($commentId)
    {
        $this->validate([
            'replyComment' => 'required|string|min:1',
        ]);

        $parentComment = TaskComment::find($commentId);

        $reply = TaskComment::create([
            'task_id' => $this->task->id,
            'user_id' => auth()->id(),
            'comment' => $this->replyComment,
            'parent_id' => $commentId,
        ]);

        // Process mentions
        $reply->processMentions();

        // Log activity
        TeamActivityFeed::logActivity(
            'comment_replied',
            $reply,
            auth()->user()->name . " replied to a comment in task: {$this->task->name}"
        );

        $this->replyComment = '';
        $this->replyingTo = null;
        $this->loadComments();

        session()->flash('message', 'Reply added successfully!');
    }

    public function startReply($commentId)
    {
        $this->replyingTo = $commentId;
        $this->replyComment = '';
    }

    public function cancelReply()
    {
        $this->replyingTo = null;
        $this->replyComment = '';
    }

    public function startEdit($commentId)
    {
        $comment = TaskComment::find($commentId);

        if ($comment->user_id !== auth()->id()) {
            return;
        }

        $this->editingComment = $commentId;
        $this->editComment = $comment->comment;
    }

    public function saveEdit($commentId)
    {
        $this->validate([
            'editComment' => 'required|string|min:1',
        ]);

        $comment = TaskComment::find($commentId);

        if ($comment->user_id !== auth()->id()) {
            return;
        }

        $originalComment = $comment->comment;
        $comment->update(['comment' => $this->editComment]);
        $comment->markAsEdited($originalComment);
        $comment->processMentions();

        $this->editingComment = null;
        $this->editComment = '';
        $this->loadComments();

        session()->flash('message', 'Comment updated successfully!');
    }

    public function cancelEdit()
    {
        $this->editingComment = null;
        $this->editComment = '';
    }

    public function deleteComment($commentId)
    {
        $comment = TaskComment::find($commentId);

        if ($comment->user_id !== auth()->id()) {
            return;
        }

        $comment->delete();
        $this->loadComments();

        session()->flash('message', 'Comment deleted successfully!');
    }

    public function toggleReaction($commentId, $reactionType)
    {
        $comment = TaskComment::find($commentId);
        $user = auth()->user();

        CommentReaction::toggleReaction($comment, $user, $reactionType);

        $this->loadComments();
        $this->dispatch('reactionToggled');
    }

    public function toggleEmojiPicker($commentId)
    {
        if (isset($this->showEmojiPicker[$commentId])) {
            unset($this->showEmojiPicker[$commentId]);
        } else {
            $this->showEmojiPicker = [$commentId => true];
        }
    }

    public function searchMentions($query)
    {
        if (strlen($query) < 1) {
            $this->mentionSuggestions = [];
            $this->showMentions = false;
            return;
        }

        try {
            // Get all mentionable users for this task context
            $mentionableUsers = $this->getMentionableUsers();

            $this->mentionSuggestions = $mentionableUsers
                ->filter(function ($user) use ($query) {
                    return $user && (
                        stripos($user->name, $query) !== false ||
                        stripos($user->email, $query) !== false
                    );
                })
                ->take(8) // Show more suggestions
                ->map(function ($user) {
                    return [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'avatar' => $this->getUserAvatar($user),
                        'role' => $this->getUserRole($user),
                    ];
                })
                ->values()
                ->toArray();

            $this->showMentions = !empty($this->mentionSuggestions);
        } catch (\Exception $e) {
            $this->mentionSuggestions = [];
            $this->showMentions = false;
        }
    }

    /**
     * Get all users that can be mentioned in this task context
     */
    private function getMentionableUsers()
    {
        $users = collect();

        // Get project members (employees with their users)
        if ($this->task->project && $this->task->project->members) {
            $projectMembers = $this->task->project->members()->with('user')->get()
                ->map(function ($employee) {
                    return $employee->user;
                })
                ->filter(); // Remove null users
            $users = $users->merge($projectMembers);
        }

        // Get task-related users
        $taskUsers = collect([
            $this->task->assignedUser,
            $this->task->creator
        ])->filter();
        $users = $users->merge($taskUsers);

        // Get users who have commented on this task
        $commenters = TaskComment::where('task_id', $this->task->id)
            ->with('user')
            ->get()
            ->pluck('user')
            ->filter();
        $users = $users->merge($commenters);

        // Return unique users, excluding current user
        return $users->unique('id')->reject(function ($user) {
            return $user && $user->id === auth()->id();
        });
    }

    /**
     * Get user avatar URL or generate initials
     */
    private function getUserAvatar($user): string
    {
        // If user has avatar method, use it
        if (method_exists($user, 'getFilamentAvatarUrl')) {
            $avatar = $user->getFilamentAvatarUrl();
            if ($avatar) {
                return $avatar;
            }
        }

        // Generate initials avatar
        $name = str($user->name)
            ->trim()
            ->explode(' ')
            ->map(fn(string $segment): string => filled($segment) ? mb_substr($segment, 0, 1) : '')
            ->join(' ');

        return 'https://ui-avatars.com/api/?name=' . urlencode($name) . '&color=FFFFFF&background=3B82F6';
    }

    /**
     * Get user role in the context of this task
     */
    private function getUserRole($user): string
    {
        if ($this->task->assignedUser && $this->task->assignedUser->id === $user->id) {
            return 'Assigned';
        }

        if ($this->task->creator && $this->task->creator->id === $user->id) {
            return 'Creator';
        }

        if ($this->task->project && $this->task->project->members()->whereHas('user', function ($query) use ($user) {
            $query->where('id', $user->id);
        })->exists()) {
            return 'Project Member';
        }

        return 'Commenter';
    }

    public function insertMention($userName)
    {
        // Find the last @ mention in the comment
        $lastMention = $this->extractLastMention();

        // Replace the incomplete mention with the complete one
        if ($lastMention !== '') {
            $this->newComment = preg_replace('/@' . preg_quote($lastMention) . '$/', '@' . $userName . ' ', $this->newComment);
        } else {
            // If no partial mention found, just append
            $this->newComment = rtrim($this->newComment) . ' @' . $userName . ' ';
        }

        $this->mentionSuggestions = [];
        $this->showMentions = false;
    }

    private function extractLastMention()
    {
        preg_match('/@(\w*)$/', $this->newComment, $matches);
        return $matches[1] ?? '';
    }

    private function processAttachments(): ?array
    {
        if (empty($this->attachments)) {
            return null;
        }

        $processedAttachments = [];

        foreach ($this->attachments as $attachment) {
            $path = $attachment->store('task-comments', 'public');

            $processedAttachments[] = [
                'name' => $attachment->getClientOriginalName(),
                'path' => $path,
                'size' => $attachment->getSize(),
                'mime_type' => $attachment->getMimeType(),
            ];
        }

        return $processedAttachments;
    }

    public function removeAttachment($index)
    {
        unset($this->attachments[$index]);
        $this->attachments = array_values($this->attachments);
    }

    public function render()
    {
        return view('livewire.enhanced-comments', [
            'availableReactions' => CommentReaction::getAvailableReactions(),
        ]);
    }
}
