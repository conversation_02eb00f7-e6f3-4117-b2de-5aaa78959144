<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\User;
use App\Models\Project;

class MentionTextarea extends Component
{
    public $value = '';
    public $placeholder = 'Type your message... Use @username to mention someone';
    public $rows = 3;
    public $name = 'content';
    public $label = '';
    public $required = false;

    // Mention functionality
    public $mentionSuggestions = [];
    public $showMentions = false;

    // Context for determining available users
    public $projectId = null;
    public $contextType = 'general'; // general, project, task
    public $contextId = null;

    protected $listeners = ['updateValue'];

    public function mount($value = '', $placeholder = null, $rows = 3, $name = 'content', $label = '', $required = false, $projectId = null, $contextType = 'general', $contextId = null)
    {
        $this->value = $value;
        $this->placeholder = $placeholder ?? $this->placeholder;
        $this->rows = $rows;
        $this->name = $name;
        $this->label = $label;
        $this->required = $required;
        $this->projectId = $projectId;
        $this->contextType = $contextType;
        $this->contextId = $contextId;
    }

    public function updatedValue()
    {
        $this->dispatch('mention-textarea-updated', [
            'name' => $this->name,
            'value' => $this->value
        ]);
    }

    public function searchMentions($query)
    {
        if (strlen($query) < 1) {
            $this->mentionSuggestions = [];
            $this->showMentions = false;
            return;
        }

        try {
            $users = collect();

            // Get users based on context
            switch ($this->contextType) {
                case 'project':
                    if ($this->projectId) {
                        $project = Project::with('members.user')->find($this->projectId);
                        if ($project) {
                            $users = $project->members->map(fn($member) => $member->user)->filter();
                        }
                    }
                    break;

                case 'task':
                    if ($this->contextId) {
                        $task = \App\Models\Task::with('project.members.user', 'assignedUser', 'creator')->find($this->contextId);
                        if ($task) {
                            $projectMembers = $task->project->members->map(fn($member) => $member->user)->filter();
                            $taskUsers = collect([$task->assignedUser, $task->creator])->filter();
                            $users = $projectMembers->merge($taskUsers)->unique('id');
                        }
                    }
                    break;

                default: // general
                    // Get all active users
                    $users = User::where('is_active', true)->get();
                    break;
            }

            $this->mentionSuggestions = $users
                ->filter(function ($user) use ($query) {
                    return $user && stripos($user->name, $query) !== false;
                })
                ->take(5)
                ->map(function ($user) {
                    return [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                    ];
                })
                ->values()
                ->toArray();

            $this->showMentions = !empty($this->mentionSuggestions);
        } catch (\Exception $e) {
            $this->mentionSuggestions = [];
            $this->showMentions = false;
        }
    }

    public function insertMention($userName)
    {
        // Find the last @ mention in the text
        $lastMention = $this->extractLastMention();

        // Replace the incomplete mention with the complete one
        if ($lastMention !== '') {
            $this->value = preg_replace('/@' . preg_quote($lastMention) . '$/', '@' . $userName . ' ', $this->value);
        } else {
            // If no partial mention found, just append
            $this->value = rtrim($this->value) . ' @' . $userName . ' ';
        }

        $this->mentionSuggestions = [];
        $this->showMentions = false;

        // Trigger update event
        $this->updatedValue();
    }

    private function extractLastMention()
    {
        preg_match('/@(\w*)$/', $this->value, $matches);
        return $matches[1] ?? '';
    }

    public function render()
    {
        return view('livewire.mention-textarea');
    }
}
