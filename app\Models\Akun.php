<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Akun extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'akun';

    protected $fillable = [
        'kode_akun',
        'nama_akun',
        'kategori_akun',
        'tipe_akun',
        'saldo_awal',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    protected $casts = [
        'saldo_awal' => 'decimal:2',
    ];

    // Relationships
    public function journalEntries()
    {
        return $this->hasMany(JournalEntry::class, 'account_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Helper methods
    public function getSaldoAkhirAttribute()
    {
        $totalDebit = $this->journalEntries()->sum('debit');
        $totalCredit = $this->journalEntries()->sum('credit');

        // Untuk akun debit (Aset, Beban), saldo = saldo_awal + debit - kredit
        // Untuk akun kredit (Kewajiban, Ekuitas, Pendapatan), saldo = saldo_awal + kredit - debit
        if ($this->tipe_akun === 'Debit') {
            return $this->saldo_awal + $totalDebit - $totalCredit;
        } else {
            return $this->saldo_awal + $totalCredit - $totalDebit;
        }
    }

    // Scopes
    public function scopeByKategori($query, $kategori)
    {
        return $query->where('kategori_akun', $kategori);
    }

    public function scopeByTipe($query, $tipe)
    {
        return $query->where('tipe_akun', $tipe);
    }
}
