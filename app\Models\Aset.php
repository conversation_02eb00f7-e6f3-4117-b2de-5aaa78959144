<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Aset extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'aset';

    protected $fillable = [
        'kode_aset',
        'nama_aset',
        'deskripsi_aset',
        'kategori_aset',
        'nilai_aset',
        'tanggal_akuisisi',
        'status_aset',
        'id_akun',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    // Relationships
    public function akun()
    {
        return $this->belongsTo(Akun::class, 'id_akun');
    }
}
