<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AturanKeterlambatan extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'aturan_keterlambatan';

    protected $fillable = [
        'nama_aturan',
        'menit_dari',
        'menit_sampai',
        'denda_nominal',
        'jenis_denda',
        'persentase_denda',
        'denda_per_menit',
        'is_active',
        'keterangan',
        'created_by',
    ];

    protected $casts = [
        'denda_nominal' => 'decimal:2',
        'persentase_denda' => 'decimal:2',
        'denda_per_menit' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * <PERSON><PERSON>i ke User yang membuat
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Hitung denda keterlambatan berdasarkan menit terlambat dan gaji
     */
    public function hitungDenda($menitTerlambat, $gajiPokok = 0)
    {
        // Cek apakah menit terlambat masuk dalam range aturan ini
        if ($menitTerlambat < $this->menit_dari) {
            return 0;
        }

        if ($this->menit_sampai && $menitTerlambat > $this->menit_sampai) {
            return 0;
        }

        switch ($this->jenis_denda) {
            case 'nominal_tetap':
                return $this->denda_nominal;

            case 'per_menit':
                return $menitTerlambat * $this->denda_per_menit;

            case 'persentase_gaji':
                if ($gajiPokok > 0) {
                    return ($gajiPokok * $this->persentase_denda) / 100;
                }
                return 0;

            default:
                return 0;
        }
    }

    /**
     * Cek apakah menit terlambat masuk dalam range aturan ini
     */
    public function isApplicable($menitTerlambat)
    {
        if ($menitTerlambat < $this->menit_dari) {
            return false;
        }

        if ($this->menit_sampai && $menitTerlambat > $this->menit_sampai) {
            return false;
        }

        return true;
    }

    /**
     * Scope untuk aturan aktif
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope untuk mencari aturan berdasarkan menit terlambat
     */
    public function scopeForLateness($query, $menitTerlambat)
    {
        return $query->where('menit_dari', '<=', $menitTerlambat)
                    ->where(function ($q) use ($menitTerlambat) {
                        $q->whereNull('menit_sampai')
                          ->orWhere('menit_sampai', '>=', $menitTerlambat);
                    })
                    ->orderBy('menit_dari', 'desc');
    }

    /**
     * Accessor untuk format range waktu
     */
    public function getRangeWaktuAttribute()
    {
        if ($this->menit_sampai) {
            return $this->menit_dari . ' - ' . $this->menit_sampai . ' menit';
        }

        return '≥ ' . $this->menit_dari . ' menit';
    }

    /**
     * Accessor untuk format denda
     */
    public function getFormattedDendaAttribute()
    {
        switch ($this->jenis_denda) {
            case 'nominal_tetap':
                return 'Rp ' . number_format($this->denda_nominal, 0, ',', '.');

            case 'per_menit':
                return 'Rp ' . number_format($this->denda_per_menit, 0, ',', '.') . ' per menit';

            case 'persentase_gaji':
                return $this->persentase_denda . '% dari gaji pokok';

            default:
                return '-';
        }
    }

    /**
     * Static method untuk mencari aturan yang berlaku untuk keterlambatan tertentu
     */
    public static function findApplicableRule($menitTerlambat)
    {
        return static::active()
                    ->forLateness($menitTerlambat)
                    ->first();
    }
}
