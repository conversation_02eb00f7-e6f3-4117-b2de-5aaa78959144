<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AturanLembur extends Model
{
    use HasFactory;

    protected $table = 'aturan_lemburs';

    protected $fillable = [
        'jenis_lembur_id',
        'jam_mulai',
        'jam_selesai',
        'multiplier',
        'keterangan',
        'is_active',
        'urutan',
    ];

    protected $casts = [
        'jam_mulai' => 'decimal:2',
        'jam_selesai' => 'decimal:2',
        'multiplier' => 'decimal:2',
        'is_active' => 'boolean',
        'urutan' => 'integer',
    ];

    /**
     * Relasi ke JenisLembur
     */
    public function jenisLembur()
    {
        return $this->belongsTo(JenisLembur::class, 'jenis_lembur_id');
    }

    /**
     * Scope untuk aturan aktif
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope untuk aturan per jam
     */
    public function scopePerJam($query)
    {
        return $query->where('tipe_perhitungan', 'per_jam');
    }

    /**
     * Scope untuk aturan per hari
     */
    public function scopePerHari($query)
    {
        return $query->where('tipe_perhitungan', 'per_hari');
    }

    /**
     * Cari aturan lembur yang berlaku untuk jam tertentu (hari biasa)
     */
    public static function findApplicableRuleForHours($jamLembur)
    {
        return static::active()
            ->perJam()
            ->where('nama_jenis_lembur', 'LIKE', '%hari biasa%')
            ->where('jam_mulai', '<=', $jamLembur)
            ->where(function ($query) use ($jamLembur) {
                $query->where('jam_selesai', '>=', $jamLembur)
                    ->orWhereNull('jam_selesai');
            })
            ->orderBy('urutan')
            ->first();
    }

    /**
     * Cari aturan lembur yang berlaku untuk jam tertentu (hari libur)
     */
    public static function findApplicableRuleForHoliday($jamLembur)
    {
        return static::active()
            ->perJam()
            ->where('nama_jenis_lembur', 'LIKE', '%hari libur%')
            ->where('jam_mulai', '<=', $jamLembur)
            ->where(function ($query) use ($jamLembur) {
                $query->where('jam_selesai', '>=', $jamLembur)
                    ->orWhereNull('jam_selesai');
            })
            ->orderBy('urutan')
            ->first();
    }

    /**
     * Cari aturan lembur per hari berdasarkan jenis
     */
    public static function findDailyRule($jenisHari)
    {
        $namaJenis = match ($jenisHari) {
            'hari_kerja' => 'Lembur HK (Hari Kerja)',
            'tanggal_merah' => 'Lembur HK (Tanggal Merah)',
            default => null
        };

        if (!$namaJenis) {
            return null;
        }

        return static::active()
            ->perHari()
            ->where('nama_jenis_lembur', $namaJenis)
            ->first();
    }

    /**
     * Hitung upah lembur berdasarkan aturan
     */
    public function hitungUpahLembur($jamLembur, $upahBulanan)
    {
        if ($this->tipe_perhitungan === 'per_hari') {
            // Untuk lembur per hari
            $upahHarian = $upahBulanan / $this->pembagi_upah_bulanan;
            return $upahHarian * $this->multiplier;
        } else {
            // Untuk lembur per jam
            $upahJam = $upahBulanan / ($this->pembagi_upah_bulanan * 8); // Asumsi 8 jam kerja per hari
            return $upahJam * $jamLembur * $this->multiplier;
        }
    }

    /**
     * Get all rules untuk hari biasa berurutan
     */
    public static function getWeekdayRules()
    {
        return static::active()
            ->perJam()
            ->where('nama_jenis_lembur', 'LIKE', '%hari biasa%')
            ->orderBy('urutan')
            ->get();
    }

    /**
     * Get all rules untuk hari libur berurutan
     */
    public static function getHolidayRules()
    {
        return static::active()
            ->perJam()
            ->where('nama_jenis_lembur', 'LIKE', '%hari libur%')
            ->orderBy('urutan')
            ->get();
    }

    /**
     * Accessor untuk format multiplier
     */
    public function getFormattedMultiplierAttribute()
    {
        return $this->multiplier . 'x';
    }

    /**
     * Accessor untuk format jam range
     */
    public function getJamRangeAttribute()
    {
        if ($this->tipe_perhitungan === 'per_hari') {
            return 'Per Hari';
        }

        if ($this->jam_selesai) {
            return "Jam {$this->jam_mulai} - {$this->jam_selesai}";
        }

        return "Jam {$this->jam_mulai}+";
    }
}
