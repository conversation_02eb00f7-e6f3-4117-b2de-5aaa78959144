<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BpjsRate extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'bpjs_rates';

    protected $fillable = [
        'bpjs_type',
        'participant_type',
        'rate_percentage',
        'max_salary_base',
        'min_salary_base',
        'is_active',
        'effective_date',
        'end_date',
        'description',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'effective_date', 'end_date'];

    protected $casts = [
        'rate_percentage' => 'decimal:2',
        'max_salary_base' => 'decimal:2',
        'min_salary_base' => 'decimal:2',
        'is_active' => 'boolean',
        'effective_date' => 'date',
        'end_date' => 'date',
    ];

    // Relationships
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('bpjs_type', $type);
    }

    public function scopeByParticipant($query, $participant)
    {
        return $query->where('participant_type', $participant);
    }

    public function scopeEffectiveOn($query, $date)
    {
        return $query->where('effective_date', '<=', $date)
                    ->where(function ($q) use ($date) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', $date);
                    });
    }

    // Helper methods
    public function getFormattedRatePercentageAttribute()
    {
        return $this->rate_percentage . '%';
    }

    public function getFormattedMaxSalaryBaseAttribute()
    {
        return $this->max_salary_base ? 'Rp ' . number_format($this->max_salary_base, 0, ',', '.') : 'No Limit';
    }

    public function getFormattedMinSalaryBaseAttribute()
    {
        return 'Rp ' . number_format($this->min_salary_base, 0, ',', '.');
    }

    public function getDisplayNameAttribute()
    {
        return $this->bpjs_type . ' - ' . $this->participant_type . ' (' . $this->formatted_rate_percentage . ')';
    }

    public function getBpjsTypeLabelAttribute()
    {
        return match($this->bpjs_type) {
            'Kesehatan' => 'BPJS Kesehatan',
            'Ketenagakerjaan' => 'BPJS Ketenagakerjaan',
            default => $this->bpjs_type
        };
    }

    public function getParticipantTypeLabelAttribute()
    {
        return match($this->participant_type) {
            'Pekerja' => 'Pekerja',
            'Perusahaan' => 'Perusahaan',
            default => $this->participant_type
        };
    }

    public function getParticipantTypeColorAttribute()
    {
        return match($this->participant_type) {
            'Pekerja' => 'info',
            'Perusahaan' => 'warning',
            default => 'gray'
        };
    }

    public function calculateContribution($salaryBase)
    {
        // Apply min/max salary base limits
        $adjustedSalary = $salaryBase;
        
        if ($this->min_salary_base && $adjustedSalary < $this->min_salary_base) {
            $adjustedSalary = $this->min_salary_base;
        }
        
        if ($this->max_salary_base && $adjustedSalary > $this->max_salary_base) {
            $adjustedSalary = $this->max_salary_base;
        }
        
        return ($adjustedSalary * $this->rate_percentage) / 100;
    }

    public function isApplicableFor($salaryBase)
    {
        if ($this->min_salary_base && $salaryBase < $this->min_salary_base) {
            return false;
        }
        
        return true; // Max salary base is handled in calculation, not eligibility
    }

    // Static methods
    public static function getActiveRates($bpjsType = null, $participantType = null, $date = null)
    {
        $date = $date ?: now();
        
        $query = static::active()->effectiveOn($date);
        
        if ($bpjsType) {
            $query->byType($bpjsType);
        }
        
        if ($participantType) {
            $query->byParticipant($participantType);
        }
        
        return $query->get();
    }

    public static function getEmployeeRate($bpjsType, $date = null)
    {
        return static::getActiveRates($bpjsType, 'Pekerja', $date)->first();
    }

    public static function getCompanyRate($bpjsType, $date = null)
    {
        return static::getActiveRates($bpjsType, 'Perusahaan', $date)->first();
    }

    public static function calculateEmployeeBpjs($salaryBase, $bpjsType, $date = null)
    {
        $rate = static::getEmployeeRate($bpjsType, $date);
        return $rate ? $rate->calculateContribution($salaryBase) : 0;
    }

    public static function calculateCompanyBpjs($salaryBase, $bpjsType, $date = null)
    {
        $rate = static::getCompanyRate($bpjsType, $date);
        return $rate ? $rate->calculateContribution($salaryBase) : 0;
    }
}
