<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class CashDisbursement extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'cash_disbursements';

    protected $fillable = [
        'disbursement_number',
        'disbursement_date',
        'expense_request_id',
        'payee_employee_id',
        'payee_name',
        'amount',
        'payment_method',
        'cash_account_id',
        'reference_number',
        'status',
        'purpose',
        'notes',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    protected $dates = ['deleted_at', 'disbursement_date', 'approved_at'];

    protected $casts = [
        'disbursement_date' => 'date',
        'amount' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    // Relationships
    public function expenseRequest()
    {
        return $this->belongsTo(ExpenseRequest::class);
    }

    public function payeeEmployee()
    {
        return $this->belongsTo(Karyawan::class, 'payee_employee_id');
    }

    public function cashAccount()
    {
        return $this->belongsTo(Akun::class, 'cash_account_id');
    }

    public function cashDisbursementItems()
    {
        return $this->hasMany(CashDisbursementItem::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPaymentMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'Completed');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'Draft');
    }

    // Helper methods
    public function getTotalItemsAttribute()
    {
        return $this->cashDisbursementItems()->count();
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'Draft' => 'gray',
            'Completed' => 'success',
            'Cancelled' => 'danger',
            default => 'gray'
        };
    }

    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'Draft' => 'Draft',
            'Completed' => 'Selesai',
            'Cancelled' => 'Dibatalkan',
            default => $this->status
        };
    }

    public function getPaymentMethodLabelAttribute()
    {
        return match($this->payment_method) {
            'Cash' => 'Tunai',
            'Bank_Transfer' => 'Transfer Bank',
            'Check' => 'Cek',
            default => $this->payment_method
        };
    }

    public function getPaymentMethodColorAttribute()
    {
        return match($this->payment_method) {
            'Cash' => 'success',
            'Bank_Transfer' => 'info',
            'Check' => 'warning',
            default => 'gray'
        };
    }

    public function getPayeeNameAttribute($value)
    {
        if ($value) return $value;
        
        return $this->payeeEmployee ? $this->payeeEmployee->nama_lengkap : null;
    }

    public function isEditable()
    {
        return $this->status === 'Draft';
    }

    public function canBeCompleted()
    {
        return $this->status === 'Draft' && $this->amount > 0;
    }

    public function canBeCancelled()
    {
        return $this->status === 'Draft';
    }

    public function complete()
    {
        if (!$this->canBeCompleted()) {
            throw new \Exception('Cash disbursement cannot be completed');
        }

        $this->status = 'Completed';
        $this->approved_by = auth()->id();
        $this->approved_at = Carbon::now();
        $this->save();

        // Update expense request status if linked
        if ($this->expenseRequest) {
            $this->expenseRequest->status = 'Paid';
            $this->expenseRequest->save();
        }

        // Create journal entry
        $this->createJournalEntry();
    }

    protected function createJournalEntry()
    {
        // This will be implemented when we create the PostingRuleEngine
        // Logic: Dr. Expense Accounts (per item), Cr. Cash/Bank Account
        // Will be handled by PostingRuleEngine based on source_type = 'CashDisbursement'
    }

    public function getFormattedDisbursementDateAttribute()
    {
        return $this->disbursement_date ? $this->disbursement_date->format('d/m/Y') : null;
    }

    public function getFormattedAmountAttribute()
    {
        return 'Rp ' . number_format($this->amount, 0, ',', '.');
    }

    public function isFromExpenseRequest()
    {
        return $this->expense_request_id !== null;
    }

    public function getDisbursementTypeAttribute()
    {
        return $this->isFromExpenseRequest() ? 'From Expense Request' : 'Direct Disbursement';
    }

    public function getDisbursementTypeColorAttribute()
    {
        return $this->isFromExpenseRequest() ? 'success' : 'info';
    }

    // Auto-generate disbursement number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($disbursement) {
            if (empty($disbursement->disbursement_number)) {
                $disbursement->disbursement_number = static::generateDisbursementNumber();
            }
        });
    }

    public static function generateDisbursementNumber()
    {
        $prefix = 'CD';
        $date = Carbon::now()->format('Ymd');
        $lastDisbursement = static::whereDate('created_at', Carbon::today())
                                 ->where('disbursement_number', 'like', $prefix . $date . '%')
                                 ->orderBy('disbursement_number', 'desc')
                                 ->first();

        if ($lastDisbursement) {
            $lastNumber = intval(substr($lastDisbursement->disbursement_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . $date . $newNumber;
    }
}
