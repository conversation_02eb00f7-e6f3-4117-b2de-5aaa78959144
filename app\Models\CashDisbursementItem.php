<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CashDisbursementItem extends Model
{
    use HasFactory;

    protected $table = 'cash_disbursement_items';

    protected $fillable = [
        'cash_disbursement_id',
        'expense_category_id',
        'description',
        'amount',
        'account_id',
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
    ];

    // Relationships
    public function cashDisbursement()
    {
        return $this->belongsTo(CashDisbursement::class);
    }

    public function expenseCategory()
    {
        return $this->belongsTo(ExpenseCategory::class);
    }

    public function account()
    {
        return $this->belongsTo(Akun::class, 'account_id');
    }

    // Helper methods
    public function getFormattedAmountAttribute()
    {
        return 'Rp ' . number_format($this->amount, 0, ',', '.');
    }

    public function getCategoryNameAttribute()
    {
        return $this->expenseCategory ? $this->expenseCategory->name : 'Uncategorized';
    }

    public function getAccountNameAttribute()
    {
        return $this->account ? $this->account->nama_akun : 'No Account';
    }
}
