<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class City extends Model
{
    use HasFactory;

    protected $fillable = [
        'province_id',
        'code',
        'name',
        'slug',
        'type',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Boot method untuk auto-generate slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($city) {
            if (empty($city->slug)) {
                $city->slug = Str::slug($city->name);
            }
        });

        static::updating(function ($city) {
            if ($city->isDirty('name') && empty($city->slug)) {
                $city->slug = Str::slug($city->name);
            }
        });
    }

    /**
     * Relasi ke Province
     */
    public function province()
    {
        return $this->belongsTo(Province::class);
    }

    /**
     * Relasi ke District
     */
    public function districts()
    {
        return $this->hasMany(District::class);
    }

    /**
     * Relasi ke Village melalui District
     */
    public function villages()
    {
        return $this->hasMany(Village::class);
    }

    /**
     * Relasi ke Customer
     */
    public function customers()
    {
        return $this->hasMany(Customer::class);
    }

    /**
     * Scope untuk city aktif
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope untuk filter berdasarkan provinsi
     */
    public function scopeByProvince($query, $provinceId)
    {
        return $query->where('province_id', $provinceId);
    }

    /**
     * Scope untuk filter berdasarkan tipe
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get formatted name dengan kode dan tipe
     */
    public function getFormattedNameAttribute(): string
    {
        $typeLabel = $this->type === 'kabupaten' ? 'Kab.' : 'Kota';
        return $this->code . ' - ' . $typeLabel . ' ' . $this->name;
    }

    /**
     * Get type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match ($this->type) {
            'kabupaten' => 'Kabupaten',
            'kota' => 'Kota',
            default => $this->type,
        };
    }

    /**
     * Get full name dengan provinsi
     */
    public function getFullNameAttribute(): string
    {
        return $this->formatted_name . ', ' . $this->province->name;
    }

    /**
     * Get total districts count
     */
    public function getDistrictsCountAttribute(): int
    {
        return $this->districts()->count();
    }

    /**
     * Get total villages count
     */
    public function getVillagesCountAttribute(): int
    {
        return $this->villages()->count();
    }

    /**
     * Get route key name untuk URL
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
