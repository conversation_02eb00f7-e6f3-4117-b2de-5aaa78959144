<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CommentReaction extends Model
{
    use HasFactory;
    protected $fillable = [
        'comment_id',
        'user_id',
        'reaction_type',
    ];

    public function comment(): BelongsTo
    {
        return $this->belongsTo(TaskComment::class, 'comment_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public static function getAvailableReactions(): array
    {
        return [
            'like' => [
                'emoji' => '👍',
                'label' => 'Like',
                'color' => 'text-blue-600',
                'bg_color' => 'bg-blue-50 dark:bg-blue-900/20',
                'border_color' => 'border-blue-200 dark:border-blue-700',
            ],
            'love' => [
                'emoji' => '❤️',
                'label' => 'Love',
                'color' => 'text-red-600',
                'bg_color' => 'bg-red-50 dark:bg-red-900/20',
                'border_color' => 'border-red-200 dark:border-red-700',
            ],
            'laugh' => [
                'emoji' => '😂',
                'label' => 'Laugh',
                'color' => 'text-yellow-600',
                'bg_color' => 'bg-yellow-50 dark:bg-yellow-900/20',
                'border_color' => 'border-yellow-200 dark:border-yellow-700',
            ],
            'wow' => [
                'emoji' => '😮',
                'label' => 'Wow',
                'color' => 'text-purple-600',
                'bg_color' => 'bg-purple-50 dark:bg-purple-900/20',
                'border_color' => 'border-purple-200 dark:border-purple-700',
            ],
            'celebrate' => [
                'emoji' => '🎉',
                'label' => 'Celebrate',
                'color' => 'text-green-600',
                'bg_color' => 'bg-green-50 dark:bg-green-900/20',
                'border_color' => 'border-green-200 dark:border-green-700',
            ],
            'thinking' => [
                'emoji' => '🤔',
                'label' => 'Thinking',
                'color' => 'text-gray-600',
                'bg_color' => 'bg-gray-50 dark:bg-gray-900/20',
                'border_color' => 'border-gray-200 dark:border-gray-700',
            ],
        ];
    }

    public function getReactionDataAttribute(): array
    {
        $reactions = self::getAvailableReactions();
        return $reactions[$this->reaction_type] ?? [
            'emoji' => '👍',
            'label' => 'Like',
            'color' => 'text-blue-600',
        ];
    }

    public static function toggleReaction(TaskComment $comment, User $user, string $reactionType): bool
    {
        $existing = self::where('comment_id', $comment->id)
            ->where('user_id', $user->id)
            ->where('reaction_type', $reactionType)
            ->first();

        if ($existing) {
            $existing->delete();
            return false; // Reaction removed
        } else {
            // Remove any other reaction from this user on this comment
            self::where('comment_id', $comment->id)
                ->where('user_id', $user->id)
                ->delete();

            // Add new reaction
            self::create([
                'comment_id' => $comment->id,
                'user_id' => $user->id,
                'reaction_type' => $reactionType,
            ]);
            return true; // Reaction added
        }
    }

    public static function getReactionSummary(TaskComment $comment): array
    {
        $reactions = self::where('comment_id', $comment->id)
            ->selectRaw('reaction_type, COUNT(*) as count')
            ->groupBy('reaction_type')
            ->get()
            ->keyBy('reaction_type');

        $availableReactions = self::getAvailableReactions();
        $summary = [];

        foreach ($availableReactions as $type => $data) {
            if (isset($reactions[$type])) {
                $summary[$type] = [
                    'count' => $reactions[$type]->count,
                    'emoji' => $data['emoji'],
                    'label' => $data['label'],
                    'color' => $data['color'],
                ];
            }
        }

        return $summary;
    }

    public static function getUserReaction(TaskComment $comment, User $user): ?string
    {
        $reaction = self::where('comment_id', $comment->id)
            ->where('user_id', $user->id)
            ->first();

        return $reaction?->reaction_type;
    }
}
