<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanySettings extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'value' => 'json',
    ];

    /**
     * Get setting value by key
     */
    public static function get(string $key, $default = null)
    {
        $setting = static::where('key', $key)->where('is_active', true)->first();

        if (!$setting) {
            return $default;
        }

        return $setting->value;
    }

    /**
     * Set setting value by key
     */
    public static function set(string $key, $value, string $type = 'string', ?string $description = null): self
    {
        return static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'description' => $description,
                'is_active' => true,
            ]
        );
    }

    /**
     * Get work period start date (hari pertama kerja)
     */
    public static function getWorkPeriodStartDate(): int
    {
        return static::get('work_period_start_date', 21);
    }

    /**
     * Get work period end date (cut-off date)
     */
    public static function getWorkPeriodEndDate(): int
    {
        return static::get('work_period_end_date', 20);
    }

    /**
     * Get current work period start date
     */
    public static function getCurrentWorkPeriodStartDate(): \Carbon\Carbon
    {
        $startDate = static::getWorkPeriodStartDate();
        $endDate = static::getWorkPeriodEndDate();
        $now = now();

        // Jika hari ini >= hari pertama kerja, maka periode dimulai bulan ini
        if ($now->day >= $startDate) {
            return $now->copy()->day($startDate);
        } else {
            // Jika hari ini < hari pertama kerja, maka periode dimulai bulan lalu
            return $now->copy()->subMonth()->day($startDate);
        }
    }

    /**
     * Get current work period end date
     */
    public static function getCurrentWorkPeriodEndDate(): \Carbon\Carbon
    {
        $startDate = static::getWorkPeriodStartDate();
        $endDate = static::getWorkPeriodEndDate();
        $now = now();

        // Jika hari ini >= hari pertama kerja, maka periode berakhir bulan depan
        if ($now->day >= $startDate) {
            return $now->copy()->addMonth()->day($endDate);
        } else {
            // Jika hari ini < hari pertama kerja, maka periode berakhir bulan ini
            return $now->copy()->day($endDate);
        }
    }

    /**
     * Get work period for specific month/year
     */
    public static function getWorkPeriod(int $month, int $year): array
    {
        $startDate = static::getWorkPeriodStartDate();
        $endDate = static::getWorkPeriodEndDate();

        // Period starts from work start date of current month
        $periodStart = \Carbon\Carbon::create($year, $month, $startDate);

        // Period ends on work end date of next month
        $periodEnd = \Carbon\Carbon::create($year, $month, $endDate)->addMonth();

        return [
            'start' => $periodStart,
            'end' => $periodEnd,
        ];
    }

    /**
     * Get available work periods for dropdown
     */
    public static function getAvailableWorkPeriods(int $monthsBack = 12): array
    {
        $periods = [];

        for ($i = 0; $i < $monthsBack; $i++) {
            $date = now()->subMonths($i);
            $period = static::getWorkPeriod($date->month, $date->year);

            $key = $date->format('Y-m');
            $label = $period['start']->format('d M Y') . ' - ' . $period['end']->format('d M Y');

            $periods[$key] = $label;
        }

        return $periods;
    }

    /**
     * Get work period info for display
     */
    public static function getWorkPeriodInfo(): array
    {
        $startDate = static::getWorkPeriodStartDate();
        $endDate = static::getWorkPeriodEndDate();
        $currentStart = static::getCurrentWorkPeriodStartDate();
        $currentEnd = static::getCurrentWorkPeriodEndDate();

        return [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'current_start' => $currentStart,
            'current_end' => $currentEnd,
            'period_label' => $currentStart->format('d M Y') . ' - ' . $currentEnd->format('d M Y'),
            'description' => "Periode kerja: {$startDate} s/d {$endDate} setiap bulan",
        ];
    }
}
