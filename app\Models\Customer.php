<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'nama',
        'email',
        'telepon',
        'alamat',
        'tanggal_lahir',
        'jenis_kelamin',
        'loyalty_points',
        'segment',
        'notes',
        'is_active',
        // Location fields
        'province_id',
        'city_id',
        'district_id',
        'village_id',
        'postal_code',
        'detail_address',
    ];

    protected $casts = [
        'tanggal_lahir' => 'date',
        'loyalty_points' => 'integer',
        'is_active' => 'boolean',
    ];

    protected $dates = ['deleted_at'];

    /**
     * Relasi ke LoyaltyTransaction
     */
    public function loyaltyTransactions()
    {
        return $this->hasMany(LoyaltyTransaction::class);
    }

    /**
     * <PERSON>lasi ke PosTransaction
     */
    public function posTransactions()
    {
        return $this->hasMany(PosTransaction::class);
    }

    /**
     * Relasi ke CustomerFeedback
     */
    public function feedbacks()
    {
        return $this->hasMany(CustomerFeedback::class);
    }

    // Removed relations to deleted models (SalesOrder, SalesInvoice, Quotation)

    /**
     * Scope untuk customer dengan status aktif
     */
    public function scopeActiveStatus($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope untuk customer dengan status tidak aktif
     */
    public function scopeInactiveStatus($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Relasi ke Province
     */
    public function province()
    {
        return $this->belongsTo(Province::class);
    }

    /**
     * Relasi ke City
     */
    public function city()
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Relasi ke District
     */
    public function district()
    {
        return $this->belongsTo(District::class);
    }

    /**
     * Relasi ke Village
     */
    public function village()
    {
        return $this->belongsTo(Village::class);
    }

    /**
     * Get membership tier based on loyalty points
     */
    public function getMembershipTierAttribute(): string
    {
        if ($this->loyalty_points >= 10000) {
            return 'Platinum';
        } elseif ($this->loyalty_points >= 5000) {
            return 'Gold';
        } elseif ($this->loyalty_points >= 1000) {
            return 'Silver';
        }

        return 'Bronze';
    }

    /**
     * Get total spending from POS transactions
     */
    public function getTotalSpendingAttribute(): float
    {
        return $this->posTransactions()->sum('net_amount') ?? 0;
    }

    /**
     * Get total transactions count
     */
    public function getTotalTransactionsAttribute(): int
    {
        return $this->posTransactions()->count();
    }

    /**
     * Get last transaction date
     */
    public function getLastTransactionDateAttribute()
    {
        return $this->posTransactions()->latest('transaction_date')->first()?->transaction_date;
    }

    /**
     * Get full address dengan wilayah administratif
     */
    public function getFullAddressAttribute(): string
    {
        $addressParts = [];

        // Detail address (RT/RW, nama jalan)
        if ($this->detail_address) {
            $addressParts[] = $this->detail_address;
        }

        // Village/Kelurahan
        if ($this->village) {
            $addressParts[] = $this->village->type_label . ' ' . $this->village->name;
        }

        // District/Kecamatan
        if ($this->district) {
            $addressParts[] = 'Kec. ' . $this->district->name;
        }

        // City/Kabupaten
        if ($this->city) {
            $addressParts[] = $this->city->type_label . ' ' . $this->city->name;
        }

        // Province
        if ($this->province) {
            $addressParts[] = $this->province->name;
        }

        // Postal code
        if ($this->postal_code) {
            $addressParts[] = $this->postal_code;
        }

        return implode(', ', array_filter($addressParts));
    }

    /**
     * Get short address (hanya sampai kecamatan)
     */
    public function getShortAddressAttribute(): string
    {
        $addressParts = [];

        if ($this->village) {
            $addressParts[] = $this->village->name;
        }

        if ($this->district) {
            $addressParts[] = $this->district->name;
        }

        if ($this->city) {
            $addressParts[] = $this->city->name;
        }

        return implode(', ', array_filter($addressParts));
    }

    /**
     * Check if customer has complete address
     */
    public function hasCompleteAddress(): bool
    {
        return $this->province_id && $this->city_id && $this->district_id && $this->village_id;
    }

    /**
     * Scope untuk filter berdasarkan segment
     */
    public function scopeBySegment($query, $segment)
    {
        return $query->where('segment', $segment);
    }

    /**
     * Scope untuk pelanggan aktif (transaksi dalam 6 bulan terakhir)
     */
    public function scopeActive($query)
    {
        return $query->whereHas('posTransactions', function ($q) {
            $q->where('transaction_date', '>=', now()->subMonths(6));
        });
    }

    /**
     * Scope untuk pelanggan tidak aktif
     */
    public function scopeInactive($query)
    {
        return $query->whereDoesntHave('posTransactions', function ($q) {
            $q->where('transaction_date', '>=', now()->subMonths(6));
        });
    }
}
