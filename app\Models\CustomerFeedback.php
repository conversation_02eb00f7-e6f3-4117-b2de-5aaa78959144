<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CustomerFeedback extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'customer_feedback';

    protected $fillable = [
        'customer_id',
        'assigned_to_user_id',
        'type',
        'subject',
        'description',
        'status',
        'resolution_notes',
    ];

    protected $dates = ['deleted_at'];

    /**
     * Relasi ke Customer
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Relasi ke User (assigned to)
     */
    public function assignedToUser()
    {
        return $this->belongsTo(User::class, 'assigned_to_user_id');
    }

    /**
     * Scope untuk feedback baru
     */
    public function scopeNew($query)
    {
        return $query->where('status', 'new');
    }

    /**
     * Scope untuk feedback dalam proses
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    /**
     * Scope untuk feedback yang sudah diselesaikan
     */
    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }

    /**
     * Scope untuk keluhan
     */
    public function scopeKeluhan($query)
    {
        return $query->where('type', 'keluhan');
    }

    /**
     * Scope untuk saran
     */
    public function scopeSaran($query)
    {
        return $query->where('type', 'saran');
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'new' => 'danger',
            'in_progress' => 'warning',
            'resolved' => 'success',
            default => 'gray',
        };
    }

    /**
     * Get type badge color
     */
    public function getTypeColorAttribute(): string
    {
        return match ($this->type) {
            'keluhan' => 'danger',
            'saran' => 'info',
            default => 'gray',
        };
    }

    /**
     * Get formatted status
     */
    public function getFormattedStatusAttribute(): string
    {
        return match ($this->status) {
            'new' => 'Baru',
            'in_progress' => 'Dalam Proses',
            'resolved' => 'Selesai',
            default => $this->status,
        };
    }

    /**
     * Get formatted type
     */
    public function getFormattedTypeAttribute(): string
    {
        return match ($this->type) {
            'keluhan' => 'Keluhan',
            'saran' => 'Saran',
            default => $this->type,
        };
    }
}
