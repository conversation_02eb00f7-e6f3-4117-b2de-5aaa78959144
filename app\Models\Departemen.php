<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Departemen extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'departemen';

    protected $fillable = [
        'nama_departemen',
    ];

    protected $dates = ['deleted_at'];


    public function divisi()
    {
        return $this->hasMany(Divisi::class, 'departemen_id');
    }



    public function karyawan()
    {
        return $this->hasMany(Karyawan::class, 'id_departemen');
    }

    public function sopDokumens()
    {
        return $this->hasMany(SopDokumen::class);
    }

    public function objectives()
    {
        return $this->hasMany(Objective::class);
    }
}
