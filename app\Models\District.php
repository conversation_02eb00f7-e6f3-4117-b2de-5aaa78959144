<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class District extends Model
{
    use HasFactory;

    protected $fillable = [
        'province_id',
        'city_id',
        'code',
        'name',
        'slug',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Boot method untuk auto-generate slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($district) {
            if (empty($district->slug)) {
                $district->slug = Str::slug($district->name);
            }
        });

        static::updating(function ($district) {
            if ($district->isDirty('name') && empty($district->slug)) {
                $district->slug = Str::slug($district->name);
            }
        });
    }

    /**
     * Relasi ke Province
     */
    public function province()
    {
        return $this->belongsTo(Province::class);
    }

    /**
     * Relasi ke City
     */
    public function city()
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Relasi ke Village
     */
    public function villages()
    {
        return $this->hasMany(Village::class);
    }

    /**
     * Relasi ke Customer
     */
    public function customers()
    {
        return $this->hasMany(Customer::class);
    }

    /**
     * Scope untuk district aktif
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope untuk filter berdasarkan provinsi
     */
    public function scopeByProvince($query, $provinceId)
    {
        return $query->where('province_id', $provinceId);
    }

    /**
     * Scope untuk filter berdasarkan city
     */
    public function scopeByCity($query, $cityId)
    {
        return $query->where('city_id', $cityId);
    }

    /**
     * Get formatted name dengan kode
     */
    public function getFormattedNameAttribute(): string
    {
        return $this->code . ' - ' . $this->name;
    }

    /**
     * Get full name dengan city dan provinsi
     */
    public function getFullNameAttribute(): string
    {
        return $this->name . ', ' . $this->city->formatted_name . ', ' . $this->province->name;
    }

    /**
     * Get total villages count
     */
    public function getVillagesCountAttribute(): int
    {
        return $this->villages()->count();
    }

    /**
     * Get route key name untuk URL
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
