<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Dokumen extends Model
{

    use SoftDeletes;
    protected $fillable = ['karyawan_id', 'nama_dokumen', 'file_path', 'created_by'];

    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
