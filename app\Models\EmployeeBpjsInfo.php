<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmployeeBpjsInfo extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'employee_bpjs_info';

    protected $fillable = [
        'employee_id',
        'bpjs_kesehatan_number',
        'bpjs_tk_number',
        'bpjs_kesehatan_active',
        'bpjs_tk_active',
        'bpjs_kesehatan_start_date',
        'bpjs_tk_start_date',
        'bpjs_kesehatan_salary_base',
        'bpjs_tk_salary_base',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'bpjs_kesehatan_start_date', 'bpjs_tk_start_date'];

    protected $casts = [
        'bpjs_kesehatan_active' => 'boolean',
        'bpjs_tk_active' => 'boolean',
        'bpjs_kesehatan_start_date' => 'date',
        'bpjs_tk_start_date' => 'date',
        'bpjs_kesehatan_salary_base' => 'decimal:2',
        'bpjs_tk_salary_base' => 'decimal:2',
    ];

    // Relationships
    public function employee()
    {
        return $this->belongsTo(Karyawan::class, 'employee_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeByEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    public function scopeKesehatanActive($query)
    {
        return $query->where('bpjs_kesehatan_active', true);
    }

    public function scopeTkActive($query)
    {
        return $query->where('bpjs_tk_active', true);
    }

    public function scopeActiveParticipants($query)
    {
        return $query->where(function ($q) {
            $q->where('bpjs_kesehatan_active', true)
              ->orWhere('bpjs_tk_active', true);
        });
    }

    // Helper methods
    public function getFormattedBpjsKesehatanNumberAttribute()
    {
        if (!$this->bpjs_kesehatan_number) return null;
        
        // Format BPJS Kesehatan: 0001-XXXXXXXX
        $number = preg_replace('/[^0-9]/', '', $this->bpjs_kesehatan_number);
        if (strlen($number) >= 12) {
            return substr($number, 0, 4) . '-' . substr($number, 4);
        }
        
        return $this->bpjs_kesehatan_number;
    }

    public function getFormattedBpjsTkNumberAttribute()
    {
        if (!$this->bpjs_tk_number) return null;
        
        // Format BPJS TK: 1700-XXXXXXXX
        $number = preg_replace('/[^0-9]/', '', $this->bpjs_tk_number);
        if (strlen($number) >= 12) {
            return substr($number, 0, 4) . '-' . substr($number, 4);
        }
        
        return $this->bpjs_tk_number;
    }

    public function getFormattedBpjsKesehatanSalaryBaseAttribute()
    {
        return 'Rp ' . number_format($this->bpjs_kesehatan_salary_base, 0, ',', '.');
    }

    public function getFormattedBpjsTkSalaryBaseAttribute()
    {
        return 'Rp ' . number_format($this->bpjs_tk_salary_base, 0, ',', '.');
    }

    public function getFormattedBpjsKesehatanStartDateAttribute()
    {
        return $this->bpjs_kesehatan_start_date ? $this->bpjs_kesehatan_start_date->format('d/m/Y') : null;
    }

    public function getFormattedBpjsTkStartDateAttribute()
    {
        return $this->bpjs_tk_start_date ? $this->bpjs_tk_start_date->format('d/m/Y') : null;
    }

    public function getBpjsKesehatanStatusAttribute()
    {
        return $this->bpjs_kesehatan_active ? 'Active' : 'Inactive';
    }

    public function getBpjsTkStatusAttribute()
    {
        return $this->bpjs_tk_active ? 'Active' : 'Inactive';
    }

    public function getBpjsKesehatanStatusColorAttribute()
    {
        return $this->bpjs_kesehatan_active ? 'success' : 'danger';
    }

    public function getBpjsTkStatusColorAttribute()
    {
        return $this->bpjs_tk_active ? 'success' : 'danger';
    }

    public function hasActiveBpjs()
    {
        return $this->bpjs_kesehatan_active || $this->bpjs_tk_active;
    }

    public function hasActiveBpjsKesehatan()
    {
        return $this->bpjs_kesehatan_active && !empty($this->bpjs_kesehatan_number);
    }

    public function hasActiveBpjsTk()
    {
        return $this->bpjs_tk_active && !empty($this->bpjs_tk_number);
    }

    public function calculateBpjsKesehatanEmployee($date = null)
    {
        if (!$this->hasActiveBpjsKesehatan()) return 0;
        
        return BpjsRate::calculateEmployeeBpjs(
            $this->bpjs_kesehatan_salary_base,
            'Kesehatan',
            $date
        );
    }

    public function calculateBpjsKesehatanCompany($date = null)
    {
        if (!$this->hasActiveBpjsKesehatan()) return 0;
        
        return BpjsRate::calculateCompanyBpjs(
            $this->bpjs_kesehatan_salary_base,
            'Kesehatan',
            $date
        );
    }

    public function calculateBpjsTkEmployee($date = null)
    {
        if (!$this->hasActiveBpjsTk()) return 0;
        
        return BpjsRate::calculateEmployeeBpjs(
            $this->bpjs_tk_salary_base,
            'Ketenagakerjaan',
            $date
        );
    }

    public function calculateBpjsTkCompany($date = null)
    {
        if (!$this->hasActiveBpjsTk()) return 0;
        
        return BpjsRate::calculateCompanyBpjs(
            $this->bpjs_tk_salary_base,
            'Ketenagakerjaan',
            $date
        );
    }

    public function calculateTotalEmployeeBpjs($date = null)
    {
        return $this->calculateBpjsKesehatanEmployee($date) + 
               $this->calculateBpjsTkEmployee($date);
    }

    public function calculateTotalCompanyBpjs($date = null)
    {
        return $this->calculateBpjsKesehatanCompany($date) + 
               $this->calculateBpjsTkCompany($date);
    }

    public function updateSalaryBase($newSalaryBase, $type = 'both')
    {
        if ($type === 'both' || $type === 'kesehatan') {
            // BPJS Kesehatan has maximum salary base
            $this->bpjs_kesehatan_salary_base = min($newSalaryBase, 12000000);
        }
        
        if ($type === 'both' || $type === 'tk') {
            // BPJS TK has no maximum salary base
            $this->bpjs_tk_salary_base = $newSalaryBase;
        }
        
        $this->save();
    }

    public function activateBpjsKesehatan($number = null, $startDate = null, $salaryBase = null)
    {
        $this->bpjs_kesehatan_active = true;
        
        if ($number) {
            $this->bpjs_kesehatan_number = $number;
        }
        
        if ($startDate) {
            $this->bpjs_kesehatan_start_date = $startDate;
        }
        
        if ($salaryBase) {
            $this->bpjs_kesehatan_salary_base = min($salaryBase, 12000000);
        }
        
        $this->save();
    }

    public function activateBpjsTk($number = null, $startDate = null, $salaryBase = null)
    {
        $this->bpjs_tk_active = true;
        
        if ($number) {
            $this->bpjs_tk_number = $number;
        }
        
        if ($startDate) {
            $this->bpjs_tk_start_date = $startDate;
        }
        
        if ($salaryBase) {
            $this->bpjs_tk_salary_base = $salaryBase;
        }
        
        $this->save();
    }

    public function deactivateBpjsKesehatan()
    {
        $this->bpjs_kesehatan_active = false;
        $this->save();
    }

    public function deactivateBpjsTk()
    {
        $this->bpjs_tk_active = false;
        $this->save();
    }

    // Static methods
    public static function getForEmployee($employeeId)
    {
        return static::byEmployee($employeeId)->first();
    }

    public static function createOrUpdateForEmployee($employeeId, $data)
    {
        $existing = static::byEmployee($employeeId)->first();
        
        if ($existing) {
            $existing->update($data);
            return $existing;
        }
        
        return static::create(array_merge($data, ['employee_id' => $employeeId]));
    }
}
