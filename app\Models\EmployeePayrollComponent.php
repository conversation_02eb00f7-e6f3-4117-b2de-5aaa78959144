<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmployeePayrollComponent extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'employee_payroll_components';

    protected $fillable = [
        'employee_id',
        'payroll_component_id',
        'amount',
        'percentage_rate',
        'is_active',
        'effective_date',
        'end_date',
        'notes',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'effective_date', 'end_date'];

    protected $casts = [
        'amount' => 'decimal:2',
        'percentage_rate' => 'decimal:2',
        'is_active' => 'boolean',
        'effective_date' => 'date',
        'end_date' => 'date',
    ];

    // Relationships
    public function employee()
    {
        return $this->belongsTo(Karyawan::class, 'employee_id');
    }

    public function payrollComponent()
    {
        return $this->belongsTo(PayrollComponent::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    public function scopeByComponent($query, $componentId)
    {
        return $query->where('payroll_component_id', $componentId);
    }

    public function scopeEffectiveOn($query, $date)
    {
        return $query->where('effective_date', '<=', $date)
                    ->where(function ($q) use ($date) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', $date);
                    });
    }

    public function scopeEarnings($query)
    {
        return $query->whereHas('payrollComponent', function ($q) {
            $q->where('component_type', 'Earning');
        });
    }

    public function scopeDeductions($query)
    {
        return $query->whereHas('payrollComponent', function ($q) {
            $q->where('component_type', 'Deduction');
        });
    }

    // Helper methods
    public function getFormattedAmountAttribute()
    {
        return $this->amount ? 'Rp ' . number_format($this->amount, 0, ',', '.') : null;
    }

    public function getFormattedPercentageRateAttribute()
    {
        return $this->percentage_rate ? $this->percentage_rate . '%' : null;
    }

    public function getFormattedEffectiveDateAttribute()
    {
        return $this->effective_date ? $this->effective_date->format('d/m/Y') : null;
    }

    public function getFormattedEndDateAttribute()
    {
        return $this->end_date ? $this->end_date->format('d/m/Y') : 'Ongoing';
    }

    public function getStatusAttribute()
    {
        if (!$this->is_active) return 'Inactive';
        
        $now = now()->toDateString();
        
        if ($this->effective_date > $now) return 'Future';
        if ($this->end_date && $this->end_date < $now) return 'Expired';
        
        return 'Active';
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'Active' => 'success',
            'Future' => 'warning',
            'Expired' => 'danger',
            'Inactive' => 'gray',
            default => 'gray'
        };
    }

    public function isEffectiveOn($date)
    {
        $checkDate = is_string($date) ? $date : $date->toDateString();
        
        return $this->is_active &&
               $this->effective_date <= $checkDate &&
               (!$this->end_date || $this->end_date >= $checkDate);
    }

    public function calculateAmount($baseAmount = 0, $payrollPeriod = null)
    {
        // If employee has custom amount, use it
        if ($this->amount) {
            return $this->amount;
        }
        
        // If employee has custom percentage, use it
        if ($this->percentage_rate) {
            return ($baseAmount * $this->percentage_rate) / 100;
        }
        
        // Otherwise, use component's default calculation
        return $this->payrollComponent->calculateAmount($baseAmount, $this->employee, $payrollPeriod);
    }

    public function getCalculationBasisAttribute()
    {
        if ($this->amount) {
            return 'Custom Amount: ' . $this->formatted_amount;
        }
        
        if ($this->percentage_rate) {
            return 'Custom Rate: ' . $this->formatted_percentage_rate;
        }
        
        return 'Component Default';
    }

    public function getCalculationBasisColorAttribute()
    {
        if ($this->amount || $this->percentage_rate) {
            return 'warning'; // Custom
        }
        
        return 'info'; // Default
    }

    public function hasCustomValues()
    {
        return $this->amount > 0 || $this->percentage_rate > 0;
    }

    public function expire($endDate = null)
    {
        $this->end_date = $endDate ?: now()->toDateString();
        $this->save();
    }

    public function extend($newEndDate = null)
    {
        $this->end_date = $newEndDate;
        $this->save();
    }

    public function activate()
    {
        $this->is_active = true;
        $this->save();
    }

    public function deactivate()
    {
        $this->is_active = false;
        $this->save();
    }
}
