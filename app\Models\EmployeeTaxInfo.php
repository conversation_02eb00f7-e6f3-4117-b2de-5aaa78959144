<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmployeeTaxInfo extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'employee_tax_info';

    protected $fillable = [
        'employee_id',
        'npwp',
        'ptkp_status',
        'previous_income',
        'previous_tax',
        'is_tax_exempted',
        'tax_exemption_reason',
        'effective_date',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'effective_date'];

    protected $casts = [
        'previous_income' => 'decimal:2',
        'previous_tax' => 'decimal:2',
        'is_tax_exempted' => 'boolean',
        'effective_date' => 'date',
    ];

    // Relationships
    public function employee()
    {
        return $this->belongsTo(Karyawan::class, 'employee_id');
    }

    public function ptkpRate()
    {
        return $this->belongsTo(PtkpRate::class, 'ptkp_status', 'status_code');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeByEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    public function scopeByPtkpStatus($query, $status)
    {
        return $query->where('ptkp_status', $status);
    }

    public function scopeTaxExempted($query)
    {
        return $query->where('is_tax_exempted', true);
    }

    public function scopeWithNpwp($query)
    {
        return $query->whereNotNull('npwp');
    }

    public function scopeEffectiveOn($query, $date)
    {
        return $query->where('effective_date', '<=', $date);
    }

    // Helper methods
    public function getFormattedNpwpAttribute()
    {
        if (!$this->npwp) return null;
        
        // Format NPWP: XX.XXX.XXX.X-XXX.XXX
        $npwp = preg_replace('/[^0-9]/', '', $this->npwp);
        if (strlen($npwp) === 15) {
            return substr($npwp, 0, 2) . '.' . 
                   substr($npwp, 2, 3) . '.' . 
                   substr($npwp, 5, 3) . '.' . 
                   substr($npwp, 8, 1) . '-' . 
                   substr($npwp, 9, 3) . '.' . 
                   substr($npwp, 12, 3);
        }
        
        return $this->npwp;
    }

    public function getFormattedPreviousIncomeAttribute()
    {
        return 'Rp ' . number_format($this->previous_income, 0, ',', '.');
    }

    public function getFormattedPreviousTaxAttribute()
    {
        return 'Rp ' . number_format($this->previous_tax, 0, ',', '.');
    }

    public function getFormattedEffectiveDateAttribute()
    {
        return $this->effective_date ? $this->effective_date->format('d/m/Y') : null;
    }

    public function getPtkpStatusLabelAttribute()
    {
        return $this->ptkpRate ? $this->ptkpRate->display_name : $this->ptkp_status;
    }

    public function getTaxStatusAttribute()
    {
        if ($this->is_tax_exempted) return 'Exempted';
        if (!$this->npwp) return 'No NPWP';
        
        return 'Taxable';
    }

    public function getTaxStatusColorAttribute()
    {
        return match($this->tax_status) {
            'Taxable' => 'success',
            'No NPWP' => 'warning',
            'Exempted' => 'info',
            default => 'gray'
        };
    }

    public function hasNpwp()
    {
        return !empty($this->npwp);
    }

    public function isTaxExempted()
    {
        return $this->is_tax_exempted;
    }

    public function isTaxable()
    {
        return !$this->is_tax_exempted && $this->hasNpwp();
    }

    public function getCurrentPtkpAmount($date = null)
    {
        $ptkpRate = PtkpRate::getByStatus($this->ptkp_status, $date);
        return $ptkpRate ? $ptkpRate->annual_amount : 0;
    }

    public function getCurrentMonthlyPtkpAmount($date = null)
    {
        $ptkpRate = PtkpRate::getByStatus($this->ptkp_status, $date);
        return $ptkpRate ? $ptkpRate->monthly_amount : 0;
    }

    public function calculateAnnualTax($grossAnnualIncome, $date = null)
    {
        if ($this->is_tax_exempted) return 0;
        
        // Get PTKP amount
        $ptkpAmount = $this->getCurrentPtkpAmount($date);
        
        // Calculate taxable income
        $taxableIncome = max(0, $grossAnnualIncome - $ptkpAmount);
        
        if ($taxableIncome <= 0) return 0;
        
        // Calculate tax using tax brackets
        $annualTax = TaxBracket::calculateIncomeTax($taxableIncome, $date);
        
        return $annualTax;
    }

    public function calculateMonthlyTax($grossMonthlyIncome, $date = null)
    {
        // Calculate annual tax based on monthly income projection
        $projectedAnnualIncome = $grossMonthlyIncome * 12;
        $annualTax = $this->calculateAnnualTax($projectedAnnualIncome, $date);
        
        // Return monthly portion
        return $annualTax / 12;
    }

    public function calculateTaxWithPreviousIncome($currentGrossIncome, $isAnnual = false, $date = null)
    {
        if ($this->is_tax_exempted) return 0;
        
        $totalIncome = $this->previous_income + $currentGrossIncome;
        $totalTax = $this->calculateAnnualTax($totalIncome, $date);
        
        // Subtract tax already paid
        $currentTax = max(0, $totalTax - $this->previous_tax);
        
        if (!$isAnnual) {
            // If calculating for monthly, estimate remaining months
            $currentMonth = now()->month;
            $remainingMonths = 13 - $currentMonth;
            $currentTax = $remainingMonths > 0 ? $currentTax / $remainingMonths : $currentTax;
        }
        
        return $currentTax;
    }

    public function updatePreviousIncome($additionalIncome, $additionalTax = 0)
    {
        $this->previous_income += $additionalIncome;
        $this->previous_tax += $additionalTax;
        $this->save();
    }

    public function resetYearlyData()
    {
        $this->previous_income = 0;
        $this->previous_tax = 0;
        $this->save();
    }

    // Static methods
    public static function getForEmployee($employeeId, $date = null)
    {
        $date = $date ?: now();
        
        return static::byEmployee($employeeId)
                    ->effectiveOn($date)
                    ->orderBy('effective_date', 'desc')
                    ->first();
    }

    public static function createOrUpdateForEmployee($employeeId, $data)
    {
        $existing = static::byEmployee($employeeId)
                         ->orderBy('effective_date', 'desc')
                         ->first();
        
        if ($existing && $existing->effective_date->isSameDay($data['effective_date'])) {
            $existing->update($data);
            return $existing;
        }
        
        return static::create(array_merge($data, ['employee_id' => $employeeId]));
    }
}
