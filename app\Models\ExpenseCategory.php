<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExpenseCategory extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'expense_categories';

    protected $fillable = [
        'name',
        'code',
        'description',
        'default_account_id',
        'daily_limit',
        'monthly_limit',
        'requires_receipt',
        'is_active',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    protected $casts = [
        'daily_limit' => 'decimal:2',
        'monthly_limit' => 'decimal:2',
        'requires_receipt' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function defaultAccount()
    {
        return $this->belongsTo(Akun::class, 'default_account_id');
    }

    public function expenseRequestItems()
    {
        return $this->hasMany(ExpenseRequestItem::class);
    }

    public function cashDisbursementItems()
    {
        return $this->hasMany(CashDisbursementItem::class);
    }

    public function pettyCashTransactions()
    {
        return $this->hasMany(PettyCashTransaction::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeRequiresReceipt($query)
    {
        return $query->where('requires_receipt', true);
    }

    // Helper methods
    public function getDisplayNameAttribute()
    {
        return $this->code . ' - ' . $this->name;
    }

    public function getFormattedDailyLimitAttribute()
    {
        return $this->daily_limit ? 'Rp ' . number_format($this->daily_limit, 0, ',', '.') : 'No Limit';
    }

    public function getFormattedMonthlyLimitAttribute()
    {
        return $this->monthly_limit ? 'Rp ' . number_format($this->monthly_limit, 0, ',', '.') : 'No Limit';
    }

    public function hasLimits()
    {
        return $this->daily_limit > 0 || $this->monthly_limit > 0;
    }

    public function checkDailyLimit($amount, $date = null)
    {
        if (!$this->daily_limit) return true;

        $date = $date ?: now()->toDateString();
        
        $todayTotal = $this->expenseRequestItems()
            ->whereDate('expense_date', $date)
            ->sum('amount');

        return ($todayTotal + $amount) <= $this->daily_limit;
    }

    public function checkMonthlyLimit($amount, $month = null, $year = null)
    {
        if (!$this->monthly_limit) return true;

        $month = $month ?: now()->month;
        $year = $year ?: now()->year;
        
        $monthlyTotal = $this->expenseRequestItems()
            ->whereMonth('expense_date', $month)
            ->whereYear('expense_date', $year)
            ->sum('amount');

        return ($monthlyTotal + $amount) <= $this->monthly_limit;
    }

    public function getDailyUsage($date = null)
    {
        $date = $date ?: now()->toDateString();
        
        return $this->expenseRequestItems()
            ->whereDate('expense_date', $date)
            ->sum('amount');
    }

    public function getMonthlyUsage($month = null, $year = null)
    {
        $month = $month ?: now()->month;
        $year = $year ?: now()->year;
        
        return $this->expenseRequestItems()
            ->whereMonth('expense_date', $month)
            ->whereYear('expense_date', $year)
            ->sum('amount');
    }

    public function getDailyRemainingAttribute()
    {
        if (!$this->daily_limit) return null;
        
        return $this->daily_limit - $this->getDailyUsage();
    }

    public function getMonthlyRemainingAttribute()
    {
        if (!$this->monthly_limit) return null;
        
        return $this->monthly_limit - $this->getMonthlyUsage();
    }
}
