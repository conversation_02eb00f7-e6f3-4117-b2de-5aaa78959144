<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class ExpenseRequest extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'expense_requests';

    protected $fillable = [
        'request_number',
        'request_date',
        'employee_id',
        'entitas_id',
        'expense_type',
        'total_amount',
        'status',
        'purpose',
        'notes',
        'requested_by',
        'approved_by',
        'approved_at',
        'rejected_by',
        'rejected_at',
        'rejection_reason',
    ];

    protected $dates = ['deleted_at', 'request_date', 'approved_at', 'rejected_at'];

    protected $casts = [
        'request_date' => 'date',
        'total_amount' => 'decimal:2',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    // Relationships
    public function employee()
    {
        return $this->belongsTo(Karyawan::class, 'employee_id');
    }

    public function entitas()
    {
        return $this->belongsTo(Entitas::class);
    }

    public function expenseRequestItems()
    {
        return $this->hasMany(ExpenseRequestItem::class);
    }

    public function cashDisbursements()
    {
        return $this->hasMany(CashDisbursement::class);
    }

    public function requestedBy()
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function rejectedBy()
    {
        return $this->belongsTo(User::class, 'rejected_by');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    public function scopeByEntitas($query, $entitasId)
    {
        return $query->where('entitas_id', $entitasId);
    }

    public function scopeByExpenseType($query, $type)
    {
        return $query->where('expense_type', $type);
    }

    public function scopePending($query)
    {
        return $query->whereIn('status', ['Draft', 'Submitted']);
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'Approved');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'Rejected');
    }

    // Helper methods
    public function getTotalItemsAttribute()
    {
        return $this->expenseRequestItems()->count();
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'Draft' => 'gray',
            'Submitted' => 'warning',
            'Approved' => 'success',
            'Rejected' => 'danger',
            'Paid' => 'success',
            'Cancelled' => 'danger',
            default => 'gray'
        };
    }

    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'Draft' => 'Draft',
            'Submitted' => 'Menunggu Persetujuan',
            'Approved' => 'Disetujui',
            'Rejected' => 'Ditolak',
            'Paid' => 'Dibayar',
            'Cancelled' => 'Dibatalkan',
            default => $this->status
        };
    }

    public function getExpenseTypeLabelAttribute()
    {
        return match($this->expense_type) {
            'Petty_Cash' => 'Kas Kecil',
            'Reimbursement' => 'Reimbursement',
            'Advance' => 'Uang Muka',
            default => $this->expense_type
        };
    }

    public function getExpenseTypeColorAttribute()
    {
        return match($this->expense_type) {
            'Petty_Cash' => 'info',
            'Reimbursement' => 'success',
            'Advance' => 'warning',
            default => 'gray'
        };
    }

    public function isEditable()
    {
        return in_array($this->status, ['Draft']);
    }

    public function canBeSubmitted()
    {
        return $this->status === 'Draft' && $this->expenseRequestItems()->count() > 0;
    }

    public function canBeApproved()
    {
        return $this->status === 'Submitted';
    }

    public function canBeRejected()
    {
        return $this->status === 'Submitted';
    }

    public function canBeCancelled()
    {
        return in_array($this->status, ['Draft', 'Submitted']);
    }

    public function canBePaid()
    {
        return $this->status === 'Approved';
    }

    public function submit()
    {
        if (!$this->canBeSubmitted()) {
            throw new \Exception('Expense request cannot be submitted');
        }

        $this->status = 'Submitted';
        $this->save();
    }

    public function approve()
    {
        if (!$this->canBeApproved()) {
            throw new \Exception('Expense request cannot be approved');
        }

        $this->status = 'Approved';
        $this->approved_by = auth()->id();
        $this->approved_at = Carbon::now();
        $this->save();
    }

    public function reject($reason = null)
    {
        if (!$this->canBeRejected()) {
            throw new \Exception('Expense request cannot be rejected');
        }

        $this->status = 'Rejected';
        $this->rejected_by = auth()->id();
        $this->rejected_at = Carbon::now();
        $this->rejection_reason = $reason;
        $this->save();
    }

    public function calculateTotals()
    {
        $total = $this->expenseRequestItems()->sum('amount');
        $this->total_amount = $total;
        $this->save();
    }

    public function getFormattedRequestDateAttribute()
    {
        return $this->request_date ? $this->request_date->format('d/m/Y') : null;
    }

    public function getFormattedTotalAmountAttribute()
    {
        return 'Rp ' . number_format($this->total_amount, 0, ',', '.');
    }

    // Auto-generate request number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($expenseRequest) {
            if (empty($expenseRequest->request_number)) {
                $expenseRequest->request_number = static::generateRequestNumber();
            }
        });
    }

    public static function generateRequestNumber()
    {
        $prefix = 'ER';
        $date = Carbon::now()->format('Ymd');
        $lastRequest = static::whereDate('created_at', Carbon::today())
                            ->where('request_number', 'like', $prefix . $date . '%')
                            ->orderBy('request_number', 'desc')
                            ->first();

        if ($lastRequest) {
            $lastNumber = intval(substr($lastRequest->request_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . $date . $newNumber;
    }
}
