<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class GoodsIssue extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'goods_issues';

    protected $fillable = [
        'issue_number',
        'issue_date',
        'sales_order_id',
        'warehouse_id',
        'issue_type',
        'reference_number',
        'status',
        'notes',
        'issued_by',
    ];

    protected $dates = ['deleted_at', 'issue_date'];

    protected $casts = [
        'issue_date' => 'date',
    ];

    // Relationships
    public function salesOrder()
    {
        return $this->belongsTo(SalesOrder::class);
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function goodsIssueItems()
    {
        return $this->hasMany(GoodsIssueItem::class);
    }

    public function issuedBy()
    {
        return $this->belongsTo(User::class, 'issued_by');
    }

    // Scopes
    public function scopeDraft($query)
    {
        return $query->where('status', 'Draft');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'Completed');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('issue_type', $type);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('issue_date', Carbon::today());
    }

    // Helper methods
    public function getTotalItemsAttribute()
    {
        return $this->goodsIssueItems()->count();
    }

    public function getTotalQuantityAttribute()
    {
        return $this->goodsIssueItems()->sum('quantity_issued');
    }

    public function getTotalValueAttribute()
    {
        return $this->goodsIssueItems()->sum('total_cost');
    }

    public function getFormattedTotalValueAttribute()
    {
        return 'Rp ' . number_format($this->total_value, 0, ',', '.');
    }

    public function canBeCompleted()
    {
        return $this->status === 'Draft' && $this->goodsIssueItems()->count() > 0;
    }

    public function canBeCancelled()
    {
        return $this->status === 'Draft';
    }

    public function complete()
    {
        if (!$this->canBeCompleted()) {
            throw new \Exception('Goods issue cannot be completed');
        }

        $this->status = 'Completed';
        $this->save();

        // Update inventory stocks
        $this->updateInventoryStocks();

        // Update sales order item quantities if applicable
        if ($this->sales_order_id) {
            $this->updateSalesOrderItems();
        }

        // Create stock movements
        $this->createStockMovements();
    }

    protected function updateInventoryStocks()
    {
        foreach ($this->goodsIssueItems as $item) {
            $inventoryStock = InventoryStock::where('product_id', $item->product_id)
                                          ->where('warehouse_id', $this->warehouse_id)
                                          ->first();

            if ($inventoryStock) {
                // Decrease available quantity
                $inventoryStock->available_quantity = max(0, $inventoryStock->available_quantity - $item->quantity_issued);
                $inventoryStock->quantity = max(0, $inventoryStock->quantity - $item->quantity_issued);
                $inventoryStock->last_movement_at = now();
                $inventoryStock->last_movement_type = 'Goods_Issue';
                $inventoryStock->updateTotalValue();
            }
        }
    }

    protected function updateSalesOrderItems()
    {
        foreach ($this->goodsIssueItems as $item) {
            if ($item->sales_order_item_id) {
                $soItem = $item->salesOrderItem;
                if ($soItem) {
                    $soItem->quantity_issued += $item->quantity_issued;
                    $soItem->save();
                }
            }
        }
    }

    protected function createStockMovements()
    {
        foreach ($this->goodsIssueItems as $item) {
            StockMovement::create([
                'movement_number' => $this->issue_number,
                'movement_date' => $this->issue_date,
                'movement_type' => $this->issue_type === 'Sales_Order' ? 'Sales_Issue' : 'Transfer_Out',
                'product_id' => $item->product_id,
                'warehouse_id' => $this->warehouse_id,
                'entitas_id' => $this->salesOrder ? $this->salesOrder->entitas_id : 1, // Default entitas
                'quantity' => -$item->quantity_issued, // Negative for outgoing
                'unit_cost' => $item->unit_cost,
                'total_value' => -$item->total_cost, // Negative for outgoing
                'reference_type' => 'GoodsIssue',
                'reference_id' => $this->id,
                'reference_number' => $this->issue_number,
                'notes' => $this->notes,
                'created_by' => $this->issued_by,
            ]);
        }
    }

    // Auto-generate issue number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($goodsIssue) {
            if (empty($goodsIssue->issue_number)) {
                $goodsIssue->issue_number = static::generateIssueNumber();
            }
        });
    }

    public static function generateIssueNumber()
    {
        $prefix = 'GI';
        $date = Carbon::now()->format('Ymd');
        $lastIssue = static::whereDate('created_at', Carbon::today())
                          ->orderBy('id', 'desc')
                          ->first();
        
        $sequence = $lastIssue ? (int) substr($lastIssue->issue_number, -4) + 1 : 1;
        
        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
