<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GoodsIssueItem extends Model
{
    use HasFactory;

    protected $table = 'goods_issue_items';

    protected $fillable = [
        'goods_issue_id',
        'sales_order_item_id',
        'product_id',
        'quantity_issued',
        'unit_cost',
        'total_cost',
        'notes',
    ];

    protected $casts = [
        'quantity_issued' => 'integer',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
    ];

    // Relationships
    public function goodsIssue()
    {
        return $this->belongsTo(GoodsIssue::class);
    }

    public function salesOrderItem()
    {
        return $this->belongsTo(SalesOrderItem::class);
    }

    public function product()
    {
        return $this->belongsTo(Produk::class, 'product_id');
    }

    // Helper methods
    public function getFormattedUnitCostAttribute()
    {
        return 'Rp ' . number_format($this->unit_cost, 0, ',', '.');
    }

    public function getFormattedTotalCostAttribute()
    {
        return 'Rp ' . number_format($this->total_cost, 0, ',', '.');
    }

    // Auto-calculate total cost
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->total_cost = $item->quantity_issued * $item->unit_cost;
        });
    }
}
