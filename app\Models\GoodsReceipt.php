<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class GoodsReceipt extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'goods_receipts';

    protected $fillable = [
        'receipt_number',
        'receipt_date',
        'purchase_order_id',
        'warehouse_id',
        'delivery_note_number',
        'status',
        'notes',
        'received_by',
    ];

    protected $dates = ['deleted_at', 'receipt_date'];

    protected $casts = [
        'receipt_date' => 'date',
    ];

    // Relationships
    public function purchaseOrder()
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function goodsReceiptItems()
    {
        return $this->hasMany(GoodsReceiptItem::class);
    }

    public function receivedBy()
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    public function scopeByPurchaseOrder($query, $purchaseOrderId)
    {
        return $query->where('purchase_order_id', $purchaseOrderId);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'Completed');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'Draft');
    }

    // Helper methods
    public function getTotalItemsAttribute()
    {
        return $this->goodsReceiptItems()->count();
    }

    public function getTotalQuantityAttribute()
    {
        return $this->goodsReceiptItems()->sum('quantity_received');
    }

    public function getTotalValueAttribute()
    {
        return $this->goodsReceiptItems()->sum('total_cost');
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'Draft' => 'gray',
            'Completed' => 'success',
            'Cancelled' => 'danger',
            default => 'gray'
        };
    }

    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'Draft' => 'Draft',
            'Completed' => 'Selesai',
            'Cancelled' => 'Dibatalkan',
            default => $this->status
        };
    }

    public function isEditable()
    {
        return $this->status === 'Draft';
    }

    public function canBeCompleted()
    {
        return $this->status === 'Draft' && $this->goodsReceiptItems()->count() > 0;
    }

    public function canBeCancelled()
    {
        return $this->status === 'Draft';
    }

    public function complete()
    {
        if (!$this->canBeCompleted()) {
            throw new \Exception('Goods receipt cannot be completed');
        }

        $this->status = 'Completed';
        $this->save();

        // Update inventory stocks
        $this->updateInventoryStocks();

        // Update PO item quantities
        $this->updatePurchaseOrderItems();

        // Create journal entry
        $this->createJournalEntry();
    }

    protected function updateInventoryStocks()
    {
        foreach ($this->goodsReceiptItems as $item) {
            $inventoryStock = InventoryStock::where('product_id', $item->product_id)
                                          ->where('warehouse_id', $this->warehouse_id)
                                          ->where('entitas_id', $this->purchaseOrder->entitas_id)
                                          ->first();

            if ($inventoryStock) {
                // Update existing stock with weighted average cost
                $oldValue = $inventoryStock->quantity * $inventoryStock->average_cost;
                $newValue = $item->quantity_received * $item->unit_cost;
                $totalQuantity = $inventoryStock->quantity + $item->quantity_received;
                
                if ($totalQuantity > 0) {
                    $newAverageCost = ($oldValue + $newValue) / $totalQuantity;
                    $inventoryStock->average_cost = $newAverageCost;
                }
                
                $inventoryStock->quantity += $item->quantity_received;
                $inventoryStock->updateTotalValue();
            } else {
                // Create new inventory stock
                InventoryStock::create([
                    'product_id' => $item->product_id,
                    'warehouse_id' => $this->warehouse_id,
                    'entitas_id' => $this->purchaseOrder->entitas_id,
                    'quantity' => $item->quantity_received,
                    'average_cost' => $item->unit_cost,
                    'total_value' => $item->quantity_received * $item->unit_cost,
                    'minimum_stock' => 0,
                    'maximum_stock' => 0,
                    'last_updated' => Carbon::now(),
                ]);
            }
        }
    }

    protected function updatePurchaseOrderItems()
    {
        foreach ($this->goodsReceiptItems as $item) {
            $item->purchaseOrderItem->updateQuantityReceived($item->quantity_received);
        }
    }

    protected function createJournalEntry()
    {
        // This will be implemented when we create the PostingRuleEngine
        // For now, we'll create a placeholder
        
        // Logic: Dr. Inventory, Cr. Goods Received Not Invoiced
        // Will be handled by PostingRuleEngine based on source_type = 'GoodsReceipt'
    }

    public function getFormattedReceiptDateAttribute()
    {
        return $this->receipt_date ? $this->receipt_date->format('d/m/Y') : null;
    }

    public function getFormattedTotalValueAttribute()
    {
        return 'Rp ' . number_format($this->total_value, 0, ',', '.');
    }

    // Auto-generate receipt number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($goodsReceipt) {
            if (empty($goodsReceipt->receipt_number)) {
                $goodsReceipt->receipt_number = static::generateReceiptNumber();
            }
        });
    }

    public static function generateReceiptNumber()
    {
        $prefix = 'GR';
        $date = Carbon::now()->format('Ymd');
        $lastReceipt = static::whereDate('created_at', Carbon::today())
                            ->where('receipt_number', 'like', $prefix . $date . '%')
                            ->orderBy('receipt_number', 'desc')
                            ->first();

        if ($lastReceipt) {
            $lastNumber = intval(substr($lastReceipt->receipt_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . $date . $newNumber;
    }
}
