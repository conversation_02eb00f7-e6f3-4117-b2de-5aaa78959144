<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GoodsReceiptItem extends Model
{
    use HasFactory;

    protected $table = 'goods_receipt_items';

    protected $fillable = [
        'goods_receipt_id',
        'purchase_order_item_id',
        'product_id',
        'quantity_received',
        'unit_cost',
        'total_cost',
        'notes',
    ];

    protected $casts = [
        'quantity_received' => 'integer',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
    ];

    // Relationships
    public function goodsReceipt()
    {
        return $this->belongsTo(GoodsReceipt::class);
    }

    public function purchaseOrderItem()
    {
        return $this->belongsTo(PurchaseOrderItem::class);
    }

    public function product()
    {
        return $this->belongsTo(Produk::class, 'product_id');
    }

    // Helper methods
    public function getFormattedUnitCostAttribute()
    {
        return 'Rp ' . number_format($this->unit_cost, 0, ',', '.');
    }

    public function getFormattedTotalCostAttribute()
    {
        return 'Rp ' . number_format($this->total_cost, 0, ',', '.');
    }

    public function getVarianceQuantityAttribute()
    {
        if (!$this->purchaseOrderItem) return 0;
        
        return $this->quantity_received - $this->purchaseOrderItem->remaining_quantity;
    }

    public function getVarianceCostAttribute()
    {
        if (!$this->purchaseOrderItem) return 0;
        
        return $this->unit_cost - $this->purchaseOrderItem->unit_price;
    }

    public function hasQuantityVariance()
    {
        return $this->variance_quantity != 0;
    }

    public function hasCostVariance()
    {
        return abs($this->variance_cost) > 0.01; // Allow small rounding differences
    }

    public function hasVariance()
    {
        return $this->hasQuantityVariance() || $this->hasCostVariance();
    }

    // Auto-calculate total cost when saving
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->total_cost = $item->quantity_received * $item->unit_cost;
        });
    }
}
