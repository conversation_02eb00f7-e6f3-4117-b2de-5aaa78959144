<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class InventoryStock extends Model
{
    use HasFactory;

    protected $table = 'inventory_stocks';

    protected $fillable = [
        'product_id',
        'warehouse_id',
        'entitas_id',
        'quantity',
        'available_quantity',
        'on_hold_quantity',
        'reserved_quantity',
        'average_cost',
        'total_value',
        'minimum_stock',
        'maximum_stock',
        'reorder_point',
        'safety_stock',
        'location_code',
        'is_batch_tracked',
        'last_updated',
        'last_movement_at',
        'last_movement_type',
    ];

    protected $dates = ['last_updated'];

    protected $casts = [
        'quantity' => 'integer',
        'available_quantity' => 'integer',
        'on_hold_quantity' => 'integer',
        'reserved_quantity' => 'integer',
        'average_cost' => 'decimal:2',
        'total_value' => 'decimal:2',
        'minimum_stock' => 'integer',
        'maximum_stock' => 'integer',
        'reorder_point' => 'integer',
        'safety_stock' => 'integer',
        'is_batch_tracked' => 'boolean',
        'last_updated' => 'date',
        'last_movement_at' => 'datetime',
    ];

    // Relationships
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function entitas()
    {
        return $this->belongsTo(Entitas::class);
    }

    // Scopes
    public function scopeLowStock($query)
    {
        return $query->whereColumn('quantity', '<=', 'minimum_stock');
    }

    public function scopeOverStock($query)
    {
        return $query->whereColumn('quantity', '>=', 'maximum_stock');
    }

    public function scopeByWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    // Helper methods
    public function getStockStatusAttribute()
    {
        if ($this->quantity <= $this->minimum_stock) {
            return 'Low Stock';
        } elseif ($this->quantity >= $this->maximum_stock) {
            return 'Over Stock';
        } else {
            return 'Normal';
        }
    }

    public function getStockStatusColorAttribute()
    {
        switch ($this->stock_status) {
            case 'Low Stock':
                return 'danger';
            case 'Over Stock':
                return 'warning';
            default:
                return 'success';
        }
    }

    public function getAvailableStockStatusAttribute()
    {
        if ($this->available_quantity <= $this->reorder_point) {
            return 'Reorder Required';
        } elseif ($this->available_quantity <= $this->minimum_stock) {
            return 'Low Available Stock';
        } elseif ($this->available_quantity >= $this->maximum_stock) {
            return 'Over Stock';
        } else {
            return 'Normal';
        }
    }

    public function getTotalAllocatedQuantityAttribute()
    {
        return $this->on_hold_quantity + $this->reserved_quantity;
    }

    public function getEffectiveAvailableQuantityAttribute()
    {
        return $this->quantity - $this->total_allocated_quantity;
    }

    public function canReserveQuantity($quantity)
    {
        return $quantity <= $this->available_quantity;
    }

    public function reserveQuantity($quantity, $reason = null)
    {
        if (!$this->canReserveQuantity($quantity)) {
            throw new \Exception('Insufficient available quantity to reserve');
        }

        $this->available_quantity -= $quantity;
        $this->reserved_quantity += $quantity;
        $this->save();

        return true;
    }

    public function releaseReservedQuantity($quantity)
    {
        $releaseAmount = min($quantity, $this->reserved_quantity);

        $this->reserved_quantity -= $releaseAmount;
        $this->available_quantity += $releaseAmount;
        $this->save();

        return $releaseAmount;
    }

    public function updateTotalValue()
    {
        $this->total_value = $this->quantity * $this->average_cost;
        $this->last_updated = Carbon::now();
        $this->save();
    }

    public function adjustStock($quantity, $newAverageCost = null)
    {
        $this->quantity += $quantity;

        if ($newAverageCost !== null) {
            $this->average_cost = $newAverageCost;
        }

        $this->updateTotalValue();
    }

    // Auto-update total value when quantity or average_cost changes
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($stock) {
            $stock->total_value = $stock->quantity * $stock->average_cost;
            if (!$stock->last_updated) {
                $stock->last_updated = Carbon::now();
            }
        });
    }
}
