<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Jabatan extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'jabatan';

    protected $fillable = [
        'nama_jabatan',
        'deskripsi',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    /**
     * Get the employees associated with this position
     */
    public function karyawan()
    {
        return $this->hasMany(Karyawan::class, 'id_jabatan');
    }
}
