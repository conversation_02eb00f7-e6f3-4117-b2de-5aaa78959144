<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JenisLembur extends Model
{
    use HasFactory;

    protected $table = 'jenis_lemburs';

    protected $fillable = [
        'nama_jenis',
        'tipe_perhitungan',
        'pembagi_upah_bulanan',
        'keterangan',
        'is_active',
        'urutan',
    ];

    protected $casts = [
        'pembagi_upah_bulanan' => 'decimal:2',
        'is_active' => 'boolean',
        'urutan' => 'integer',
    ];

    /**
     * Relasi ke AturanLembur
     */
    public function aturanLembur()
    {
        return $this->hasMany(AturanLembur::class, 'jenis_lembur_id');
    }

    /**
     * Relasi ke AturanLembur yang aktif
     */
    public function aturanLemburAktif()
    {
        return $this->hasMany(AturanLembur::class, 'jenis_lembur_id')->where('is_active', true)->orderBy('urutan');
    }

    /**
     * Scope untuk jenis lembur aktif
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope untuk jenis lembur per jam
     */
    public function scopePerJam($query)
    {
        return $query->where('tipe_perhitungan', 'per_jam');
    }

    /**
     * Scope untuk jenis lembur per hari
     */
    public function scopePerHari($query)
    {
        return $query->where('tipe_perhitungan', 'per_hari');
    }

    /**
     * Hitung upah lembur berdasarkan jenis dan jam
     */
    public function hitungUpahLembur($jamLembur, $upahBulanan)
    {
        if ($this->tipe_perhitungan === 'per_hari') {
            // Untuk lembur per hari, ambil aturan pertama
            $aturan = $this->aturanLemburAktif()->first();
            if ($aturan) {
                $upahHarian = $upahBulanan / $this->pembagi_upah_bulanan;
                return $upahHarian * $aturan->multiplier;
            }
            return 0;
        }

        // Untuk lembur per jam, hitung bertingkat
        $totalUpah = 0;
        $jamSisa = $jamLembur;
        $aturanList = $this->aturanLemburAktif;

        foreach ($aturanList as $aturan) {
            if ($jamSisa <= 0) break;

            $jamMulai = $aturan->jam_mulai;
            $jamSelesai = $aturan->jam_selesai ?: 999;

            // Hitung jam yang berlaku untuk aturan ini
            $jamBerlaku = 0;

            if ($jamSisa >= $jamMulai) {
                if ($jamSelesai == 999) {
                    // Aturan terbuka (jam 2+)
                    $jamBerlaku = $jamSisa - $jamMulai + 1;
                } else {
                    // Aturan dengan range tertentu
                    $jamBerlaku = min($jamSisa, $jamSelesai - $jamMulai + 1);
                }

                if ($jamBerlaku > 0) {
                    $upahJam = $upahBulanan / ($this->pembagi_upah_bulanan * 8); // 8 jam kerja per hari
                    $upahAturan = $upahJam * $jamBerlaku * $aturan->multiplier;
                    $totalUpah += $upahAturan;
                    $jamSisa -= $jamBerlaku;
                }
            }
        }

        return $totalUpah;
    }

    /**
     * Get breakdown perhitungan upah lembur
     */
    public function getBreakdownUpahLembur($jamLembur, $upahBulanan)
    {
        $breakdown = [
            'jenis_lembur' => $this->nama_jenis,
            'jenis_hari' => $this->nama_jenis, // Untuk backward compatibility
            'tipe_perhitungan' => $this->tipe_perhitungan,
            'jumlah_jam' => $jamLembur,
            'upah_bulanan' => $upahBulanan,
            'detail_perhitungan' => [],
            'total_upah' => 0,
        ];

        if ($this->tipe_perhitungan === 'per_hari') {
            $aturan = $this->aturanLemburAktif()->first();
            if ($aturan) {
                $upahHarian = $upahBulanan / $this->pembagi_upah_bulanan;
                $upah = $upahHarian * $aturan->multiplier;

                $breakdown['detail_perhitungan'][] = [
                    'aturan' => $aturan->keterangan ?: 'Per Hari',
                    'multiplier' => $aturan->multiplier,
                    'upah' => $upah,
                ];
                $breakdown['total_upah'] = $upah;
            }
            return $breakdown;
        }

        // Untuk per jam
        $jamSisa = $jamLembur;
        $aturanList = $this->aturanLemburAktif;

        foreach ($aturanList as $aturan) {
            if ($jamSisa <= 0) break;

            $jamMulai = $aturan->jam_mulai;
            $jamSelesai = $aturan->jam_selesai ?: 999;

            $jamBerlaku = 0;

            if ($jamSisa >= $jamMulai) {
                if ($jamSelesai == 999) {
                    $jamBerlaku = $jamSisa - $jamMulai + 1;
                } else {
                    $jamBerlaku = min($jamSisa, $jamSelesai - $jamMulai + 1);
                }

                if ($jamBerlaku > 0) {
                    $upahJam = $upahBulanan / ($this->pembagi_upah_bulanan * 8);
                    $upahAturan = $upahJam * $jamBerlaku * $aturan->multiplier;

                    $breakdown['detail_perhitungan'][] = [
                        'aturan' => $aturan->keterangan ?: "Jam {$jamMulai}" . ($jamSelesai != 999 ? "-{$jamSelesai}" : '+'),
                        'jam_berlaku' => $jamBerlaku,
                        'multiplier' => $aturan->multiplier,
                        'upah' => $upahAturan,
                    ];
                    $breakdown['total_upah'] += $upahAturan;
                    $jamSisa -= $jamBerlaku;
                }
            }
        }

        return $breakdown;
    }
}
