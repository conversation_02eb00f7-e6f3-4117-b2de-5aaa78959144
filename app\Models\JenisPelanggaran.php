<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class JenisPelanggaran extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'jenis_pelanggaran';

    protected $fillable = [
        'kode_pelanggaran',
        'nama_pelanggaran',
        'deskripsi',
        'kategori',
        'denda_nominal',
        'jenis_denda',
        'persentase_denda',
        'is_active',
        'keterangan',
        'created_by',
    ];

    protected $casts = [
        'denda_nominal' => 'decimal:2',
        'persentase_denda' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Relasi ke Pelanggaran
     */
    public function pelanggarans()
    {
        return $this->hasMany(Pelanggaran::class);
    }

    /**
     * Relasi ke User yang membuat
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Hitung nominal denda berdasarkan gaji karyawan
     */
    public function hitungDenda($gajiPokok = 0)
    {
        if ($this->jenis_denda === 'nominal_tetap') {
            return $this->denda_nominal;
        }

        if ($this->jenis_denda === 'persentase_gaji' && $gajiPokok > 0) {
            return ($gajiPokok * $this->persentase_denda) / 100;
        }

        return 0;
    }

    /**
     * Scope untuk pelanggaran aktif
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope berdasarkan kategori
     */
    public function scopeByKategori($query, $kategori)
    {
        return $query->where('kategori', $kategori);
    }

    /**
     * Accessor untuk format denda
     */
    public function getFormattedDendaAttribute()
    {
        if ($this->jenis_denda === 'nominal_tetap') {
            return 'Rp ' . number_format($this->denda_nominal, 0, ',', '.');
        }

        return $this->persentase_denda . '% dari gaji pokok';
    }

    /**
     * Accessor untuk badge kategori
     */
    public function getKategoriBadgeAttribute()
    {
        $badges = [
            'ringan' => 'success',
            'sedang' => 'warning',
            'berat' => 'danger',
        ];

        return $badges[$this->kategori] ?? 'secondary';
    }
}
