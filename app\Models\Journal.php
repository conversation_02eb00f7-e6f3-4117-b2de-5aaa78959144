<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Journal extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'journals';

    protected $fillable = [
        'journal_number',
        'transaction_date',
        'reference_number',
        'source_type',
        'source_id',
        'description',
        'status',
        'posting_rule_id',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'transaction_date'];

    protected $casts = [
        'transaction_date' => 'date',
    ];

    // Relationships
    public function journalEntries()
    {
        return $this->hasMany(JournalEntry::class);
    }

    public function source()
    {
        return $this->morphTo();
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopePosted($query)
    {
        return $query->where('status', 'Posted');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'Draft');
    }

    public function scopeBySourceType($query, $type)
    {
        return $query->where('source_type', $type);
    }

    // Helper methods
    public function getTotalDebitAttribute()
    {
        // Use loaded relationship if available, otherwise query
        if ($this->relationLoaded('journalEntries')) {
            return $this->journalEntries->sum('debit');
        }
        return $this->journalEntries()->sum('debit');
    }

    public function getTotalCreditAttribute()
    {
        // Use loaded relationship if available, otherwise query
        if ($this->relationLoaded('journalEntries')) {
            return $this->journalEntries->sum('credit');
        }
        return $this->journalEntries()->sum('credit');
    }

    public function isBalanced()
    {
        return $this->getTotalDebitAttribute() == $this->getTotalCreditAttribute();
    }

    public function getFormattedJournalDateAttribute()
    {
        return $this->transaction_date ? $this->transaction_date->format('d/m/Y') : null;
    }

    public function getFormattedTotalDebitAttribute()
    {
        return 'Rp ' . number_format($this->getTotalDebitAttribute(), 0, ',', '.');
    }

    public function getFormattedTotalCreditAttribute()
    {
        return 'Rp ' . number_format($this->getTotalCreditAttribute(), 0, ',', '.');
    }

    // Auto-generate journal number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($journal) {
            if (empty($journal->journal_number)) {
                $journal->journal_number = static::generateJournalNumber();
            }
        });
    }

    public static function generateJournalNumber()
    {
        $year = date('Y');
        $month = date('m');
        $lastJournal = static::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastJournal ? (int)substr($lastJournal->journal_number, -4) + 1 : 1;

        return sprintf('JRN-%s%s-%04d', $year, $month, $sequence);
    }
}
