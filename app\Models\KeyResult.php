<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use App\Traits\SafeDateOperations;

class KeyResult extends Model
{
    use HasFactory, SoftDeletes, LogsActivity, SafeDateOperations;

    protected $fillable = [
        'objective_id',
        'nama_key_result',
        'deskripsi',
        'tipe_metrik',
        'target_value',
        'current_value',
        'unit_measurement',
        'progress_percentage',
        'weight',
        'milestones',
        'last_updated_at',
        'status',
        'due_date',
        'created_by',
    ];

    protected $casts = [
        'target_value' => 'decimal:2',
        'current_value' => 'decimal:2',
        'progress_percentage' => 'integer',
        'weight' => 'integer',
        'milestones' => 'array',
        'last_updated_at' => 'datetime',
        'due_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    // Relationships
    public function objective(): BelongsTo
    {
        return $this->belongsTo(Objective::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Key Results are indicators, not directly connected to tasks
    // Tasks are connected to Objectives instead

    public function documents(): MorphMany
    {
        return $this->morphMany(OkrDocument::class, 'documentable');
    }

    public function activities(): MorphMany
    {
        return $this->morphMany(\Spatie\Activitylog\Models\Activity::class, 'subject');
    }

    // Scopes
    public function scopeByObjective($query, $objectiveId)
    {
        return $query->where('objective_id', $objectiveId);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
            ->whereNotIn('status', ['completed']);
    }

    public function scopeAtRisk($query)
    {
        return $query->where('status', 'at_risk')
            ->orWhere(function ($q) {
                $q->where('due_date', '<=', now()->addDays(7))
                    ->where('progress_percentage', '<', 70)
                    ->whereNotIn('status', ['completed']);
            });
    }

    // Accessors & Mutators
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'not_started' => 'Belum Dimulai',
            'in_progress' => 'Sedang Berjalan',
            'completed' => 'Selesai',
            'at_risk' => 'Berisiko',
            default => 'Unknown',
        };
    }

    public function getTipeMetrikLabelAttribute(): string
    {
        return match ($this->tipe_metrik) {
            'number' => 'Angka',
            'percentage' => 'Persentase',
            'currency' => 'Mata Uang',
            'boolean' => 'Ya/Tidak',
            default => 'Unknown',
        };
    }

    public function getFormattedTargetValueAttribute(): string
    {
        return $this->formatValue($this->target_value);
    }

    public function getFormattedCurrentValueAttribute(): string
    {
        return $this->formatValue($this->current_value);
    }

    public function getProgressColorAttribute(): string
    {
        return match (true) {
            $this->progress_percentage >= 100 => 'success',
            $this->progress_percentage >= 80 => 'info',
            $this->progress_percentage >= 60 => 'warning',
            $this->progress_percentage >= 40 => 'primary',
            default => 'danger',
        };
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date &&
            $this->due_date->isPast() &&
            $this->status !== 'completed';
    }

    public function getDaysRemainingAttribute(): ?int
    {
        if (!$this->due_date || $this->status === 'completed') {
            return null;
        }

        return now()->diffInDays($this->due_date, false);
    }

    // Methods
    public function updateProgress(): int
    {
        if ($this->tipe_metrik === 'boolean') {
            $progress = $this->current_value >= 1 ? 100 : 0;
        } else {
            $progress = $this->target_value > 0
                ? min(100, round(($this->current_value / $this->target_value) * 100))
                : 0;
        }

        $this->update(['progress_percentage' => $progress]);

        // Update status based on progress
        $this->updateStatus();

        // Update parent objective progress
        $this->objective->calculateProgress();
        $this->objective->updateStatus();

        return $progress;
    }

    public function updateStatus(): void
    {
        $newStatus = match (true) {
            $this->progress_percentage >= 100 => 'completed',
            $this->progress_percentage > 0 => 'in_progress',
            $this->isAtRisk() => 'at_risk',
            default => 'not_started',
        };

        if ($this->status !== $newStatus) {
            $this->update(['status' => $newStatus]);
        }
    }

    // Key Results progress is updated manually or based on target achievement
    // Tasks are managed at Objective level, not Key Result level

    public function isAtRisk(): bool
    {
        if ($this->status === 'completed') {
            return false;
        }

        // Consider at risk if due date is within 7 days and progress < 70%
        if ($this->due_date && $this->due_date->diffInDays(now()) <= 7) {
            return $this->progress_percentage < 70;
        }

        return false;
    }

    public function incrementValue(float $amount): void
    {
        $newValue = $this->current_value + $amount;
        $this->update(['current_value' => $newValue]);
        $this->updateProgress();
    }

    public function setValue(float $value): void
    {
        $this->update(['current_value' => $value]);
        $this->updateProgress();
    }

    private function formatValue(float $value): string
    {
        return match ($this->tipe_metrik) {
            'percentage' => number_format($value, 1) . '%',
            'currency' => 'Rp ' . number_format($value, 0, ',', '.'),
            'boolean' => $value >= 1 ? 'Ya' : 'Tidak',
            default => number_format($value, 2) . ($this->unit_measurement ? ' ' . $this->unit_measurement : ''),
        };
    }

    public function getAchievementRate(): float
    {
        if ($this->target_value <= 0) {
            return 0;
        }

        return min(100, ($this->current_value / $this->target_value) * 100);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'nama_key_result',
                'current_value',
                'progress_percentage',
                'status',
                'due_date'
            ])
            ->logOnlyDirty();
    }
}
