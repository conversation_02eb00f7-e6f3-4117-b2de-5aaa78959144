<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class KpiPenilaian extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'kpi_penilaians';

    protected $fillable = [
        'karyawan_id',
        'periode',
        'target_kpi',
        'realisasi_kpi',
        'nilai_akhir',
        'status_penilaian',
        'penilai_id',
        'tanggal_penilaian',
        'kategori_penilaian',
        'keterangan',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    protected $casts = [
        'target_kpi' => 'decimal:2',
        'realisasi_kpi' => 'decimal:2',
        'tanggal_penilaian' => 'date',
        'kategori_penilaian' => 'array',
    ];

    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class);
    }

    public function penilai()
    {
        return $this->belongsTo(User::class, 'penilai_id');
    }

    /**
     * Calculate achievement percentage
     */
    public function getAchievementPercentageAttribute()
    {
        if (!$this->target_kpi || $this->target_kpi == 0) {
            return 0;
        }

        return round(($this->realisasi_kpi / $this->target_kpi) * 100, 2);
    }

    /**
     * Get achievement color based on percentage
     */
    public function getAchievementColorAttribute()
    {
        $percentage = $this->achievement_percentage;

        if ($percentage >= 100) return 'success';
        if ($percentage >= 80) return 'warning';
        if ($percentage >= 70) return 'danger';
        return 'gray';
    }
}
