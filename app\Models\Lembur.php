<?php

namespace App\Models;

use EightyNine\Approvals\Models\ApprovableModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Lembur extends ApprovableModel
{
    use HasFactory, SoftDeletes, \App\Traits\HasOptimizedQueries;

    protected $table = 'lembur';

    protected $fillable = [
        'karyawan_id',
        'tanggal',
        'jumlah_jam',
        'jenis_lembur',
        'jenis_lembur_id',
        'upah_lembur',
        'deskripsi',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'tanggal'];

    // Relationship dengan Karyawan
    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class, 'karyawan_id');
    }

    // Relationship dengan User (creator)
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Relationship dengan JenisLembur
    public function jenisLembur()
    {
        return $this->belongsTo(JenisLembur::class, 'jenis_lembur_id');
    }

    // Scope untuk filter berdasarkan bulan
    public function scopeFilterByMonth($query, $month, $year = null)
    {
        $year = $year ?? date('Y');
        return $query->whereMonth('tanggal', $month)
            ->whereYear('tanggal', $year);
    }

    /**
     * Hitung upah lembur berdasarkan jenis lembur yang dipilih
     */
    public function hitungUpahLembur($upahBulanan = null)
    {
        if (!$upahBulanan) {
            // Ambil upah bulanan dari basis gaji karyawan (periode_gaji = 'Basis Gaji')
            $basisGaji = $this->karyawan->penggajian()
                ->where('periode_gaji', 'Basis Gaji')
                ->latest()
                ->first();
            $upahBulanan = $basisGaji ? $basisGaji->gaji_pokok : 0;
        }

        if ($upahBulanan <= 0) {
            return 0;
        }

        // Jika ada jenis lembur yang dipilih, gunakan itu
        if ($this->jenis_lembur_id && $this->jenisLembur) {
            return $this->jenisLembur->hitungUpahLembur($this->jumlah_jam, $upahBulanan);
        }

        // Fallback ke sistem lama jika belum ada jenis lembur
        $jenisHari = $this->getJenisHari();
        return $this->hitungUpahLemburPerJam($upahBulanan, $jenisHari);
    }

    /**
     * Hitung upah lembur per jam berdasarkan aturan bertingkat
     */
    private function hitungUpahLemburPerJam($upahBulanan, $jenisHari)
    {
        $totalUpah = 0;
        $jamSisa = $this->jumlah_jam;

        // Ambil aturan berdasarkan jenis hari
        $aturanList = $jenisHari === 'hari_libur'
            ? \App\Models\AturanLembur::getHolidayRules()
            : \App\Models\AturanLembur::getWeekdayRules();

        foreach ($aturanList as $aturan) {
            if ($jamSisa <= 0) break;

            $jamMulai = $aturan->jam_mulai;
            $jamSelesai = $aturan->jam_selesai ?: 999; // Jika null, anggap unlimited

            // Hitung jam yang berlaku untuk aturan ini
            $jamBerlaku = 0;

            if ($jamSisa >= $jamMulai) {
                if ($jamSelesai == 999) {
                    // Aturan terbuka (jam 2+)
                    $jamBerlaku = $jamSisa - $jamMulai + 1;
                } else {
                    // Aturan dengan range tertentu
                    $jamBerlaku = min($jamSisa, $jamSelesai - $jamMulai + 1);
                }

                if ($jamBerlaku > 0) {
                    $upahAturan = $aturan->hitungUpahLembur($jamBerlaku, $upahBulanan);
                    $totalUpah += $upahAturan;
                    $jamSisa -= $jamBerlaku;
                }
            }
        }

        return $totalUpah;
    }

    /**
     * Tentukan jenis hari berdasarkan tanggal lembur
     */
    private function getJenisHari()
    {
        $tanggal = \Carbon\Carbon::parse($this->tanggal);
        $dayOfWeek = $tanggal->dayOfWeek;

        // Cek apakah tanggal merah (hari libur nasional)
        // Untuk sementara, anggap Sabtu dan Minggu sebagai hari libur
        if ($dayOfWeek == 0 || $dayOfWeek == 6) { // Minggu = 0, Sabtu = 6
            return 'hari_libur';
        }

        // Untuk implementasi lebih lanjut, bisa ditambahkan pengecekan
        // terhadap tabel hari libur nasional

        return 'hari_biasa';
    }

    /**
     * Get formatted upah lembur
     */
    public function getFormattedUpahLemburAttribute()
    {
        $upah = $this->hitungUpahLembur();
        return 'Rp ' . number_format($upah, 0, ',', '.');
    }

    /**
     * Get breakdown upah lembur untuk debugging
     */
    public function getBreakdownUpahLembur($upahBulanan = null)
    {
        if (!$upahBulanan) {
            // Ambil upah bulanan dari basis gaji karyawan (periode_gaji = 'Basis Gaji')
            $basisGaji = $this->karyawan->penggajian()
                ->where('periode_gaji', 'Basis Gaji')
                ->latest()
                ->first();
            $upahBulanan = $basisGaji ? $basisGaji->gaji_pokok : 0;
        }

        // Jika ada jenis lembur yang dipilih, gunakan breakdown dari JenisLembur
        if ($this->jenis_lembur_id && $this->jenisLembur) {
            $breakdown = $this->jenisLembur->getBreakdownUpahLembur($this->jumlah_jam, $upahBulanan);
            $breakdown['total_upah'] = $this->jenisLembur->hitungUpahLembur($this->jumlah_jam, $upahBulanan);
            $breakdown['jenis_hari'] = $this->jenisLembur->nama_jenis; // Tambahkan jenis_hari
            return $breakdown;
        }

        // Fallback ke sistem lama
        $jenisHari = $this->getJenisHari();
        $breakdown = [
            'jenis_lembur' => 'Sistem Lama - ' . ucfirst(str_replace('_', ' ', $jenisHari)),
            'jenis_hari' => $jenisHari,
            'tipe_perhitungan' => 'per_jam',
            'jumlah_jam' => $this->jumlah_jam,
            'upah_bulanan' => $upahBulanan,
            'detail_perhitungan' => [],
            'total_upah' => 0,
        ];

        // Untuk lembur per jam (sistem lama)
        $jamSisa = $this->jumlah_jam;
        $aturanList = $jenisHari === 'hari_libur'
            ? \App\Models\AturanLembur::getHolidayRules()
            : \App\Models\AturanLembur::getWeekdayRules();

        foreach ($aturanList as $aturan) {
            if ($jamSisa <= 0) break;

            $jamMulai = $aturan->jam_mulai;
            $jamSelesai = $aturan->jam_selesai ?: 999;

            $jamBerlaku = 0;

            if ($jamSisa >= $jamMulai) {
                if ($jamSelesai == 999) {
                    $jamBerlaku = $jamSisa - $jamMulai + 1;
                } else {
                    $jamBerlaku = min($jamSisa, $jamSelesai - $jamMulai + 1);
                }

                if ($jamBerlaku > 0) {
                    $upahJam = $upahBulanan / (30 * 8); // Asumsi 30 hari, 8 jam per hari
                    $upahAturan = $upahJam * $jamBerlaku * $aturan->multiplier;
                    $breakdown['detail_perhitungan'][] = [
                        'aturan' => 'Sistem Lama - ' . $aturan->keterangan,
                        'jam_berlaku' => $jamBerlaku,
                        'multiplier' => $aturan->multiplier,
                        'upah' => $upahAturan,
                    ];
                    $breakdown['total_upah'] += $upahAturan;
                    $jamSisa -= $jamBerlaku;
                }
            }
        }

        return $breakdown;
    }

    // Scope untuk filter berdasarkan karyawan
    public function scopeFilterByKaryawan($query, $karyawanId)
    {
        return $query->where('karyawan_id', $karyawanId);
    }
}
