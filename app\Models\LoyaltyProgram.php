<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LoyaltyProgram extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'type',
        'is_active',
        'points_per_amount',
        'points_multiplier',
        'minimum_transaction',
        'max_points_per_transaction',
        'redemption_rate',
        'minimum_redemption',
        'time_multipliers',
        'day_multipliers',
        'tier_multipliers',
        'birthday_bonus_points',
        'vip_bonus_multiplier',
        'start_date',
        'end_date',
        'points_expiry_months',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'points_per_amount' => 'decimal:2',
        'points_multiplier' => 'decimal:2',
        'minimum_transaction' => 'decimal:2',
        'redemption_rate' => 'decimal:2',
        'vip_bonus_multiplier' => 'decimal:2',
        'time_multipliers' => 'array',
        'day_multipliers' => 'array',
        'tier_multipliers' => 'array',
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = auth()->id();
        });

        static::updating(function ($model) {
            $model->updated_by = auth()->id();
        });
    }

    /**
     * Get the active loyalty program
     */
    public static function getActive()
    {
        return static::where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('start_date')
                      ->orWhere('start_date', '<=', now());
            })
            ->where(function ($query) {
                $query->whereNull('end_date')
                      ->orWhere('end_date', '>=', now());
            })
            ->first();
    }

    /**
     * Calculate points for a given amount
     */
    public function calculatePoints($amount, $customer = null, $transactionDate = null): int
    {
        if ($amount < $this->minimum_transaction) {
            return 0;
        }

        $basePoints = (int) floor($amount / $this->points_per_amount);
        $finalPoints = $basePoints * $this->points_multiplier;

        // Apply tier multiplier
        if ($customer && $this->tier_multipliers) {
            $tierMultiplier = $this->getTierMultiplier($customer);
            $finalPoints *= $tierMultiplier;
        }

        // Apply time-based multiplier
        if ($transactionDate && $this->time_multipliers) {
            $timeMultiplier = $this->getTimeMultiplier($transactionDate);
            $finalPoints *= $timeMultiplier;
        }

        // Apply day-based multiplier
        if ($transactionDate && $this->day_multipliers) {
            $dayMultiplier = $this->getDayMultiplier($transactionDate);
            $finalPoints *= $dayMultiplier;
        }

        // Apply VIP bonus
        if ($customer && $customer->is_vip) {
            $finalPoints *= $this->vip_bonus_multiplier;
        }

        // Apply birthday bonus
        if ($customer && $this->isBirthday($customer, $transactionDate)) {
            $finalPoints += $this->birthday_bonus_points;
        }

        return min((int) $finalPoints, $this->max_points_per_transaction);
    }

    private function getTierMultiplier($customer): float
    {
        if (!$this->tier_multipliers || !$customer->loyalty_points) {
            return 1.0;
        }

        foreach (array_reverse($this->tier_multipliers) as $tier => $config) {
            if ($customer->loyalty_points >= $config['min_points']) {
                return $config['multiplier'];
            }
        }

        return 1.0;
    }

    private function getTimeMultiplier($transactionDate): float
    {
        if (!$this->time_multipliers) return 1.0;

        $currentTime = $transactionDate->format('H:i');

        foreach ($this->time_multipliers as $period => $config) {
            if ($currentTime >= $config['start'] && $currentTime <= $config['end']) {
                return $config['multiplier'];
            }
        }

        return 1.0;
    }

    private function getDayMultiplier($transactionDate): float
    {
        if (!$this->day_multipliers) return 1.0;

        $dayName = strtolower($transactionDate->format('l'));

        return $this->day_multipliers[$dayName] ?? 1.0;
    }

    private function isBirthday($customer, $transactionDate): bool
    {
        if (!$customer->tanggal_lahir) return false;

        $today = $transactionDate->format('m-d');
        $birthday = $customer->tanggal_lahir->format('m-d');

        return $today === $birthday;
    }

    /**
     * Relationships
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
