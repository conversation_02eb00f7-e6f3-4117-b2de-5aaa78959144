<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LoyaltyTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'type',
        'points',
        'transaction_reference_id',
        'reference_type',
        'description',
    ];

    protected $casts = [
        'points' => 'integer',
    ];

    /**
     * Relasi ke Customer
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * <PERSON>lasi polymorphic ke transaction reference
     */
    public function transactionReference()
    {
        return $this->morphTo('reference', 'reference_type', 'transaction_reference_id');
    }

    /**
     * Scope untuk transaksi earn
     */
    public function scopeEarn($query)
    {
        return $query->where('type', 'earn');
    }

    /**
     * Scope untuk transaksi redeem
     */
    public function scopeRedeem($query)
    {
        return $query->where('type', 'redeem');
    }

    /**
     * Static method untuk membuat transaksi earn
     */
    public static function createEarnTransaction(
        int $customerId,
        int $points,
        string $description,
        $referenceId = null,
        string $referenceType = null
    ): self {
        return self::create([
            'customer_id' => $customerId,
            'type' => 'earn',
            'points' => abs($points), // Pastikan positif
            'transaction_reference_id' => $referenceId,
            'reference_type' => $referenceType,
            'description' => $description,
        ]);
    }

    /**
     * Static method untuk membuat transaksi redeem
     */
    public static function createRedeemTransaction(
        int $customerId,
        int $points,
        string $description,
        $referenceId = null,
        string $referenceType = null
    ): self {
        return self::create([
            'customer_id' => $customerId,
            'type' => 'redeem',
            'points' => -abs($points), // Pastikan negatif
            'transaction_reference_id' => $referenceId,
            'reference_type' => $referenceType,
            'description' => $description,
        ]);
    }

    /**
     * Boot method untuk update loyalty points di customer
     */
    protected static function boot()
    {
        parent::boot();

        static::created(function ($loyaltyTransaction) {
            $loyaltyTransaction->updateCustomerLoyaltyPoints();
        });

        static::updated(function ($loyaltyTransaction) {
            $loyaltyTransaction->updateCustomerLoyaltyPoints();
        });

        static::deleted(function ($loyaltyTransaction) {
            $loyaltyTransaction->updateCustomerLoyaltyPoints();
        });
    }

    /**
     * Update loyalty points di customer
     */
    private function updateCustomerLoyaltyPoints()
    {
        $customer = $this->customer;
        if ($customer) {
            $totalPoints = $customer->loyaltyTransactions()->sum('points');
            $customer->update(['loyalty_points' => max(0, $totalPoints)]);
        }
    }
}
