<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MutasiPromosiDemosi extends Model
{
    use HasFactory;

    protected $table = 'mutasi_promosi_demosi';

    protected $fillable = [
        'karyawan_id',
        'tipe',
        'entitas_id',
        'departemen_id',
        'divisi_id',
        'jabatan_id',
        'tanggal_efektif',
        'alasan',
        'is_active',
        'posisi_awal',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'tanggal_efektif' => 'date',
        'posisi_awal' => 'array',
    ];

    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class);
    }

    public function entitas()
    {
        return $this->belongsTo(Entitas::class);
    }

    public function departemen()
    {
        return $this->belongsTo(Departemen::class);
    }

    public function divisi()
    {
        return $this->belongsTo(Divisi::class);
    }

    public function jabatan()
    {
        return $this->belongsTo(Jabatan::class);
    }
}
