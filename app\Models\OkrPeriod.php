<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use App\Traits\SafeDateOperations;
use Carbon\Carbon;

class OkrPeriod extends Model
{
    use HasFactory, SoftDeletes, LogsActivity, SafeDateOperations;

    protected $fillable = [
        'nama_periode',
        'deskripsi',
        'tipe_periode',
        'tahun',
        'quarter',
        'tanggal_mulai',
        'tanggal_selesai',
        'status',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'tanggal_mulai' => 'date',
        'tanggal_selesai' => 'date',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    // Relationships
    public function objectives(): HasMany
    {
        return $this->hasMany(Objective::class, 'okr_period_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('tipe_periode', $type);
    }

    public function scopeByYear($query, $year)
    {
        return $query->where('tahun', $year);
    }

    public function scopeByQuarter($query, $quarter)
    {
        return $query->where('quarter', $quarter);
    }

    public function scopeCurrent($query)
    {
        return $query->where('tanggal_mulai', '<=', now())
                    ->where('tanggal_selesai', '>=', now());
    }

    // Accessors & Mutators
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'draft' => 'Draft',
            'active' => 'Aktif',
            'completed' => 'Selesai',
            'archived' => 'Diarsipkan',
            default => 'Unknown',
        };
    }

    public function getTipePeriodeLabelAttribute(): string
    {
        return match ($this->tipe_periode) {
            'quarterly' => 'Kuartalan',
            'yearly' => 'Tahunan',
            'monthly' => 'Bulanan',
            'custom' => 'Kustom',
            default => 'Unknown',
        };
    }

    public function getDurationDaysAttribute(): int
    {
        return $this->tanggal_mulai->diffInDays($this->tanggal_selesai) + 1;
    }

    public function getDaysRemainingAttribute(): ?int
    {
        if ($this->tanggal_selesai->isPast()) {
            return null;
        }
        
        return now()->diffInDays($this->tanggal_selesai, false);
    }

    public function getProgressPercentageAttribute(): int
    {
        $totalDays = $this->duration_days;
        $daysPassed = $this->tanggal_mulai->diffInDays(now()) + 1;
        
        if ($daysPassed <= 0) return 0;
        if ($daysPassed >= $totalDays) return 100;
        
        return round(($daysPassed / $totalDays) * 100);
    }

    public function getIsCurrentAttribute(): bool
    {
        return $this->tanggal_mulai <= now() && $this->tanggal_selesai >= now();
    }

    // Methods
    public static function createQuarterlyPeriod(int $year, int $quarter): self
    {
        $quarterData = self::getQuarterData($year, $quarter);
        
        return self::create([
            'nama_periode' => "Q{$quarter} {$year}",
            'deskripsi' => "Periode kuartalan Q{$quarter} tahun {$year}",
            'tipe_periode' => 'quarterly',
            'tahun' => $year,
            'quarter' => $quarter,
            'tanggal_mulai' => $quarterData['start'],
            'tanggal_selesai' => $quarterData['end'],
            'status' => 'draft',
            'is_active' => false,
            'created_by' => auth()->id(),
        ]);
    }

    public static function createYearlyPeriod(int $year): self
    {
        return self::create([
            'nama_periode' => "Tahun {$year}",
            'deskripsi' => "Periode tahunan {$year}",
            'tipe_periode' => 'yearly',
            'tahun' => $year,
            'quarter' => null,
            'tanggal_mulai' => Carbon::create($year, 1, 1),
            'tanggal_selesai' => Carbon::create($year, 12, 31),
            'status' => 'draft',
            'is_active' => false,
            'created_by' => auth()->id(),
        ]);
    }

    public function activate(): void
    {
        // Deactivate other periods of the same type
        self::where('tipe_periode', $this->tipe_periode)
            ->where('id', '!=', $this->id)
            ->update(['is_active' => false]);
            
        $this->update([
            'is_active' => true,
            'status' => 'active'
        ]);
    }

    public function complete(): void
    {
        $this->update([
            'status' => 'completed',
            'is_active' => false
        ]);
    }

    public function archive(): void
    {
        $this->update([
            'status' => 'archived',
            'is_active' => false
        ]);
    }

    public function rolloverObjectives(OkrPeriod $targetPeriod): int
    {
        $rolledCount = 0;
        
        foreach ($this->objectives as $objective) {
            // Only rollover incomplete objectives
            if ($objective->status !== 'completed') {
                $newObjective = $objective->replicate();
                $newObjective->okr_period_id = $targetPeriod->id;
                $newObjective->periode_mulai = $targetPeriod->tanggal_mulai;
                $newObjective->periode_selesai = $targetPeriod->tanggal_selesai;
                $newObjective->progress_percentage = 0;
                $newObjective->status = 'draft';
                $newObjective->actual_completion = null;
                $newObjective->save();
                
                // Rollover key results
                foreach ($objective->keyResults as $keyResult) {
                    $newKeyResult = $keyResult->replicate();
                    $newKeyResult->objective_id = $newObjective->id;
                    $newKeyResult->current_value = 0;
                    $newKeyResult->progress_percentage = 0;
                    $newKeyResult->status = 'not_started';
                    $newKeyResult->save();
                }
                
                // Rollover tactics
                foreach ($objective->tactics as $tactic) {
                    $newTactic = $tactic->replicate();
                    $newTactic->objective_id = $newObjective->id;
                    $newTactic->progress_percentage = 0;
                    $newTactic->status = 'planned';
                    $newTactic->actual_hours = 0;
                    $newTactic->save();
                }
                
                $rolledCount++;
            }
        }
        
        return $rolledCount;
    }

    public function getObjectivesSummary(): array
    {
        $objectives = $this->objectives;
        
        return [
            'total' => $objectives->count(),
            'completed' => $objectives->where('status', 'completed')->count(),
            'active' => $objectives->where('status', 'active')->count(),
            'draft' => $objectives->where('status', 'draft')->count(),
            'cancelled' => $objectives->where('status', 'cancelled')->count(),
            'avg_progress' => round($objectives->avg('progress_percentage'), 1),
        ];
    }

    private static function getQuarterData(int $year, int $quarter): array
    {
        return match ($quarter) {
            1 => [
                'start' => Carbon::create($year, 1, 1),
                'end' => Carbon::create($year, 3, 31),
            ],
            2 => [
                'start' => Carbon::create($year, 4, 1),
                'end' => Carbon::create($year, 6, 30),
            ],
            3 => [
                'start' => Carbon::create($year, 7, 1),
                'end' => Carbon::create($year, 9, 30),
            ],
            4 => [
                'start' => Carbon::create($year, 10, 1),
                'end' => Carbon::create($year, 12, 31),
            ],
            default => throw new \InvalidArgumentException('Invalid quarter: ' . $quarter),
        };
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'nama_periode',
                'status',
                'is_active',
                'tanggal_mulai',
                'tanggal_selesai'
            ])
            ->logOnlyDirty();
    }
}
