<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Outlet extends Model
{
    use HasFactory;

    protected $table = 'outlets';

    protected $fillable = [
        'name',
        'code',
        'type',
        'category',
        'address',
        'city',
        'phone',
        'email',
        'manager_name',
        'opening_date',
        'opening_time',
        'closing_time',
        'latitude',
        'longitude',
        'delivery_radius',
        'tax_rate',
        'service_charge_rate',
        'status',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'opening_date' => 'date',
        'opening_time' => 'datetime:H:i',
        'closing_time' => 'datetime:H:i',
        'latitude' => 'decimal:14',
        'longitude' => 'decimal:14',
        'delivery_radius' => 'integer',
        'tax_rate' => 'decimal:2',
        'service_charge_rate' => 'decimal:2',
    ];

    // Relationships
    public function dailyTransactions()
    {
        return $this->hasMany(DailyTransaction::class);
    }

    /**
     * Get the price lists assigned to this outlet
     */
    public function priceLists()
    {
        return $this->belongsToMany(PriceList::class, 'outlet_price_lists')
            ->withPivot(['priority', 'is_active', 'effective_from', 'effective_until'])
            ->withTimestamps();
    }

    /**
     * Get the active price lists assigned to this outlet
     */
    public function activePriceLists()
    {
        return $this->belongsToMany(PriceList::class, 'outlet_price_lists')
            ->withPivot(['priority', 'is_active', 'effective_from', 'effective_until'])
            ->wherePivot('is_active', true)
            ->where('price_lists.is_active', true)
            ->withTimestamps();
    }

    /**
     * Get users assigned to this outlet
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'outlet_user_id')
            ->withPivot(['role', 'is_active', 'assigned_from', 'assigned_until', 'notes'])
            ->withTimestamps();
    }

    /**
     * Get active users assigned to this outlet
     */
    public function activeUsers()
    {
        return $this->belongsToMany(User::class, 'outlet_user_id')
            ->withPivot(['role', 'is_active', 'assigned_from', 'assigned_until', 'notes'])
            ->wherePivot('is_active', true)
            ->withTimestamps();
    }

    /**
     * Get all user assignments for this outlet (for Filament relation manager)
     */
    public function assignedUsers()
    {
        return $this->hasMany(\App\Models\OutletUserAssignment::class)
            ->with(['userWithKaryawan.karyawan.jabatan'])
            ->select([
                'id', 'outlet_id', 'user_id', 'role', 'is_active',
                'assigned_from', 'assigned_until', 'created_at', 'updated_at'
            ]);
    }

    /**
     * Assign a price list to this outlet
     */
    public function assignPriceList($priceListId, $priority = 1, $effectiveFrom = null, $effectiveUntil = null)
    {
        return $this->priceLists()->attach($priceListId, [
            'priority' => $priority,
            'is_active' => true,
            'effective_from' => $effectiveFrom,
            'effective_until' => $effectiveUntil,
        ]);
    }

    /**
     * Remove a price list from this outlet
     */
    public function removePriceList($priceListId)
    {
        return $this->priceLists()->detach($priceListId);
    }

    /**
     * Assign a user to this outlet
     */
    public function assignUser($userId, $role = 'staff', $assignedFrom = null, $assignedUntil = null)
    {
        return $this->users()->attach($userId, [
            'role' => $role,
            'is_active' => true,
            'assigned_from' => $assignedFrom,
            'assigned_until' => $assignedUntil,
        ]);
    }

    /**
     * Remove a user from this outlet
     */
    public function removeUser($userId, $role = null)
    {
        $query = $this->users();

        if ($role) {
            $query->wherePivot('role', $role);
        }

        return $query->detach($userId);
    }

    /**
     * Get the primary price list for this outlet (highest priority)
     */
    public function getPrimaryPriceList()
    {
        return $this->activePriceLists()->orderBy('outlet_price_lists.priority')->first();
    }

    /**
     * Get price for a specific product at this outlet
     */
    public function getPriceForProduct($productId)
    {
        return $this->getProductPrice($productId);
    }

    /**
     * Get price for a specific product at this outlet (alias for API compatibility)
     */
    public function getProductPrice($productId)
    {
        $priceLists = $this->activePriceLists()->orderBy('outlet_price_lists.priority')->get();

        // Check each price list for this product
        foreach ($priceLists as $priceList) {
            $priceListItem = $priceList->activeItems()->where('product_id', $productId)->first();
            if ($priceListItem) {
                return $priceListItem->price;
            }
        }

        // If no specific price found, try global price list
        $globalPriceList = PriceList::getGlobal();
        if ($globalPriceList) {
            $globalPrice = $globalPriceList->getPriceForProduct($productId);
            if ($globalPrice) {
                return $globalPrice;
            }
        }

        // Final fallback to product's default price
        $product = Product::find($productId);
        return $product ? $product->price : null;
    }

    /**
     * Get cost price for a specific product at this outlet
     */
    public function getProductCostPrice($productId)
    {
        $priceLists = $this->activePriceLists()->orderBy('outlet_price_lists.priority')->get();

        // Check each price list for this product
        foreach ($priceLists as $priceList) {
            $priceListItem = $priceList->activeItems()->where('product_id', $productId)->first();
            if ($priceListItem && $priceListItem->cost_price) {
                return $priceListItem->cost_price;
            }
        }

        // If no specific cost price found, try global price list
        $globalPriceList = PriceList::getGlobal();
        if ($globalPriceList) {
            $globalCostPrice = $globalPriceList->getCostPriceForProduct($productId);
            if ($globalCostPrice) {
                return $globalCostPrice;
            }
        }

        // Final fallback to product's default cost price
        $product = Product::find($productId);
        return $product ? $product->cost_price : null;
    }

    /**
     * POS Transactions relationship
     */
    public function posTransactions()
    {
        return $this->hasMany(\App\Models\PosTransaction::class);
    }

    /**
     * Products relationship (for outlet-specific inventory)
     */
    public function products()
    {
        return $this->hasMany(\App\Models\Product::class);
    }

    /**
     * Karyawan (employees) working at this outlet
     */
    public function karyawan()
    {
        return $this->hasMany(\App\Models\Karyawan::class, 'outlet_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeFnB($query)
    {
        return $query->where('category', 'FnB');
    }

    public function scopeVOO($query)
    {
        return $query->where('category', 'VOO');
    }

    // Accessors & Mutators
    public function getFormattedAddressAttribute(): string
    {
        return $this->address . ($this->city ? ', ' . $this->city : '');
    }

    public function getOperatingHoursAttribute(): string
    {
        return $this->opening_time->format('H:i') . ' - ' . $this->closing_time->format('H:i');
    }

    public function getIsOpenAttribute(): bool
    {
        $now = now()->format('H:i');
        $opening = $this->opening_time->format('H:i');
        $closing = $this->closing_time->format('H:i');

        return $now >= $opening && $now <= $closing && $this->status === 'active';
    }

    // Helper Methods
    public function getTodayRevenue(): float
    {
        return $this->posTransactions()
            ->whereDate('transaction_date', today())
            ->sum('net_amount');
    }

    public function getTodayTransactionCount(): int
    {
        return $this->posTransactions()
            ->whereDate('transaction_date', today())
            ->count();
    }

    public function getAverageTransactionValue(): float
    {
        $count = $this->getTodayTransactionCount();
        return $count > 0 ? $this->getTodayRevenue() / $count : 0;
    }
}
