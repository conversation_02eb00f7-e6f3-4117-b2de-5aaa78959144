<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OutletUserAssignment extends Model
{
    use HasFactory;

    protected $table = 'outlet_user_id';

    protected $fillable = [
        'outlet_id',
        'user_id',
        'role',
        'is_active',
        'assigned_from',
        'assigned_until',
        'notes',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'assigned_from' => 'datetime',
        'assigned_until' => 'datetime',
    ];

    protected static function booted()
    {
        static::created(function ($assignment) {
            self::clearCacheForOutlet($assignment->outlet_id);
        });

        static::updated(function ($assignment) {
            self::clearCacheForOutlet($assignment->outlet_id);
        });

        static::deleted(function ($assignment) {
            self::clearCacheForOutlet($assignment->outlet_id);
        });
    }

    protected static function clearCacheForOutlet($outletId)
    {
        cache()->forget("outlet_assignment_counts_{$outletId}");
        cache()->forget("outlet_assignment_stats_{$outletId}");
        cache()->forget('user_options_with_jabatan');
    }

    /**
     * Relationship to Outlet
     */
    public function outlet()
    {
        return $this->belongsTo(Outlet::class)->select(['id', 'name']);
    }

    /**
     * Relationship to User
     */
    public function user()
    {
        return $this->belongsTo(User::class)->select(['id', 'name', 'email']);
    }

    /**
     * Relationship to User with Karyawan data (for display purposes)
     */
    public function userWithKaryawan()
    {
        return $this->belongsTo(User::class, 'user_id')
            ->with(['karyawan.jabatan:id,nama_jabatan'])
            ->select(['id', 'name', 'email']);
    }

    /**
     * Scope for active assignments
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for inactive assignments
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope by role
     */
    public function scopeByRole($query, $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Scope by outlet
     */
    public function scopeByOutlet($query, $outletId)
    {
        return $query->where('outlet_id', $outletId);
    }

    /**
     * Get formatted assignment period
     */
    public function getAssignmentPeriodAttribute()
    {
        if (!$this->assigned_from && !$this->assigned_until) {
            return 'Permanent';
        }

        $from = $this->assigned_from ? $this->assigned_from->format('d/m/Y') : 'Start';
        $until = $this->assigned_until ? $this->assigned_until->format('d/m/Y') : 'Ongoing';

        return "{$from} - {$until}";
    }

    /**
     * Check if assignment is currently effective
     */
    public function isEffective($date = null)
    {
        if (!$this->is_active) {
            return false;
        }

        $date = $date ?: now()->toDateString();
        
        if ($this->assigned_from && $date < $this->assigned_from->toDateString()) {
            return false;
        }

        if ($this->assigned_until && $date > $this->assigned_until->toDateString()) {
            return false;
        }

        return true;
    }

    /**
     * Get role badge color
     */
    public function getRoleBadgeColorAttribute()
    {
        return match($this->role) {
            'kepala toko' => 'success',
            'kasir' => 'primary',
            'supervisor' => 'warning',
            'staff' => 'gray',
            default => 'gray',
        };
    }

    /**
     * Get status badge color
     */
    public function getStatusBadgeColorAttribute()
    {
        if (!$this->is_active) {
            return 'danger';
        }

        return $this->isEffective() ? 'success' : 'warning';
    }

    /**
     * Get status text
     */
    public function getStatusTextAttribute()
    {
        if (!$this->is_active) {
            return 'Inactive';
        }

        return $this->isEffective() ? 'Active' : 'Scheduled';
    }

    /**
     * Get user's karyawan information
     */
    public function getUserKaryawanAttribute()
    {
        if ($this->user && $this->user->karyawan) {
            return $this->user->karyawan;
        }
        return null;
    }

    /**
     * Get user's jabatan
     */
    public function getUserJabatanAttribute()
    {
        if ($this->user_karyawan && $this->user_karyawan->jabatan) {
            return $this->user_karyawan->jabatan->nama_jabatan;
        }
        return null;
    }
}
