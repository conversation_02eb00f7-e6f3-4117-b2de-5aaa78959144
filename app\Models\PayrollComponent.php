<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PayrollComponent extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'payroll_components';

    protected $fillable = [
        'component_code',
        'component_name',
        'component_type',
        'calculation_type',
        'fixed_amount',
        'percentage_rate',
        'calculation_formula',
        'account_id',
        'is_taxable',
        'is_active',
        'description',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    protected $casts = [
        'fixed_amount' => 'decimal:2',
        'percentage_rate' => 'decimal:2',
        'is_taxable' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function account()
    {
        return $this->belongsTo(Akun::class, 'account_id');
    }

    public function employeePayrollComponents()
    {
        return $this->hasMany(EmployeePayrollComponent::class);
    }

    public function payrollTransactionDetails()
    {
        return $this->hasMany(PayrollTransactionDetail::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeEarnings($query)
    {
        return $query->where('component_type', 'Earning');
    }

    public function scopeDeductions($query)
    {
        return $query->where('component_type', 'Deduction');
    }

    public function scopeTaxable($query)
    {
        return $query->where('is_taxable', true);
    }

    public function scopeByCalculationType($query, $type)
    {
        return $query->where('calculation_type', $type);
    }

    // Helper methods
    public function getDisplayNameAttribute()
    {
        return $this->component_code . ' - ' . $this->component_name;
    }

    public function getComponentTypeLabelAttribute()
    {
        return match($this->component_type) {
            'Earning' => 'Pendapatan',
            'Deduction' => 'Potongan',
            default => $this->component_type
        };
    }

    public function getComponentTypeColorAttribute()
    {
        return match($this->component_type) {
            'Earning' => 'success',
            'Deduction' => 'danger',
            default => 'gray'
        };
    }

    public function getCalculationTypeLabelAttribute()
    {
        return match($this->calculation_type) {
            'Fixed' => 'Tetap',
            'Percentage' => 'Persentase',
            'Formula' => 'Formula',
            default => $this->calculation_type
        };
    }

    public function getCalculationTypeColorAttribute()
    {
        return match($this->calculation_type) {
            'Fixed' => 'info',
            'Percentage' => 'warning',
            'Formula' => 'primary',
            default => 'gray'
        };
    }

    public function getFormattedFixedAmountAttribute()
    {
        return $this->fixed_amount ? 'Rp ' . number_format($this->fixed_amount, 0, ',', '.') : null;
    }

    public function getFormattedPercentageRateAttribute()
    {
        return $this->percentage_rate ? $this->percentage_rate . '%' : null;
    }

    public function isEarning()
    {
        return $this->component_type === 'Earning';
    }

    public function isDeduction()
    {
        return $this->component_type === 'Deduction';
    }

    public function isFixed()
    {
        return $this->calculation_type === 'Fixed';
    }

    public function isPercentage()
    {
        return $this->calculation_type === 'Percentage';
    }

    public function isFormula()
    {
        return $this->calculation_type === 'Formula';
    }

    public function calculateAmount($baseAmount = 0, $employee = null, $payrollPeriod = null)
    {
        switch ($this->calculation_type) {
            case 'Fixed':
                return $this->fixed_amount ?? 0;
                
            case 'Percentage':
                return ($baseAmount * ($this->percentage_rate ?? 0)) / 100;
                
            case 'Formula':
                return $this->evaluateFormula($this->calculation_formula, $baseAmount, $employee, $payrollPeriod);
                
            default:
                return 0;
        }
    }

    protected function evaluateFormula($formula, $baseAmount, $employee, $payrollPeriod)
    {
        // Simple formula evaluation
        // This is a basic implementation - in production, you might want to use a more robust formula parser
        
        if (!$formula) return 0;
        
        // Replace variables in formula
        $variables = [
            'BASE_SALARY' => $baseAmount,
            'BASIC_SALARY' => $employee ? ($employee->gaji_pokok ?? 0) : 0,
            'WORKING_DAYS' => $payrollPeriod ? 22 : 22, // Default 22 working days
            'ATTENDANCE_DAYS' => $payrollPeriod ? 22 : 22, // This should come from attendance data
        ];
        
        $evaluatedFormula = $formula;
        foreach ($variables as $var => $value) {
            $evaluatedFormula = str_replace($var, $value, $evaluatedFormula);
        }
        
        // Basic math evaluation (be careful with eval in production!)
        try {
            // Only allow basic math operations for security
            if (preg_match('/^[0-9+\-*\/\(\)\.\s]+$/', $evaluatedFormula)) {
                return eval("return $evaluatedFormula;");
            }
        } catch (Exception $e) {
            // Log error and return 0
            return 0;
        }
        
        return 0;
    }

    public function getUsageCountAttribute()
    {
        return $this->employeePayrollComponents()->count();
    }

    public function canBeDeleted()
    {
        return $this->usage_count === 0;
    }

    // Common payroll components
    public static function getCommonComponents()
    {
        return [
            // Earnings
            ['code' => 'GAPOK', 'name' => 'Gaji Pokok', 'type' => 'Earning', 'calc_type' => 'Fixed'],
            ['code' => 'TUNJAB', 'name' => 'Tunjangan Jabatan', 'type' => 'Earning', 'calc_type' => 'Fixed'],
            ['code' => 'TUNKEL', 'name' => 'Tunjangan Keluarga', 'type' => 'Earning', 'calc_type' => 'Percentage'],
            ['code' => 'TUNMAKAN', 'name' => 'Tunjangan Makan', 'type' => 'Earning', 'calc_type' => 'Fixed'],
            ['code' => 'TUNTRANS', 'name' => 'Tunjangan Transport', 'type' => 'Earning', 'calc_type' => 'Fixed'],
            ['code' => 'LEMBUR', 'name' => 'Lembur', 'type' => 'Earning', 'calc_type' => 'Formula'],
            
            // Deductions
            ['code' => 'PPH21', 'name' => 'PPh 21', 'type' => 'Deduction', 'calc_type' => 'Formula'],
            ['code' => 'BPJSKES', 'name' => 'BPJS Kesehatan', 'type' => 'Deduction', 'calc_type' => 'Percentage'],
            ['code' => 'BPJSTK', 'name' => 'BPJS Ketenagakerjaan', 'type' => 'Deduction', 'calc_type' => 'Percentage'],
            ['code' => 'KASBON', 'name' => 'Kasbon', 'type' => 'Deduction', 'calc_type' => 'Fixed'],
            ['code' => 'ALPHA', 'name' => 'Potongan Alpha', 'type' => 'Deduction', 'calc_type' => 'Formula'],
            ['code' => 'TERLAMBAT', 'name' => 'Potongan Terlambat', 'type' => 'Deduction', 'calc_type' => 'Formula'],
        ];
    }
}
