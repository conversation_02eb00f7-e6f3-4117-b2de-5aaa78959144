<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PayrollDeduction extends Model
{
    use HasFactory;

    protected $fillable = [
        'payroll_transaction_id',
        'jenis_potongan',
        'kode_referensi',
        'deskripsi',
        'nominal',
        'keterangan',
        'tanggal_kejadian',
    ];

    protected $casts = [
        'nominal' => 'decimal:2',
        'tanggal_kejadian' => 'date',
    ];

    /**
     * Relasi ke PayrollTransaction
     */
    public function payrollTransaction()
    {
        return $this->belongsTo(PayrollTransaction::class);
    }

    /**
     * Scope berdasarkan jenis potongan
     */
    public function scopeByJenis($query, $jenis)
    {
        return $query->where('jenis_potongan', $jenis);
    }

    /**
     * Scope untuk potongan keterlambatan
     */
    public function scopeKeterlambatan($query)
    {
        return $query->where('jenis_potongan', 'keterlambatan');
    }

    /**
     * Scope untuk potongan pelanggaran
     */
    public function scopePelanggaran($query)
    {
        return $query->where('jenis_potongan', 'pelanggaran');
    }

    /**
     * Scope untuk potongan BPJS
     */
    public function scopeBpjs($query)
    {
        return $query->whereIn('jenis_potongan', ['bpjs_kesehatan', 'bpjs_tk']);
    }

    /**
     * Scope untuk potongan kasir
     */
    public function scopeKasir($query)
    {
        return $query->where('jenis_potongan', 'kasir');
    }

    /**
     * Scope untuk potongan stok opname
     */
    public function scopeStokOpname($query)
    {
        return $query->where('jenis_potongan', 'stok_opname');
    }

    /**
     * Scope untuk potongan retur
     */
    public function scopeRetur($query)
    {
        return $query->where('jenis_potongan', 'retur');
    }

    /**
     * Scope untuk potongan kasbon
     */
    public function scopeKasbon($query)
    {
        return $query->where('jenis_potongan', 'kasbon');
    }

    /**
     * Scope untuk potongan sakit tanpa surat
     */
    public function scopeSakitTanpaSurat($query)
    {
        return $query->where('jenis_potongan', 'sakit_tanpa_surat');
    }

    /**
     * Scope untuk potongan alpha
     */
    public function scopeAlpha($query)
    {
        return $query->where('jenis_potongan', 'alpha');
    }

    /**
     * Scope untuk potongan cuti melebihi kuota
     */
    public function scopeCutiMelebihiKuota($query)
    {
        return $query->where('jenis_potongan', 'cuti_melebihi_kuota');
    }

    /**
     * Accessor untuk format nominal
     */
    public function getFormattedNominalAttribute()
    {
        return 'Rp ' . number_format($this->nominal, 0, ',', '.');
    }

    /**
     * Accessor untuk badge jenis potongan
     */
    public function getJenisBadgeAttribute()
    {
        $badges = [
            'bpjs_kesehatan' => 'info',
            'bpjs_tk' => 'info',
            'keterlambatan' => 'warning',
            'pelanggaran' => 'danger',
            'kasir' => 'warning',
            'stok_opname' => 'info',
            'retur' => 'danger',
            'kasbon' => 'secondary',
            'sakit_tanpa_surat' => 'danger',
            'alpha' => 'danger',
            'cuti_melebihi_kuota' => 'warning',
            'lainnya' => 'secondary',
        ];

        return $badges[$this->jenis_potongan] ?? 'secondary';
    }

    /**
     * Accessor untuk label jenis potongan
     */
    public function getJenisLabelAttribute()
    {
        $labels = [
            'bpjs_kesehatan' => 'BPJS Kesehatan',
            'bpjs_tk' => 'BPJS Tenaga Kerja',
            'keterlambatan' => 'Keterlambatan',
            'pelanggaran' => 'Pelanggaran',
            'kasir' => 'Potongan Kasir',
            'stok_opname' => 'Potongan Stok Opname',
            'retur' => 'Potongan Retur',
            'kasbon' => 'Potongan Kasbon',
            'sakit_tanpa_surat' => 'Sakit Tanpa Surat',
            'alpha' => 'Alpha/Tidak Hadir',
            'cuti_melebihi_kuota' => 'Cuti Melebihi Kuota',
            'lainnya' => 'Lainnya',
        ];

        return $labels[$this->jenis_potongan] ?? $this->jenis_potongan;
    }

    /**
     * Get referensi object berdasarkan jenis potongan
     */
    public function getReferensiObjectAttribute()
    {
        if (!$this->kode_referensi) {
            return null;
        }

        switch ($this->jenis_potongan) {
            case 'keterlambatan':
                return Absensi::with([
                    'jadwal.shift:id,nama_shift',
                    'jadwal.entitas:id,nama',
                    'karyawan:id,nama_lengkap'
                ])->find($this->kode_referensi);

            case 'pelanggaran':
                return Pelanggaran::find($this->kode_referensi);

            case 'kasir':
            case 'stok_opname':
            case 'retur':
            case 'kasbon':
                return \App\Models\PotonganKaryawan::with([
                    'karyawan:id,nama_lengkap',
                    'creator:id,name'
                ])->find($this->kode_referensi);

            case 'sakit_tanpa_surat':
            case 'cuti_melebihi_kuota':
                return \App\Models\CutiIzin::with([
                    'karyawan:id,nama_lengkap',
                    'approvedBy:id,name'
                ])->find($this->kode_referensi);

            case 'alpha':
                return Absensi::with([
                    'jadwal.shift:id,nama_shift',
                    'jadwal.entitas:id,nama',
                    'karyawan:id,nama_lengkap'
                ])->find($this->kode_referensi);

            default:
                return null;
        }
    }
}
