<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Pelanggaran extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'karyawan_id',
        'jenis_pelanggaran_id',
        'jenis_manual', // renamed from 'jenis'
        'tanggal',
        'keterangan',
        'nominal_denda',
        'created_by'
    ];

    protected $casts = [
        'tanggal' => 'date',
        'nominal_denda' => 'decimal:2',
    ];

    /**
     * <PERSON><PERSON>i ke Karyawan
     */
    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class);
    }

    /**
     * <PERSON>lasi ke JenisPelanggaran
     */
    public function jenisPelanggaran()
    {
        return $this->belongsTo(JenisPelanggaran::class);
    }

    /**
     * Relasi ke User yang membuat
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Boot method untuk auto-calculate denda
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->calculateDenda();
        });

        static::updating(function ($model) {
            if ($model->isDirty('jenis_pelanggaran_id')) {
                $model->calculateDenda();
            }
        });
    }

    /**
     * Hitung nominal denda berdasarkan jenis pelanggaran
     */
    private function calculateDenda()
    {
        if ($this->jenisPelanggaran) {
            // Ambil gaji pokok karyawan dari penggajian terbaru
            $gajiPokok = 0;
            if ($this->karyawan) {
                $penggajianTerbaru = $this->karyawan->penggajian()->latest()->first();
                if ($penggajianTerbaru) {
                    $gajiPokok = $penggajianTerbaru->gaji_pokok;
                }
            }

            $this->nominal_denda = $this->jenisPelanggaran->hitungDenda($gajiPokok);
        }
    }

    /**
     * Accessor untuk jenis pelanggaran (backward compatibility)
     */
    public function getJenisAttribute()
    {
        return $this->jenisPelanggaran ? $this->jenisPelanggaran->nama_pelanggaran : $this->jenis_manual;
    }

    /**
     * Accessor untuk format nominal denda
     */
    public function getFormattedDendaAttribute()
    {
        return 'Rp ' . number_format($this->nominal_denda, 0, ',', '.');
    }

    /**
     * Scope untuk pelanggaran dalam periode tertentu
     */
    public function scopeInPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('tanggal', [$startDate, $endDate]);
    }

    /**
     * Scope untuk pelanggaran berdasarkan karyawan
     */
    public function scopeByKaryawan($query, $karyawanId)
    {
        return $query->where('karyawan_id', $karyawanId);
    }
}
