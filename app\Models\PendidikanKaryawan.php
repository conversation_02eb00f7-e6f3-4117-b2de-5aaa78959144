<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PendidikanKaryawan extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'pendidikan_karyawan';

    protected $fillable = [
        'karyawan_id',
        'tingkat',
        'institusi',
        'jurusan',
        'tahun_lulus'
    ];

    protected $dates = ['deleted_at'];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Add validation before saving
        static::saving(function ($model) {
            $exists = static::where('karyawan_id', $model->karyawan_id)
                ->where('tingkat', $model->tingkat)
                ->when($model->exists, function ($query) use ($model) {
                    return $query->where('id', '!=', $model->id);
                })
                ->exists();

            if ($exists) {
                // For API or direct model usage, still throw exception
                // The RelationManager will handle this with notifications
                throw new \Exception('Tingkat pendidikan ini sudah ada untuk karyawan ini.');
            }
        });
    }

    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class);
    }
}
