<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PenggajianKaryawan extends Model
{
    use HasFactory;

    protected $table = 'penggajian_karyawan';

    protected $fillable = [
        'karyawan_id',
        'no_penggajian',
        'periode_gaji',
        'gaji_pokok',
        'tunjangan_jabatan',
        'tunjangan_umum',
        'tunjangan_sembako',
        'bpjs_kesehatan_dipotong',
        'bpjs_tk_dipotong',
        'potongan_lainnya',
        'take_home_pay',
        'keterangan',
    ];

    protected $casts = [
        'gaji_pokok' => 'float',
        'tunjangan_jabatan' => 'float',
        'tunjangan_umum' => 'float',
        'tunjangan_sembako' => 'float',
        'bpjs_kesehatan_dipotong' => 'float',
        'bpjs_tk_dipotong' => 'float',
        'potongan_lainnya' => 'float',
        'take_home_pay' => 'float',
    ];

    /**
     * Relasi ke Karyawan
     */
    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class);
    }

    /**
     * Perhitungan Take Home Pay
     */
    public function calculateTakeHomePay(): float
    {
        $totalGaji = $this->gaji_pokok + $this->tunjangan_jabatan + $this->tunjangan_umum + $this->tunjangan_sembako;
        $totalPotongan = $this->bpjs_kesehatan_dipotong + $this->bpjs_tk_dipotong + $this->potongan_lainnya;

        return max(0, $totalGaji - $totalPotongan);
    }

    /**
     * Boot method untuk update otomatis `take_home_pay` dan `no_penggajian`
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->take_home_pay = $model->calculateTakeHomePay();

            if (!$model->no_penggajian) {
                $latestId = self::max('id') + 1;
                $prefix = config('app_constants.defaults.payroll_number_prefix', 'PG');
                $padding = config('app_constants.defaults.payroll_number_padding', 6);
                $model->no_penggajian = $prefix . '-' . str_pad($latestId, $padding, '0', STR_PAD_LEFT);
            }
        });

        static::updating(function ($model) {
            $model->take_home_pay = $model->calculateTakeHomePay();
        });
    }

    /**
     * Accessor untuk format take home pay
     */
    public function getFormattedTakeHomePayAttribute()
    {
        return number_format($this->take_home_pay, 2, ',', '.');
    }
}
