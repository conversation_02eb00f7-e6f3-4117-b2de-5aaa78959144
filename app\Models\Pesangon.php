<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Pesangon extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'pesangons';

    protected $fillable = [
        'karyawan_id',
        'periode_pembayaran',
        'jenis_pesangon',
        'nominal_pesangon',
        'keterangan',
        'status',
        'tanggal_pembayaran',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'periode_pembayaran' => 'date',
        'nominal_pesangon' => 'decimal:2',
        'tanggal_pembayaran' => 'date',
        'approved_at' => 'datetime',
    ];

    /**
     * Relasi ke Karyawan
     */
    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class);
    }

    /**
     * Relasi ke User yang membuat
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relasi ke User yang approve
     */
    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope untuk status tertentu
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope untuk periode tertentu
     */
    public function scopeByPeriode($query, $periode)
    {
        return $query->where('periode_pembayaran', $periode);
    }

    /**
     * Scope untuk karyawan tertentu
     */
    public function scopeByKaryawan($query, $karyawanId)
    {
        return $query->where('karyawan_id', $karyawanId);
    }

    /**
     * Scope untuk pesangon yang belum dibayar
     */
    public function scopeBelumDibayar($query)
    {
        return $query->whereIn('status', ['pending', 'approved']);
    }

    /**
     * Scope untuk pesangon yang sudah dibayar
     */
    public function scopeSudahDibayar($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Get formatted nominal
     */
    public function getFormattedNominalAttribute()
    {
        return 'Rp ' . number_format($this->nominal_pesangon, 0, ',', '.');
    }

    /**
     * Get formatted periode
     */
    public function getFormattedPeriodeAttribute()
    {
        return $this->periode_pembayaran->format('F Y');
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute()
    {
        return match ($this->status) {
            'pending' => 'warning',
            'approved' => 'info',
            'paid' => 'success',
            default => 'gray',
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute()
    {
        return match ($this->status) {
            'pending' => 'Menunggu Persetujuan',
            'approved' => 'Disetujui',
            'paid' => 'Sudah Dibayar',
            default => $this->status,
        };
    }

    /**
     * Get jenis pesangon label
     */
    public function getJenisPesangonLabelAttribute()
    {
        return match ($this->jenis_pesangon) {
            'phk' => 'PHK',
            'pensiun' => 'Pensiun',
            'resign' => 'Resign',
            'kontrak_habis' => 'Kontrak Habis',
            'lainnya' => 'Lainnya',
            default => $this->jenis_pesangon,
        };
    }
}
