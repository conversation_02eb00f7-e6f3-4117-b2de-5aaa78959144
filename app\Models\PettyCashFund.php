<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PettyCashFund extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'petty_cash_funds';

    protected $fillable = [
        'fund_name',
        'fund_code',
        'entitas_id',
        'custodian_employee_id',
        'cash_account_id',
        'initial_amount',
        'current_balance',
        'maximum_amount',
        'minimum_balance',
        'is_active',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    protected $casts = [
        'initial_amount' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'maximum_amount' => 'decimal:2',
        'minimum_balance' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function entitas()
    {
        return $this->belongsTo(Entitas::class);
    }

    public function custodianEmployee()
    {
        return $this->belongsTo(Karyawan::class, 'custodian_employee_id');
    }

    public function cashAccount()
    {
        return $this->belongsTo(Akun::class, 'cash_account_id');
    }

    public function pettyCashTransactions()
    {
        return $this->hasMany(PettyCashTransaction::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByEntitas($query, $entitasId)
    {
        return $query->where('entitas_id', $entitasId);
    }

    public function scopeLowBalance($query)
    {
        return $query->whereColumn('current_balance', '<=', 'minimum_balance');
    }

    // Helper methods
    public function getDisplayNameAttribute()
    {
        return $this->fund_code . ' - ' . $this->fund_name;
    }

    public function getFormattedCurrentBalanceAttribute()
    {
        return 'Rp ' . number_format($this->current_balance, 0, ',', '.');
    }

    public function getFormattedMaximumAmountAttribute()
    {
        return 'Rp ' . number_format($this->maximum_amount, 0, ',', '.');
    }

    public function getFormattedMinimumBalanceAttribute()
    {
        return 'Rp ' . number_format($this->minimum_balance, 0, ',', '.');
    }

    public function getBalanceStatusAttribute()
    {
        if ($this->current_balance <= $this->minimum_balance) {
            return 'Low';
        } elseif ($this->current_balance >= $this->maximum_amount * 0.8) {
            return 'High';
        } else {
            return 'Normal';
        }
    }

    public function getBalanceStatusColorAttribute()
    {
        return match($this->balance_status) {
            'Low' => 'danger',
            'High' => 'warning',
            'Normal' => 'success',
            default => 'gray'
        };
    }

    public function getBalancePercentageAttribute()
    {
        if ($this->maximum_amount == 0) return 0;
        
        return round(($this->current_balance / $this->maximum_amount) * 100, 2);
    }

    public function needsReplenishment()
    {
        return $this->current_balance <= $this->minimum_balance;
    }

    public function getReplenishmentAmountAttribute()
    {
        if (!$this->needsReplenishment()) return 0;
        
        return $this->maximum_amount - $this->current_balance;
    }

    public function canDisbursement($amount)
    {
        return $this->is_active && $this->current_balance >= $amount;
    }

    public function disbursement($amount, $description, $categoryId = null, $accountId = null)
    {
        if (!$this->canDisbursement($amount)) {
            throw new \Exception('Insufficient petty cash balance');
        }

        $transaction = PettyCashTransaction::create([
            'petty_cash_fund_id' => $this->id,
            'transaction_type' => 'Disbursement',
            'amount' => $amount,
            'description' => $description,
            'expense_category_id' => $categoryId,
            'account_id' => $accountId,
            'status' => 'Completed',
            'created_by' => auth()->id(),
        ]);

        $this->current_balance -= $amount;
        $this->save();

        return $transaction;
    }

    public function replenishment($amount, $description = null)
    {
        $transaction = PettyCashTransaction::create([
            'petty_cash_fund_id' => $this->id,
            'transaction_type' => 'Replenishment',
            'amount' => $amount,
            'description' => $description ?: 'Petty cash replenishment',
            'account_id' => $this->cash_account_id,
            'status' => 'Completed',
            'created_by' => auth()->id(),
        ]);

        $this->current_balance += $amount;
        $this->save();

        return $transaction;
    }

    public function getTotalDisbursementsAttribute()
    {
        return $this->pettyCashTransactions()
                   ->where('transaction_type', 'Disbursement')
                   ->sum('amount');
    }

    public function getTotalReplenishmentsAttribute()
    {
        return $this->pettyCashTransactions()
                   ->where('transaction_type', 'Replenishment')
                   ->sum('amount');
    }

    public function getMonthlyDisbursementsAttribute()
    {
        return $this->pettyCashTransactions()
                   ->where('transaction_type', 'Disbursement')
                   ->whereMonth('created_at', now()->month)
                   ->whereYear('created_at', now()->year)
                   ->sum('amount');
    }

    public function getMonthlyReplenishmentsAttribute()
    {
        return $this->pettyCashTransactions()
                   ->where('transaction_type', 'Replenishment')
                   ->whereMonth('created_at', now()->month)
                   ->whereYear('created_at', now()->year)
                   ->sum('amount');
    }
}
