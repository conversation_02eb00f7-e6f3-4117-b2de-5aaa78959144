<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class PettyCashTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'petty_cash_transactions';

    protected $fillable = [
        'transaction_number',
        'transaction_date',
        'petty_cash_fund_id',
        'transaction_type',
        'amount',
        'description',
        'recipient_name',
        'receipt_number',
        'expense_category_id',
        'account_id',
        'status',
        'notes',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'transaction_date'];

    protected $casts = [
        'transaction_date' => 'date',
        'amount' => 'decimal:2',
    ];

    // Relationships
    public function pettyCashFund()
    {
        return $this->belongsTo(PettyCashFund::class);
    }

    public function expenseCategory()
    {
        return $this->belongsTo(ExpenseCategory::class);
    }

    public function account()
    {
        return $this->belongsTo(Akun::class, 'account_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeByTransactionType($query, $type)
    {
        return $query->where('transaction_type', $type);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeDisbursements($query)
    {
        return $query->where('transaction_type', 'Disbursement');
    }

    public function scopeReplenishments($query)
    {
        return $query->where('transaction_type', 'Replenishment');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'Completed');
    }

    // Helper methods
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'Draft' => 'gray',
            'Completed' => 'success',
            'Cancelled' => 'danger',
            default => 'gray'
        };
    }

    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'Draft' => 'Draft',
            'Completed' => 'Selesai',
            'Cancelled' => 'Dibatalkan',
            default => $this->status
        };
    }

    public function getTransactionTypeLabelAttribute()
    {
        return match($this->transaction_type) {
            'Disbursement' => 'Pengeluaran',
            'Replenishment' => 'Pengisian',
            default => $this->transaction_type
        };
    }

    public function getTransactionTypeColorAttribute()
    {
        return match($this->transaction_type) {
            'Disbursement' => 'danger',
            'Replenishment' => 'success',
            default => 'gray'
        };
    }

    public function isDisbursement()
    {
        return $this->transaction_type === 'Disbursement';
    }

    public function isReplenishment()
    {
        return $this->transaction_type === 'Replenishment';
    }

    public function getFormattedTransactionDateAttribute()
    {
        return $this->transaction_date ? $this->transaction_date->format('d/m/Y') : null;
    }

    public function getFormattedAmountAttribute()
    {
        return 'Rp ' . number_format($this->amount, 0, ',', '.');
    }

    public function getAmountWithSignAttribute()
    {
        $sign = $this->isDisbursement() ? '-' : '+';
        return $sign . $this->formatted_amount;
    }

    public function getCategoryNameAttribute()
    {
        return $this->expenseCategory ? $this->expenseCategory->name : 'Uncategorized';
    }

    public function getAccountNameAttribute()
    {
        return $this->account ? $this->account->nama_akun : 'No Account';
    }

    public function isEditable()
    {
        return $this->status === 'Draft';
    }

    public function canBeCompleted()
    {
        return $this->status === 'Draft';
    }

    public function canBeCancelled()
    {
        return $this->status === 'Draft';
    }

    public function complete()
    {
        if (!$this->canBeCompleted()) {
            throw new \Exception('Transaction cannot be completed');
        }

        $this->status = 'Completed';
        $this->save();

        // Update petty cash fund balance
        if ($this->isDisbursement()) {
            $this->pettyCashFund->current_balance -= $this->amount;
        } else {
            $this->pettyCashFund->current_balance += $this->amount;
        }
        $this->pettyCashFund->save();

        // Create journal entry
        $this->createJournalEntry();
    }

    protected function createJournalEntry()
    {
        // This will be implemented when we create the PostingRuleEngine
        // Logic depends on transaction type:
        // Disbursement: Dr. Expense Account, Cr. Petty Cash Account
        // Replenishment: Dr. Petty Cash Account, Cr. Cash/Bank Account
        // Will be handled by PostingRuleEngine based on source_type = 'PettyCashTransaction'
    }

    // Auto-generate transaction number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (empty($transaction->transaction_number)) {
                $transaction->transaction_number = static::generateTransactionNumber();
            }
            
            if (empty($transaction->transaction_date)) {
                $transaction->transaction_date = Carbon::now();
            }
        });
    }

    public static function generateTransactionNumber()
    {
        $prefix = 'PC';
        $date = Carbon::now()->format('Ymd');
        $lastTransaction = static::whereDate('created_at', Carbon::today())
                                ->where('transaction_number', 'like', $prefix . $date . '%')
                                ->orderBy('transaction_number', 'desc')
                                ->first();

        if ($lastTransaction) {
            $lastNumber = intval(substr($lastTransaction->transaction_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . $date . $newNumber;
    }
}
