<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PosTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'transaction_number',
        'customer_id',
        'user_id',
        'outlet_id',
        'transaction_date',
        'total_amount',
        'discount_amount',
        'tax_amount',
        'net_amount',
        'payment_method',
        'amount_paid',
        'change_given',
        'loyalty_points_used',
        'loyalty_points_earned',
        'table_number',
        'is_offline_transaction',
        'synced_at',
    ];

    protected $casts = [
        'transaction_date' => 'datetime',
        'total_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'change_given' => 'decimal:2',
        'loyalty_points_used' => 'integer',
        'loyalty_points_earned' => 'integer',
        'is_offline_transaction' => 'boolean',
        'synced_at' => 'datetime',
    ];

    protected $dates = ['deleted_at'];

    /**
     * Boot method untuk auto-generate transaction number
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (empty($transaction->transaction_number)) {
                $transaction->transaction_number = static::generateTransactionNumber();
            }

            if (empty($transaction->transaction_date)) {
                $transaction->transaction_date = now();
            }
        });

        static::created(function ($transaction) {
            // Auto-create loyalty transaction if customer and points earned
            if ($transaction->customer_id && $transaction->loyalty_points_earned > 0) {
                LoyaltyTransaction::createEarnTransaction(
                    $transaction->customer_id,
                    $transaction->loyalty_points_earned,
                    'Pembelian POS: ' . $transaction->transaction_number,
                    $transaction->id,
                    'pos_transaction'
                );
            }
        });
    }

    /**
     * Generate unique transaction number
     */
    public static function generateTransactionNumber(): string
    {
        $prefix = 'POS';
        $date = now()->format('Ymd');
        $lastTransaction = static::whereDate('created_at', now()->toDateString())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastTransaction ? (int) substr($lastTransaction->transaction_number, -4) + 1 : 1;

        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Relasi ke Customer
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Relasi ke User (Kasir)
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relasi ke Outlet
     */
    public function outlet()
    {
        return $this->belongsTo(Outlet::class);
    }

    /**
     * Relasi ke PosTransactionItem
     */
    public function posTransactionItems()
    {
        return $this->hasMany(PosTransactionItem::class);
    }

    /**
     * Alias for posTransactionItems for easier access
     */
    public function items()
    {
        return $this->hasMany(PosTransactionItem::class);
    }

    /**
     * Calculate totals from items
     */
    public function calculateTotals()
    {
        $totalAmount = $this->posTransactionItems()->sum('total_price');
        $netAmount = $totalAmount - $this->discount_amount + $this->tax_amount;

        $this->update([
            'total_amount' => $totalAmount,
            'net_amount' => $netAmount,
        ]);
    }

    /**
     * Calculate loyalty points earned using active loyalty program
     */
    public function calculateLoyaltyPoints(): int
    {
        // Get active loyalty program
        $loyaltyProgram = LoyaltyProgram::getActive();

        if (!$loyaltyProgram) {
            // Fallback to config-based calculation
            return $this->calculateLoyaltyPointsFromConfig();
        }

        return $loyaltyProgram->calculatePoints(
            $this->net_amount,
            $this->customer,
            $this->transaction_date
        );
    }

    /**
     * Fallback calculation using config
     */
    private function calculateLoyaltyPointsFromConfig(): int
    {
        // Check minimum transaction
        if ($this->net_amount < config('loyalty.minimum_transaction', 10000)) {
            return 0;
        }

        // Base calculation
        $pointsPerAmount = config('loyalty.points_per_amount', 1000);
        $basePoints = (int) floor($this->net_amount / $pointsPerAmount);

        // Apply base multiplier
        $multiplier = config('loyalty.points_multiplier', 1);
        $finalPoints = $basePoints * $multiplier;

        // Customer tier bonus
        if ($this->customer) {
            $tierMultiplier = $this->getCustomerTierMultiplier();
            $finalPoints = (int) ($finalPoints * $tierMultiplier);
        }

        // Time-based multipliers
        $timeMultiplier = $this->getTimeBasedMultiplier();
        $finalPoints = (int) ($finalPoints * $timeMultiplier);

        // Day-based multipliers (double points on weekends)
        if ($this->isDoublePointsDay()) {
            $finalPoints *= 2;
        }

        // Birthday bonus
        if ($this->customer && $this->isCustomerBirthday()) {
            $finalPoints += config('loyalty.birthday_bonus_points', 100);
        }

        // Apply maximum limit
        $maxPoints = config('loyalty.max_points_per_transaction', 1000);
        return min($finalPoints, $maxPoints);
    }

    /**
     * Get payment method label
     */
    public function getPaymentMethodLabelAttribute(): string
    {
        return match ($this->payment_method) {
            'cash' => 'Tunai',
            'card' => 'Kartu',
            'transfer' => 'Transfer',
            'ewallet' => 'E-Wallet',
            'qris' => 'QRIS',
            default => $this->payment_method,
        };
    }

    /**
     * Get payment method color
     */
    public function getPaymentMethodColorAttribute(): string
    {
        return match ($this->payment_method) {
            'cash' => 'success',
            'card' => 'info',
            'transfer' => 'warning',
            'ewallet' => 'primary',
            'qris' => 'secondary',
            default => 'gray',
        };
    }

    /**
     * Check if transaction is F&B
     */
    public function isFnbTransaction(): bool
    {
        return $this->posTransactionItems()
            ->whereHas('product', function ($query) {
                $query->where('is_food_item', true);
            })
            ->exists();
    }

    /**
     * Scope untuk transaksi hari ini
     */
    public function scopeToday($query)
    {
        return $query->whereDate('transaction_date', now()->toDateString());
    }

    /**
     * Scope untuk transaksi bulan ini
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('transaction_date', now()->month)
            ->whereYear('transaction_date', now()->year);
    }

    /**
     * Scope untuk transaksi F&B
     */
    public function scopeFnb($query)
    {
        return $query->whereHas('posTransactionItems.product', function ($q) {
            $q->where('is_food_item', true);
        });
    }

    /**
     * Scope untuk transaksi offline
     */
    public function scopeOffline($query)
    {
        return $query->where('is_offline_transaction', true);
    }

    /**
     * Scope untuk transaksi yang belum tersinkron
     */
    public function scopeUnsynced($query)
    {
        return $query->where('is_offline_transaction', true)
            ->whereNull('synced_at');
    }

    /**
     * Get customer tier multiplier
     */
    private function getCustomerTierMultiplier(): float
    {
        if (!$this->customer) return 1.0;

        $customerPoints = $this->customer->loyalty_points ?? 0;
        $tiers = config('loyalty.customer_tiers', []);

        foreach (array_reverse($tiers) as $tier => $config) {
            if ($customerPoints >= $config['min_points']) {
                return $config['multiplier'];
            }
        }

        return 1.0;
    }

    /**
     * Get time-based multiplier
     */
    private function getTimeBasedMultiplier(): float
    {
        $currentTime = $this->transaction_date->format('H:i');
        $timeMultipliers = config('loyalty.time_multipliers', []);

        foreach ($timeMultipliers as $period => $config) {
            if ($currentTime >= $config['start'] && $currentTime <= $config['end']) {
                return $config['multiplier'];
            }
        }

        return 1.0;
    }

    /**
     * Check if today is double points day
     */
    private function isDoublePointsDay(): bool
    {
        $dayName = strtolower($this->transaction_date->format('l'));
        $doublePointsDays = config('loyalty.double_points_days', []);

        return in_array($dayName, $doublePointsDays);
    }

    /**
     * Check if today is customer's birthday
     */
    private function isCustomerBirthday(): bool
    {
        if (!$this->customer || !$this->customer->tanggal_lahir) {
            return false;
        }

        $today = $this->transaction_date->format('m-d');
        $birthday = $this->customer->tanggal_lahir->format('m-d');

        return $today === $birthday;
    }
}
