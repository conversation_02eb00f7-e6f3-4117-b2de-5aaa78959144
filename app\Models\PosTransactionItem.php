<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PosTransactionItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'pos_transaction_id',
        'product_id',
        'quantity',
        'unit_price',
        'discount_per_item',
        'total_price',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'discount_per_item' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    /**
     * Boot method untuk auto-calculate total price
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->total_price = $item->quantity * ($item->unit_price - $item->discount_per_item);
        });

        static::saved(function ($item) {
            $item->posTransaction->calculateTotals();
        });

        static::deleted(function ($item) {
            $item->posTransaction->calculateTotals();
        });
    }

    /**
     * Relasi ke PosTransaction
     */
    public function posTransaction()
    {
        return $this->belongsTo(PosTransaction::class);
    }

    /**
     * Relasi ke Product
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get net unit price (after discount)
     */
    public function getNetUnitPriceAttribute(): float
    {
        return $this->unit_price - $this->discount_per_item;
    }

    /**
     * Get total discount for this item
     */
    public function getTotalDiscountAttribute(): float
    {
        return $this->quantity * $this->discount_per_item;
    }

    /**
     * Get formatted unit price
     */
    public function getFormattedUnitPriceAttribute(): string
    {
        return 'Rp ' . number_format($this->unit_price, 0, ',', '.');
    }

    /**
     * Get formatted total price
     */
    public function getFormattedTotalPriceAttribute(): string
    {
        return 'Rp ' . number_format($this->total_price, 0, ',', '.');
    }

    /**
     * Get formatted discount per item
     */
    public function getFormattedDiscountPerItemAttribute(): string
    {
        return 'Rp ' . number_format($this->discount_per_item, 0, ',', '.');
    }
}
