<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PostingRule extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'posting_rules';

    protected $fillable = [
        'rule_name',
        'source_type',
        'trigger_condition',
        'description',
        'conditions',
        'is_conditional',
        'is_active',
        'priority',
        'effective_date',
        'end_date',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'effective_date', 'end_date'];

    protected $casts = [
        'trigger_condition' => 'array',
        'conditions' => 'array',
        'is_conditional' => 'boolean',
        'is_active' => 'boolean',
        'priority' => 'integer',
        'effective_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    // Relationships
    public function postingRuleEntries()
    {
        return $this->hasMany(PostingRuleEntry::class)->orderBy('sort_order');
    }

    public function postingRuleConditions()
    {
        return $this->hasMany(PostingRuleCondition::class);
    }

    public function postingRuleMappings()
    {
        return $this->hasMany(PostingRuleMapping::class)->orderBy('sequence');
    }

    public function postingRuleLogs()
    {
        return $this->hasMany(PostingRuleLog::class);
    }

    public function journalEntries()
    {
        return $this->hasMany(JournalEntry::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function journals()
    {
        return $this->hasMany(Journal::class, 'posting_rule_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeBySourceType($query, $sourceType)
    {
        return $query->where('source_type', $sourceType);
    }

    public function scopeOrderedByPriority($query)
    {
        return $query->orderBy('priority', 'asc');
    }

    // Helper methods
    public function evaluateCondition($sourceModel)
    {
        if (empty($this->trigger_condition)) {
            return true; // Jika tidak ada kondisi, selalu true
        }

        foreach ($this->trigger_condition as $field => $expectedValue) {
            $actualValue = data_get($sourceModel, $field);

            if ($actualValue != $expectedValue) {
                return false;
            }
        }

        return true;
    }

    public function getSourceTypeOptions()
    {
        return [
            'Sale' => 'Penjualan',
            'Purchase' => 'Pembelian',
            'Payment' => 'Pembayaran',
            'Receipt' => 'Penerimaan',
            'ManualAdjust' => 'Penyesuaian Manual',
            'Expense' => 'Beban',
            'Revenue' => 'Pendapatan',
        ];
    }
}
