<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PostingRuleCondition extends Model
{
    use HasFactory;

    protected $table = 'posting_rule_conditions';

    protected $fillable = [
        'posting_rule_id',
        'field_name',
        'operator',
        'value',
        'logical_operator',
        'group_number',
    ];

    protected $casts = [
        'group_number' => 'integer',
    ];

    // Relationships
    public function postingRule()
    {
        return $this->belongsTo(PostingRule::class);
    }

    // Helper methods
    public function getDecodedValueAttribute()
    {
        return json_decode($this->value, true) ?? $this->value;
    }

    public function getOperatorLabelAttribute()
    {
        return match($this->operator) {
            '=' => 'Equals',
            '!=' => 'Not Equals',
            '>' => 'Greater Than',
            '>=' => 'Greater Than or Equal',
            '<' => 'Less Than',
            '<=' => 'Less Than or Equal',
            'in' => 'In',
            'not_in' => 'Not In',
            'between' => 'Between',
            'contains' => 'Contains',
            'starts_with' => 'Starts With',
            'ends_with' => 'Ends With',
            'is_null' => 'Is Null',
            'is_not_null' => 'Is Not Null',
            default => $this->operator
        };
    }

    public function getLogicalOperatorLabelAttribute()
    {
        return match($this->logical_operator) {
            'AND' => 'AND',
            'OR' => 'OR',
            default => $this->logical_operator
        };
    }

    // Scopes
    public function scopeByPostingRule($query, $postingRuleId)
    {
        return $query->where('posting_rule_id', $postingRuleId);
    }

    public function scopeByGroup($query, $groupNumber)
    {
        return $query->where('group_number', $groupNumber);
    }

    public function scopeByField($query, $fieldName)
    {
        return $query->where('field_name', $fieldName);
    }
}
