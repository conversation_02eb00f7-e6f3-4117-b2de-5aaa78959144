<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Exception;

class PostingRuleEntry extends Model
{
    use HasFactory;

    protected $table = 'posting_rule_entries';

    protected $fillable = [
        'posting_rule_id',
        'account_id',
        'dc_type',
        'amount_type',
        'fixed_amount',
        'source_property',
        'calculation_expression',
        'description_template',
        'sort_order',
    ];

    protected $casts = [
        'fixed_amount' => 'decimal:2',
        'sort_order' => 'integer',
    ];

    // Relationships
    public function postingRule()
    {
        return $this->belongsTo(PostingRule::class);
    }

    public function account()
    {
        return $this->belongsTo(Akun::class, 'account_id');
    }

    // Helper methods
    public function calculateAmount($sourceModel)
    {
        switch ($this->amount_type) {
            case 'Fixed':
                return $this->fixed_amount;

            case 'SourceValue':
                return data_get($sourceModel, $this->source_property, 0);

            case 'Calculated':
                return $this->evaluateExpression($sourceModel);

            default:
                return 0;
        }
    }

    public function generateDescription($sourceModel)
    {
        $description = $this->description_template;

        // Replace placeholders like {source.property} with actual values
        $description = preg_replace_callback('/\{([^}]+)\}/', function ($matches) use ($sourceModel) {
            $property = $matches[1];
            return data_get($sourceModel, $property, $matches[0]);
        }, $description);

        return $description;
    }

    private function evaluateExpression($sourceModel)
    {
        $expression = $this->calculation_expression;

        // Handle special cases for sale items calculations
        if (strpos($expression, 'sale_items.sum(') !== false) {
            return $this->evaluateSaleItemsSum($sourceModel, $expression);
        }

        // Replace source.property dengan nilai aktual
        $expression = preg_replace_callback('/source\.([a-zA-Z_][a-zA-Z0-9_.]*)/', function ($matches) use ($sourceModel) {
            $property = $matches[1];
            return data_get($sourceModel, $property, 0);
        }, $expression);

        // Evaluasi ekspresi matematika sederhana
        // PERINGATAN: eval() berbahaya untuk production, gunakan library parser yang aman
        try {
            // Hanya izinkan operasi matematika dasar
            if (preg_match('/^[0-9+\-*\/\.\(\)\s]+$/', $expression)) {
                return eval("return $expression;");
            }
        } catch (Exception $e) {
            // Log error dan return 0
            Log::error('Error evaluating expression: ' . $expression, ['error' => $e->getMessage()]);
        }

        return 0;
    }

    private function evaluateSaleItemsSum($sourceModel, $expression)
    {
        // Handle expressions like: sale_items.sum(quantity * unit_cost)
        if (preg_match('/sale_items\.sum\(([^)]+)\)/', $expression, $matches)) {
            $sumExpression = $matches[1];
            $total = 0;

            if (method_exists($sourceModel, 'saleItems')) {
                foreach ($sourceModel->saleItems as $item) {
                    // Replace item properties in the expression
                    $itemExpression = $sumExpression;
                    $itemExpression = str_replace('quantity', $item->quantity, $itemExpression);
                    $itemExpression = str_replace('unit_cost', $item->unit_cost, $itemExpression);
                    $itemExpression = str_replace('unit_price', $item->unit_price, $itemExpression);
                    $itemExpression = str_replace('total_cost', $item->total_cost, $itemExpression);
                    $itemExpression = str_replace('total_price', $item->total_price, $itemExpression);

                    // Evaluate the expression for this item
                    try {
                        if (preg_match('/^[0-9+\-*\/\.\(\)\s]+$/', $itemExpression)) {
                            $total += eval("return $itemExpression;");
                        }
                    } catch (Exception $e) {
                        Log::error('Error evaluating sale item expression: ' . $itemExpression, ['error' => $e->getMessage()]);
                    }
                }
            }

            return $total;
        }

        return 0;
    }

    public function getAmountTypeOptions()
    {
        return [
            'Fixed' => 'Jumlah Tetap',
            'SourceValue' => 'Nilai dari Source',
            'Calculated' => 'Perhitungan',
        ];
    }

    public function getDcTypeOptions()
    {
        return [
            'Debit' => 'Debit',
            'Credit' => 'Credit',
        ];
    }
}
