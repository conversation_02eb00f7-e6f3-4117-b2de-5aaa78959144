<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PostingRuleLog extends Model
{
    use HasFactory;

    protected $table = 'posting_rule_logs';

    protected $fillable = [
        'posting_rule_id',
        'source_type',
        'source_id',
        'action',
        'source_data',
        'conditions_result',
        'journal_id',
        'error_message',
        'executed_at',
        'executed_by',
    ];

    protected $dates = ['executed_at'];

    protected $casts = [
        'source_data' => 'array',
        'conditions_result' => 'array',
        'executed_at' => 'datetime',
    ];

    // Relationships
    public function postingRule()
    {
        return $this->belongsTo(PostingRule::class);
    }

    public function journal()
    {
        return $this->belongsTo(Journal::class, 'journal_id');
    }

    public function executedBy()
    {
        return $this->belongsTo(User::class, 'executed_by');
    }

    public function sourceModel()
    {
        return $this->morphTo('source', 'source_type', 'source_id');
    }

    // Helper methods
    public function getActionLabelAttribute()
    {
        return match ($this->action) {
            'executed' => 'Executed',
            'failed' => 'Failed',
            'skipped' => 'Skipped',
            default => $this->action
        };
    }

    public function getActionColorAttribute()
    {
        return match ($this->action) {
            'executed' => 'success',
            'failed' => 'danger',
            'skipped' => 'warning',
            default => 'gray'
        };
    }

    public function getFormattedExecutedAtAttribute()
    {
        return $this->executed_at ? $this->executed_at->format('d/m/Y H:i:s') : null;
    }

    public function getSourceTypeDisplayAttribute()
    {
        return str_replace('App\\Models\\', '', $this->source_type);
    }

    public function isSuccessful()
    {
        return $this->action === 'executed';
    }

    public function isFailed()
    {
        return $this->action === 'failed';
    }

    public function isSkipped()
    {
        return $this->action === 'skipped';
    }

    // Scopes
    public function scopeByPostingRule($query, $postingRuleId)
    {
        return $query->where('posting_rule_id', $postingRuleId);
    }

    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    public function scopeBySourceType($query, $sourceType)
    {
        return $query->where('source_type', $sourceType);
    }

    public function scopeSuccessful($query)
    {
        return $query->where('action', 'executed');
    }

    public function scopeFailed($query)
    {
        return $query->where('action', 'failed');
    }

    public function scopeSkipped($query)
    {
        return $query->where('action', 'skipped');
    }

    public function scopeRecent($query, $days = 7)
    {
        return $query->where('executed_at', '>=', now()->subDays($days));
    }
}
