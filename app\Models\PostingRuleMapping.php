<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PostingRuleMapping extends Model
{
    use HasFactory;

    protected $table = 'posting_rule_mappings';

    protected $fillable = [
        'posting_rule_id',
        'mapping_type',
        'account_source',
        'account_field',
        'account_id',
        'amount_source',
        'amount_field',
        'fixed_amount',
        'calculation_formula',
        'description_template',
        'sequence',
    ];

    protected $casts = [
        'fixed_amount' => 'decimal:2',
        'sequence' => 'integer',
    ];

    // Relationships
    public function postingRule()
    {
        return $this->belongsTo(PostingRule::class);
    }

    public function account()
    {
        return $this->belongsTo(Akun::class, 'account_id');
    }

    // Helper methods
    public function getMappingTypeLabelAttribute()
    {
        return match($this->mapping_type) {
            'debit' => 'Debit',
            'credit' => 'Credit',
            default => $this->mapping_type
        };
    }

    public function getMappingTypeColorAttribute()
    {
        return match($this->mapping_type) {
            'debit' => 'success',
            'credit' => 'danger',
            default => 'gray'
        };
    }

    public function getAccountSourceLabelAttribute()
    {
        return match($this->account_source) {
            'fixed' => 'Fixed Account',
            'field' => 'Field Value',
            'lookup' => 'Lookup',
            default => $this->account_source
        };
    }

    public function getAmountSourceLabelAttribute()
    {
        return match($this->amount_source) {
            'fixed' => 'Fixed Amount',
            'field' => 'Field Value',
            'calculation' => 'Calculation',
            default => $this->amount_source
        };
    }

    public function getFormattedFixedAmountAttribute()
    {
        return $this->fixed_amount ? 'Rp ' . number_format($this->fixed_amount, 0, ',', '.') : null;
    }

    public function getAccountDisplayAttribute()
    {
        if ($this->account_source === 'fixed' && $this->account) {
            return $this->account->kode_akun . ' - ' . $this->account->nama_akun;
        } elseif ($this->account_source === 'field') {
            return 'Field: ' . $this->account_field;
        } elseif ($this->account_source === 'lookup') {
            return 'Lookup';
        }
        
        return 'Not configured';
    }

    public function getAmountDisplayAttribute()
    {
        if ($this->amount_source === 'fixed') {
            return $this->formatted_fixed_amount;
        } elseif ($this->amount_source === 'field') {
            return 'Field: ' . $this->amount_field;
        } elseif ($this->amount_source === 'calculation') {
            return 'Formula: ' . $this->calculation_formula;
        }
        
        return 'Not configured';
    }

    // Scopes
    public function scopeByPostingRule($query, $postingRuleId)
    {
        return $query->where('posting_rule_id', $postingRuleId);
    }

    public function scopeByMappingType($query, $type)
    {
        return $query->where('mapping_type', $type);
    }

    public function scopeDebit($query)
    {
        return $query->where('mapping_type', 'debit');
    }

    public function scopeCredit($query)
    {
        return $query->where('mapping_type', 'credit');
    }

    public function scopeOrderedBySequence($query)
    {
        return $query->orderBy('sequence');
    }
}
