<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PotonganKaryawan extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'potongan_karyawan';

    protected $fillable = [
        'karyawan_id',
        'jenis_potongan',
        'nominal',
        'bulan_potongan',
        'keterangan',
        'created_by'
    ];

    protected $casts = [
        'nominal' => 'decimal:2',
        'bulan_potongan' => 'date',
    ];

    /**
     * <PERSON>lasi ke Karyawan
     */
    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class);
    }

    /**
     * <PERSON><PERSON>i ke User yang membuat
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope berdasarkan jenis potongan
     */
    public function scopeByJenis($query, $jenis)
    {
        return $query->where('jenis_potongan', $jenis);
    }

    /**
     * Scope berdasarkan bulan potongan
     */
    public function scopeByBulan($query, $bulan)
    {
        return $query->where('bulan_potongan', $bulan);
    }

    /**
     * Scope untuk potongan kasir
     */
    public function scopeKasir($query)
    {
        return $query->where('jenis_potongan', 'kasir');
    }

    /**
     * Scope untuk potongan stok opname
     */
    public function scopeStokOpname($query)
    {
        return $query->where('jenis_potongan', 'stok_opname');
    }

    /**
     * Scope untuk potongan retur
     */
    public function scopeRetur($query)
    {
        return $query->where('jenis_potongan', 'retur');
    }

    /**
     * Scope untuk potongan kasbon
     */
    public function scopeKasbon($query)
    {
        return $query->where('jenis_potongan', 'kasbon');
    }

    /**
     * Accessor untuk format nominal
     */
    public function getFormattedNominalAttribute()
    {
        return 'Rp ' . number_format($this->nominal, 0, ',', '.');
    }

    /**
     * Accessor untuk badge jenis potongan
     */
    public function getJenisBadgeAttribute()
    {
        $badges = [
            'kasir' => 'warning',
            'stok_opname' => 'info',
            'retur' => 'danger',
            'kasbon' => 'secondary',
        ];

        return $badges[$this->jenis_potongan] ?? 'secondary';
    }

    /**
     * Accessor untuk label jenis potongan
     */
    public function getJenisLabelAttribute()
    {
        $labels = [
            'kasir' => 'Potongan Kasir',
            'stok_opname' => 'Potongan Stok Opname',
            'retur' => 'Potongan Retur',
            'kasbon' => 'Potongan Kasbon',
        ];

        return $labels[$this->jenis_potongan] ?? $this->jenis_potongan;
    }

    /**
     * Accessor untuk format tanggal potongan
     */
    public function getFormattedBulanAttribute()
    {
        if ($this->bulan_potongan) {
            return $this->bulan_potongan->format('d M Y');
        }

        return '-';
    }

    /**
     * Boot method untuk auto-fill created_by
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->created_by && auth()->check()) {
                $model->created_by = auth()->id();
            }
        });
    }
}
