<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PriceList extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'description',
        'is_global',
        'is_active',
        'effective_from',
        'effective_until',
        'created_by',
    ];

    protected $casts = [
        'is_global' => 'boolean',
        'is_active' => 'boolean',
        'effective_from' => 'datetime',
        'effective_until' => 'datetime',
    ];

    protected $dates = ['deleted_at'];

    /**
     * Relationship to PriceListItem
     */
    public function items()
    {
        return $this->hasMany(PriceListItem::class);
    }

    /**
     * Relationship to active PriceListItem
     */
    public function activeItems()
    {
        return $this->hasMany(PriceListItem::class)->where('is_active', true);
    }

    /**
     * Relationship to outlets through pivot table
     */
    public function outlets()
    {
        return $this->belongsToMany(Outlet::class, 'outlet_price_lists')
            ->withPivot(['priority', 'is_active', 'effective_from', 'effective_until'])
            ->withTimestamps()
            ->orderBy('outlet_price_lists.priority', 'asc');
    }

    /**
     * Relationship to products through price list items
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'price_list_items')
            ->withPivot(['price', 'cost_price', 'is_active'])
            ->withTimestamps();
    }

    /**
     * Relationship to creator
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the global price list
     */
    public static function getGlobal()
    {
        return static::where('is_global', true)->where('is_active', true)->first();
    }

    /**
     * Add a product to this price list
     */
    public function addProduct($productId, $price, $costPrice = null, $isActive = true)
    {
        return PriceListItem::updateOrCreate(
            [
                'price_list_id' => $this->id,
                'product_id' => $productId,
            ],
            [
                'price' => $price,
                'cost_price' => $costPrice,
                'is_active' => $isActive,
            ]
        );
    }

    /**
     * Remove a product from this price list
     */
    public function removeProduct($productId)
    {
        return $this->items()->where('product_id', $productId)->delete();
    }

    /**
     * Get price for a specific product
     */
    public function getPriceForProduct($productId)
    {
        $item = $this->activeItems()->where('product_id', $productId)->first();
        return $item ? $item->price : null;
    }

    /**
     * Get cost price for a specific product
     */
    public function getCostPriceForProduct($productId)
    {
        $item = $this->activeItems()->where('product_id', $productId)->first();
        return $item ? $item->cost_price : null;
    }

    /**
     * Duplicate this price list
     */
    public function duplicate($newName = null, $newCode = null)
    {
        $newName = $newName ?: $this->name . ' (Copy)';
        $newCode = $newCode ?: $this->code . '_COPY';

        // Create new price list
        $newPriceList = static::create([
            'name' => $newName,
            'code' => $newCode,
            'description' => $this->description,
            'is_global' => false, // Copies are never global
            'is_active' => false, // Copies start as inactive
            'effective_from' => $this->effective_from,
            'effective_until' => $this->effective_until,
            'created_by' => auth()->id(),
        ]);

        // Copy all items
        foreach ($this->items as $item) {
            $newPriceList->addProduct(
                $item->product_id,
                $item->price,
                $item->cost_price,
                $item->is_active
            );
        }

        return $newPriceList;
    }

    /**
     * Scope for active price lists
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for global price lists
     */
    public function scopeGlobal($query)
    {
        return $query->where('is_global', true);
    }

    /**
     * Check if price list is currently effective
     */
    public function isEffective($date = null)
    {
        $date = $date ?: now()->toDateString();
        
        $effectiveFrom = $this->effective_from ? $this->effective_from->toDateString() : null;
        $effectiveUntil = $this->effective_until ? $this->effective_until->toDateString() : null;

        if ($effectiveFrom && $date < $effectiveFrom) {
            return false;
        }

        if ($effectiveUntil && $date > $effectiveUntil) {
            return false;
        }

        return true;
    }

    /**
     * Get formatted effective period
     */
    public function getEffectivePeriodAttribute()
    {
        if (!$this->effective_from && !$this->effective_until) {
            return 'Always';
        }

        $from = $this->effective_from ? $this->effective_from->format('d/m/Y') : 'Start';
        $until = $this->effective_until ? $this->effective_until->format('d/m/Y') : 'End';

        return "{$from} - {$until}";
    }

    /**
     * Get total products count
     */
    public function getTotalProductsAttribute()
    {
        return $this->items()->count();
    }

    /**
     * Get active products count
     */
    public function getActiveProductsAttribute()
    {
        return $this->activeItems()->count();
    }

    /**
     * Add all products to this price list with their default prices
     */
    public function addAllProducts($useDefaultPrice = true, $applyDiscount = 0)
    {
        $products = Product::where('is_active', true)->get();
        $added = 0;

        foreach ($products as $product) {
            // Skip if product already exists in this price list
            if ($this->items()->where('product_id', $product->id)->exists()) {
                continue;
            }

            $price = $useDefaultPrice ? $product->price : 0;

            // Apply discount if specified (e.g., 10 for 10% discount)
            if ($applyDiscount > 0) {
                $price = $price * (1 - ($applyDiscount / 100));
            }

            $this->addProduct($product->id, $price, $product->cost_price);
            $added++;
        }

        return $added;
    }

    /**
     * Sync all products to this price list (add missing ones)
     */
    public function syncAllProducts($useDefaultPrice = true)
    {
        return $this->addAllProducts($useDefaultPrice);
    }
}
