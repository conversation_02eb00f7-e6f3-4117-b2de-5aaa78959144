<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PriceListItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'price_list_id',
        'product_id',
        'price',
        'cost_price',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Relationship to PriceList
     */
    public function priceList()
    {
        return $this->belongsTo(PriceList::class);
    }

    /**
     * Relationship to Product
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute()
    {
        return 'Rp ' . number_format($this->price, 0, ',', '.');
    }

    /**
     * Get formatted cost price
     */
    public function getFormattedCostPriceAttribute()
    {
        return $this->cost_price ? 'Rp ' . number_format($this->cost_price, 0, ',', '.') : '-';
    }

    /**
     * Get profit margin
     */
    public function getProfitMarginAttribute()
    {
        if (!$this->cost_price || $this->cost_price == 0) {
            return 0;
        }

        return (($this->price - $this->cost_price) / $this->cost_price) * 100;
    }

    /**
     * Get profit amount
     */
    public function getProfitAmountAttribute()
    {
        return $this->price - ($this->cost_price ?? 0);
    }

    /**
     * Get formatted profit margin
     */
    public function getFormattedProfitMarginAttribute()
    {
        return number_format($this->profit_margin, 2) . '%';
    }

    /**
     * Get formatted profit amount
     */
    public function getFormattedProfitAmountAttribute()
    {
        return 'Rp ' . number_format($this->profit_amount, 0, ',', '.');
    }

    /**
     * Scope for active items
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for inactive items
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Boot method for cache clearing
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($item) {
            // Clear price cache when item is updated
            // Cache clearing can be implemented later with PriceResolutionService
        });

        static::deleted(function ($item) {
            // Clear price cache when item is deleted
            // Cache clearing can be implemented later with PriceResolutionService
        });
    }
}
