<?php

namespace App\Models;

use RingleSoft\LaravelProcessApproval\Models\ProcessApprovalFlowStep as BaseProcessApprovalFlowStep;

class ProcessApprovalFlowStep extends BaseProcessApprovalFlowStep
{
    /**
     * Override to disable tenancy features
     */
    public function getTenantIdAttribute()
    {
        return null;
    }

    /**
     * Override to disable tenancy features
     */
    public function setTenantIdAttribute($value)
    {
        // Do nothing - ignore tenant_id assignments
    }

    /**
     * Override to ensure tenant_id is not included in queries
     */
    protected function newBaseQueryBuilder()
    {
        $query = parent::newBaseQueryBuilder();

        // Remove any tenant_id related constraints
        return $query;
    }

    /**
     * Override to disable tenancy scoping
     */
    public function scopeTenant($query, $tenantId = null)
    {
        return $query;
    }

    /**
     * Override to ensure tenant_id is not in fillable
     */
    public function getFillable()
    {
        $fillable = parent::getFillable();

        // Remove tenant_id from fillable if it exists
        return array_diff($fillable, ['tenant_id']);
    }

    /**
     * Override to ensure tenant_id is not in attributes
     */
    public function getAttributes()
    {
        $attributes = parent::getAttributes();

        // Remove tenant_id from attributes if it exists
        unset($attributes['tenant_id']);

        return $attributes;
    }

    /**
     * Override to handle tenant_id access attempts
     */
    public function getAttribute($key)
    {
        if ($key === 'tenant_id') {
            return null;
        }

        return parent::getAttribute($key);
    }

    /**
     * Override to handle tenant_id setting attempts
     */
    public function setAttribute($key, $value)
    {
        if ($key === 'tenant_id') {
            return $this;
        }

        return parent::setAttribute($key, $value);
    }

    /**
     * Override to handle any property access that might trigger tenant_id
     */
    public function __get($key)
    {
        if ($key === 'tenant_id') {
            return null;
        }

        return parent::__get($key);
    }

    /**
     * Override to handle any property setting that might trigger tenant_id
     */
    public function __set($key, $value)
    {
        if ($key === 'tenant_id') {
            return;
        }

        parent::__set($key, $value);
    }

    /**
     * Override to remove tenant_id from toArray output
     */
    public function toArray()
    {
        $array = parent::toArray();
        unset($array['tenant_id']);
        return $array;
    }

    /**
     * Override to remove tenant_id from JSON output
     */
    public function toJson($options = 0)
    {
        $array = $this->toArray();
        unset($array['tenant_id']);
        return json_encode($array, $options);
    }

    /**
     * Boot method to remove any global scopes that might add tenant_id
     */
    protected static function boot()
    {
        parent::boot();

        // Remove the MultiTenantScope that's causing the issue
        static::withoutGlobalScope('RingleSoft\LaravelProcessApproval\Scopes\MultiTenantScope');
    }
}
