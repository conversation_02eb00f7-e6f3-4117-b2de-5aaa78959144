<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Produk extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'produk';

    protected $fillable = [
        'kode',
        'nama',
        'deskripsi',
        'unit_cost',
        'selling_price',
        'average_cost',
        'id_produk_kategori',
        'id_satuan',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    protected $casts = [
        'unit_cost' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'average_cost' => 'decimal:2',
    ];

    // Relationships
    public function kategori()
    {
        return $this->belongsTo(ProdukKategori::class, 'id_produk_kategori');
    }

    public function satuan()
    {
        return $this->belongsTo(Satuan::class, 'id_satuan');
    }

    public function inventory()
    {
        return $this->hasOne(Inventory::class, 'product_id');
    }

    public function saleItems()
    {
        return $this->hasMany(SaleItem::class, 'product_id');
    }

    public function inventoryStocks()
    {
        return $this->hasMany(InventoryStock::class, 'product_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Helper methods
    public function getTotalStockAttribute()
    {
        return $this->inventoryStocks()->sum('quantity');
    }

    public function getTotalStockValueAttribute()
    {
        return $this->inventoryStocks()->sum('total_value');
    }

    /**
     * Get stock quantity for specific entitas
     */
    public function getStockByEntitas($entitasId)
    {
        return $this->inventoryStocks()
            ->where('entitas_id', $entitasId)
            ->sum('quantity');
    }

    /**
     * Get available stock for specific entitas
     */
    public function getAvailableStockByEntitas($entitasId)
    {
        $stock = $this->inventoryStocks()
            ->where('entitas_id', $entitasId)
            ->first();

        return $stock ? $stock->quantity : 0;
    }
}
