<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Project extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'start_date',
        'end_date',
        'customer_id',
        'status',
        'created_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    public function members(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'project_members', 'project_id', 'user_id')
            ->withPivot(['role', 'hourly_rate', 'capacity_hours_per_week', 'joined_at', 'left_at', 'is_active', 'permissions', 'metadata', 'added_by'])
            ->withTimestamps();
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // public function quotations(): HasMany
    // {
    //     return $this->hasMany(Quotation::class);
    // }

    // public function salesOrders(): HasMany
    // {
    //     return $this->hasMany(SalesOrder::class);
    // }

    // Performance-optimized query scopes
    public function scopeWithOptimizedCounts($query)
    {
        return $query->withCount([
            'tasks',
            'tasks as completed_tasks_count' => function ($query) {
                $query->where('status', 'completed');
            },
            'tasks as in_progress_tasks_count' => function ($query) {
                $query->where('status', 'in_progress');
            },
            'tasks as todo_tasks_count' => function ($query) {
                $query->where('status', 'todo');
            },
            'members as active_members_count' => function ($query) {
                $query->where('is_active', true);
            }
        ]);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeForUser($query, $userId)
    {
        return $query->whereHas('members', function ($q) use ($userId) {
            $q->where('user_id', $userId)->where('is_active', true);
        });
    }

    public function scopeWithRecentTasks($query, $limit = 5)
    {
        return $query->with(['tasks' => function ($query) use ($limit) {
            $query->select('id', 'project_id', 'name', 'status', 'assigned_to', 'due_date', 'created_at')
                ->with('assignedUser:id,name')
                ->latest()
                ->limit($limit);
        }]);
    }

    public function scopeForDashboard($query)
    {
        return $query->select([
            'id',
            'name',
            'description',
            'status',
            'start_date',
            'end_date',
            'customer_id',
            'created_at'
        ])
            ->with(['customer:id,name'])
            ->withOptimizedCounts();
    }

    public function activities(): HasMany
    {
        return $this->hasMany(ProjectActivity::class)->orderBy('created_at', 'desc');
    }

    // Get project revenue from sales orders
    public function getTotalRevenueAttribute(): float
    {
        // return $this->salesOrders()->sum('total_amount') ?? 0.0;
        return 0.0; // Placeholder until SalesOrder model is available
    }

    // Get project progress based on completed tasks
    public function getProgressPercentageAttribute(): float
    {
        $totalTasks = $this->tasks()->count();
        if ($totalTasks === 0) {
            return 0;
        }

        $completedTasks = $this->tasks()->where('status', 'completed')->count();
        return round(($completedTasks / $totalTasks) * 100, 2);
    }

    // Get project budget from quotations
    public function getTotalBudgetAttribute(): float
    {
        // return $this->quotations()->sum('total_amount') ?? 0.0;
        return 0.0; // Placeholder until Quotation model is available
    }

    // Get project health status
    public function getProjectHealthAttribute(): string
    {
        $progress = $this->progress_percentage;
        $daysRemaining = $this->end_date ? now()->diffInDays($this->end_date, false) : null;

        if ($this->status === 'completed') {
            return 'completed';
        }

        if ($daysRemaining !== null && $daysRemaining < 0) {
            return 'overdue';
        }

        if ($progress >= 80) {
            return 'on_track';
        } elseif ($progress >= 50) {
            return 'at_risk';
        } else {
            return 'behind';
        }
    }

    // Process mentions in project description
    public function processMentions(): void
    {
        if (!$this->description) return;

        preg_match_all('/@(\w+)/', $this->description, $matches);
        $usernames = $matches[1];

        foreach ($usernames as $username) {
            $user = User::where('name', 'like', "%{$username}%")->first();
            if ($user) {
                TeamMention::create([
                    'mentionable_type' => self::class,
                    'mentionable_id' => $this->id,
                    'mentioned_user_id' => $user->id,
                    'mentioned_by_user_id' => auth()->id() ?? 1,
                    'context' => 'project_description',
                ]);
            }
        }
    }
}
