<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ProjectActivity extends Model
{
    protected $fillable = [
        'project_id',
        'user_id',
        'activity_type',
        'subject_type',
        'subject_id',
        'description',
        'properties',
    ];

    protected $casts = [
        'properties' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function subject(): MorphTo
    {
        return $this->morphTo();
    }

    // Helper method to create activity log
    public static function log(string $type, Model $subject, string $description, array $properties = []): self
    {
        // Get project_id based on subject type
        $projectId = null;
        if ($subject instanceof Project) {
            $projectId = $subject->id;
        } elseif (isset($subject->project_id)) {
            $projectId = $subject->project_id;
        } elseif (isset($subject->task) && isset($subject->task->project_id)) {
            $projectId = $subject->task->project_id;
        }

        return self::create([
            'project_id' => $projectId,
            'user_id' => auth()->id() ?? 1, // Fallback to admin user
            'activity_type' => $type,
            'subject_type' => get_class($subject),
            'subject_id' => $subject->id,
            'description' => $description,
            'properties' => $properties,
        ]);
    }
}
