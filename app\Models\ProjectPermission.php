<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class ProjectPermission extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'user_id',
        'permission_type',
        'specific_permissions',
        'can_view_project',
        'can_edit_project',
        'can_delete_project',
        'can_manage_members',
        'can_view_tasks',
        'can_create_tasks',
        'can_edit_tasks',
        'can_delete_tasks',
        'can_assign_tasks',
        'can_view_timesheets',
        'can_edit_timesheets',
        'can_view_reports',
        'can_export_data',
        'granted_at',
        'expires_at',
        'granted_by',
        'notes',
    ];

    protected $casts = [
        'specific_permissions' => 'array',
        'can_view_project' => 'boolean',
        'can_edit_project' => 'boolean',
        'can_delete_project' => 'boolean',
        'can_manage_members' => 'boolean',
        'can_view_tasks' => 'boolean',
        'can_create_tasks' => 'boolean',
        'can_edit_tasks' => 'boolean',
        'can_delete_tasks' => 'boolean',
        'can_assign_tasks' => 'boolean',
        'can_view_timesheets' => 'boolean',
        'can_edit_timesheets' => 'boolean',
        'can_view_reports' => 'boolean',
        'can_export_data' => 'boolean',
        'granted_at' => 'datetime',
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function grantedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'granted_by');
    }

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    public function scopeExpired(Builder $query): Builder
    {
        return $query->where('expires_at', '<=', now());
    }

    public function scopeForProject(Builder $query, int $projectId): Builder
    {
        return $query->where('project_id', $projectId);
    }

    public function scopeForUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    // Helper methods
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function isActive(): bool
    {
        return !$this->isExpired();
    }

    public function hasPermission(string $permission): bool
    {
        if ($this->isExpired()) {
            return false;
        }

        return $this->getAttribute("can_{$permission}") ?? false;
    }

    public function grantPermission(string $permission): void
    {
        $this->setAttribute("can_{$permission}", true);
        $this->save();
    }

    public function revokePermission(string $permission): void
    {
        $this->setAttribute("can_{$permission}", false);
        $this->save();
    }
}
