<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Province extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'slug',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Boot method untuk auto-generate slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($province) {
            if (empty($province->slug)) {
                $province->slug = Str::slug($province->name);
            }
        });

        static::updating(function ($province) {
            if ($province->isDirty('name') && empty($province->slug)) {
                $province->slug = Str::slug($province->name);
            }
        });
    }

    /**
     * Relasi ke City
     */
    public function cities()
    {
        return $this->hasMany(City::class);
    }

    /**
     * Relasi ke District melalui City
     */
    public function districts()
    {
        return $this->hasMany(District::class);
    }

    /**
     * Relasi ke Village melalui District
     */
    public function villages()
    {
        return $this->hasMany(Village::class);
    }

    /**
     * Relasi ke Customer
     */
    public function customers()
    {
        return $this->hasMany(Customer::class);
    }

    /**
     * Scope untuk provinsi aktif
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get formatted name dengan kode
     */
    public function getFormattedNameAttribute(): string
    {
        return $this->code . ' - ' . $this->name;
    }

    /**
     * Get total cities count
     */
    public function getCitiesCountAttribute(): int
    {
        return $this->cities()->count();
    }

    /**
     * Get total districts count
     */
    public function getDistrictsCountAttribute(): int
    {
        return $this->districts()->count();
    }

    /**
     * Get total villages count
     */
    public function getVillagesCountAttribute(): int
    {
        return $this->villages()->count();
    }

    /**
     * Get route key name untuk URL
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
