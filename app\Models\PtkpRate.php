<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PtkpRate extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'ptkp_rates';

    protected $fillable = [
        'status_code',
        'status_description',
        'annual_amount',
        'monthly_amount',
        'is_active',
        'effective_date',
        'end_date',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'effective_date', 'end_date'];

    protected $casts = [
        'annual_amount' => 'decimal:2',
        'monthly_amount' => 'decimal:2',
        'is_active' => 'boolean',
        'effective_date' => 'date',
        'end_date' => 'date',
    ];

    // Relationships
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function employeeTaxInfo()
    {
        return $this->hasMany(EmployeeTaxInfo::class, 'ptkp_status', 'status_code');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeEffectiveOn($query, $date)
    {
        return $query->where('effective_date', '<=', $date)
                    ->where(function ($q) use ($date) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', $date);
                    });
    }

    public function scopeByStatus($query, $statusCode)
    {
        return $query->where('status_code', $statusCode);
    }

    // Helper methods
    public function getFormattedAnnualAmountAttribute()
    {
        return 'Rp ' . number_format($this->annual_amount, 0, ',', '.');
    }

    public function getFormattedMonthlyAmountAttribute()
    {
        return 'Rp ' . number_format($this->monthly_amount, 0, ',', '.');
    }

    public function getDisplayNameAttribute()
    {
        return $this->status_code . ' - ' . $this->status_description;
    }

    public function getFormattedEffectiveDateAttribute()
    {
        return $this->effective_date ? $this->effective_date->format('d/m/Y') : null;
    }

    public function getFormattedEndDateAttribute()
    {
        return $this->end_date ? $this->end_date->format('d/m/Y') : 'Ongoing';
    }

    public function isEffectiveOn($date)
    {
        $checkDate = is_string($date) ? $date : $date->toDateString();
        
        return $this->is_active &&
               $this->effective_date <= $checkDate &&
               (!$this->end_date || $this->end_date >= $checkDate);
    }

    // Static methods
    public static function getByStatus($statusCode, $date = null)
    {
        $date = $date ?: now();
        
        return static::active()
                    ->effectiveOn($date)
                    ->byStatus($statusCode)
                    ->first();
    }

    public static function getActivePtkpRates($date = null)
    {
        $date = $date ?: now();
        
        return static::active()
                    ->effectiveOn($date)
                    ->orderBy('status_code')
                    ->get();
    }

    public static function getCurrentPtkpRates()
    {
        return static::getActivePtkpRates();
    }

    // Default Indonesian PTKP rates (2024)
    public static function getDefaultRates()
    {
        return [
            [
                'status_code' => 'TK/0',
                'status_description' => 'Tidak Kawin, Tanpa Tanggungan',
                'annual_amount' => 54000000,
                'monthly_amount' => 4500000,
            ],
            [
                'status_code' => 'TK/1',
                'status_description' => 'Tidak Kawin, 1 Tanggungan',
                'annual_amount' => 58500000,
                'monthly_amount' => 4875000,
            ],
            [
                'status_code' => 'TK/2',
                'status_description' => 'Tidak Kawin, 2 Tanggungan',
                'annual_amount' => 63000000,
                'monthly_amount' => 5250000,
            ],
            [
                'status_code' => 'TK/3',
                'status_description' => 'Tidak Kawin, 3 Tanggungan',
                'annual_amount' => 67500000,
                'monthly_amount' => 5625000,
            ],
            [
                'status_code' => 'K/0',
                'status_description' => 'Kawin, Tanpa Tanggungan',
                'annual_amount' => 58500000,
                'monthly_amount' => 4875000,
            ],
            [
                'status_code' => 'K/1',
                'status_description' => 'Kawin, 1 Tanggungan',
                'annual_amount' => 63000000,
                'monthly_amount' => 5250000,
            ],
            [
                'status_code' => 'K/2',
                'status_description' => 'Kawin, 2 Tanggungan',
                'annual_amount' => 67500000,
                'monthly_amount' => 5625000,
            ],
            [
                'status_code' => 'K/3',
                'status_description' => 'Kawin, 3 Tanggungan',
                'annual_amount' => 72000000,
                'monthly_amount' => 6000000,
            ],
            [
                'status_code' => 'K/I/0',
                'status_description' => 'Kawin, Penghasilan Istri Digabung, Tanpa Tanggungan',
                'annual_amount' => 112500000,
                'monthly_amount' => 9375000,
            ],
            [
                'status_code' => 'K/I/1',
                'status_description' => 'Kawin, Penghasilan Istri Digabung, 1 Tanggungan',
                'annual_amount' => 117000000,
                'monthly_amount' => 9750000,
            ],
            [
                'status_code' => 'K/I/2',
                'status_description' => 'Kawin, Penghasilan Istri Digabung, 2 Tanggungan',
                'annual_amount' => 121500000,
                'monthly_amount' => 10125000,
            ],
            [
                'status_code' => 'K/I/3',
                'status_description' => 'Kawin, Penghasilan Istri Digabung, 3 Tanggungan',
                'annual_amount' => 126000000,
                'monthly_amount' => 10500000,
            ],
        ];
    }

    public function getStatusCategoryAttribute()
    {
        if (str_starts_with($this->status_code, 'TK')) {
            return 'Tidak Kawin';
        } elseif (str_starts_with($this->status_code, 'K/I')) {
            return 'Kawin (Istri Digabung)';
        } elseif (str_starts_with($this->status_code, 'K')) {
            return 'Kawin';
        }
        
        return 'Lainnya';
    }

    public function getStatusCategoryColorAttribute()
    {
        return match($this->status_category) {
            'Tidak Kawin' => 'info',
            'Kawin' => 'success',
            'Kawin (Istri Digabung)' => 'warning',
            default => 'gray'
        };
    }

    public function getDependentsCountAttribute()
    {
        // Extract dependents count from status code
        if (preg_match('/\/(\d+)$/', $this->status_code, $matches)) {
            return (int) $matches[1];
        }
        
        return 0;
    }
}
