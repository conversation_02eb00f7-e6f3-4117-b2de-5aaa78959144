<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class PurchaseInvoice extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'purchase_invoices';

    protected $fillable = [
        'invoice_number',
        'supplier_invoice_number',
        'invoice_date',
        'due_date',
        'supplier_id',
        'purchase_order_id',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'paid_amount',
        'outstanding_amount',
        'status',
        'notes',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    protected $dates = ['deleted_at', 'invoice_date', 'due_date', 'approved_at'];

    protected $casts = [
        'invoice_date' => 'date',
        'due_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'outstanding_amount' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    // Relationships
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function purchaseOrder()
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    public function purchaseInvoiceItems()
    {
        return $this->hasMany(PurchaseInvoiceItem::class);
    }

    public function purchasePayments()
    {
        return $this->hasMany(PurchasePayment::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeBySupplier($query, $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    public function scopePending($query)
    {
        return $query->whereIn('status', ['Draft', 'Submitted']);
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'Approved');
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', Carbon::now())
                    ->where('outstanding_amount', '>', 0)
                    ->whereIn('status', ['Approved', 'Partially_Paid']);
    }

    public function scopeUnpaid($query)
    {
        return $query->where('outstanding_amount', '>', 0)
                    ->whereIn('status', ['Approved', 'Partially_Paid']);
    }

    // Helper methods
    public function getTotalItemsAttribute()
    {
        return $this->purchaseInvoiceItems()->count();
    }

    public function getPaymentProgressAttribute()
    {
        if ($this->total_amount == 0) return 0;
        
        return round(($this->paid_amount / $this->total_amount) * 100, 2);
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'Draft' => 'gray',
            'Submitted' => 'warning',
            'Approved' => 'success',
            'Partially_Paid' => 'info',
            'Paid' => 'success',
            'Overdue' => 'danger',
            'Cancelled' => 'danger',
            default => 'gray'
        };
    }

    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'Draft' => 'Draft',
            'Submitted' => 'Menunggu Persetujuan',
            'Approved' => 'Disetujui',
            'Partially_Paid' => 'Sebagian Dibayar',
            'Paid' => 'Lunas',
            'Overdue' => 'Jatuh Tempo',
            'Cancelled' => 'Dibatalkan',
            default => $this->status
        };
    }

    public function getDaysUntilDueAttribute()
    {
        if (!$this->due_date) return null;
        
        return Carbon::now()->diffInDays($this->due_date, false);
    }

    public function isOverdue()
    {
        if (!$this->due_date) return false;
        
        return $this->due_date->isPast() && $this->outstanding_amount > 0;
    }

    public function isEditable()
    {
        return in_array($this->status, ['Draft']);
    }

    public function canBeApproved()
    {
        return $this->status === 'Submitted';
    }

    public function canBeCancelled()
    {
        return in_array($this->status, ['Draft', 'Submitted', 'Approved']) && $this->paid_amount == 0;
    }

    public function canBePartiallyPaid()
    {
        return in_array($this->status, ['Approved', 'Partially_Paid']) && $this->outstanding_amount > 0;
    }

    public function addPayment($amount)
    {
        if ($amount <= 0 || $amount > $this->outstanding_amount) {
            throw new \Exception('Invalid payment amount');
        }

        $this->paid_amount += $amount;
        $this->outstanding_amount -= $amount;

        // Update status based on payment
        if ($this->outstanding_amount <= 0) {
            $this->status = 'Paid';
            $this->outstanding_amount = 0; // Ensure it's exactly 0
        } else {
            $this->status = 'Partially_Paid';
        }

        $this->save();
    }

    public function calculateTotals()
    {
        $subtotal = $this->purchaseInvoiceItems()->sum('total_price');
        $this->subtotal = $subtotal;
        $this->total_amount = $subtotal + $this->tax_amount - $this->discount_amount;
        $this->outstanding_amount = $this->total_amount - $this->paid_amount;
        $this->save();
    }

    public function approve()
    {
        if (!$this->canBeApproved()) {
            throw new \Exception('Invoice cannot be approved');
        }

        $this->status = 'Approved';
        $this->approved_by = auth()->id();
        $this->approved_at = Carbon::now();
        $this->save();

        // Create journal entry
        $this->createJournalEntry();
    }

    protected function createJournalEntry()
    {
        // This will be implemented when we create the PostingRuleEngine
        // Logic depends on whether this invoice has associated goods receipt or not
        // Will be handled by PostingRuleEngine based on source_type = 'PurchaseInvoice'
    }

    public function getFormattedInvoiceDateAttribute()
    {
        return $this->invoice_date ? $this->invoice_date->format('d/m/Y') : null;
    }

    public function getFormattedDueDateAttribute()
    {
        return $this->due_date ? $this->due_date->format('d/m/Y') : null;
    }

    public function getFormattedTotalAmountAttribute()
    {
        return 'Rp ' . number_format($this->total_amount, 0, ',', '.');
    }

    public function getFormattedOutstandingAmountAttribute()
    {
        return 'Rp ' . number_format($this->outstanding_amount, 0, ',', '.');
    }

    public function getFormattedPaidAmountAttribute()
    {
        return 'Rp ' . number_format($this->paid_amount, 0, ',', '.');
    }

    // Auto-generate invoice number and calculate outstanding amount
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($invoice) {
            if (empty($invoice->invoice_number)) {
                $invoice->invoice_number = static::generateInvoiceNumber();
            }
            
            // Set outstanding amount equal to total amount initially
            $invoice->outstanding_amount = $invoice->total_amount - $invoice->paid_amount;
        });

        static::updating(function ($invoice) {
            // Recalculate outstanding amount
            $invoice->outstanding_amount = $invoice->total_amount - $invoice->paid_amount;
            
            // Update status based on payment
            if ($invoice->outstanding_amount <= 0 && $invoice->total_amount > 0) {
                $invoice->status = 'Paid';
            } elseif ($invoice->paid_amount > 0 && $invoice->outstanding_amount > 0) {
                $invoice->status = 'Partially_Paid';
            }
        });
    }

    public static function generateInvoiceNumber()
    {
        $prefix = 'PI';
        $date = Carbon::now()->format('Ymd');
        $lastInvoice = static::whereDate('created_at', Carbon::today())
                            ->where('invoice_number', 'like', $prefix . $date . '%')
                            ->orderBy('invoice_number', 'desc')
                            ->first();

        if ($lastInvoice) {
            $lastNumber = intval(substr($lastInvoice->invoice_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . $date . $newNumber;
    }
}
