<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseInvoiceItem extends Model
{
    use HasFactory;

    protected $table = 'purchase_invoice_items';

    protected $fillable = [
        'purchase_invoice_id',
        'product_id',
        'description',
        'quantity',
        'unit_price',
        'total_price',
        'account_id',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    // Relationships
    public function purchaseInvoice()
    {
        return $this->belongsTo(PurchaseInvoice::class);
    }

    public function product()
    {
        return $this->belongsTo(Produk::class, 'product_id');
    }

    public function account()
    {
        return $this->belongsTo(Akun::class, 'account_id');
    }

    // Helper methods
    public function getFormattedUnitPriceAttribute()
    {
        return 'Rp ' . number_format($this->unit_price, 0, ',', '.');
    }

    public function getFormattedTotalPriceAttribute()
    {
        return 'Rp ' . number_format($this->total_price, 0, ',', '.');
    }

    public function isInventoryItem()
    {
        return $this->product_id !== null;
    }

    public function isExpenseItem()
    {
        return $this->product_id === null;
    }

    public function getItemTypeAttribute()
    {
        return $this->isInventoryItem() ? 'Inventory' : 'Expense';
    }

    public function getItemTypeColorAttribute()
    {
        return $this->isInventoryItem() ? 'success' : 'info';
    }

    // Auto-calculate total price when saving
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->total_price = $item->quantity * $item->unit_price;
        });

        static::saved(function ($item) {
            // Update parent invoice totals
            $item->purchaseInvoice->calculateTotals();
        });

        static::deleted(function ($item) {
            // Update parent invoice totals
            $item->purchaseInvoice->calculateTotals();
        });
    }
}
