<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class PurchaseOrder extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'purchase_orders';

    protected $fillable = [
        'po_number',
        'po_date',
        'expected_delivery_date',
        'supplier_id',
        'warehouse_id',
        'entitas_id',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'status',
        'notes',
        'payment_terms',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    protected $dates = ['deleted_at', 'po_date', 'expected_delivery_date', 'approved_at'];

    protected $casts = [
        'po_date' => 'date',
        'expected_delivery_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    // Relationships
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function entitas()
    {
        return $this->belongsTo(Entitas::class);
    }

    public function purchaseOrderItems()
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    public function goodsReceipts()
    {
        return $this->hasMany(GoodsReceipt::class);
    }

    public function purchaseInvoices()
    {
        return $this->hasMany(PurchaseInvoice::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeBySupplier($query, $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    public function scopeByWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    public function scopeByEntitas($query, $entitasId)
    {
        return $query->where('entitas_id', $entitasId);
    }

    public function scopePending($query)
    {
        return $query->whereIn('status', ['Draft', 'Submitted']);
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'Approved');
    }

    public function scopeOverdue($query)
    {
        return $query->where('expected_delivery_date', '<', Carbon::now())
                    ->whereIn('status', ['Approved', 'Partially_Received']);
    }

    // Helper methods
    public function getTotalItemsAttribute()
    {
        return $this->purchaseOrderItems()->count();
    }

    public function getTotalQuantityAttribute()
    {
        return $this->purchaseOrderItems()->sum('quantity_ordered');
    }

    public function getTotalReceivedQuantityAttribute()
    {
        return $this->purchaseOrderItems()->sum('quantity_received');
    }

    public function getReceiptProgressAttribute()
    {
        $totalOrdered = $this->total_quantity;
        $totalReceived = $this->total_received_quantity;
        
        if ($totalOrdered == 0) return 0;
        
        return round(($totalReceived / $totalOrdered) * 100, 2);
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'Draft' => 'gray',
            'Submitted' => 'warning',
            'Approved' => 'success',
            'Partially_Received' => 'info',
            'Completed' => 'success',
            'Cancelled' => 'danger',
            default => 'gray'
        };
    }

    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'Draft' => 'Draft',
            'Submitted' => 'Menunggu Persetujuan',
            'Approved' => 'Disetujui',
            'Partially_Received' => 'Sebagian Diterima',
            'Completed' => 'Selesai',
            'Cancelled' => 'Dibatalkan',
            default => $this->status
        };
    }

    public function isEditable()
    {
        return in_array($this->status, ['Draft']);
    }

    public function canBeApproved()
    {
        return $this->status === 'Submitted';
    }

    public function canBeCancelled()
    {
        return in_array($this->status, ['Draft', 'Submitted', 'Approved']);
    }

    public function canReceiveGoods()
    {
        return in_array($this->status, ['Approved', 'Partially_Received']);
    }

    public function updateReceiptStatus()
    {
        $totalOrdered = $this->total_quantity;
        $totalReceived = $this->total_received_quantity;

        if ($totalReceived == 0) {
            $this->status = 'Approved';
        } elseif ($totalReceived >= $totalOrdered) {
            $this->status = 'Completed';
        } else {
            $this->status = 'Partially_Received';
        }

        $this->save();
    }

    public function calculateTotals()
    {
        $subtotal = $this->purchaseOrderItems()->sum('total_price');
        $this->subtotal = $subtotal;
        $this->total_amount = $subtotal + $this->tax_amount - $this->discount_amount;
        $this->save();
    }

    // Auto-generate PO number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($purchaseOrder) {
            if (empty($purchaseOrder->po_number)) {
                $purchaseOrder->po_number = static::generatePoNumber();
            }
        });
    }

    public static function generatePoNumber()
    {
        $prefix = 'PO';
        $date = Carbon::now()->format('Ymd');
        $lastPo = static::whereDate('created_at', Carbon::today())
                       ->where('po_number', 'like', $prefix . $date . '%')
                       ->orderBy('po_number', 'desc')
                       ->first();

        if ($lastPo) {
            $lastNumber = intval(substr($lastPo->po_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . $date . $newNumber;
    }

    public function getFormattedPoDateAttribute()
    {
        return $this->po_date ? $this->po_date->format('d/m/Y') : null;
    }

    public function getFormattedExpectedDeliveryDateAttribute()
    {
        return $this->expected_delivery_date ? $this->expected_delivery_date->format('d/m/Y') : null;
    }

    public function getDaysUntilDeliveryAttribute()
    {
        if (!$this->expected_delivery_date) return null;
        
        return Carbon::now()->diffInDays($this->expected_delivery_date, false);
    }

    public function isOverdue()
    {
        if (!$this->expected_delivery_date) return false;
        
        return $this->expected_delivery_date->isPast() && 
               in_array($this->status, ['Approved', 'Partially_Received']);
    }
}
