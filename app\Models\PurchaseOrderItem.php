<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseOrderItem extends Model
{
    use HasFactory;

    protected $table = 'purchase_order_items';

    protected $fillable = [
        'purchase_order_id',
        'product_id',
        'quantity_ordered',
        'quantity_received',
        'unit_price',
        'total_price',
        'notes',
    ];

    protected $casts = [
        'quantity_ordered' => 'integer',
        'quantity_received' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    // Relationships
    public function purchaseOrder()
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    public function product()
    {
        return $this->belongsTo(Produk::class, 'product_id');
    }

    public function goodsReceiptItems()
    {
        return $this->hasMany(GoodsReceiptItem::class);
    }

    // Helper methods
    public function getRemainingQuantityAttribute()
    {
        return $this->quantity_ordered - $this->quantity_received;
    }

    public function getReceiptProgressAttribute()
    {
        if ($this->quantity_ordered == 0) return 0;
        
        return round(($this->quantity_received / $this->quantity_ordered) * 100, 2);
    }

    public function getReceiptStatusAttribute()
    {
        if ($this->quantity_received == 0) {
            return 'Not Received';
        } elseif ($this->quantity_received >= $this->quantity_ordered) {
            return 'Fully Received';
        } else {
            return 'Partially Received';
        }
    }

    public function getReceiptStatusColorAttribute()
    {
        return match($this->receipt_status) {
            'Not Received' => 'gray',
            'Partially Received' => 'warning',
            'Fully Received' => 'success',
            default => 'gray'
        };
    }

    public function canReceiveMore()
    {
        return $this->quantity_received < $this->quantity_ordered;
    }

    public function updateQuantityReceived($additionalQuantity)
    {
        $this->quantity_received += $additionalQuantity;
        $this->save();

        // Update parent PO status
        $this->purchaseOrder->updateReceiptStatus();
    }

    public function getFormattedUnitPriceAttribute()
    {
        return 'Rp ' . number_format($this->unit_price, 0, ',', '.');
    }

    public function getFormattedTotalPriceAttribute()
    {
        return 'Rp ' . number_format($this->total_price, 0, ',', '.');
    }

    // Auto-calculate total price when saving
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->total_price = $item->quantity_ordered * $item->unit_price;
        });

        static::saved(function ($item) {
            // Update parent PO totals
            $item->purchaseOrder->calculateTotals();
        });

        static::deleted(function ($item) {
            // Update parent PO totals
            $item->purchaseOrder->calculateTotals();
        });
    }
}
