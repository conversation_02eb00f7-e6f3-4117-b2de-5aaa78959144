<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class PurchasePayment extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'purchase_payments';

    protected $fillable = [
        'payment_number',
        'payment_date',
        'supplier_id',
        'purchase_invoice_id',
        'payment_amount',
        'payment_method',
        'reference_number',
        'bank_account_id',
        'status',
        'notes',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    protected $dates = ['deleted_at', 'payment_date', 'approved_at'];

    protected $casts = [
        'payment_date' => 'date',
        'payment_amount' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    // Relationships
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function purchaseInvoice()
    {
        return $this->belongsTo(PurchaseInvoice::class);
    }

    public function bankAccount()
    {
        return $this->belongsTo(Akun::class, 'bank_account_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeBySupplier($query, $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    public function scopeByPaymentMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'Completed');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'Draft');
    }

    // Helper methods
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'Draft' => 'gray',
            'Completed' => 'success',
            'Cancelled' => 'danger',
            default => 'gray'
        };
    }

    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'Draft' => 'Draft',
            'Completed' => 'Selesai',
            'Cancelled' => 'Dibatalkan',
            default => $this->status
        };
    }

    public function getPaymentMethodLabelAttribute()
    {
        return match($this->payment_method) {
            'Cash' => 'Tunai',
            'Bank_Transfer' => 'Transfer Bank',
            'Check' => 'Cek',
            'Credit_Card' => 'Kartu Kredit',
            default => $this->payment_method
        };
    }

    public function getPaymentMethodColorAttribute()
    {
        return match($this->payment_method) {
            'Cash' => 'success',
            'Bank_Transfer' => 'info',
            'Check' => 'warning',
            'Credit_Card' => 'primary',
            default => 'gray'
        };
    }

    public function isEditable()
    {
        return $this->status === 'Draft';
    }

    public function canBeCompleted()
    {
        return $this->status === 'Draft' && $this->payment_amount > 0;
    }

    public function canBeCancelled()
    {
        return $this->status === 'Draft';
    }

    public function complete()
    {
        if (!$this->canBeCompleted()) {
            throw new \Exception('Payment cannot be completed');
        }

        $this->status = 'Completed';
        $this->approved_by = auth()->id();
        $this->approved_at = Carbon::now();
        $this->save();

        // Update invoice payment
        if ($this->purchaseInvoice) {
            $this->purchaseInvoice->addPayment($this->payment_amount);
        }

        // Create journal entry
        $this->createJournalEntry();
    }

    protected function createJournalEntry()
    {
        // This will be implemented when we create the PostingRuleEngine
        // Logic: Dr. Accounts Payable, Cr. Cash/Bank Account
        // Will be handled by PostingRuleEngine based on source_type = 'PurchasePayment'
    }

    public function getFormattedPaymentDateAttribute()
    {
        return $this->payment_date ? $this->payment_date->format('d/m/Y') : null;
    }

    public function getFormattedPaymentAmountAttribute()
    {
        return 'Rp ' . number_format($this->payment_amount, 0, ',', '.');
    }

    public function isAdvancePayment()
    {
        return $this->purchase_invoice_id === null;
    }

    public function getPaymentTypeAttribute()
    {
        return $this->isAdvancePayment() ? 'Advance Payment' : 'Invoice Payment';
    }

    public function getPaymentTypeColorAttribute()
    {
        return $this->isAdvancePayment() ? 'warning' : 'success';
    }

    // Auto-generate payment number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($payment) {
            if (empty($payment->payment_number)) {
                $payment->payment_number = static::generatePaymentNumber();
            }
        });
    }

    public static function generatePaymentNumber()
    {
        $prefix = 'PP';
        $date = Carbon::now()->format('Ymd');
        $lastPayment = static::whereDate('created_at', Carbon::today())
                            ->where('payment_number', 'like', $prefix . $date . '%')
                            ->orderBy('payment_number', 'desc')
                            ->first();

        if ($lastPayment) {
            $lastNumber = intval(substr($lastPayment->payment_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . $date . $newNumber;
    }
}
