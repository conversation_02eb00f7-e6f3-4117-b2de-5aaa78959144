<?php

namespace App\Models\ReferenceData;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class KpiStatusPenilaian extends Model
{
    use HasFactory;

    protected $table = 'kpi_status_penilaian';

    protected $fillable = [
        'kode',
        'nama',
        'deskripsi',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get active options for select
     */
    public static function getOptions(): array
    {
        return static::where('is_active', true)
            ->orderBy('sort_order')
            ->pluck('nama', 'kode')
            ->toArray();
    }
}
