<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class RiwayatKontrak extends Model
{
    use HasFactory;

    protected $table = 'riwayat_kontrak';

    protected $fillable = [
        'karyawan_id',
        'jenis_kontrak',
        'no_kontrak',
        'tgl_mulai',
        'tanggal_mulai_kerja',
        'tgl_selesai',
        'keterangan',
        'is_active',
    ];

    /**
     * Relasi ke tabel karyawan
     */
    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class);
    }

    /**
     * Boot method untuk otomatisasi status kontrak
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            $model->checkAndUpdateStatus();
        });

        static::updating(function ($model) {
            $model->checkAndUpdateStatus();
        });
    }

    /**
     * Method untuk mengupdate status kontrak secara otomatis
     */
    protected function checkAndUpdateStatus()
    {
        $today = Carbon::now()->startOfDay();

        // Jika `tgl_selesai` telah te<PERSON><PERSON> dan `jenis_kontrak` bukan PKWTT
        if ($this->tgl_selesai && $this->tgl_selesai < $today && $this->jenis_kontrak !== 'PKWTT') {
            $this->is_active = 0;
        }
    }
}
