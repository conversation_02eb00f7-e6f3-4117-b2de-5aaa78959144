<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class SalesOrder extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'sales_orders';

    protected $fillable = [
        'so_number',
        'so_date',
        'expected_delivery_date',
        'customer_id',
        'warehouse_id',
        'entitas_id',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'status',
        'notes',
        'payment_terms',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    protected $dates = ['deleted_at', 'so_date', 'expected_delivery_date', 'approved_at'];

    protected $casts = [
        'so_date' => 'date',
        'expected_delivery_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    // Relationships
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function entitas()
    {
        return $this->belongsTo(Entitas::class);
    }

    public function salesOrderItems()
    {
        return $this->hasMany(SalesOrderItem::class);
    }

    public function goodsIssues()
    {
        return $this->hasMany(GoodsIssue::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeDraft($query)
    {
        return $query->where('status', 'Draft');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'Approved');
    }

    public function scopePending($query)
    {
        return $query->whereIn('status', ['Draft', 'Submitted']);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'Completed');
    }

    public function scopeOverdue($query)
    {
        return $query->where('expected_delivery_date', '<', Carbon::now())
                    ->whereIn('status', ['Approved', 'Partially_Issued']);
    }

    // Helper methods
    public function getTotalItemsAttribute()
    {
        return $this->salesOrderItems()->count();
    }

    public function getTotalQuantityAttribute()
    {
        return $this->salesOrderItems()->sum('quantity_ordered');
    }

    public function getTotalIssuedQuantityAttribute()
    {
        return $this->salesOrderItems()->sum('quantity_issued');
    }

    public function getIssueProgressAttribute()
    {
        $totalOrdered = $this->total_quantity;
        $totalIssued = $this->total_issued_quantity;
        
        if ($totalOrdered == 0) return 0;
        
        return round(($totalIssued / $totalOrdered) * 100, 2);
    }

    public function getFormattedTotalAmountAttribute()
    {
        return 'Rp ' . number_format($this->total_amount, 0, ',', '.');
    }

    public function canBeApproved()
    {
        return $this->status === 'Submitted';
    }

    public function canBeCancelled()
    {
        return in_array($this->status, ['Draft', 'Submitted', 'Approved']);
    }

    public function canBeCompleted()
    {
        return $this->status === 'Partially_Issued' && $this->issue_progress >= 100;
    }

    // Auto-generate SO number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($salesOrder) {
            if (empty($salesOrder->so_number)) {
                $salesOrder->so_number = static::generateSoNumber();
            }
        });

        static::saved(function ($salesOrder) {
            $salesOrder->updateStatus();
        });
    }

    public static function generateSoNumber()
    {
        $prefix = 'SO';
        $date = Carbon::now()->format('Ymd');
        $lastOrder = static::whereDate('created_at', Carbon::today())
                          ->orderBy('id', 'desc')
                          ->first();
        
        $sequence = $lastOrder ? (int) substr($lastOrder->so_number, -4) + 1 : 1;
        
        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function updateStatus()
    {
        if ($this->status === 'Cancelled' || $this->status === 'Completed') {
            return;
        }

        $totalOrdered = $this->total_quantity;
        $totalIssued = $this->total_issued_quantity;

        if ($totalIssued == 0) {
            // No items issued yet
            if ($this->status !== 'Draft' && $this->status !== 'Submitted') {
                $this->status = 'Approved';
            }
        } elseif ($totalIssued < $totalOrdered) {
            // Partially issued
            $this->status = 'Partially_Issued';
        } else {
            // Fully issued
            $this->status = 'Completed';
        }

        $this->saveQuietly(); // Prevent infinite loop
    }

    public function updateTotals()
    {
        $this->subtotal = $this->salesOrderItems()->sum('total_price');
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        $this->saveQuietly();
    }
}
