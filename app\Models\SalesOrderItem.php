<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesOrderItem extends Model
{
    use HasFactory;

    protected $table = 'sales_order_items';

    protected $fillable = [
        'sales_order_id',
        'product_id',
        'quantity_ordered',
        'quantity_issued',
        'unit_price',
        'total_price',
        'notes',
    ];

    protected $casts = [
        'quantity_ordered' => 'integer',
        'quantity_issued' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    // Relationships
    public function salesOrder()
    {
        return $this->belongsTo(SalesOrder::class);
    }

    public function product()
    {
        return $this->belongsTo(Produk::class, 'product_id');
    }

    public function goodsIssueItems()
    {
        return $this->hasMany(GoodsIssueItem::class);
    }

    // Helper methods
    public function getRemainingQuantityAttribute()
    {
        return $this->quantity_ordered - $this->quantity_issued;
    }

    public function getIssueProgressAttribute()
    {
        if ($this->quantity_ordered == 0) return 0;
        return round(($this->quantity_issued / $this->quantity_ordered) * 100, 2);
    }

    public function getFormattedUnitPriceAttribute()
    {
        return 'Rp ' . number_format($this->unit_price, 0, ',', '.');
    }

    public function getFormattedTotalPriceAttribute()
    {
        return 'Rp ' . number_format($this->total_price, 0, ',', '.');
    }

    public function isFullyIssued()
    {
        return $this->quantity_issued >= $this->quantity_ordered;
    }

    public function canIssueQuantity($quantity)
    {
        return $quantity <= $this->remaining_quantity;
    }

    // Auto-calculate total price
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->total_price = $item->quantity_ordered * $item->unit_price;
        });

        static::saved(function ($item) {
            // Update parent sales order totals
            $item->salesOrder->updateTotals();
        });
    }
}
