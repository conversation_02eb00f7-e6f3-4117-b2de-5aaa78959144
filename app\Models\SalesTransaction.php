<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SalesTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'sales_transactions';

    protected $fillable = [
        'transaction_code',
        'transaction_date',
        'customer_name',
        'warehouse_id',
        'entitas_id',
        'payment_method',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'notes',
        'status',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'transaction_date'];

    protected $casts = [
        'transaction_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    // Relationships
    public function saleItems()
    {
        return $this->hasMany(SaleItem::class);
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function entitas()
    {
        return $this->belongsTo(Entitas::class);
    }

    public function journals()
    {
        return $this->morphMany(Journal::class, 'source');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'Completed');
    }

    public function scopeByPaymentMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    public function scopeByWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    // Helper methods
    public function getTotalNetAmountAttribute()
    {
        return $this->subtotal - $this->discount_amount;
    }

    public function getTotalCostAttribute()
    {
        return $this->saleItems()->sum('total_cost');
    }

    public function getGrossProfitAttribute()
    {
        return $this->total_net_amount - $this->total_cost;
    }

    public function getGrossProfitMarginAttribute()
    {
        if ($this->total_net_amount == 0) {
            return 0;
        }
        return ($this->gross_profit / $this->total_net_amount) * 100;
    }

    // Auto-generate transaction code
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (empty($transaction->transaction_code)) {
                $transaction->transaction_code = static::generateTransactionCode();
            }
        });
    }

    public static function generateTransactionCode()
    {
        $date = date('Ymd');
        $lastTransaction = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastTransaction ? (int)substr($lastTransaction->transaction_code, -4) + 1 : 1;

        return sprintf('TRX-%s-%04d', $date, $sequence);
    }
}
