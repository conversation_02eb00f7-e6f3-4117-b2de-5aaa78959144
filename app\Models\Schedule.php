<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Schedule extends Model
{
    use HasFactory;

    protected $table = 'jadwal_kerja';

    protected $fillable = [
        'karyawan_id',
        'entitas_id',
        'shift_id',
        'supervisor_id',
        'tanggal_jadwal',
        'waktu_masuk',
        'waktu_keluar',
        'status',
        'keterangan',
        'is_approved',
    ];

    protected $casts = [
        'is_approved' => 'boolean',
        'tanggal_jadwal' => 'date',
    ];

    /**
     * Get the employee associated with this schedule
     */
    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class);
    }

    /**
     * Get the shift associated with this schedule
     */
    public function shift()
    {
        return $this->belongsTo(Shift::class);
    }

    /**
     * Get the supervisor who created this schedule
     */
    public function supervisor()
    {
        return $this->belongsTo(User::class, 'supervisor_id');
    }

    /**
     * Get the entitas/entity where this schedule is assigned
     */
    public function entitas()
    {
        return $this->belongsTo(Entitas::class, 'entitas_id');
    }

    /**
     * Get the attendance record associated with this schedule
     */
    public function absensi()
    {
        return $this->hasOne(Absensi::class, 'jadwal_id');
    }

    /**
     * Get the leave/permission record for this schedule date
     */
    public function cutiIzin()
    {
        return $this->hasOneThrough(
            \App\Models\CutiIzin::class,
            \App\Models\Karyawan::class,
            'id', // Foreign key on karyawan table
            'karyawan_id', // Foreign key on cuti_izin table
            'karyawan_id', // Local key on schedules table
            'id' // Local key on karyawan table
        )->where('status', 'approved')
            ->whereDate('tanggal_mulai', '<=', $this->tanggal_jadwal)
            ->whereDate('tanggal_selesai', '>=', $this->tanggal_jadwal);
    }

    /**
     * Get dynamic status based on attendance and current time
     */
    public function getStatusDinamis(): string
    {
        // 1. Cek apakah ada cuti/izin/sakit yang approved
        $cutiIzin = \App\Models\CutiIzin::where('karyawan_id', $this->karyawan_id)
            ->where('status', 'approved')
            ->whereDate('tanggal_mulai', '<=', $this->tanggal_jadwal)
            ->whereDate('tanggal_selesai', '>=', $this->tanggal_jadwal)
            ->first();

        if ($cutiIzin) {
            return ucfirst($cutiIzin->jenis_permohonan); // Cuti, Izin, Sakit
        }

        // 2. Cek apakah sudah ada absensi berdasarkan jadwal_id atau kombinasi karyawan+tanggal
        $absensi = $this->absensi;

        // Jika tidak ada absensi berdasarkan jadwal_id, cari berdasarkan karyawan_id dan tanggal
        if (!$absensi) {
            $absensi = \App\Models\Absensi::where('karyawan_id', $this->karyawan_id)
                ->whereDate('tanggal_absensi', $this->tanggal_jadwal)
                ->first();
        }

        if ($absensi) {
            // Jika sudah absen, ambil status dari absensi
            if ($absensi->waktu_masuk && $absensi->waktu_keluar) {
                // Sudah absen masuk dan keluar - gunakan status dari database atau hitung keterlambatan
                if ($absensi->status === 'terlambat') {
                    return 'Hadir Terlambat';
                } elseif (in_array($absensi->status, ['hadir'])) {
                    return 'Hadir Tepat Waktu';
                } else {
                    // Fallback: hitung keterlambatan jika status tidak jelas
                    $isLate = $this->checkIfLateFromAbsensi($absensi);
                    return $isLate ? 'Hadir Terlambat' : 'Hadir Tepat Waktu';
                }
            } elseif ($absensi->waktu_masuk) {
                // Sudah absen masuk, belum keluar
                if ($absensi->status === 'terlambat') {
                    return 'Hadir Terlambat (Belum Keluar)';
                } elseif (in_array($absensi->status, ['hadir'])) {
                    return 'Hadir Tepat Waktu (Belum Keluar)';
                } else {
                    // Fallback: hitung keterlambatan jika status tidak jelas
                    $isLate = $this->checkIfLateFromAbsensi($absensi);
                    return $isLate ? 'Hadir Terlambat (Belum Keluar)' : 'Hadir Tepat Waktu (Belum Keluar)';
                }
            }
        }

        // 3. Belum ada absensi, cek berdasarkan waktu
        $now = \Carbon\Carbon::now();
        $tanggalJadwal = \Carbon\Carbon::parse($this->tanggal_jadwal);

        // Jika bukan hari ini, tidak perlu cek waktu
        if (!$tanggalJadwal->isToday()) {
            if ($tanggalJadwal->isFuture()) {
                return 'Belum Absen';
            } else {
                // Jadwal sudah lewat tapi belum absen
                return 'Alfa';
            }
        }

        // Untuk hari ini, cek berdasarkan waktu
        try {
            $waktuMasukJadwal = \Carbon\Carbon::createFromFormat('H:i:s', $this->waktu_masuk);
            $waktuMasukHariIni = $tanggalJadwal->copy()
                ->setHour($waktuMasukJadwal->hour)
                ->setMinute($waktuMasukJadwal->minute)
                ->setSecond($waktuMasukJadwal->second);

            // Jika jadwal belum tiba
            if ($now->lessThan($waktuMasukHariIni)) {
                return 'Belum Absen';
            }

            // Jika sudah lewat waktu masuk + toleransi (2 jam)
            $batasAlfa = $waktuMasukHariIni->copy()->addHours(2);
            if ($now->greaterThan($batasAlfa)) {
                return 'Alfa';
            }

            // Jika dalam rentang waktu kerja tapi belum absen
            return 'Belum Absen';
        } catch (\Exception) {
            // Jika ada error parsing waktu, return default
            return 'Belum Absen';
        }
    }

    /**
     * Check if attendance time is late based on schedule (legacy method)
     */
    private function checkIfLate($waktuMasuk): bool
    {
        if (!$this->shift || !$waktuMasuk) {
            return false;
        }

        try {
            $actualEntry = \Carbon\Carbon::parse($waktuMasuk);
            $shiftStart = \Carbon\Carbon::createFromFormat('H:i:s', $this->shift->waktu_mulai);

            // Set tanggal yang sama untuk perbandingan
            $shiftStartToday = $actualEntry->copy()
                ->setHour($shiftStart->hour)
                ->setMinute($shiftStart->minute)
                ->setSecond($shiftStart->second);

            $toleranceMinutes = $this->shift->toleransi_keterlambatan ?? 0;
            $shiftStartWithTolerance = $shiftStartToday->copy()->addMinutes($toleranceMinutes);

            return $actualEntry->greaterThan($shiftStartWithTolerance);
        } catch (\Exception) {
            return false;
        }
    }

    /**
     * Check if attendance is late using the same logic as Absensi model
     */
    private function checkIfLateFromAbsensi($absensi): bool
    {
        if (!$this->shift || !$absensi->waktu_masuk) {
            return false;
        }

        try {
            $shift = $this->shift;
            $actualEntry = \Carbon\Carbon::parse($absensi->waktu_masuk);

            // Handle split shift (same logic as in Absensi model)
            if ($shift->isSplitShift()) {
                $currentPeriod = $shift->getCurrentPeriod($actualEntry->format('H:i:s'));
                $periods = $shift->getWorkPeriods();

                foreach ($periods as $period) {
                    if ($period['periode'] == $currentPeriod) {
                        $shiftStart = \Carbon\Carbon::parse($period['waktu_mulai']);
                        $toleranceMinutes = $period['toleransi_keterlambatan'] ?? 0;

                        // Set same date for comparison
                        $shiftStartToday = $actualEntry->copy()
                            ->setHour($shiftStart->hour)
                            ->setMinute($shiftStart->minute)
                            ->setSecond($shiftStart->second);

                        return $actualEntry->greaterThan($shiftStartToday->addMinutes($toleranceMinutes));
                    }
                }
            } else {
                // Regular shift logic (same as in Absensi model)
                $shiftStart = \Carbon\Carbon::parse($shift->waktu_mulai);
                $toleranceMinutes = $shift->toleransi_keterlambatan ?? 0;

                // Set same date for comparison
                $shiftStartToday = $actualEntry->copy()
                    ->setHour($shiftStart->hour)
                    ->setMinute($shiftStart->minute)
                    ->setSecond($shiftStart->second);

                return $actualEntry->greaterThan($shiftStartToday->addMinutes($toleranceMinutes));
            }

            return false;
        } catch (\Exception) {
            return false;
        }
    }

    /**
     * Get status color for display
     */
    public function getStatusColor(): string
    {
        $status = $this->getStatusDinamis();

        return match (true) {
            str_contains($status, 'Tepat Waktu') => 'success',
            str_contains($status, 'Terlambat') => 'warning',
            $status === 'Alfa' => 'danger',
            in_array($status, ['Cuti', 'Izin', 'Sakit']) => 'info',
            $status === 'Belum Absen' => 'secondary',
            default => 'secondary'
        };
    }

    /**
     * Debug method to check status calculation
     */
    public function debugStatusDinamis(): array
    {
        $absensi = $this->absensi;

        // Jika tidak ada absensi berdasarkan jadwal_id, cari berdasarkan karyawan_id dan tanggal
        if (!$absensi) {
            $absensi = \App\Models\Absensi::where('karyawan_id', $this->karyawan_id)
                ->whereDate('tanggal_absensi', $this->tanggal_jadwal)
                ->first();
        }

        $debug = [
            'jadwal_info' => [
                'id' => $this->id,
                'karyawan_id' => $this->karyawan_id,
                'tanggal_jadwal' => $this->tanggal_jadwal,
                'shift_id' => $this->shift_id,
                'waktu_masuk' => $this->waktu_masuk,
                'waktu_keluar' => $this->waktu_keluar,
            ],
            'absensi_info' => null,
            'status_calculation' => [],
        ];

        if ($absensi) {
            $debug['absensi_info'] = [
                'id' => $absensi->id,
                'jadwal_id' => $absensi->jadwal_id,
                'karyawan_id' => $absensi->karyawan_id,
                'tanggal_absensi' => $absensi->tanggal_absensi,
                'waktu_masuk' => $absensi->waktu_masuk,
                'waktu_keluar' => $absensi->waktu_keluar,
                'status' => $absensi->status,
                'periode' => $absensi->periode ?? 1,
            ];

            $debug['status_calculation'] = [
                'has_waktu_masuk' => !is_null($absensi->waktu_masuk),
                'has_waktu_keluar' => !is_null($absensi->waktu_keluar),
                'status_from_db' => $absensi->status,
                'is_late_calculated' => $this->checkIfLateFromAbsensi($absensi),
                'final_status' => $this->getStatusDinamis(),
            ];
        } else {
            $debug['status_calculation'] = [
                'no_absensi_found' => true,
                'final_status' => $this->getStatusDinamis(),
            ];
        }

        return $debug;
    }
}
