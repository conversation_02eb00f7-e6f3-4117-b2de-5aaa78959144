<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SopDokumen extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'sop_dokumens';

    protected $fillable = [
        'judul_sop',
        'deskripsi',
        'file_path',
        'scope_type',
        'departemen_id',
        'divisi_id',
        'status',
        'tanggal_berlaku',
        'tanggal_berakhir',
        'versi',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    protected $casts = [
        'tanggal_berlaku' => 'date',
        'tanggal_berakhir' => 'date',
    ];

    // Relasi ke Departemen
    public function departemen()
    {
        return $this->belongsTo(Departemen::class);
    }

    // Relasi ke Divisi
    public function divisi()
    {
        return $this->belongsTo(Divisi::class);
    }

    // Relasi ke User yang membuat
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scope untuk SOP aktif
    public function scopeAktif($query)
    {
        return $query->where('status', 'aktif');
    }

    // Scope untuk SOP yang masih berlaku
    public function scopeBerlaku($query)
    {
        $today = now()->toDateString();
        return $query->where(function ($q) use ($today) {
            $q->where('tanggal_berlaku', '<=', $today)
                ->where(function ($q2) use ($today) {
                    $q2->whereNull('tanggal_berakhir')
                        ->orWhere('tanggal_berakhir', '>=', $today);
                });
        });
    }

    // Scope untuk SOP berdasarkan departemen
    public function scopeForDepartemen($query, $departemenId)
    {
        return $query->where('scope_type', 'departemen')
            ->where('departemen_id', $departemenId);
    }

    // Scope untuk SOP berdasarkan divisi
    public function scopeForDivisi($query, $divisiId)
    {
        return $query->where('scope_type', 'divisi')
            ->where('divisi_id', $divisiId);
    }

    // Scope untuk SOP berdasarkan karyawan (departemen dan divisi)
    public function scopeForKaryawan($query, $karyawan)
    {
        if (!$karyawan) {
            return $query->whereRaw('1 = 0');
        }

        // Jika karyawan tidak memiliki departemen dan divisi, return query kosong
        if (!$karyawan->id_departemen && !$karyawan->id_divisi) {
            return $query->whereRaw('1 = 0');
        }

        return $query->where(function ($q) use ($karyawan) {
            $hasCondition = false;

            // SOP untuk departemen karyawan
            if ($karyawan->id_departemen) {
                $q->where(function ($subQ) use ($karyawan) {
                    $subQ->where('scope_type', 'departemen')
                        ->where('departemen_id', $karyawan->id_departemen);
                });
                $hasCondition = true;
            }

            // SOP untuk divisi karyawan
            if ($karyawan->id_divisi) {
                if ($hasCondition) {
                    $q->orWhere(function ($subQ) use ($karyawan) {
                        $subQ->where('scope_type', 'divisi')
                            ->where('divisi_id', $karyawan->id_divisi);
                    });
                } else {
                    $q->where(function ($subQ) use ($karyawan) {
                        $subQ->where('scope_type', 'divisi')
                            ->where('divisi_id', $karyawan->id_divisi);
                    });
                }
            }
        });
    }

    // Accessor untuk mendapatkan nama scope
    public function getScopeNameAttribute()
    {
        if ($this->scope_type === 'departemen') {
            return $this->departemen?->nama_departemen ?? 'Departemen Tidak Ditemukan';
        } elseif ($this->scope_type === 'divisi') {
            return $this->divisi?->nama_divisi ?? 'Divisi Tidak Ditemukan';
        }
        return 'Tidak Diketahui';
    }

    // Accessor untuk status badge
    public function getStatusBadgeAttribute()
    {
        return match ($this->status) {
            'aktif' => 'success',
            'tidak_aktif' => 'danger',
            default => 'secondary'
        };
    }

    // Method untuk cek apakah SOP masih berlaku
    public function isBerlaku()
    {
        $today = now()->toDate();

        if ($this->status !== 'aktif') {
            return false;
        }

        if ($this->tanggal_berlaku && $this->tanggal_berlaku > $today) {
            return false;
        }

        if ($this->tanggal_berakhir && $this->tanggal_berakhir < $today) {
            return false;
        }

        return true;
    }
}
