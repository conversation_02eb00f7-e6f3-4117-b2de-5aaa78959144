<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class StockAdjustment extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'stock_adjustments';

    protected $fillable = [
        'adjustment_number',
        'adjustment_date',
        'adjustment_type',
        'adjustment_reason',
        'warehouse_id',
        'entitas_id',
        'description',
        'status',
        'notes',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    protected $dates = ['deleted_at', 'adjustment_date', 'approved_at'];

    protected $casts = [
        'adjustment_date' => 'date',
        'approved_at' => 'datetime',
    ];

    // Adjustment types
    const ADJUSTMENT_TYPES = [
        'Increase' => 'Penambahan',
        'Decrease' => 'Pengurangan',
    ];

    // Adjustment reasons
    const ADJUSTMENT_REASONS = [
        'Damaged' => 'Barang Rusak',
        'Expired' => 'Kadaluarsa',
        'Lost' => 'Kehilangan',
        'Found' => 'Barang Ditemukan',
        'Correction' => 'Koreksi',
        'Write_Off' => 'Penghapusan',
        'Other' => 'Lainnya',
    ];

    // Status
    const STATUS_DRAFT = 'Draft';
    const STATUS_APPROVED = 'Approved';
    const STATUS_CANCELLED = 'Cancelled';

    // Relationships
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function entitas()
    {
        return $this->belongsTo(Entitas::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function stockAdjustmentItems()
    {
        return $this->hasMany(StockAdjustmentItem::class);
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    public function scopeByEntitas($query, $entitasId)
    {
        return $query->where('entitas_id', $entitasId);
    }

    public function scopeDraft($query)
    {
        return $query->where('status', self::STATUS_DRAFT);
    }

    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    // Helper methods
    public function getAdjustmentTypeNameAttribute()
    {
        return self::ADJUSTMENT_TYPES[$this->adjustment_type] ?? $this->adjustment_type;
    }

    public function getStatusColorAttribute()
    {
        return match ($this->status) {
            self::STATUS_DRAFT => 'gray',
            self::STATUS_APPROVED => 'success',
            self::STATUS_CANCELLED => 'danger',
            default => 'gray'
        };
    }

    public function getTotalItemsAttribute()
    {
        return $this->stockAdjustmentItems()->count();
    }

    public function getTotalQuantityAdjustmentAttribute()
    {
        return $this->stockAdjustmentItems()->sum('quantity');
    }

    public function canBeApproved()
    {
        return $this->status === self::STATUS_DRAFT && $this->stockAdjustmentItems()->count() > 0;
    }

    public function canBeCancelled()
    {
        return $this->status === self::STATUS_DRAFT;
    }

    public function canBeEdited()
    {
        return $this->status === self::STATUS_DRAFT;
    }

    public function approve($approvedBy = null)
    {
        if (!$this->canBeApproved()) {
            throw new \Exception('Stock adjustment cannot be approved');
        }

        $this->status = self::STATUS_APPROVED;
        $this->approved_by = $approvedBy ?: auth()->id();
        $this->approved_at = Carbon::now();
        $this->save();

        // Create stock movements for each adjustment item
        $this->createStockMovements();

        // Calculate total value adjustment
        $this->calculateTotalValueAdjustment();
    }

    public function cancel()
    {
        if (!$this->canBeCancelled()) {
            throw new \Exception('Stock adjustment cannot be cancelled');
        }

        $this->status = self::STATUS_CANCELLED;
        $this->save();
    }

    public function processAdjustment()
    {
        if ($this->status !== self::STATUS_APPROVED) {
            throw new \Exception('Only approved adjustments can be processed');
        }

        // Process each adjustment item
        foreach ($this->stockAdjustmentItems as $item) {
            if ($item->quantity_adjustment != 0) {
                $this->updateInventoryStock($item);
            }
        }

        // Create stock movements
        $this->createStockMovements();

        // Calculate total value adjustment
        $this->calculateTotalValueAdjustment();
    }

    protected function updateInventoryStock($item)
    {
        $inventoryStock = InventoryStock::where('product_id', $item->product_id)
            ->where('warehouse_id', $this->warehouse_id)
            ->where('entitas_id', $this->entitas_id)
            ->first();

        if (!$inventoryStock) {
            // Create new inventory stock record if it doesn't exist
            $inventoryStock = InventoryStock::create([
                'product_id' => $item->product_id,
                'warehouse_id' => $this->warehouse_id,
                'entitas_id' => $this->entitas_id,
                'quantity' => max(0, $item->quantity_adjustment),
                'available_quantity' => max(0, $item->quantity_adjustment),
                'on_hold_quantity' => 0,
                'reserved_quantity' => 0,
                'average_cost' => $item->unit_cost,
                'total_value' => max(0, $item->quantity_adjustment) * $item->unit_cost,
                'minimum_stock' => 0,
                'maximum_stock' => 0,
                'last_movement_at' => now(),
                'last_movement_type' => $item->quantity_adjustment > 0 ? 'Adjustment_In' : 'Adjustment_Out',
            ]);
        } else {
            // Update existing inventory stock
            $newQuantity = max(0, $inventoryStock->quantity + $item->quantity_adjustment);
            $newAvailableQuantity = max(0, $inventoryStock->available_quantity + $item->quantity_adjustment);

            $inventoryStock->quantity = $newQuantity;
            $inventoryStock->available_quantity = $newAvailableQuantity;
            $inventoryStock->last_movement_at = now();
            $inventoryStock->last_movement_type = $item->quantity_adjustment > 0 ? 'Adjustment_In' : 'Adjustment_Out';
            $inventoryStock->updateTotalValue();
        }
    }

    protected function createStockMovements()
    {
        foreach ($this->stockAdjustmentItems as $item) {
            if ($item->quantity_adjustment != 0) {
                $movementType = $item->quantity_adjustment > 0 ? 'Adjustment_In' : 'Adjustment_Out';

                StockMovement::create([
                    'movement_date' => $this->adjustment_date,
                    'movement_type' => $movementType,
                    'product_id' => $item->product_id,
                    'warehouse_id' => $this->warehouse_id,
                    'entitas_id' => $this->entitas_id,
                    'quantity' => abs($item->quantity_adjustment),
                    'unit_cost' => $item->unit_cost,
                    'reference_type' => StockAdjustment::class,
                    'reference_id' => $this->id,
                    'reference_number' => $this->adjustment_number,
                    'notes' => $this->description . ' - ' . $item->item_notes,
                    'created_by' => $this->approved_by,
                ]);
            }
        }
    }

    protected function calculateTotalValueAdjustment()
    {
        $this->total_value_adjustment = $this->stockAdjustmentItems()
            ->selectRaw('SUM(quantity_adjustment * unit_cost) as total')
            ->value('total') ?? 0;
        $this->save();
    }

    // Auto-generate adjustment number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($adjustment) {
            if (empty($adjustment->adjustment_number)) {
                $adjustment->adjustment_number = static::generateAdjustmentNumber();
            }
        });
    }

    public static function generateAdjustmentNumber()
    {
        $prefix = 'ADJ';
        $date = Carbon::now()->format('Ymd');
        $lastAdjustment = static::whereDate('created_at', Carbon::today())
            ->where('adjustment_number', 'like', $prefix . $date . '%')
            ->orderBy('adjustment_number', 'desc')
            ->first();

        if ($lastAdjustment) {
            $lastNumber = intval(substr($lastAdjustment->adjustment_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . $date . $newNumber;
    }
}
