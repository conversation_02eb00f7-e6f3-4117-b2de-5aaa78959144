<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StockAdjustmentItem extends Model
{
    use HasFactory;

    protected $table = 'stock_adjustment_items';

    protected $fillable = [
        'stock_adjustment_id',
        'product_id',
        'system_quantity',
        'physical_quantity',
        'quantity_adjustment',
        'unit_cost',
        'total_adjustment_value',
        'item_notes',
    ];

    protected $casts = [
        'system_quantity' => 'integer',
        'physical_quantity' => 'integer',
        'quantity_adjustment' => 'integer',
        'unit_cost' => 'decimal:2',
        'total_adjustment_value' => 'decimal:2',
    ];

    // Relationships
    public function stockAdjustment()
    {
        return $this->belongsTo(StockAdjustment::class);
    }

    public function product()
    {
        return $this->belongsTo(Produk::class, 'product_id');
    }

    // Helper methods
    public function getFormattedTotalValueAttribute()
    {
        return 'Rp ' . number_format($this->total_adjustment_value, 0, ',', '.');
    }

    public function getFormattedUnitCostAttribute()
    {
        return 'Rp ' . number_format($this->unit_cost, 0, ',', '.');
    }

    // Auto-calculate total value
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->total_adjustment_value = $item->quantity_adjustment * $item->unit_cost;
        });
    }
}
