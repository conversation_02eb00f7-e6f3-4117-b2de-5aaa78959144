<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class StockMovement extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'stock_movements';

    protected $fillable = [
        'movement_number',
        'movement_date',
        'movement_type',
        'product_id',
        'warehouse_id',
        'entitas_id',
        'quantity',
        'unit_cost',
        'total_value',
        'reference_type',
        'reference_id',
        'reference_number',
        'notes',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'movement_date'];

    protected $casts = [
        'movement_date' => 'date',
        'quantity' => 'integer',
        'unit_cost' => 'decimal:2',
        'total_value' => 'decimal:2',
    ];

    // Movement types
    const MOVEMENT_TYPES = [
        'Purchase_Receipt' => 'Penerimaan Pembelian',
        'Sales_Issue' => 'Pengeluaran Penjualan',
        'Transfer_Out' => 'Transfer Keluar',
        'Transfer_In' => 'Transfer Masuk',
        'Adjustment_In' => 'Penyesuaian Masuk',
        'Adjustment_Out' => 'Penyesuaian Keluar',
        'Opening_Balance' => 'Saldo Awal',
        'Production_In' => 'Produksi Masuk',
        'Production_Out' => 'Produksi Keluar',
    ];

    // Relationships
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function entitas()
    {
        return $this->belongsTo(Entitas::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function reference()
    {
        return $this->morphTo('reference', 'reference_type', 'reference_id');
    }

    // Scopes
    public function scopeByMovementType($query, $type)
    {
        return $query->where('movement_type', $type);
    }

    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeByWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    public function scopeByEntitas($query, $entitasId)
    {
        return $query->where('entitas_id', $entitasId);
    }

    public function scopeIncoming($query)
    {
        return $query->whereIn('movement_type', [
            'Purchase_Receipt',
            'Transfer_In',
            'Adjustment_In',
            'Opening_Balance',
            'Production_In'
        ]);
    }

    public function scopeOutgoing($query)
    {
        return $query->whereIn('movement_type', [
            'Sales_Issue',
            'Transfer_Out',
            'Adjustment_Out',
            'Production_Out'
        ]);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('movement_date', [$startDate, $endDate]);
    }

    // Helper methods
    public function getMovementTypeNameAttribute()
    {
        return self::MOVEMENT_TYPES[$this->movement_type] ?? $this->movement_type;
    }

    public function getMovementDirectionAttribute()
    {
        $incomingTypes = ['Purchase_Receipt', 'Transfer_In', 'Adjustment_In', 'Opening_Balance', 'Production_In'];
        return in_array($this->movement_type, $incomingTypes) ? 'IN' : 'OUT';
    }

    public function getSignedQuantityAttribute()
    {
        return $this->movement_direction === 'IN' ? $this->quantity : -$this->quantity;
    }

    public function getFormattedTotalValueAttribute()
    {
        return 'Rp ' . number_format($this->total_value, 0, ',', '.');
    }

    // Auto-generate movement number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($movement) {
            if (empty($movement->movement_number)) {
                $movement->movement_number = static::generateMovementNumber();
            }

            // Auto-calculate total value
            $movement->total_value = $movement->quantity * $movement->unit_cost;
        });

        static::created(function ($movement) {
            // Update inventory stock after movement is created
            $movement->updateInventoryStock();
        });
    }

    public static function generateMovementNumber()
    {
        $prefix = 'MOV';
        $date = Carbon::now()->format('Ymd');
        $lastMovement = static::whereDate('created_at', Carbon::today())
            ->where('movement_number', 'like', $prefix . $date . '%')
            ->orderBy('movement_number', 'desc')
            ->first();

        if ($lastMovement) {
            $lastNumber = intval(substr($lastMovement->movement_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . $date . $newNumber;
    }

    public function updateInventoryStock()
    {
        // For now, use only product_id and warehouse_id due to unique constraint
        $inventoryStock = InventoryStock::where('product_id', $this->product_id)
            ->where('warehouse_id', $this->warehouse_id)
            ->first();

        if ($inventoryStock) {
            // Update existing stock
            $newQuantity = $inventoryStock->quantity + $this->signed_quantity;

            // Calculate new average cost for incoming movements
            if ($this->movement_direction === 'IN' && $this->quantity > 0) {
                $totalValue = ($inventoryStock->quantity * $inventoryStock->average_cost) +
                    ($this->quantity * $this->unit_cost);
                $totalQuantity = $inventoryStock->quantity + $this->quantity;

                if ($totalQuantity > 0) {
                    $inventoryStock->average_cost = $totalValue / $totalQuantity;
                }
            }

            $inventoryStock->quantity = max(0, $newQuantity); // Prevent negative stock
            $inventoryStock->updateTotalValue();
        } else {
            // Create new inventory stock for incoming movements
            if ($this->movement_direction === 'IN') {
                InventoryStock::create([
                    'product_id' => $this->product_id,
                    'warehouse_id' => $this->warehouse_id,
                    'entitas_id' => $this->entitas_id, // Use first entitas for this product/warehouse
                    'quantity' => $this->quantity,
                    'average_cost' => $this->unit_cost,
                    'total_value' => $this->total_value,
                    'minimum_stock' => 0,
                    'maximum_stock' => 0,
                    'last_updated' => Carbon::now(),
                ]);
            }
        }
    }
}
