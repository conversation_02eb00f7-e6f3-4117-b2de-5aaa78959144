<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class StockOpname extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'stock_opnames';

    protected $fillable = [
        'opname_number',
        'opname_date',
        'entitas_id',
        'warehouse_id',
        'status',
        'description',
        'notes',
        'total_variance_value',
        'total_items_counted',
        'total_variance_items',
        'created_by',
        'approved_by',
        'approved_at',
        'completed_by',
        'completed_at',
    ];

    protected $dates = ['deleted_at', 'opname_date', 'approved_at', 'completed_at'];

    protected $casts = [
        'opname_date' => 'date',
        'approved_at' => 'datetime',
        'completed_at' => 'datetime',
        'total_variance_value' => 'decimal:2',
        'total_items_counted' => 'integer',
        'total_variance_items' => 'integer',
    ];

    // Status constants
    const STATUS_DRAFT = 'Draft';
    const STATUS_IN_PROGRESS = 'In_Progress';
    const STATUS_COMPLETED = 'Completed';
    const STATUS_CANCELLED = 'Cancelled';

    // Relationships
    public function entitas()
    {
        return $this->belongsTo(Entitas::class);
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function completedBy()
    {
        return $this->belongsTo(User::class, 'completed_by');
    }

    public function stockOpnameItems()
    {
        return $this->hasMany(StockOpnameItem::class);
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByEntitas($query, $entitasId)
    {
        return $query->where('entitas_id', $entitasId);
    }

    public function scopeByWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    public function scopeDraft($query)
    {
        return $query->where('status', self::STATUS_DRAFT);
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', self::STATUS_IN_PROGRESS);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    // Helper methods
    public function getStatusColorAttribute()
    {
        return match ($this->status) {
            self::STATUS_DRAFT => 'gray',
            self::STATUS_IN_PROGRESS => 'warning',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_CANCELLED => 'danger',
            default => 'gray'
        };
    }

    public function getTotalItemsAttribute()
    {
        return $this->stockOpnameItems()->count();
    }

    public function getCountedItemsAttribute()
    {
        return $this->stockOpnameItems()->where('is_counted', true)->count();
    }

    public function getCountingProgressAttribute()
    {
        $total = $this->total_items;
        if ($total == 0) return 0;
        return round(($this->counted_items / $total) * 100, 2);
    }

    public function getSurplusItemsAttribute()
    {
        return $this->stockOpnameItems()->where('variance_type', 'Surplus')->count();
    }

    public function getShortageItemsAttribute()
    {
        return $this->stockOpnameItems()->where('variance_type', 'Shortage')->count();
    }

    public function getMatchItemsAttribute()
    {
        return $this->stockOpnameItems()->where('variance_type', 'Match')->count();
    }

    public function canBeStarted()
    {
        return $this->status === self::STATUS_DRAFT && $this->stockOpnameItems()->count() > 0;
    }

    public function canBeCompleted()
    {
        return $this->status === self::STATUS_IN_PROGRESS &&
            $this->stockOpnameItems()->where('is_counted', false)->count() === 0;
    }

    public function canBeCancelled()
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_IN_PROGRESS]);
    }

    public function canBeEdited()
    {
        return $this->status === self::STATUS_DRAFT;
    }

    public function startOpname($userId = null)
    {
        if (!$this->canBeStarted()) {
            throw new \Exception('Stock opname cannot be started');
        }

        $this->status = self::STATUS_IN_PROGRESS;
        $this->approved_by = $userId ?: auth()->id();
        $this->approved_at = Carbon::now();
        $this->save();

        // Load current stock quantities
        $this->loadCurrentStockQuantities();
    }

    public function completeOpname($userId = null)
    {
        if (!$this->canBeCompleted()) {
            throw new \Exception('Stock opname cannot be completed');
        }

        $this->status = self::STATUS_COMPLETED;
        $this->completed_by = $userId ?: auth()->id();
        $this->completed_at = Carbon::now();

        // Calculate totals
        $this->calculateTotals();

        $this->save();

        // Generate stock adjustments
        $this->generateStockAdjustments();
    }

    public function cancelOpname()
    {
        if (!$this->canBeCancelled()) {
            throw new \Exception('Stock opname cannot be cancelled');
        }

        $this->status = self::STATUS_CANCELLED;
        $this->save();
    }

    protected function loadCurrentStockQuantities()
    {
        foreach ($this->stockOpnameItems as $item) {
            $currentStock = InventoryStock::where('product_id', $item->product_id)
                ->where('warehouse_id', $this->warehouse_id)
                ->where('entitas_id', $this->entitas_id)
                ->first();

            $item->system_quantity = $currentStock ? $currentStock->quantity : 0;
            $item->unit_cost = $currentStock ? $currentStock->average_cost : 0;
            $item->save();
        }
    }

    protected function calculateTotals()
    {
        $this->total_items_counted = $this->stockOpnameItems()->where('is_counted', true)->count();
        $this->total_variance_items = $this->stockOpnameItems()->where('variance_type', '!=', 'Match')->count();
        $this->total_variance_value = $this->stockOpnameItems()->sum('variance_value');
        $this->save();
    }

    protected function generateStockAdjustments()
    {
        $varianceItems = $this->stockOpnameItems()->where('variance_type', '!=', 'Match')->get();

        if ($varianceItems->count() > 0) {
            $adjustment = StockAdjustment::create([
                'adjustment_date' => $this->completed_at,
                'adjustment_type' => 'Stock_Opname',
                'adjustment_reason' => 'Stock_Opname',
                'warehouse_id' => $this->warehouse_id,
                'entitas_id' => $this->entitas_id,
                'description' => 'Stock adjustment from opname: ' . $this->opname_number,
                'status' => 'Approved',
                'created_by' => $this->completed_by,
                'approved_by' => $this->completed_by,
                'approved_at' => $this->completed_at,
            ]);

            foreach ($varianceItems as $item) {
                if ($item->variance_quantity != 0) {
                    StockAdjustmentItem::create([
                        'stock_adjustment_id' => $adjustment->id,
                        'product_id' => $item->product_id,
                        'quantity' => $item->variance_quantity,
                        'unit_cost' => $item->unit_cost,
                        'item_notes' => 'Stock opname variance: ' . $item->variance_type,
                    ]);
                }
            }
        }
    }

    // Auto-generate opname number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($opname) {
            if (empty($opname->opname_number)) {
                $opname->opname_number = static::generateOpnameNumber();
            }
        });
    }

    public static function generateOpnameNumber()
    {
        $prefix = 'SO';
        $date = Carbon::now()->format('Ymd');
        $lastOpname = static::whereDate('created_at', Carbon::today())
            ->where('opname_number', 'like', $prefix . $date . '%')
            ->orderBy('opname_number', 'desc')
            ->first();

        if ($lastOpname) {
            $lastNumber = intval(substr($lastOpname->opname_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . $date . $newNumber;
    }
}
