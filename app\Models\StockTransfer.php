<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class StockTransfer extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'stock_transfers';

    protected $fillable = [
        'transfer_number',
        'transfer_date',
        'from_warehouse_id',
        'to_warehouse_id',
        'from_entitas_id',
        'to_entitas_id',
        'status',
        'transfer_reason',
        'notes',
        'requested_by',
        'approved_by',
        'approved_at',
        'shipped_by',
        'shipped_at',
        'sent_by',
        'sent_at',
        'received_by',
        'received_at',
    ];

    protected $dates = ['deleted_at', 'transfer_date', 'approved_at', 'shipped_at', 'sent_at', 'received_at'];

    protected $casts = [
        'transfer_date' => 'date',
        'approved_at' => 'datetime',
        'shipped_at' => 'datetime',
        'sent_at' => 'datetime',
        'received_at' => 'datetime',
    ];

    // Relationships
    public function fromWarehouse()
    {
        return $this->belongsTo(Warehouse::class, 'from_warehouse_id');
    }

    public function toWarehouse()
    {
        return $this->belongsTo(Warehouse::class, 'to_warehouse_id');
    }

    public function fromEntitas()
    {
        return $this->belongsTo(Entitas::class, 'from_entitas_id');
    }

    public function toEntitas()
    {
        return $this->belongsTo(Entitas::class, 'to_entitas_id');
    }

    public function stockTransferItems()
    {
        return $this->hasMany(StockTransferItem::class);
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByFromWarehouse($query, $warehouseId)
    {
        return $query->where('from_warehouse_id', $warehouseId);
    }

    public function scopeByToWarehouse($query, $warehouseId)
    {
        return $query->where('to_warehouse_id', $warehouseId);
    }

    public function scopeByFromEntitas($query, $entitasId)
    {
        return $query->where('from_entitas_id', $entitasId);
    }

    public function scopeByToEntitas($query, $entitasId)
    {
        return $query->where('to_entitas_id', $entitasId);
    }





    public function requestedBy()
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function shippedBy()
    {
        return $this->belongsTo(User::class, 'shipped_by');
    }

    public function receivedBy()
    {
        return $this->belongsTo(User::class, 'received_by');
    }



    public function scopePending($query)
    {
        return $query->whereIn('status', ['Draft', 'Submitted']);
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'Approved');
    }

    public function scopeInTransit($query)
    {
        return $query->where('status', 'In_Transit');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'Completed');
    }

    // Helper methods
    public function getTotalItemsAttribute()
    {
        return $this->stockTransferItems()->count();
    }

    public function getTotalQuantityAttribute()
    {
        return $this->stockTransferItems()->sum('quantity_requested');
    }

    public function getTotalShippedQuantityAttribute()
    {
        return $this->stockTransferItems()->sum('quantity_shipped');
    }

    public function getTotalReceivedQuantityAttribute()
    {
        return $this->stockTransferItems()->sum('quantity_received');
    }

    public function getStatusColorAttribute()
    {
        return match ($this->status) {
            'Draft' => 'gray',
            'Submitted' => 'warning',
            'Approved' => 'success',
            'In_Transit' => 'info',
            'Completed' => 'success',
            'Cancelled' => 'danger',
            default => 'gray'
        };
    }

    public function getStatusLabelAttribute()
    {
        return match ($this->status) {
            'Draft' => 'Draft',
            'Submitted' => 'Menunggu Persetujuan',
            'Approved' => 'Disetujui',
            'In_Transit' => 'Dalam Perjalanan',
            'Completed' => 'Selesai',
            'Cancelled' => 'Dibatalkan',
            default => $this->status
        };
    }

    public function getTransferRouteAttribute()
    {
        return $this->fromEntitas->nama . ' → ' . $this->toEntitas->nama;
    }

    public function isEditable()
    {
        return in_array($this->status, ['Draft']);
    }

    public function canBeSubmitted()
    {
        return $this->status === 'Draft' && $this->stockTransferItems()->count() > 0;
    }

    public function canBeApproved()
    {
        return $this->status === 'Submitted';
    }

    public function canBeShipped()
    {
        return $this->status === 'Approved';
    }

    public function canBeReceived()
    {
        return $this->status === 'In_Transit';
    }

    public function canBeCancelled()
    {
        return in_array($this->status, ['Draft', 'Submitted', 'Approved']);
    }

    public function submit()
    {
        if (!$this->canBeSubmitted()) {
            throw new \Exception('Transfer cannot be submitted');
        }

        $this->status = 'Submitted';
        $this->save();
    }

    public function approve()
    {
        if (!$this->canBeApproved()) {
            throw new \Exception('Transfer cannot be approved');
        }

        $this->status = 'Approved';
        $this->approved_by = auth()->id();
        $this->approved_at = Carbon::now();
        $this->save();
    }

    public function ship()
    {
        if (!$this->canBeShipped()) {
            throw new \Exception('Transfer cannot be shipped');
        }

        // Check stock availability
        foreach ($this->stockTransferItems as $item) {
            $stock = InventoryStock::where('product_id', $item->product_id)
                ->where('warehouse_id', $this->from_warehouse_id)
                ->where('entitas_id', $this->from_entitas_id)
                ->first();

            if (!$stock || $stock->quantity < $item->quantity_requested) {
                throw new \Exception("Insufficient stock for product: {$item->product->nama}");
            }
        }

        $this->status = 'In_Transit';
        $this->shipped_by = auth()->id();
        $this->shipped_at = Carbon::now();
        $this->save();

        // Reduce stock from source
        $this->reduceSourceStock();
    }

    public function receive()
    {
        if (!$this->canBeReceived()) {
            throw new \Exception('Transfer cannot be received');
        }

        $this->status = 'Completed';
        $this->received_by = auth()->id();
        $this->received_at = Carbon::now();
        $this->save();

        // Add stock to destination
        $this->addDestinationStock();

        // Create journal entry
        $this->createJournalEntry();
    }

    protected function reduceSourceStock()
    {
        foreach ($this->stockTransferItems as $item) {
            $stock = InventoryStock::where('product_id', $item->product_id)
                ->where('warehouse_id', $this->from_warehouse_id)
                ->where('entitas_id', $this->from_entitas_id)
                ->first();

            if ($stock) {
                $stock->quantity -= $item->quantity_shipped;
                $stock->updateTotalValue();

                // Create stock movement
                StockMovement::create([
                    'movement_number' => $this->transfer_number . '-OUT',
                    'movement_date' => $this->shipped_at,
                    'movement_type' => 'Transfer_Out',
                    'product_id' => $item->product_id,
                    'warehouse_id' => $this->from_warehouse_id,
                    'entitas_id' => $this->from_entitas_id,
                    'quantity' => -$item->quantity_shipped,
                    'unit_cost' => $stock->average_cost,
                    'total_value' => - ($item->quantity_shipped * $stock->average_cost),
                    'reference_type' => 'StockTransfer',
                    'reference_id' => $this->id,
                    'reference_number' => $this->transfer_number,
                    'created_by' => auth()->id(),
                ]);
            }
        }
    }

    protected function addDestinationStock()
    {
        foreach ($this->stockTransferItems as $item) {
            $stock = InventoryStock::where('product_id', $item->product_id)
                ->where('warehouse_id', $this->to_warehouse_id)
                ->where('entitas_id', $this->to_entitas_id)
                ->first();

            $transferCost = $item->unit_cost ?? $item->product->average_cost ?? 0;

            if ($stock) {
                // Update existing stock with weighted average
                $oldValue = $stock->quantity * $stock->average_cost;
                $newValue = $item->quantity_received * $transferCost;
                $totalQuantity = $stock->quantity + $item->quantity_received;

                if ($totalQuantity > 0) {
                    $stock->average_cost = ($oldValue + $newValue) / $totalQuantity;
                }

                $stock->quantity += $item->quantity_received;
                $stock->updateTotalValue();
            } else {
                // Create new stock
                InventoryStock::create([
                    'product_id' => $item->product_id,
                    'warehouse_id' => $this->to_warehouse_id,
                    'entitas_id' => $this->to_entitas_id,
                    'quantity' => $item->quantity_received,
                    'average_cost' => $transferCost,
                    'total_value' => $item->quantity_received * $transferCost,
                    'minimum_stock' => 0,
                    'maximum_stock' => 0,
                    'last_updated' => Carbon::now(),
                ]);
            }

            // Create stock movement
            StockMovement::create([
                'movement_number' => $this->transfer_number . '-IN',
                'movement_date' => $this->received_at,
                'movement_type' => 'Transfer_In',
                'product_id' => $item->product_id,
                'warehouse_id' => $this->to_warehouse_id,
                'entitas_id' => $this->to_entitas_id,
                'quantity' => $item->quantity_received,
                'unit_cost' => $transferCost,
                'total_value' => $item->quantity_received * $transferCost,
                'reference_type' => 'StockTransfer',
                'reference_id' => $this->id,
                'reference_number' => $this->transfer_number,
                'created_by' => auth()->id(),
            ]);
        }
    }

    protected function createJournalEntry()
    {
        // This will be implemented when we create the PostingRuleEngine
        // Logic: Inter-branch inventory transfer
        // Will be handled by PostingRuleEngine based on source_type = 'StockTransfer'
    }

    public function getFormattedTransferDateAttribute()
    {
        return $this->transfer_date ? $this->transfer_date->format('d/m/Y') : null;
    }

    // Auto-generate transfer number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transfer) {
            if (empty($transfer->transfer_number)) {
                $transfer->transfer_number = static::generateTransferNumber();
            }
        });
    }

    public static function generateTransferNumber()
    {
        $prefix = 'ST';
        $date = Carbon::now()->format('Ymd');
        $lastTransfer = static::whereDate('created_at', Carbon::today())
            ->where('transfer_number', 'like', $prefix . $date . '%')
            ->orderBy('transfer_number', 'desc')
            ->first();

        if ($lastTransfer) {
            $lastNumber = intval(substr($lastTransfer->transfer_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . $date . $newNumber;
    }
}
