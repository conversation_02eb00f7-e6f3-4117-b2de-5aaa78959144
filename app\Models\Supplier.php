<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Supplier extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'supplier';

    protected $fillable = [
        'nama',
        'nama_perusahaan',
        'email',
        'nomor_handphone',
        'npwp',
        'alamat',
        'info_lainnya',
        'akun_bank',
        'nama_bank',
        'kantor_cabang_bank',
        'nomor_rekening',
        'pemegang_akun_bank',
        'id_akun_hutang',
        'syarat_pembayaran_utama',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    // Relationships
    public function akunHutang()
    {
        return $this->belongsTo(Akun::class, 'id_akun_hutang');
    }

    public function purchaseOrders()
    {
        return $this->hasMany(PurchaseOrder::class);
    }

    public function purchaseInvoices()
    {
        return $this->hasMany(PurchaseInvoice::class);
    }

    public function purchasePayments()
    {
        return $this->hasMany(PurchasePayment::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Helper methods
    public function getTotalPurchaseOrdersAttribute()
    {
        return $this->purchaseOrders()->count();
    }

    public function getTotalPurchaseAmountAttribute()
    {
        return $this->purchaseOrders()->sum('total_amount');
    }

    public function getOutstandingAmountAttribute()
    {
        return $this->purchaseInvoices()->sum('outstanding_amount');
    }
}
