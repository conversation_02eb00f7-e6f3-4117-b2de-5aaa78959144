<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use App\Traits\SafeDateOperations;

class Tactic extends Model
{
    use HasFactory, SoftDeletes, LogsActivity, SafeDateOperations;

    protected $fillable = [
        'objective_id',
        'nama_tactic',
        'deskripsi',
        'jenis_tactic',
        'priority',
        'status',
        'pemilik',
        'tanggal_mulai',
        'tanggal_target',
        'estimasi_effort',
        'skor_dampak',
        'progress_percentage',
        'sumber_daya',
        'kriteria_keberhasilan',
        'dependensi',
        'created_by',
    ];

    protected $casts = [
        'progress_percentage' => 'integer',
        'tanggal_mulai' => 'date',
        'tanggal_target' => 'date',
        'estimasi_effort' => 'integer',
        'skor_dampak' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    // Relationships
    public function objective(): BelongsTo
    {
        return $this->belongsTo(Objective::class);
    }

    public function pemilik(): BelongsTo
    {
        return $this->belongsTo(User::class, 'pemilik');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Note: Tactics are strategies, not directly connected to tasks
    // Tasks are connected to Key Results instead

    public function documents(): MorphMany
    {
        return $this->morphMany(OkrDocument::class, 'documentable');
    }

    // Scopes
    public function scopeByObjective($query, $objectiveId)
    {
        return $query->where('objective_id', $objectiveId);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
            ->whereNotIn('status', ['completed']);
    }

    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['high', 'critical']);
    }

    // Tactics are strategies, no longer connected to tasks
    // Tasks are connected to Objectives instead

    // Accessors & Mutators
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'planned' => 'Direncanakan',
            'in_progress' => 'Sedang Berjalan',
            'completed' => 'Selesai',
            'blocked' => 'Terblokir',
            default => 'Unknown',
        };
    }

    public function getPriorityLabelAttribute(): string
    {
        return match ($this->priority) {
            'low' => 'Rendah',
            'medium' => 'Sedang',
            'high' => 'Tinggi',
            'critical' => 'Kritis',
            default => 'Unknown',
        };
    }

    public function getPriorityColorAttribute(): string
    {
        return match ($this->priority) {
            'critical' => 'danger',
            'high' => 'warning',
            'medium' => 'info',
            'low' => 'success',
            default => 'secondary',
        };
    }

    public function getProgressColorAttribute(): string
    {
        return match (true) {
            $this->progress_percentage >= 100 => 'success',
            $this->progress_percentage >= 80 => 'info',
            $this->progress_percentage >= 60 => 'warning',
            $this->progress_percentage >= 40 => 'primary',
            default => 'danger',
        };
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date &&
            $this->due_date->isPast() &&
            $this->status !== 'completed';
    }

    public function getDaysRemainingAttribute(): ?int
    {
        if (!$this->due_date || $this->status === 'completed') {
            return null;
        }

        return now()->diffInDays($this->due_date, false);
    }

    public function getHoursVarianceAttribute(): int
    {
        if (!$this->estimated_hours) {
            return 0;
        }

        return $this->actual_hours - $this->estimated_hours;
    }

    // Methods
    // Tactics progress is managed manually, not calculated from tasks

    public function updateStatus(): void
    {
        $newStatus = match (true) {
            $this->progress_percentage >= 100 => 'completed',
            $this->progress_percentage > 0 => 'in_progress',
            default => 'planned',
        };

        if ($this->status !== $newStatus && $this->status !== 'blocked') {
            $this->update(['status' => $newStatus]);
        }
    }

    // Tactics are strategies, not connected to tasks
    // Task management is handled at Objective level

    public function addHours(int $hours): void
    {
        $this->update(['actual_hours' => $this->actual_hours + $hours]);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'nama_tactic',
                'status',
                'progress_percentage',
                'priority',
                'assigned_to',
                'due_date'
            ])
            ->logOnlyDirty();
    }
}
