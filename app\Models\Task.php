<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use App\Traits\SafeDateOperations;

class Task extends Model
{
    use HasFactory, SoftDeletes, LogsActivity, SafeDateOperations;

    protected $fillable = [
        'project_id',
        'name',
        'description',
        'assigned_to',
        'start_date',
        'due_date',
        'status',
        'created_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'due_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function timesheets(): HasMany
    {
        return $this->hasMany(Timesheet::class);
    }



    public function comments(): HasMany
    {
        return $this->hasMany(TaskComment::class)->whereNull('parent_id')->orderBy('created_at', 'desc');
    }

    public function mentions(): HasMany
    {
        return $this->hasMany(TeamMention::class, 'mentionable_id')
            ->where('mentionable_type', self::class);
    }

    public function objectives(): BelongsToMany
    {
        return $this->belongsToMany(Objective::class, 'objective_tasks')
            ->withPivot('contribution_percentage')
            ->withTimestamps();
    }

    // Calculate total hours logged for this task
    public function getTotalHoursAttribute(): float
    {
        return $this->timesheets()->sum('hours');
    }

    // Performance-optimized query scopes
    public function scopeWithOptimizedRelations($query)
    {
        return $query->with([
            'project:id,name,status',
            'assignedUser:id,name,email',
            'creator:id,name'
        ]);
    }

    public function scopeForDashboard($query, $userId)
    {
        return $query->select([
            'id',
            'name',
            'status',
            'due_date',
            'assigned_to',
            'project_id',
            'created_at',
            'updated_at'
        ])
            ->where('assigned_to', $userId)
            ->with(['project:id,name,status'])
            ->orderBy('due_date', 'asc');
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
            ->whereNotIn('status', ['completed']);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeForProject($query, $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    public function scopeWithCounts($query)
    {
        return $query->withCount(['comments', 'timesheets'])
            ->withSum('timesheets', 'hours');
    }

    public function scopeRecentlyUpdated($query, $days = 7)
    {
        return $query->where('updated_at', '>=', now()->subDays($days));
    }



    // Get progress percentage based on status
    public function getProgressPercentageAttribute(): int
    {
        return match ($this->status) {
            'todo' => 0,
            'in_progress' => 50,
            'completed' => 100,
            default => 0,
        };
    }

    // Check if task is overdue (safe null check)
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date && $this->due_date->isPast() && $this->status !== 'completed';
    }

    // Get safe due date string
    public function getDueDateStringAttribute(): string
    {
        return $this->due_date ? $this->due_date->format('M d, Y') : 'No due date';
    }

    public function extractMentions(): array
    {
        preg_match_all('/@([A-Z][a-zA-Z0-9]*(?:\s+[A-Z][a-zA-Z0-9]*)*)(?=\s+[a-z]|\s*$|\s*[^\w]|$)/', $this->description ?? '', $matches);
        return array_map('trim', $matches[1] ?? []);
    }

    public function processMentions(): void
    {
        $mentionedUsernames = $this->extractMentions();
        $mentionedUsers = User::whereIn('name', $mentionedUsernames)->pluck('id')->toArray();

        if (!empty($mentionedUsers)) {
            TeamMention::createFromTask($this, $mentionedUsers, $this->creator ?? auth()->user());
        }
    }

    public function getFormattedDescriptionAttribute(): string
    {
        $description = $this->description ?? '';

        // Replace @mentions with clickable links
        $description = preg_replace_callback('/@([A-Z][a-zA-Z0-9]*(?:\s+[A-Z][a-zA-Z0-9]*)*)(?=\s+[a-z]|\s*$|\s*[^\w]|$)/', function ($matches) {
            $username = trim($matches[1]);
            $user = User::where('name', $username)->first();

            if ($user) {
                return "<span class='mention mention-task' data-user-id='{$user->id}' title='{$user->name} ({$user->email})'>@{$username}</span>";
            }

            return $matches[0];
        }, $description);

        // Convert line breaks to HTML
        $description = nl2br($description);

        return $description;
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'assigned_to', 'due_date', 'status'])
            ->logOnlyDirty();
    }
}
