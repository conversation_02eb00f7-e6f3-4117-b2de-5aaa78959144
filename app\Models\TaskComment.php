<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class TaskComment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'task_id',
        'user_id',
        'parent_id',
        'comment',
        'attachments',
    ];

    protected $casts = [
        'attachments' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $appends = ['formatted_comment'];

    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    public function replies(): Has<PERSON>any
    {
        return $this->hasMany(self::class, 'parent_id')->orderBy('created_at', 'asc');
    }

    public function reactions(): HasMany
    {
        return $this->hasMany(CommentReaction::class, 'comment_id');
    }

    // Get all replies recursively
    public function allReplies(): HasMany
    {
        return $this->replies()->with('allReplies');
    }

    // Check if this is a reply to another comment
    public function isReply(): bool
    {
        return !is_null($this->parent_id);
    }

    // Get the thread root comment
    public function getRootComment(): self
    {
        if ($this->isReply()) {
            return $this->parent->getRootComment();
        }
        return $this;
    }

    // Get formatted comment with processed mentions
    public function getFormattedCommentAttribute(): string
    {
        if (!$this->comment) return '';

        $formattedComment = $this->comment;

        // Convert mentions to styled spans
        $formattedComment = preg_replace(
            '/@(\w+)/',
            '<span class="mention bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm font-medium">@$1</span>',
            $formattedComment
        );

        // Convert line breaks to <br> tags
        $formattedComment = nl2br($formattedComment);

        return $formattedComment;
    }

    // Process mentions in comment content
    public function processMentions(): void
    {
        if (!$this->comment) return;

        preg_match_all('/@(\w+)/', $this->comment, $matches);
        $usernames = $matches[1];
        $mentionedUsers = [];

        foreach ($usernames as $username) {
            $user = User::where('name', 'like', "%{$username}%")->first();
            if ($user) {
                $mentionedUsers[] = $user->id;

                TeamMention::create([
                    'mentionable_type' => self::class,
                    'mentionable_id' => $this->id,
                    'mentioned_user_id' => $user->id,
                    'mentioned_by_user_id' => auth()->id() ?? 1,
                    'context' => 'task_comment',
                ]);
            }
        }

        // Note: mentioned_users is not in the database schema anymore
        // Mentions are tracked in team_mentions table
    }

    // boot saving
    protected static function booted()
    {
        static::created(function ($comment) {
            // This event fires only when a new comment is created.
            $userName = auth()->user()?->name ?? 'System';
            $userId = auth()->id() ?? $comment->user_id; // Use comment user_id as fallback

            // Only create activity if ProjectActivity model exists and user exists
            if (class_exists('\App\Models\ProjectActivity') && $userId) {
                \App\Models\ProjectActivity::create([
                    'project_id' => $comment->task->project_id,
                    'user_id' => $userId,
                    'activity_type' => 'comment_created', // More accurate activity type
                    'subject_type' => get_class($comment),
                    'subject_id' => $comment->id, // Now $comment->id is available
                    'description' => $userName . " added a comment to task: " . $comment->task->name,
                    'properties' => [],
                ]);
            }
        });
    }
}
