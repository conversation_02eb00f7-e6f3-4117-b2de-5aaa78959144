<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TaxBracket extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'tax_brackets';

    protected $fillable = [
        'bracket_name',
        'min_income',
        'max_income',
        'tax_rate',
        'cumulative_tax',
        'is_active',
        'effective_date',
        'end_date',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'effective_date', 'end_date'];

    protected $casts = [
        'min_income' => 'decimal:2',
        'max_income' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'cumulative_tax' => 'decimal:2',
        'is_active' => 'boolean',
        'effective_date' => 'date',
        'end_date' => 'date',
    ];

    // Relationships
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeEffectiveOn($query, $date)
    {
        return $query->where('effective_date', '<=', $date)
                    ->where(function ($q) use ($date) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', $date);
                    });
    }

    public function scopeOrderedByIncome($query)
    {
        return $query->orderBy('min_income');
    }

    // Helper methods
    public function getFormattedMinIncomeAttribute()
    {
        return 'Rp ' . number_format($this->min_income, 0, ',', '.');
    }

    public function getFormattedMaxIncomeAttribute()
    {
        return $this->max_income ? 'Rp ' . number_format($this->max_income, 0, ',', '.') : 'Unlimited';
    }

    public function getFormattedTaxRateAttribute()
    {
        return $this->tax_rate . '%';
    }

    public function getFormattedCumulativeTaxAttribute()
    {
        return 'Rp ' . number_format($this->cumulative_tax, 0, ',', '.');
    }

    public function getIncomeRangeAttribute()
    {
        $min = $this->formatted_min_income;
        $max = $this->max_income ? $this->formatted_max_income : 'Above';
        
        return $min . ' - ' . $max;
    }

    public function isApplicableFor($income)
    {
        if ($income < $this->min_income) return false;
        if ($this->max_income && $income > $this->max_income) return false;
        
        return true;
    }

    public function calculateTaxForBracket($income)
    {
        if (!$this->isApplicableFor($income)) return 0;
        
        $taxableIncome = min($income, $this->max_income ?: $income) - $this->min_income;
        return ($taxableIncome * $this->tax_rate) / 100;
    }

    // Static methods for tax calculation
    public static function calculateIncomeTax($annualIncome, $date = null)
    {
        $date = $date ?: now();
        
        $brackets = static::active()
                         ->effectiveOn($date)
                         ->orderedByIncome()
                         ->get();
        
        $totalTax = 0;
        
        foreach ($brackets as $bracket) {
            if ($annualIncome <= $bracket->min_income) break;
            
            $taxableIncome = min($annualIncome, $bracket->max_income ?: $annualIncome) - $bracket->min_income;
            $bracketTax = ($taxableIncome * $bracket->tax_rate) / 100;
            $totalTax += $bracketTax;
            
            if ($bracket->max_income && $annualIncome <= $bracket->max_income) break;
        }
        
        return $totalTax;
    }

    public static function getActiveBrackets($date = null)
    {
        $date = $date ?: now();
        
        return static::active()
                    ->effectiveOn($date)
                    ->orderedByIncome()
                    ->get();
    }

    public static function getCurrentTaxBrackets()
    {
        return static::getActiveBrackets();
    }

    // Default Indonesian tax brackets (2024)
    public static function getDefaultBrackets()
    {
        return [
            [
                'bracket_name' => 'Tarif 1',
                'min_income' => 0,
                'max_income' => 60000000,
                'tax_rate' => 5,
                'cumulative_tax' => 0,
            ],
            [
                'bracket_name' => 'Tarif 2',
                'min_income' => 60000000,
                'max_income' => *********,
                'tax_rate' => 15,
                'cumulative_tax' => 3000000, // 5% of 60M
            ],
            [
                'bracket_name' => 'Tarif 3',
                'min_income' => *********,
                'max_income' => *********,
                'tax_rate' => 25,
                'cumulative_tax' => 31500000, // 3M + 15% of 190M
            ],
            [
                'bracket_name' => 'Tarif 4',
                'min_income' => *********,
                'max_income' => *********0,
                'tax_rate' => 30,
                'cumulative_tax' => 94000000, // 31.5M + 25% of 250M
            ],
            [
                'bracket_name' => 'Tarif 5',
                'min_income' => *********0,
                'max_income' => null,
                'tax_rate' => 35,
                'cumulative_tax' => 1444000000, // 94M + 30% of 4.5B
            ],
        ];
    }
}
