<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class TeamMention extends Model
{
    use HasFactory;

    protected $fillable = [
        'mentionable_type',
        'mentionable_id',
        'mentioned_user_id',
        'mentioned_by_user_id',
        'context',
        'is_read',
        'read_at',
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'read_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function mentionable(): MorphTo
    {
        return $this->morphTo();
    }

    public function mentionedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'mentioned_user_id');
    }

    public function mentionedByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'mentioned_by_user_id');
    }

    // Mark mention as read
    public function markAsRead(): void
    {
        $this->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
    }

    // Scope for unread mentions
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    // Scope for mentions of a specific user
    public function scopeForUser($query, $userId)
    {
        return $query->where('mentioned_user_id', $userId);
    }

    // Scope for mentions in a specific context
    public function scopeInContext($query, $context)
    {
        return $query->where('context', $context);
    }

    // Static method to create mentions from a task
    public static function createFromTask($task, $mentionedUserIds, $mentionedByUser)
    {
        foreach ($mentionedUserIds as $userId) {
            static::create([
                'mentionable_type' => get_class($task),
                'mentionable_id' => $task->id,
                'mentioned_user_id' => $userId,
                'mentioned_by_user_id' => $mentionedByUser->id,
                'context' => 'task_description',
            ]);
        }
    }
}
