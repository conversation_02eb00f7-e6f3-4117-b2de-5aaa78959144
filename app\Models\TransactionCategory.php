<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransactionCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'type',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Relationships
    public function subcategories()
    {
        return $this->hasMany(TransactionSubcategory::class, 'category_id');
    }

    public function transactions()
    {
        return $this->hasMany(DailyTransaction::class, 'expense_category', 'code');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Accessors
    public function getTypeDisplayAttribute()
    {
        return match ($this->type) {
            'revenue' => 'Pendapatan',
            'expense' => 'Pengeluaran',
            default => $this->type,
        };
    }
}
