<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Village extends Model
{
    use HasFactory;

    protected $fillable = [
        'province_id',
        'city_id',
        'district_id',
        'code',
        'name',
        'slug',
        'type',
        'postal_code',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Boot method untuk auto-generate slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($village) {
            if (empty($village->slug)) {
                $village->slug = Str::slug($village->name);
            }
        });

        static::updating(function ($village) {
            if ($village->isDirty('name') && empty($village->slug)) {
                $village->slug = Str::slug($village->name);
            }
        });
    }

    /**
     * Relasi ke Province
     */
    public function province()
    {
        return $this->belongsTo(Province::class);
    }

    /**
     * Relasi ke City
     */
    public function city()
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Relasi ke District
     */
    public function district()
    {
        return $this->belongsTo(District::class);
    }

    /**
     * Relasi ke Customer
     */
    public function customers()
    {
        return $this->hasMany(Customer::class);
    }

    /**
     * Scope untuk village aktif
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope untuk filter berdasarkan provinsi
     */
    public function scopeByProvince($query, $provinceId)
    {
        return $query->where('province_id', $provinceId);
    }

    /**
     * Scope untuk filter berdasarkan city
     */
    public function scopeByCity($query, $cityId)
    {
        return $query->where('city_id', $cityId);
    }

    /**
     * Scope untuk filter berdasarkan district
     */
    public function scopeByDistrict($query, $districtId)
    {
        return $query->where('district_id', $districtId);
    }

    /**
     * Scope untuk filter berdasarkan tipe
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get formatted name dengan kode dan tipe
     */
    public function getFormattedNameAttribute(): string
    {
        $typeLabel = $this->type === 'kelurahan' ? 'Kel.' : 'Desa';
        return $this->code . ' - ' . $typeLabel . ' ' . $this->name;
    }

    /**
     * Get type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match ($this->type) {
            'kelurahan' => 'Kelurahan',
            'desa' => 'Desa',
            default => $this->type,
        };
    }

    /**
     * Get full name dengan district, city, dan provinsi
     */
    public function getFullNameAttribute(): string
    {
        return $this->formatted_name . ', ' . $this->district->name . ', ' . $this->city->formatted_name . ', ' . $this->province->name;
    }

    /**
     * Get full address dengan postal code
     */
    public function getFullAddressAttribute(): string
    {
        $address = $this->full_name;
        if ($this->postal_code) {
            $address .= ' ' . $this->postal_code;
        }
        return $address;
    }

    /**
     * Get route key name untuk URL
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
