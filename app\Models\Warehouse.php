<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Warehouse extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'warehouses';

    protected $fillable = [
        'name',
        'code',
        'address',
        'phone',
        'manager_name',
        'is_active',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Relationships
    public function inventoryStocks()
    {
        return $this->hasMany(InventoryStock::class);
    }

    public function salesTransactions()
    {
        return $this->hasMany(SalesTransaction::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Helper methods
    public function getDisplayNameAttribute()
    {
        return $this->code . ' - ' . $this->name;
    }

    public function getTotalProductsAttribute()
    {
        return $this->inventoryStocks()->count();
    }

    public function getTotalStockValueAttribute()
    {
        return $this->inventoryStocks()->sum('total_value');
    }
}
