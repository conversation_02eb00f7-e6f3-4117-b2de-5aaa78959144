<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\KeyResult;

class KeyResultAtRiskNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public KeyResult $keyResult
    ) {}

    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('⚠️ Key Result At Risk: ' . $this->keyResult->nama_key_result)
            ->greeting('Halo ' . $notifiable->name . ',')
            ->line('Key Result berikut ini berisiko tidak tercapai:')
            ->line('**' . $this->keyResult->nama_key_result . '**')
            ->line('Objective: ' . $this->keyResult->objective->nama_objective)
            ->line('Current Progress: ' . $this->keyResult->progress_percentage . '%')
            ->line('Target: ' . $this->keyResult->formatted_target_value)
            ->line('Current Value: ' . $this->keyResult->formatted_current_value)
            ->line('Due Date: ' . $this->keyResult->due_date?->format('d M Y'))
            ->line('Mohon segera lakukan tindakan untuk memastikan key result ini dapat tercapai.')
            ->action('Lihat Key Result', route('filament.admin.resources.objectives.edit', $this->keyResult->objective))
            ->line('Terima kasih atas perhatiannya.');
    }

    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'key_result_at_risk',
            'key_result_id' => $this->keyResult->id,
            'key_result_name' => $this->keyResult->nama_key_result,
            'objective_name' => $this->keyResult->objective->nama_objective,
            'progress_percentage' => $this->keyResult->progress_percentage,
            'due_date' => $this->keyResult->due_date?->format('Y-m-d'),
            'days_remaining' => $this->keyResult->days_remaining,
            'message' => 'Key Result "' . $this->keyResult->nama_key_result . '" berisiko tidak tercapai.',
        ];
    }
}
