<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Objective;

class ObjectiveMilestoneNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Objective $objective,
        public int $milestone
    ) {}

    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    public function toMail(object $notifiable): MailMessage
    {
        $emoji = match ($this->milestone) {
            25 => '🎯',
            50 => '🚀',
            75 => '⭐',
            100 => '🎉',
            default => '📈',
        };

        $message = match ($this->milestone) {
            25 => 'telah mencapai 25% progress!',
            50 => 'telah mencapai 50% progress!',
            75 => 'hampir selesai dengan 75% progress!',
            100 => 'telah selesai 100%! Selamat!',
            default => 'telah mencapai ' . $this->milestone . '% progress!',
        };

        return (new MailMessage)
            ->subject($emoji . ' Milestone Achieved: ' . $this->objective->nama_objective)
            ->greeting('Halo ' . $notifiable->name . ',')
            ->line('<PERSON><PERSON> baik! Objective berikut ini ' . $message)
            ->line('**' . $this->objective->nama_objective . '**')
            ->line('Progress: ' . $this->objective->progress_percentage . '%')
            ->line('Owner: ' . $this->objective->owner->name)
            ->line('Departemen: ' . ($this->objective->departemen->nama_departemen ?? 'Tidak ada'))
            ->when($this->milestone < 100, function ($mail) {
                return $mail->line('Terus pertahankan momentum untuk mencapai target 100%!');
            })
            ->when($this->milestone === 100, function ($mail) {
                return $mail->line('Terima kasih atas kerja keras dan dedikasi dalam mencapai objective ini!');
            })
            ->action('Lihat Objective', route('filament.admin.resources.objectives.edit', $this->objective))
            ->line('Selamat atas pencapaian ini!');
    }

    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'objective_milestone',
            'objective_id' => $this->objective->id,
            'objective_name' => $this->objective->nama_objective,
            'milestone' => $this->milestone,
            'progress_percentage' => $this->objective->progress_percentage,
            'owner_name' => $this->objective->owner->name,
            'message' => 'Objective "' . $this->objective->nama_objective . '" telah mencapai ' . $this->milestone . '% progress!',
        ];
    }
}
