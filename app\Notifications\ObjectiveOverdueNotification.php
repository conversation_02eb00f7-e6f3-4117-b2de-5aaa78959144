<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Objective;

class ObjectiveOverdueNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Objective $objective
    ) {}

    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('🚨 Objective Overdue: ' . $this->objective->nama_objective)
            ->greeting('Halo ' . $notifiable->name . ',')
            ->line('Objective berikut ini sudah melewati target completion date:')
            ->line('**' . $this->objective->nama_objective . '**')
            ->line('Target Completion: ' . $this->objective->target_completion?->format('d M Y'))
            ->line('Current Progress: ' . $this->objective->progress_percentage . '%')
            ->line('Status: ' . $this->objective->status_label)
            ->line('Mohon segera lakukan review dan update progress objective ini.')
            ->action('Lihat Objective', route('filament.admin.resources.objectives.edit', $this->objective))
            ->line('Terima kasih atas perhatiannya.');
    }

    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'objective_overdue',
            'objective_id' => $this->objective->id,
            'objective_name' => $this->objective->nama_objective,
            'target_completion' => $this->objective->target_completion?->format('Y-m-d'),
            'progress_percentage' => $this->objective->progress_percentage,
            'days_overdue' => abs($this->objective->days_remaining ?? 0),
            'message' => 'Objective "' . $this->objective->nama_objective . '" sudah melewati target completion date.',
        ];
    }
}
