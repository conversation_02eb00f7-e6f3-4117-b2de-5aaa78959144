<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Tactic;

class TacticBlockedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Tactic $tactic
    ) {}

    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('🚫 Tactic Blocked: ' . $this->tactic->nama_tactic)
            ->greeting('Halo ' . $notifiable->name . ',')
            ->line('Tactic berikut ini sedang dalam status blocked:')
            ->line('**' . $this->tactic->nama_tactic . '**')
            ->line('Objective: ' . $this->tactic->objective->nama_objective)
            ->line('Priority: ' . $this->tactic->priority_label)
            ->line('Assigned To: ' . ($this->tactic->assignedUser->name ?? 'Tidak ada'))
            ->line('Due Date: ' . $this->tactic->due_date?->format('d M Y'))
            ->line('Mohon segera lakukan review dan unblock tactic ini agar objective dapat berjalan sesuai rencana.')
            ->action('Lihat Tactic', route('filament.admin.resources.objectives.edit', $this->tactic->objective))
            ->line('Terima kasih atas perhatiannya.');
    }

    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'tactic_blocked',
            'tactic_id' => $this->tactic->id,
            'tactic_name' => $this->tactic->nama_tactic,
            'objective_name' => $this->tactic->objective->nama_objective,
            'priority' => $this->tactic->priority,
            'assigned_to' => $this->tactic->assignedUser->name ?? null,
            'due_date' => $this->tactic->due_date?->format('Y-m-d'),
            'message' => 'Tactic "' . $this->tactic->nama_tactic . '" sedang dalam status blocked.',
        ];
    }
}
