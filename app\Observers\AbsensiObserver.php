<?php

namespace App\Observers;

use App\Models\Absensi;
use App\Services\AttendanceService;
use Illuminate\Support\Facades\Log;

class AbsensiObserver
{
    /**
     * Handle the Absensi "creating" event.
     */
    public function creating(Absensi $absensi): void
    {
        // Set automatic status if waktu_masuk is provided and status is not already set to special values
        if ($absensi->waktu_masuk && 
            !in_array($absensi->status, ['cuti', 'izin', 'sakit']) &&
            $absensi->karyawan_id) {
            
            $automaticStatus = AttendanceService::determineAttendanceStatus(
                $absensi->karyawan_id,
                $absensi->waktu_masuk,
                $absensi->periode ?? 1
            );
            
            // Only set automatic status if current status is generic
            if (in_array($absensi->status, ['hadir', 'terlambat', null])) {
                $absensi->status = $automaticStatus;
            }
        }
    }

    /**
     * Handle the Absensi "created" event.
     */
    public function created(Absensi $absensi): void
    {
        // Log attendance creation for monitoring
        Log::info('Attendance record created', [
            'absensi_id' => $absensi->id,
            'karyawan_id' => $absensi->karyawan_id,
            'tanggal_absensi' => $absensi->tanggal_absensi,
            'status' => $absensi->status,
            'waktu_masuk' => $absensi->waktu_masuk,
            'is_automatic' => !$absensi->wasRecentlyCreated || $absensi->keterangan && str_contains($absensi->keterangan, 'Auto-generated')
        ]);
    }

    /**
     * Handle the Absensi "updating" event.
     */
    public function updating(Absensi $absensi): void
    {
        // If waktu_masuk is being set and status is not special, update status automatically
        if ($absensi->isDirty('waktu_masuk') && 
            $absensi->waktu_masuk &&
            !in_array($absensi->status, ['cuti', 'izin', 'sakit']) &&
            $absensi->karyawan_id) {
            
            $automaticStatus = AttendanceService::determineAttendanceStatus(
                $absensi->karyawan_id,
                $absensi->waktu_masuk,
                $absensi->periode ?? 1
            );
            
            // Update status if it's currently generic
            if (in_array($absensi->status, ['hadir', 'terlambat'])) {
                $absensi->status = $automaticStatus;
            }
        }
    }

    /**
     * Handle the Absensi "updated" event.
     */
    public function updated(Absensi $absensi): void
    {
        // Log status changes for monitoring
        if ($absensi->isDirty('status')) {
            Log::info('Attendance status updated', [
                'absensi_id' => $absensi->id,
                'karyawan_id' => $absensi->karyawan_id,
                'tanggal_absensi' => $absensi->tanggal_absensi,
                'old_status' => $absensi->getOriginal('status'),
                'new_status' => $absensi->status,
                'waktu_masuk' => $absensi->waktu_masuk
            ]);
        }
    }
}
