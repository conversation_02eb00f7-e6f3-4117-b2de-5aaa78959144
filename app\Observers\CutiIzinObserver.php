<?php

namespace App\Observers;

use App\Models\CutiIzin;
use App\Models\Absensi;
use App\Models\Schedule;
use App\Services\WhatsAppService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class CutiIzinObserver
{
    /**
     * Handle the CutiIzin "created" event.
     * Send WhatsApp notification when new leave request is submitted
     */
    public function created(CutiIzin $cutiIzin): void
    {
        $this->sendNewLeaveRequestNotification($cutiIzin);
    }

    /**
     * Handle the CutiIzin "updated" event.
     */
    public function updated(CutiIzin $cutiIzin): void
    {
        // Check if status changed to approved
        if ($cutiIzin->isDirty('status') && $cutiIzin->status === 'approved') {
            $this->createAttendanceRecords($cutiIzin);
        }

        // Send WhatsApp notification when status changes
        if ($cutiIzin->isDirty('status') && in_array($cutiIzin->status, ['approved', 'rejected'])) {
            $this->sendStatusUpdateNotification($cutiIzin);
        }
    }

    /**
     * Create attendance records for approved leave
     */
    private function createAttendanceRecords(CutiIzin $cutiIzin): void
    {
        try {
            $startDate = Carbon::parse($cutiIzin->tanggal_mulai);
            $endDate = Carbon::parse($cutiIzin->tanggal_selesai);
            $karyawanId = $cutiIzin->karyawan_id;

            // Determine status based on leave type
            $status = match ($cutiIzin->jenis_permohonan) {
                'cuti' => 'cuti',
                'izin' => 'izin',
                'sakit' => 'sakit',
                default => 'cuti'
            };

            $createdCount = 0;
            $skippedCount = 0;

            // Create attendance record for each day in the leave period
            $currentDate = $startDate->copy();
            while ($currentDate->lte($endDate)) {
                // Check if attendance record already exists for this date
                $existingAttendance = Absensi::where('karyawan_id', $karyawanId)
                    ->whereDate('tanggal_absensi', $currentDate->format('Y-m-d'))
                    ->first();

                if ($existingAttendance) {
                    // Update existing record if it's not already processed
                    if (in_array($existingAttendance->status, ['hadir', 'terlambat', 'alpha'])) {
                        $existingAttendance->update([
                            'status' => $status,
                            'keterangan' => "Auto-generated from approved {$cutiIzin->jenis_permohonan}: {$cutiIzin->alasan}",
                            'approved_by' => $cutiIzin->approved_by,
                            'approved_at' => $cutiIzin->approved_at,
                        ]);
                        $createdCount++;
                    } else {
                        $skippedCount++;
                    }
                } else {
                    // Find schedule for this date
                    $schedule = Schedule::with('entitas')
                        ->where('karyawan_id', $karyawanId)
                        ->whereDate('tanggal_jadwal', $currentDate->format('Y-m-d'))
                        ->first();

                    // Create new attendance record
                    Absensi::create([
                        'karyawan_id' => $karyawanId,
                        'jadwal_id' => $schedule?->id,
                        'tanggal_absensi' => $currentDate->format('Y-m-d'),
                        'status' => $status,
                        'keterangan' => "Auto-generated from approved {$cutiIzin->jenis_permohonan}: {$cutiIzin->alasan}",
                        'approved_by' => $cutiIzin->approved_by,
                        'approved_at' => $cutiIzin->approved_at,
                        'periode' => 1, // Default to period 1
                    ]);
                    $createdCount++;
                }

                $currentDate->addDay();
            }

            Log::info("Attendance records created for approved leave", [
                'cuti_izin_id' => $cutiIzin->id,
                'karyawan_id' => $karyawanId,
                'jenis_permohonan' => $cutiIzin->jenis_permohonan,
                'created_count' => $createdCount,
                'skipped_count' => $skippedCount,
                'date_range' => $startDate->format('Y-m-d') . ' to ' . $endDate->format('Y-m-d')
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to create attendance records for approved leave", [
                'cuti_izin_id' => $cutiIzin->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Send WhatsApp notification for new leave request
     */
    private function sendNewLeaveRequestNotification(CutiIzin $cutiIzin): void
    {
        try {
            $whatsAppService = app(WhatsAppService::class);

            if (!$whatsAppService->isEnabled()) {
                return;
            }

            // Nomor hardcode untuk notifikasi (format lokal Indonesia)
            $adminPhoneNumber = '085272726519';

            $karyawan = $cutiIzin->karyawan;
            $jenisPermohonan = $cutiIzin->jenis_permohonan_label;
            $tanggalMulai = $cutiIzin->tanggal_mulai->format('d/m/Y');
            $tanggalSelesai = $cutiIzin->tanggal_selesai->format('d/m/Y');

            $message = "🔔 *PENGAJUAN CUTI/IZIN BARU*\n\n";
            $message .= "Karyawan: *{$karyawan->nama_lengkap}*\n";
            $message .= "NIP: {$karyawan->nip}\n";
            $message .= "Jenis: *{$jenisPermohonan}*\n";
            $message .= "Tanggal: {$tanggalMulai}";

            if ($tanggalMulai !== $tanggalSelesai) {
                $message .= " s/d {$tanggalSelesai}";
            }

            $message .= "\nJumlah Hari: {$cutiIzin->jumlah_hari} hari\n";
            $message .= "Alasan: {$cutiIzin->alasan}\n\n";

            if ($cutiIzin->keterangan_tambahan) {
                $message .= "Keterangan: {$cutiIzin->keterangan_tambahan}\n\n";
            }

            $message .= "Status: *PENDING* (Menunggu Persetujuan)\n\n";
            $message .= "Silakan cek sistem untuk review dan approval.\n\n";
            $message .= "📅 Diajukan pada: " . $cutiIzin->created_at->format('d/m/Y H:i');

            $result = $whatsAppService->sendMessage($adminPhoneNumber, $message);

            if ($result['success']) {
                Log::info('WhatsApp notification sent for new leave request', [
                    'cuti_izin_id' => $cutiIzin->id,
                    'karyawan_id' => $cutiIzin->karyawan_id,
                    'admin_phone' => $adminPhoneNumber
                ]);
            } else {
                Log::warning('Failed to send WhatsApp notification for new leave request', [
                    'cuti_izin_id' => $cutiIzin->id,
                    'error' => $result['error'] ?? 'Unknown error'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('WhatsApp notification error for new leave request', [
                'cuti_izin_id' => $cutiIzin->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send WhatsApp notification for status update
     */
    private function sendStatusUpdateNotification(CutiIzin $cutiIzin): void
    {
        try {
            $whatsAppService = app(WhatsAppService::class);

            if (!$whatsAppService->isEnabled()) {
                return;
            }

            // Kirim ke nomor hardcode admin
            $adminPhoneNumber = '085272726519';

            // Kirim juga ke karyawan jika ada nomor HP
            $karyawan = $cutiIzin->karyawan;
            $karyawanPhone = $karyawan->no_hp ?? null;

            $status = $cutiIzin->status === 'approved' ? 'DISETUJUI ✅' : 'DITOLAK ❌';
            $jenisPermohonan = $cutiIzin->jenis_permohonan_label;
            $tanggalMulai = $cutiIzin->tanggal_mulai->format('d/m/Y');
            $tanggalSelesai = $cutiIzin->tanggal_selesai->format('d/m/Y');

            // Pesan untuk admin
            $adminMessage = "📋 *UPDATE STATUS CUTI/IZIN*\n\n";
            $adminMessage .= "Karyawan: *{$karyawan->nama_lengkap}*\n";
            $adminMessage .= "NIP: {$karyawan->nip}\n";
            $adminMessage .= "Jenis: *{$jenisPermohonan}*\n";
            $adminMessage .= "Tanggal: {$tanggalMulai}";

            if ($tanggalMulai !== $tanggalSelesai) {
                $adminMessage .= " s/d {$tanggalSelesai}";
            }

            $adminMessage .= "\nStatus: *{$status}*\n";

            if ($cutiIzin->status === 'rejected' && $cutiIzin->rejection_reason) {
                $adminMessage .= "Alasan Penolakan: {$cutiIzin->rejection_reason}\n";
            }

            $adminMessage .= "\n⏰ Diupdate pada: " . now()->format('d/m/Y H:i');

            // Kirim ke admin
            $whatsAppService->sendMessage($adminPhoneNumber, $adminMessage);

            // Pesan untuk karyawan (jika ada nomor HP)
            if (!empty($karyawanPhone)) {
                $karyawanMessage = "Halo {$karyawan->nama_lengkap},\n\n";
                $karyawanMessage .= "Permohonan {$jenisPermohonan} Anda untuk tanggal {$tanggalMulai}";

                if ($tanggalMulai !== $tanggalSelesai) {
                    $karyawanMessage .= " s/d {$tanggalSelesai}";
                }

                $karyawanMessage .= " telah *{$status}*.\n\n";

                if ($cutiIzin->status === 'rejected' && $cutiIzin->rejection_reason) {
                    $karyawanMessage .= "Alasan: {$cutiIzin->rejection_reason}\n\n";
                    $karyawanMessage .= "Silakan hubungi HRD untuk informasi lebih lanjut.\n\n";
                } else {
                    $karyawanMessage .= "Silakan koordinasi dengan atasan langsung Anda.\n\n";
                }

                $karyawanMessage .= "Terima kasih.\nTim HRD";

                $whatsAppService->sendMessage($karyawanPhone, $karyawanMessage);
            }

            Log::info('WhatsApp notification sent for status update', [
                'cuti_izin_id' => $cutiIzin->id,
                'status' => $cutiIzin->status,
                'admin_notified' => true,
                'karyawan_notified' => !empty($karyawanPhone)
            ]);
        } catch (\Exception $e) {
            Log::error('WhatsApp notification error for status update', [
                'cuti_izin_id' => $cutiIzin->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
