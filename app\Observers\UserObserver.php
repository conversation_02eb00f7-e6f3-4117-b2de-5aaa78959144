<?php

namespace App\Observers;

use App\Models\User;
use App\Models\Karyawan;

class UserObserver
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        $this->syncKaryawan($user);
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        $this->sync<PERSON><PERSON>awan($user);
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        // If user is deleted, remove the association with karyawan
        if ($user->karyawan) {
            $user->karyawan->update(['id_user' => null]);
        }
    }

    /**
     * Sync karyawan relationship with user
     */
    private function syncKaryawan(User $user): void
    {
        // Check if karyawan_id is set in the request
        $karyawanId = request('karyawan_id');
        
        if ($karyawanId) {
            // Find the karyawan
            $karyawan = Karyawan::find($karyawanId);
            
            if ($karyawan) {
                // Update the karyawan with the user_id
                $karyawan->update(['id_user' => $user->id]);
                
                // If the user's email is empty, use the karyawan's email
                if (empty($user->email) && !empty($karyawan->email)) {
                    $user->update(['email' => $karyawan->email]);
                }
                
                // If the user's name is empty, use the karyawan's name
                if (empty($user->name) && !empty($karyawan->nama_lengkap)) {
                    $user->update(['name' => $karyawan->nama_lengkap]);
                }
            }
        }
    }
}
