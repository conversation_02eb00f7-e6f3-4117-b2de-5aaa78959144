<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use RingleSoft\LaravelProcessApproval\Models\ProcessApprovalFlow;
use RingleSoft\LaravelProcessApproval\Models\ProcessApprovalFlowStep;

class ApprovalFixServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Override the models in the container
        $this->app->bind(
            'RingleSoft\LaravelProcessApproval\Models\ProcessApprovalFlow',
            'App\Models\ProcessApprovalFlow'
        );
        
        $this->app->bind(
            'RingleSoft\LaravelProcessApproval\Models\ProcessApprovalFlowStep', 
            'App\Models\ProcessApprovalFlowStep'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Force disable tenancy in config
        config(['process_approval.tenancy.enabled' => false]);
        
        // Remove global scopes from models after they're booted
        $this->removeGlobalScopes();
    }
    
    /**
     * Remove problematic global scopes
     */
    protected function removeGlobalScopes(): void
    {
        // Remove MultiTenantScope from ProcessApprovalFlow
        try {
            ProcessApprovalFlow::withoutGlobalScope('RingleSoft\LaravelProcessApproval\Scopes\MultiTenantScope');
        } catch (\Exception $e) {
            // Ignore if scope doesn't exist
        }
        
        // Remove MultiTenantScope from ProcessApprovalFlowStep
        try {
            ProcessApprovalFlowStep::withoutGlobalScope('RingleSoft\LaravelProcessApproval\Scopes\MultiTenantScope');
        } catch (\Exception $e) {
            // Ignore if scope doesn't exist
        }
    }
}
