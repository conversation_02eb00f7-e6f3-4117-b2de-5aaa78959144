<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class MarketingPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('marketing')
            ->path('marketing')
            ->login()
            ->colors([
                'primary' => Color::Blue,
            ])
            ->font('Poppins')
            ->viteTheme('resources/css/filament/admin/theme.css')
            ->discoverResources(in: app_path('Filament/Marketing/Resources'), for: 'App\\Filament\\Marketing\\Resources')
            ->discoverPages(in: app_path('Filament/Marketing/Pages'), for: 'App\\Filament\\Marketing\\Pages')
            ->discoverWidgets(in: app_path('Filament/Marketing/Widgets'), for: 'App\\Filament\\Marketing\\Widgets')
            ->pages([
                \App\Filament\Marketing\Pages\Dashboard::class,
            ])
            ->widgets([
                // Marketing specific widgets will be added here
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                \App\Http\Middleware\HandleSessionExpired::class,
                \App\Http\Middleware\RedirectBasedOnRole::class,
            ])
            ->authMiddleware([
                Authenticate::class,
                // Add marketing specific middleware if needed
            ])
            ->authGuard('web')
            ->brandName('PT. Viera Anugrah Pertama - Marketing')
            ->favicon(asset('images/viera-logo.png'))
            ->databaseNotifications()
            ->navigation(true)
            ->sidebarCollapsibleOnDesktop()
            ->maxContentWidth('full')
            ->defaultAvatarProvider(
                \Filament\AvatarProviders\UiAvatarsProvider::class
            )
            ->navigationGroups([
                'CRM' => 'Customer Relationship Management',
                'Sales' => 'Sales Management',
                'Products' => 'Product Management',
                'Reports' => 'Reports & Analytics',
                'Settings' => 'Settings',
            ]);
    }
}
