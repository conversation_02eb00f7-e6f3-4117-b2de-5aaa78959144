<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class PosPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('pos')
            ->path('pos')
            ->login()
            ->colors([
                'primary' => Color::Indigo,
                'danger' => Color::Rose,
                'gray' => Color::Gray,
                'info' => Color::Blue,
                'success' => Color::Emerald,
                'warning' => Color::Orange,
            ])
            ->font('Poppins')
            ->viteTheme('resources/css/filament/pos/theme.css')
            ->discoverResources(in: app_path('Filament/Pos/Resources'), for: 'App\\Filament\\Pos\\Resources')
            ->discoverPages(in: app_path('Filament/Pos/Pages'), for: 'App\\Filament\\Pos\\Pages')
            ->discoverWidgets(in: app_path('Filament/Pos/Widgets'), for: 'App\\Filament\\Pos\\Widgets')
            ->pages([
                \App\Filament\Pos\Pages\Dashboard::class,
            ])
            ->widgets([
                \App\Filament\Pos\Widgets\WelcomeWidget::class,
                \App\Filament\Pos\Widgets\SalesOverviewWidget::class,
                \App\Filament\Pos\Widgets\RevenueAnalyticsWidget::class,
                \App\Filament\Pos\Widgets\HourlySalesWidget::class,
                \App\Filament\Pos\Widgets\PaymentMethodsWidget::class,
                \App\Filament\Pos\Widgets\TopProductsWidget::class,
                \App\Filament\Pos\Widgets\LocationPerformanceWidget::class,
                \App\Filament\Pos\Widgets\RecentTransactionsWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                \App\Http\Middleware\HandleSessionExpired::class,
                \App\Http\Middleware\RedirectBasedOnRole::class,
            ])
            ->authMiddleware([
                Authenticate::class,
                // Add POS specific middleware if needed
            ])
            ->authGuard('web')
            ->brandName('PT. Viera Anugrah Pertama - POS Backoffice')
            ->favicon(asset('images/viera-logo.png'))
            ->databaseNotifications()
            ->navigation(true)
            ->sidebarCollapsibleOnDesktop()
            ->maxContentWidth('full')
            ->defaultAvatarProvider(
                \Filament\AvatarProviders\UiAvatarsProvider::class
            )
            ->navigationGroups([
                'POS Management',
                'Customer Management',
                'Product Management',
                'Location Management',
                'Business Management',
                'Reports & Analytics',
                'System Settings',
            ]);
    }
}
