<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class WarehousePanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('warehouse')
            ->path('warehouse')
            ->login()
            ->colors([
                'primary' => Color::Orange,
            ])
            ->font('Poppins')
            ->viteTheme('resources/css/filament/admin/theme.css')
            ->discoverResources(in: app_path('Filament/Warehouse/Resources'), for: 'App\\Filament\\Warehouse\\Resources')
            ->discoverPages(in: app_path('Filament/Warehouse/Pages'), for: 'App\\Filament\\Warehouse\\Pages')
            ->discoverWidgets(in: app_path('Filament/Warehouse/Widgets'), for: 'App\\Filament\\Warehouse\\Widgets')
            ->pages([
                \App\Filament\Warehouse\Pages\Dashboard::class,
                \App\Filament\Warehouse\Pages\StockOverview::class,
                \App\Filament\Warehouse\Pages\StockReport::class,
                \App\Filament\Warehouse\Pages\MovementHistoryReport::class,
            ])
            ->widgets([
                // Dashboard widgets will be added here
                \App\Filament\Warehouse\Widgets\TotalProductsWidget::class,
                \App\Filament\Warehouse\Widgets\ActiveWarehousesWidget::class,
                \App\Filament\Warehouse\Widgets\LowStockAlertsWidget::class,
                \App\Filament\Warehouse\Widgets\PendingOrdersWidget::class,
                \App\Filament\Warehouse\Widgets\StockMovementChart::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->navigationGroups([
                'Master Data',
                'Procurement',
                'Sales',
                'Inventory Management',
                'Reports',
            ]);
    }
}
