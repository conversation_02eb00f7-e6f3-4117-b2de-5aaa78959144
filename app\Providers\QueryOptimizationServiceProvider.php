<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class QueryOptimizationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Enable query logging in development
        if (app()->environment('local')) {
            $this->enableQueryLogging();
        }

        // Prevent lazy loading in production only
        if (app()->environment('production')) {
            Model::preventLazyLoading();
        }

        // In development, allow lazy loading for debugging
        // Model::preventLazyLoading(!app()->environment('local'));

        // Enable strict mode only in production for better performance
        if (app()->environment('production')) {
            Model::shouldBeStrict();
        }

        // Add global scopes for common queries
        $this->addGlobalScopes();
    }

    /**
     * Enable query logging for development
     */
    protected function enableQueryLogging(): void
    {
        DB::listen(function ($query) {
            // Log slow queries (> 1 second)
            if ($query->time > 1000) {
                Log::warning('Slow Query Detected', [
                    'sql' => $query->sql,
                    'bindings' => $query->bindings,
                    'time' => $query->time . 'ms'
                ]);
            }

            // Log N+1 queries
            if (str_contains($query->sql, 'select * from') && !str_contains($query->sql, 'limit')) {
                $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10);
                $isInLoop = false;

                foreach ($backtrace as $trace) {
                    if (isset($trace['function']) && in_array($trace['function'], ['each', 'map', 'filter', 'reduce'])) {
                        $isInLoop = true;
                        break;
                    }
                }

                if ($isInLoop) {
                    Log::warning('Potential N+1 Query Detected', [
                        'sql' => $query->sql,
                        'bindings' => $query->bindings,
                        'time' => $query->time . 'ms'
                    ]);
                }
            }
        });
    }

    /**
     * Add global scopes for common queries
     */
    protected function addGlobalScopes(): void
    {
        // Add global scope for active employees
        \App\Models\Karyawan::addGlobalScope('active_by_default', function ($builder) {
            // This can be overridden by calling withInactive() or withoutGlobalScope()
            // $builder->where('status_aktif', true);
        });

        // Add global scope for non-deleted records (if using soft deletes)
        // This is already handled by SoftDeletes trait, but we can add custom logic here
    }

    /**
     * Register query optimization macros
     */
    protected function registerQueryMacros(): void
    {
        // Add macro for efficient pagination
        \Illuminate\Database\Query\Builder::macro('efficientPaginate', function ($perPage = 15) {
            return $this->simplePaginate($perPage);
        });

        // Add macro for counting without loading all records
        \Illuminate\Database\Eloquent\Builder::macro('fastCount', function () {
            return $this->toBase()->getCountForPagination();
        });

        // Add macro for chunked processing
        \Illuminate\Database\Eloquent\Builder::macro('chunkById', function ($count, callable $callback, $column = null, $alias = null) {
            $column = $column ?? $this->getModel()->getKeyName();
            $alias = $alias ?? $column;

            $lastId = null;

            do {
                $clone = clone $this;

                if ($lastId !== null) {
                    $clone->where($column, '>', $lastId);
                }

                $results = $clone->orderBy($column)->limit($count)->get();

                $countResults = $results->count();

                if ($countResults == 0) {
                    break;
                }

                if ($callback($results) === false) {
                    return false;
                }

                $lastId = $results->last()->{$alias};
            } while ($countResults == $count);

            return true;
        });
    }
}
