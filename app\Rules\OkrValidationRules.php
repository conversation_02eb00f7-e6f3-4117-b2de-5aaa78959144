<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class ValidObjectiveDates implements Rule
{
    public function passes($attribute, $value)
    {
        $startDate = request()->input('start_date');
        $dueDate = request()->input('due_date');

        if (!$startDate || !$dueDate) {
            return true; // Skip if dates are not provided
        }

        return strtotime($dueDate) > strtotime($startDate);
    }

    public function message()
    {
        return 'Due date must be after start date.';
    }
}

class ValidProgressPercentage implements Rule
{
    public function passes($attribute, $value)
    {
        return $value >= 0 && $value <= 100;
    }

    public function message()
    {
        return 'Progress percentage must be between 0 and 100.';
    }
}

class ValidKeyResultTarget implements Rule
{
    public function passes($attribute, $value)
    {
        $metricType = request()->input('tipe_metrik');

        if ($metricType === 'boolean') {
            return in_array($value, [0, 1]);
        }

        if ($metricType === 'percentage') {
            return $value >= 0 && $value <= 100;
        }

        return $value > 0; // For numeric types, target should be positive
    }

    public function message()
    {
        $metricType = request()->input('tipe_metrik');

        return match ($metricType) {
            'boolean' => 'Boolean target must be 0 or 1.',
            'percentage' => 'Percentage target must be between 0 and 100.',
            default => 'Target value must be greater than 0.'
        };
    }
}
