<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\KeyResult;
use Closure;

class ValidKeyResultWeight implements ValidationRule
{
    private $objectiveId;

    public function __construct($objectiveId)
    {
        $this->objectiveId = $objectiveId;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->objectiveId) {
            return; // Skip validation if no objective
        }

        $totalWeight = KeyResult::where('objective_id', $this->objectiveId)
            ->where('id', '!=', request()->route('record'))
            ->sum('weight');

        if (($totalWeight + $value) > 100) {
            $fail('Total weight of all key results for this objective cannot exceed 100.');
        }
    }
}
