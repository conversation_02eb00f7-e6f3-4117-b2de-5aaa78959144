<?php

namespace App\Services;

use App\Models\Absensi;
use App\Models\Schedule;
use App\Models\AturanKeterlambatan;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class AttendanceService
{
    /**
     * Determine attendance status based on schedule and time
     */
    public static function determineAttendanceStatus($karyawanId, $datetime = null, $periode = 1): string
    {
        if (!$datetime) {
            $datetime = Carbon::now();
        }

        try {
            // Get schedule for the employee on this date
            $schedule = Schedule::with(['shift', 'karyawan', 'entitas'])
                ->where('karyawan_id', $karyawanId)
                ->whereDate('tanggal_jadwal', $datetime->toDateString())
                ->first();

            if (!$schedule || !$schedule->shift) {
                return 'hadir'; // Default status if no schedule
            }

            $shift = $schedule->shift;
            $actualTime = Carbon::parse($datetime);

            // Handle split shift
            if ($shift->isSplitShift()) {
                $periods = $shift->getWorkPeriods();

                foreach ($periods as $period) {
                    if ($period['periode'] == $periode) {
                        $shiftStart = Carbon::parse($period['waktu_mulai']);
                        $toleranceMinutes = $period['toleransi_keterlambatan'] ?? 0;

                        return $actualTime->greaterThan($shiftStart->addMinutes($toleranceMinutes))
                            ? 'terlambat'
                            : 'hadir';
                    }
                }
            } else {
                // Regular shift logic
                $shiftStart = Carbon::parse($shift->waktu_mulai);
                $toleranceMinutes = $shift->toleransi_keterlambatan ?? 0;

                return $actualTime->greaterThan($shiftStart->addMinutes($toleranceMinutes))
                    ? 'terlambat'
                    : 'hadir';
            }

            return 'hadir';
        } catch (\Exception $e) {
            Log::warning('Failed to determine attendance status: ' . $e->getMessage());
            return 'hadir';
        }
    }

    /**
     * Calculate lateness minutes for an attendance record
     */
    public static function calculateLatenessMinutes(Absensi $absensi): int
    {
        if (!$absensi->jadwal || !$absensi->jadwal->shift || !$absensi->waktu_masuk) {
            return 0;
        }

        $shift = $absensi->jadwal->shift;
        $actualEntry = Carbon::parse($absensi->waktu_masuk);

        // Handle split shift
        if ($shift->isSplitShift()) {
            $currentPeriod = $shift->getCurrentPeriod($actualEntry->format('H:i:s'));
            $periods = $shift->getWorkPeriods();

            foreach ($periods as $period) {
                if ($period['periode'] == $currentPeriod) {
                    $shiftStart = Carbon::parse($period['waktu_mulai']);
                    $toleranceMinutes = $period['toleransi_keterlambatan'] ?? 0;
                    $allowedEntry = $shiftStart->copy()->addMinutes($toleranceMinutes);

                    if ($actualEntry->greaterThan($allowedEntry)) {
                        return $actualEntry->diffInMinutes($allowedEntry);
                    }
                }
            }
        } else {
            // Regular shift
            $shiftStart = Carbon::parse($shift->waktu_mulai);
            $toleranceMinutes = $shift->toleransi_keterlambatan ?? 0;
            $allowedEntry = $shiftStart->copy()->addMinutes($toleranceMinutes);

            if ($actualEntry->greaterThan($allowedEntry)) {
                return $actualEntry->diffInMinutes($allowedEntry);
            }
        }

        return 0;
    }

    /**
     * Apply lateness rules and calculate deduction
     */
    public static function calculateLatenessDeduction(Absensi $absensi, $gajiPokok = 0): array
    {
        $latenessMinutes = self::calculateLatenessMinutes($absensi);

        if ($latenessMinutes <= 0) {
            return [
                'minutes_late' => 0,
                'deduction_amount' => 0,
                'applicable_rule' => null
            ];
        }

        // Find applicable lateness rule
        $aturan = AturanKeterlambatan::findApplicableRule($latenessMinutes);
        $deductionAmount = 0;

        if ($aturan) {
            $deductionAmount = $aturan->hitungDenda($latenessMinutes, $gajiPokok);
        }

        return [
            'minutes_late' => $latenessMinutes,
            'deduction_amount' => $deductionAmount,
            'applicable_rule' => $aturan
        ];
    }

    /**
     * Update attendance status based on timing
     */
    public static function updateAttendanceStatus(Absensi $absensi): void
    {
        if (!$absensi->waktu_masuk) {
            return;
        }

        $newStatus = self::determineAttendanceStatus(
            $absensi->karyawan_id,
            $absensi->waktu_masuk,
            $absensi->periode ?? 1
        );

        // Only update if status changed and it's not a special status (cuti, izin, sakit)
        if (
            $absensi->status !== $newStatus &&
            !in_array($absensi->status, ['cuti', 'izin', 'sakit'])
        ) {

            $absensi->update(['status' => $newStatus]);

            Log::info('Attendance status updated automatically', [
                'absensi_id' => $absensi->id,
                'karyawan_id' => $absensi->karyawan_id,
                'old_status' => $absensi->getOriginal('status'),
                'new_status' => $newStatus,
                'waktu_masuk' => $absensi->waktu_masuk
            ]);
        }
    }

    /**
     * Process attendance record for automatic status and deduction calculation
     */
    public static function processAttendanceRecord(Absensi $absensi): array
    {
        // Update status if needed
        self::updateAttendanceStatus($absensi);

        // Calculate lateness data
        $latenessData = self::calculateLatenessDeduction($absensi);

        return [
            'status_updated' => true,
            'lateness_data' => $latenessData
        ];
    }
}
