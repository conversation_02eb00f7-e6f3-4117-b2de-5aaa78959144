<?php

namespace App\Services;

use App\Models\Objective;
use App\Models\KeyResult;
use App\Models\Tactic;
use App\Models\User;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class OkrProgressTracker
{
    /**
     * Update progress for all objectives and their components
     */
    public function updateAllProgress(): void
    {
        $objectives = Objective::with(['keyResults', 'tactics'])->get();

        foreach ($objectives as $objective) {
            $this->updateObjectiveProgress($objective);
        }

        Log::info('OKR Progress updated for all objectives', ['count' => $objectives->count()]);
    }

    /**
     * Update progress for a specific objective
     */
    public function updateObjectiveProgress(Objective $objective): void
    {
        // Update key results progress
        foreach ($objective->keyResults as $keyResult) {
            $keyResult->updateProgress();
        }

        // Tactics progress is managed manually, not calculated from tasks
        // Update tactics status based on current progress
        foreach ($objective->tactics as $tactic) {
            $tactic->updateStatus();
        }

        // Update objective progress
        $objective->calculateProgress();
        $objective->updateStatus();

        Log::info('Progress updated for objective', ['objective_id' => $objective->id]);
    }

    /**
     * Check for objectives that need attention and send notifications
     */
    public function checkAndNotify(): void
    {
        $this->checkOverdueObjectives();
        $this->checkAtRiskKeyResults();
        $this->checkBlockedTactics();
        $this->checkMilestoneAchievements();
    }

    /**
     * Check for overdue objectives
     */
    private function checkOverdueObjectives(): void
    {
        $overdueObjectives = Objective::where('target_completion', '<', now())
            ->where('status', '!=', 'completed')
            ->with(['owner'])
            ->get();

        foreach ($overdueObjectives as $objective) {
            $this->sendOverdueNotification($objective);
        }
    }

    /**
     * Check for at-risk key results
     */
    private function checkAtRiskKeyResults(): void
    {
        $atRiskKeyResults = KeyResult::where('status', 'at_risk')
            ->orWhere(function ($query) {
                $query->where('due_date', '<=', now()->addDays(7))
                    ->where('progress_percentage', '<', 70)
                    ->whereNotIn('status', ['completed']);
            })
            ->with(['objective.owner'])
            ->get();

        foreach ($atRiskKeyResults as $keyResult) {
            $this->sendAtRiskNotification($keyResult);
        }
    }

    /**
     * Check for blocked tactics
     */
    private function checkBlockedTactics(): void
    {
        $blockedTactics = Tactic::where('status', 'blocked')
            ->with(['objective.owner', 'assignedUser'])
            ->get();

        foreach ($blockedTactics as $tactic) {
            $this->sendBlockedTacticNotification($tactic);
        }
    }

    /**
     * Check for milestone achievements
     */
    private function checkMilestoneAchievements(): void
    {
        // Check for objectives that just reached certain milestones
        $milestones = [25, 50, 75, 100];

        foreach ($milestones as $milestone) {
            $objectives = Objective::where('progress_percentage', '>=', $milestone)
                ->where('progress_percentage', '<', $milestone + 5) // Recently achieved
                ->where('updated_at', '>=', now()->subHours(24)) // Updated in last 24 hours
                ->with(['owner'])
                ->get();

            foreach ($objectives as $objective) {
                $this->sendMilestoneNotification($objective, $milestone);
            }
        }
    }

    /**
     * Send overdue notification
     */
    private function sendOverdueNotification(Objective $objective): void
    {
        $notification = new \App\Notifications\ObjectiveOverdueNotification($objective);

        // Notify objective owner
        if ($objective->owner) {
            $objective->owner->notify($notification);
        }

        // Notify supervisors/managers
        $supervisors = User::whereIn('role', ['super_admin', 'direktur', 'manager_hrd'])->get();
        Notification::send($supervisors, $notification);

        Log::info('Overdue notification sent', ['objective_id' => $objective->id]);
    }

    /**
     * Send at-risk notification
     */
    private function sendAtRiskNotification(KeyResult $keyResult): void
    {
        $notification = new \App\Notifications\KeyResultAtRiskNotification($keyResult);

        // Notify objective owner
        if ($keyResult->objective->owner) {
            $keyResult->objective->owner->notify($notification);
        }

        Log::info('At-risk notification sent', ['key_result_id' => $keyResult->id]);
    }

    /**
     * Send blocked tactic notification
     */
    private function sendBlockedTacticNotification(Tactic $tactic): void
    {
        $notification = new \App\Notifications\TacticBlockedNotification($tactic);

        // Notify assigned user
        if ($tactic->assignedUser) {
            $tactic->assignedUser->notify($notification);
        }

        // Notify objective owner
        if ($tactic->objective->owner) {
            $tactic->objective->owner->notify($notification);
        }

        Log::info('Blocked tactic notification sent', ['tactic_id' => $tactic->id]);
    }

    /**
     * Send milestone achievement notification
     */
    private function sendMilestoneNotification(Objective $objective, int $milestone): void
    {
        $notification = new \App\Notifications\ObjectiveMilestoneNotification($objective, $milestone);

        // Notify objective owner
        if ($objective->owner) {
            $objective->owner->notify($notification);
        }

        // Notify team members if it's a significant milestone
        if ($milestone >= 75) {
            $supervisors = User::whereIn('role', ['super_admin', 'direktur', 'manager_hrd'])->get();
            Notification::send($supervisors, $notification);
        }

        Log::info('Milestone notification sent', [
            'objective_id' => $objective->id,
            'milestone' => $milestone
        ]);
    }

    /**
     * Get progress summary for dashboard
     */
    public function getProgressSummary(): array
    {
        $objectives = Objective::with(['keyResults', 'tactics'])->get();

        return [
            'total_objectives' => $objectives->count(),
            'completed_objectives' => $objectives->where('status', 'completed')->count(),
            'overdue_objectives' => $objectives->filter(fn($obj) => $obj->is_overdue)->count(),
            'at_risk_key_results' => KeyResult::where('status', 'at_risk')->count(),
            'blocked_tactics' => Tactic::where('status', 'blocked')->count(),
            'overall_progress' => round($objectives->avg('progress_percentage'), 1),
        ];
    }

    /**
     * Get objectives that need immediate attention
     */
    public function getObjectivesNeedingAttention(): array
    {
        return [
            'overdue' => Objective::where('target_completion', '<', now())
                ->where('status', '!=', 'completed')
                ->with(['owner'])
                ->get(),
            'at_risk' => Objective::where('progress_percentage', '<', 50)
                ->where('target_completion', '<=', now()->addWeeks(2))
                ->where('status', '!=', 'completed')
                ->with(['owner'])
                ->get(),
            'stalled' => Objective::where('updated_at', '<', now()->subWeeks(2))
                ->where('status', 'active')
                ->with(['owner'])
                ->get(),
        ];
    }

    /**
     * Generate progress report
     */
    public function generateProgressReport(string $period = 'current_quarter'): array
    {
        $dateRange = $this->getDateRange($period);

        $objectives = Objective::whereBetween('periode_mulai', [$dateRange['start'], $dateRange['end']])
            ->with(['keyResults', 'tactics', 'departemen'])
            ->get();

        return [
            'period' => $period,
            'date_range' => $dateRange,
            'summary' => [
                'total_objectives' => $objectives->count(),
                'completed' => $objectives->where('status', 'completed')->count(),
                'in_progress' => $objectives->where('status', 'active')->count(),
                'average_progress' => round($objectives->avg('progress_percentage'), 1),
            ],
            'by_department' => $objectives->groupBy('departemen.nama_departemen')
                ->map(function ($deptObjectives) {
                    return [
                        'count' => $deptObjectives->count(),
                        'avg_progress' => round($deptObjectives->avg('progress_percentage'), 1),
                        'completed' => $deptObjectives->where('status', 'completed')->count(),
                    ];
                }),
            'key_results_summary' => [
                'total' => $objectives->sum(fn($obj) => $obj->keyResults->count()),
                'completed' => $objectives->sum(fn($obj) => $obj->keyResults->where('status', 'completed')->count()),
                'at_risk' => $objectives->sum(fn($obj) => $obj->keyResults->where('status', 'at_risk')->count()),
            ],
            'tactics_summary' => [
                'total' => $objectives->sum(fn($obj) => $obj->tactics->count()),
                'completed' => $objectives->sum(fn($obj) => $obj->tactics->where('status', 'completed')->count()),
                'blocked' => $objectives->sum(fn($obj) => $obj->tactics->where('status', 'blocked')->count()),
            ],
        ];
    }

    private function getDateRange(string $period): array
    {
        return match ($period) {
            'current_quarter' => [
                'start' => Carbon::now()->startOfQuarter(),
                'end' => Carbon::now()->endOfQuarter(),
            ],
            'last_quarter' => [
                'start' => Carbon::now()->subQuarter()->startOfQuarter(),
                'end' => Carbon::now()->subQuarter()->endOfQuarter(),
            ],
            'current_year' => [
                'start' => Carbon::now()->startOfYear(),
                'end' => Carbon::now()->endOfYear(),
            ],
            default => [
                'start' => Carbon::now()->startOfQuarter(),
                'end' => Carbon::now()->endOfQuarter(),
            ],
        };
    }
}
