<?php

namespace App\Services;

use App\Models\PayrollPeriod;
use App\Models\PayrollTransaction;
use App\Models\PayrollDeduction;
use App\Models\Karyawan;
use App\Models\PenggajianKaryawan;
use App\Models\Absensi;
use App\Models\Pelanggaran;
use App\Models\AturanKeterlambatan;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class PayrollService
{
    /**
     * Generate payroll untuk periode tertentu
     */
    public function generatePayroll(PayrollPeriod $period, ?array $karyawanIds = null)
    {
        DB::beginTransaction();

        try {
            // Ambil daftar karyawan yang akan diproses
            $karyawans = $this->getKaryawansForPayroll($karyawanIds);

            foreach ($karyawans as $karyawan) {
                $this->generatePayrollForKaryawan($period, $karyawan);
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Generate payroll untuk satu karyawan
     */
    private function generatePayrollForKaryawan(PayrollPeriod $period, Karyawan $karyawan)
    {
        // Ambil basis gaji karyawan
        $basisGaji = $this->getBasisGaji($karyawan);
        if (!$basisGaji) {
            throw new \Exception("Basis gaji untuk karyawan {$karyawan->nama_lengkap} tidak ditemukan");
        }

        // Hitung data absensi dan keterlambatan
        $absensiData = $this->calculateAbsensiData($karyawan, $period);

        // Hitung data absensi tambahan (sakit, izin, cuti)
        $absensiTambahanData = $this->calculateAbsensiTambahanData($karyawan, $period);

        // Hitung potongan pelanggaran
        $pelanggaranData = $this->calculatePelanggaranData($karyawan, $period);

        // Hitung potongan karyawan (kasir, stok opname, retur, kasbon)
        $potonganKaryawanData = $this->calculatePotonganKaryawanData($karyawan, $period);

        // Hitung potongan absensi (sakit tanpa surat, alpha, cuti melebihi kuota)
        $potonganAbsensiData = $this->calculatePotonganAbsensiData($karyawan, $period, $absensiData);

        // Hitung komponen lembur
        $lemburData = $this->calculateLemburData($karyawan, $period);

        // Hitung kekurangan gaji bulan sebelumnya
        $kekuranganGajiData = $this->calculateKekuranganGajiData($karyawan, $period);

        // Hitung pesangon
        $pesangonData = $this->calculatePesangonData($karyawan, $period);

        // Buat transaksi payroll
        $payrollTransaction = PayrollTransaction::create([
            'payroll_period_id' => $period->id,
            'karyawan_id' => $karyawan->id,
            'penggajian_karyawan_id' => $basisGaji->id,

            // Komponen gaji dari basis
            'gaji_pokok' => $basisGaji->gaji_pokok,
            'tunjangan_jabatan' => $basisGaji->tunjangan_jabatan,
            'tunjangan_umum' => $basisGaji->tunjangan_umum,
            'tunjangan_sembako' => $basisGaji->tunjangan_sembako,

            // Komponen lembur
            'lembur_biasa' => $lemburData['lembur_biasa'],
            'lembur_tanggal_merah' => $lemburData['lembur_tanggal_merah'],
            'lembur_tambah_hk' => $lemburData['lembur_tambah_hk'],

            // Komponen penerimaan lainnya
            'kekurangan_gaji_bulan_sebelum' => $kekuranganGajiData['kekurangan_gaji_bulan_sebelum'],
            'claim_sakit_dengan_surat' => 0, // TODO: Implementasi claim sakit
            'pesangon' => $pesangonData['pesangon'],
            'insentif' => 0, // TODO: Implementasi insentif

            // Potongan BPJS dari basis
            'potongan_bpjs_kesehatan' => $basisGaji->bpjs_kesehatan_dipotong,
            'potongan_bpjs_tk' => $basisGaji->bpjs_tk_dipotong,

            // Potongan yang dihitung
            'potongan_keterlambatan' => $absensiData['total_denda_keterlambatan'],
            'potongan_pelanggaran' => $pelanggaranData['total_denda_pelanggaran'],
            'potongan_lainnya' => ($basisGaji->potongan_lainnya ?? 0) + $potonganKaryawanData['total_potongan_karyawan'] + $potonganAbsensiData['total_potongan_absensi'],

            // Metadata absensi
            'total_hari_kerja' => $absensiData['total_hari_kerja'],
            'total_hari_hadir' => $absensiData['total_hari_hadir'],
            'total_menit_terlambat' => $absensiData['total_menit_terlambat'],
            'total_pelanggaran' => $pelanggaranData['total_pelanggaran'],

            // Data absensi tambahan
            'sakit_dengan_surat' => $absensiTambahanData['sakit_dengan_surat'],
            'sakit_tanpa_surat' => $absensiTambahanData['sakit_tanpa_surat'],
            'izin' => $absensiTambahanData['izin'],
            'ambil_cuti' => $absensiTambahanData['ambil_cuti'],
            'sisa_cuti' => $absensiTambahanData['sisa_cuti'],

            'created_by' => auth()->id(),
        ]);

        // Simpan detail potongan BPJS
        $this->createBpjsDeductions($payrollTransaction, $basisGaji);

        // Simpan detail potongan keterlambatan
        $this->createLatenessDeductions($payrollTransaction, $absensiData['detail_keterlambatan']);

        // Simpan detail potongan pelanggaran
        $this->createViolationDeductions($payrollTransaction, $pelanggaranData['detail_pelanggaran']);

        // Simpan detail potongan karyawan
        $this->createEmployeeDeductions($payrollTransaction, $potonganKaryawanData['detail_potongan_karyawan']);

        // Simpan detail potongan absensi
        $this->createAbsensiDeductions($payrollTransaction, $potonganAbsensiData['detail_potongan_absensi']);

        return $payrollTransaction;
    }

    /**
     * Ambil basis gaji karyawan terbaru
     */
    private function getBasisGaji(Karyawan $karyawan)
    {
        return $karyawan->penggajian()->latest()->first();
    }

    /**
     * Hitung data absensi dan keterlambatan
     */
    private function calculateAbsensiData(Karyawan $karyawan, PayrollPeriod $period)
    {
        $absensiRecords = Absensi::where('karyawan_id', $karyawan->id)
            ->whereBetween('tanggal_absensi', [$period->tanggal_mulai, $period->tanggal_cutoff])
            ->with([
                'jadwal.shift:id,nama_shift,waktu_mulai,waktu_selesai,is_split_shift,toleransi_keterlambatan,waktu_mulai_periode2,waktu_selesai_periode2,toleransi_keterlambatan_periode2',
                'jadwal.entitas:id,nama'
            ])
            ->get();

        $totalHariKerja = $this->calculateWorkingDays($period->tanggal_mulai, $period->tanggal_cutoff);
        $totalHariHadir = $absensiRecords->where('waktu_masuk', '!=', null)->count();
        $totalMenitTerlambat = 0;
        $totalDendaKeterlambatan = 0;
        $detailKeterlambatan = [];

        foreach ($absensiRecords as $absensi) {
            if ($absensi->is_late) {
                $menitTerlambat = $this->calculateLatenessMinutes($absensi);
                $totalMenitTerlambat += $menitTerlambat;

                // Hanya hitung denda jika belum diberi toleransi
                if (!$absensi->is_tolerance_given) {
                    // Cari aturan keterlambatan yang berlaku
                    $aturan = AturanKeterlambatan::findApplicableRule($menitTerlambat);
                    if ($aturan) {
                        $gajiPokok = $this->getBasisGaji($karyawan)->gaji_pokok ?? 0;
                        $dendaKeterlambatan = $aturan->hitungDenda($menitTerlambat, $gajiPokok);
                        $totalDendaKeterlambatan += $dendaKeterlambatan;

                        $detailKeterlambatan[] = [
                            'absensi_id' => $absensi->id,
                            'tanggal' => $absensi->tanggal_absensi,
                            'menit_terlambat' => $menitTerlambat,
                            'aturan_id' => $aturan->id,
                            'nominal_denda' => $dendaKeterlambatan,
                        ];
                    }
                }
            }
        }

        return [
            'total_hari_kerja' => $totalHariKerja,
            'total_hari_hadir' => $totalHariHadir,
            'total_menit_terlambat' => $totalMenitTerlambat,
            'total_denda_keterlambatan' => $totalDendaKeterlambatan,
            'detail_keterlambatan' => $detailKeterlambatan,
        ];
    }

    /**
     * Hitung data pelanggaran
     */
    private function calculatePelanggaranData(Karyawan $karyawan, PayrollPeriod $period)
    {
        $pelanggaranRecords = Pelanggaran::where('karyawan_id', $karyawan->id)
            ->whereBetween('tanggal', [$period->tanggal_mulai, $period->tanggal_cutoff])
            ->with('jenisPelanggaran')
            ->get();

        $totalPelanggaran = $pelanggaranRecords->count();
        $totalDendaPelanggaran = $pelanggaranRecords->sum('nominal_denda');

        $detailPelanggaran = $pelanggaranRecords->map(function ($pelanggaran) {
            return [
                'pelanggaran_id' => $pelanggaran->id,
                'tanggal' => $pelanggaran->tanggal,
                'jenis' => $pelanggaran->jenis,
                'nominal_denda' => $pelanggaran->nominal_denda,
            ];
        })->toArray();

        return [
            'total_pelanggaran' => $totalPelanggaran,
            'total_denda_pelanggaran' => $totalDendaPelanggaran,
            'detail_pelanggaran' => $detailPelanggaran,
        ];
    }

    /**
     * Hitung hari kerja dalam periode (exclude weekend)
     */
    private function calculateWorkingDays($startDate, $endDate)
    {
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);
        $workingDays = 0;

        while ($start->lte($end)) {
            if (!$start->isWeekend()) {
                $workingDays++;
            }
            $start->addDay();
        }

        return $workingDays;
    }

    /**
     * Hitung menit keterlambatan
     */
    private function calculateLatenessMinutes(Absensi $absensi)
    {
        if (!$absensi->jadwal || !$absensi->jadwal->shift || !$absensi->waktu_masuk) {
            return 0;
        }

        $shift = $absensi->jadwal->shift;
        $actualEntry = Carbon::parse($absensi->waktu_masuk);

        // Handle split shift
        if ($shift->is_split_shift ?? false) {
            $currentPeriod = $shift->getCurrentPeriod($actualEntry->format('H:i:s'));
            $periods = $shift->getWorkPeriods();

            foreach ($periods as $period) {
                if ($period['periode'] == $currentPeriod) {
                    $shiftStart = Carbon::parse($period['waktu_mulai']);
                    $toleranceMinutes = $period['toleransi_keterlambatan'] ?? 0;
                    $allowedEntry = $shiftStart->copy()->addMinutes($toleranceMinutes);

                    if ($actualEntry->greaterThan($allowedEntry)) {
                        return $actualEntry->diffInMinutes($allowedEntry);
                    }
                }
            }
        } else {
            // Regular shift
            $shiftStart = Carbon::parse($shift->waktu_mulai);
            $toleranceMinutes = $shift->toleransi_keterlambatan ?? 0;
            $allowedEntry = $shiftStart->copy()->addMinutes($toleranceMinutes);

            if ($actualEntry->greaterThan($allowedEntry)) {
                return $actualEntry->diffInMinutes($allowedEntry);
            }
        }

        return 0;
    }

    /**
     * Ambil daftar karyawan untuk payroll
     */
    private function getKaryawansForPayroll(?array $karyawanIds = null)
    {
        $query = Karyawan::with(['penggajian' => function ($q) {
            $q->latest();
        }])->where('status_aktif', true);

        if ($karyawanIds) {
            $query->whereIn('id', $karyawanIds);
        }

        return $query->get();
    }

    /**
     * Buat detail potongan BPJS
     */
    private function createBpjsDeductions(PayrollTransaction $payroll, PenggajianKaryawan $basisGaji)
    {
        if ($basisGaji->bpjs_kesehatan_dipotong > 0) {
            PayrollDeduction::create([
                'payroll_transaction_id' => $payroll->id,
                'jenis_potongan' => 'bpjs_kesehatan',
                'deskripsi' => 'Potongan BPJS Kesehatan',
                'nominal' => $basisGaji->bpjs_kesehatan_dipotong,
            ]);
        }

        if ($basisGaji->bpjs_tk_dipotong > 0) {
            PayrollDeduction::create([
                'payroll_transaction_id' => $payroll->id,
                'jenis_potongan' => 'bpjs_tk',
                'deskripsi' => 'Potongan BPJS Tenaga Kerja',
                'nominal' => $basisGaji->bpjs_tk_dipotong,
            ]);
        }
    }

    /**
     * Buat detail potongan keterlambatan
     */
    private function createLatenessDeductions(PayrollTransaction $payroll, array $detailKeterlambatan)
    {
        foreach ($detailKeterlambatan as $detail) {
            PayrollDeduction::create([
                'payroll_transaction_id' => $payroll->id,
                'jenis_potongan' => 'keterlambatan',
                'kode_referensi' => $detail['absensi_id'],
                'deskripsi' => "Keterlambatan {$detail['menit_terlambat']} menit",
                'nominal' => $detail['nominal_denda'],
                'tanggal_kejadian' => $detail['tanggal'],
            ]);
        }
    }

    /**
     * Buat detail potongan pelanggaran
     */
    private function createViolationDeductions(PayrollTransaction $payroll, array $detailPelanggaran)
    {
        foreach ($detailPelanggaran as $detail) {
            PayrollDeduction::create([
                'payroll_transaction_id' => $payroll->id,
                'jenis_potongan' => 'pelanggaran',
                'kode_referensi' => $detail['pelanggaran_id'],
                'deskripsi' => "Pelanggaran: {$detail['jenis']}",
                'nominal' => $detail['nominal_denda'],
                'tanggal_kejadian' => $detail['tanggal'],
            ]);
        }
    }

    /**
     * Hitung data potongan karyawan
     */
    private function calculatePotonganKaryawanData(Karyawan $karyawan, PayrollPeriod $period)
    {
        // Ambil potongan yang tanggalnya berada dalam periode payroll
        $potonganRecords = \App\Models\PotonganKaryawan::where('karyawan_id', $karyawan->id)
            ->whereBetween('bulan_potongan', [$period->tanggal_mulai, $period->tanggal_cutoff])
            ->get();

        $totalPotonganKaryawan = $potonganRecords->sum('nominal');

        $detailPotonganKaryawan = $potonganRecords->map(function ($potongan) {
            return [
                'potongan_karyawan_id' => $potongan->id,
                'jenis_potongan' => $potongan->jenis_potongan,
                'nominal' => $potongan->nominal,
                'keterangan' => $potongan->keterangan,
                'tanggal_potongan' => $potongan->bulan_potongan,
            ];
        })->toArray();

        return [
            'total_potongan_karyawan' => $totalPotonganKaryawan,
            'detail_potongan_karyawan' => $detailPotonganKaryawan,
        ];
    }

    /**
     * Buat detail potongan karyawan
     */
    private function createEmployeeDeductions(PayrollTransaction $payroll, array $detailPotonganKaryawan)
    {
        foreach ($detailPotonganKaryawan as $detail) {
            PayrollDeduction::create([
                'payroll_transaction_id' => $payroll->id,
                'jenis_potongan' => $detail['jenis_potongan'],
                'kode_referensi' => $detail['potongan_karyawan_id'],
                'deskripsi' => $this->getPotonganDescription($detail['jenis_potongan'], $detail['keterangan']),
                'nominal' => $detail['nominal'],
                'keterangan' => $detail['keterangan'],
                'tanggal_kejadian' => $detail['tanggal_potongan'],
            ]);
        }
    }

    /**
     * Generate deskripsi untuk potongan karyawan
     */
    private function getPotonganDescription($jenisPotongan, $keterangan = null)
    {
        $labels = [
            'kasir' => 'Potongan Kasir',
            'stok_opname' => 'Potongan Stok Opname',
            'retur' => 'Potongan Retur',
            'kasbon' => 'Potongan Kasbon',
        ];

        $baseDescription = $labels[$jenisPotongan] ?? $jenisPotongan;

        if ($keterangan) {
            return $baseDescription . ': ' . $keterangan;
        }

        return $baseDescription;
    }

    /**
     * Hitung data potongan absensi (sakit tanpa surat, alpha, cuti melebihi kuota)
     */
    private function calculatePotonganAbsensiData(Karyawan $karyawan, PayrollPeriod $period, array $absensiData)
    {
        $detailPotonganAbsensi = [];
        $totalPotonganAbsensi = 0;

        // 1. Hitung potongan sakit tanpa surat
        $sakitTanpaSurat = $this->calculateSakitTanpaSuratDeduction($karyawan, $period);
        if ($sakitTanpaSurat['total'] > 0) {
            $detailPotonganAbsensi = array_merge($detailPotonganAbsensi, $sakitTanpaSurat['detail']);
            $totalPotonganAbsensi += $sakitTanpaSurat['total'];
        }

        // 2. Hitung potongan alpha/tidak hadir tanpa keterangan
        $alphaDeduction = $this->calculateAlphaDeduction($karyawan, $period, $absensiData);
        if ($alphaDeduction['total'] > 0) {
            $detailPotonganAbsensi = array_merge($detailPotonganAbsensi, $alphaDeduction['detail']);
            $totalPotonganAbsensi += $alphaDeduction['total'];
        }

        // 3. Hitung potongan cuti melebihi kuota
        $cutiMelebihiKuota = $this->calculateCutiMelebihiKuotaDeduction($karyawan, $period);
        if ($cutiMelebihiKuota['total'] > 0) {
            $detailPotonganAbsensi = array_merge($detailPotonganAbsensi, $cutiMelebihiKuota['detail']);
            $totalPotonganAbsensi += $cutiMelebihiKuota['total'];
        }

        return [
            'total_potongan_absensi' => $totalPotonganAbsensi,
            'detail_potongan_absensi' => $detailPotonganAbsensi,
        ];
    }

    /**
     * Hitung potongan sakit tanpa surat
     */
    private function calculateSakitTanpaSuratDeduction(Karyawan $karyawan, PayrollPeriod $period)
    {
        $sakitTanpaSurat = \App\Models\CutiIzin::where('karyawan_id', $karyawan->id)
            ->where('jenis_permohonan', 'sakit')
            ->where('status', 'approved')
            ->where('dokumen_pendukung', null) // Tanpa surat dokter
            ->whereBetween('tanggal_mulai', [$period->tanggal_mulai, $period->tanggal_cutoff])
            ->get();

        $total = 0;
        $detail = [];
        $gajiHarian = $this->calculateDailyWage($karyawan);

        foreach ($sakitTanpaSurat as $sakit) {
            $potongan = $sakit->jumlah_hari * $gajiHarian;
            $total += $potongan;

            $detail[] = [
                'jenis_potongan' => 'sakit_tanpa_surat',
                'kode_referensi' => $sakit->id,
                'deskripsi' => "Sakit tanpa surat ({$sakit->jumlah_hari} hari): {$sakit->alasan}",
                'nominal' => $potongan,
                'keterangan' => "Periode: {$sakit->tanggal_mulai->format('d M Y')} - {$sakit->tanggal_selesai->format('d M Y')}",
                'tanggal_kejadian' => $sakit->tanggal_mulai,
            ];
        }

        return [
            'total' => $total,
            'detail' => $detail,
        ];
    }

    /**
     * Hitung potongan alpha/tidak hadir tanpa keterangan
     */
    private function calculateAlphaDeduction(Karyawan $karyawan, PayrollPeriod $period, array $absensiData)
    {
        // Hitung hari alpha = hari kerja - hari hadir - hari cuti/izin/sakit yang approved
        $hariKerja = $absensiData['total_hari_kerja'];
        $hariHadir = $absensiData['total_hari_hadir'];

        // Hitung hari cuti/izin/sakit yang approved dalam periode
        $hariCutiIzinSakit = \App\Models\CutiIzin::where('karyawan_id', $karyawan->id)
            ->where('status', 'approved')
            ->where(function ($query) use ($period) {
                $query->whereBetween('tanggal_mulai', [$period->tanggal_mulai, $period->tanggal_cutoff])
                    ->orWhereBetween('tanggal_selesai', [$period->tanggal_mulai, $period->tanggal_cutoff])
                    ->orWhere(function ($q) use ($period) {
                        $q->where('tanggal_mulai', '<=', $period->tanggal_mulai)
                            ->where('tanggal_selesai', '>=', $period->tanggal_cutoff);
                    });
            })
            ->sum('jumlah_hari');

        $hariAlpha = $hariKerja - $hariHadir - $hariCutiIzinSakit;

        if ($hariAlpha <= 0) {
            return ['total' => 0, 'detail' => []];
        }

        $gajiHarian = $this->calculateDailyWage($karyawan);
        $totalPotongan = $hariAlpha * $gajiHarian;

        $detail = [[
            'jenis_potongan' => 'alpha',
            'kode_referensi' => null,
            'deskripsi' => "Alpha/Tidak hadir tanpa keterangan ({$hariAlpha} hari)",
            'nominal' => $totalPotongan,
            'keterangan' => "Periode: {$period->tanggal_mulai->format('d M Y')} - {$period->tanggal_cutoff->format('d M Y')}",
            'tanggal_kejadian' => $period->tanggal_cutoff,
        ]];

        return [
            'total' => $totalPotongan,
            'detail' => $detail,
        ];
    }

    /**
     * Hitung potongan cuti melebihi kuota
     */
    private function calculateCutiMelebihiKuotaDeduction(Karyawan $karyawan, PayrollPeriod $period)
    {
        // Ambil cuti yang approved dalam periode
        $cutiRecords = \App\Models\CutiIzin::where('karyawan_id', $karyawan->id)
            ->where('jenis_permohonan', 'cuti')
            ->where('status', 'approved')
            ->whereBetween('tanggal_mulai', [$period->tanggal_mulai, $period->tanggal_cutoff])
            ->get();

        if ($cutiRecords->isEmpty()) {
            return ['total' => 0, 'detail' => []];
        }

        // Cek kuota cuti karyawan
        $cutiModel = new \App\Models\CutiIzin();
        $remainingQuota = $cutiModel->getRemainingLeaveQuota($karyawan->id);

        // Hitung total cuti yang diambil tahun ini
        $currentYear = now()->year;
        $totalCutiTahunIni = \App\Models\CutiIzin::where('karyawan_id', $karyawan->id)
            ->where('jenis_permohonan', 'cuti')
            ->where('status', 'approved')
            ->whereYear('tanggal_mulai', $currentYear)
            ->sum('jumlah_hari');

        // Tentukan kuota tahunan berdasarkan kontrak
        $activeContract = $karyawan->riwayatKontrak()
            ->where('is_active', 1)
            ->orderBy('tgl_mulai', 'desc')
            ->first();

        $yearlyQuota = 0;
        if ($activeContract) {
            $yearlyQuota = match ($activeContract->jenis_kontrak) {
                'PKWT', 'PKWTT' => 12,
                default => 0
            };
        }

        $hariMelebihiKuota = max(0, $totalCutiTahunIni - $yearlyQuota);

        if ($hariMelebihiKuota <= 0) {
            return ['total' => 0, 'detail' => []];
        }

        $gajiHarian = $this->calculateDailyWage($karyawan);
        $total = 0;
        $detail = [];

        foreach ($cutiRecords as $cuti) {
            // Hitung berapa hari dari cuti ini yang melebihi kuota
            $hariPotongan = min($cuti->jumlah_hari, $hariMelebihiKuota);
            if ($hariPotongan > 0) {
                $potongan = $hariPotongan * $gajiHarian;
                $total += $potongan;

                $detail[] = [
                    'jenis_potongan' => 'cuti_melebihi_kuota',
                    'kode_referensi' => $cuti->id,
                    'deskripsi' => "Cuti melebihi kuota ({$hariPotongan} hari): {$cuti->alasan}",
                    'nominal' => $potongan,
                    'keterangan' => "Periode: {$cuti->tanggal_mulai->format('d M Y')} - {$cuti->tanggal_selesai->format('d M Y')}",
                    'tanggal_kejadian' => $cuti->tanggal_mulai,
                ];

                $hariMelebihiKuota -= $hariPotongan;
            }
        }

        return [
            'total' => $total,
            'detail' => $detail,
        ];
    }

    /**
     * Hitung gaji harian karyawan
     */
    private function calculateDailyWage(Karyawan $karyawan)
    {
        $basisGaji = $this->getBasisGaji($karyawan);
        $gajiPokok = $basisGaji->gaji_pokok ?? 0;

        // Asumsi 30 hari kerja per bulan
        return $gajiPokok / 30;
    }

    /**
     * Buat detail potongan absensi
     */
    private function createAbsensiDeductions(PayrollTransaction $payroll, array $detailPotonganAbsensi)
    {
        foreach ($detailPotonganAbsensi as $detail) {
            PayrollDeduction::create([
                'payroll_transaction_id' => $payroll->id,
                'jenis_potongan' => $detail['jenis_potongan'],
                'kode_referensi' => $detail['kode_referensi'],
                'deskripsi' => $detail['deskripsi'],
                'nominal' => $detail['nominal'],
                'keterangan' => $detail['keterangan'],
                'tanggal_kejadian' => $detail['tanggal_kejadian'],
            ]);
        }
    }

    /**
     * Hitung komponen lembur berdasarkan jenis
     */
    private function calculateLemburData(Karyawan $karyawan, PayrollPeriod $period)
    {
        $lemburRecords = \App\Models\Lembur::where('karyawan_id', $karyawan->id)
            ->whereBetween('tanggal', [$period->tanggal_mulai, $period->tanggal_cutoff])
            ->with('jenisLembur')
            ->get();

        $lemburBiasa = 0;
        $lemburTanggalMerah = 0;
        $lemburTambahHK = 0;
        $totalLembur = 0;

        foreach ($lemburRecords as $lembur) {
            $upahLembur = $lembur->hitungUpahLembur();
            $totalLembur += $upahLembur;

            // Kategorikan berdasarkan jenis lembur
            if ($lembur->jenisLembur) {
                $namaJenis = strtolower($lembur->jenisLembur->nama_jenis);

                if (str_contains($namaJenis, 'hari biasa') || str_contains($namaJenis, 'weekday')) {
                    $lemburBiasa += $upahLembur;
                } elseif (str_contains($namaJenis, 'tanggal merah') || str_contains($namaJenis, 'holiday')) {
                    $lemburTanggalMerah += $upahLembur;
                } elseif (str_contains($namaJenis, 'hk') || str_contains($namaJenis, 'hari kerja')) {
                    $lemburTambahHK += $upahLembur;
                } else {
                    // Default ke lembur biasa jika tidak bisa dikategorikan
                    $lemburBiasa += $upahLembur;
                }
            } else {
                // Fallback untuk data lama tanpa jenis lembur
                $jenisHari = $this->getJenisHariFromTanggal($lembur->tanggal);
                if ($jenisHari === 'hari_libur') {
                    $lemburTanggalMerah += $upahLembur;
                } else {
                    $lemburBiasa += $upahLembur;
                }
            }
        }

        return [
            'lembur_biasa' => $lemburBiasa,
            'lembur_tanggal_merah' => $lemburTanggalMerah,
            'lembur_tambah_hk' => $lemburTambahHK,
            'total_lembur' => $totalLembur,
            'detail_lembur' => $lemburRecords,
        ];
    }

    /**
     * Hitung kekurangan gaji bulan sebelumnya
     */
    private function calculateKekuranganGajiData(Karyawan $karyawan, PayrollPeriod $period)
    {
        // Ambil kekurangan gaji yang sudah disetujui dan belum dibayar
        $kekuranganGaji = \App\Models\KekuranganGaji::where('karyawan_id', $karyawan->id)
            ->where('status', 'approved')
            ->where('periode_kekurangan', '<', $period->tanggal_mulai)
            ->sum('nominal_kekurangan');

        return [
            'kekurangan_gaji_bulan_sebelum' => $kekuranganGaji,
            'detail_kekurangan' => \App\Models\KekuranganGaji::where('karyawan_id', $karyawan->id)
                ->where('status', 'approved')
                ->where('periode_kekurangan', '<', $period->tanggal_mulai)
                ->get(),
        ];
    }

    /**
     * Hitung pesangon yang sudah disetujui
     */
    private function calculatePesangonData(Karyawan $karyawan, PayrollPeriod $period)
    {
        // Ambil pesangon yang sudah disetujui dan belum dibayar untuk periode ini
        $pesangon = \App\Models\Pesangon::where('karyawan_id', $karyawan->id)
            ->where('status', 'approved')
            ->where('periode_pembayaran', $period->tanggal_mulai->format('Y-m-01'))
            ->sum('nominal_pesangon');

        return [
            'pesangon' => $pesangon,
            'detail_pesangon' => \App\Models\Pesangon::where('karyawan_id', $karyawan->id)
                ->where('status', 'approved')
                ->where('periode_pembayaran', $period->tanggal_mulai->format('Y-m-01'))
                ->get(),
        ];
    }

    /**
     * Hitung data absensi tambahan (sakit, izin, cuti)
     */
    private function calculateAbsensiTambahanData(Karyawan $karyawan, PayrollPeriod $period)
    {
        // Hitung data dari CutiIzin yang approved dalam periode
        $cutiIzinRecords = \App\Models\CutiIzin::where('karyawan_id', $karyawan->id)
            ->where('status', 'approved')
            ->where(function ($query) use ($period) {
                $query->whereBetween('tanggal_mulai', [$period->tanggal_mulai, $period->tanggal_cutoff])
                    ->orWhereBetween('tanggal_selesai', [$period->tanggal_mulai, $period->tanggal_cutoff])
                    ->orWhere(function ($q) use ($period) {
                        $q->where('tanggal_mulai', '<=', $period->tanggal_mulai)
                            ->where('tanggal_selesai', '>=', $period->tanggal_cutoff);
                    });
            })
            ->get();

        $sakitDenganSurat = 0;
        $sakitTanpaSurat = 0;
        $izin = 0;
        $ambilCuti = 0;

        foreach ($cutiIzinRecords as $record) {
            // Hitung hari yang overlap dengan periode payroll
            $startDate = max($record->tanggal_mulai, $period->tanggal_mulai);
            $endDate = min($record->tanggal_selesai, $period->tanggal_cutoff);

            $days = $this->calculateWorkingDaysBetween($startDate, $endDate);

            switch ($record->jenis_permohonan) {
                case 'sakit':
                    if ($record->dokumen_pendukung) {
                        $sakitDenganSurat += $days;
                    } else {
                        $sakitTanpaSurat += $days;
                    }
                    break;
                case 'izin':
                    $izin += $days;
                    break;
                case 'cuti':
                    $ambilCuti += $days;
                    break;
            }
        }

        // Hitung sisa cuti tahunan
        $cutiModel = new \App\Models\CutiIzin();
        $sisaCuti = $cutiModel->getRemainingLeaveQuota($karyawan->id);

        return [
            'sakit_dengan_surat' => $sakitDenganSurat,
            'sakit_tanpa_surat' => $sakitTanpaSurat,
            'izin' => $izin,
            'ambil_cuti' => $ambilCuti,
            'sisa_cuti' => $sisaCuti,
        ];
    }

    /**
     * Hitung hari kerja antara dua tanggal
     */
    private function calculateWorkingDaysBetween($startDate, $endDate)
    {
        $start = \Carbon\Carbon::parse($startDate);
        $end = \Carbon\Carbon::parse($endDate);
        $workingDays = 0;

        while ($start->lte($end)) {
            if (!$start->isWeekend()) {
                $workingDays++;
            }
            $start->addDay();
        }

        return $workingDays;
    }

    /**
     * Recalculate lateness deductions for a payroll transaction
     */
    public function recalculateLatenessDeductions(PayrollTransaction $payroll)
    {
        // Hitung ulang data absensi dan keterlambatan
        $absensiData = $this->calculateAbsensiData($payroll->karyawan, $payroll->payrollPeriod);

        // Update potongan keterlambatan di payroll
        $payroll->update([
            'potongan_keterlambatan' => $absensiData['total_denda_keterlambatan'],
            'total_potongan' => $payroll->potongan_bpjs_kesehatan +
                $payroll->potongan_bpjs_tk +
                $absensiData['total_denda_keterlambatan'] +
                $payroll->potongan_pelanggaran +
                $payroll->potongan_lainnya,
            'take_home_pay' => $payroll->total_gaji_kotor -
                ($payroll->potongan_bpjs_kesehatan +
                    $payroll->potongan_bpjs_tk +
                    $absensiData['total_denda_keterlambatan'] +
                    $payroll->potongan_pelanggaran +
                    $payroll->potongan_lainnya),
        ]);

        // Hapus detail potongan keterlambatan yang lama
        $payroll->payrollDeductions()->where('jenis_potongan', 'keterlambatan')->delete();

        // Buat detail potongan keterlambatan yang baru
        $this->createLatenessDeductions($payroll, $absensiData['detail_keterlambatan']);

        return $absensiData;
    }

    /**
     * Helper method untuk menentukan jenis hari dari tanggal
     */
    private function getJenisHariFromTanggal($tanggal)
    {
        $date = \Carbon\Carbon::parse($tanggal);
        $dayOfWeek = $date->dayOfWeek;

        // Sabtu = 6, Minggu = 0
        if ($dayOfWeek == 0 || $dayOfWeek == 6) {
            return 'hari_libur';
        }

        // TODO: Tambahkan pengecekan hari libur nasional dari database
        return 'hari_biasa';
    }
}
