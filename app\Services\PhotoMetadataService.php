<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;

class PhotoMetadataService
{
    /**
     * Extract metadata from uploaded photo
     */
    public static function extractMetadata(UploadedFile $file, ?float $latitude = null, ?float $longitude = null): array
    {
        $metadata = [
            'filename' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'captured_at' => Carbon::now()->toISOString(),
            'latitude' => $latitude,
            'longitude' => $longitude,
            'status_kehadiran' => null, // Will be set later
        ];

        try {
            // Try to read EXIF data if available
            $tempPath = $file->getRealPath();
            if (function_exists('exif_read_data') && in_array($file->getMimeType(), ['image/jpeg', 'image/jpg'])) {
                $exifData = @exif_read_data($tempPath);

                if ($exifData) {
                    // Extract datetime from EXIF if available
                    if (isset($exifData['DateTime'])) {
                        $metadata['exif_datetime'] = $exifData['DateTime'];
                    }

                    // Extract GPS data from EXIF if available and not provided
                    if (!$latitude && !$longitude && isset($exifData['GPS'])) {
                        $gps = self::extractGPSFromExif($exifData['GPS']);
                        if ($gps) {
                            $metadata['latitude'] = $gps['latitude'];
                            $metadata['longitude'] = $gps['longitude'];
                        }
                    }

                    // Extract camera info
                    if (isset($exifData['Make'])) {
                        $metadata['camera_make'] = $exifData['Make'];
                    }
                    if (isset($exifData['Model'])) {
                        $metadata['camera_model'] = $exifData['Model'];
                    }
                }
            }
        } catch (\Exception $e) {
            Log::warning('Failed to extract EXIF data: ' . $e->getMessage());
        }

        return $metadata;
    }

    /**
     * Determine attendance status based on schedule and time
     */
    public static function determineAttendanceStatus($karyawanId, $datetime = null): string
    {
        if (!$datetime) {
            $datetime = Carbon::now();
        }

        try {
            // Get today's schedule for the employee
            $schedule = \App\Models\Schedule::with(['shift', 'karyawan'])
                ->whereHas('karyawan', function ($query) use ($karyawanId) {
                    $query->where('id', $karyawanId);
                })
                ->whereDate('tanggal_jadwal', $datetime->toDateString())
                ->first();

            if (!$schedule || !$schedule->shift) {
                return 'Tidak Ada Jadwal';
            }

            $shift = $schedule->shift;
            $actualTime = Carbon::parse($datetime);

            // Handle split shift
            if ($shift->isSplitShift()) {
                $currentPeriod = $shift->getCurrentPeriod($actualTime->format('H:i:s'));
                $periods = $shift->getWorkPeriods();

                foreach ($periods as $period) {
                    if ($period['periode'] == $currentPeriod) {
                        $shiftStart = Carbon::parse($period['waktu_mulai']);
                        $toleranceMinutes = $period['toleransi_keterlambatan'] ?? 0;

                        // Compare with shift start time + tolerance
                        if ($actualTime->lessThanOrEqualTo($shiftStart->addMinutes($toleranceMinutes))) {
                            return 'Tepat Waktu';
                        } else {
                            return 'Telat';
                        }
                    }
                }
            } else {
                // Regular shift logic
                $shiftStart = Carbon::parse($shift->waktu_mulai);
                $toleranceMinutes = $shift->toleransi_keterlambatan ?? 0;

                // Compare with shift start time + tolerance
                if ($actualTime->lessThanOrEqualTo($shiftStart->addMinutes($toleranceMinutes))) {
                    return 'Tepat Waktu';
                } else {
                    return 'Telat';
                }
            }

            return 'Unknown'; // Fallback if no period matches
        } catch (\Exception $e) {
            Log::warning('Failed to determine attendance status: ' . $e->getMessage());
            return 'Unknown';
        }
    }

    /**
     * Extract GPS coordinates from EXIF data
     */
    private static function extractGPSFromExif(array $gpsData): ?array
    {
        try {
            if (!isset($gpsData['GPSLatitude']) || !isset($gpsData['GPSLongitude'])) {
                return null;
            }

            $latitude = self::convertGPSCoordinate($gpsData['GPSLatitude'], $gpsData['GPSLatitudeRef']);
            $longitude = self::convertGPSCoordinate($gpsData['GPSLongitude'], $gpsData['GPSLongitudeRef']);

            return [
                'latitude' => $latitude,
                'longitude' => $longitude
            ];
        } catch (\Exception $e) {
            Log::warning('Failed to extract GPS from EXIF: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Convert GPS coordinate from EXIF format to decimal
     */
    private static function convertGPSCoordinate(array $coordinate, string $hemisphere): float
    {
        $degrees = count($coordinate) > 0 ? self::gpsToDecimal($coordinate[0]) : 0;
        $minutes = count($coordinate) > 1 ? self::gpsToDecimal($coordinate[1]) : 0;
        $seconds = count($coordinate) > 2 ? self::gpsToDecimal($coordinate[2]) : 0;

        $decimal = $degrees + ($minutes / 60) + ($seconds / 3600);

        // Apply hemisphere
        if (in_array($hemisphere, ['S', 'W'])) {
            $decimal *= -1;
        }

        return $decimal;
    }

    /**
     * Convert GPS fraction to decimal
     */
    private static function gpsToDecimal(string $fraction): float
    {
        $parts = explode('/', $fraction);
        if (count($parts) == 2) {
            return floatval($parts[0]) / floatval($parts[1]);
        }
        return floatval($fraction);
    }

    /**
     * Format metadata for display like in the image
     */
    public static function formatMetadataForDisplay(array $metadata): array
    {
        $formatted = [];

        // Latitude & Longitude
        if (isset($metadata['latitude']) && isset($metadata['longitude'])) {
            $formatted['coordinates'] = sprintf(
                "%.6f°, %.6f°",
                $metadata['latitude'],
                $metadata['longitude']
            );
            $formatted['coordinates_display'] = sprintf(
                "%.6f°, %.6f°",
                $metadata['latitude'],
                $metadata['longitude']
            );
        }

        // DateTime with Indonesia timezone
        if (isset($metadata['captured_at'])) {
            $date = Carbon::parse($metadata['captured_at'])->setTimezone('Asia/Jakarta');
            $formatted['datetime'] = $date->format('d/m/Y H:i:s');
            $formatted['datetime_display'] = $date->format('d M Y, H:i:s') . ' WIB';
        }

        // Status Kehadiran
        if (isset($metadata['status_kehadiran'])) {
            $formatted['status_kehadiran'] = $metadata['status_kehadiran'];
        }

        // Camera info (like in the image: XIAOMI 13T)
        if (isset($metadata['camera_make']) && isset($metadata['camera_model'])) {
            $formatted['camera'] = strtoupper($metadata['camera_make'] . ' ' . $metadata['camera_model']);
            $formatted['camera_info'] = strtoupper($metadata['camera_make'] . ' ' . $metadata['camera_model']);
        } elseif (isset($metadata['camera_make'])) {
            $formatted['camera'] = strtoupper($metadata['camera_make']);
            $formatted['camera_info'] = strtoupper($metadata['camera_make']);
        } elseif (isset($metadata['camera_model'])) {
            $formatted['camera'] = strtoupper($metadata['camera_model']);
            $formatted['camera_info'] = strtoupper($metadata['camera_model']);
        }

        return $formatted;
    }
}
