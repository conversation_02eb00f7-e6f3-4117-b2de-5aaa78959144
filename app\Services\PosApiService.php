<?php

namespace App\Services;

use App\Models\User;
use App\Models\PosTransaction;
use App\Models\Product;
use App\Models\Customer;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class PosApiService
{
    /**
     * Create API token for POS user
     */
    public function createPosToken(User $user, string $deviceName, array $abilities = null): array
    {
        $abilities = $abilities ?? config('pos.api.token.abilities');
        
        $token = $user->createToken($deviceName, $abilities);

        // Log token creation
        Log::channel('pos')->info('POS token created', [
            'user_id' => $user->id,
            'device_name' => $deviceName,
            'abilities' => $abilities,
        ]);

        return [
            'token' => $token->plainTextToken,
            'abilities' => $abilities,
            'expires_at' => config('pos.api.token.expiration'),
        ];
    }

    /**
     * Validate POS user permissions
     */
    public function validatePosAccess(User $user): bool
    {
        // Check if user has required roles
        $allowedRoles = ['admin', 'supervisor', 'manager'];
        
        if (in_array($user->role, $allowedRoles)) {
            return true;
        }

        // Check Shield roles
        if ($user->hasAnyRole(['super_admin', 'manager_hrd', 'kepala_toko', 'keptok'])) {
            return true;
        }

        // Check if karyawan has POS access
        if ($user->role === 'karyawan' && $user->karyawan) {
            // Add specific logic for karyawan POS access
            return true;
        }

        return false;
    }

    /**
     * Get products for POS sync
     */
    public function getProductsForSync(array $filters = []): array
    {
        $cacheKey = 'pos_products_' . md5(serialize($filters));
        
        return Cache::remember($cacheKey, 300, function () use ($filters) {
            $query = Product::with('category')
                ->where('is_active', true);

            // Apply filters
            if (isset($filters['category_id'])) {
                $query->where('category_id', $filters['category_id']);
            }

            if (isset($filters['in_stock']) && $filters['in_stock']) {
                $query->where('stock_quantity', '>', 0);
            }

            if (isset($filters['search'])) {
                $search = $filters['search'];
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('sku', 'like', "%{$search}%");
                });
            }

            $products = $query->get([
                'id', 'name', 'sku', 'barcode', 'price', 'cost_price', 
                'category_id', 'stock_quantity', 'is_food_item'
            ]);

            return [
                'products' => $products,
                'last_updated' => now()->toISOString(),
                'total_count' => $products->count(),
            ];
        });
    }

    /**
     * Get customers for POS sync
     */
    public function getCustomersForSync(array $filters = []): array
    {
        $cacheKey = 'pos_customers_' . md5(serialize($filters));
        
        return Cache::remember($cacheKey, 300, function () use ($filters) {
            $query = Customer::where('is_active', true);

            if (isset($filters['search'])) {
                $search = $filters['search'];
                $query->where(function ($q) use ($search) {
                    $q->where('nama', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('telepon', 'like', "%{$search}%");
                });
            }

            $customers = $query->get([
                'id', 'nama', 'email', 'telepon', 'loyalty_points'
            ]);

            return [
                'customers' => $customers,
                'last_updated' => now()->toISOString(),
                'total_count' => $customers->count(),
            ];
        });
    }

    /**
     * Process offline transaction sync
     */
    public function processOfflineTransactions(array $transactions, User $user): array
    {
        $results = [
            'synced' => [],
            'failed' => [],
            'conflicts' => [],
        ];

        foreach ($transactions as $transactionData) {
            try {
                // Check for existing transaction
                $existing = PosTransaction::where('transaction_number', $transactionData['transaction_number'])->first();
                
                if ($existing) {
                    $results['conflicts'][] = [
                        'transaction_number' => $transactionData['transaction_number'],
                        'reason' => 'Transaction already exists',
                        'existing_id' => $existing->id,
                    ];
                    continue;
                }

                // Create transaction
                $transaction = $this->createTransactionFromSync($transactionData, $user);
                
                $results['synced'][] = [
                    'transaction_number' => $transaction->transaction_number,
                    'id' => $transaction->id,
                    'synced_at' => $transaction->synced_at,
                ];

                // Log successful sync
                Log::channel('pos')->info('Transaction synced successfully', [
                    'transaction_id' => $transaction->id,
                    'transaction_number' => $transaction->transaction_number,
                    'user_id' => $user->id,
                ]);

            } catch (\Exception $e) {
                $results['failed'][] = [
                    'transaction_number' => $transactionData['transaction_number'] ?? 'unknown',
                    'error' => $e->getMessage(),
                ];

                // Log sync failure
                Log::channel('pos')->error('Transaction sync failed', [
                    'transaction_data' => $transactionData,
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $results;
    }

    /**
     * Create transaction from sync data
     */
    private function createTransactionFromSync(array $data, User $user): PosTransaction
    {
        // Create transaction
        $transaction = PosTransaction::create([
            'transaction_number' => $data['transaction_number'],
            'customer_id' => $data['customer_id'] ?? null,
            'user_id' => $user->id,
            'transaction_date' => $data['transaction_date'],
            'total_amount' => $data['total_amount'],
            'discount_amount' => $data['discount_amount'] ?? 0,
            'tax_amount' => $data['tax_amount'] ?? 0,
            'net_amount' => $data['net_amount'],
            'payment_method' => $data['payment_method'],
            'amount_paid' => $data['amount_paid'],
            'change_given' => $data['change_given'] ?? 0,
            'table_number' => $data['table_number'] ?? null,
            'is_offline_transaction' => true,
            'synced_at' => now(),
        ]);

        // Create transaction items
        foreach ($data['items'] as $itemData) {
            $transaction->items()->create([
                'product_id' => $itemData['product_id'],
                'quantity' => $itemData['quantity'],
                'unit_price' => $itemData['unit_price'],
                'discount_per_item' => $itemData['discount_per_item'] ?? 0,
            ]);

            // Update product stock if enabled
            if (config('pos.business.inventory.auto_deduct_stock')) {
                $product = Product::find($itemData['product_id']);
                if ($product) {
                    $newStock = $product->stock_quantity - $itemData['quantity'];
                    
                    // Check if negative stock is allowed
                    if (!config('pos.business.inventory.allow_negative_stock') && $newStock < 0) {
                        throw new \Exception("Insufficient stock for product: {$product->name}");
                    }
                    
                    $product->update(['stock_quantity' => $newStock]);
                }
            }
        }

        return $transaction;
    }

    /**
     * Get sync statistics
     */
    public function getSyncStatistics(): array
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();

        return [
            'total_transactions' => PosTransaction::count(),
            'offline_transactions' => PosTransaction::where('is_offline_transaction', true)->count(),
            'today_transactions' => PosTransaction::whereDate('transaction_date', $today)->count(),
            'month_transactions' => PosTransaction::where('transaction_date', '>=', $thisMonth)->count(),
            'last_sync' => PosTransaction::where('is_offline_transaction', true)
                ->latest('synced_at')
                ->value('synced_at'),
            'total_revenue_today' => PosTransaction::whereDate('transaction_date', $today)->sum('net_amount'),
            'total_revenue_month' => PosTransaction::where('transaction_date', '>=', $thisMonth)->sum('net_amount'),
        ];
    }

    /**
     * Clear POS cache
     */
    public function clearCache(): void
    {
        Cache::forget('pos_products_*');
        Cache::forget('pos_customers_*');
        
        Log::channel('pos')->info('POS cache cleared');
    }
}
