<?php

namespace App\Services;

use App\Models\Category;
use App\Models\Customer;
use App\Models\Outlet;
use App\Models\Product;
use App\Models\User;
use Illuminate\Support\Facades\Cache;

class PosCache
{
    const CACHE_TTL = 300; // 5 minutes

    /**
     * Get cached category options
     */
    public static function getCategoryOptions()
    {
        return Cache::remember('pos_category_options', self::CACHE_TTL, function () {
            return Category::select(['id', 'name'])
                ->orderBy('name')
                ->pluck('name', 'id');
        });
    }

    /**
     * Get cached outlet options
     */
    public static function getOutletOptions()
    {
        return Cache::remember('pos_outlet_options', self::CACHE_TTL, function () {
            return Outlet::select(['id', 'name'])
                ->where('is_active', true)
                ->orderBy('name')
                ->pluck('name', 'id');
        });
    }

    /**
     * Get cached customer options
     */
    public static function getCustomerOptions()
    {
        return Cache::remember('pos_customer_options', self::CACHE_TTL, function () {
            return Customer::select(['id', 'nama', 'email'])
                ->where('is_active', true)
                ->orderBy('nama')
                ->get()
                ->mapWithKeys(function ($customer) {
                    $label = $customer->nama;
                    if ($customer->email) {
                        $label .= ' (' . $customer->email . ')';
                    }
                    return [$customer->id => $label];
                });
        });
    }

    /**
     * Get cached user options with jabatan
     */
    public static function getUserOptionsWithJabatan()
    {
        return Cache::remember('pos_user_options_jabatan', self::CACHE_TTL, function () {
            return User::with(['karyawan.jabatan:id,nama_jabatan'])
                ->select(['id', 'name'])
                ->get()
                ->mapWithKeys(function ($user) {
                    $label = $user->name;
                    if ($user->karyawan?->jabatan) {
                        $label .= ' (' . $user->karyawan->jabatan->nama_jabatan . ')';
                    }
                    return [$user->id => $label];
                });
        });
    }

    /**
     * Get cached product options
     */
    public static function getProductOptions()
    {
        return Cache::remember('pos_product_options', self::CACHE_TTL, function () {
            return Product::select(['id', 'name', 'sku', 'price'])
                ->orderBy('name')
                ->get()
                ->mapWithKeys(function ($product) {
                    $label = $product->name;
                    if ($product->sku) {
                        $label .= ' (' . $product->sku . ')';
                    }
                    $label .= ' - Rp ' . number_format($product->price, 0, ',', '.');
                    return [$product->id => $label];
                });
        });
    }

    /**
     * Get dashboard stats with caching
     */
    public static function getDashboardStats()
    {
        return Cache::remember('pos_dashboard_stats', 60, function () {
            return [
                'total_products' => Product::count(),
                'active_products' => Product::where('is_active', true)->count(),
                'total_customers' => Customer::count(),
                'active_customers' => Customer::where('is_active', true)->count(),
                'total_outlets' => Outlet::count(),
                'active_outlets' => Outlet::where('is_active', true)->count(),
                'today_transactions' => \App\Models\PosTransaction::whereDate('transaction_date', today())->count(),
                'today_revenue' => \App\Models\PosTransaction::whereDate('transaction_date', today())
                    ->sum('net_amount'),
            ];
        });
    }

    /**
     * Clear all POS caches
     */
    public static function clearAll()
    {
        $keys = [
            'pos_category_options',
            'pos_outlet_options',
            'pos_customer_options',
            'pos_user_options_jabatan',
            'pos_product_options',
            'pos_dashboard_stats',
        ];

        foreach ($keys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * Clear specific cache
     */
    public static function clear($key)
    {
        Cache::forget("pos_{$key}");
    }

    /**
     * Clear category related caches
     */
    public static function clearCategoryCache()
    {
        Cache::forget('pos_category_options');
        Cache::forget('pos_product_options'); // Products include category info
    }

    /**
     * Clear outlet related caches
     */
    public static function clearOutletCache()
    {
        Cache::forget('pos_outlet_options');
        Cache::forget('pos_dashboard_stats');
    }

    /**
     * Clear customer related caches
     */
    public static function clearCustomerCache()
    {
        Cache::forget('pos_customer_options');
        Cache::forget('pos_dashboard_stats');
    }

    /**
     * Clear product related caches
     */
    public static function clearProductCache()
    {
        Cache::forget('pos_product_options');
        Cache::forget('pos_dashboard_stats');
    }

    /**
     * Clear user related caches
     */
    public static function clearUserCache()
    {
        Cache::forget('pos_user_options_jabatan');
    }

    /**
     * Get cached role options
     */
    public static function getRoleOptions()
    {
        return [
            'kepala toko' => 'Kepala Toko',
            'kasir' => 'Kasir',
            'supervisor' => 'Supervisor',
            'staff' => 'Staff',
            'admin' => 'Admin',
        ];
    }

    /**
     * Get cached payment method options
     */
    public static function getPaymentMethodOptions()
    {
        return [
            'cash' => 'Cash',
            'card' => 'Card',
            'transfer' => 'Bank Transfer',
            'e_wallet' => 'E-Wallet',
            'qris' => 'QRIS',
        ];
    }

    /**
     * Get cached transaction status options
     */
    public static function getTransactionStatusOptions()
    {
        return [
            'pending' => 'Pending',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            'refunded' => 'Refunded',
        ];
    }
}
