<?php

namespace App\Services;

use App\Models\PostingRule;
use App\Models\Journal;
use App\Models\PostingRuleCondition;
use App\Models\PostingRuleMapping;
use App\Models\PostingRuleLog;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PostingRuleEngine
{
    protected $sourceModel;
    protected $sourceData;
    protected $executedBy;

    public function __construct($sourceModel = null, $executedBy = null)
    {
        $this->sourceModel = $sourceModel;
        $this->sourceData = $sourceModel ? $sourceModel->toArray() : [];
        $this->executedBy = $executedBy ?: (auth()->id() ?? 1);
    }

    /**
     * Execute posting rules for a given source model
     */
    public function execute($sourceModel, $executedBy = null)
    {
        $this->sourceModel = $sourceModel;
        // Include accessors in source data
        $this->sourceData = array_merge($sourceModel->toArray(), $sourceModel->getAttributes());
        $this->executedBy = $executedBy ?: (auth()->id() ?? 1);

        $sourceType = get_class($sourceModel);
        $sourceId = $sourceModel->id;

        try {
            // Get applicable posting rules
            $postingRules = $this->getApplicablePostingRules($sourceType);

            $journalEntries = [];

            foreach ($postingRules as $rule) {
                try {
                    // Check if conditions are met
                    if ($this->evaluateConditions($rule)) {
                        // Create journal entry
                        $journalEntry = $this->createJournalEntry($rule);

                        if ($journalEntry) {
                            $journalEntries[] = $journalEntry;

                            // Log successful execution
                            $this->logExecution($rule, 'executed', null, $journalEntry->id);
                        }
                    } else {
                        // Log skipped execution
                        $this->logExecution($rule, 'skipped', 'Conditions not met');
                    }
                } catch (\Exception $e) {
                    // Log failed execution
                    $this->logExecution($rule, 'failed', $e->getMessage());

                    // Continue with other rules
                    Log::error("Posting rule execution failed", [
                        'rule_id' => $rule->id,
                        'source_type' => $sourceType,
                        'source_id' => $sourceId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            return $journalEntries;
        } catch (\Exception $e) {
            Log::error("Posting rule engine execution failed", [
                'source_type' => $sourceType,
                'source_id' => $sourceId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Get applicable posting rules for source type
     */
    protected function getApplicablePostingRules($sourceType)
    {
        return PostingRule::where('source_type', $sourceType)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('effective_date')
                    ->orWhere('effective_date', '<=', now());
            })
            ->where(function ($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            })
            ->orderBy('priority', 'asc')
            ->get();
    }

    /**
     * Evaluate conditions for a posting rule
     */
    protected function evaluateConditions($rule)
    {
        if (!$rule->is_conditional) {
            return true;
        }

        $conditions = PostingRuleCondition::where('posting_rule_id', $rule->id)
            ->orderBy('group_number')
            ->get();

        if ($conditions->isEmpty()) {
            return true;
        }

        // Group conditions by group_number
        $conditionGroups = $conditions->groupBy('group_number');
        $groupResults = [];

        foreach ($conditionGroups as $groupConditions) {
            $groupResult = $this->evaluateConditionGroup($groupConditions);
            $groupResults[] = $groupResult;
        }

        // All groups must be true (AND logic between groups)
        return !in_array(false, $groupResults);
    }

    /**
     * Evaluate a group of conditions
     */
    protected function evaluateConditionGroup($conditions)
    {
        $results = [];

        foreach ($conditions as $condition) {
            $fieldValue = $this->getFieldValue($condition->field_name);
            $conditionValue = json_decode($condition->value, true) ?? $condition->value;

            $result = $this->evaluateCondition($fieldValue, $condition->operator, $conditionValue);
            $results[] = [
                'result' => $result,
                'operator' => $condition->logical_operator
            ];
        }

        // Evaluate logical operators within group
        return $this->evaluateLogicalOperators($results);
    }

    /**
     * Get field value from source data
     */
    protected function getFieldValue($fieldName)
    {
        // First try to get from model directly (includes accessors)
        if ($this->sourceModel && property_exists($this->sourceModel, $fieldName)) {
            return $this->sourceModel->$fieldName;
        }

        // Try accessor method
        if ($this->sourceModel && method_exists($this->sourceModel, 'getAttribute')) {
            try {
                $value = $this->sourceModel->getAttribute($fieldName);
                if ($value !== null) {
                    Log::info("Field value found via accessor", ['field' => $fieldName, 'value' => $value]);
                    return $value;
                }
            } catch (\Exception) {
                // Continue to array lookup
            }
        }

        // Support dot notation for nested fields in array data
        $keys = explode('.', $fieldName);
        $value = $this->sourceData;

        Log::info("Getting field value from array", [
            'field_name' => $fieldName,
            'source_data_keys' => array_keys($this->sourceData),
            'looking_for' => $keys
        ]);

        foreach ($keys as $key) {
            if (is_array($value) && isset($value[$key])) {
                $value = $value[$key];
            } else {
                Log::warning("Field not found", ['field' => $fieldName, 'key' => $key, 'available_keys' => is_array($value) ? array_keys($value) : 'not array']);
                return null;
            }
        }

        Log::info("Field value found from array", ['field' => $fieldName, 'value' => $value]);
        return $value;
    }

    /**
     * Evaluate single condition
     */
    protected function evaluateCondition($fieldValue, $operator, $conditionValue)
    {
        switch ($operator) {
            case '=':
            case 'equals':
                return $fieldValue == $conditionValue;

            case '!=':
            case 'not_equals':
                return $fieldValue != $conditionValue;

            case '>':
            case 'greater_than':
                return $fieldValue > $conditionValue;

            case '>=':
            case 'greater_than_or_equal':
                return $fieldValue >= $conditionValue;

            case '<':
            case 'less_than':
                return $fieldValue < $conditionValue;

            case '<=':
            case 'less_than_or_equal':
                return $fieldValue <= $conditionValue;

            case 'in':
                return in_array($fieldValue, (array) $conditionValue);

            case 'not_in':
                return !in_array($fieldValue, (array) $conditionValue);

            case 'between':
                if (is_array($conditionValue) && count($conditionValue) >= 2) {
                    return $fieldValue >= $conditionValue[0] && $fieldValue <= $conditionValue[1];
                }
                return false;

            case 'contains':
                return str_contains((string) $fieldValue, (string) $conditionValue);

            case 'starts_with':
                return str_starts_with((string) $fieldValue, (string) $conditionValue);

            case 'ends_with':
                return str_ends_with((string) $fieldValue, (string) $conditionValue);

            case 'is_null':
                return is_null($fieldValue);

            case 'is_not_null':
                return !is_null($fieldValue);

            default:
                return false;
        }
    }

    /**
     * Evaluate logical operators
     */
    protected function evaluateLogicalOperators($results)
    {
        if (empty($results)) {
            return true;
        }

        $finalResult = $results[0]['result'];

        for ($i = 1; $i < count($results); $i++) {
            $operator = $results[$i - 1]['operator'] ?? 'AND';
            $currentResult = $results[$i]['result'];

            if ($operator === 'OR') {
                $finalResult = $finalResult || $currentResult;
            } else { // AND
                $finalResult = $finalResult && $currentResult;
            }
        }

        return $finalResult;
    }

    /**
     * Create journal entry based on posting rule
     */
    protected function createJournalEntry($rule)
    {
        DB::beginTransaction();

        try {
            // Create journal entry header
            $journalEntry = Journal::create([
                'transaction_date' => $this->getJournalDate(),
                'source_type' => get_class($this->sourceModel),
                'source_id' => $this->sourceModel->id,
                'reference_number' => $this->getSourceNumber(),
                'posting_rule_id' => $rule->id,
                'description' => $this->generateDescription($rule),
                'status' => 'Draft',
                'created_by' => $this->executedBy,
            ]);

            // Create journal entry details based on mappings
            $mappings = PostingRuleMapping::where('posting_rule_id', $rule->id)
                ->orderBy('sequence')
                ->get();

            foreach ($mappings as $mapping) {
                $this->createJournalEntryDetail($journalEntry, $mapping);
            }

            // Validate that journal is balanced
            if (!$journalEntry->isBalanced()) {
                $totalDebit = $journalEntry->getTotalDebitAttribute();
                $totalCredit = $journalEntry->getTotalCreditAttribute();
                throw new \Exception("Journal entry is not balanced. Debit: {$totalDebit}, Credit: {$totalCredit}");
            }

            DB::commit();
            return $journalEntry;
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Create journal entry detail based on mapping
     */
    protected function createJournalEntryDetail($journalEntry, $mapping)
    {
        // Get account
        $accountId = $this->getAccountId($mapping);
        if (!$accountId) {
            throw new \Exception("Could not determine account for mapping: {$mapping->id}");
        }

        // Get amount
        $amount = $this->getAmount($mapping);
        Log::info("Creating journal detail", [
            'mapping_id' => $mapping->id,
            'account_id' => $accountId,
            'amount' => $amount,
            'amount_source' => $mapping->amount_source,
            'amount_field' => $mapping->amount_field
        ]);

        if ($amount <= 0) {
            Log::warning("Skipping zero amount for mapping", ['mapping_id' => $mapping->id, 'amount' => $amount]);
            return; // Skip zero amounts
        }

        // Create detail
        $debitAmount = $mapping->mapping_type === 'debit' ? $amount : 0;
        $creditAmount = $mapping->mapping_type === 'credit' ? $amount : 0;
        $description = $this->generateDetailDescription($mapping);

        // Create journal entry detail
        $sortOrder = $journalEntry->journalEntries()->max('sort_order') + 1;

        $journalEntry->journalEntries()->create([
            'account_id' => $accountId,
            'debit' => $debitAmount,
            'credit' => $creditAmount,
            'description' => $description,
            'sort_order' => $sortOrder,
        ]);
    }

    /**
     * Get account ID based on mapping configuration
     */
    protected function getAccountId($mapping)
    {
        switch ($mapping->account_source) {
            case 'fixed':
                return $mapping->account_id;

            case 'field':
                return $this->getFieldValue($mapping->account_field);

            case 'lookup':
                // Implement lookup logic based on business rules
                return $this->lookupAccount($mapping);

            default:
                return null;
        }
    }

    /**
     * Get amount based on mapping configuration
     */
    protected function getAmount($mapping)
    {
        switch ($mapping->amount_source) {
            case 'fixed':
                return $mapping->fixed_amount ?? 0;

            case 'field':
                return $this->getFieldValue($mapping->amount_field) ?? 0;

            case 'calculation':
                return $this->calculateAmount($mapping->calculation_formula);

            default:
                return 0;
        }
    }

    /**
     * Calculate amount using formula
     */
    protected function calculateAmount($formula)
    {
        if (!$formula) return 0;

        // Replace field references with actual values
        $evaluatedFormula = $formula;

        // Find all field references in format {field_name}
        preg_match_all('/\{([^}]+)\}/', $formula, $matches);

        foreach ($matches[1] as $fieldName) {
            $fieldValue = $this->getFieldValue($fieldName) ?? 0;
            $evaluatedFormula = str_replace('{' . $fieldName . '}', $fieldValue, $evaluatedFormula);
        }

        // Basic math evaluation (be careful with eval in production!)
        try {
            if (preg_match('/^[0-9+\-*\/\(\)\.\s]+$/', $evaluatedFormula)) {
                return eval("return $evaluatedFormula;");
            }
        } catch (\Exception $e) {
            Log::error("Formula evaluation failed", [
                'formula' => $formula,
                'evaluated' => $evaluatedFormula,
                'error' => $e->getMessage()
            ]);
        }

        return 0;
    }

    /**
     * Lookup account based on business rules
     */
    protected function lookupAccount($mapping)
    {
        // Implement specific lookup logic based on your business requirements
        // This is a placeholder for custom account lookup logic
        return $mapping->account_id;
    }

    /**
     * Generate journal description
     */
    protected function generateDescription($rule)
    {
        $template = $rule->description ?? 'Auto-generated journal entry';

        // Replace placeholders with actual values
        return $this->replacePlaceholders($template);
    }

    /**
     * Generate detail description
     */
    protected function generateDetailDescription($mapping)
    {
        $template = $mapping->description_template ?? '';

        return $this->replacePlaceholders($template);
    }

    /**
     * Replace placeholders in templates
     */
    protected function replacePlaceholders($template)
    {
        // Find all placeholders in format {field_name}
        preg_match_all('/\{([^}]+)\}/', $template, $matches);

        foreach ($matches[1] as $fieldName) {
            $fieldValue = $this->getFieldValue($fieldName) ?? '';
            $template = str_replace('{' . $fieldName . '}', $fieldValue, $template);
        }

        return $template;
    }

    /**
     * Get journal date
     */
    protected function getJournalDate()
    {
        // Try to get date from source model
        $dateFields = ['journal_date', 'transaction_date', 'date', 'created_at'];

        foreach ($dateFields as $field) {
            $date = $this->getFieldValue($field);
            if ($date) {
                return Carbon::parse($date)->toDateString();
            }
        }

        return Carbon::now()->toDateString();
    }

    /**
     * Get source number
     */
    protected function getSourceNumber()
    {
        $numberFields = ['number', 'document_number', 'transaction_number', 'id'];

        foreach ($numberFields as $field) {
            $number = $this->getFieldValue($field);
            if ($number) {
                return $number;
            }
        }

        return null;
    }

    /**
     * Log posting rule execution
     */
    protected function logExecution($rule, $action, $errorMessage = null, $journalEntryId = null)
    {
        PostingRuleLog::create([
            'posting_rule_id' => $rule->id,
            'source_type' => get_class($this->sourceModel),
            'source_id' => $this->sourceModel->id,
            'action' => $action,
            'source_data' => $this->sourceData,
            'conditions_result' => $rule->is_conditional ? $this->getConditionsResult($rule) : null,
            'journal_id' => $journalEntryId,
            'error_message' => $errorMessage,
            'executed_at' => Carbon::now(),
            'executed_by' => $this->executedBy,
        ]);
    }

    /**
     * Get conditions evaluation result
     */
    protected function getConditionsResult($rule = null)
    {
        // This would contain detailed condition evaluation results
        // For now, return basic info
        return [
            'evaluated' => true,
            'timestamp' => Carbon::now()->toISOString()
        ];
    }
}
