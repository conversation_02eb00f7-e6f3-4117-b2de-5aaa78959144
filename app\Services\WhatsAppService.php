<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class WhatsAppService
{
    protected string $apiUrl;
    protected string $apiToken;
    protected bool $enabled;

    public function __construct()
    {
        $this->apiUrl = config('services.starsender.api_url');
        $this->apiToken = config('services.starsender.api_token');
        $this->enabled = config('services.starsender.enabled', true);
    }

    /**
     * Send a WhatsApp message
     *
     * @param string $phoneNumber Phone number (e.g., 08123456789 or 6281234567890)
     * @param string $message Message content
     * @param array $options Additional options (file, delay, schedule)
     * @return array Response from API
     * @throws Exception
     */
    public function sendMessage(string $phoneNumber, string $message, array $options = []): array
    {
        if (!$this->enabled) {
            Log::info('WhatsApp service is disabled');
            return ['success' => false, 'message' => 'WhatsApp service is disabled'];
        }

        if (empty($this->apiToken)) {
            throw new Exception('Starsender API token is not configured');
        }

        // Format phone number for <PERSON>ender (keep original format, don't add country code)
        $phoneNumber = $this->formatPhoneNumber($phoneNumber);

        $payload = [
            'messageType' => 'text',
            'to' => $phoneNumber,
            'body' => $message,
        ];

        // Add file if provided
        if (isset($options['file_url'])) {
            $payload['file'] = $options['file_url'];
        }

        // Add delay if provided (in seconds)
        if (isset($options['delay'])) {
            $payload['delay'] = (int) $options['delay'];
        }

        // Add schedule if provided (timestamp in milliseconds)
        if (isset($options['schedule'])) {
            $payload['schedule'] = (int) $options['schedule'];
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => $this->apiToken,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/api/send', $payload);

            $responseData = $response->json();

            if ($response->successful()) {
                Log::info('WhatsApp message sent successfully', [
                    'phone' => $phoneNumber,
                    'message_length' => strlen($message),
                    'response' => $responseData
                ]);

                return [
                    'success' => true,
                    'data' => $responseData,
                    'message' => 'Message sent successfully'
                ];
            } else {
                Log::error('Failed to send WhatsApp message', [
                    'phone' => $phoneNumber,
                    'status' => $response->status(),
                    'response' => $responseData
                ]);

                return [
                    'success' => false,
                    'error' => $responseData['message'] ?? 'Unknown error',
                    'status_code' => $response->status()
                ];
            }
        } catch (Exception $e) {
            Log::error('WhatsApp service error', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);

            throw new Exception('Failed to send WhatsApp message: ' . $e->getMessage());
        }
    }

    /**
     * Send a WhatsApp message with media
     *
     * @param string $phoneNumber
     * @param string $fileUrl
     * @param string $caption
     * @return array
     * @throws Exception
     */
    public function sendMediaMessage(string $phoneNumber, string $fileUrl, string $caption = ''): array
    {
        return $this->sendMessage($phoneNumber, $caption, [
            'file_url' => $fileUrl
        ]);
    }

    /**
     * Send bulk WhatsApp messages
     *
     * @param array $recipients Array of ['phone' => '...', 'message' => '...', 'options' => [...]]
     * @return array Results for each recipient
     */
    public function sendBulkMessages(array $recipients): array
    {
        $results = [];

        foreach ($recipients as $recipient) {
            try {
                $result = $this->sendMessage(
                    $recipient['phone'],
                    $recipient['message'],
                    $recipient['options'] ?? []
                );
                $results[] = array_merge($result, ['phone' => $recipient['phone']]);
            } catch (Exception $e) {
                $results[] = [
                    'success' => false,
                    'phone' => $recipient['phone'],
                    'error' => $e->getMessage()
                ];
            }

            // Add delay between messages to avoid rate limiting
            usleep(1000000); // 1 second delay
        }

        return $results;
    }

    /**
     * Send scheduled WhatsApp message
     *
     * @param string $phoneNumber
     * @param string $message
     * @param int $scheduleTimestamp Timestamp in milliseconds
     * @param array $options
     * @return array
     * @throws Exception
     */
    public function sendScheduledMessage(string $phoneNumber, string $message, int $scheduleTimestamp, array $options = []): array
    {
        $options['schedule'] = $scheduleTimestamp;
        return $this->sendMessage($phoneNumber, $message, $options);
    }

    /**
     * Send delayed WhatsApp message
     *
     * @param string $phoneNumber
     * @param string $message
     * @param int $delaySeconds Delay in seconds
     * @param array $options
     * @return array
     * @throws Exception
     */
    public function sendDelayedMessage(string $phoneNumber, string $message, int $delaySeconds, array $options = []): array
    {
        $options['delay'] = $delaySeconds;
        return $this->sendMessage($phoneNumber, $message, $options);
    }

    /**
     * Format phone number for Starsender API
     * Starsender accepts various formats: 08123456789, 6281234567890, +6281234567890
     *
     * @param string $phoneNumber
     * @return string
     */
    protected function formatPhoneNumber(string $phoneNumber): string
    {
        // Remove all non-numeric characters except +
        $cleaned = preg_replace('/[^0-9+]/', '', $phoneNumber);

        // Remove + if present
        $cleaned = ltrim($cleaned, '+');

        // If starts with 62, keep as is
        if (substr($cleaned, 0, 2) === '62') {
            return $cleaned;
        }

        // If starts with 0, keep as is (Indonesian local format)
        if (substr($cleaned, 0, 1) === '0') {
            return $cleaned;
        }

        // If doesn't start with 0 or 62, assume it's local number without 0
        return '0' . $cleaned;
    }

    /**
     * Validate phone number format
     *
     * @param string $phoneNumber
     * @return bool
     */
    protected function isValidPhoneNumber(string $phoneNumber): bool
    {
        // Accept Indonesian formats: 08xxx, 62xxx, or international +62xxx
        return preg_match('/^(0[0-9]{8,13}|62[0-9]{8,13}|\+62[0-9]{8,13})$/', $phoneNumber);
    }

    /**
     * Check if service is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->enabled && !empty($this->apiToken);
    }

    /**
     * Get service status
     *
     * @return array
     */
    public function getStatus(): array
    {
        return [
            'enabled' => $this->enabled,
            'configured' => !empty($this->apiToken),
            'api_url' => $this->apiUrl,
        ];
    }
}
