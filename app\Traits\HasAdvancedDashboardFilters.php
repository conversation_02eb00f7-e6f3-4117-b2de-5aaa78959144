<?php

namespace App\Traits;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Carbon\Carbon;
use Livewire\Attributes\On;
use App\Models\CompanySettings;

trait HasAdvancedDashboardFilters
{
    public $filters = [
        'period_type' => 'current_work_period',
        'start_date' => null,
        'end_date' => null,
        'work_period' => null,
        'quick_filter' => null,
    ];

    public function updatedFilters()
    {
        $this->updateDateFilters($this->filters['period_type']);
        // Store filters in session for widgets to access
        session(['dashboard_filters' => $this->filters]);
        // Dispatch event to widgets
        $this->dispatch('filtersUpdated', $this->filters);
    }

    public function mount(): void
    {
        // Initialize with current payroll period
        $this->initializeDefaultFilters();

        // Store filters in session
        session(['dashboard_filters' => $this->filters]);

        // Call parent mount if it exists
        if (method_exists(parent::class, 'mount')) {
            parent::mount();
        }
    }

    #[On('updateCharts')]
    public function refreshWidgets()
    {
        $this->dispatch('$refresh');
    }

    public function dashboardFiltersForm(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('filters.period_type')
                    ->label('Periode')
                    ->options([
                        'current_work_period' => 'Periode Kerja Saat Ini',
                        'last_work_period' => 'Periode Kerja Sebelumnya',
                        'this_month' => 'Bulan Ini (Kalender)',
                        'last_month' => 'Bulan Lalu (Kalender)',
                        'this_week' => 'Minggu Ini',
                        'last_week' => 'Minggu Lalu',
                        'today' => 'Hari Ini',
                        'yesterday' => 'Kemarin',
                        'work_period' => 'Pilih Periode Kerja',
                        'custom' => 'Rentang Tanggal Custom',
                    ])
                    ->default('current_work_period')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->updateDateFilters($state);
                    }),

                Select::make('filters.work_period')
                    ->label('Periode Kerja')
                    ->options(fn() => CompanySettings::getAvailableWorkPeriods())
                    ->visible(fn($get) => $get('filters.period_type') === 'work_period')
                    ->live()
                    ->afterStateUpdated(fn() => $this->updateWorkPeriodFilter()),

                DatePicker::make('filters.start_date')
                    ->label('Tanggal Mulai')
                    ->visible(fn($get) => $get('filters.period_type') === 'custom')
                    ->live()
                    ->afterStateUpdated(fn() => $this->dispatch('updateCharts')),

                DatePicker::make('filters.end_date')
                    ->label('Tanggal Selesai')
                    ->visible(fn($get) => $get('filters.period_type') === 'custom')
                    ->live()
                    ->afterStateUpdated(fn() => $this->dispatch('updateCharts')),
            ])
            ->columns(4)
            ->statePath('filters');
    }

    protected function initializeDefaultFilters()
    {
        $this->filters['period_type'] = 'current_work_period';
        $this->updateDateFilters('current_work_period');
    }

    protected function updateDateFilters($periodType)
    {
        switch ($periodType) {
            case 'current_work_period':
                $this->filters['start_date'] = CompanySettings::getCurrentWorkPeriodStartDate();
                $this->filters['end_date'] = CompanySettings::getCurrentWorkPeriodEndDate();
                break;

            case 'last_work_period':
                $currentStart = CompanySettings::getCurrentWorkPeriodStartDate();
                $this->filters['start_date'] = $currentStart->copy()->subMonth();
                $this->filters['end_date'] = $currentStart->copy()->subDay();
                break;

            case 'this_month':
                $this->filters['start_date'] = now()->startOfMonth();
                $this->filters['end_date'] = now()->endOfMonth();
                break;

            case 'last_month':
                $this->filters['start_date'] = now()->subMonth()->startOfMonth();
                $this->filters['end_date'] = now()->subMonth()->endOfMonth();
                break;

            case 'this_week':
                $this->filters['start_date'] = now()->startOfWeek();
                $this->filters['end_date'] = now()->endOfWeek();
                break;

            case 'last_week':
                $this->filters['start_date'] = now()->subWeek()->startOfWeek();
                $this->filters['end_date'] = now()->subWeek()->endOfWeek();
                break;

            case 'today':
                $this->filters['start_date'] = now()->startOfDay();
                $this->filters['end_date'] = now()->endOfDay();
                break;

            case 'yesterday':
                $this->filters['start_date'] = now()->subDay()->startOfDay();
                $this->filters['end_date'] = now()->subDay()->endOfDay();
                break;
        }

        $this->dispatch('updateCharts');
    }

    protected function updateWorkPeriodFilter()
    {
        if ($this->filters['work_period']) {
            [$year, $month] = explode('-', $this->filters['work_period']);
            $period = CompanySettings::getWorkPeriod((int)$month, (int)$year);

            $this->filters['start_date'] = $period['start'];
            $this->filters['end_date'] = $period['end'];

            $this->dispatch('updateCharts');
        }
    }

    public function getFilteredDateRange(): array
    {
        return [
            'start' => $this->filters['start_date'] ? Carbon::parse($this->filters['start_date']) : CompanySettings::getCurrentWorkPeriodStartDate(),
            'end' => $this->filters['end_date'] ? Carbon::parse($this->filters['end_date']) : CompanySettings::getCurrentWorkPeriodEndDate(),
        ];
    }

    public function getWidgetData(): array
    {
        return [
            'filters' => $this->filters,
            'dateRange' => $this->getFilteredDateRange(),
        ];
    }

    public function getFilterLabel(): string
    {
        $dateRange = $this->getFilteredDateRange();
        $workPeriodInfo = CompanySettings::getWorkPeriodInfo();

        // Show work period info for work period-based filters
        if (in_array($this->filters['period_type'], ['current_work_period', 'last_work_period', 'work_period'])) {
            $label = $dateRange['start']->format('d M Y') . ' - ' . $dateRange['end']->format('d M Y');
            $label .= ' (Periode: ' . $workPeriodInfo['start_date'] . '-' . $workPeriodInfo['end_date'] . ')';
            return $label;
        }

        // Regular date range
        if ($dateRange['start']->isSameDay($dateRange['end'])) {
            return $dateRange['start']->format('d M Y');
        }

        if ($dateRange['start']->isSameMonth($dateRange['end'])) {
            return $dateRange['start']->format('M Y');
        }

        return $dateRange['start']->format('d M Y') . ' - ' . $dateRange['end']->format('d M Y');
    }

    public function getCurrentWorkPeriodInfo(): array
    {
        return CompanySettings::getWorkPeriodInfo();
    }
}
