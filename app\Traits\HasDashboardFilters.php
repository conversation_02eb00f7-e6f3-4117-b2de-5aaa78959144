<?php

namespace App\Traits;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Carbon\Carbon;
use Livewire\Attributes\On;

trait HasDashboardFilters
{
    public $filters = [
        'date_range' => 'this_month',
        'start_date' => null,
        'end_date' => null,
        'month' => 6, // Default to current month
        'year' => 2025, // Default to current year
    ];

    public function updatedFilters()
    {
        $this->updateDateFilters($this->filters['date_range']);
        // Store filters in session for widgets to access
        session(['dashboard_filters' => $this->filters]);
        // Dispatch event to widgets
        $this->dispatch('filtersUpdated', $this->filters);
    }

    public function mount(): void
    {
        // Initialize session with default filters
        session(['dashboard_filters' => $this->filters]);
        // Initialize default filters
        $this->updateDateFilters($this->filters['date_range']);

        // Call parent mount if it exists
        if (method_exists(parent::class, 'mount')) {
            parent::mount();
        }
    }

    #[On('updateCharts')]
    public function refreshWidgets()
    {
        $this->dispatch('$refresh');
    }

    public function dashboardFiltersForm(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('filters.date_range')
                    ->label('Periode')
                    ->options([
                        'today' => 'Hari Ini',
                        'yesterday' => 'Kemarin',
                        'this_week' => 'Minggu Ini',
                        'last_week' => 'Minggu Lalu',
                        'this_month' => 'Bulan Ini',
                        'last_month' => 'Bulan Lalu',
                        'this_quarter' => 'Kuartal Ini',
                        'last_quarter' => 'Kuartal Lalu',
                        'this_year' => 'Tahun Ini',
                        'last_year' => 'Tahun Lalu',
                        'custom' => 'Custom Range',
                        'monthly' => 'Per Bulan',
                    ])
                    ->default('this_month')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->updateDateFilters($state);
                    }),

                DatePicker::make('filters.start_date')
                    ->label('Tanggal Mulai')
                    ->visible(fn($get) => $get('filters.date_range') === 'custom')
                    ->live()
                    ->afterStateUpdated(fn() => $this->dispatch('updateCharts')),

                DatePicker::make('filters.end_date')
                    ->label('Tanggal Selesai')
                    ->visible(fn($get) => $get('filters.date_range') === 'custom')
                    ->live()
                    ->afterStateUpdated(fn() => $this->dispatch('updateCharts')),

                Select::make('filters.month')
                    ->label('Bulan')
                    ->options([
                        1 => 'Januari',
                        2 => 'Februari',
                        3 => 'Maret',
                        4 => 'April',
                        5 => 'Mei',
                        6 => 'Juni',
                        7 => 'Juli',
                        8 => 'Agustus',
                        9 => 'September',
                        10 => 'Oktober',
                        11 => 'November',
                        12 => 'Desember',
                    ])
                    ->visible(fn($get) => $get('filters.date_range') === 'monthly')
                    ->default(now()->month)
                    ->live()
                    ->afterStateUpdated(fn() => $this->dispatch('updateCharts')),

                Select::make('filters.year')
                    ->label('Tahun')
                    ->options(function () {
                        $years = [];
                        for ($year = 2020; $year <= now()->year + 1; $year++) {
                            $years[$year] = $year;
                        }
                        return $years;
                    })
                    ->visible(fn($get) => $get('filters.date_range') === 'monthly')
                    ->default(now()->year)
                    ->live()
                    ->afterStateUpdated(fn() => $this->dispatch('updateCharts')),
            ])
            ->columns(4)
            ->statePath('filters');
    }

    protected function updateDateFilters($range)
    {
        switch ($range) {
            case 'today':
                $this->filters['start_date'] = now()->startOfDay();
                $this->filters['end_date'] = now()->endOfDay();
                break;
            case 'yesterday':
                $this->filters['start_date'] = now()->subDay()->startOfDay();
                $this->filters['end_date'] = now()->subDay()->endOfDay();
                break;
            case 'this_week':
                $this->filters['start_date'] = now()->startOfWeek();
                $this->filters['end_date'] = now()->endOfWeek();
                break;
            case 'last_week':
                $this->filters['start_date'] = now()->subWeek()->startOfWeek();
                $this->filters['end_date'] = now()->subWeek()->endOfWeek();
                break;
            case 'this_month':
                $this->filters['start_date'] = now()->startOfMonth();
                $this->filters['end_date'] = now()->endOfMonth();
                break;
            case 'last_month':
                $this->filters['start_date'] = now()->subMonth()->startOfMonth();
                $this->filters['end_date'] = now()->subMonth()->endOfMonth();
                break;
            case 'this_quarter':
                $this->filters['start_date'] = now()->startOfQuarter();
                $this->filters['end_date'] = now()->endOfQuarter();
                break;
            case 'last_quarter':
                $this->filters['start_date'] = now()->subQuarter()->startOfQuarter();
                $this->filters['end_date'] = now()->subQuarter()->endOfQuarter();
                break;
            case 'this_year':
                $this->filters['start_date'] = now()->startOfYear();
                $this->filters['end_date'] = now()->endOfYear();
                break;
            case 'last_year':
                $this->filters['start_date'] = now()->subYear()->startOfYear();
                $this->filters['end_date'] = now()->subYear()->endOfYear();
                break;
            case 'monthly':
                $month = $this->filters['month'] ?? now()->month;
                $year = $this->filters['year'] ?? now()->year;
                $this->filters['start_date'] = Carbon::create($year, $month, 1)->startOfMonth();
                $this->filters['end_date'] = Carbon::create($year, $month, 1)->endOfMonth();
                break;
        }

        $this->dispatch('updateCharts');
    }

    public function getFilteredDateRange(): array
    {
        if ($this->filters['date_range'] === 'custom') {
            return [
                'start' => $this->filters['start_date'] ? Carbon::parse($this->filters['start_date']) : now()->startOfMonth(),
                'end' => $this->filters['end_date'] ? Carbon::parse($this->filters['end_date']) : now()->endOfMonth(),
            ];
        }

        if ($this->filters['date_range'] === 'monthly') {
            $month = $this->filters['month'] ?? now()->month;
            $year = $this->filters['year'] ?? now()->year;
            return [
                'start' => Carbon::create($year, $month, 1)->startOfMonth(),
                'end' => Carbon::create($year, $month, 1)->endOfMonth(),
            ];
        }

        return [
            'start' => $this->filters['start_date'] ? Carbon::parse($this->filters['start_date']) : now()->startOfMonth(),
            'end' => $this->filters['end_date'] ? Carbon::parse($this->filters['end_date']) : now()->endOfMonth(),
        ];
    }



    public function getWidgetData(): array
    {
        return [
            'filters' => $this->filters,
            'dateRange' => $this->getFilteredDateRange(),
        ];
    }

    public function getFilterLabel(): string
    {
        $dateRange = $this->getFilteredDateRange();

        if ($dateRange['start']->isSameDay($dateRange['end'])) {
            return $dateRange['start']->format('d M Y');
        }

        if ($dateRange['start']->isSameMonth($dateRange['end'])) {
            return $dateRange['start']->format('M Y');
        }

        return $dateRange['start']->format('d M Y') . ' - ' . $dateRange['end']->format('d M Y');
    }
}
