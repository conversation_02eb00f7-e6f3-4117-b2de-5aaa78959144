<?php

namespace App\Traits;

use Carbon\Carbon;
use Livewire\Attributes\On;

trait HasDateRangeFilters
{
    public $filters = [];
    public $dateRange = [];

    public function mount(): void
    {
        // Initialize filters from session or defaults
        $this->filters = session('dashboard_filters', [
            'period_type' => 'this_month',
            'start_date' => null,
            'end_date' => null,
        ]);

        // Calculate initial date range
        $this->updateDateRange();

        // Call parent mount if it exists
        if (method_exists(parent::class, 'mount')) {
            parent::mount();
        }
    }

    #[On('filtersUpdated')]
    public function updateFilters($filters): void
    {
        $this->filters = $filters;
        $this->updateDateRange();
        $this->dispatch('$refresh');
    }

    #[On('updateCharts')]
    public function refreshWidget(): void
    {
        $this->filters = session('dashboard_filters', $this->filters);
        $this->updateDateRange();
        $this->dispatch('$refresh');
    }

    protected function updateDateRange(): void
    {
        $this->dateRange = $this->getFilteredDateRange();
    }

    protected function getFilteredDateRange(): array
    {
        // If custom date range is set
        if (isset($this->filters['start_date']) && isset($this->filters['end_date']) 
            && $this->filters['start_date'] && $this->filters['end_date']) {
            return [
                'start' => Carbon::parse($this->filters['start_date']),
                'end' => Carbon::parse($this->filters['end_date']),
            ];
        }

        // Handle different period types
        $periodType = $this->filters['period_type'] ?? 'this_month';

        switch ($periodType) {
            case 'this_month':
                return [
                    'start' => now()->startOfMonth(),
                    'end' => now()->endOfMonth(),
                ];

            case 'last_month':
                return [
                    'start' => now()->subMonth()->startOfMonth(),
                    'end' => now()->subMonth()->endOfMonth(),
                ];

            case 'this_week':
                return [
                    'start' => now()->startOfWeek(),
                    'end' => now()->endOfWeek(),
                ];

            case 'last_week':
                return [
                    'start' => now()->subWeek()->startOfWeek(),
                    'end' => now()->subWeek()->endOfWeek(),
                ];

            case 'today':
                return [
                    'start' => now()->startOfDay(),
                    'end' => now()->endOfDay(),
                ];

            case 'yesterday':
                return [
                    'start' => now()->subDay()->startOfDay(),
                    'end' => now()->subDay()->endOfDay(),
                ];

            case 'this_year':
                return [
                    'start' => now()->startOfYear(),
                    'end' => now()->endOfYear(),
                ];

            case 'last_year':
                return [
                    'start' => now()->subYear()->startOfYear(),
                    'end' => now()->subYear()->endOfYear(),
                ];

            default:
                // Fallback to current month
                return [
                    'start' => now()->startOfMonth(),
                    'end' => now()->endOfMonth(),
                ];
        }
    }

    protected function getDateRangeForQuery(): array
    {
        if (empty($this->dateRange)) {
            $this->updateDateRange();
        }
        
        return [
            'start' => $this->dateRange['start']->format('Y-m-d'),
            'end' => $this->dateRange['end']->format('Y-m-d'),
        ];
    }

    protected function getDateRangeForQueryWithTime(): array
    {
        if (empty($this->dateRange)) {
            $this->updateDateRange();
        }
        
        return [
            'start' => $this->dateRange['start']->format('Y-m-d H:i:s'),
            'end' => $this->dateRange['end']->format('Y-m-d H:i:s'),
        ];
    }

    protected function getDateRangeLabel(): string
    {
        if (empty($this->dateRange)) {
            $this->updateDateRange();
        }
        
        if ($this->dateRange['start']->isSameDay($this->dateRange['end'])) {
            return $this->dateRange['start']->format('d M Y');
        }

        if ($this->dateRange['start']->isSameMonth($this->dateRange['end'])) {
            return $this->dateRange['start']->format('M Y');
        }

        return $this->dateRange['start']->format('d M Y') . ' - ' . $this->dateRange['end']->format('d M Y');
    }

    protected function isCurrentPeriod(): bool
    {
        if (empty($this->dateRange)) {
            $this->updateDateRange();
        }
        
        $now = now();
        return $now->between($this->dateRange['start'], $this->dateRange['end']);
    }

    protected function getDaysInRange(): int
    {
        if (empty($this->dateRange)) {
            $this->updateDateRange();
        }
        
        return $this->dateRange['start']->diffInDays($this->dateRange['end']) + 1;
    }

    protected function getWeeksInRange(): int
    {
        if (empty($this->dateRange)) {
            $this->updateDateRange();
        }
        
        return $this->dateRange['start']->diffInWeeks($this->dateRange['end']) + 1;
    }

    protected function getMonthsInRange(): int
    {
        if (empty($this->dateRange)) {
            $this->updateDateRange();
        }
        
        return $this->dateRange['start']->diffInMonths($this->dateRange['end']) + 1;
    }
}
