<?php

namespace App\Traits;

use Filament\Tables;
use Illuminate\Support\Str;
use Filament\Notifications\Notification;

trait HasExportActions
{
    public static function getExportActions(string $exportClass, string $modelName): array
    {
        $actions = [];

        // Excel Export - only if package is available
        if (class_exists('Maatwebsite\Excel\Facades\Excel')) {
            $actions[] = Tables\Actions\Action::make('export_excel')
                ->label('Export Excel')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->action(function () use ($exportClass, $modelName) {
                    try {
                        $fileName = Str::slug($modelName) . '_' . now()->format('Y-m-d_H-i-s') . '.xlsx';

                        return response()->streamDownload(function () use ($exportClass) {
                            echo \Maatwebsite\Excel\Facades\Excel::raw(new $exportClass, \Maatwebsite\Excel\Excel::XLSX);
                        }, $fileName);
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Export Excel Error')
                            ->body('Terjadi kesalahan saat export Excel: ' . $e->getMessage())
                            ->danger()
                            ->send();

                        return null;
                    }
                });
        } else {
            // Fallback CSV export if Excel package not available
            $actions[] = Tables\Actions\Action::make('export_csv')
                ->label('Export CSV')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->action(function () use ($exportClass, $modelName) {
                    try {
                        $fileName = Str::slug($modelName) . '_' . now()->format('Y-m-d_H-i-s') . '.csv';
                        $export = new $exportClass;
                        $data = $export->collection();
                        $headings = $export->headings();

                        return response()->streamDownload(function () use ($data, $headings, $export) {
                            $handle = fopen('php://output', 'w');

                            // Add BOM for UTF-8
                            fwrite($handle, "\xEF\xBB\xBF");

                            // Add headings
                            fputcsv($handle, $headings);

                            // Add data
                            foreach ($data as $row) {
                                // Check if export class has map method
                                if (method_exists($export, 'map')) {
                                    $mappedRow = $export->map($row);
                                    fputcsv($handle, $mappedRow);
                                } else {
                                    // Fallback to array conversion
                                    $rowArray = is_array($row) ? $row : $row->toArray();
                                    fputcsv($handle, $rowArray);
                                }
                            }

                            fclose($handle);
                        }, $fileName, [
                            'Content-Type' => 'text/csv',
                            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                        ]);
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Export CSV Error')
                            ->body('Terjadi kesalahan saat export CSV: ' . $e->getMessage())
                            ->danger()
                            ->send();

                        return null;
                    }
                });
        }

        // PDF Export - only if package is available
        if (class_exists('Barryvdh\DomPDF\Facade\Pdf')) {
            $actions[] = Tables\Actions\Action::make('export_pdf')
                ->label('Export PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->color('danger')
                ->action(function () use ($exportClass, $modelName) {
                    try {
                        $fileName = Str::slug($modelName) . '_' . now()->format('Y-m-d_H-i-s') . '.pdf';
                        $export = new $exportClass;
                        $data = $export->collection();
                        $headings = $export->headings();

                        $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadView('exports.pdf-template', [
                            'title' => $modelName,
                            'headings' => $headings,
                            'data' => $data,
                            'export' => $export
                        ]);

                        return response()->streamDownload(function () use ($pdf) {
                            echo $pdf->output();
                        }, $fileName);
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Export PDF Error')
                            ->body('Terjadi kesalahan saat export PDF: ' . $e->getMessage())
                            ->danger()
                            ->send();

                        return null;
                    }
                });
        }

        return $actions;
    }
}
