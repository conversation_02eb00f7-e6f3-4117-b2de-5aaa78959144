<?php

namespace App\Traits;

use App\Models\MediaLibrary;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

trait HasMediaUpload
{
    /**
     * Create a file upload section for forms
     */
    public static function getMediaUploadSection(
        string $label = 'Upload File',
        string $helperText = 'Upload file yang akan disimpan di Media Library',
        array $acceptedFileTypes = ['image/*', 'application/pdf'],
        int $maxSize = 10240, // 10MB default
        bool $multiple = true,
        string $directory = 'uploads'
    ): Section {
        return Section::make($label)
            ->schema([
                FileUpload::make('media_uploads')
                    ->label('File')
                    ->multiple($multiple)
                    ->directory($directory)
                    ->visibility('public')
                    ->acceptedFileTypes($acceptedFileTypes)
                    ->maxSize($maxSize)
                    ->helperText($helperText)
                    ->columnSpanFull(),
            ]);
    }

    /**
     * Process uploaded files and save to Media Library
     */
    public function processMediaUploads(
        array $uploadedFiles,
        string $category = 'general',
        array $tags = [],
        string $description = '',
        bool $isPublic = true
    ): MediaLibrary {
        // Create MediaLibrary record
        $mediaLibrary = MediaLibrary::create([
            'name' => $this->getMediaName(),
            'description' => $description,
            'category' => $category,
            'tags' => $tags,
            'uploaded_by' => Auth::id(),
            'is_public' => $isPublic,
        ]);

        // Process each uploaded file
        foreach ($uploadedFiles as $file) {
            if ($file instanceof UploadedFile) {
                $collection = MediaLibrary::getCollectionFromMimeType($file->getMimeType());

                $mediaLibrary
                    ->addMediaFromRequest('media_uploads')
                    ->toMediaCollection($collection);
            } elseif (is_string($file)) {
                // Handle file path string
                $this->addFileFromPath($mediaLibrary, $file);
            }
        }

        return $mediaLibrary;
    }

    /**
     * Add file from storage path
     */
    protected function addFileFromPath(MediaLibrary $mediaLibrary, string $filePath): void
    {
        $fullPath = Storage::disk('public')->path($filePath);

        if (file_exists($fullPath)) {
            $mimeType = mime_content_type($fullPath);
            $collection = MediaLibrary::getCollectionFromMimeType($mimeType);

            $mediaLibrary
                ->addMedia($fullPath)
                ->toMediaCollection($collection);
        }
    }

    /**
     * Get media name for the record
     */
    protected function getMediaName(): string
    {
        if (property_exists($this, 'name') && !empty($this->name)) {
            return $this->name;
        }

        if (property_exists($this, 'title') && !empty($this->title)) {
            return $this->title;
        }

        return class_basename($this) . ' #' . ($this->id ?? 'New');
    }

    /**
     * Get media upload form component for specific use cases
     */
    public static function getDocumentUpload(string $label = 'Upload Dokumen'): Section
    {
        return self::getMediaUploadSection(
            label: $label,
            helperText: 'Upload dokumen (PDF, Word, Excel, PowerPoint)',
            acceptedFileTypes: [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            ],
            maxSize: 50 * 1024, // 50MB
            directory: 'documents'
        );
    }

    /**
     * Get image upload form component
     */
    public static function getImageUpload(string $label = 'Upload Gambar'): Section
    {
        return self::getMediaUploadSection(
            label: $label,
            helperText: 'Upload gambar (JPEG, PNG, GIF, WebP)',
            acceptedFileTypes: ['image/*'],
            maxSize: 5 * 1024, // 5MB
            directory: 'images'
        );
    }

    /**
     * Get attachment upload for tickets, projects, etc.
     */
    public static function getAttachmentUpload(string $label = 'Upload Lampiran'): Section
    {
        return self::getMediaUploadSection(
            label: $label,
            helperText: 'Upload file lampiran (semua jenis file)',
            acceptedFileTypes: [
                'image/*',
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'text/plain',
                'application/zip',
                'video/*',
                'audio/*',
            ],
            maxSize: 100 * 1024, // 100MB
            directory: 'attachments'
        );
    }
}
