<?php

namespace App\Traits;

use App\Models\TeamMention;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HasMentions
{
    /**
     * Get mentions where this user was mentioned
     */
    public function mentions(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(TeamMention::class, 'mentioned_user_id');
    }

    /**
     * Get mentions created by this user
     */
    public function createdMentions(): Has<PERSON>any
    {
        return $this->hasMany(TeamMention::class, 'mentioned_by_user_id');
    }

    /**
     * Get unread mentions for this user
     */
    public function unreadMentions(): <PERSON><PERSON><PERSON>
    {
        return $this->mentions()->unread();
    }

    /**
     * Get unread mentions count
     */
    public function getUnreadMentionsCountAttribute(): int
    {
        return $this->unreadMentions()->count();
    }

    /**
     * Mark all mentions as read for this user
     */
    public function markAllMentionsAsRead(): void
    {
        $this->mentions()->unread()->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
    }

    /**
     * Get recent mentions for this user
     */
    public function getRecentMentions(int $limit = 10)
    {
        return $this->mentions()
            ->with(['mentionable', 'mentionedByUser'])
            ->latest()
            ->limit($limit)
            ->get();
    }
}
