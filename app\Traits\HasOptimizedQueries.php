<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;

trait HasOptimizedQueries
{
    /**
     * Scope to eager load common relationships for Karyawan
     */
    public function scopeWithBasicRelations(Builder $query): Builder
    {
        return $query->with([
            'jabatan',
            'departemen',
            'divisi.departemen',
            'entitas',
            'user',
            'supervisor'
        ]);
    }

    /**
     * Scope to eager load all relationships for detailed view
     */
    public function scopeWithAllRelations(Builder $query): Builder
    {
        return $query->with([
            'jabatan',
            'departemen',
            'divisi.departemen',
            'entitas',
            'user',
            'supervisor',
            'riwayatKontrak',
            'penggajian',
            'kerabatDarurat',
            'pendidikan',
            'bpjs',
            'kpiPenilaians.penilai',
            'pelanggarans',
            'dokumens',
            'schedules.shift',
            'absensi.jadwal.shift',
            'absensi.jadwal.entitas'
        ]);
    }

    /**
     * Scope for active employees only
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status_aktif', true);
    }

    /**
     * Scope for employees by entity
     */
    public function scopeByEntity(Builder $query, int $entityId): Builder
    {
        return $query->where('id_entitas', $entityId);
    }

    /**
     * Scope for employees by supervisor
     */
    public function scopeBySupervisor(Builder $query, int $supervisorId): Builder
    {
        return $query->where('supervisor_id', $supervisorId);
    }

    /**
     * Scope for employees by department
     */
    public function scopeByDepartment(Builder $query, int $departmentId): Builder
    {
        return $query->where('id_departemen', $departmentId);
    }

    /**
     * Scope for employees by division
     */
    public function scopeByDivision(Builder $query, int $divisionId): Builder
    {
        return $query->where('id_divisi', $divisionId);
    }

    /**
     * Scope to get KPI data with penilai information
     */
    public function scopeWithKpiData(Builder $query): Builder
    {
        return $query->with([
            'kpiPenilaians' => function ($q) {
                $q->with('penilai:id,name,role')
                    ->orderBy('periode', 'desc');
            }
        ]);
    }

    /**
     * Scope to get latest KPI for each employee
     */
    public function scopeWithLatestKpi(Builder $query): Builder
    {
        return $query->with([
            'kpiPenilaians' => function ($q) {
                $q->with('penilai:id,name,role')
                    ->latest('periode')
                    ->limit(1);
            }
        ]);
    }

    /**
     * Scope to search by name or NIP
     */
    public function scopeSearch(Builder $query, string $search): Builder
    {
        return $query->where(function ($q) use ($search) {
            $q->where('nama_lengkap', 'like', "%{$search}%")
                ->orWhere('nip', 'like', "%{$search}%")
                ->orWhere('nik', 'like', "%{$search}%");
        });
    }
}
