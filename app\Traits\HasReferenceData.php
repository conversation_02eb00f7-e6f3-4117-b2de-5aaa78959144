<?php

namespace App\Traits;

use App\Models\ReferenceData\KpiNilaiAkhir;
use App\Models\ReferenceData\KpiStatusPenilaian;
use Illuminate\Support\Facades\DB;

trait HasReferenceData
{
    /**
     * Get options from reference table
     */
    public static function getReferenceOptions(string $tableName): array
    {
        return DB::table($tableName)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->pluck('nama', 'kode')
            ->toArray();
    }

    /**
     * Get KPI Nilai Akhir options
     */
    public static function getKpiNilaiAkhirOptions(): array
    {
        return KpiNilaiAkhir::getOptions();
    }

    /**
     * Get KPI Status Penilaian options
     */
    public static function getKpiStatusPenilaianOptions(): array
    {
        return KpiStatusPenilaian::getOptions();
    }

    /**
     * Get Mutasi Tipe options
     */
    public static function getMutasiTipeOptions(): array
    {
        return static::getReferenceOptions('mutasi_tipe');
    }

    /**
     * Get SOP Scope Type options
     */
    public static function getSopScopeTypeOptions(): array
    {
        return static::getReferenceOptions('sop_scope_type');
    }

    /**
     * Get SOP Status options
     */
    public static function getSopStatusOptions(): array
    {
        return static::getReferenceOptions('sop_status');
    }

    /**
     * Get Akun Kategori options
     */
    public static function getAkunKategoriOptions(): array
    {
        return static::getReferenceOptions('akun_kategori');
    }

    /**
     * Get Akun Tipe options
     */
    public static function getAkunTipeOptions(): array
    {
        return static::getReferenceOptions('akun_tipe');
    }

    /**
     * Get Aset Kategori options
     */
    public static function getAsetKategoriOptions(): array
    {
        return static::getReferenceOptions('aset_kategori');
    }

    /**
     * Get Jenis Kelamin options
     */
    public static function getJenisKelaminOptions(): array
    {
        return static::getReferenceOptions('jenis_kelamin');
    }

    /**
     * Get Status Pernikahan options
     */
    public static function getStatusPernikahanOptions(): array
    {
        return static::getReferenceOptions('status_pernikahan');
    }

    /**
     * Get Absensi Status options
     */
    public static function getAbsensiStatusOptions(): array
    {
        return static::getReferenceOptions('absensi_status');
    }

    /**
     * Get User Role options
     */
    public static function getUserRoleOptions(): array
    {
        return static::getReferenceOptions('user_role');
    }

    /**
     * Get display name for reference value
     */
    public static function getReferenceDisplayName(string $tableName, string $kode): ?string
    {
        return DB::table($tableName)
            ->where('kode', $kode)
            ->where('is_active', true)
            ->value('nama');
    }
}
