<?php

namespace App\Traits;

use Carbon\Carbon;
use Livewire\Attributes\On;

trait HasWidgetFilters
{
    public $widgetFilters = [
        'date_range' => 'this_month',
        'start_date' => null,
        'end_date' => null,
        'month' => null,
        'year' => null,
    ];

    #[On('updateCharts')]
    public function updateWidgetData(): void
    {
        // This will trigger widget refresh
        $this->dispatch('$refresh');
    }

    #[On('filtersUpdated')]
    public function handleFiltersUpdated($filters): void
    {
        // Store filters for this widget instance
        $this->widgetFilters = $filters;
        // Refresh the widget
        $this->dispatch('$refresh');
    }

    public function getFilteredDateRange(): array
    {
        // Use widget filters if available
        if ($this->widgetFilters && isset($this->widgetFilters['date_range'])) {
            return $this->parseDateRange($this->widgetFilters);
        }

        // Try to get filters from session (set by dashboard)
        $sessionFilters = session('dashboard_filters');
        if ($sessionFilters) {
            return $this->parseDateRange($sessionFilters);
        }

        // Get filters from parent dashboard if available
        if (method_exists($this, 'getOwnerRecord') && $this->getOwnerRecord()) {
            $owner = $this->getOwnerRecord();
            if (property_exists($owner, 'filters')) {
                return $this->parseDateRange($owner->filters);
            }
        }

        // Get filters from Livewire component if available
        if (property_exists($this, 'filters') && $this->filters) {
            return $this->parseDateRange($this->filters);
        }

        // Default to current month
        return [
            'start' => now()->startOfMonth(),
            'end' => now()->endOfMonth(),
        ];
    }

    private function parseDateRange($filters): array
    {
        // Check if using new advanced filter system
        if (isset($filters['period_type'])) {
            return $this->parseAdvancedDateRange($filters);
        }

        // Legacy filter system
        $dateRange = $filters['date_range'] ?? 'this_month';

        switch ($dateRange) {
            case 'today':
                return [
                    'start' => now()->startOfDay(),
                    'end' => now()->endOfDay(),
                ];
            case 'yesterday':
                return [
                    'start' => now()->subDay()->startOfDay(),
                    'end' => now()->subDay()->endOfDay(),
                ];
            case 'this_week':
                return [
                    'start' => now()->startOfWeek(),
                    'end' => now()->endOfWeek(),
                ];
            case 'last_week':
                return [
                    'start' => now()->subWeek()->startOfWeek(),
                    'end' => now()->subWeek()->endOfWeek(),
                ];
            case 'this_month':
                return [
                    'start' => now()->startOfMonth(),
                    'end' => now()->endOfMonth(),
                ];
            case 'last_month':
                return [
                    'start' => now()->subMonth()->startOfMonth(),
                    'end' => now()->subMonth()->endOfMonth(),
                ];
            case 'this_quarter':
                return [
                    'start' => now()->startOfQuarter(),
                    'end' => now()->endOfQuarter(),
                ];
            case 'last_quarter':
                return [
                    'start' => now()->subQuarter()->startOfQuarter(),
                    'end' => now()->subQuarter()->endOfQuarter(),
                ];
            case 'this_year':
                return [
                    'start' => now()->startOfYear(),
                    'end' => now()->endOfYear(),
                ];
            case 'last_year':
                return [
                    'start' => now()->subYear()->startOfYear(),
                    'end' => now()->subYear()->endOfYear(),
                ];
            case 'custom':
                return [
                    'start' => $filters['start_date'] ? Carbon::parse($filters['start_date']) : now()->startOfMonth(),
                    'end' => $filters['end_date'] ? Carbon::parse($filters['end_date']) : now()->endOfMonth(),
                ];
            case 'monthly':
                $month = $filters['month'] ?? now()->month;
                $year = $filters['year'] ?? now()->year;
                return [
                    'start' => Carbon::create($year, $month, 1)->startOfMonth(),
                    'end' => Carbon::create($year, $month, 1)->endOfMonth(),
                ];
            default:
                return [
                    'start' => now()->startOfMonth(),
                    'end' => now()->endOfMonth(),
                ];
        }
    }

    private function parseAdvancedDateRange($filters): array
    {
        // If start_date and end_date are explicitly set, use them
        if (isset($filters['start_date']) && isset($filters['end_date'])) {
            return [
                'start' => Carbon::parse($filters['start_date']),
                'end' => Carbon::parse($filters['end_date']),
            ];
        }

        $periodType = $filters['period_type'] ?? 'current_payroll';

        switch ($periodType) {
            case 'current_work_period':
            case 'current_payroll':
                return [
                    'start' => \App\Models\CompanySettings::getCurrentWorkPeriodStartDate(),
                    'end' => \App\Models\CompanySettings::getCurrentWorkPeriodEndDate(),
                ];

            case 'last_work_period':
            case 'last_payroll':
                $currentStart = \App\Models\CompanySettings::getCurrentWorkPeriodStartDate();
                return [
                    'start' => $currentStart->copy()->subMonth(),
                    'end' => $currentStart->copy()->subDay(),
                ];

            case 'work_period':
            case 'payroll_period':
                if (isset($filters['work_period']) || isset($filters['payroll_period'])) {
                    $periodKey = $filters['work_period'] ?? $filters['payroll_period'];
                    [$year, $month] = explode('-', $periodKey);
                    $period = \App\Models\CompanySettings::getWorkPeriod((int)$month, (int)$year);
                    return [
                        'start' => $period['start'],
                        'end' => $period['end'],
                    ];
                }
                // Fallback to current work period
                return [
                    'start' => \App\Models\CompanySettings::getCurrentWorkPeriodStartDate(),
                    'end' => \App\Models\CompanySettings::getCurrentWorkPeriodEndDate(),
                ];

            case 'this_month':
                return [
                    'start' => now()->startOfMonth(),
                    'end' => now()->endOfMonth(),
                ];

            case 'last_month':
                return [
                    'start' => now()->subMonth()->startOfMonth(),
                    'end' => now()->subMonth()->endOfMonth(),
                ];

            case 'this_week':
                return [
                    'start' => now()->startOfWeek(),
                    'end' => now()->endOfWeek(),
                ];

            case 'last_week':
                return [
                    'start' => now()->subWeek()->startOfWeek(),
                    'end' => now()->subWeek()->endOfWeek(),
                ];

            case 'today':
                return [
                    'start' => now()->startOfDay(),
                    'end' => now()->endOfDay(),
                ];

            case 'yesterday':
                return [
                    'start' => now()->subDay()->startOfDay(),
                    'end' => now()->subDay()->endOfDay(),
                ];

            default:
                return [
                    'start' => \App\Models\CompanySettings::getCurrentWorkPeriodStartDate(),
                    'end' => \App\Models\CompanySettings::getCurrentWorkPeriodEndDate(),
                ];
        }
    }

    protected function applyDateFilter($query, $dateColumn = 'created_at')
    {
        $dateRange = $this->getFilteredDateRange();

        return $query->whereBetween($dateColumn, [
            $dateRange['start'],
            $dateRange['end']
        ]);
    }

    protected function getFilterLabel(): string
    {
        $dateRange = $this->getFilteredDateRange();

        if ($dateRange['start']->isSameDay($dateRange['end'])) {
            return $dateRange['start']->format('d M Y');
        }

        if ($dateRange['start']->isSameMonth($dateRange['end'])) {
            return $dateRange['start']->format('M Y');
        }

        return $dateRange['start']->format('d M Y') . ' - ' . $dateRange['end']->format('d M Y');
    }
}
