<?php

namespace App\Traits;

/**
 * PowerJoins Compatibility Trait
 * 
 * This trait provides compatibility for Filament Support which expects
 * PowerJoins trait to exist, but the newer version of PowerJoins package
 * uses mixins instead of traits.
 */
trait PowerJoinsCompatibility
{
    // This trait is intentionally empty because PowerJoins methods
    // are now provided via mixins that are automatically registered
    // by the PowerJoinsServiceProvider.
    
    // The methods like joinRelationship, leftJoinRelationship, etc.
    // are available on all Eloquent Builder instances via mixins.
}
