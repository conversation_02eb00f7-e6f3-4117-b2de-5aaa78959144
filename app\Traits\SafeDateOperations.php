<?php

namespace App\Traits;

use Carbon\Carbon;

trait SafeDateOperations
{
    /**
     * Safely format a date field with null check
     */
    public function safeFormatDate($dateField, $format = 'M d, Y', $default = 'No date'): string
    {
        $date = $this->{$dateField};
        return $date ? $date->format($format) : $default;
    }

    /**
     * Safely check if a date field is in the past
     */
    public function safeDateIsPast($dateField): bool
    {
        $date = $this->{$dateField};
        return $date ? $date->isPast() : false;
    }

    /**
     * Safely check if a date field is in the future
     */
    public function safeDateIsFuture($dateField): bool
    {
        $date = $this->{$dateField};
        return $date ? $date->isFuture() : false;
    }

    /**
     * Safely get days difference between two date fields
     */
    public function safeDateDiff($startField, $endField): int
    {
        $start = $this->{$startField};
        $end = $this->{$endField};
        
        if (!$start || !$end) {
            return 0;
        }
        
        return $start->diffInDays($end);
    }

    /**
     * Safely get days until a date field
     */
    public function safeDaysUntil($dateField): int
    {
        $date = $this->{$dateField};
        return $date ? now()->diffInDays($date, false) : 0;
    }

    /**
     * Safely get days since a date field
     */
    public function safeDaysSince($dateField): int
    {
        $date = $this->{$dateField};
        return $date ? $date->diffInDays(now()) : 0;
    }

    /**
     * Get a safe Carbon instance or default
     */
    public function safeCarbon($dateField, $default = null): ?Carbon
    {
        $date = $this->{$dateField};
        return $date ?: ($default ? Carbon::parse($default) : null);
    }

    /**
     * Check if date field is between two dates
     */
    public function safeDateBetween($dateField, $start, $end): bool
    {
        $date = $this->{$dateField};
        if (!$date) return false;
        
        return $date->between($start, $end);
    }

    /**
     * Get relative time string safely
     */
    public function safeRelativeTime($dateField, $default = 'No date'): string
    {
        $date = $this->{$dateField};
        return $date ? $date->diffForHumans() : $default;
    }
}
