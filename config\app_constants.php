<?php

return [
    /*
    |--------------------------------------------------------------------------
    | User Roles
    |--------------------------------------------------------------------------
    |
    | Define all user roles used throughout the application
    |
    */
    'user_roles' => [
        'admin' => 'Admin',
        'supervisor' => 'Supervisor',
        'karyawan' => 'Karyawan',
    ],

    /*
    |--------------------------------------------------------------------------
    | Attendance Status
    |--------------------------------------------------------------------------
    |
    | Define all attendance status options
    |
    */
    'attendance_status' => [
        'hadir' => 'Hadir',
        'terlambat' => 'Terlambat',
        'izin' => 'Izin',
        'sakit' => 'Sakit',
        'cuti' => 'Cuti',
        'alpha' => 'Alpha (Tanpa Keterangan)',
    ],

    /*
    |--------------------------------------------------------------------------
    | Schedule Status
    |--------------------------------------------------------------------------
    |
    | Define all schedule status options
    |
    */
    'schedule_status' => [
        'scheduled' => 'Terjadwal',
        'completed' => 'Selesai',
        'cancelled' => 'Dibatalkan',
        'rescheduled' => 'Dijadwal Ulang',
    ],

    /*
    |--------------------------------------------------------------------------
    | Gender Options
    |--------------------------------------------------------------------------
    |
    | Define gender options for employee data
    |
    */
    'gender_options' => [
        'L' => 'Laki-laki',
        'P' => 'Perempuan',
    ],

    /*
    |--------------------------------------------------------------------------
    | Marital Status Options
    |--------------------------------------------------------------------------
    |
    | Define marital status options for employee data
    |
    */
    'marital_status' => [
        'belum_menikah' => 'Belum Menikah',
        'menikah' => 'Menikah',
        'cerai' => 'Cerai',
        'janda' => 'Janda',
        'duda' => 'Duda',
    ],

    /*
    |--------------------------------------------------------------------------
    | Religion Options
    |--------------------------------------------------------------------------
    |
    | Define religion options for employee data
    |
    */
    'religion_options' => [
        'islam' => 'Islam',
        'kristen' => 'Kristen',
        'katolik' => 'Katolik',
        'hindu' => 'Hindu',
        'buddha' => 'Buddha',
        'konghucu' => 'Konghucu',
        'lainnya' => 'Lainnya',
    ],

    /*
    |--------------------------------------------------------------------------
    | Blood Type Options
    |--------------------------------------------------------------------------
    |
    | Define blood type options for employee data
    |
    */
    'blood_types' => [
        'A' => 'A',
        'B' => 'B',
        'AB' => 'AB',
        'O' => 'O',
        'A+' => 'A+',
        'A-' => 'A-',
        'B+' => 'B+',
        'B-' => 'B-',
        'AB+' => 'AB+',
        'AB-' => 'AB-',
        'O+' => 'O+',
        'O-' => 'O-',
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Directories
    |--------------------------------------------------------------------------
    |
    | Define directories for file uploads
    |
    */
    'upload_directories' => [
        'employee_photos' => 'foto-karyawan',
        'attendance_photos_in' => 'absensi/masuk',
        'attendance_photos_out' => 'absensi/keluar',
        'documents' => 'dokumen-karyawan',
        'contracts' => 'kontrak-karyawan',
        'payroll_documents' => 'dokumen-penggajian',
    ],

    /*
    |--------------------------------------------------------------------------
    | Navigation Groups
    |--------------------------------------------------------------------------
    |
    | Define navigation groups for Filament panels
    |
    */
    'navigation_groups' => [
        'master_data' => 'Data Master',
        'employee_management' => 'Manajemen Karyawan',
        'attendance_management' => 'Manajemen Absensi',
        'payroll_management' => 'Manajemen Penggajian',
        'reports' => 'Laporan',
        'settings' => 'Pengaturan',
        'user_management' => 'Manajemen User',
    ],

    /*
    |--------------------------------------------------------------------------
    | Badge Colors
    |--------------------------------------------------------------------------
    |
    | Define badge colors for different status
    |
    */
    'badge_colors' => [
        'attendance_status' => [
            'hadir' => 'success',
            'terlambat' => 'warning',
            'izin' => 'info',
            'sakit' => 'info',
            'cuti' => 'primary',
            'alpha' => 'danger',
        ],
        'schedule_status' => [
            'scheduled' => 'primary',
            'completed' => 'success',
            'cancelled' => 'danger',
            'rescheduled' => 'warning',
        ],
        'approval_status' => [
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Pagination Settings
    |--------------------------------------------------------------------------
    |
    | Define default pagination settings
    |
    */
    'pagination' => [
        'default_per_page' => 25,
        'per_page_options' => [10, 25, 50, 100],
    ],

    /*
    |--------------------------------------------------------------------------
    | Date Formats
    |--------------------------------------------------------------------------
    |
    | Define date formats used throughout the application
    |
    */
    'date_formats' => [
        'display' => 'd M Y',
        'display_with_time' => 'd M Y H:i',
        'input' => 'Y-m-d',
        'database' => 'Y-m-d H:i:s',
    ],

    /*
    |--------------------------------------------------------------------------
    | Number Formats
    |--------------------------------------------------------------------------
    |
    | Define number formats for different purposes
    |
    */
    'number_formats' => [
        'currency' => [
            'decimals' => 0,
            'decimal_separator' => ',',
            'thousands_separator' => '.',
            'prefix' => 'Rp ',
        ],
        'percentage' => [
            'decimals' => 2,
            'suffix' => '%',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | System Limits
    |--------------------------------------------------------------------------
    |
    | Define system limits and constraints
    |
    */
    'limits' => [
        'max_file_size' => 5120, // KB
        'max_image_size' => 2048, // KB
        'max_document_size' => 10240, // KB
        'attendance_photo_max_size' => 1024, // KB
        'bulk_schedule_max_employees' => 500,
        'export_max_records' => 10000,
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Values
    |--------------------------------------------------------------------------
    |
    | Define default values for various fields
    |
    */
    'defaults' => [
        'user_role' => 'karyawan',
        'employee_status' => true,
        'attendance_status' => 'hadir',
        'schedule_status' => 'scheduled',
        'shift_tolerance_minutes' => 15,
        'work_hours_per_day' => 8,
        'work_days_per_week' => 5,
        'payroll_number_prefix' => 'PG',
        'payroll_number_padding' => 6,
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Rules
    |--------------------------------------------------------------------------
    |
    | Define common validation rules
    |
    */
    'validation' => [
        'nip_length' => 20,
        'nik_length' => 16,
        'phone_min_length' => 10,
        'phone_max_length' => 15,
        'password_min_length' => 8,
        'name_max_length' => 100,
        'address_max_length' => 500,
        'description_max_length' => 1000,
    ],
];
