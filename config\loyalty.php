<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Loyalty Points Configuration
    |--------------------------------------------------------------------------
    |
    | Configure how loyalty points are calculated and managed
    |
    */

    // Basic Points Calculation
    'points_per_amount' => env('LOYALTY_POINTS_PER_AMOUNT', 1000), // 1 point per 1000 rupiah
    'points_multiplier' => env('LOYALTY_POINTS_MULTIPLIER', 1), // Base multiplier
    
    // VIP Customer Bonus
    'vip_bonus_multiplier' => env('LOYALTY_VIP_BONUS', 1.5), // 50% bonus for VIP
    
    // Minimum Transaction for Points
    'minimum_transaction' => env('LOYALTY_MIN_TRANSACTION', 10000), // Minimum 10k for points
    
    // Maximum Points per Transaction
    'max_points_per_transaction' => env('LOYALTY_MAX_POINTS', 1000), // Max 1000 points per transaction
    
    // Points Expiry
    'points_expiry_months' => env('LOYALTY_EXPIRY_MONTHS', 12), // Points expire after 12 months
    
    // Redemption Settings
    'redemption_rate' => env('LOYALTY_REDEMPTION_RATE', 100), // 100 points = 1000 rupiah
    'minimum_redemption' => env('LOYALTY_MIN_REDEMPTION', 50), // Minimum 50 points to redeem
    
    // Special Promotions
    'double_points_days' => [
        'friday', 'saturday', 'sunday' // Double points on weekends
    ],
    
    'birthday_bonus_points' => env('LOYALTY_BIRTHDAY_BONUS', 100), // Bonus points on birthday
    
    // Product Category Multipliers
    'category_multipliers' => [
        'food' => 1.0,
        'beverage' => 1.2, // 20% more points for beverages
        'dessert' => 1.5,  // 50% more points for desserts
        'premium' => 2.0,  // Double points for premium items
    ],
    
    // Time-based Multipliers
    'time_multipliers' => [
        'happy_hour' => [
            'start' => '15:00',
            'end' => '17:00',
            'multiplier' => 1.5
        ],
        'late_night' => [
            'start' => '21:00',
            'end' => '23:59',
            'multiplier' => 1.3
        ]
    ],
    
    // Customer Tier Settings
    'customer_tiers' => [
        'bronze' => [
            'min_points' => 0,
            'multiplier' => 1.0
        ],
        'silver' => [
            'min_points' => 1000,
            'multiplier' => 1.1
        ],
        'gold' => [
            'min_points' => 5000,
            'multiplier' => 1.2
        ],
        'platinum' => [
            'min_points' => 10000,
            'multiplier' => 1.5
        ],
        'diamond' => [
            'min_points' => 25000,
            'multiplier' => 2.0
        ]
    ]
];
