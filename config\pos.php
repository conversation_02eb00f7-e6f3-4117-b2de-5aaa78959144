<?php

return [
    /*
    |--------------------------------------------------------------------------
    | POS System Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for POS (Point of Sale) system integration
    |
    */

    'api' => [
        /*
        |--------------------------------------------------------------------------
        | API Rate Limiting
        |--------------------------------------------------------------------------
        |
        | Rate limiting configuration for POS API endpoints
        |
        */
        'rate_limit' => [
            'sync' => env('POS_SYNC_RATE_LIMIT', '100,1'), // 100 requests per minute
            'auth' => env('POS_AUTH_RATE_LIMIT', '10,1'),  // 10 requests per minute
            'general' => env('POS_GENERAL_RATE_LIMIT', '60,1'), // 60 requests per minute
        ],

        /*
        |--------------------------------------------------------------------------
        | Token Configuration
        |--------------------------------------------------------------------------
        |
        | Configuration for API tokens
        |
        */
        'token' => [
            'expiration' => env('POS_TOKEN_EXPIRATION', null), // null = no expiration
            'abilities' => [
                'pos:read',
                'pos:write',
                'pos:transactions',
                'pos:products',
                'pos:customers',
                'pos:sync',
            ],
        ],

        /*
        |--------------------------------------------------------------------------
        | Sync Configuration
        |--------------------------------------------------------------------------
        |
        | Configuration for data synchronization
        |
        */
        'sync' => [
            'batch_size' => env('POS_SYNC_BATCH_SIZE', 50),
            'timeout' => env('POS_SYNC_TIMEOUT', 30),
            'retry_attempts' => env('POS_SYNC_RETRY_ATTEMPTS', 3),
            'conflict_resolution' => env('POS_CONFLICT_RESOLUTION', 'server_wins'), // server_wins, client_wins, manual
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Offline Mode Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for offline mode support
    |
    */
    'offline' => [
        'enabled' => env('POS_OFFLINE_ENABLED', true),
        'storage_limit' => env('POS_OFFLINE_STORAGE_LIMIT', 1000), // Max transactions to store offline
        'auto_sync_interval' => env('POS_AUTO_SYNC_INTERVAL', 300), // 5 minutes in seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | Security settings for POS system
    |
    */
    'security' => [
        'allowed_ips' => env('POS_ALLOWED_IPS', null), // Comma-separated IPs, null = allow all
        'require_https' => env('POS_REQUIRE_HTTPS', false),
        'session_timeout' => env('POS_SESSION_TIMEOUT', 3600), // 1 hour in seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Business Rules
    |--------------------------------------------------------------------------
    |
    | Business logic configuration
    |
    */
    'business' => [
        'loyalty_points' => [
            'enabled' => env('POS_LOYALTY_ENABLED', true),
            'earn_rate' => env('POS_LOYALTY_EARN_RATE', 1), // 1 point per 1000 IDR
            'redeem_rate' => env('POS_LOYALTY_REDEEM_RATE', 100), // 100 points = 1000 IDR
        ],
        
        'discounts' => [
            'max_percentage' => env('POS_MAX_DISCOUNT_PERCENTAGE', 50),
            'require_approval' => env('POS_DISCOUNT_REQUIRE_APPROVAL', true),
        ],

        'inventory' => [
            'auto_deduct_stock' => env('POS_AUTO_DEDUCT_STOCK', true),
            'allow_negative_stock' => env('POS_ALLOW_NEGATIVE_STOCK', false),
            'low_stock_threshold' => env('POS_LOW_STOCK_THRESHOLD', 10),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Configuration
    |--------------------------------------------------------------------------
    |
    | Notification settings for POS events
    |
    */
    'notifications' => [
        'low_stock' => env('POS_NOTIFY_LOW_STOCK', true),
        'large_transactions' => env('POS_NOTIFY_LARGE_TRANSACTIONS', true),
        'large_transaction_threshold' => env('POS_LARGE_TRANSACTION_THRESHOLD', 1000000), // 1 million IDR
        'sync_failures' => env('POS_NOTIFY_SYNC_FAILURES', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Logging settings for POS operations
    |
    */
    'logging' => [
        'enabled' => env('POS_LOGGING_ENABLED', true),
        'level' => env('POS_LOG_LEVEL', 'info'),
        'channels' => [
            'transactions' => env('POS_LOG_TRANSACTIONS', true),
            'sync' => env('POS_LOG_SYNC', true),
            'auth' => env('POS_LOG_AUTH', true),
            'errors' => env('POS_LOG_ERRORS', true),
        ],
    ],
];
