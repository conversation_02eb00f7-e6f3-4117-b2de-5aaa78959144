<?php

require_once 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

// Create new Spreadsheet object
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set document properties
$spreadsheet->getProperties()
    ->setCreator("PT. Viera Anugrah Pertama")
    ->setLastModifiedBy("Marketing Team")
    ->setTitle("Marketing Task List Import Template")
    ->setSubject("Task Management")
    ->setDescription("Template untuk import task list marketing panel")
    ->setKeywords("marketing task import template")
    ->setCategory("Task Management");

// Set sheet title
$sheet->setTitle('Marketing Tasks');

// Define headers
$headers = [
    'A1' => 'id',
    'B1' => 'project_id', 
    'C1' => 'modul',
    'D1' => 'name',
    'E1' => 'description',
    'F1' => 'assigned_to',
    'G1' => 'due_date',
    'H1' => 'start_date',
    'I1' => 'status',
    'J1' => 'created_by',
    'K1' => 'deleted_at',
    'L1' => 'created_at',
    'M1' => 'updated_at'
];

// Set headers
foreach ($headers as $cell => $value) {
    $sheet->setCellValue($cell, $value);
}

// Style headers
$headerStyle = [
    'font' => [
        'bold' => true,
        'color' => ['rgb' => 'FFFFFF'],
        'size' => 12
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => '3B82F6']
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
            'color' => ['rgb' => '000000']
        ]
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER
    ]
];

$sheet->getStyle('A1:M1')->applyFromArray($headerStyle);

// Set column widths
$sheet->getColumnDimension('A')->setWidth(8);   // id
$sheet->getColumnDimension('B')->setWidth(12);  // project_id
$sheet->getColumnDimension('C')->setWidth(20);  // modul
$sheet->getColumnDimension('D')->setWidth(40);  // name
$sheet->getColumnDimension('E')->setWidth(60);  // description
$sheet->getColumnDimension('F')->setWidth(15);  // assigned_to
$sheet->getColumnDimension('G')->setWidth(15);  // due_date
$sheet->getColumnDimension('H')->setWidth(15);  // start_date
$sheet->getColumnDimension('I')->setWidth(15);  // status
$sheet->getColumnDimension('J')->setWidth(12);  // created_by
$sheet->getColumnDimension('K')->setWidth(15);  // deleted_at
$sheet->getColumnDimension('L')->setWidth(20);  // created_at
$sheet->getColumnDimension('M')->setWidth(20);  // updated_at

// Sample data from marketing_task_list.txt
$sampleData = [
    [1, 1, 'Panel Infrastructure', 'Create MarketingPanelProvider with proper configuration', 'Setup marketing panel provider dengan konfigurasi authentication dan middleware yang tepat', '', '2025-01-14', '2025-01-01', 'completed', 1, '', '2025-01-01 08:00:00', '2025-01-14 10:00:00'],
    [2, 1, 'Panel Infrastructure', 'Setup marketing panel routing', 'Konfigurasi routing /marketing dengan authentication dan role-based access', '', '2025-01-14', '2025-01-01', 'completed', 1, '', '2025-01-01 09:00:00', '2025-01-14 10:00:00'],
    [3, 1, 'Panel Infrastructure', 'Configure authentication and middleware', 'Setup authentication middleware dan session management untuk marketing panel', '', '2025-01-14', '2025-01-01', 'completed', 1, '', '2025-01-01 10:00:00', '2025-01-14 10:00:00'],
    [4, 1, 'Panel Infrastructure', 'Setup custom theme and styling', 'Implementasi Tailwind CSS theme khusus untuk marketing panel', '', '2025-01-14', '2025-01-02', 'completed', 1, '', '2025-01-02 08:00:00', '2025-01-14 10:00:00'],
    [5, 1, 'Dashboard', 'Create Marketing Dashboard page', 'Membuat halaman dashboard utama untuk marketing panel', '', '2025-01-14', '2025-01-03', 'completed', 1, '', '2025-01-03 08:00:00', '2025-01-14 10:00:00'],
    [6, 1, 'Dashboard', 'Implement TotalCustomersWidget', 'Widget untuk menampilkan total customers dengan growth metrics', '', '2025-01-14', '2025-01-03', 'completed', 1, '', '2025-01-03 09:00:00', '2025-01-14 10:00:00'],
    [7, 1, 'CRM', 'Create Customer model with comprehensive fields', 'Model Customer dengan fields lengkap termasuk geographic data', '', '2025-01-14', '2025-01-04', 'completed', 1, '', '2025-01-04 08:00:00', '2025-01-14 10:00:00'],
    [8, 1, 'CRM', 'Implement CustomerResource with full CRUD', 'Resource Customer dengan operasi CRUD lengkap', '', '2025-01-14', '2025-01-04', 'completed', 1, '', '2025-01-04 09:00:00', '2025-01-14 10:00:00'],
    [9, 1, 'Product', 'Create Product model with pricing and inventory', 'Model Product dengan pricing cost price selling price dan inventory', '', '2025-01-14', '2025-01-05', 'completed', 1, '', '2025-01-05 08:00:00', '2025-01-14 10:00:00'],
    [10, 1, 'Product', 'Implement ProductResource with full management', 'Resource Product dengan management lengkap CRUD operations', '', '2025-01-14', '2025-01-05', 'completed', 1, '', '2025-01-05 09:00:00', '2025-01-14 10:00:00'],
    [11, 1, 'Advanced Analytics', 'Implement advanced customer behavior analytics', 'Analytics behavior customer yang advanced dengan machine learning', '', '2025-02-15', '2025-01-15', 'in_progress', 1, '', '2025-01-15 08:00:00', '2025-01-15 08:00:00'],
    [12, 1, 'Advanced Analytics', 'Create predictive analytics for customer churn', 'Predictive analytics untuk prediksi customer churn', '', '2025-02-20', '2025-01-16', 'in_progress', 1, '', '2025-01-16 08:00:00', '2025-01-16 08:00:00'],
    [13, 1, 'Performance Optimization', 'Optimize database queries for large datasets', 'Optimasi query database untuk handling large datasets', '', '2025-04-15', '2025-02-01', 'todo', 1, '', '2025-02-01 08:00:00', '2025-02-01 08:00:00'],
    [14, 1, 'Mobile Enhancement', 'Optimize mobile interface', 'Optimasi interface untuk mobile devices', '', '2025-05-25', '2025-02-09', 'todo', 1, '', '2025-02-09 08:00:00', '2025-02-09 08:00:00'],
    [15, 1, 'Integration', 'Create API endpoints for external integrations', 'API endpoints untuk integrasi dengan sistem external', '', '2025-06-30', '2025-03-01', 'todo', 1, '', '2025-03-01 08:00:00', '2025-03-01 08:00:00']
];

// Add sample data
$row = 2;
foreach ($sampleData as $data) {
    $col = 'A';
    foreach ($data as $value) {
        $sheet->setCellValue($col . $row, $value);
        $col++;
    }
    $row++;
}

// Style data rows
$dataStyle = [
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
            'color' => ['rgb' => 'CCCCCC']
        ]
    ],
    'alignment' => [
        'vertical' => Alignment::VERTICAL_TOP,
        'wrapText' => true
    ]
];

$sheet->getStyle('A2:M' . ($row - 1))->applyFromArray($dataStyle);

// Color code status
for ($i = 2; $i < $row; $i++) {
    $status = $sheet->getCell('I' . $i)->getValue();
    $statusStyle = [];
    
    switch ($status) {
        case 'completed':
            $statusStyle = [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'D1FAE5'] // Light green
                ],
                'font' => ['color' => ['rgb' => '065F46']] // Dark green
            ];
            break;
        case 'in_progress':
            $statusStyle = [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FEF3C7'] // Light yellow
                ],
                'font' => ['color' => ['rgb' => '92400E']] // Dark yellow
            ];
            break;
        case 'todo':
            $statusStyle = [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E5E7EB'] // Light gray
                ],
                'font' => ['color' => ['rgb' => '374151']] // Dark gray
            ];
            break;
    }
    
    if (!empty($statusStyle)) {
        $sheet->getStyle('I' . $i)->applyFromArray($statusStyle);
    }
}

// Add instructions sheet
$instructionsSheet = $spreadsheet->createSheet();
$instructionsSheet->setTitle('Instructions');

$instructions = [
    ['Marketing Task List Import Template'],
    [''],
    ['FIELD DESCRIPTIONS:'],
    ['id', 'Unique identifier (auto-increment)'],
    ['project_id', 'Project ID (1 = Marketing Panel)'],
    ['modul', 'Module name (Panel Infrastructure, Dashboard, CRM, Product, etc.)'],
    ['name', 'Task name (max 255 characters)'],
    ['description', 'Detailed task description'],
    ['assigned_to', 'User ID of assigned person (optional)'],
    ['due_date', 'Due date (YYYY-MM-DD format)'],
    ['start_date', 'Start date (YYYY-MM-DD format)'],
    ['status', 'Task status: todo, in_progress, completed'],
    ['created_by', 'User ID who created the task'],
    ['deleted_at', 'Soft delete timestamp (leave empty)'],
    ['created_at', 'Creation timestamp (YYYY-MM-DD HH:MM:SS)'],
    ['updated_at', 'Last update timestamp (YYYY-MM-DD HH:MM:SS)'],
    [''],
    ['STATUS VALUES:'],
    ['todo', 'Task not started yet'],
    ['in_progress', 'Task currently being worked on'],
    ['completed', 'Task finished successfully'],
    [''],
    ['MODULES:'],
    ['Panel Infrastructure', 'Basic panel setup and configuration'],
    ['Dashboard', 'Dashboard and widgets implementation'],
    ['CRM', 'Customer Relationship Management'],
    ['Product', 'Product management and catalog'],
    ['Category', 'Product category management'],
    ['Sales', 'Sales and transaction management'],
    ['Loyalty', 'Customer loyalty program'],
    ['Feedback', 'Customer feedback system'],
    ['Analytics', 'Analytics and reporting'],
    ['UI/UX', 'User interface and experience'],
    ['Security', 'Security and access control'],
    ['Export', 'Data export functionality'],
    ['Advanced Analytics', 'Advanced analytics features'],
    ['Enhanced Reporting', 'Enhanced reporting capabilities'],
    ['Performance Optimization', 'System performance improvements'],
    ['Mobile Enhancement', 'Mobile interface improvements'],
    ['Integration', 'External system integrations'],
    ['Automation', 'Marketing automation features']
];

$row = 1;
foreach ($instructions as $instruction) {
    if (count($instruction) == 1) {
        $instructionsSheet->setCellValue('A' . $row, $instruction[0]);
        if ($row == 1) {
            $instructionsSheet->getStyle('A' . $row)->applyFromArray([
                'font' => ['bold' => true, 'size' => 16, 'color' => ['rgb' => '1F2937']],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '3B82F6']],
                'font' => ['color' => ['rgb' => 'FFFFFF']]
            ]);
        } elseif (in_array($instruction[0], ['FIELD DESCRIPTIONS:', 'STATUS VALUES:', 'MODULES:'])) {
            $instructionsSheet->getStyle('A' . $row)->applyFromArray([
                'font' => ['bold' => true, 'size' => 12, 'color' => ['rgb' => '1F2937']]
            ]);
        }
    } else {
        $instructionsSheet->setCellValue('A' . $row, $instruction[0]);
        $instructionsSheet->setCellValue('B' . $row, $instruction[1]);
        $instructionsSheet->getStyle('A' . $row)->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => '374151']]
        ]);
    }
    $row++;
}

$instructionsSheet->getColumnDimension('A')->setWidth(25);
$instructionsSheet->getColumnDimension('B')->setWidth(60);

// Set active sheet back to main sheet
$spreadsheet->setActiveSheetIndex(0);

// Save Excel file
$writer = new Xlsx($spreadsheet);
$filename = 'marketing_task_import_template.xlsx';
$writer->save($filename);

echo "Excel file created successfully: " . $filename . "\n";
echo "File contains:\n";
echo "- Main sheet with sample marketing tasks\n";
echo "- Instructions sheet with field descriptions\n";
echo "- Color-coded status indicators\n";
echo "- Proper formatting and styling\n";

?>
