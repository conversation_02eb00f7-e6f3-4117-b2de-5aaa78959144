<?php

namespace Database\Factories;

use App\Models\CommentReaction;
use App\Models\TaskComment;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CommentReaction>
 */
class CommentReactionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CommentReaction::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'comment_id' => TaskComment::factory(),
            'user_id' => User::factory(),
            'reaction_type' => $this->faker->randomElement(['like', 'love', 'laugh', 'angry', 'sad']),
        ];
    }

    /**
     * Indicate that the reaction is for a specific comment.
     */
    public function forComment(TaskComment $comment): static
    {
        return $this->state(fn (array $attributes) => [
            'comment_id' => $comment->id,
        ]);
    }

    /**
     * Indicate that the reaction is by a specific user.
     */
    public function byUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Indicate that the reaction is a like.
     */
    public function like(): static
    {
        return $this->state(fn (array $attributes) => [
            'reaction_type' => 'like',
        ]);
    }

    /**
     * Indicate that the reaction is a love.
     */
    public function love(): static
    {
        return $this->state(fn (array $attributes) => [
            'reaction_type' => 'love',
        ]);
    }

    /**
     * Indicate that the reaction is a laugh.
     */
    public function laugh(): static
    {
        return $this->state(fn (array $attributes) => [
            'reaction_type' => 'laugh',
        ]);
    }

    /**
     * Indicate that the reaction is angry.
     */
    public function angry(): static
    {
        return $this->state(fn (array $attributes) => [
            'reaction_type' => 'angry',
        ]);
    }

    /**
     * Indicate that the reaction is sad.
     */
    public function sad(): static
    {
        return $this->state(fn (array $attributes) => [
            'reaction_type' => 'sad',
        ]);
    }

    /**
     * Indicate that the reaction has a specific type.
     */
    public function withType(string $type): static
    {
        return $this->state(fn (array $attributes) => [
            'reaction_type' => $type,
        ]);
    }
}
