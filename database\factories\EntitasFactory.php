<?php

namespace Database\Factories;

use App\Models\Entitas;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Entitas>
 */
class EntitasFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Entitas::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'nama' => $this->faker->company(),
            'alamat' => $this->faker->address(),
            'keterangan' => $this->faker->optional()->sentence(),
            'latitude' => $this->faker->latitude(-8, -5), // Indonesia latitude range
            'longitude' => $this->faker->longitude(95, 141), // Indonesia longitude range
            'radius' => $this->faker->numberBetween(50, 200), // Geofencing radius in meters
            'enable_geofencing' => $this->faker->boolean(80), // 80% chance of being enabled
        ];
    }

    /**
     * Indicate that the entitas has geofencing enabled.
     */
    public function withGeofencing(): static
    {
        return $this->state(fn(array $attributes) => [
            'enable_geofencing' => true,
            'latitude' => $this->faker->latitude(-8, -5),
            'longitude' => $this->faker->longitude(95, 141),
            'radius' => $this->faker->numberBetween(100, 300),
        ]);
    }

    /**
     * Indicate that the entitas has geofencing disabled.
     */
    public function withoutGeofencing(): static
    {
        return $this->state(fn(array $attributes) => [
            'enable_geofencing' => false,
            'latitude' => null,
            'longitude' => null,
            'radius' => null,
        ]);
    }

    /**
     * Indicate that the entitas is in Jakarta area.
     */
    public function jakarta(): static
    {
        return $this->state(fn(array $attributes) => [
            'nama' => 'Toko ' . $this->faker->streetName(),
            'alamat' => $this->faker->streetAddress() . ', Jakarta',
            'latitude' => $this->faker->latitude(-6.3, -6.1), // Jakarta latitude range
            'longitude' => $this->faker->longitude(106.7, 106.9), // Jakarta longitude range
        ]);
    }

    /**
     * Indicate that the entitas is a main store.
     */
    public function mainStore(): static
    {
        return $this->state(fn(array $attributes) => [
            'nama' => 'Toko Pusat',
            'alamat' => 'Jl. Sudirman No. 1, Jakarta Pusat',
            'latitude' => -6.200000,
            'longitude' => 106.816666,
            'radius' => 100,
            'enable_geofencing' => true,
        ]);
    }

    /**
     * Indicate that the entitas is a branch store.
     */
    public function branchStore(): static
    {
        return $this->state(fn(array $attributes) => [
            'nama' => 'Toko Cabang ' . $this->faker->city(),
            'alamat' => $this->faker->streetAddress() . ', ' . $this->faker->city(),
            'latitude' => $this->faker->latitude(-8, -5),
            'longitude' => $this->faker->longitude(95, 141),
            'radius' => $this->faker->numberBetween(50, 150),
            'enable_geofencing' => true,
        ]);
    }
}
