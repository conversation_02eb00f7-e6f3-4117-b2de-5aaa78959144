<?php

namespace Database\Factories;

use App\Models\InventoryStock;
use App\Models\Produk;
use App\Models\Warehouse;
use App\Models\Entitas;
use Illuminate\Database\Eloquent\Factories\Factory;

class InventoryStockFactory extends Factory
{
    protected $model = InventoryStock::class;

    public function definition(): array
    {
        $quantity = $this->faker->numberBetween(10, 200);
        $availableQty = $this->faker->numberBetween(5, $quantity);
        $onHoldQty = $this->faker->numberBetween(0, 10);
        $reservedQty = $this->faker->numberBetween(0, 10);
        $unitCost = $this->faker->numberBetween(10000, 100000);

        return [
            'product_id' => Produk::factory(),
            'warehouse_id' => Warehouse::factory(),
            'entitas_id' => Entitas::factory(),
            'quantity' => $quantity,
            'available_quantity' => $availableQty,
            'on_hold_quantity' => $onHoldQty,
            'reserved_quantity' => $reservedQty,
            'average_cost' => $unitCost,
            'total_value' => $quantity * $unitCost,
            'minimum_stock' => $this->faker->numberBetween(5, 20),
            'maximum_stock' => $this->faker->numberBetween(300, 500),
            'reorder_point' => $this->faker->numberBetween(10, 30),
            'safety_stock' => $this->faker->numberBetween(5, 15),
            'location_code' => 'A' . $this->faker->numberBetween(1, 5) . '-B' . $this->faker->numberBetween(1, 10) . '-C' . $this->faker->numberBetween(1, 20),
            'is_batch_tracked' => $this->faker->boolean(30),
            'last_updated' => now(),
            'last_movement_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'last_movement_type' => $this->faker->randomElement(['Purchase_Receipt', 'Sales_Issue', 'Adjustment_In', 'Adjustment_Out']),
        ];
    }
}
