<?php

namespace Database\Factories;

use App\Models\Karyawan;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class KaryawanFactory extends Factory
{
    protected $model = Karyawan::class;

    public function definition(): array
    {
        return [
            'nama_lengkap' => $this->faker->name(),
            'nip' => 'K' . str_pad($this->faker->unique()->numberBetween(1, 9999), 4, '0', STR_PAD_LEFT),
            'nik' => $this->faker->numerify('################'),
            'email' => $this->faker->unique()->safeEmail(),
            'nomor_telepon' => $this->faker->phoneNumber(),
            'alamat' => $this->faker->address(),
            'kota_lahir' => $this->faker->city(),
            'tanggal_lahir' => $this->faker->dateTimeBetween('-50 years', '-20 years'),
            'jenis_kelamin' => $this->faker->randomElement(['Laki-laki', 'Perempuan']),
            'status_pernikahan' => $this->faker->randomElement(['Belum Menikah', 'Menikah', 'Cerai']),
            'status_aktif' => 1,
            'id_user' => null, // Will be set manually in tests
        ];
    }

    public function withUser(): static
    {
        return $this->state(function (array $attributes) {
            $user = User::factory()->create(['role' => 'karyawan']);
            return [
                'id_user' => $user->id,
                'email' => $user->email,
            ];
        });
    }

    public function active(): static
    {
        return $this->state(fn() => [
            'status_aktif' => 1,
        ]);
    }

    public function inactive(): static
    {
        return $this->state(fn() => [
            'status_aktif' => 0,
        ]);
    }
}
