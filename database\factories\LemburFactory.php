<?php

namespace Database\Factories;

use App\Models\Lembur;
use App\Models\Karyawan;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Lembur>
 */
class LemburFactory extends Factory
{
    protected $model = Lembur::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'karyawan_id' => Karyawan::factory(),
            'tanggal' => $this->faker->dateTimeBetween('-30 days', 'now')->format('Y-m-d'),
            'jumlah_jam' => $this->faker->randomFloat(1, 0.5, 8.0), // 0.5 to 8.0 hours
            'deskripsi' => $this->faker->optional(0.8)->sentence(10), // 80% chance of having description
            'created_by' => User::factory(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the lembur is for current month.
     */
    public function currentMonth(): static
    {
        return $this->state(fn (array $attributes) => [
            'tanggal' => $this->faker->dateTimeBetween('first day of this month', 'now')->format('Y-m-d'),
        ]);
    }

    /**
     * Indicate that the lembur is for last month.
     */
    public function lastMonth(): static
    {
        return $this->state(fn (array $attributes) => [
            'tanggal' => $this->faker->dateTimeBetween('first day of last month', 'last day of last month')->format('Y-m-d'),
        ]);
    }

    /**
     * Indicate that the lembur has high hours.
     */
    public function highHours(): static
    {
        return $this->state(fn (array $attributes) => [
            'jumlah_jam' => $this->faker->randomFloat(1, 6.0, 12.0), // 6.0 to 12.0 hours
        ]);
    }

    /**
     * Indicate that the lembur has low hours.
     */
    public function lowHours(): static
    {
        return $this->state(fn (array $attributes) => [
            'jumlah_jam' => $this->faker->randomFloat(1, 0.5, 2.0), // 0.5 to 2.0 hours
        ]);
    }

    /**
     * Indicate that the lembur is for weekend (for testing validation).
     */
    public function weekend(): static
    {
        return $this->state(function (array $attributes) {
            // Find next weekend date
            $date = Carbon::now();
            while (!$date->isWeekend()) {
                $date->addDay();
            }
            
            return [
                'tanggal' => $date->format('Y-m-d'),
            ];
        });
    }

    /**
     * Indicate that the lembur has detailed description.
     */
    public function withDetailedDescription(): static
    {
        $descriptions = [
            'Menyelesaikan laporan bulanan yang harus diserahkan besok pagi untuk keperluan audit internal',
            'Membantu persiapan presentasi untuk klien besar yang akan dilaksanakan minggu depan',
            'Mengatasi masalah sistem yang urgent dan memerlukan penanganan segera',
            'Menyelesaikan proyek yang deadline-nya mepet dan tidak bisa ditunda lagi',
            'Membantu tim lain yang kekurangan tenaga untuk menyelesaikan target bulanan',
            'Melakukan maintenance sistem di luar jam kerja untuk menghindari gangguan operasional',
            'Menyelesaikan audit internal yang mendadak dari kantor pusat',
            'Mempersiapkan dokumen untuk meeting penting dengan stakeholder',
            'Mengatasi komplain pelanggan yang harus segera ditangani untuk menjaga reputasi perusahaan',
            'Menyelesaikan inventory stock opname yang harus selesai sebelum akhir bulan',
        ];

        return $this->state(fn (array $attributes) => [
            'deskripsi' => $this->faker->randomElement($descriptions),
        ]);
    }

    /**
     * Indicate that the lembur is for specific karyawan.
     */
    public function forKaryawan(Karyawan $karyawan): static
    {
        return $this->state(fn (array $attributes) => [
            'karyawan_id' => $karyawan->id,
        ]);
    }

    /**
     * Indicate that the lembur is created by specific user.
     */
    public function createdBy(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'created_by' => $user->id,
        ]);
    }

    /**
     * Indicate that the lembur is for specific date.
     */
    public function onDate(string $date): static
    {
        return $this->state(fn (array $attributes) => [
            'tanggal' => $date,
        ]);
    }
}
