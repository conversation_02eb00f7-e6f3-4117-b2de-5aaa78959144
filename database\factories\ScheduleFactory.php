<?php

namespace Database\Factories;

use App\Models\Schedule;
use App\Models\Karyawan;
use App\Models\Shift;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

class ScheduleFactory extends Factory
{
    protected $model = Schedule::class;

    public function definition(): array
    {
        return [
            'karyawan_id' => Karyawan::factory(),
            'shift_id' => Shift::factory(),
            'tanggal_jadwal' => $this->faker->dateTimeBetween('-1 month', '+1 month')->format('Y-m-d'),
            'status' => $this->faker->randomElement(['scheduled', 'completed', 'cancelled']),
            'keterangan' => $this->faker->optional()->sentence(),
        ];
    }

    public function today(): static
    {
        return $this->state(fn (array $attributes) => [
            'tanggal_jadwal' => Carbon::today()->format('Y-m-d'),
        ]);
    }

    public function tomorrow(): static
    {
        return $this->state(fn (array $attributes) => [
            'tanggal_jadwal' => Carbon::tomorrow()->format('Y-m-d'),
        ]);
    }

    public function yesterday(): static
    {
        return $this->state(fn (array $attributes) => [
            'tanggal_jadwal' => Carbon::yesterday()->format('Y-m-d'),
        ]);
    }

    public function scheduled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'scheduled',
        ]);
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
        ]);
    }

    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
        ]);
    }
}
