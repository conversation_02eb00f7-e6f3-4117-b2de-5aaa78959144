<?php

namespace Database\Factories;

use App\Models\Shift;
use Illuminate\Database\Eloquent\Factories\Factory;

class ShiftFactory extends Factory
{
    protected $model = Shift::class;

    public function definition(): array
    {
        $shifts = [
            ['nama' => 'Pagi', 'mulai' => '08:00:00', 'selesai' => '17:00:00'],
            ['nama' => 'Siang', 'mulai' => '13:00:00', 'selesai' => '22:00:00'],
            ['nama' => 'Malam', 'mulai' => '22:00:00', 'selesai' => '06:00:00'],
        ];

        $shift = $this->faker->randomElement($shifts);

        return [
            'nama_shift' => $shift['nama'],
            'waktu_mulai' => $shift['mulai'],
            'waktu_selesai' => $shift['selesai'],
            'toleransi_keterlambatan' => $this->faker->numberBetween(0, 30),
            'keterangan' => $this->faker->sentence(),
            'is_active' => true,
            'created_by' => 1,
        ];
    }

    public function morning(): static
    {
        return $this->state(fn(array $attributes) => [
            'nama_shift' => 'Pagi',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
        ]);
    }

    public function afternoon(): static
    {
        return $this->state(fn(array $attributes) => [
            'nama_shift' => 'Siang',
            'waktu_mulai' => '13:00:00',
            'waktu_selesai' => '22:00:00',
        ]);
    }

    public function night(): static
    {
        return $this->state(fn(array $attributes) => [
            'nama_shift' => 'Malam',
            'waktu_mulai' => '22:00:00',
            'waktu_selesai' => '06:00:00',
        ]);
    }
}
