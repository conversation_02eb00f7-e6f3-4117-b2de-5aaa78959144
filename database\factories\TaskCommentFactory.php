<?php

namespace Database\Factories;

use App\Models\TaskComment;
use App\Models\Task;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TaskComment>
 */
class TaskCommentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = TaskComment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'task_id' => Task::factory(),
            'user_id' => User::factory(),
            'parent_id' => null,
            'comment' => $this->faker->paragraph(2),
            'attachments' => null,
        ];
    }

    /**
     * Indicate that the comment is for a specific task.
     */
    public function forTask(Task $task): static
    {
        return $this->state(fn(array $attributes) => [
            'task_id' => $task->id,
        ]);
    }

    /**
     * Indicate that the comment is by a specific user.
     */
    public function byUser(User $user): static
    {
        return $this->state(fn(array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Indicate that the comment is a reply to another comment.
     */
    public function replyTo(TaskComment $parentComment): static
    {
        return $this->state(fn(array $attributes) => [
            'parent_id' => $parentComment->id,
            'task_id' => $parentComment->task_id,
        ]);
    }

    /**
     * Indicate that the comment has specific content.
     */
    public function withContent(string $content): static
    {
        return $this->state(fn(array $attributes) => [
            'comment' => $content,
        ]);
    }

    /**
     * Indicate that the comment has attachments.
     */
    public function withAttachments(array $attachments): static
    {
        return $this->state(fn(array $attributes) => [
            'attachments' => $attachments,
        ]);
    }
}
