<?php

namespace Database\Factories;

use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\Factory;

class WarehouseFactory extends Factory
{
    protected $model = Warehouse::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->company . ' Warehouse',
            'code' => 'WH-' . $this->faker->unique()->numerify('###'),
            'address' => $this->faker->address,
            'phone' => $this->faker->phoneNumber,
            'manager_name' => $this->faker->name,
            'is_active' => true,
            'created_by' => 1,
        ];
    }
}
