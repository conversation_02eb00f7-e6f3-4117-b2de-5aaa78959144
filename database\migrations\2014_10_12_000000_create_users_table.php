d:\laragon\www\erp\database\migrations\2025_06_07_063425_create_activity_log_table.php d:\laragon\www\erp\database\migrations\2025_06_07_063426_add_event_column_to_activity_log_table.php d:\laragon\www\erp\database\migrations\2025_06_07_063427_add_batch_uuid_column_to_activity_log_table.php<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
