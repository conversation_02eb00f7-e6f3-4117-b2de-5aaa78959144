<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('sop_dokumens', function (Blueprint $table) {
            $table->id();
            $table->string('judul_sop');
            $table->text('deskripsi')->nullable();
            $table->string('file_path');
            $table->enum('scope_type', ['departemen', 'divisi']); // Menentukan apakah SOP untuk departemen atau divisi
            $table->unsignedBigInteger('departemen_id')->nullable(); // Jika scope_type = departemen
            $table->unsignedBigInteger('divisi_id')->nullable(); // Jika scope_type = divisi
            $table->enum('status', ['aktif', 'tidak_aktif'])->default('aktif');
            $table->date('tanggal_berlaku')->nullable();
            $table->date('tanggal_berakhir')->nullable();
            $table->string('versi', 10)->default('1.0');
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
            $table->softDeletes();

            // Indexes untuk performa
            $table->index(['scope_type', 'departemen_id']);
            $table->index(['scope_type', 'divisi_id']);
            $table->index(['status', 'tanggal_berlaku']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('sop_dokumens');
    }
};
