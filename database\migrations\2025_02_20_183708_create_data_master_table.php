<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Tabel Akun Keuangan
        Schema::create('akun', function (Blueprint $table) {
            $table->id();
            $table->string('kode_akun');
            $table->string('nama_akun');
            $table->enum('kategori_akun', ['Aset', 'Kewajiban', 'Ekuitas', 'Pendapatan', 'Beban']);
            $table->enum('tipe_akun', ['Debit', 'Kredit']);
            $table->decimal('saldo_awal', 10, 2)->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Tabel Data Satuan
        Schema::create('satuan', function (Blueprint $table) {
            $table->id();
            $table->string('nama_satuan');
            $table->text('deskripsi_satuan')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Tabel Data Divisi
        Schema::create('divisi', function (Blueprint $table) {
            $table->id();
            $table->string('nama_divisi');
            $table->text('deskripsi')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Tabel Data Jabatan
        Schema::create('jabatan', function (Blueprint $table) {
            $table->id();
            $table->string('nama_jabatan');
            $table->text('deskripsi')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Tabel Aset
        Schema::create('aset', function (Blueprint $table) {
            $table->id();
            $table->string('kode_aset');
            $table->string('nama_aset');
            $table->text('deskripsi_aset')->nullable();
            $table->enum('kategori_aset', ['Tanah', 'Bangunan', 'Peralatan', 'Transportasi', 'Lainnya']);
            $table->decimal('nilai_aset', 10, 2);
            $table->dateTime('tanggal_akuisisi');
            $table->integer('status_aset')->nullable();
            $table->unsignedBigInteger('id_akun')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Tabel Karyawan
        Schema::create('karyawan', function (Blueprint $table) {
            $table->id();
            $table->string('nama_lengkap')->nullable();
            $table->string('nip')->nullable();
            $table->string('nik')->nullable();
            $table->string('kota_lahir')->nullable();
            $table->date('tanggal_lahir')->nullable();
            $table->text('alamat')->nullable();
            $table->string('nomor_telepon')->nullable();
            $table->unsignedBigInteger('id_jabatan')->nullable();
            $table->unsignedBigInteger('id_divisi')->nullable();
            $table->unsignedBigInteger('id_user')->nullable();
            $table->integer('status_aktif')->default(0);
            $table->string('foto_profil')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Tabel Supplier
        Schema::create('supplier', function (Blueprint $table) {
            $table->id();
            $table->string('nama');
            $table->string('nama_perusahaan');
            $table->string('email');
            $table->string('nomor_handphone');
            $table->string('npwp');
            $table->text('alamat');
            $table->text('info_lainnya')->nullable();
            $table->string('akun_bank');
            $table->string('nama_bank');
            $table->string('kantor_cabang_bank');
            $table->string('nomor_rekening');
            $table->string('pemegang_akun_bank');
            $table->unsignedBigInteger('id_akun_hutang');
            $table->string('syarat_pembayaran_utama');
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
            $table->softDeletes();
        });

        // Tabel Kategori Produk
        Schema::create('produk_kategori', function (Blueprint $table) {
            $table->id();
            $table->string('nama')->nullable();
            $table->text('deskripsi')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
            $table->softDeletes();
        });

        // Tabel Produk
        Schema::create('produk', function (Blueprint $table) {
            $table->id();
            $table->string('kode')->nullable();
            $table->string('nama')->nullable();
            $table->text('deskripsi')->nullable();
            $table->decimal('unit_cost', 12, 2)->nullable(); // Harga pokok per unit
            $table->decimal('selling_price', 12, 2)->nullable(); // Harga jual per unit
            $table->unsignedBigInteger('id_produk_kategori')->nullable();
            $table->unsignedBigInteger('id_satuan')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Tabel Tambahan SDM
        Schema::create('riwayat_kontrak', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('karyawan_id');
            $table->string('jenis_kontrak');
            $table->date('tgl_mulai');
            $table->date('tgl_selesai')->nullable();
            $table->text('keterangan')->nullable();
            $table->boolean('is_active')->default(0);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('penggajian_karyawan', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('karyawan_id');
            $table->decimal('gaji_pokok', 12, 2)->nullable();
            $table->decimal('tunjangan_jabatan', 12, 2)->nullable();
            $table->decimal('tunjangan_umum', 12, 2)->nullable();
            $table->decimal('tunjangan_sembako', 12, 2)->nullable();
            $table->decimal('bpjs_kesehatan_dipotong', 12, 2)->nullable();
            $table->decimal('take_home_pay', 12, 2)->nullable();
            $table->string('periode_gaji');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('kerabat_darurat', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('karyawan_id');
            $table->string('nama_kerabat');
            $table->string('hubungan');
            $table->string('no_hp_kerabat');
            $table->text('alamat_kerabat')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('pendidikan_karyawan', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('karyawan_id');
            $table->string('tingkat');
            $table->string('institusi');
            $table->string('jurusan');
            $table->year('tahun_lulus')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('resign_log', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('karyawan_id');
            $table->date('tanggal_resign');
            $table->text('alasan_resign');
            $table->text('catatan')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('karyawan_bpjs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('karyawan_id');
            $table->string('bpjs_kes')->nullable();
            $table->string('bpjs_tk')->nullable();
            $table->string('npwp')->nullable();
            $table->string('rekening')->nullable();
            $table->string('bank')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Tabel yang memiliki FK ke karyawan
        Schema::dropIfExists('karyawan_bpjs');
        Schema::dropIfExists('resign_log');
        Schema::dropIfExists('pendidikan_karyawan');
        Schema::dropIfExists('kerabat_darurat');
        Schema::dropIfExists('penggajian_karyawan');
        Schema::dropIfExists('riwayat_kontrak');

        // Tabel master lainnya
        Schema::dropIfExists('produk');
        Schema::dropIfExists('produk_kategori');
        Schema::dropIfExists('supplier');
        Schema::dropIfExists('karyawan');
        Schema::dropIfExists('aset');
        Schema::dropIfExists('jabatan');
        Schema::dropIfExists('divisi');
        Schema::dropIfExists('satuan');
        Schema::dropIfExists('akun');
    }
};
