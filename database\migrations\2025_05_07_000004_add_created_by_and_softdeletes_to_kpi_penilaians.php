<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (Schema::hasTable('kpi_penilaians')) {
            Schema::table('kpi_penilaians', function (Blueprint $table) {
                if (!Schema::hasColumn('kpi_penilaians', 'created_by')) {
                    $table->unsignedBigInteger('created_by')->nullable()->after('keterangan');
                }
                if (!Schema::hasColumn('kpi_penilaians', 'deleted_at')) {
                    $table->softDeletes();
                }
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasTable('kpi_penilaians')) {
            Schema::table('kpi_penilaians', function (Blueprint $table) {
                if (Schema::hasColumn('kpi_penilaians', 'created_by')) {
                    $table->dropColumn('created_by');
                }
                if (Schema::hasColumn('kpi_penilaians', 'deleted_at')) {
                    $table->dropSoftDeletes();
                }
            });
        }
    }
};
