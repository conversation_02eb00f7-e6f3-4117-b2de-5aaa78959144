<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('karyawan', function (Blueprint $table) {
            $table->string('nomor_kk')->nullable()->after('nik');            // Nomor Kartu Keluarga
            $table->string('agama')->nullable()->after('tanggal_lahir');     // Agama
            $table->enum('jenis_kelamin', ['Laki-laki', 'Perempuan'])->nullable()->after('agama');  // Jenis <PERSON>
            $table->enum('status_pernikahan', ['Belum Menikah', 'Menikah', 'Cerai'])->nullable()->after('jenis_kelamin'); // Status Pernikahan
            $table->integer('jumlah_anak')->nullable()->after('status_pernikahan');   // Jumlah Anak
            $table->text('alamat_ktp')->nullable()->after('alamat');         // Alamat pada KTP
            $table->string('nama_ibu_kandung')->nullable()->after('alamat_ktp');  // Nama Ibu Kandung
            $table->string('golongan_darah')->nullable()->after('nama_ibu_kandung');  // Golongan Darah
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('karyawan', function (Blueprint $table) {
            $table->dropColumn([
                'nomor_kk',
                'agama',
                'jenis_kelamin',
                'status_pernikahan',
                'jumlah_anak',
                'alamat_ktp',
                'nama_ibu_kandung',
                'golongan_darah',
            ]);
        });
    }
};
