<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('riwayat_kontrak', function (Blueprint $table) {
            $table->date('tanggal_mulai_kerja')->nullable()->after('tgl_selesai');
            $table->date('tgl_selesai')->nullable()->change(); // Mengubah tgl_selesai menjadi nullable
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('riwayat_kontrak', function (Blueprint $table) {
            $table->dropColumn('tanggal_mulai_kerja');
            $table->date('tgl_selesai')->nullable(false)->change();
        });
    }
};
