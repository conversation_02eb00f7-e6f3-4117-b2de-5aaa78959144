<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('penggajian_karyawan', function (Blueprint $table) {
            $table->string('no_penggajian')->nullable()->unique()->after('id');
            $table->decimal('bpjs_tk_dipotong', 12, 2)->nullable()->after('bpjs_kesehatan_dipotong');
            $table->decimal('potongan_lainnya', 12, 2)->nullable()->after('bpjs_tk_dipotong');
            $table->text('keterangan')->nullable()->after('potongan_lainnya');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('penggajian_karyawan', function (Blueprint $table) {
            $table->dropColumn(['no_penggajian', 'bpjs_tk_dipotong', 'potongan_lainnya', 'keterangan']);
        });
    }
};
