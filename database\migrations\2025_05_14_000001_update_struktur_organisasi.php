<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Buat tabel entitas
        Schema::create('entitas', function (Blueprint $table) {
            $table->id();
            $table->string('nama');
            $table->text('alamat')->nullable();
            $table->text('keterangan')->nullable();
            $table->timestamps();
        });

        // Buat tabel departemen
        Schema::create('departemen', function (Blueprint $table) {
            $table->id();
            $table->string('nama_departemen');
            $table->unsignedBigInteger('entitas_id'); // tanpa foreign constraint
            $table->timestamps();
        });

        // Tambah departemen_id ke tabel divisi
        Schema::table('divisi', function (Blueprint $table) {
            $table->unsignedBigInteger('departemen_id')->nullable()->after('nama_divisi');
        });

        // Tambah divisi_id ke tabel jabatan
        Schema::table('jabatan', function (Blueprint $table) {
            $table->unsignedBigInteger('divisi_id')->nullable()->after('nama_jabatan');
        });
    }

    public function down(): void
    {
        Schema::table('divisi', function (Blueprint $table) {
            $table->dropColumn('departemen_id');
        });

        Schema::table('jabatan', function (Blueprint $table) {
            $table->dropColumn('divisi_id');
        });

        Schema::dropIfExists('departemen');
        Schema::dropIfExists('entitas');
    }
};
