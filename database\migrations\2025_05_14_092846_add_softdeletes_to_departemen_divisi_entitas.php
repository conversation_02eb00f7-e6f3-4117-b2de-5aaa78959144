<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (!Schema::hasColumn('departemen', 'deleted_at')) {
            Schema::table('departemen', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        if (!Schema::hasColumn('divisi', 'deleted_at')) {
            Schema::table('divisi', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        if (!Schema::hasColumn('entitas', 'deleted_at')) {
            Schema::table('entitas', function (Blueprint $table) {
                $table->softDeletes();
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasColumn('departemen', 'deleted_at')) {
            Schema::table('departemen', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        }

        if (Schema::hasColumn('divisi', 'deleted_at')) {
            Schema::table('divisi', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        }

        if (Schema::hasColumn('entitas', 'deleted_at')) {
            Schema::table('entitas', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        }
    }
};
