<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('mutasi_promosi_demosi', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('karyawan_id');
            $table->enum('tipe', ['promosi', 'demosi', 'mutasi', 'posisi_awal']);
            $table->unsignedBigInteger('entitas_id');
            $table->unsignedBigInteger('departemen_id');
            $table->unsignedBigInteger('divisi_id');
            $table->unsignedBigInteger('jabatan_id');
            $table->boolean('is_active')->default(false);
            $table->date('tanggal_efektif');
            $table->text('alasan')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('mutasi_promosi_demosi');
    }
};
