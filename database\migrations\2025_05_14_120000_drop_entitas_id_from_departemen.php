<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (Schema::hasColumn('departemen', 'entitas_id')) {
            Schema::table('departemen', function (Blueprint $table) {
                $table->dropColumn('entitas_id');
            });
        }
    }

    public function down(): void
    {
        Schema::table('departemen', function (Blueprint $table) {
            $table->unsignedBigInteger('entitas_id')->nullable();
        });
    }
};
