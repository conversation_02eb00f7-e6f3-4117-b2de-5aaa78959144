<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kpi_penilaians', function (Blueprint $table) {
            $table->id();
            $table->foreignId('karyawan_id')->constrained('karyawan')->onDelete('cascade');
            $table->string('periode'); // Format: YYYY-MM
            $table->decimal('target_kpi', 5, 2)->default(0); // Target KPI dalam persen
            $table->decimal('realisasi_kpi', 5, 2)->default(0); // Realisasi KPI dalam persen
            $table->enum('nilai_akhir', ['A', 'B', 'C', 'D'])->nullable();
            $table->enum('status_penilaian', ['Draft', 'Proses', 'Selesai'])->default('Draft');
            $table->foreignId('penilai_id')->nullable()->constrained('users')->onDelete('set null');
            $table->date('tanggal_penilaian')->nullable();
            $table->json('kategori_penilaian')->nullable(); // Detail breakdown penilaian
            $table->text('keterangan')->nullable();
            $table->timestamps();

            // Index untuk performa
            $table->index(['karyawan_id', 'periode']);
            $table->index('status_penilaian');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kpi_penilaians');
    }
};
