<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('kpi_penilaians', function (Blueprint $table) {
            // Add created_by column if it doesn't exist
            if (!Schema::hasColumn('kpi_penilaians', 'created_by')) {
                $table->unsignedBigInteger('created_by')->nullable()->after('keterangan');
            }

            // Add deleted_at column if it doesn't exist
            if (!Schema::hasColumn('kpi_penilaians', 'deleted_at')) {
                $table->softDeletes();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('kpi_penilaians', function (Blueprint $table) {
            if (Schema::hasColumn('kpi_penilaians', 'created_by')) {
                $table->dropColumn('created_by');
            }

            if (Schema::hasColumn('kpi_penilaians', 'deleted_at')) {
                $table->dropSoftDeletes();
            }
        });
    }
};
