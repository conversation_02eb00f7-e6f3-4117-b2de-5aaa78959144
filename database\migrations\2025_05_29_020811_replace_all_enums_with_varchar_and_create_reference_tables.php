<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Create reference tables for enum values
        $this->createReferenceTable('kpi_nilai_akhir', [
            ['kode' => 'A', 'nama' => 'Sangat Baik', 'deskripsi' => 'Pencapaian sangat memuaskan'],
            ['kode' => 'B', 'nama' => 'Baik', 'deskripsi' => 'Pencapaian baik'],
            ['kode' => 'C', 'nama' => 'Cukup', 'deskripsi' => 'Pencapaian cukup'],
            ['kode' => 'D', 'nama' => 'Kurang', 'deskripsi' => 'Pencapaian kurang'],
        ]);

        $this->createReferenceTable('kpi_status_penilaian', [
            ['kode' => 'Draft', 'nama' => 'Draft', 'deskripsi' => 'Penilaian masih dalam tahap draft'],
            ['kode' => 'Proses', 'nama' => 'Proses', 'deskripsi' => 'Penilaian sedang dalam proses'],
            ['kode' => 'Selesai', 'nama' => 'Selesai', 'deskripsi' => 'Penilaian sudah selesai'],
        ]);

        $this->createReferenceTable('mutasi_tipe', [
            ['kode' => 'promosi', 'nama' => 'Promosi', 'deskripsi' => 'Kenaikan jabatan'],
            ['kode' => 'demosi', 'nama' => 'Demosi', 'deskripsi' => 'Penurunan jabatan'],
            ['kode' => 'mutasi', 'nama' => 'Mutasi', 'deskripsi' => 'Perpindahan posisi'],
            ['kode' => 'posisi_awal', 'nama' => 'Posisi Awal', 'deskripsi' => 'Posisi awal karyawan'],
        ]);

        $this->createReferenceTable('sop_scope_type', [
            ['kode' => 'departemen', 'nama' => 'Departemen', 'deskripsi' => 'SOP berlaku untuk departemen'],
            ['kode' => 'divisi', 'nama' => 'Divisi', 'deskripsi' => 'SOP berlaku untuk divisi'],
        ]);

        $this->createReferenceTable('sop_status', [
            ['kode' => 'aktif', 'nama' => 'Aktif', 'deskripsi' => 'SOP sedang aktif'],
            ['kode' => 'tidak_aktif', 'nama' => 'Tidak Aktif', 'deskripsi' => 'SOP tidak aktif'],
        ]);

        $this->createReferenceTable('akun_kategori', [
            ['kode' => 'Aset', 'nama' => 'Aset', 'deskripsi' => 'Kategori akun aset'],
            ['kode' => 'Kewajiban', 'nama' => 'Kewajiban', 'deskripsi' => 'Kategori akun kewajiban'],
            ['kode' => 'Ekuitas', 'nama' => 'Ekuitas', 'deskripsi' => 'Kategori akun ekuitas'],
            ['kode' => 'Pendapatan', 'nama' => 'Pendapatan', 'deskripsi' => 'Kategori akun pendapatan'],
            ['kode' => 'Beban', 'nama' => 'Beban', 'deskripsi' => 'Kategori akun beban'],
        ]);

        $this->createReferenceTable('akun_tipe', [
            ['kode' => 'Debit', 'nama' => 'Debit', 'deskripsi' => 'Tipe akun debit'],
            ['kode' => 'Kredit', 'nama' => 'Kredit', 'deskripsi' => 'Tipe akun kredit'],
        ]);

        $this->createReferenceTable('aset_kategori', [
            ['kode' => 'Tanah', 'nama' => 'Tanah', 'deskripsi' => 'Kategori aset tanah'],
            ['kode' => 'Bangunan', 'nama' => 'Bangunan', 'deskripsi' => 'Kategori aset bangunan'],
            ['kode' => 'Peralatan', 'nama' => 'Peralatan', 'deskripsi' => 'Kategori aset peralatan'],
            ['kode' => 'Transportasi', 'nama' => 'Transportasi', 'deskripsi' => 'Kategori aset transportasi'],
            ['kode' => 'Lainnya', 'nama' => 'Lainnya', 'deskripsi' => 'Kategori aset lainnya'],
        ]);

        $this->createReferenceTable('jenis_kelamin', [
            ['kode' => 'Laki-laki', 'nama' => 'Laki-laki', 'deskripsi' => 'Jenis kelamin laki-laki'],
            ['kode' => 'Perempuan', 'nama' => 'Perempuan', 'deskripsi' => 'Jenis kelamin perempuan'],
        ]);

        $this->createReferenceTable('status_pernikahan', [
            ['kode' => 'Belum Menikah', 'nama' => 'Belum Menikah', 'deskripsi' => 'Status belum menikah'],
            ['kode' => 'Menikah', 'nama' => 'Menikah', 'deskripsi' => 'Status menikah'],
            ['kode' => 'Cerai', 'nama' => 'Cerai', 'deskripsi' => 'Status cerai'],
        ]);

        $this->createReferenceTable('absensi_status', [
            ['kode' => 'hadir', 'nama' => 'Hadir', 'deskripsi' => 'Status hadir'],
            ['kode' => 'terlambat', 'nama' => 'Terlambat', 'deskripsi' => 'Status terlambat'],
            ['kode' => 'izin', 'nama' => 'Izin', 'deskripsi' => 'Status izin'],
            ['kode' => 'sakit', 'nama' => 'Sakit', 'deskripsi' => 'Status sakit'],
            ['kode' => 'cuti', 'nama' => 'Cuti', 'deskripsi' => 'Status cuti'],
            ['kode' => 'alpha', 'nama' => 'Alpha', 'deskripsi' => 'Status alpha/tidak hadir tanpa keterangan'],
        ]);

        $this->createReferenceTable('user_role', [
            ['kode' => 'admin', 'nama' => 'Administrator', 'deskripsi' => 'Role administrator sistem'],
            ['kode' => 'supervisor', 'nama' => 'Supervisor', 'deskripsi' => 'Role supervisor'],
            ['kode' => 'karyawan', 'nama' => 'Karyawan', 'deskripsi' => 'Role karyawan'],
        ]);

        // 2. Convert enum columns to varchar
        $this->convertEnumToVarchar();
    }

    /**
     * Create reference table for enum values
     */
    private function createReferenceTable(string $tableName, array $data): void
    {
        if (!Schema::hasTable($tableName)) {
            Schema::create($tableName, function (Blueprint $table) {
                $table->id();
                $table->string('kode', 50)->unique();
                $table->string('nama', 100);
                $table->text('deskripsi')->nullable();
                $table->boolean('is_active')->default(true);
                $table->integer('sort_order')->default(0);
                $table->timestamps();
            });

            // Insert default data
            foreach ($data as $index => $item) {
                DB::table($tableName)->insert([
                    'kode' => $item['kode'],
                    'nama' => $item['nama'],
                    'deskripsi' => $item['deskripsi'],
                    'is_active' => true,
                    'sort_order' => $index + 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    /**
     * Convert enum columns to varchar
     */
    private function convertEnumToVarchar(): void
    {
        // KPI Penilaians
        if (Schema::hasTable('kpi_penilaians')) {
            Schema::table('kpi_penilaians', function (Blueprint $table) {
                $table->string('nilai_akhir', 50)->nullable()->change();
                $table->string('status_penilaian', 50)->default('Draft')->change();
            });
        }

        // Mutasi Promosi Demosi
        if (Schema::hasTable('mutasi_promosi_demosi')) {
            Schema::table('mutasi_promosi_demosi', function (Blueprint $table) {
                $table->string('tipe', 50)->change();
            });
        }

        // SOP Dokumens
        if (Schema::hasTable('sop_dokumens')) {
            Schema::table('sop_dokumens', function (Blueprint $table) {
                $table->string('scope_type', 50)->change();
                $table->string('status', 50)->default('aktif')->change();
            });
        }

        // Akun
        if (Schema::hasTable('akun')) {
            Schema::table('akun', function (Blueprint $table) {
                $table->string('kategori_akun', 50)->change();
                $table->string('tipe_akun', 50)->change();
            });
        }

        // Aset
        if (Schema::hasTable('aset')) {
            Schema::table('aset', function (Blueprint $table) {
                $table->string('kategori_aset', 50)->change();
            });
        }

        // Karyawan
        if (Schema::hasTable('karyawan')) {
            Schema::table('karyawan', function (Blueprint $table) {
                $table->string('jenis_kelamin', 50)->nullable()->change();
                $table->string('status_pernikahan', 50)->nullable()->change();
            });
        }

        // Absensi
        if (Schema::hasTable('absensi')) {
            Schema::table('absensi', function (Blueprint $table) {
                $table->string('status', 50)->default('hadir')->change();
            });
        }

        // Users - Add role column if it doesn't exist
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                if (!Schema::hasColumn('users', 'role')) {
                    $table->string('role', 50)->default('karyawan')->after('password');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop reference tables
        $referenceTables = [
            'kpi_nilai_akhir',
            'kpi_status_penilaian',
            'mutasi_tipe',
            'sop_scope_type',
            'sop_status',
            'akun_kategori',
            'akun_tipe',
            'aset_kategori',
            'jenis_kelamin',
            'status_pernikahan',
            'absensi_status',
            'user_role',
        ];

        foreach ($referenceTables as $table) {
            Schema::dropIfExists($table);
        }

        // Note: Reverting varchar back to enum would require recreating the columns
        // which is complex and risky. It's better to keep varchar for flexibility.
    }
};
