<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Helper method to add index if it doesn't exist
     */
    private function addIndexIfNotExists(string $tableName, string $column, string $indexName): void
    {
        $exists = DB::select("SHOW INDEX FROM {$tableName} WHERE Key_name = ?", [$indexName]);
        if (empty($exists)) {
            DB::statement("ALTER TABLE {$tableName} ADD INDEX {$indexName} ({$column})");
        }
    }

    /**
     * Helper method to add composite index if it doesn't exist
     */
    private function addCompositeIndexIfNotExists(string $tableName, array $columns, string $indexName): void
    {
        $exists = DB::select("SHOW INDEX FROM {$tableName} WHERE Key_name = ?", [$indexName]);
        if (empty($exists)) {
            $columnList = implode(', ', $columns);
            DB::statement("ALTER TABLE {$tableName} ADD INDEX {$indexName} ({$columnList})");
        }
    }
    public function up(): void
    {
        // Add indexes for frequently queried columns in karyawan table
        $this->addIndexIfNotExists('karyawan', 'id_entitas', 'idx_karyawan_entitas');
        $this->addIndexIfNotExists('karyawan', 'id_departemen', 'idx_karyawan_departemen');
        $this->addIndexIfNotExists('karyawan', 'id_divisi', 'idx_karyawan_divisi');
        $this->addIndexIfNotExists('karyawan', 'id_jabatan', 'idx_karyawan_jabatan');
        $this->addIndexIfNotExists('karyawan', 'id_user', 'idx_karyawan_user');
        $this->addIndexIfNotExists('karyawan', 'supervisor_id', 'idx_karyawan_supervisor');
        $this->addIndexIfNotExists('karyawan', 'nip', 'idx_karyawan_nip');
        $this->addIndexIfNotExists('karyawan', 'nik', 'idx_karyawan_nik');
        $this->addIndexIfNotExists('karyawan', 'email', 'idx_karyawan_email');
        $this->addIndexIfNotExists('karyawan', 'status_aktif', 'idx_karyawan_status_aktif');

        // Composite indexes
        $this->addCompositeIndexIfNotExists('karyawan', ['id_entitas', 'status_aktif'], 'idx_karyawan_entitas_status');
        $this->addCompositeIndexIfNotExists('karyawan', ['supervisor_id', 'status_aktif'], 'idx_karyawan_supervisor_status');

        // Add indexes for absensi table
        $this->addIndexIfNotExists('absensi', 'karyawan_id', 'idx_absensi_karyawan');
        $this->addIndexIfNotExists('absensi', 'jadwal_id', 'idx_absensi_jadwal');
        $this->addIndexIfNotExists('absensi', 'approved_by', 'idx_absensi_approved_by');
        $this->addIndexIfNotExists('absensi', 'tanggal_absensi', 'idx_absensi_tanggal');
        $this->addIndexIfNotExists('absensi', 'status', 'idx_absensi_status');

        // Composite indexes for common queries
        $this->addCompositeIndexIfNotExists('absensi', ['karyawan_id', 'tanggal_absensi'], 'idx_absensi_karyawan_tanggal');
        $this->addCompositeIndexIfNotExists('absensi', ['tanggal_absensi', 'status'], 'idx_absensi_tanggal_status');
        $this->addCompositeIndexIfNotExists('absensi', ['approved_by', 'approved_at'], 'idx_absensi_approval');

        // Add indexes for jadwal_kerja table
        $this->addIndexIfNotExists('jadwal_kerja', 'karyawan_id', 'idx_jadwal_karyawan');
        $this->addIndexIfNotExists('jadwal_kerja', 'shift_id', 'idx_jadwal_shift');
        $this->addIndexIfNotExists('jadwal_kerja', 'supervisor_id', 'idx_jadwal_supervisor');
        $this->addIndexIfNotExists('jadwal_kerja', 'tanggal_jadwal', 'idx_jadwal_tanggal');
        $this->addIndexIfNotExists('jadwal_kerja', 'status', 'idx_jadwal_status');
        $this->addIndexIfNotExists('jadwal_kerja', 'is_approved', 'idx_jadwal_approved');

        // Composite indexes
        $this->addCompositeIndexIfNotExists('jadwal_kerja', ['karyawan_id', 'tanggal_jadwal'], 'idx_jadwal_karyawan_tanggal');
        $this->addCompositeIndexIfNotExists('jadwal_kerja', ['supervisor_id', 'is_approved'], 'idx_jadwal_supervisor_approved');

        // Add indexes for jadwal_masal table
        $this->addIndexIfNotExists('jadwal_masal', 'shift_id', 'idx_jadwal_masal_shift');
        $this->addIndexIfNotExists('jadwal_masal', 'created_by', 'idx_jadwal_masal_created_by');
        $this->addCompositeIndexIfNotExists('jadwal_masal', ['tanggal_mulai', 'tanggal_selesai'], 'idx_jadwal_masal_periode');

        // Add indexes for jadwal_masal_karyawan pivot table
        $this->addIndexIfNotExists('jadwal_masal_karyawan', 'jadwal_masal_id', 'idx_jmk_jadwal_masal');
        $this->addIndexIfNotExists('jadwal_masal_karyawan', 'karyawan_id', 'idx_jmk_karyawan');

        // Add indexes for other frequently used tables
        $this->addIndexIfNotExists('penggajian_karyawan', 'karyawan_id', 'idx_penggajian_karyawan');
        $this->addIndexIfNotExists('penggajian_karyawan', 'periode_gaji', 'idx_penggajian_periode');
        $this->addCompositeIndexIfNotExists('penggajian_karyawan', ['karyawan_id', 'periode_gaji'], 'idx_penggajian_karyawan_periode');

        $this->addIndexIfNotExists('riwayat_kontrak', 'karyawan_id', 'idx_riwayat_kontrak_karyawan');
        $this->addIndexIfNotExists('riwayat_kontrak', 'tgl_mulai', 'idx_riwayat_kontrak_mulai');
        $this->addIndexIfNotExists('riwayat_kontrak', 'tgl_selesai', 'idx_riwayat_kontrak_selesai');

        $this->addIndexIfNotExists('kpi_penilaians', 'karyawan_id', 'idx_kpi_karyawan');
        $this->addIndexIfNotExists('kpi_penilaians', 'periode', 'idx_kpi_periode');
        $this->addCompositeIndexIfNotExists('kpi_penilaians', ['karyawan_id', 'periode'], 'idx_kpi_karyawan_periode');

        $this->addIndexIfNotExists('dokumens', 'karyawan_id', 'idx_dokumen_karyawan');
        $this->addIndexIfNotExists('dokumens', 'created_by', 'idx_dokumen_created_by');

        $this->addIndexIfNotExists('pelanggarans', 'karyawan_id', 'idx_pelanggaran_karyawan');
        $this->addIndexIfNotExists('pelanggarans', 'tanggal', 'idx_pelanggaran_tanggal');

        // Add indexes for master data tables
        $this->addIndexIfNotExists('divisi', 'departemen_id', 'idx_divisi_departemen');
        $this->addIndexIfNotExists('shift', 'nama_shift', 'idx_shift_nama');
    }

    public function down(): void
    {
        // Drop indexes in reverse order
        Schema::table('shift', function (Blueprint $table) {
            $table->dropIndex('idx_shift_nama');
        });

        Schema::table('divisi', function (Blueprint $table) {
            $table->dropIndex('idx_divisi_departemen');
        });

        Schema::table('pelanggarans', function (Blueprint $table) {
            $table->dropIndex('idx_pelanggaran_karyawan');
            $table->dropIndex('idx_pelanggaran_tanggal');
        });

        Schema::table('dokumens', function (Blueprint $table) {
            $table->dropIndex('idx_dokumen_karyawan');
            $table->dropIndex('idx_dokumen_created_by');
        });

        Schema::table('kpi_penilaians', function (Blueprint $table) {
            $table->dropIndex('idx_kpi_karyawan');
            $table->dropIndex('idx_kpi_periode');
            $table->dropIndex('idx_kpi_karyawan_periode');
        });

        Schema::table('riwayat_kontrak', function (Blueprint $table) {
            $table->dropIndex('idx_riwayat_kontrak_karyawan');
            $table->dropIndex('idx_riwayat_kontrak_mulai');
            $table->dropIndex('idx_riwayat_kontrak_selesai');
        });

        Schema::table('penggajian_karyawan', function (Blueprint $table) {
            $table->dropIndex('idx_penggajian_karyawan');
            $table->dropIndex('idx_penggajian_periode');
            $table->dropIndex('idx_penggajian_karyawan_periode');
        });

        Schema::table('jadwal_masal_karyawan', function (Blueprint $table) {
            $table->dropIndex('idx_jmk_jadwal_masal');
            $table->dropIndex('idx_jmk_karyawan');
        });

        Schema::table('jadwal_masal', function (Blueprint $table) {
            $table->dropIndex('idx_jadwal_masal_shift');
            $table->dropIndex('idx_jadwal_masal_created_by');
            $table->dropIndex('idx_jadwal_masal_periode');
        });

        Schema::table('jadwal_kerja', function (Blueprint $table) {
            $table->dropIndex('idx_jadwal_karyawan');
            $table->dropIndex('idx_jadwal_shift');
            $table->dropIndex('idx_jadwal_supervisor');
            $table->dropIndex('idx_jadwal_tanggal');
            $table->dropIndex('idx_jadwal_status');
            $table->dropIndex('idx_jadwal_approved');
            $table->dropIndex('idx_jadwal_karyawan_tanggal');
            $table->dropIndex('idx_jadwal_supervisor_approved');
        });

        Schema::table('absensi', function (Blueprint $table) {
            $table->dropIndex('idx_absensi_karyawan');
            $table->dropIndex('idx_absensi_jadwal');
            $table->dropIndex('idx_absensi_approved_by');
            $table->dropIndex('idx_absensi_tanggal');
            $table->dropIndex('idx_absensi_status');
            $table->dropIndex('idx_absensi_karyawan_tanggal');
            $table->dropIndex('idx_absensi_tanggal_status');
            $table->dropIndex('idx_absensi_approval');
        });

        Schema::table('karyawan', function (Blueprint $table) {
            $table->dropIndex('idx_karyawan_entitas');
            $table->dropIndex('idx_karyawan_departemen');
            $table->dropIndex('idx_karyawan_divisi');
            $table->dropIndex('idx_karyawan_jabatan');
            $table->dropIndex('idx_karyawan_user');
            $table->dropIndex('idx_karyawan_supervisor');
            $table->dropIndex('idx_karyawan_nip');
            $table->dropIndex('idx_karyawan_nik');
            $table->dropIndex('idx_karyawan_email');
            $table->dropIndex('idx_karyawan_status_aktif');
            $table->dropIndex('idx_karyawan_entitas_status');
            $table->dropIndex('idx_karyawan_supervisor_status');
        });
    }
};
