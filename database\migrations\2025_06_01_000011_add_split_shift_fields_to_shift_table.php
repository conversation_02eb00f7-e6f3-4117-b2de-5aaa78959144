<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('shift', function (Blueprint $table) {
            // Check if columns exist before adding them
            if (!Schema::hasColumn('shift', 'is_split_shift')) {
                $table->boolean('is_split_shift')->default(false)->after('toleransi_keterlambatan');
            }
            
            if (!Schema::hasColumn('shift', 'waktu_mulai_periode2')) {
                $table->time('waktu_mulai_periode2')->nullable()->after('is_split_shift');
            }
            
            if (!Schema::hasColumn('shift', 'waktu_selesai_periode2')) {
                $table->time('waktu_selesai_periode2')->nullable()->after('waktu_mulai_periode2');
            }
            
            if (!Schema::hasColumn('shift', 'toleransi_keterlambatan_periode2')) {
                $table->integer('toleransi_keterlambatan_periode2')->nullable()->after('waktu_selesai_periode2');
            }
        });
    }

    public function down(): void
    {
        Schema::table('shift', function (Blueprint $table) {
            // Check if columns exist before dropping them
            if (Schema::hasColumn('shift', 'is_split_shift')) {
                $table->dropColumn('is_split_shift');
            }
            
            if (Schema::hasColumn('shift', 'waktu_mulai_periode2')) {
                $table->dropColumn('waktu_mulai_periode2');
            }
            
            if (Schema::hasColumn('shift', 'waktu_selesai_periode2')) {
                $table->dropColumn('waktu_selesai_periode2');
            }
            
            if (Schema::hasColumn('shift', 'toleransi_keterlambatan_periode2')) {
                $table->dropColumn('toleransi_keterlambatan_periode2');
            }
        });
    }
};
