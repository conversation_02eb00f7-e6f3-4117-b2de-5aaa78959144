<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('entitas', function (Blueprint $table) {
            $table->decimal('latitude', 10, 8)->nullable()->after('alamat')
                ->comment('Latitude koordinat entitas untuk geofencing absensi');
            
            $table->decimal('longitude', 11, 8)->nullable()->after('latitude')
                ->comment('Longitude koordinat entitas untuk geofencing absensi');
            
            $table->integer('radius')->default(100)->after('longitude')
                ->comment('Radius dalam meter yang diperbolehkan untuk absensi');
            
            $table->boolean('enable_geofencing')->default(true)->after('radius')
                ->comment('Aktifkan/nonaktifkan geofencing untuk entitas ini');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('entitas', function (Blueprint $table) {
            $table->dropColumn([
                'latitude',
                'longitude', 
                'radius',
                'enable_geofencing'
            ]);
        });
    }
};
