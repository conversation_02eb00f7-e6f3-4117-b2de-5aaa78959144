<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jenis_pelanggaran', function (Blueprint $table) {
            $table->id();
            $table->string('kode_pelanggaran', 20)->unique()->comment('Kode unik pelanggaran');
            $table->string('nama_pelanggaran')->comment('Nama jenis pelanggaran');
            $table->text('deskripsi')->nullable()->comment('Deskripsi detail pelanggaran');
            $table->enum('kategori', ['ringan', 'sedang', 'berat'])->comment('Kategori tingkat pelanggaran');
            $table->decimal('denda_nominal', 12, 2)->default(0)->comment('Nominal denda dalam rupiah');
            $table->enum('jenis_denda', ['nominal_tetap', 'persentase_gaji'])->default('nominal_tetap')->comment('Jenis perhitungan denda');
            $table->decimal('persentase_denda', 5, 2)->nullable()->comment('Persentase denda jika jenis_denda = persentase_gaji');
            $table->boolean('is_active')->default(true)->comment('Status aktif pelanggaran');
            $table->text('keterangan')->nullable()->comment('Keterangan tambahan');
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index('kode_pelanggaran');
            $table->index('kategori');
            $table->index('is_active');
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jenis_pelanggaran');
    }
};
