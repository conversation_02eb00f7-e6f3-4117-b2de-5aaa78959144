<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('aturan_keterlambatan', function (Blueprint $table) {
            $table->id();
            $table->string('nama_aturan')->comment('Nama aturan keterlambatan');
            $table->integer('menit_dari')->comment('Menit keterlambatan dari (dalam menit)');
            $table->integer('menit_sampai')->nullable()->comment('Menit keterlambatan sampai (dalam menit), null = tidak terbatas');
            $table->decimal('denda_nominal', 12, 2)->default(0)->comment('Nominal denda dalam rupiah');
            $table->enum('jenis_denda', ['nominal_tetap', 'per_menit', 'persentase_gaji'])->default('nominal_tetap')->comment('Jenis perhitungan denda');
            $table->decimal('persentase_denda', 5, 2)->nullable()->comment('Persentase denda jika jenis_denda = persentase_gaji');
            $table->decimal('denda_per_menit', 12, 2)->nullable()->comment('Denda per menit jika jenis_denda = per_menit');
            $table->boolean('is_active')->default(true)->comment('Status aktif aturan');
            $table->text('keterangan')->nullable()->comment('Keterangan tambahan');
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['menit_dari', 'menit_sampai']);
            $table->index('is_active');
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('aturan_keterlambatan');
    }
};
