<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payroll_periods', function (Blueprint $table) {
            $table->id();
            $table->string('kode_periode', 20)->unique()->comment('Kode unik periode payroll');
            $table->string('nama_periode')->comment('Nama periode payroll');
            $table->date('tanggal_mulai')->comment('Tanggal mulai periode');
            $table->date('tanggal_selesai')->comment('Tanggal selesai periode');
            $table->date('tanggal_cutoff')->comment('Tanggal cutoff untuk perhitungan');
            $table->enum('status', ['draft', 'processing', 'completed', 'cancelled'])->default('draft')->comment('Status periode payroll');
            $table->text('keterangan')->nullable()->comment('Keterangan periode');
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('processed_by')->nullable()->comment('User yang memproses payroll');
            $table->timestamp('processed_at')->nullable()->comment('Waktu pemrosesan payroll');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index('kode_periode');
            $table->index(['tanggal_mulai', 'tanggal_selesai']);
            $table->index('status');
            $table->index('created_by');
            $table->index('processed_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payroll_periods');
    }
};
