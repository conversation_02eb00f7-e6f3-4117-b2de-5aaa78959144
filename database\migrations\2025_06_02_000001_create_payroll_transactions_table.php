<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payroll_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('no_payroll', 30)->unique()->comment('Nomor payroll unik');
            $table->unsignedBigInteger('payroll_period_id')->comment('ID periode payroll');
            $table->unsignedBigInteger('karyawan_id')->comment('ID karyawan');
            $table->unsignedBigInteger('penggajian_karyawan_id')->comment('ID basis gaji karyawan');
            
            // Komponen Gaji (dari basis gaji)
            $table->decimal('gaji_pokok', 12, 2)->default(0)->comment('Gaji pokok');
            $table->decimal('tunjangan_jabatan', 12, 2)->default(0)->comment('Tunjangan jabatan');
            $table->decimal('tunjangan_umum', 12, 2)->default(0)->comment('Tunjangan umum');
            $table->decimal('tunjangan_sembako', 12, 2)->default(0)->comment('Tunjangan sembako');
            $table->decimal('total_gaji_kotor', 12, 2)->default(0)->comment('Total gaji kotor');
            
            // Komponen Potongan
            $table->decimal('potongan_bpjs_kesehatan', 12, 2)->default(0)->comment('Potongan BPJS Kesehatan');
            $table->decimal('potongan_bpjs_tk', 12, 2)->default(0)->comment('Potongan BPJS Tenaga Kerja');
            $table->decimal('potongan_keterlambatan', 12, 2)->default(0)->comment('Total potongan keterlambatan');
            $table->decimal('potongan_pelanggaran', 12, 2)->default(0)->comment('Total potongan pelanggaran');
            $table->decimal('potongan_lainnya', 12, 2)->default(0)->comment('Potongan lainnya');
            $table->decimal('total_potongan', 12, 2)->default(0)->comment('Total semua potongan');
            
            // Take Home Pay
            $table->decimal('take_home_pay', 12, 2)->default(0)->comment('Gaji bersih yang diterima');
            
            // Metadata
            $table->integer('total_hari_kerja')->default(0)->comment('Total hari kerja dalam periode');
            $table->integer('total_hari_hadir')->default(0)->comment('Total hari hadir');
            $table->integer('total_menit_terlambat')->default(0)->comment('Total menit keterlambatan');
            $table->integer('total_pelanggaran')->default(0)->comment('Total jumlah pelanggaran');
            
            $table->enum('status', ['draft', 'approved', 'paid', 'cancelled'])->default('draft')->comment('Status payroll');
            $table->text('keterangan')->nullable()->comment('Keterangan payroll');
            
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable()->comment('User yang approve');
            $table->timestamp('approved_at')->nullable()->comment('Waktu approve');
            $table->timestamps();
            $table->softDeletes();

            // Foreign Keys
            $table->foreign('payroll_period_id')->references('id')->on('payroll_periods')->onDelete('cascade');
            $table->foreign('karyawan_id')->references('id')->on('karyawan')->onDelete('cascade');
            $table->foreign('penggajian_karyawan_id')->references('id')->on('penggajian_karyawan')->onDelete('cascade');

            // Indexes
            $table->index('no_payroll');
            $table->index(['payroll_period_id', 'karyawan_id']);
            $table->index('status');
            $table->index('created_by');
            $table->index('approved_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payroll_transactions');
    }
};
