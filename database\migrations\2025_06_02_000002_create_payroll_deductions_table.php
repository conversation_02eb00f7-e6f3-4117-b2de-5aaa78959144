<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payroll_deductions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('payroll_transaction_id')->comment('ID transaksi payroll');
            $table->enum('jenis_potongan', ['bpjs_kesehatan', 'bpjs_tk', 'keterlambatan', 'pelanggaran', 'lainnya'])->comment('<PERSON><PERSON> potongan');
            $table->string('kode_referensi')->nullable()->comment('Kode referensi (ID absensi untuk keterlambatan, ID pelanggaran untuk pelanggaran)');
            $table->string('deskripsi')->comment('Deskripsi detail potongan');
            $table->decimal('nominal', 12, 2)->default(0)->comment('Nominal potongan');
            $table->text('keterangan')->nullable()->comment('Keterangan tambahan');
            $table->date('tanggal_kejadian')->nullable()->comment('Tanggal kejadian (untuk keterlambatan/pelanggaran)');
            $table->timestamps();

            // Foreign Keys
            $table->foreign('payroll_transaction_id')->references('id')->on('payroll_transactions')->onDelete('cascade');

            // Indexes
            $table->index(['payroll_transaction_id', 'jenis_potongan']);
            $table->index('kode_referensi');
            $table->index('tanggal_kejadian');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payroll_deductions');
    }
};
