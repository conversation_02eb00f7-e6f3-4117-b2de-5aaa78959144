<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pelanggarans', function (Blueprint $table) {
            // Add foreign key to jenis_pelanggaran
            $table->unsignedBigInteger('jenis_pelanggaran_id')->nullable()->after('karyawan_id')->comment('ID jenis pelanggaran');
            
            // Rename existing 'jenis' column to 'jenis_manual' for backward compatibility
            $table->renameColumn('jenis', 'jenis_manual');
            
            // Add calculated denda field
            $table->decimal('nominal_denda', 12, 2)->default(0)->after('keterangan')->comment('Nominal denda yang dihitung');
            
            // Add foreign key constraint
            $table->foreign('jenis_pelanggaran_id')->references('id')->on('jenis_pelanggaran')->onDelete('set null');
            
            // Add index
            $table->index('jenis_pelanggaran_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pelanggarans', function (Blueprint $table) {
            // Drop foreign key and index
            $table->dropForeign(['jenis_pelanggaran_id']);
            $table->dropIndex(['jenis_pelanggaran_id']);
            
            // Drop new columns
            $table->dropColumn(['jenis_pelanggaran_id', 'nominal_denda']);
            
            // Rename back
            $table->renameColumn('jenis_manual', 'jenis');
        });
    }
};
