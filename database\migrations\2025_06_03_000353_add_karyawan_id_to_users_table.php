<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Tambahkan kolom karyawan_id
            $table->unsignedBigInteger('karyawan_id')->nullable()->after('role');

            // Jika ingin relasi foreign key ke tabel karyawan, bisa aktifkan baris berikut:
            // $table->foreign('karyawan_id')->references('id')->on('karyawan')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Jika sebelumnya pakai foreign key, hapus constraint dulu:
            // $table->dropForeign(['karyawan_id']);
            $table->dropColumn('karyawan_id');
        });
    }
};
