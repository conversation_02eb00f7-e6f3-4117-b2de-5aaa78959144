<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_permissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('permission_type')->default('custom');
            $table->json('specific_permissions')->nullable();
            $table->boolean('can_view_project')->default(true);
            $table->boolean('can_edit_project')->default(false);
            $table->boolean('can_delete_project')->default(false);
            $table->boolean('can_manage_members')->default(false);
            $table->boolean('can_view_tasks')->default(true);
            $table->boolean('can_create_tasks')->default(false);
            $table->boolean('can_edit_tasks')->default(false);
            $table->boolean('can_delete_tasks')->default(false);
            $table->boolean('can_assign_tasks')->default(false);
            $table->boolean('can_view_timesheets')->default(false);
            $table->boolean('can_edit_timesheets')->default(false);
            $table->boolean('can_view_reports')->default(false);
            $table->boolean('can_export_data')->default(false);
            $table->timestamp('granted_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->foreignId('granted_by')->nullable()->constrained('users');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->unique(['project_id', 'user_id']);
            $table->index(['user_id', 'expires_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_permissions');
    }
};
