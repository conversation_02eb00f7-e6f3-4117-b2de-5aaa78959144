<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique()->comment('Setting key identifier');
            $table->json('value')->comment('Setting value (can be any type)');
            $table->string('type')->default('string')->comment('Value type: string, integer, boolean, array, etc');
            $table->text('description')->nullable()->comment('Setting description');
            $table->boolean('is_active')->default(true)->comment('Whether setting is active');
            $table->timestamps();

            // Indexes
            $table->index('key');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_settings');
    }
};
