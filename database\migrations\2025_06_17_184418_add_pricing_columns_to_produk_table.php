<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('produk', function (Blueprint $table) {
            // Only add columns if they don't exist
            if (!Schema::hasColumn('produk', 'unit_cost')) {
                $table->decimal('unit_cost', 12, 2)->nullable()->after('deskripsi');
            }
            if (!Schema::hasColumn('produk', 'selling_price')) {
                $table->decimal('selling_price', 12, 2)->nullable()->after('unit_cost');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('produk', function (Blueprint $table) {
            $table->dropColumn(['unit_cost', 'selling_price']);
        });
    }
};
