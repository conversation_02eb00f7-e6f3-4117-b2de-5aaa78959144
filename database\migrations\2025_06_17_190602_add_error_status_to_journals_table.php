<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('journals', function (Blueprint $table) {
            $table->dropColumn('status');
        });

        Schema::table('journals', function (Blueprint $table) {
            $table->enum('status', ['Draft', 'Posted', 'Cancelled', 'Error'])->default('Draft')->after('description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('journals', function (Blueprint $table) {
            $table->dropColumn('status');
        });

        Schema::table('journals', function (Blueprint $table) {
            $table->enum('status', ['Draft', 'Posted', 'Cancelled'])->default('Draft')->after('description');
        });
    }
};
