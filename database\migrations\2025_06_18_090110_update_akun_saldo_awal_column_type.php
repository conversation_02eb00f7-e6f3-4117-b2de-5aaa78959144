<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('akun', function (Blueprint $table) {
            // Ubah kolom saldo_awal dari decimal(10,2) ke decimal(15,2)
            // untuk menampung nilai yang lebih besar
            $table->decimal('saldo_awal', 15, 2)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('akun', function (Blueprint $table) {
            // Kembalikan ke decimal(10,2)
            $table->decimal('saldo_awal', 10, 2)->nullable()->change();
        });
    }
};
