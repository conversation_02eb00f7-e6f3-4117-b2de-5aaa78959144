<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Add mentions and reactions to task_comments
        if (Schema::hasTable('task_comments')) {
            Schema::table('task_comments', function (Blueprint $table) {
                if (!Schema::hasColumn('task_comments', 'mentions')) {
                    $table->json('mentions')->nullable()->after('comment');
                }
                if (!Schema::hasColumn('task_comments', 'reactions')) {
                    $table->json('reactions')->nullable()->after('mentions');
                }
                if (!Schema::hasColumn('task_comments', 'is_edited')) {
                    $table->boolean('is_edited')->default(false)->after('reactions');
                }
                if (!Schema::hasColumn('task_comments', 'edited_at')) {
                    $table->timestamp('edited_at')->nullable()->after('is_edited');
                }
                if (!Schema::hasColumn('task_comments', 'original_comment')) {
                    $table->text('original_comment')->nullable()->after('edited_at');
                }
            });
        }

        // Create team mentions table for tracking
        if (!Schema::hasTable('team_mentions')) {
            Schema::create('team_mentions', function (Blueprint $table) {
                $table->id();
                $table->morphs('mentionable'); // What was mentioned in (comment, task, etc.)
                $table->foreignId('mentioned_user_id')->constrained('users')->onDelete('cascade');
                $table->foreignId('mentioned_by_user_id')->constrained('users')->onDelete('cascade');
                $table->string('mention_type')->default('user'); // user, team, role
                $table->text('context')->nullable(); // Context around the mention
                $table->boolean('is_read')->default(false);
                $table->timestamp('read_at')->nullable();
                $table->timestamps();

                $table->index(['mentioned_user_id', 'is_read']);
                $table->index(['mentionable_type', 'mentionable_id'], 'team_mentions_morph_index');
            });
        }

        // Create file shares table for better file management
        if (!Schema::hasTable('file_shares')) {
            Schema::create('file_shares', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('original_name');
                $table->string('file_path');
                $table->string('mime_type');
                $table->bigInteger('file_size'); // in bytes
                $table->morphs('shareable'); // What this file is shared with (task, project, comment)
                $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
                $table->text('description')->nullable();
                $table->json('metadata')->nullable(); // Additional file metadata
                $table->boolean('is_public')->default(false);
                $table->timestamp('expires_at')->nullable();
                $table->timestamps();

                // morphs() already creates the index, so we don't need to add it manually
                $table->index(['uploaded_by', 'created_at']);
            });
        }

        // Create real-time collaboration sessions
        if (!Schema::hasTable('collaboration_sessions')) {
            Schema::create('collaboration_sessions', function (Blueprint $table) {
                $table->id();
                $table->morphs('sessionable'); // What is being collaborated on
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('session_type')->default('viewing'); // viewing, editing, commenting
                $table->json('cursor_position')->nullable(); // For real-time cursor tracking
                $table->json('selection')->nullable(); // For text selection tracking
                $table->timestamp('last_activity_at')->nullable();
                $table->timestamps();

                $table->index(['sessionable_type', 'sessionable_id', 'last_activity_at'], 'collab_sessions_morph_activity_idx');
                $table->index(['user_id', 'last_activity_at'], 'collab_sessions_user_activity_idx');
            });
        }

        // Create team activity feeds
        if (!Schema::hasTable('team_activity_feeds')) {
            Schema::create('team_activity_feeds', function (Blueprint $table) {
                $table->id();
                $table->morphs('feedable'); // What this activity is about
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('activity_type'); // comment_added, task_assigned, file_shared, etc.
                $table->text('activity_description');
                $table->json('activity_data')->nullable(); // Additional activity data
                $table->json('affected_users')->nullable(); // Users affected by this activity
                $table->boolean('is_public')->default(true);
                $table->timestamps();

                $table->index(['feedable_type', 'feedable_id', 'created_at'], 'team_activity_feeds_morph_created_idx');
                $table->index(['user_id', 'created_at'], 'team_activity_feeds_user_created_idx');
                $table->index(['activity_type', 'created_at'], 'team_activity_feeds_type_created_idx');
            });
        }

        // Create comment reactions table
        if (!Schema::hasTable('comment_reactions')) {
            Schema::create('comment_reactions', function (Blueprint $table) {
                $table->id();
                $table->foreignId('comment_id')->constrained('task_comments')->onDelete('cascade');
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('reaction_type'); // like, love, laugh, angry, sad, etc.
                $table->timestamps();

                $table->unique(['comment_id', 'user_id', 'reaction_type']);
                $table->index(['comment_id', 'reaction_type']);
            });
        }

        // Create team presence table for online status
        if (!Schema::hasTable('team_presence')) {
            Schema::create('team_presence', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('status')->default('offline'); // online, away, busy, offline
                $table->text('status_message')->nullable();
                $table->morphs('current_context'); // What they're currently working on
                $table->timestamp('last_seen_at')->nullable();
                $table->json('device_info')->nullable();
                $table->timestamps();

                $table->unique('user_id');
                $table->index(['status', 'last_seen_at']);
            });
        }

        // Create collaborative editing locks
        if (!Schema::hasTable('editing_locks')) {
            Schema::create('editing_locks', function (Blueprint $table) {
                $table->id();
                $table->morphs('lockable'); // What is being edited
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('lock_type')->default('editing'); // editing, reviewing
                $table->timestamp('locked_at')->nullable();
                $table->timestamp('expires_at')->nullable();
                $table->json('lock_data')->nullable(); // Additional lock information
                $table->timestamps();

                // morphs() already creates the index, so we don't need to add it manually
                $table->index(['user_id', 'expires_at'], 'editing_locks_user_expires_idx');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('editing_locks');
        Schema::dropIfExists('team_presence');
        Schema::dropIfExists('comment_reactions');
        Schema::dropIfExists('team_activity_feeds');
        Schema::dropIfExists('collaboration_sessions');
        Schema::dropIfExists('file_shares');
        Schema::dropIfExists('team_mentions');

        if (Schema::hasTable('task_comments')) {
            Schema::table('task_comments', function (Blueprint $table) {
                $table->dropColumn([
                    'mentions',
                    'reactions',
                    'is_edited',
                    'edited_at',
                    'original_comment'
                ]);
            });
        }
    }
};
