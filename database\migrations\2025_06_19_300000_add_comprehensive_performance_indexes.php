<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Add comprehensive performance indexes for project management system

        // Tasks table - most critical for performance
        try {
            Schema::table('tasks', function (Blueprint $table) {
                // Core query indexes
                $table->index(['project_id', 'status'], 'idx_tasks_project_status');
                $table->index(['assigned_to', 'status'], 'idx_tasks_assigned_status');
                $table->index(['due_date', 'status'], 'idx_tasks_due_status');
                $table->index(['created_by', 'created_at'], 'idx_tasks_creator_date');

                // Dashboard and reporting indexes
                $table->index(['status', 'created_at'], 'idx_tasks_status_date');
                $table->index(['assigned_to', 'due_date'], 'idx_tasks_assigned_due');
                $table->index(['project_id', 'assigned_to', 'status'], 'idx_tasks_project_assigned_status');

                // Overdue tasks optimization
                $table->index(['due_date', 'status', 'assigned_to'], 'idx_tasks_overdue_check');
            });
        } catch (\Exception $e) {
            // Indexes might already exist
        }

        // Task Comments table - for collaboration features
        try {
            Schema::table('task_comments', function (Blueprint $table) {
                $table->index(['task_id', 'created_at'], 'idx_comments_task_date');
                $table->index(['user_id', 'created_at'], 'idx_comments_user_date');
                $table->index(['parent_id'], 'idx_comments_parent');
                $table->index(['task_id', 'parent_id'], 'idx_comments_task_parent');
            });
        } catch (\Exception $e) {
            // Indexes might already exist
        }

        // Project Members table - for team management
        try {
            Schema::table('project_members', function (Blueprint $table) {
                $table->index(['project_id', 'is_active'], 'idx_members_project_active');
                $table->index(['user_id', 'is_active'], 'idx_members_user_active');
                $table->index(['project_id', 'role'], 'idx_members_project_role');
                $table->index(['joined_at', 'left_at'], 'idx_members_duration');
            });
        } catch (\Exception $e) {
            // Indexes might already exist
        }

        // Timesheets table - for time tracking
        try {
            if (Schema::hasTable('timesheets')) {
                Schema::table('timesheets', function (Blueprint $table) {
                    $table->index(['task_id', 'date'], 'idx_timesheets_task_date');
                    $table->index(['user_id', 'date'], 'idx_timesheets_user_date');
                    $table->index(['project_id', 'date'], 'idx_timesheets_project_date');
                    $table->index(['date', 'hours'], 'idx_timesheets_date_hours');
                });
            }
        } catch (\Exception $e) {
            // Indexes might already exist
        }

        // Team Mentions table - for collaboration
        try {
            if (Schema::hasTable('team_mentions')) {
                Schema::table('team_mentions', function (Blueprint $table) {
                    $table->index(['mentioned_user_id', 'is_read'], 'idx_mentions_user_read');
                    $table->index(['mentioned_by_user_id', 'created_at'], 'idx_mentions_by_date');
                    $table->index(['mentionable_type', 'mentionable_id'], 'idx_mentions_morph');
                    $table->index(['is_read', 'created_at'], 'idx_mentions_read_date');
                });
            }
        } catch (\Exception $e) {
            // Indexes might already exist
        }

        // Team Activity Feeds table - for activity tracking
        try {
            if (Schema::hasTable('team_activity_feeds')) {
                Schema::table('team_activity_feeds', function (Blueprint $table) {
                    $table->index(['user_id', 'created_at'], 'idx_activity_user_date');
                    $table->index(['activity_type', 'created_at'], 'idx_activity_type_date');
                    $table->index(['feedable_type', 'feedable_id'], 'idx_activity_morph');
                    $table->index(['is_public', 'created_at'], 'idx_activity_public_date');
                });
            }
        } catch (\Exception $e) {
            // Indexes might already exist
        }

        // Notifications table - for notification system
        try {
            if (Schema::hasTable('notifications')) {
                Schema::table('notifications', function (Blueprint $table) {
                    $table->index(['notifiable_type', 'notifiable_id'], 'idx_notifications_morph');
                    $table->index(['read_at'], 'idx_notifications_read');
                    $table->index(['created_at'], 'idx_notifications_created');
                    $table->index(['notifiable_id', 'read_at'], 'idx_notifications_user_read');
                });
            }
        } catch (\Exception $e) {
            // Indexes might already exist
        }

        // File Shares table - for file management
        try {
            if (Schema::hasTable('file_shares')) {
                Schema::table('file_shares', function (Blueprint $table) {
                    $table->index(['shareable_type', 'shareable_id'], 'idx_files_morph');
                    $table->index(['uploaded_by', 'created_at'], 'idx_files_uploader_date');
                    $table->index(['is_public'], 'idx_files_public');
                    $table->index(['expires_at'], 'idx_files_expires');
                });
            }
        } catch (\Exception $e) {
            // Indexes might already exist
        }

        // Comment Reactions table - for reactions
        try {
            if (Schema::hasTable('comment_reactions')) {
                Schema::table('comment_reactions', function (Blueprint $table) {
                    $table->index(['comment_id', 'reaction_type'], 'idx_reactions_comment_type');
                    $table->index(['user_id', 'created_at'], 'idx_reactions_user_date');
                });
            }
        } catch (\Exception $e) {
            // Indexes might already exist
        }



        // Add composite indexes for complex queries
        try {
            // Multi-table query optimization
            DB::statement('CREATE INDEX IF NOT EXISTS idx_tasks_dashboard_query ON tasks (assigned_to, status, due_date, created_at)');
            DB::statement('CREATE INDEX IF NOT EXISTS idx_projects_active_query ON projects (status, end_date, created_at)');
            DB::statement('CREATE INDEX IF NOT EXISTS idx_comments_recent_query ON task_comments (task_id, parent_id, created_at)');
        } catch (\Exception $e) {
            // Indexes might already exist
        }
    }

    public function down(): void
    {
        // Drop indexes in reverse order
        try {
            Schema::table('tasks', function (Blueprint $table) {
                $table->dropIndex('idx_tasks_project_status');
                $table->dropIndex('idx_tasks_assigned_status');
                $table->dropIndex('idx_tasks_due_status');
                $table->dropIndex('idx_tasks_creator_date');
                $table->dropIndex('idx_tasks_status_date');
                $table->dropIndex('idx_tasks_assigned_due');
                $table->dropIndex('idx_tasks_project_assigned_status');
                $table->dropIndex('idx_tasks_overdue_check');
            });
        } catch (\Exception $e) {
            // Ignore errors
        }

        try {
            Schema::table('task_comments', function (Blueprint $table) {
                $table->dropIndex('idx_comments_task_date');
                $table->dropIndex('idx_comments_user_date');
                $table->dropIndex('idx_comments_parent');
                $table->dropIndex('idx_comments_task_parent');
            });
        } catch (\Exception $e) {
            // Ignore errors
        }

        try {
            Schema::table('project_members', function (Blueprint $table) {
                $table->dropIndex('idx_members_project_active');
                $table->dropIndex('idx_members_user_active');
                $table->dropIndex('idx_members_project_role');
                $table->dropIndex('idx_members_duration');
            });
        } catch (\Exception $e) {
            // Ignore errors
        }

        // Drop other indexes similarly...
        try {
            DB::statement('DROP INDEX IF EXISTS idx_tasks_dashboard_query');
            DB::statement('DROP INDEX IF EXISTS idx_projects_active_query');
            DB::statement('DROP INDEX IF EXISTS idx_comments_recent_query');
        } catch (\Exception $e) {
            // Ignore errors
        }
    }
};
