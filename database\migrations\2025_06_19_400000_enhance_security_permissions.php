<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Create security audit logs table
        if (!Schema::hasTable('security_audit_logs')) {
            Schema::create('security_audit_logs', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
                $table->string('event_type'); // login, logout, permission_change, access_denied, etc.
                $table->string('resource_type')->nullable(); // project, task, user, etc.
                $table->unsignedBigInteger('resource_id')->nullable();
                $table->string('action'); // view, create, update, delete, etc.
                $table->json('old_values')->nullable();
                $table->json('new_values')->nullable();
                $table->string('ip_address')->nullable();
                $table->string('user_agent')->nullable();
                $table->string('session_id')->nullable();
                $table->enum('risk_level', ['low', 'medium', 'high', 'critical'])->default('low');
                $table->text('description')->nullable();
                $table->json('metadata')->nullable();
                $table->timestamps();

                // Indexes for performance
                $table->index(['user_id', 'created_at']);
                $table->index(['event_type', 'created_at']);
                $table->index(['resource_type', 'resource_id']);
                $table->index(['risk_level', 'created_at']);
                $table->index(['ip_address', 'created_at']);
            });
        }

        // Create project permissions table for granular project-level access
        if (!Schema::hasTable('project_permissions')) {
            Schema::create('project_permissions', function (Blueprint $table) {
                $table->id();
                $table->foreignId('project_id')->constrained()->onDelete('cascade');
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('permission_type'); // view, edit, manage, admin
                $table->json('specific_permissions')->nullable(); // detailed permissions array
                $table->boolean('can_view_project')->default(true);
                $table->boolean('can_edit_project')->default(false);
                $table->boolean('can_delete_project')->default(false);
                $table->boolean('can_manage_members')->default(false);
                $table->boolean('can_view_tasks')->default(true);
                $table->boolean('can_create_tasks')->default(false);
                $table->boolean('can_edit_tasks')->default(false);
                $table->boolean('can_delete_tasks')->default(false);
                $table->boolean('can_assign_tasks')->default(false);
                $table->boolean('can_view_timesheets')->default(false);
                $table->boolean('can_edit_timesheets')->default(false);
                $table->boolean('can_view_reports')->default(false);
                $table->boolean('can_export_data')->default(false);
                $table->timestamp('granted_at')->useCurrent();
                $table->timestamp('expires_at')->nullable();
                $table->foreignId('granted_by')->constrained('users')->onDelete('cascade');
                $table->text('notes')->nullable();
                $table->timestamps();

                // Unique constraint
                $table->unique(['project_id', 'user_id']);
                
                // Indexes
                $table->index(['project_id', 'permission_type']);
                $table->index(['user_id', 'permission_type']);
                $table->index(['expires_at']);
            });
        }

        // Create security settings table
        if (!Schema::hasTable('security_settings')) {
            Schema::create('security_settings', function (Blueprint $table) {
                $table->id();
                $table->string('key')->unique();
                $table->text('value');
                $table->string('type')->default('string'); // string, boolean, integer, json
                $table->text('description')->nullable();
                $table->boolean('is_system')->default(false);
                $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
                $table->timestamps();
            });
        }

        // Create login attempts table for security monitoring
        if (!Schema::hasTable('login_attempts')) {
            Schema::create('login_attempts', function (Blueprint $table) {
                $table->id();
                $table->string('email');
                $table->string('ip_address');
                $table->boolean('successful')->default(false);
                $table->string('user_agent')->nullable();
                $table->string('failure_reason')->nullable();
                $table->json('metadata')->nullable();
                $table->timestamps();

                // Indexes
                $table->index(['email', 'created_at']);
                $table->index(['ip_address', 'created_at']);
                $table->index(['successful', 'created_at']);
            });
        }

        // Create session security table
        if (!Schema::hasTable('session_security')) {
            Schema::create('session_security', function (Blueprint $table) {
                $table->id();
                $table->string('session_id')->unique();
                $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
                $table->string('ip_address');
                $table->string('user_agent');
                $table->timestamp('last_activity');
                $table->boolean('is_active')->default(true);
                $table->enum('security_level', ['normal', 'elevated', 'high'])->default('normal');
                $table->json('flags')->nullable(); // suspicious activity flags
                $table->timestamps();

                // Indexes
                $table->index(['user_id', 'is_active']);
                $table->index(['ip_address', 'last_activity']);
                $table->index(['security_level', 'is_active']);
            });
        }

        // Add security fields to existing tables
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                if (!Schema::hasColumn('users', 'last_login_at')) {
                    $table->timestamp('last_login_at')->nullable()->after('email_verified_at');
                }
                if (!Schema::hasColumn('users', 'last_login_ip')) {
                    $table->string('last_login_ip')->nullable()->after('last_login_at');
                }
                if (!Schema::hasColumn('users', 'failed_login_attempts')) {
                    $table->integer('failed_login_attempts')->default(0)->after('last_login_ip');
                }
                if (!Schema::hasColumn('users', 'locked_until')) {
                    $table->timestamp('locked_until')->nullable()->after('failed_login_attempts');
                }
                if (!Schema::hasColumn('users', 'two_factor_enabled')) {
                    $table->boolean('two_factor_enabled')->default(false)->after('locked_until');
                }
                if (!Schema::hasColumn('users', 'security_level')) {
                    $table->enum('security_level', ['standard', 'elevated', 'admin'])->default('standard')->after('two_factor_enabled');
                }
            });
        }

        // Add security tracking to project_members table
        if (Schema::hasTable('project_members')) {
            Schema::table('project_members', function (Blueprint $table) {
                if (!Schema::hasColumn('project_members', 'access_level')) {
                    $table->enum('access_level', ['read', 'write', 'admin'])->default('read')->after('role');
                }
                if (!Schema::hasColumn('project_members', 'last_accessed_at')) {
                    $table->timestamp('last_accessed_at')->nullable()->after('access_level');
                }
                if (!Schema::hasColumn('project_members', 'access_count')) {
                    $table->integer('access_count')->default(0)->after('last_accessed_at');
                }
            });
        }
    }

    public function down(): void
    {
        // Drop tables in reverse order
        Schema::dropIfExists('session_security');
        Schema::dropIfExists('login_attempts');
        Schema::dropIfExists('security_settings');
        Schema::dropIfExists('project_permissions');
        Schema::dropIfExists('security_audit_logs');

        // Remove added columns
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn([
                    'last_login_at',
                    'last_login_ip', 
                    'failed_login_attempts',
                    'locked_until',
                    'two_factor_enabled',
                    'security_level'
                ]);
            });
        }

        if (Schema::hasTable('project_members')) {
            Schema::table('project_members', function (Blueprint $table) {
                $table->dropColumn([
                    'access_level',
                    'last_accessed_at',
                    'access_count'
                ]);
            });
        }
    }
};
