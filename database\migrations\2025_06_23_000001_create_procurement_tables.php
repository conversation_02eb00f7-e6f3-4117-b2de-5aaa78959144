<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Tabel Purchase Orders (PO)
        Schema::create('purchase_orders', function (Blueprint $table) {
            $table->id();
            $table->string('po_number')->unique(); // Auto-generated PO number
            $table->date('po_date');
            $table->date('expected_delivery_date')->nullable();
            $table->unsignedBigInteger('supplier_id');
            $table->unsignedBigInteger('warehouse_id'); // Gudang tujuan
            $table->unsignedBigInteger('entitas_id'); // Entitas yang melakukan pembelian
            $table->decimal('subtotal', 12, 2);
            $table->decimal('tax_amount', 12, 2)->default(0);
            $table->decimal('discount_amount', 12, 2)->default(0);
            $table->decimal('total_amount', 12, 2);
            $table->enum('status', ['Draft', 'Submitted', 'Approved', 'Partially_Received', 'Completed', 'Cancelled'])->default('Draft');
            $table->text('notes')->nullable();
            $table->string('payment_terms')->nullable(); // Net 30, COD, dll
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys commented out to avoid dependency issues
            // $table->foreign('supplier_id')->references('id')->on('supplier')->onDelete('cascade');
            // $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            // $table->foreign('entitas_id')->references('id')->on('entitas')->onDelete('cascade');
            $table->index(['po_date', 'status']);
        });

        // Tabel Purchase Order Items
        Schema::create('purchase_order_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('purchase_order_id');
            $table->unsignedBigInteger('product_id');
            $table->integer('quantity_ordered');
            $table->integer('quantity_received')->default(0);
            $table->decimal('unit_price', 12, 2);
            $table->decimal('total_price', 12, 2); // quantity_ordered * unit_price
            $table->text('notes')->nullable();
            $table->timestamps();

            // Foreign keys commented out to avoid dependency issues
            // $table->foreign('purchase_order_id')->references('id')->on('purchase_orders')->onDelete('cascade');
            // $table->foreign('product_id')->references('id')->on('produk')->onDelete('cascade');
        });

        // Tabel Goods Receipts (Penerimaan Barang)
        Schema::create('goods_receipts', function (Blueprint $table) {
            $table->id();
            $table->string('receipt_number')->unique(); // Auto-generated receipt number
            $table->date('receipt_date');
            $table->unsignedBigInteger('purchase_order_id');
            $table->unsignedBigInteger('warehouse_id');
            $table->string('delivery_note_number')->nullable(); // Nomor surat jalan
            $table->enum('status', ['Draft', 'Completed', 'Cancelled'])->default('Draft');
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('received_by')->nullable(); // User yang menerima
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys commented out to avoid dependency issues
            // $table->foreign('purchase_order_id')->references('id')->on('purchase_orders')->onDelete('cascade');
            // $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            $table->index(['receipt_date', 'status']);
        });

        // Tabel Goods Receipt Items
        Schema::create('goods_receipt_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('goods_receipt_id');
            $table->unsignedBigInteger('purchase_order_item_id');
            $table->unsignedBigInteger('product_id');
            $table->integer('quantity_received');
            $table->decimal('unit_cost', 12, 2); // Bisa berbeda dari PO jika ada penyesuaian
            $table->decimal('total_cost', 12, 2); // quantity_received * unit_cost
            $table->text('notes')->nullable();
            $table->timestamps();

            // Foreign keys commented out to avoid dependency issues
            // $table->foreign('goods_receipt_id')->references('id')->on('goods_receipts')->onDelete('cascade');
            // $table->foreign('purchase_order_item_id')->references('id')->on('purchase_order_items')->onDelete('cascade');
            // $table->foreign('product_id')->references('id')->on('produk')->onDelete('cascade');
        });

        // Tabel Purchase Invoices (Faktur Pembelian)
        Schema::create('purchase_invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number')->unique(); // Auto-generated invoice number
            $table->string('supplier_invoice_number')->nullable(); // Nomor faktur dari supplier
            $table->date('invoice_date');
            $table->date('due_date');
            $table->unsignedBigInteger('supplier_id');
            $table->unsignedBigInteger('purchase_order_id')->nullable(); // Bisa null untuk invoice tanpa PO
            $table->decimal('subtotal', 12, 2);
            $table->decimal('tax_amount', 12, 2)->default(0);
            $table->decimal('discount_amount', 12, 2)->default(0);
            $table->decimal('total_amount', 12, 2);
            $table->decimal('paid_amount', 12, 2)->default(0);
            $table->decimal('outstanding_amount', 12, 2); // total_amount - paid_amount
            $table->enum('status', ['Draft', 'Submitted', 'Approved', 'Partially_Paid', 'Paid', 'Overdue', 'Cancelled'])->default('Draft');
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys commented out to avoid dependency issues
            // $table->foreign('supplier_id')->references('id')->on('supplier')->onDelete('cascade');
            // $table->foreign('purchase_order_id')->references('id')->on('purchase_orders')->onDelete('set null');
            $table->index(['invoice_date', 'due_date', 'status']);
        });

        // Tabel Purchase Invoice Items
        Schema::create('purchase_invoice_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('purchase_invoice_id');
            $table->unsignedBigInteger('product_id')->nullable(); // Bisa null untuk item non-inventory
            $table->string('description'); // Deskripsi item
            $table->integer('quantity');
            $table->decimal('unit_price', 12, 2);
            $table->decimal('total_price', 12, 2); // quantity * unit_price
            $table->unsignedBigInteger('account_id'); // Akun untuk posting (Inventory/Expense)
            $table->timestamps();

            // Foreign keys commented out to avoid dependency issues
            // $table->foreign('purchase_invoice_id')->references('id')->on('purchase_invoices')->onDelete('cascade');
            // $table->foreign('product_id')->references('id')->on('produk')->onDelete('set null');
            // $table->foreign('account_id')->references('id')->on('akun')->onDelete('cascade');
        });

        // Tabel Purchase Payments (Pembayaran Pembelian)
        Schema::create('purchase_payments', function (Blueprint $table) {
            $table->id();
            $table->string('payment_number')->unique(); // Auto-generated payment number
            $table->date('payment_date');
            $table->unsignedBigInteger('supplier_id');
            $table->unsignedBigInteger('purchase_invoice_id')->nullable(); // Bisa null untuk advance payment
            $table->decimal('payment_amount', 12, 2);
            $table->enum('payment_method', ['Cash', 'Bank_Transfer', 'Check', 'Credit_Card'])->default('Bank_Transfer');
            $table->string('reference_number')->nullable(); // Nomor referensi bank/check
            $table->unsignedBigInteger('bank_account_id')->nullable(); // Akun bank yang digunakan
            $table->enum('status', ['Draft', 'Completed', 'Cancelled'])->default('Draft');
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys commented out to avoid dependency issues
            // $table->foreign('supplier_id')->references('id')->on('supplier')->onDelete('cascade');
            // $table->foreign('purchase_invoice_id')->references('id')->on('purchase_invoices')->onDelete('set null');
            // $table->foreign('bank_account_id')->references('id')->on('akun')->onDelete('set null');
            $table->index(['payment_date', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_payments');
        Schema::dropIfExists('purchase_invoice_items');
        Schema::dropIfExists('purchase_invoices');
        Schema::dropIfExists('goods_receipt_items');
        Schema::dropIfExists('goods_receipts');
        Schema::dropIfExists('purchase_order_items');
        Schema::dropIfExists('purchase_orders');
    }
};
