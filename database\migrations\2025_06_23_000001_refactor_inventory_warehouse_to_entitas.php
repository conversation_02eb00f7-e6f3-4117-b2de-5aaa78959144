<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Create missing warehouses table if not exists
        if (!Schema::hasTable('warehouses')) {
            Schema::create('warehouses', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('code')->unique();
                $table->text('address')->nullable();
                $table->string('phone')->nullable();
                $table->string('manager_name')->nullable();
                $table->boolean('is_active')->default(true);
                $table->unsignedBigInteger('created_by')->nullable();
                $table->timestamps();
                $table->softDeletes();

                $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            });
        }

        // Step 2: Create missing inventory_stocks table if not exists
        if (!Schema::hasTable('inventory_stocks')) {
            Schema::create('inventory_stocks', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('product_id');
                $table->unsignedBigInteger('warehouse_id');
                $table->integer('quantity')->default(0);
                $table->decimal('average_cost', 12, 2)->default(0);
                $table->decimal('total_value', 12, 2)->default(0);
                $table->integer('minimum_stock')->default(0);
                $table->integer('maximum_stock')->default(0);
                $table->date('last_updated')->nullable();
                $table->timestamps();

                $table->foreign('product_id')->references('id')->on('produk')->onDelete('cascade');
                $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
                $table->unique(['product_id', 'warehouse_id']);
            });
        }

        // Step 3: Add entitas_id to inventory_stocks table
        Schema::table('inventory_stocks', function (Blueprint $table) {
            if (!Schema::hasColumn('inventory_stocks', 'entitas_id')) {
                $table->unsignedBigInteger('entitas_id')->nullable()->after('warehouse_id');
                $table->foreign('entitas_id')->references('id')->on('entitas')->onDelete('cascade');
            }
        });

        // Step 4: Add entitas_id to sales_transactions table (if table exists)
        if (Schema::hasTable('sales_transactions')) {
            Schema::table('sales_transactions', function (Blueprint $table) {
                if (!Schema::hasColumn('sales_transactions', 'entitas_id')) {
                    // Check if warehouse_id column exists, if not add after id
                    $afterColumn = Schema::hasColumn('sales_transactions', 'warehouse_id') ? 'warehouse_id' : 'id';
                    $table->unsignedBigInteger('entitas_id')->nullable()->after($afterColumn);
                    // Comment out foreign key to avoid dependency issues
                    // $table->foreign('entitas_id')->references('id')->on('entitas')->onDelete('cascade');
                }
            });
        }

        // Step 5: Migrate data from warehouse to entitas
        // Create mapping between warehouses and entitas
        $this->migrateWarehouseToEntitas();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove entitas_id from sales_transactions (if table exists)
        if (Schema::hasTable('sales_transactions')) {
            Schema::table('sales_transactions', function (Blueprint $table) {
                if (Schema::hasColumn('sales_transactions', 'entitas_id')) {
                    // Try to drop foreign key if it exists
                    try {
                        $table->dropForeign(['entitas_id']);
                    } catch (Exception) {
                        // Foreign key might not exist, continue
                    }
                    $table->dropColumn('entitas_id');
                }
            });
        }

        // Remove entitas_id from inventory_stocks
        Schema::table('inventory_stocks', function (Blueprint $table) {
            if (Schema::hasColumn('inventory_stocks', 'entitas_id')) {
                $table->dropForeign(['entitas_id']);
                $table->dropColumn('entitas_id');
            }
        });
    }

    /**
     * Migrate entitas data to warehouse and update references
     */
    private function migrateWarehouseToEntitas(): void
    {
        // Get all existing entitas
        $entitasList = DB::table('entitas')->get();

        foreach ($entitasList as $entitas) {
            // Create corresponding warehouse for each entitas if not exists
            $warehouseId = DB::table('warehouses')->where('name', $entitas->nama)->value('id');

            if (!$warehouseId) {
                $warehouseId = DB::table('warehouses')->insertGetId([
                    'name' => $entitas->nama,
                    'code' => 'ENT-' . $entitas->id,
                    'address' => $entitas->alamat,
                    'phone' => null,
                    'manager_name' => null,
                    'is_active' => true,
                    'created_by' => null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Update inventory_stocks to use entitas_id (set to current entitas)
            // For existing inventory_stocks without entitas_id, map them to this entitas
            DB::table('inventory_stocks')
                ->whereNull('entitas_id')
                ->where('warehouse_id', $warehouseId)
                ->update(['entitas_id' => $entitas->id]);

            // Update sales_transactions to use entitas_id (if table exists and has warehouse_id)
            if (Schema::hasTable('sales_transactions') && Schema::hasColumn('sales_transactions', 'warehouse_id')) {
                DB::table('sales_transactions')
                    ->whereNull('entitas_id')
                    ->where('warehouse_id', $warehouseId)
                    ->update(['entitas_id' => $entitas->id]);
            }
        }

        // For any remaining inventory_stocks or sales_transactions without entitas_id,
        // assign them to the first available entitas
        $firstEntitas = DB::table('entitas')->first();
        if ($firstEntitas) {
            DB::table('inventory_stocks')
                ->whereNull('entitas_id')
                ->update(['entitas_id' => $firstEntitas->id]);

            if (Schema::hasTable('sales_transactions')) {
                DB::table('sales_transactions')
                    ->whereNull('entitas_id')
                    ->update(['entitas_id' => $firstEntitas->id]);
            }
        }
    }
};
