<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Tabel Expense Categories (<PERSON><PERSON><PERSON>)
        Schema::create('expense_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Nama kategori (Transport, Meals, Office Supplies, dll)
            $table->string('code')->unique(); // Kode kategori
            $table->text('description')->nullable();
            $table->unsignedBigInteger('default_account_id'); // Akun default untuk kategori ini
            $table->decimal('daily_limit', 12, 2)->nullable(); // Limit harian untuk kategori ini
            $table->decimal('monthly_limit', 12, 2)->nullable(); // Limit bulanan untuk kategori ini
            $table->boolean('requires_receipt')->default(true); // Apakah perlu bukti
            $table->boolean('is_active')->default(true);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('default_account_id')->references('id')->on('akun')->onDelete('cascade');
        });

        // Tabel Expense Requests (Permintaan Pengeluaran)
        Schema::create('expense_requests', function (Blueprint $table) {
            $table->id();
            $table->string('request_number')->unique(); // Auto-generated request number
            $table->date('request_date');
            $table->unsignedBigInteger('employee_id'); // Karyawan yang mengajukan
            $table->unsignedBigInteger('entitas_id'); // Entitas tempat karyawan bekerja
            $table->enum('expense_type', ['Petty_Cash', 'Reimbursement', 'Advance']); // Jenis pengeluaran
            $table->decimal('total_amount', 12, 2);
            $table->enum('status', ['Draft', 'Submitted', 'Approved', 'Rejected', 'Paid', 'Cancelled'])->default('Draft');
            $table->text('purpose')->nullable(); // Tujuan pengeluaran
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('requested_by')->nullable(); // User yang mengajukan
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->unsignedBigInteger('rejected_by')->nullable();
            $table->timestamp('rejected_at')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('employee_id')->references('id')->on('karyawan')->onDelete('cascade');
            $table->foreign('entitas_id')->references('id')->on('entitas')->onDelete('cascade');
            $table->index(['request_date', 'status']);
        });

        // Tabel Expense Request Items (Detail Item Pengeluaran)
        Schema::create('expense_request_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expense_request_id');
            $table->unsignedBigInteger('expense_category_id');
            $table->string('description'); // Deskripsi pengeluaran
            $table->decimal('amount', 12, 2);
            $table->date('expense_date'); // Tanggal pengeluaran
            $table->string('receipt_number')->nullable(); // Nomor bukti
            $table->string('receipt_file_path')->nullable(); // Path file bukti
            $table->unsignedBigInteger('account_id'); // Akun untuk posting
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('expense_request_id')->references('id')->on('expense_requests')->onDelete('cascade');
            $table->foreign('expense_category_id')->references('id')->on('expense_categories')->onDelete('cascade');
            $table->foreign('account_id')->references('id')->on('akun')->onDelete('cascade');
        });

        // Tabel Cash Disbursements (Pengeluaran Kas)
        Schema::create('cash_disbursements', function (Blueprint $table) {
            $table->id();
            $table->string('disbursement_number')->unique(); // Auto-generated disbursement number
            $table->date('disbursement_date');
            $table->unsignedBigInteger('expense_request_id')->nullable(); // Bisa null untuk pengeluaran langsung
            $table->unsignedBigInteger('payee_employee_id')->nullable(); // Karyawan penerima
            $table->string('payee_name')->nullable(); // Nama penerima (jika bukan karyawan)
            $table->decimal('amount', 12, 2);
            $table->enum('payment_method', ['Cash', 'Bank_Transfer', 'Check'])->default('Cash');
            $table->unsignedBigInteger('cash_account_id'); // Akun kas/bank yang digunakan
            $table->string('reference_number')->nullable(); // Nomor referensi
            $table->enum('status', ['Draft', 'Completed', 'Cancelled'])->default('Draft');
            $table->text('purpose')->nullable(); // Tujuan pembayaran
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expense_request_id')->references('id')->on('expense_requests')->onDelete('set null');
            $table->foreign('payee_employee_id')->references('id')->on('karyawan')->onDelete('set null');
            $table->foreign('cash_account_id')->references('id')->on('akun')->onDelete('cascade');
            $table->index(['disbursement_date', 'status']);
        });

        // Tabel Cash Disbursement Items (Detail Pengeluaran Kas)
        Schema::create('cash_disbursement_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('cash_disbursement_id');
            $table->unsignedBigInteger('expense_category_id')->nullable();
            $table->string('description'); // Deskripsi pengeluaran
            $table->decimal('amount', 12, 2);
            $table->unsignedBigInteger('account_id'); // Akun untuk posting (expense account)
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('cash_disbursement_id')->references('id')->on('cash_disbursements')->onDelete('cascade');
            $table->foreign('expense_category_id')->references('id')->on('expense_categories')->onDelete('set null');
            $table->foreign('account_id')->references('id')->on('akun')->onDelete('cascade');
        });

        // Tabel Petty Cash Funds (Dana Kas Kecil)
        Schema::create('petty_cash_funds', function (Blueprint $table) {
            $table->id();
            $table->string('fund_name'); // Nama dana kas kecil
            $table->string('fund_code')->unique(); // Kode dana
            $table->unsignedBigInteger('entitas_id'); // Entitas pemilik dana
            $table->unsignedBigInteger('custodian_employee_id'); // Karyawan pemegang dana
            $table->unsignedBigInteger('cash_account_id'); // Akun kas untuk dana ini
            $table->decimal('initial_amount', 12, 2); // Jumlah awal dana
            $table->decimal('current_balance', 12, 2); // Saldo saat ini
            $table->decimal('maximum_amount', 12, 2); // Maksimal dana
            $table->decimal('minimum_balance', 12, 2)->default(0); // Minimum saldo untuk replenishment
            $table->boolean('is_active')->default(true);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('entitas_id')->references('id')->on('entitas')->onDelete('cascade');
            $table->foreign('custodian_employee_id')->references('id')->on('karyawan')->onDelete('cascade');
            $table->foreign('cash_account_id')->references('id')->on('akun')->onDelete('cascade');
        });

        // Tabel Petty Cash Transactions (Transaksi Kas Kecil)
        Schema::create('petty_cash_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_number')->unique(); // Auto-generated transaction number
            $table->date('transaction_date');
            $table->unsignedBigInteger('petty_cash_fund_id');
            $table->enum('transaction_type', ['Disbursement', 'Replenishment']); // Pengeluaran atau pengisian
            $table->decimal('amount', 12, 2);
            $table->string('description');
            $table->string('recipient_name')->nullable(); // Nama penerima
            $table->string('receipt_number')->nullable(); // Nomor bukti
            $table->unsignedBigInteger('expense_category_id')->nullable();
            $table->unsignedBigInteger('account_id'); // Akun untuk posting
            $table->enum('status', ['Draft', 'Completed', 'Cancelled'])->default('Completed');
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('petty_cash_fund_id')->references('id')->on('petty_cash_funds')->onDelete('cascade');
            $table->foreign('expense_category_id')->references('id')->on('expense_categories')->onDelete('set null');
            $table->foreign('account_id')->references('id')->on('akun')->onDelete('cascade');
            $table->index(['transaction_date', 'transaction_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('petty_cash_transactions');
        Schema::dropIfExists('petty_cash_funds');
        Schema::dropIfExists('cash_disbursement_items');
        Schema::dropIfExists('cash_disbursements');
        Schema::dropIfExists('expense_request_items');
        Schema::dropIfExists('expense_requests');
        Schema::dropIfExists('expense_categories');
    }
};
