<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, ensure warehouse_id column exists in sales_transactions if needed
        if (Schema::hasTable('sales_transactions') && !Schema::hasColumn('sales_transactions', 'warehouse_id')) {
            Schema::table('sales_transactions', function (Blueprint $table) {
                $table->unsignedBigInteger('warehouse_id')->nullable()->after('customer_name');
                $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('set null');
            });
        }

        // Clear existing entitas_id mappings to start fresh
        if (Schema::hasColumn('inventory_stocks', 'entitas_id')) {
            DB::table('inventory_stocks')->update(['entitas_id' => null]);
        }
        if (Schema::hasColumn('sales_transactions', 'entitas_id')) {
            DB::table('sales_transactions')->update(['entitas_id' => null]);
        }

        // Get all existing entitas
        $entitasList = DB::table('entitas')->get();

        foreach ($entitasList as $entitas) {
            // Create corresponding warehouse for each entitas if not exists
            $warehouseId = DB::table('warehouses')->where('name', $entitas->nama)->value('id');

            if (!$warehouseId) {
                $warehouseId = DB::table('warehouses')->insertGetId([
                    'name' => $entitas->nama,
                    'code' => 'ENT-' . $entitas->id,
                    'address' => $entitas->alamat,
                    'phone' => null,
                    'manager_name' => null,
                    'is_active' => true,
                    'created_by' => null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Update inventory_stocks to use entitas_id (only if both columns exist)
            if (Schema::hasColumn('inventory_stocks', 'entitas_id') && Schema::hasColumn('inventory_stocks', 'warehouse_id')) {
                DB::table('inventory_stocks')
                    ->whereNull('entitas_id')
                    ->where('warehouse_id', $warehouseId)
                    ->update(['entitas_id' => $entitas->id]);
            }

            // Update sales_transactions to use entitas_id (only if both columns exist)
            if (Schema::hasColumn('sales_transactions', 'entitas_id') && Schema::hasColumn('sales_transactions', 'warehouse_id')) {
                DB::table('sales_transactions')
                    ->whereNull('entitas_id')
                    ->where('warehouse_id', $warehouseId)
                    ->update(['entitas_id' => $entitas->id]);
            }
        }

        // For any remaining records without entitas_id, assign them to the first available entitas
        $firstEntitas = DB::table('entitas')->first();
        if ($firstEntitas) {
            // Update inventory_stocks
            if (Schema::hasColumn('inventory_stocks', 'entitas_id')) {
                DB::table('inventory_stocks')
                    ->whereNull('entitas_id')
                    ->update(['entitas_id' => $firstEntitas->id]);
            }

            // Update sales_transactions
            if (Schema::hasColumn('sales_transactions', 'entitas_id')) {
                DB::table('sales_transactions')
                    ->whereNull('entitas_id')
                    ->update(['entitas_id' => $firstEntitas->id]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reset entitas_id to null (only if columns exist)
        if (Schema::hasColumn('inventory_stocks', 'entitas_id')) {
            DB::table('inventory_stocks')->update(['entitas_id' => null]);
        }
        if (Schema::hasColumn('sales_transactions', 'entitas_id')) {
            DB::table('sales_transactions')->update(['entitas_id' => null]);
        }

        // Optionally remove warehouse_id from sales_transactions if it was added by this migration
        // Note: Only remove if you're sure it was added by this migration and not needed elsewhere
        // if (Schema::hasColumn('sales_transactions', 'warehouse_id')) {
        //     Schema::table('sales_transactions', function (Blueprint $table) {
        //         $table->dropForeign(['warehouse_id']);
        //         $table->dropColumn('warehouse_id');
        //     });
        // }
    }
};
