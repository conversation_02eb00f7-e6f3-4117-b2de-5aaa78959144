<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Tabel Payroll Components (Komponen Gaji)
        Schema::create('payroll_components', function (Blueprint $table) {
            $table->id();
            $table->string('component_code')->unique(); // <PERSON><PERSON> komponen (GAPOK, TUNJAB, BPJSKES, dll)
            $table->string('component_name'); // Nama komponen
            $table->enum('component_type', ['Earning', 'Deduction']); // Pendapatan atau Potongan
            $table->enum('calculation_type', ['Fixed', 'Percentage', 'Formula']); // Jenis perhitungan
            $table->decimal('fixed_amount', 12, 2)->nullable(); // Jumlah tetap
            $table->decimal('percentage_rate', 5, 2)->nullable(); // Persentase (untuk BPJS, dll)
            $table->text('calculation_formula')->nullable(); // Formula perhitungan
            $table->unsignedBigInteger('account_id'); // Akun untuk posting jurnal
            $table->boolean('is_taxable')->default(true); // Apakah kena pajak
            $table->boolean('is_active')->default(true);
            $table->text('description')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('account_id')->references('id')->on('akun')->onDelete('cascade');
        });

        // Tabel Employee Payroll Components (Komponen Gaji per Karyawan)
        Schema::create('employee_payroll_components', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('employee_id');
            $table->unsignedBigInteger('payroll_component_id');
            $table->decimal('amount', 12, 2)->nullable(); // Jumlah khusus untuk karyawan ini
            $table->decimal('percentage_rate', 5, 2)->nullable(); // Persentase khusus
            $table->boolean('is_active')->default(true);
            $table->date('effective_date'); // Tanggal berlaku
            $table->date('end_date')->nullable(); // Tanggal berakhir
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('employee_id')->references('id')->on('karyawan')->onDelete('cascade');
            $table->foreign('payroll_component_id')->references('id')->on('payroll_components')->onDelete('cascade');
            $table->unique(['employee_id', 'payroll_component_id', 'effective_date'], 'unique_employee_component_date');
        });

        // Tabel Tax Brackets (Tarif Pajak PPh 21)
        Schema::create('tax_brackets', function (Blueprint $table) {
            $table->id();
            $table->string('bracket_name'); // Nama bracket (Tarif 1, Tarif 2, dll)
            $table->decimal('min_income', 12, 2); // Penghasilan minimum
            $table->decimal('max_income', 12, 2)->nullable(); // Penghasilan maksimum (null = unlimited)
            $table->decimal('tax_rate', 5, 2); // Tarif pajak dalam persen
            $table->decimal('cumulative_tax', 12, 2)->default(0); // Pajak kumulatif dari bracket sebelumnya
            $table->boolean('is_active')->default(true);
            $table->date('effective_date'); // Tanggal berlaku
            $table->date('end_date')->nullable(); // Tanggal berakhir
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Tabel PTKP (Penghasilan Tidak Kena Pajak)
        Schema::create('ptkp_rates', function (Blueprint $table) {
            $table->id();
            $table->string('status_code'); // TK/0, TK/1, K/0, K/1, K/2, K/3
            $table->string('status_description'); // Deskripsi status
            $table->decimal('annual_amount', 12, 2); // Jumlah PTKP per tahun
            $table->decimal('monthly_amount', 12, 2); // Jumlah PTKP per bulan
            $table->boolean('is_active')->default(true);
            $table->date('effective_date'); // Tanggal berlaku
            $table->date('end_date')->nullable(); // Tanggal berakhir
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Tabel Employee Tax Info (Info Pajak Karyawan)
        Schema::create('employee_tax_info', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('employee_id');
            $table->string('npwp')->nullable(); // NPWP karyawan
            $table->string('ptkp_status')->default('TK/0'); // Status PTKP
            $table->decimal('previous_income', 12, 2)->default(0); // Penghasilan sebelumnya (untuk perhitungan tahunan)
            $table->decimal('previous_tax', 12, 2)->default(0); // Pajak yang sudah dibayar sebelumnya
            $table->boolean('is_tax_exempted')->default(false); // Apakah dikecualikan dari pajak
            $table->text('tax_exemption_reason')->nullable(); // Alasan dikecualikan
            $table->date('effective_date'); // Tanggal berlaku
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('employee_id')->references('id')->on('karyawan')->onDelete('cascade');
            $table->unique(['employee_id', 'effective_date']);
        });

        // Tabel Payroll Transaction Details (Detail Transaksi Payroll)
        Schema::create('payroll_transaction_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('payroll_transaction_id');
            $table->unsignedBigInteger('payroll_component_id');
            $table->decimal('amount', 12, 2);
            $table->decimal('calculation_base', 12, 2)->nullable(); // Dasar perhitungan
            $table->decimal('rate_used', 5, 2)->nullable(); // Rate yang digunakan
            $table->text('calculation_notes')->nullable(); // Catatan perhitungan
            $table->timestamps();

            $table->foreign('payroll_transaction_id')->references('id')->on('payroll_transactions')->onDelete('cascade');
            $table->foreign('payroll_component_id')->references('id')->on('payroll_components')->onDelete('cascade');
        });

        // Tabel BPJS Rates (Tarif BPJS)
        Schema::create('bpjs_rates', function (Blueprint $table) {
            $table->id();
            $table->enum('bpjs_type', ['Kesehatan', 'Ketenagakerjaan']); // Jenis BPJS
            $table->string('participant_type'); // Pekerja, Perusahaan
            $table->decimal('rate_percentage', 5, 2); // Persentase tarif
            $table->decimal('max_salary_base', 12, 2)->nullable(); // Maksimal gaji sebagai dasar
            $table->decimal('min_salary_base', 12, 2)->nullable(); // Minimal gaji sebagai dasar
            $table->boolean('is_active')->default(true);
            $table->date('effective_date'); // Tanggal berlaku
            $table->date('end_date')->nullable(); // Tanggal berakhir
            $table->text('description')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Tabel Employee BPJS Info (Info BPJS Karyawan)
        Schema::create('employee_bpjs_info', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('employee_id');
            $table->string('bpjs_kesehatan_number')->nullable(); // Nomor BPJS Kesehatan
            $table->string('bpjs_tk_number')->nullable(); // Nomor BPJS Ketenagakerjaan
            $table->boolean('bpjs_kesehatan_active')->default(false);
            $table->boolean('bpjs_tk_active')->default(false);
            $table->date('bpjs_kesehatan_start_date')->nullable();
            $table->date('bpjs_tk_start_date')->nullable();
            $table->decimal('bpjs_kesehatan_salary_base', 12, 2)->nullable(); // Gaji dasar untuk BPJS Kesehatan
            $table->decimal('bpjs_tk_salary_base', 12, 2)->nullable(); // Gaji dasar untuk BPJS TK
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('employee_id')->references('id')->on('karyawan')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_bpjs_info');
        Schema::dropIfExists('bpjs_rates');
        Schema::dropIfExists('payroll_transaction_details');
        Schema::dropIfExists('employee_tax_info');
        Schema::dropIfExists('ptkp_rates');
        Schema::dropIfExists('tax_brackets');
        Schema::dropIfExists('employee_payroll_components');
        Schema::dropIfExists('payroll_components');
    }
};
