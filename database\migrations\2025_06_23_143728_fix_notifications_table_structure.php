<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if notifications table exists, if not create it
        if (!Schema::hasTable('notifications')) {
            Schema::create('notifications', function (Blueprint $table) {
                $table->uuid('id')->primary();
                $table->string('type');
                $table->morphs('notifiable');
                $table->json('data');
                $table->timestamp('read_at')->nullable();
                $table->timestamps();

                $table->index(['notifiable_type', 'notifiable_id']);
                $table->index('type');
                $table->index('read_at');
            });
        } else {
            // If table exists, ensure it has the correct structure
            Schema::table('notifications', function (Blueprint $table) {
                // Check if columns exist before adding them
                if (!Schema::hasColumn('notifications', 'id')) {
                    $table->uuid('id')->primary();
                }
                if (!Schema::hasColumn('notifications', 'type')) {
                    $table->string('type');
                }
                if (!Schema::hasColumn('notifications', 'notifiable_type')) {
                    $table->string('notifiable_type');
                }
                if (!Schema::hasColumn('notifications', 'notifiable_id')) {
                    $table->unsignedBigInteger('notifiable_id');
                }
                if (!Schema::hasColumn('notifications', 'data')) {
                    $table->json('data');
                }
                if (!Schema::hasColumn('notifications', 'read_at')) {
                    $table->timestamp('read_at')->nullable();
                }
                if (!Schema::hasColumn('notifications', 'created_at')) {
                    $table->timestamp('created_at')->nullable();
                }
                if (!Schema::hasColumn('notifications', 'updated_at')) {
                    $table->timestamp('updated_at')->nullable();
                }
            });

            // Add indexes if they don't exist
            try {
                Schema::table('notifications', function (Blueprint $table) {
                    $table->index(['notifiable_type', 'notifiable_id'], 'notifications_notifiable_index');
                    $table->index('type', 'notifications_type_index');
                    $table->index('read_at', 'notifications_read_at_index');
                });
            } catch (\Exception $e) {
                // Indexes might already exist, ignore error
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Don't drop the table in down method to prevent data loss
        // Schema::dropIfExists('notifications');
    }
};
