<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only create tables that don't exist and enhance existing ones

        // Create posting_rule_conditions table if it doesn't exist
        if (!Schema::hasTable('posting_rule_conditions')) {
            Schema::create('posting_rule_conditions', function (Blueprint $table) {
                $table->id();
                $table->foreignId('posting_rule_id')->constrained()->onDelete('cascade');
                $table->string('field_name'); // e.g., 'amount', 'supplier_id', 'entitas_id'
                $table->string('operator'); // e.g., '>', '<', '=', 'in', 'between'
                $table->text('value'); // JSON for complex values
                $table->string('logical_operator')->default('AND'); // AND, OR
                $table->integer('group_number')->default(1); // For grouping conditions
                $table->timestamps();

                $table->index(['posting_rule_id', 'group_number']);
            });
        }

        // Create posting_rule_mappings table if it doesn't exist
        if (!Schema::hasTable('posting_rule_mappings')) {
            Schema::create('posting_rule_mappings', function (Blueprint $table) {
                $table->id();
                $table->foreignId('posting_rule_id')->constrained()->onDelete('cascade');
                $table->string('mapping_type'); // 'debit', 'credit'
                $table->string('account_source'); // 'fixed', 'field', 'lookup'
                $table->string('account_field')->nullable(); // Field name if account_source = 'field'
                $table->unsignedBigInteger('account_id')->nullable();
                $table->foreign('account_id')->references('id')->on('akun');
                $table->string('amount_source'); // 'fixed', 'field', 'calculation'
                $table->string('amount_field')->nullable(); // Field name if amount_source = 'field'
                $table->decimal('fixed_amount', 15, 2)->nullable();
                $table->text('calculation_formula')->nullable(); // For complex calculations
                $table->text('description_template')->nullable(); // Template for journal description
                $table->integer('sequence')->default(1); // Order of entries
                $table->timestamps();

                $table->index(['posting_rule_id', 'sequence']);
            });
        }

        // Create posting_rule_logs table if it doesn't exist
        if (!Schema::hasTable('posting_rule_logs')) {
            Schema::create('posting_rule_logs', function (Blueprint $table) {
                $table->id();
                $table->foreignId('posting_rule_id')->constrained();
                $table->string('source_type');
                $table->unsignedBigInteger('source_id');
                $table->string('action'); // 'executed', 'failed', 'skipped'
                $table->json('source_data')->nullable(); // Snapshot of source data
                $table->json('conditions_result')->nullable(); // Result of condition evaluation
                $table->foreignId('journal_id')->nullable()->constrained('journals')->onDelete('set null');
                $table->text('error_message')->nullable();
                $table->timestamp('executed_at');
                $table->foreignId('executed_by')->constrained('users');
                $table->timestamps();

                $table->index(['source_type', 'source_id']);
                $table->index(['posting_rule_id', 'executed_at']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('posting_rule_logs');
        Schema::dropIfExists('posting_rule_mappings');
        Schema::dropIfExists('posting_rule_conditions');
    }
};
