<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_opname_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('stock_opname_id');
            $table->unsignedBigInteger('product_id');
            $table->integer('system_quantity')->default(0);
            $table->integer('physical_quantity')->nullable();
            $table->integer('variance_quantity')->default(0);
            $table->decimal('unit_cost', 15, 2)->default(0);
            $table->decimal('variance_value', 15, 2)->default(0);
            $table->enum('variance_type', ['Match', 'Surplus', 'Shortage'])->default('Match');
            $table->text('variance_reason')->nullable();
            $table->text('notes')->nullable();
            $table->boolean('is_counted')->default(false);
            $table->timestamp('counted_at')->nullable();
            $table->unsignedBigInteger('counted_by')->nullable();
            $table->timestamps();

            // Unique constraint and indexes
            $table->unique(['stock_opname_id', 'product_id']);
            $table->index(['stock_opname_id', 'variance_type']);
            $table->index(['is_counted', 'variance_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_opname_items');
    }
};
