<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_opnames', function (Blueprint $table) {
            $table->id();
            $table->string('opname_number')->unique();
            $table->date('opname_date');
            $table->unsignedBigInteger('entitas_id');
            $table->unsignedBigInteger('warehouse_id')->nullable();
            $table->enum('status', ['Draft', 'In_Progress', 'Completed', 'Cancelled'])->default('Draft');
            $table->text('description')->nullable();
            $table->text('notes')->nullable();
            $table->decimal('total_variance_value', 15, 2)->default(0);
            $table->integer('total_items_counted')->default(0);
            $table->integer('total_variance_items')->default(0);
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->unsignedBigInteger('completed_by')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['entitas_id', 'warehouse_id', 'opname_date']);
            $table->index(['status', 'opname_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_opnames');
    }
};
