<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create stock_movements table
        Schema::create('stock_movements', function (Blueprint $table) {
            $table->id();
            $table->string('movement_number')->nullable(); // Auto-generated movement number
            $table->date('movement_date');
            $table->enum('movement_type', [
                'Opening_Balance',
                'Purchase_Receipt', 
                'Sales_Issue',
                'Transfer_In',
                'Transfer_Out',
                'Adjustment_In',
                'Adjustment_Out',
                'Production_In',
                'Production_Out'
            ]);
            $table->unsignedBigInteger('product_id');
            $table->unsignedBigInteger('warehouse_id');
            $table->unsignedBigInteger('entitas_id');
            $table->integer('quantity'); // Can be positive or negative
            $table->decimal('unit_cost', 12, 2)->default(0);
            $table->decimal('total_value', 12, 2)->default(0);
            $table->string('reference_type')->nullable(); // Model class name
            $table->unsignedBigInteger('reference_id')->nullable(); // Reference to source document
            $table->string('reference_number')->nullable(); // Human readable reference
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('product_id')->references('id')->on('produk')->onDelete('cascade');
            $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            $table->foreign('entitas_id')->references('id')->on('entitas')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');

            // Indexes for performance
            $table->index(['movement_date', 'movement_type']);
            $table->index(['product_id', 'warehouse_id', 'entitas_id']);
            $table->index(['reference_type', 'reference_id']);
        });

        // Create stock_adjustments table
        Schema::create('stock_adjustments', function (Blueprint $table) {
            $table->id();
            $table->string('adjustment_number')->unique(); // Auto-generated adjustment number
            $table->date('adjustment_date');
            $table->enum('adjustment_type', ['Increase', 'Decrease']);
            $table->string('adjustment_reason')->nullable();
            $table->unsignedBigInteger('warehouse_id');
            $table->unsignedBigInteger('entitas_id');
            $table->text('description')->nullable();
            $table->enum('status', ['Draft', 'Submitted', 'Approved', 'Cancelled'])->default('Draft');
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            $table->foreign('entitas_id')->references('id')->on('entitas')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['adjustment_date', 'status']);
            $table->index(['warehouse_id', 'entitas_id']);
        });

        // Create stock_adjustment_items table
        Schema::create('stock_adjustment_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('stock_adjustment_id');
            $table->unsignedBigInteger('product_id');
            $table->integer('system_quantity')->default(0); // Current system quantity
            $table->integer('physical_quantity')->nullable(); // Actual counted quantity
            $table->integer('quantity_adjustment')->default(0); // Difference (physical - system)
            $table->decimal('unit_cost', 12, 2)->default(0);
            $table->decimal('total_adjustment_value', 12, 2)->default(0);
            $table->text('item_notes')->nullable();
            $table->timestamps();

            // Foreign keys
            $table->foreign('stock_adjustment_id')->references('id')->on('stock_adjustments')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('produk')->onDelete('cascade');

            // Unique constraint
            $table->unique(['stock_adjustment_id', 'product_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_adjustment_items');
        Schema::dropIfExists('stock_adjustments');
        Schema::dropIfExists('stock_movements');
    }
};
