<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('cuti_izin', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('karyawan_id');
            $table->enum('jenis_permohonan', ['cuti', 'izin', 'sakit'])->comment('Type: cuti (leave), izin (permission), or sakit (sick leave)');
            $table->date('tanggal_mulai');
            $table->date('tanggal_selesai');
            $table->integer('jumlah_hari')->comment('Calculated number of days');
            $table->text('alasan')->comment('Reason for leave/permission request');
            $table->text('keterangan_tambahan')->nullable()->comment('Additional notes or details');
            $table->string('dokumen_pendukung')->nullable()->comment('Supporting document file path');
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->unsignedBigInteger('approved_by')->nullable()->comment('Supervisor who approved/rejected');
            $table->timestamp('approved_at')->nullable();
            $table->text('rejection_reason')->nullable()->comment('Reason for rejection if status is rejected');
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('karyawan_id')->references('id')->on('karyawan')->onDelete('cascade');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');

            // Indexes for better performance
            $table->index(['karyawan_id', 'status'], 'idx_cuti_izin_karyawan_status');
            $table->index(['tanggal_mulai', 'tanggal_selesai'], 'idx_cuti_izin_dates');
            $table->index(['jenis_permohonan', 'status'], 'idx_cuti_izin_type_status');
            $table->index('approved_by', 'idx_cuti_izin_approved_by');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cuti_izin');
    }
};
