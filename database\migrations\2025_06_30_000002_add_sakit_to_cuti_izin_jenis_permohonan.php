<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Add 'sakit' option to the jenis_permohonan enum
        DB::statement("ALTER TABLE cuti_izin MODIFY COLUMN jenis_permohonan ENUM('cuti', 'izin', 'sakit') COMMENT 'Type: cuti (leave), izin (permission), or sakit (sick leave)'");
    }

    public function down(): void
    {
        // Remove 'sakit' option from the jenis_permohonan enum
        DB::statement("ALTER TABLE cuti_izin MODIFY COLUMN jenis_permohonan ENUM('cuti', 'izin') COMMENT 'Type: cuti (leave) or izin (permission)'");
    }
};
