<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('entitas', function (Blueprint $table) {
            // Change latitude to support 17 decimal places
            // Total digits (M) = 2 (for integer part e.g., -90) + 17 (decimal places) = 19
            $table->decimal('latitude', 19, 17)->nullable()->change();

            // Change longitude to support 17 decimal places
            // Total digits (M) = 3 (for integer part e.g., -180) + 17 (decimal places) = 20
            $table->decimal('longitude', 20, 17)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('entitas', function (Blueprint $table) {
            // Revert latitude to its previous precision
            $table->decimal('latitude', 10, 8)->nullable()->change();

            // Revert longitude to its previous precision
            $table->decimal('longitude', 11, 8)->nullable()->change();
        });
    }
};
