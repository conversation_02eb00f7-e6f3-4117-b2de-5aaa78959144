<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('absensi', function (Blueprint $table) {
            // Update precision for latitude_masuk
            // From (10, 8) to (19, 17) for 17 decimal places (2 integer + 17 decimal)
            $table->decimal('latitude_masuk', 19, 17)->nullable()->change();

            // Update precision for longitude_masuk
            // From (11, 8) to (20, 17) for 17 decimal places (3 integer + 17 decimal)
            $table->decimal('longitude_masuk', 20, 17)->nullable()->change();

            // Update precision for latitude_keluar
            // From (10, 8) to (19, 17)
            $table->decimal('latitude_keluar', 19, 17)->nullable()->change();

            // Update precision for longitude_keluar
            // From (11, 8) to (20, 17)
            $table->decimal('longitude_keluar', 20, 17)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('absensi', function (Blueprint $table) {
            // Revert precision for latitude_masuk back to original
            $table->decimal('latitude_masuk', 10, 8)->nullable()->change();

            // Revert precision for longitude_masuk back to original
            $table->decimal('longitude_masuk', 11, 8)->nullable()->change();

            // Revert precision for latitude_keluar back to original
            $table->decimal('latitude_keluar', 10, 8)->nullable()->change();

            // Revert precision for longitude_keluar back to original
            $table->decimal('longitude_keluar', 11, 8)->nullable()->change();
        });
    }
};
