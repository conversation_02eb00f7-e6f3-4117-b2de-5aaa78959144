<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add entitas_id to jadwal_kerja table
        Schema::table('jadwal_kerja', function (Blueprint $table) {
            if (!Schema::hasColumn('jadwal_kerja', 'entitas_id')) {
                $table->unsignedBigInteger('entitas_id')->nullable()->after('karyawan_id')
                    ->comment('ID entitas/toko tempat karyawan bekerja pada jadwal ini');
                
                $table->foreign('entitas_id')->references('id')->on('entitas')->onDelete('set null');
                $table->index('entitas_id', 'idx_jadwal_kerja_entitas');
            }
        });

        // Add entitas_id to jadwal_masal table
        Schema::table('jadwal_masal', function (Blueprint $table) {
            if (!Schema::hasColumn('jadwal_masal', 'entitas_id')) {
                $table->unsignedBigInteger('entitas_id')->nullable()->after('shift_id')
                    ->comment('ID entitas/toko untuk jadwal masal ini');
                
                $table->foreign('entitas_id')->references('id')->on('entitas')->onDelete('set null');
                $table->index('entitas_id', 'idx_jadwal_masal_entitas');
            }
        });

        // Add composite indexes for better query performance
        Schema::table('jadwal_kerja', function (Blueprint $table) {
            // Index for filtering schedules by entitas and date
            if (!$this->indexExists('jadwal_kerja', 'idx_jadwal_entitas_tanggal')) {
                $table->index(['entitas_id', 'tanggal_jadwal'], 'idx_jadwal_entitas_tanggal');
            }
            
            // Index for filtering schedules by karyawan and entitas
            if (!$this->indexExists('jadwal_kerja', 'idx_jadwal_karyawan_entitas')) {
                $table->index(['karyawan_id', 'entitas_id'], 'idx_jadwal_karyawan_entitas');
            }
        });

        Schema::table('jadwal_masal', function (Blueprint $table) {
            // Index for filtering bulk schedules by entitas and period
            if (!$this->indexExists('jadwal_masal', 'idx_jadwal_masal_entitas_periode')) {
                $table->index(['entitas_id', 'tanggal_mulai', 'tanggal_selesai'], 'idx_jadwal_masal_entitas_periode');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('jadwal_kerja', function (Blueprint $table) {
            // Drop indexes first
            if ($this->indexExists('jadwal_kerja', 'idx_jadwal_entitas_tanggal')) {
                $table->dropIndex('idx_jadwal_entitas_tanggal');
            }
            if ($this->indexExists('jadwal_kerja', 'idx_jadwal_karyawan_entitas')) {
                $table->dropIndex('idx_jadwal_karyawan_entitas');
            }
            if ($this->indexExists('jadwal_kerja', 'idx_jadwal_kerja_entitas')) {
                $table->dropIndex('idx_jadwal_kerja_entitas');
            }
            
            // Drop foreign key and column
            if (Schema::hasColumn('jadwal_kerja', 'entitas_id')) {
                $table->dropForeign(['entitas_id']);
                $table->dropColumn('entitas_id');
            }
        });

        Schema::table('jadwal_masal', function (Blueprint $table) {
            // Drop indexes first
            if ($this->indexExists('jadwal_masal', 'idx_jadwal_masal_entitas_periode')) {
                $table->dropIndex('idx_jadwal_masal_entitas_periode');
            }
            if ($this->indexExists('jadwal_masal', 'idx_jadwal_masal_entitas')) {
                $table->dropIndex('idx_jadwal_masal_entitas');
            }
            
            // Drop foreign key and column
            if (Schema::hasColumn('jadwal_masal', 'entitas_id')) {
                $table->dropForeign(['entitas_id']);
                $table->dropColumn('entitas_id');
            }
        });
    }

    /**
     * Check if index exists
     */
    private function indexExists(string $table, string $index): bool
    {
        $indexes = Schema::getConnection()->getDoctrineSchemaManager()
            ->listTableIndexes($table);
        
        return array_key_exists($index, $indexes);
    }
};
