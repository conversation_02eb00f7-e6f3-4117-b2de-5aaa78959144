<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();

            // Basic Information
            $table->string('nama')->comment('Nama lengkap pelanggan');
            $table->string('email')->nullable()->unique()->comment('Email pelanggan');
            $table->string('telepon')->nullable()->index()->comment('Nomor telepon pelanggan');
            $table->text('alamat')->nullable()->comment('Alamat lengkap pelanggan');

            // Personal Information
            $table->date('tanggal_lahir')->nullable()->comment('Tanggal lahir pelanggan');
            $table->enum('jenis_kelamin', ['L', 'P'])->nullable()->comment('Jenis kelamin: L=Laki-laki, P=Perempuan');

            // Business Information
            $table->integer('loyalty_points')->default(0)->comment('Poin loyalitas pelanggan');
            $table->string('segment')->nullable()->index()->comment('Segmen pelanggan: top_spenders, frequent_buyers, lapsed_customers, product_specific_buyers');

            // Additional Information
            $table->text('notes')->nullable()->comment('Catatan tambahan tentang pelanggan');

            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['nama']);
            $table->index(['loyalty_points']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
