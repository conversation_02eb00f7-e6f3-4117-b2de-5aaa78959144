<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loyalty_transactions', function (Blueprint $table) {
            $table->id();

            // Foreign Keys
            $table->foreignId('customer_id')->constrained('customers')->onDelete('cascade');

            // Transaction Details
            $table->enum('type', ['earn', 'redeem'])->comment('Jenis transaksi: earn=mendapat poin, redeem=tukar poin');
            $table->integer('points')->comment('Ju<PERSON><PERSON> poin (positif untuk earn, negatif untuk redeem)');

            // Reference Information
            $table->unsignedBigInteger('transaction_reference_id')->nullable()->comment('ID referensi ke PosTransaction/SalesOrder');
            $table->string('reference_type')->nullable()->comment('Tipe referensi: pos_transaction, sales_order, manual');

            // Description
            $table->text('description')->comment('Deskripsi transaksi loyalitas');

            $table->timestamps();

            // Indexes
            $table->index(['customer_id', 'type'], 'loyalty_customer_type_idx');
            $table->index(['transaction_reference_id', 'reference_type'], 'loyalty_reference_idx');
            $table->index(['created_at'], 'loyalty_created_at_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loyalty_transactions');
    }
};
