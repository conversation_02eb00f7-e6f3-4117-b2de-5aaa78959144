<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_feedback', function (Blueprint $table) {
            $table->id();

            // Foreign Keys
            $table->foreignId('customer_id')->constrained('customers')->onDelete('cascade');
            $table->foreignId('assigned_to_user_id')->nullable()->constrained('users')->onDelete('set null');

            // Feedback Details
            $table->enum('type', ['keluhan', 'saran'])->comment('Jenis feedback: keluhan atau saran');
            $table->string('subject')->comment('Subjek feedback');
            $table->text('description')->comment('Deskripsi detail feedback');

            // Status Management
            $table->enum('status', ['new', 'in_progress', 'resolved'])->default('new')->comment('Status penanganan feedback');
            $table->text('resolution_notes')->nullable()->comment('Catatan penyelesaian');

            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['customer_id', 'type'], 'feedback_customer_type_idx');
            $table->index(['assigned_to_user_id'], 'feedback_assigned_user_idx');
            $table->index(['status'], 'feedback_status_idx');
            $table->index(['created_at'], 'feedback_created_at_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_feedback');
    }
};
