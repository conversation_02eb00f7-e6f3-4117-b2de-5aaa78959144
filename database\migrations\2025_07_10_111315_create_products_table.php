<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();

            // Basic Information
            $table->string('name')->comment('Nama produk');
            $table->string('sku')->nullable()->unique()->comment('Stock Keeping Unit');
            $table->string('barcode')->nullable()->unique()->comment('Barcode produk');
            $table->text('description')->nullable()->comment('Deskripsi produk');

            // Pricing
            $table->decimal('price', 15, 2)->comment('Harga jual');
            $table->decimal('cost_price', 15, 2)->nullable()->comment('Harga beli/cost');

            // Category
            $table->foreignId('category_id')->constrained('categories')->onDelete('cascade');

            // Inventory
            $table->integer('stock_quantity')->default(0)->comment('Jumlah stok');

            // Status
            $table->boolean('is_active')->default(true)->comment('Status aktif produk');
            $table->boolean('is_food_item')->default(false)->comment('Apakah produk makanan/minuman');

            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['name']);
            $table->index(['category_id']);
            $table->index(['is_active']);
            $table->index(['is_food_item']);
            $table->index(['stock_quantity']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
