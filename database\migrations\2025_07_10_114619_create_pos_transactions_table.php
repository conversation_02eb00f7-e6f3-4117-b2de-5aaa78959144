<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pos_transactions', function (Blueprint $table) {
            $table->id();

            // Transaction Information
            $table->string('transaction_number')->unique()->comment('Nomor transaksi auto-generated');
            $table->foreignId('customer_id')->nullable()->constrained('customers')->onDelete('set null');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade')->comment('Kasir/User yang melakukan transaksi');

            // Transaction Details
            $table->dateTime('transaction_date')->comment('Tanggal dan waktu transaksi');

            // Amounts
            $table->decimal('total_amount', 15, 2)->comment('Total sebelum diskon dan pajak');
            $table->decimal('discount_amount', 15, 2)->default(0)->comment('Jumlah diskon');
            $table->decimal('tax_amount', 15, 2)->default(0)->comment('Jumlah pajak');
            $table->decimal('net_amount', 15, 2)->comment('Total bersih setelah diskon dan pajak');

            // Payment Information
            $table->enum('payment_method', ['cash', 'card', 'transfer', 'ewallet', 'qris'])->comment('Metode pembayaran');
            $table->decimal('amount_paid', 15, 2)->comment('Jumlah yang dibayar');
            $table->decimal('change_given', 15, 2)->default(0)->comment('Kembalian');

            // Loyalty Information
            $table->integer('loyalty_points_used')->default(0)->comment('Poin loyalitas yang digunakan');
            $table->integer('loyalty_points_earned')->default(0)->comment('Poin loyalitas yang didapat');

            // Additional Information
            $table->string('table_number')->nullable()->comment('Nomor meja (untuk F&B)');
            $table->boolean('is_offline_transaction')->default(false)->comment('Transaksi offline yang belum tersinkron');
            $table->timestamp('synced_at')->nullable()->comment('Waktu sinkronisasi untuk transaksi offline');

            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['transaction_number']);
            $table->index(['customer_id']);
            $table->index(['user_id']);
            $table->index(['transaction_date']);
            $table->index(['payment_method']);
            $table->index(['is_offline_transaction']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pos_transactions');
    }
};
