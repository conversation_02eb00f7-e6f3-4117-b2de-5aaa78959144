<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pos_transaction_items', function (Blueprint $table) {
            $table->id();

            // Foreign Keys
            $table->foreignId('pos_transaction_id')->constrained('pos_transactions')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');

            // Item Details
            $table->integer('quantity')->comment('Jumlah item');
            $table->decimal('unit_price', 15, 2)->comment('Harga per unit saat transaksi');
            $table->decimal('discount_per_item', 15, 2)->default(0)->comment('Diskon per item');
            $table->decimal('total_price', 15, 2)->comment('Total harga item (quantity * (unit_price - discount_per_item))');

            $table->timestamps();

            // Indexes
            $table->index(['pos_transaction_id']);
            $table->index(['product_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pos_transaction_items');
    }
};
