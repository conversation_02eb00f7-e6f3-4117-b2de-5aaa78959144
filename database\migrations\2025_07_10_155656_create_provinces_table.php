<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('provinces', function (Blueprint $table) {
            $table->id();

            // Province Information
            $table->string('code', 10)->unique()->comment('<PERSON><PERSON> provinsi (misal: 11, 12, 13)');
            $table->string('name')->comment('Nama provinsi');
            $table->string('slug')->unique()->comment('Slug untuk URL');

            // Additional Information
            $table->text('description')->nullable()->comment('<PERSON>kripsi provinsi');
            $table->boolean('is_active')->default(true)->comment('Status aktif');

            $table->timestamps();

            // Indexes
            $table->index(['code']);
            $table->index(['name']);
            $table->index(['slug']);
            $table->index(['is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('provinces');
    }
};
