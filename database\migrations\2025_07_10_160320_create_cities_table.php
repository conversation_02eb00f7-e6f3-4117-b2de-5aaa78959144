<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cities', function (Blueprint $table) {
            $table->id();

            // Foreign Key
            $table->foreignId('province_id')->constrained('provinces')->onDelete('cascade');

            // City Information
            $table->string('code', 10)->comment('Kode kota/kabupaten (misal: 1101, 1102)');
            $table->string('name')->comment('Nama kota/kabupaten');
            $table->string('slug')->comment('Slug untuk URL');
            $table->enum('type', ['kabupaten', 'kota'])->comment('Tipe: kabupaten atau kota');

            // Additional Information
            $table->text('description')->nullable()->comment('Deskripsi kota/kabupaten');
            $table->boolean('is_active')->default(true)->comment('Status aktif');

            $table->timestamps();

            // Indexes
            $table->index(['province_id']);
            $table->index(['code']);
            $table->index(['name']);
            $table->index(['slug']);
            $table->index(['type']);
            $table->index(['is_active']);

            // Unique constraint
            $table->unique(['province_id', 'code']);
            $table->unique(['province_id', 'slug']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cities');
    }
};
