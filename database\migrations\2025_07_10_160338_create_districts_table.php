<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('districts', function (Blueprint $table) {
            $table->id();

            // Foreign Keys
            $table->foreignId('province_id')->constrained('provinces')->onDelete('cascade');
            $table->foreignId('city_id')->constrained('cities')->onDelete('cascade');

            // District Information
            $table->string('code', 10)->comment('Kode kecamatan (misal: 110101, 110102)');
            $table->string('name')->comment('Nama kecamatan');
            $table->string('slug')->comment('Slug untuk URL');

            // Additional Information
            $table->text('description')->nullable()->comment('Deskripsi kecamatan');
            $table->boolean('is_active')->default(true)->comment('Status aktif');

            $table->timestamps();

            // Indexes
            $table->index(['province_id']);
            $table->index(['city_id']);
            $table->index(['code']);
            $table->index(['name']);
            $table->index(['slug']);
            $table->index(['is_active']);

            // Unique constraint
            $table->unique(['city_id', 'code']);
            $table->unique(['city_id', 'slug']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('districts');
    }
};
