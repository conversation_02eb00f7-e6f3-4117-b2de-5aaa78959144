<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('villages', function (Blueprint $table) {
            $table->id();

            // Foreign Keys
            $table->foreignId('province_id')->constrained('provinces')->onDelete('cascade');
            $table->foreignId('city_id')->constrained('cities')->onDelete('cascade');
            $table->foreignId('district_id')->constrained('districts')->onDelete('cascade');

            // Village Information
            $table->string('code', 15)->comment('Kode kelurahan/desa (misal: 1101010001)');
            $table->string('name')->comment('Nama kelurahan/desa');
            $table->string('slug')->comment('Slug untuk URL');
            $table->enum('type', ['kelurahan', 'desa'])->comment('Tipe: kelurahan atau desa');

            // Additional Information
            $table->string('postal_code', 10)->nullable()->comment('Kode pos');
            $table->text('description')->nullable()->comment('Deskripsi kelurahan/desa');
            $table->boolean('is_active')->default(true)->comment('Status aktif');

            $table->timestamps();

            // Indexes
            $table->index(['province_id']);
            $table->index(['city_id']);
            $table->index(['district_id']);
            $table->index(['code']);
            $table->index(['name']);
            $table->index(['slug']);
            $table->index(['type']);
            $table->index(['postal_code']);
            $table->index(['is_active']);

            // Unique constraint
            $table->unique(['district_id', 'code']);
            $table->unique(['district_id', 'slug']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('villages');
    }
};
