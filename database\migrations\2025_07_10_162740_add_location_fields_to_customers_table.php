<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Add location fields
            $table->foreignId('province_id')->nullable()->constrained('provinces')->onDelete('set null')->comment('ID Provinsi');
            $table->foreignId('city_id')->nullable()->constrained('cities')->onDelete('set null')->comment('ID Kota/Kabupaten');
            $table->foreignId('district_id')->nullable()->constrained('districts')->onDelete('set null')->comment('ID Kecamatan');
            $table->foreignId('village_id')->nullable()->constrained('villages')->onDelete('set null')->comment('ID Kelurahan/Desa');

            // Additional address fields
            $table->string('postal_code', 10)->nullable()->comment('Kode pos');
            $table->text('detail_address')->nullable()->comment('Alamat detail (RT/RW, nama jalan, dll)');

            // Add indexes for better performance
            $table->index(['province_id']);
            $table->index(['city_id']);
            $table->index(['district_id']);
            $table->index(['village_id']);
            $table->index(['postal_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['province_id']);
            $table->dropIndex(['city_id']);
            $table->dropIndex(['district_id']);
            $table->dropIndex(['village_id']);
            $table->dropIndex(['postal_code']);

            // Drop foreign key constraints
            $table->dropForeign(['province_id']);
            $table->dropForeign(['city_id']);
            $table->dropForeign(['district_id']);
            $table->dropForeign(['village_id']);

            // Drop columns
            $table->dropColumn([
                'province_id',
                'city_id',
                'district_id',
                'village_id',
                'postal_code',
                'detail_address'
            ]);
        });
    }
};
