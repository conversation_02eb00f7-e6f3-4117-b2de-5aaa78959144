<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Since the role field is already varchar (from previous migration),
        // we just need to ensure the 'manager' role is properly supported.
        // The field can already accept 'manager' value, but we'll add a comment for clarity.

        Schema::table('users', function (Blueprint $table) {
            // Update the role column comment to include manager
            $table->string('role', 50)->default('karyawan')->change()
                ->comment('User role: admin, supervisor, manager, karyawan');
        });

        // Update any existing users that should be managers
        // This is optional and can be customized based on business logic
        $this->updateExistingManagerUsers();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Convert any manager roles back to supervisor or karyawan
        DB::table('users')
            ->where('role', 'manager')
            ->update(['role' => 'supervisor']);

        Schema::table('users', function (Blueprint $table) {
            // Revert comment
            $table->string('role', 50)->default('karyawan')->change()
                ->comment('User role: admin, supervisor, karyawan');
        });
    }

    /**
     * Update existing users to manager role based on business logic
     */
    private function updateExistingManagerUsers(): void
    {
        // Example: Update specific users to manager role
        // You can customize this based on your business requirements

        // Update users with specific email patterns to manager
        DB::table('users')
            ->where('email', 'like', '%manager%')
            ->where('role', '!=', 'admin')
            ->update(['role' => 'manager']);

        // Or update based on name patterns
        DB::table('users')
            ->where('name', 'like', '%Manager%')
            ->where('role', '!=', 'admin')
            ->update(['role' => 'manager']);
    }
};
