<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the role column comment to include keptok
        Schema::table('users', function (Blueprint $table) {
            $table->string('role', 50)->default('karyawan')->change()
                ->comment('User role: admin, supervisor, manager, keptok, karyawan');
        });

        // Update any existing users that should be keptok
        $this->updateExistingKeptokUsers();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Convert any keptok roles back to karyawan
        DB::table('users')
            ->where('role', 'keptok')
            ->update(['role' => 'karyawan']);

        Schema::table('users', function (Blueprint $table) {
            // Revert comment
            $table->string('role', 50)->default('karyawan')->change()
                ->comment('User role: admin, supervisor, manager, karyawan');
        });
    }

    /**
     * Update existing users to keptok role based on business logic
     */
    private function updateExistingKeptokUsers(): void
    {
        // Update users with specific email patterns to keptok
        DB::table('users')
            ->where('email', 'like', '%keptok%')
            ->where('role', '!=', 'admin')
            ->update(['role' => 'keptok']);

        // Or update based on name patterns
        DB::table('users')
            ->where('name', 'like', '%Kepala Toko%')
            ->where('role', '!=', 'admin')
            ->update(['role' => 'keptok']);

        // Update users with "Keptok" in name
        DB::table('users')
            ->where('name', 'like', '%Keptok%')
            ->where('role', '!=', 'admin')
            ->update(['role' => 'keptok']);
    }
};
