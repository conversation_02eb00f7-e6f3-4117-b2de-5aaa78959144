<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loyalty_programs', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('type')->default('points'); // points, cashback, discount
            $table->boolean('is_active')->default(true);

            // Points Configuration
            $table->decimal('points_per_amount', 10, 2)->default(1000); // 1 point per 1000 rupiah
            $table->decimal('points_multiplier', 5, 2)->default(1.00);
            $table->decimal('minimum_transaction', 12, 2)->default(10000);
            $table->integer('max_points_per_transaction')->default(1000);

            // Redemption Configuration
            $table->decimal('redemption_rate', 10, 2)->default(100); // 100 points = 1000 rupiah
            $table->integer('minimum_redemption')->default(50);

            // Time-based Rules
            $table->json('time_multipliers')->nullable(); // Happy hour, etc
            $table->json('day_multipliers')->nullable(); // Weekend bonus, etc

            // Customer Tier Rules
            $table->json('tier_multipliers')->nullable();

            // Special Bonuses
            $table->integer('birthday_bonus_points')->default(100);
            $table->decimal('vip_bonus_multiplier', 5, 2)->default(1.50);

            // Validity
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->integer('points_expiry_months')->default(12);

            // Audit
            $table->foreignId('created_by')->nullable()->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['is_active', 'start_date', 'end_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loyalty_programs');
    }
};
