<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create objectives table
        Schema::create('objectives', function (Blueprint $table) {
            $table->id();
            $table->string('nama_objective');
            $table->text('deskripsi')->nullable();
            $table->date('periode_mulai');
            $table->date('periode_selesai');
            $table->enum('status', ['draft', 'active', 'completed', 'cancelled'])->default('draft');
            $table->integer('progress_percentage')->default(0);
            $table->date('target_completion')->nullable();
            $table->date('actual_completion')->nullable();
            $table->unsignedBigInteger('owner_id');
            $table->unsignedBigInteger('departemen_id')->nullable();
            $table->unsignedBigInteger('divisi_id')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['status', 'periode_mulai', 'periode_selesai']);
            $table->index(['owner_id', 'status']);
            $table->index(['departemen_id', 'divisi_id']);
        });

        // Create key_results table
        Schema::create('key_results', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('objective_id');
            $table->string('nama_key_result');
            $table->text('deskripsi')->nullable();
            $table->enum('tipe_metrik', ['number', 'percentage', 'currency', 'boolean'])->default('number');
            $table->decimal('target_value', 15, 2);
            $table->decimal('current_value', 15, 2)->default(0);
            $table->string('unit_measurement')->nullable();
            $table->integer('progress_percentage')->default(0);
            $table->enum('status', ['not_started', 'in_progress', 'completed', 'at_risk'])->default('not_started');
            $table->date('due_date')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['objective_id', 'status']);
            $table->index(['due_date', 'status']);
        });

        // Create tactics table
        Schema::create('tactics', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('objective_id');
            $table->string('nama_tactic');
            $table->text('deskripsi')->nullable();
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->enum('status', ['planned', 'in_progress', 'completed', 'blocked'])->default('planned');
            $table->integer('progress_percentage')->default(0);
            $table->date('start_date')->nullable();
            $table->date('due_date')->nullable();
            $table->unsignedBigInteger('assigned_to')->nullable();
            $table->integer('estimated_hours')->nullable();
            $table->integer('actual_hours')->default(0);
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['objective_id', 'status']);
            $table->index(['assigned_to', 'status']);
            $table->index(['priority', 'due_date']);
        });

        // Create objective_tasks pivot table (many-to-many relationship)
        // Objectives are connected to Tasks directly
        if (Schema::hasTable('tasks')) {
            Schema::create('objective_tasks', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('objective_id');
                $table->unsignedBigInteger('task_id');
                $table->integer('contribution_percentage')->default(100);
                $table->timestamps();

                // Unique constraint to prevent duplicate relationships
                $table->unique(['objective_id', 'task_id']);

                // Indexes for performance
                $table->index(['objective_id']);
                $table->index(['task_id']);
            });
        }

        // Create supporting documents table
        Schema::create('okr_documents', function (Blueprint $table) {
            $table->id();
            $table->string('documentable_type'); // Objective, KeyResult, Tactic
            $table->unsignedBigInteger('documentable_id');
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('file_path');
            $table->string('file_name');
            $table->string('file_type');
            $table->integer('file_size');
            $table->unsignedBigInteger('uploaded_by');
            $table->timestamps();
            $table->softDeletes();

            // Polymorphic index
            $table->index(['documentable_type', 'documentable_id']);
            $table->index(['uploaded_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tactic_tasks');
        Schema::dropIfExists('tactics');
        Schema::dropIfExists('key_results');
        Schema::dropIfExists('objectives');
    }
};
