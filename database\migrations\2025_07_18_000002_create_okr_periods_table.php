<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create okr_periods table
        Schema::create('okr_periods', function (Blueprint $table) {
            $table->id();
            $table->string('nama_periode');
            $table->text('deskripsi')->nullable();
            $table->enum('tipe_periode', ['quarterly', 'yearly', 'monthly', 'custom'])->default('quarterly');
            $table->integer('tahun');
            $table->integer('quarter')->nullable(); // For quarterly periods
            $table->date('tanggal_mulai');
            $table->date('tanggal_selesai');
            $table->enum('status', ['draft', 'active', 'completed', 'archived'])->default('draft');
            $table->boolean('is_active')->default(false);
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['tipe_periode', 'tahun', 'quarter']);
            $table->index(['tanggal_mulai', 'tanggal_selesai']);
            $table->index(['status', 'is_active']);

            // Index for active periods of same type
            $table->index(['tipe_periode', 'is_active']);
        });

        // Add okr_period_id to objectives table
        Schema::table('objectives', function (Blueprint $table) {
            $table->unsignedBigInteger('okr_period_id')->nullable()->after('id');
            $table->index(['okr_period_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('objectives', function (Blueprint $table) {
            $table->dropColumn('okr_period_id');
        });

        Schema::dropIfExists('okr_periods');
    }
};
