<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Note: Both objective_tasks and okr_documents tables are already created
        // in 2025_07_18_000001_create_okr_tables.php
        // This migration only handles the tactic_tasks table removal

        // Drop tactic_tasks table if exists (tactics no longer connect to tasks)
        Schema::dropIfExists('tactic_tasks');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Note: Both okr_documents and objective_tasks tables are managed
        // by 2025_07_18_000001_create_okr_tables.php - don't drop them here
        // This migration only handles recreating tactic_tasks table

        // Recreate tactic_tasks if needed
        if (Schema::hasTable('tasks')) {
            Schema::create('tactic_tasks', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('tactic_id');
                $table->unsignedBigInteger('task_id');
                $table->integer('contribution_percentage')->default(100);
                $table->timestamps();

                $table->unique(['tactic_id', 'task_id']);
                $table->index(['tactic_id']);
                $table->index(['task_id']);
            });
        }
    }
};
