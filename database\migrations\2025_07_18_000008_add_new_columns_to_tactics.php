<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tactics', function (Blueprint $table) {
            // Add new columns for proper tactic structure
            $table->string('jenis_tactic')->nullable()->after('deskripsi');
            $table->unsignedBigInteger('pemilik')->nullable()->after('assigned_to');
            $table->date('tanggal_mulai')->nullable()->after('pemilik');
            $table->date('tanggal_target')->nullable()->after('tanggal_mulai');
            $table->integer('estimasi_effort')->nullable()->comment('Estimasi effort dalam jam')->after('tanggal_target');
            $table->integer('skor_dampak')->nullable()->comment('Skor dampak 1-10')->after('estimasi_effort');
            $table->text('sumber_daya')->nullable()->after('actual_hours');
            $table->text('kriteria_keberhasilan')->nullable()->after('sumber_daya');
            $table->text('dependensi')->nullable()->after('kriteria_keberhasilan');
            
            // Add index for pemilik
            $table->index(['pemilik']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tactics', function (Blueprint $table) {
            $table->dropIndex(['pemilik']);
            $table->dropColumn([
                'jenis_tactic',
                'pemilik',
                'tanggal_mulai',
                'tanggal_target',
                'estimasi_effort',
                'skor_dampak',
                'sumber_daya',
                'kriteria_keberhasilan',
                'dependensi'
            ]);
        });
    }
};
