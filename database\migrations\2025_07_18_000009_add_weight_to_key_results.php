<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('key_results', function (Blueprint $table) {
            $table->integer('weight')->default(1)->after('progress_percentage')
                  ->comment('Weight for calculating objective progress (1-10)');
            $table->json('milestones')->nullable()->after('weight')
                  ->comment('JSON array of milestones for tracking progress');
            $table->timestamp('last_updated_at')->nullable()->after('milestones')
                  ->comment('When progress was last updated');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('key_results', function (Blueprint $table) {
            $table->dropColumn(['weight', 'milestones', 'last_updated_at']);
        });
    }
};
