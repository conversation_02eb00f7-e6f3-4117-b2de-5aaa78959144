<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transaction_subcategories', function (Blueprint $table) {
            if (!Schema::hasColumn('transaction_subcategories', 'category_id')) {
                $table->foreignId('category_id')->after('id')->constrained('transaction_categories')->onDelete('cascade');
            }
            if (!Schema::hasColumn('transaction_subcategories', 'code')) {
                $table->string('code')->after('category_id')->unique();
            }
            if (!Schema::hasColumn('transaction_subcategories', 'name')) {
                $table->string('name')->after('code');
            }
            if (!Schema::hasColumn('transaction_subcategories', 'description')) {
                $table->text('description')->nullable()->after('name');
            }
            if (!Schema::hasColumn('transaction_subcategories', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('description');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transaction_subcategories', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
            $table->dropColumn(['category_id', 'code', 'name', 'description', 'is_active']);
        });
    }
};
