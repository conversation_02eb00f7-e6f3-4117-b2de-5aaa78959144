<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create sales_orders table
        Schema::create('sales_orders', function (Blueprint $table) {
            $table->id();
            $table->string('so_number')->unique(); // Auto-generated SO number
            $table->date('so_date');
            $table->date('expected_delivery_date')->nullable();
            $table->unsignedBigInteger('customer_id');
            $table->unsignedBigInteger('warehouse_id'); // Gudang asal
            $table->unsignedBigInteger('entitas_id'); // Entitas yang melakukan penjualan
            $table->decimal('subtotal', 12, 2);
            $table->decimal('tax_amount', 12, 2)->default(0);
            $table->decimal('discount_amount', 12, 2)->default(0);
            $table->decimal('total_amount', 12, 2);
            $table->enum('status', ['Draft', 'Submitted', 'Approved', 'Partially_Issued', 'Completed', 'Cancelled'])->default('Draft');
            $table->text('notes')->nullable();
            $table->string('payment_terms')->nullable(); // Net 30, COD, dll
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
            $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            $table->foreign('entitas_id')->references('id')->on('entitas')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['so_date', 'status']);
            $table->index(['customer_id', 'warehouse_id']);
        });

        // Create sales_order_items table
        Schema::create('sales_order_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('sales_order_id');
            $table->unsignedBigInteger('product_id');
            $table->integer('quantity_ordered');
            $table->integer('quantity_issued')->default(0);
            $table->decimal('unit_price', 12, 2);
            $table->decimal('total_price', 12, 2); // quantity_ordered * unit_price
            $table->text('notes')->nullable();
            $table->timestamps();

            // Foreign keys
            $table->foreign('sales_order_id')->references('id')->on('sales_orders')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('produk')->onDelete('cascade');

            // Unique constraint
            $table->unique(['sales_order_id', 'product_id']);
        });

        // Create goods_issues table (Pengeluaran Barang)
        Schema::create('goods_issues', function (Blueprint $table) {
            $table->id();
            $table->string('issue_number')->unique(); // Auto-generated issue number
            $table->date('issue_date');
            $table->unsignedBigInteger('sales_order_id')->nullable(); // Bisa null untuk issue tanpa SO
            $table->unsignedBigInteger('warehouse_id');
            $table->enum('issue_type', ['Sales_Order', 'Transfer_Out', 'Adjustment', 'Production', 'Other'])->default('Sales_Order');
            $table->string('reference_number')->nullable(); // Nomor referensi eksternal
            $table->enum('status', ['Draft', 'Completed', 'Cancelled'])->default('Draft');
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('issued_by')->nullable(); // User yang mengeluarkan
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('sales_order_id')->references('id')->on('sales_orders')->onDelete('set null');
            $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            $table->foreign('issued_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['issue_date', 'status']);
            $table->index(['warehouse_id', 'issue_type']);
        });

        // Create goods_issue_items table
        Schema::create('goods_issue_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('goods_issue_id');
            $table->unsignedBigInteger('sales_order_item_id')->nullable(); // Null jika bukan dari SO
            $table->unsignedBigInteger('product_id');
            $table->integer('quantity_issued');
            $table->decimal('unit_cost', 12, 2); // Cost untuk HPP calculation
            $table->decimal('total_cost', 12, 2); // quantity_issued * unit_cost
            $table->text('notes')->nullable();
            $table->timestamps();

            // Foreign keys
            $table->foreign('goods_issue_id')->references('id')->on('goods_issues')->onDelete('cascade');
            $table->foreign('sales_order_item_id')->references('id')->on('sales_order_items')->onDelete('set null');
            $table->foreign('product_id')->references('id')->on('produk')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('goods_issue_items');
        Schema::dropIfExists('goods_issues');
        Schema::dropIfExists('sales_order_items');
        Schema::dropIfExists('sales_orders');
    }
};
