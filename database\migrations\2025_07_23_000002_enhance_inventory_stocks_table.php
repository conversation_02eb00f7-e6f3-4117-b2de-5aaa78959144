<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inventory_stocks', function (Blueprint $table) {
            // Add new quantity tracking fields
            $table->integer('available_quantity')->default(0)->after('quantity')->comment('Quantity available for sale/use');
            $table->integer('on_hold_quantity')->default(0)->after('available_quantity')->comment('Quantity on hold (quality check, etc.)');
            $table->integer('reserved_quantity')->default(0)->after('on_hold_quantity')->comment('Quantity reserved for sales orders');
            
            // Add reorder point and safety stock
            $table->integer('reorder_point')->default(0)->after('maximum_stock')->comment('Reorder point for automatic procurement');
            $table->integer('safety_stock')->default(0)->after('reorder_point')->comment('Safety stock level');
            
            // Add location tracking within warehouse
            $table->string('location_code')->nullable()->after('safety_stock')->comment('Location code within warehouse (e.g., A1-B2-C3)');
            
            // Add batch/lot tracking capability
            $table->boolean('is_batch_tracked')->default(false)->after('location_code')->comment('Whether this product uses batch/lot tracking');
            
            // Add last movement tracking
            $table->timestamp('last_movement_at')->nullable()->after('last_updated')->comment('Timestamp of last stock movement');
            $table->string('last_movement_type')->nullable()->after('last_movement_at')->comment('Type of last movement');
        });

        // Update existing records to set available_quantity = quantity
        DB::statement('UPDATE inventory_stocks SET available_quantity = quantity WHERE available_quantity = 0');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inventory_stocks', function (Blueprint $table) {
            $table->dropColumn([
                'available_quantity',
                'on_hold_quantity', 
                'reserved_quantity',
                'reorder_point',
                'safety_stock',
                'location_code',
                'is_batch_tracked',
                'last_movement_at',
                'last_movement_type'
            ]);
        });
    }
};
