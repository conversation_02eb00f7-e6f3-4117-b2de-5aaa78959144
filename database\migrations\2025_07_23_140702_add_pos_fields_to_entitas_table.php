<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('entitas', function (Blueprint $table) {
            // Check if columns don't exist before adding them
            if (!Schema::hasColumn('entitas', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('nama');
            }
            if (!Schema::hasColumn('entitas', 'jenis')) {
                $table->enum('jenis', ['toko', 'gudang', 'kantor', 'cabang'])->default('toko')->after('is_active');
            }
            if (!Schema::hasColumn('entitas', 'kode')) {
                $table->string('kode')->nullable()->after('jenis');
            }
            if (!Schema::hasColumn('entitas', 'kota')) {
                $table->string('kota')->nullable()->after('kode');
            }
            if (!Schema::hasColumn('entitas', 'manager_name')) {
                $table->string('manager_name')->nullable()->after('kota');
            }
            if (!Schema::hasColumn('entitas', 'email')) {
                $table->string('email')->nullable()->after('manager_name');
            }
            if (!Schema::hasColumn('entitas', 'opening_date')) {
                $table->date('opening_date')->nullable()->after('email');
            }
            if (!Schema::hasColumn('entitas', 'opening_time')) {
                $table->time('opening_time')->default('08:00')->after('opening_date');
            }
            if (!Schema::hasColumn('entitas', 'closing_time')) {
                $table->time('closing_time')->default('22:00')->after('opening_time');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('entitas', function (Blueprint $table) {
            $table->dropColumn([
                'is_active',
                'jenis',
                'kode',
                'kota',
                'manager_name',
                'email',
                'opening_date',
                'opening_time',
                'closing_time'
            ]);
        });
    }
};
