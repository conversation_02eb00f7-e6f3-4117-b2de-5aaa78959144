<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('outlets', function (Blueprint $table) {
            // Basic outlet information
            if (!Schema::hasColumn('outlets', 'code')) {
                $table->string('code')->nullable()->after('name');
            }
            if (!Schema::hasColumn('outlets', 'type')) {
                $table->enum('type', ['toko', 'restoran', 'kafe', 'minimarket', 'supermarket'])->default('toko')->after('code');
            }
            if (!Schema::hasColumn('outlets', 'city')) {
                $table->string('city')->nullable()->after('address');
            }
            if (!Schema::hasColumn('outlets', 'phone')) {
                $table->string('phone')->nullable()->after('city');
            }
            if (!Schema::hasColumn('outlets', 'email')) {
                $table->string('email')->nullable()->after('phone');
            }

            // Management information
            if (!Schema::hasColumn('outlets', 'manager_name')) {
                $table->string('manager_name')->nullable()->after('email');
            }
            if (!Schema::hasColumn('outlets', 'opening_date')) {
                $table->date('opening_date')->nullable()->after('manager_name');
            }

            // Operating hours
            if (!Schema::hasColumn('outlets', 'opening_time')) {
                $table->time('opening_time')->default('08:00')->after('opening_date');
            }
            if (!Schema::hasColumn('outlets', 'closing_time')) {
                $table->time('closing_time')->default('22:00')->after('opening_time');
            }

            // Location for delivery
            if (!Schema::hasColumn('outlets', 'latitude')) {
                $table->decimal('latitude', 17, 14)->nullable()->after('closing_time');
            }
            if (!Schema::hasColumn('outlets', 'longitude')) {
                $table->decimal('longitude', 17, 14)->nullable()->after('latitude');
            }
            if (!Schema::hasColumn('outlets', 'delivery_radius')) {
                $table->integer('delivery_radius')->default(5000)->comment('Radius delivery dalam meter')->after('longitude');
            }

            // Business settings
            if (!Schema::hasColumn('outlets', 'tax_rate')) {
                $table->decimal('tax_rate', 5, 2)->default(11.00)->comment('Tax rate in percentage')->after('delivery_radius');
            }
            if (!Schema::hasColumn('outlets', 'service_charge_rate')) {
                $table->decimal('service_charge_rate', 5, 2)->default(0.00)->comment('Service charge rate in percentage')->after('tax_rate');
            }

            // Status and metadata
            if (!Schema::hasColumn('outlets', 'status')) {
                $table->enum('status', ['active', 'inactive', 'maintenance', 'closed'])->default('active')->after('service_charge_rate');
            }
        });

        // Fill empty codes for existing outlets
        $outlets = DB::table('outlets')->whereNull('code')->orWhere('code', '')->get();
        foreach ($outlets as $outlet) {
            $code = 'OUT' . str_pad($outlet->id, 3, '0', STR_PAD_LEFT);
            DB::table('outlets')->where('id', $outlet->id)->update(['code' => $code]);
        }

        // Now add unique constraint
        Schema::table('outlets', function (Blueprint $table) {
            if (Schema::hasColumn('outlets', 'code')) {
                $table->unique('code');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('outlets', function (Blueprint $table) {
            $table->dropColumn([
                'code',
                'type',
                'city',
                'phone',
                'email',
                'manager_name',
                'opening_date',
                'opening_time',
                'closing_time',
                'latitude',
                'longitude',
                'delivery_radius',
                'tax_rate',
                'service_charge_rate',
                'status',
            ]);
        });
    }
};
