<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('objectives', function (Blueprint $table) {
            $table->unsignedBigInteger('entitas_id')->nullable()->after('owner_id')
                ->comment('ID entitas/toko untuk objective ini');
            
            $table->foreign('entitas_id')->references('id')->on('entitas')->onDelete('set null');
            $table->index('entitas_id', 'idx_objectives_entitas');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('objectives', function (Blueprint $table) {
            $table->dropForeign(['entitas_id']);
            $table->dropIndex('idx_objectives_entitas');
            $table->dropColumn('entitas_id');
        });
    }
};
