<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('outlet_user_id', function (Blueprint $table) {
            $table->id();
            $table->foreignId('outlet_id')->constrained('outlets')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('role')->nullable()->comment('Role of user in this outlet (manager, cashier, etc.)');
            $table->boolean('is_active')->default(true)->comment('Is this assignment active');
            $table->date('assigned_from')->nullable()->comment('Assignment start date');
            $table->date('assigned_until')->nullable()->comment('Assignment end date');
            $table->text('notes')->nullable()->comment('Additional notes');
            $table->timestamps();

            // Indexes
            $table->index(['outlet_id', 'user_id']);
            $table->index(['outlet_id', 'is_active']);
            $table->index(['user_id', 'is_active']);

            // Unique constraint to prevent duplicate assignments
            $table->unique(['outlet_id', 'user_id'], 'unique_outlet_user');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('outlet_user_id');
    }
};
