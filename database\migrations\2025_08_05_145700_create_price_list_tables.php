<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create price_lists table
        Schema::create('price_lists', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Price list name');
            $table->string('code')->unique()->comment('Unique price list code');
            $table->text('description')->nullable()->comment('Price list description');
            $table->boolean('is_global')->default(false)->comment('Is this a global price list');
            $table->boolean('is_active')->default(true)->comment('Is this price list active');
            $table->date('effective_from')->nullable()->comment('Effective start date');
            $table->date('effective_until')->nullable()->comment('Effective end date');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['is_active']);
            $table->index(['is_global']);
            $table->index(['effective_from', 'effective_until']);
        });

        // Create price_list_items table
        Schema::create('price_list_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('price_list_id')->constrained('price_lists')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->decimal('price', 15, 2)->comment('Product price in this price list');
            $table->decimal('cost_price', 15, 2)->nullable()->comment('Product cost price');
            $table->boolean('is_active')->default(true)->comment('Is this item active');
            $table->timestamps();

            // Indexes
            $table->index(['price_list_id', 'product_id']);
            $table->index(['product_id']);
            $table->index(['is_active']);

            // Unique constraint to prevent duplicate products in same price list
            $table->unique(['price_list_id', 'product_id'], 'unique_price_list_product');
        });

        // Create outlet_price_lists pivot table
        Schema::create('outlet_price_lists', function (Blueprint $table) {
            $table->id();
            $table->foreignId('outlet_id')->constrained('outlets')->onDelete('cascade');
            $table->foreignId('price_list_id')->constrained('price_lists')->onDelete('cascade');
            $table->integer('priority')->default(1)->comment('Priority order (1 = highest)');
            $table->boolean('is_active')->default(true)->comment('Is this assignment active');
            $table->date('effective_from')->nullable()->comment('Assignment effective start date');
            $table->date('effective_until')->nullable()->comment('Assignment effective end date');
            $table->timestamps();

            // Indexes
            $table->index(['outlet_id', 'priority']);
            $table->index(['outlet_id', 'is_active']);
            $table->index(['price_list_id']);

            // Unique constraint to prevent duplicate assignments
            $table->unique(['outlet_id', 'price_list_id'], 'unique_outlet_price_list');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('outlet_price_lists');
        Schema::dropIfExists('price_list_items');
        Schema::dropIfExists('price_lists');
    }
};
