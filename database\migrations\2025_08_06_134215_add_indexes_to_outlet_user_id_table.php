<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('outlet_user_id', function (Blueprint $table) {
            // Add indexes for better query performance
            $table->index(['outlet_id', 'is_active'], 'idx_outlet_active');
            $table->index(['user_id', 'is_active'], 'idx_user_active');
            $table->index(['outlet_id', 'role'], 'idx_outlet_role');
            $table->index(['outlet_id', 'user_id'], 'idx_outlet_user');
            $table->index(['is_active', 'assigned_from', 'assigned_until'], 'idx_active_dates');
            $table->index('role', 'idx_role');
            $table->index('created_at', 'idx_created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('outlet_user_id', function (Blueprint $table) {
            $table->dropIndex('idx_outlet_active');
            $table->dropIndex('idx_user_active');
            $table->dropIndex('idx_outlet_role');
            $table->dropIndex('idx_outlet_user');
            $table->dropIndex('idx_active_dates');
            $table->dropIndex('idx_role');
            $table->dropIndex('idx_created_at');
        });
    }
};
