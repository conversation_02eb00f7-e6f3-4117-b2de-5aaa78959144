<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('potongan_karyawan', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('karyawan_id')->comment('ID karyawan');
            $table->enum('jenis_potongan', ['kasir', 'stok_opname', 'retur', 'kasbon'])->comment('Jenis potongan karyawan');
            $table->decimal('nominal', 12, 2)->default(0)->comment('Nominal potongan dalam rupiah');
            $table->string('bulan_potongan', 7)->comment('Bulan potongan dalam format YYYY-MM');
            $table->text('keterangan')->nullable()->comment('Keterangan tambahan');
            $table->unsignedBigInteger('created_by')->nullable()->comment('ID user yang membuat');
            $table->timestamps();
            $table->softDeletes();

            // Foreign Keys
            $table->foreign('karyawan_id')->references('id')->on('karyawan')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['karyawan_id', 'jenis_potongan']);
            $table->index('bulan_potongan');
            $table->index('created_by');

            // Unique constraint untuk mencegah duplikasi potongan per karyawan per jenis per bulan
            $table->unique(['karyawan_id', 'jenis_potongan', 'bulan_potongan'], 'unique_potongan_per_bulan');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('potongan_karyawan');
    }
};
