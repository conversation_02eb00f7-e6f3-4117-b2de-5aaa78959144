<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('potongan_karyawan', function (Blueprint $table) {
            // Pertama, ubah ke string yang lebih panjang untuk menampung tanggal
            $table->string('bulan_potongan', 10)->change();
        });

        // Konversi data yang sudah ada dari format YYYY-MM ke YYYY-MM-01
        DB::statement("UPDATE potongan_karyawan SET bulan_potongan = CONCAT(bulan_potongan, '-01') WHERE bulan_potongan REGEXP '^[0-9]{4}-[0-9]{2}$'");

        Schema::table('potongan_karyawan', function (Blueprint $table) {
            // Sekarang ubah ke date
            $table->date('bulan_potongan')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('potongan_karyawan', function (Blueprint $table) {
            // Kembalikan ke string format YYYY-MM
            $table->string('bulan_potongan', 7)->change();
        });
    }
};
