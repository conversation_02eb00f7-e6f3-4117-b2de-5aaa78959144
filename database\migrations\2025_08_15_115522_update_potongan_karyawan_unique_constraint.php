<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('potongan_karyawan', function (Blueprint $table) {
            // Drop unique constraint lama
            $table->dropUnique('unique_potongan_per_bulan');

            // Buat unique constraint baru dengan nama yang lebih sesuai
            $table->unique(['karyawan_id', 'jenis_potongan', 'bulan_potongan'], 'unique_potongan_per_tanggal');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('potongan_karyawan', function (Blueprint $table) {
            // Drop unique constraint baru
            $table->dropUnique('unique_potongan_per_tanggal');

            // Kembalikan unique constraint lama
            $table->unique(['karyawan_id', 'jenis_potongan', 'bulan_potongan'], 'unique_potongan_per_bulan');
        });
    }
};
