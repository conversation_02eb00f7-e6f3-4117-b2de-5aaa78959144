<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update enum untuk jenis_potongan di tabel payroll_deductions
        DB::statement("ALTER TABLE payroll_deductions MODIFY COLUMN jenis_potongan ENUM('bpjs_kesehatan', 'bpjs_tk', 'keterlambatan', 'pelanggaran', 'kasir', 'stok_opname', 'retur', 'kasbon', 'sakit_tanpa_surat', 'alpha', 'cuti_melebihi_kuota', 'lainnya') COMMENT 'Jenis potongan'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Kembalikan enum ke nilai sebelumnya
        DB::statement("ALTER TABLE payroll_deductions MODIFY COLUMN jenis_potongan ENUM('bpjs_kesehatan', 'bpjs_tk', 'keterlambatan', 'pelanggaran', 'kasir', 'stok_opname', 'retur', 'kasbon', 'lainnya') COMMENT 'Jenis potongan'");
    }
};
