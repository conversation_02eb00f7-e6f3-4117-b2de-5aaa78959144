<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('aturan_lemburs', function (Blueprint $table) {
            $table->id();
            $table->string('nama_jenis_lembur')->comment('Nama jenis lembur');
            $table->enum('tipe_perhitungan', ['per_jam', 'per_hari'])->comment('Tipe perhitungan: per jam atau per hari');
            $table->decimal('jam_mulai', 5, 2)->nullable()->comment('Jam mulai untuk range (untuk tipe per_jam)');
            $table->decimal('jam_selesai', 5, 2)->nullable()->comment('Jam selesai untuk range (untuk tipe per_jam)');
            $table->decimal('multiplier', 5, 2)->comment('Pengali upah (1.5x, 2x, 3x, 4x)');
            $table->decimal('pembagi_upah_bulanan', 5, 2)->default(30)->comment('Pembagi untuk menghitung upah harian (26 atau 30)');
            $table->text('keterangan')->nullable()->comment('Keterangan tambahan');
            $table->boolean('is_active')->default(true)->comment('Status aktif aturan');
            $table->integer('urutan')->default(0)->comment('Urutan prioritas aturan');
            $table->timestamps();

            // Indexes
            $table->index(['tipe_perhitungan', 'is_active']);
            $table->index('urutan');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('aturan_lemburs');
    }
};
