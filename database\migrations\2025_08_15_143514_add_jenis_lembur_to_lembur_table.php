<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lembur', function (Blueprint $table) {
            $table->enum('jenis_lembur', ['hari_biasa', 'hari_libur', 'hk_hari_kerja', 'hk_tanggal_merah'])
                ->default('hari_biasa')
                ->after('jumlah_jam')
                ->comment('Jenis lembur: hari_biasa, hari_libur, hk_hari_kerja, hk_tanggal_merah');

            $table->decimal('upah_lembur', 12, 2)
                ->default(0)
                ->after('jenis_lembur')
                ->comment('Upah lembur yang dihitung berdasarkan aturan');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lembur', function (Blueprint $table) {
            $table->dropColumn(['jenis_lembur', 'upah_lembur']);
        });
    }
};
