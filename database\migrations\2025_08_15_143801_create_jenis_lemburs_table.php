<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jenis_lemburs', function (Blueprint $table) {
            $table->id();
            $table->string('nama_jenis')->comment('Nama jenis lembur');
            $table->enum('tipe_perhitungan', ['per_jam', 'per_hari'])->comment('Tipe perhitungan: per jam atau per hari');
            $table->decimal('pembagi_upah_bulanan', 5, 2)->default(30)->comment('Pembagi untuk menghitung upah harian (26 atau 30)');
            $table->text('keterangan')->nullable()->comment('Keterangan jenis lembur');
            $table->boolean('is_active')->default(true)->comment('Status aktif jenis lembur');
            $table->integer('urutan')->default(0)->comment('Urutan tampilan');
            $table->timestamps();

            // Indexes
            $table->index(['is_active', 'urutan']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jenis_lemburs');
    }
};
