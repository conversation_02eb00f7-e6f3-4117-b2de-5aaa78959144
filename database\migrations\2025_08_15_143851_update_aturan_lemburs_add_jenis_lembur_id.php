<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('aturan_lemburs', function (Blueprint $table) {
            // Tambah foreign key ke jenis_lemburs
            $table->unsignedBigInteger('jenis_lembur_id')->after('id')->comment('ID jenis lembur');
            $table->foreign('jenis_lembur_id')->references('id')->on('jenis_lemburs')->onDelete('cascade');

            // Hapus kolom yang tidak diperlukan lagi
            $table->dropColumn(['nama_jenis_lembur', 'tipe_perhitungan', 'pembagi_upah_bulanan']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('aturan_lemburs', function (Blueprint $table) {
            // Kembalikan kolom yang dihapus
            $table->string('nama_jenis_lembur')->comment('Nama jenis lembur');
            $table->enum('tipe_perhitungan', ['per_jam', 'per_hari'])->comment('Tipe perhitungan: per jam atau per hari');
            $table->decimal('pembagi_upah_bulanan', 5, 2)->default(30)->comment('Pembagi untuk menghitung upah harian (26 atau 30)');

            // Hapus foreign key dan kolom
            $table->dropForeign(['jenis_lembur_id']);
            $table->dropColumn('jenis_lembur_id');
        });
    }
};
