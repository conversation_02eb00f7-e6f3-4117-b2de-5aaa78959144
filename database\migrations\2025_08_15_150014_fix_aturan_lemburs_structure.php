<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Hapus tabel aturan_lemburs dan buat ulang dengan struktur yang benar
        Schema::dropIfExists('aturan_lemburs');

        Schema::create('aturan_lemburs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('jenis_lembur_id')->comment('ID jenis lembur');
            $table->decimal('jam_mulai', 5, 2)->nullable()->comment('Jam mulai untuk range (untuk tipe per_jam)');
            $table->decimal('jam_selesai', 5, 2)->nullable()->comment('Jam selesai untuk range (untuk tipe per_jam)');
            $table->decimal('multiplier', 5, 2)->comment('Pengali upah (1.5x, 2x, 3x, 4x)');
            $table->text('keterangan')->nullable()->comment('Keterangan tambahan');
            $table->boolean('is_active')->default(true)->comment('Status aktif aturan');
            $table->integer('urutan')->default(0)->comment('Urutan prioritas aturan');
            $table->timestamps();

            // Foreign key
            $table->foreign('jenis_lembur_id')->references('id')->on('jenis_lemburs')->onDelete('cascade');

            // Indexes
            $table->index(['jenis_lembur_id', 'is_active']);
            $table->index('urutan');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('aturan_lemburs');
    }
};
