<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lembur', function (Blueprint $table) {
            $table->unsignedBigInteger('jenis_lembur_id')
                ->nullable()
                ->after('jenis_lembur')
                ->comment('ID jenis lembur dari tabel jenis_lemburs');

            $table->foreign('jenis_lembur_id')
                ->references('id')
                ->on('jenis_lemburs')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lembur', function (Blueprint $table) {
            $table->dropForeign(['jenis_lembur_id']);
            $table->dropColumn('jenis_lembur_id');
        });
    }
};
