<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kekurangan_gajis', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('karyawan_id')->comment('ID karyawan');
            $table->date('periode_kekurangan')->comment('Periode bulan kekurangan gaji (YYYY-MM-01)');
            $table->decimal('nominal_kekurangan', 12, 2)->comment('Nominal kekurangan gaji');
            $table->text('keterangan')->nullable()->comment('Keterangan kekurangan gaji');
            $table->enum('status', ['pending', 'approved', 'paid'])->default('pending')->comment('Status kekurangan gaji');
            $table->date('tanggal_pembayaran')->nullable()->comment('Tanggal pembayaran kekurangan');
            $table->unsignedBigInteger('created_by')->nullable()->comment('User yang membuat');
            $table->unsignedBigInteger('approved_by')->nullable()->comment('User yang approve');
            $table->timestamp('approved_at')->nullable()->comment('Tanggal approve');
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('karyawan_id')->references('id')->on('karyawan')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['karyawan_id', 'periode_kekurangan']);
            $table->index(['status', 'tanggal_pembayaran']);
            $table->unique(['karyawan_id', 'periode_kekurangan'], 'unique_karyawan_periode');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kekurangan_gajis');
    }
};
