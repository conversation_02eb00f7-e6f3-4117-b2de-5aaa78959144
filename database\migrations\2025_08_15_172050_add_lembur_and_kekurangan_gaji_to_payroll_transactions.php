<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payroll_transactions', function (Blueprint $table) {
            // Komponen Lembur
            $table->decimal('lembur_biasa', 12, 2)->default(0)->after('tunjangan_sembako')->comment('Lembur hari biasa');
            $table->decimal('lembur_tanggal_merah', 12, 2)->default(0)->after('lembur_biasa')->comment('Lembur tanggal merah');
            $table->decimal('lembur_tambah_hk', 12, 2)->default(0)->after('lembur_tanggal_merah')->comment('Lembur tambah HK');

            // Komponen Penerimaan Lainnya
            $table->decimal('kekurangan_gaji_bulan_sebelum', 12, 2)->default(0)->after('lembur_tambah_hk')->comment('Kekurangan gaji bulan sebelumnya');
            $table->decimal('claim_sakit_dengan_surat', 12, 2)->default(0)->after('kekurangan_gaji_bulan_sebelum')->comment('Claim sakit dengan surat');
            $table->decimal('pesangon', 12, 2)->default(0)->after('claim_sakit_dengan_surat')->comment('Pesangon');
            $table->decimal('insentif', 12, 2)->default(0)->after('pesangon')->comment('Insentif');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payroll_transactions', function (Blueprint $table) {
            $table->dropColumn([
                'lembur_biasa',
                'lembur_tanggal_merah',
                'lembur_tambah_hk',
                'kekurangan_gaji_bulan_sebelum',
                'claim_sakit_dengan_surat',
                'pesangon',
                'insentif'
            ]);
        });
    }
};
