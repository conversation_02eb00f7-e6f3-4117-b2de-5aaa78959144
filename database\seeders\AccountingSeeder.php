<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Akun;
use App\Models\ProdukKategori;
use App\Models\Satuan;
use App\Models\Produk;
use App\Models\Inventory;

class AccountingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed Chart of Accounts (COA)
        $accounts = [
            // ASET
            ['kode_akun' => '1001', 'nama_akun' => 'Kas', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => ********],
            ['kode_akun' => '1002', 'nama_akun' => 'Bank BCA', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => ********],
            ['kode_akun' => '1003', 'nama_akun' => 'Bank Mandiri', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => ********],
            ['kode_akun' => '1101', 'nama_akun' => 'Piutang Usaha', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '1201', 'nama_akun' => 'Persediaan Barang Dagang', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => ********],
            ['kode_akun' => '1301', 'nama_akun' => 'Peralatan Kantor', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 5000000],
            ['kode_akun' => '1302', 'nama_akun' => 'Kendaraan', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => ********],

            // KEWAJIBAN
            ['kode_akun' => '2001', 'nama_akun' => 'Utang Usaha', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '2002', 'nama_akun' => 'Utang Bank', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '2101', 'nama_akun' => 'Utang PPN', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '2102', 'nama_akun' => 'Utang Gaji', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],

            // EKUITAS
            ['kode_akun' => '3001', 'nama_akun' => 'Modal Pemilik', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => ********],
            ['kode_akun' => '3002', 'nama_akun' => 'Laba Ditahan', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => 5000000],

            // PENDAPATAN
            ['kode_akun' => '4001', 'nama_akun' => 'Pendapatan Penjualan', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '4002', 'nama_akun' => 'Pendapatan Jasa', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '4003', 'nama_akun' => 'Pendapatan Lain-lain', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],

            // BEBAN
            ['kode_akun' => '5001', 'nama_akun' => 'Harga Pokok Penjualan', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '5101', 'nama_akun' => 'Beban Gaji', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '5102', 'nama_akun' => 'Beban Listrik', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '5103', 'nama_akun' => 'Beban Telepon', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '5104', 'nama_akun' => 'Beban Sewa', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '5105', 'nama_akun' => 'Beban Transportasi', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '5106', 'nama_akun' => 'Beban Administrasi', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
        ];

        foreach ($accounts as $account) {
            Akun::create($account);
        }

        // Seed Product Categories
        $categories = [
            ['nama' => 'Elektronik', 'deskripsi' => 'Produk elektronik dan gadget', 'created_by' => 1],
            ['nama' => 'Fashion', 'deskripsi' => 'Pakaian dan aksesoris', 'created_by' => 1],
            ['nama' => 'Makanan & Minuman', 'deskripsi' => 'Produk makanan dan minuman', 'created_by' => 1],
            ['nama' => 'Kesehatan', 'deskripsi' => 'Produk kesehatan dan kecantikan', 'created_by' => 1],
            ['nama' => 'Rumah Tangga', 'deskripsi' => 'Peralatan rumah tangga', 'created_by' => 1],
        ];

        foreach ($categories as $category) {
            ProdukKategori::create($category);
        }

        // Seed Units
        $units = [
            ['nama_satuan' => 'Pcs', 'deskripsi_satuan' => 'Pieces/Unit', 'created_by' => 1],
            ['nama_satuan' => 'Kg', 'deskripsi_satuan' => 'Kilogram', 'created_by' => 1],
            ['nama_satuan' => 'Liter', 'deskripsi_satuan' => 'Liter', 'created_by' => 1],
            ['nama_satuan' => 'Box', 'deskripsi_satuan' => 'Box/Kotak', 'created_by' => 1],
            ['nama_satuan' => 'Pack', 'deskripsi_satuan' => 'Pack/Kemasan', 'created_by' => 1],
        ];

        foreach ($units as $unit) {
            Satuan::create($unit);
        }

        // Seed Sample Products
        $products = [
            [
                'kode' => 'ELK001',
                'nama' => 'Smartphone Samsung Galaxy A54',
                'deskripsi' => 'Smartphone Android dengan kamera 50MP',
                'unit_cost' => 3500000,
                'selling_price' => 4200000,
                'id_produk_kategori' => 1, // Elektronik
                'id_satuan' => 1, // Pcs
                'created_by' => 1
            ],
            [
                'kode' => 'ELK002',
                'nama' => 'Laptop ASUS VivoBook',
                'deskripsi' => 'Laptop untuk kebutuhan sehari-hari',
                'unit_cost' => 6500000,
                'selling_price' => 7800000,
                'id_produk_kategori' => 1, // Elektronik
                'id_satuan' => 1, // Pcs
                'created_by' => 1
            ],
            [
                'kode' => 'FSH001',
                'nama' => 'Kaos Polo Pria',
                'deskripsi' => 'Kaos polo cotton combed 30s',
                'unit_cost' => 75000,
                'selling_price' => 120000,
                'id_produk_kategori' => 2, // Fashion
                'id_satuan' => 1, // Pcs
                'created_by' => 1
            ],
            [
                'kode' => 'FNB001',
                'nama' => 'Kopi Arabica Premium',
                'deskripsi' => 'Kopi arabica single origin',
                'unit_cost' => 85000,
                'selling_price' => 125000,
                'id_produk_kategori' => 3, // Makanan & Minuman
                'id_satuan' => 5, // Pack
                'created_by' => 1
            ],
            [
                'kode' => 'HSE001',
                'nama' => 'Vitamin C 1000mg',
                'deskripsi' => 'Suplemen vitamin C untuk daya tahan tubuh',
                'unit_cost' => 45000,
                'selling_price' => 65000,
                'id_produk_kategori' => 4, // Kesehatan
                'id_satuan' => 4, // Box
                'created_by' => 1
            ]
        ];

        foreach ($products as $product) {
            $createdProduct = Produk::create($product);

            // Create inventory record for each product
            Inventory::create([
                'product_id' => $createdProduct->id,
                'quantity' => rand(10, 100), // Random stock between 10-100
                'unit_cost' => $product['unit_cost'],
                'created_by' => 1
            ]);
        }
    }
}
