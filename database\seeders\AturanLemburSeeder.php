<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\AturanLembur;

class AturanLemburSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Hapus data lama jika ada
        AturanLembur::truncate();

        // Aturan Lembur Hari Biasa (per jam)
        $aturanHariBiasa = [
            [
                'nama_jenis_lembur' => 'Lembur Hari Biasa - Jam 1',
                'tipe_perhitungan' => 'per_jam',
                'jam_mulai' => 1.00,
                'jam_selesai' => 1.00,
                'multiplier' => 1.50,
                'pembagi_upah_bulanan' => 30,
                'keterangan' => 'Lembur jam pertama di hari biasa dengan multiplier 1.5x',
                'is_active' => true,
                'urutan' => 1,
            ],
            [
                'nama_jenis_lembur' => 'Lembur Hari Biasa - Jam 2+',
                'tipe_perhitungan' => 'per_jam',
                'jam_mulai' => 2.00,
                'jam_selesai' => null, // Jam 2 dan seterusnya
                'multiplier' => 2.00,
                'pembagi_upah_bulanan' => 30,
                'keterangan' => 'Lembur jam kedua dan seterusnya di hari biasa dengan multiplier 2x',
                'is_active' => true,
                'urutan' => 2,
            ],
        ];

        // Aturan Lembur Hari Libur (per jam)
        $aturanHariLibur = [
            [
                'nama_jenis_lembur' => 'Lembur Hari Libur - Jam 1-8',
                'tipe_perhitungan' => 'per_jam',
                'jam_mulai' => 1.00,
                'jam_selesai' => 8.00,
                'multiplier' => 2.00,
                'pembagi_upah_bulanan' => 30,
                'keterangan' => 'Lembur jam 1-8 di hari libur dengan multiplier 2x',
                'is_active' => true,
                'urutan' => 3,
            ],
            [
                'nama_jenis_lembur' => 'Lembur Hari Libur - Jam 9',
                'tipe_perhitungan' => 'per_jam',
                'jam_mulai' => 9.00,
                'jam_selesai' => 9.00,
                'multiplier' => 3.00,
                'pembagi_upah_bulanan' => 30,
                'keterangan' => 'Lembur jam ke-9 di hari libur dengan multiplier 3x',
                'is_active' => true,
                'urutan' => 4,
            ],
            [
                'nama_jenis_lembur' => 'Lembur Hari Libur - Jam 10-11',
                'tipe_perhitungan' => 'per_jam',
                'jam_mulai' => 10.00,
                'jam_selesai' => 11.00,
                'multiplier' => 4.00,
                'pembagi_upah_bulanan' => 30,
                'keterangan' => 'Lembur jam 10-11 di hari libur dengan multiplier 4x',
                'is_active' => true,
                'urutan' => 5,
            ],
        ];

        // Aturan Lembur HK (per hari)
        $aturanHK = [
            [
                'nama_jenis_lembur' => 'Lembur HK (Hari Kerja)',
                'tipe_perhitungan' => 'per_hari',
                'jam_mulai' => null,
                'jam_selesai' => null,
                'multiplier' => 1.00,
                'pembagi_upah_bulanan' => 26,
                'keterangan' => 'Lembur HK di hari kerja dengan multiplier 1x upah harian (upah bulanan / 26)',
                'is_active' => true,
                'urutan' => 6,
            ],
            [
                'nama_jenis_lembur' => 'Lembur HK (Tanggal Merah)',
                'tipe_perhitungan' => 'per_hari',
                'jam_mulai' => null,
                'jam_selesai' => null,
                'multiplier' => 2.00,
                'pembagi_upah_bulanan' => 26,
                'keterangan' => 'Lembur HK di tanggal merah dengan multiplier 2x upah harian (upah bulanan / 26)',
                'is_active' => true,
                'urutan' => 7,
            ],
        ];

        // Insert semua data
        $allRules = array_merge($aturanHariBiasa, $aturanHariLibur, $aturanHK);

        foreach ($allRules as $rule) {
            AturanLembur::create($rule);
        }

        $this->command->info('Seeder AturanLembur berhasil dijalankan!');
        $this->command->info('Total aturan yang dibuat: ' . count($allRules));
    }
}
