<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Outlet;

class BasicOutletSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $outlets = [
            [
                'name' => 'Viera Oleh-oleh Cabang Sudirman',
                'category' => 'retail',
                'address' => 'Jalan Sudirman',
                'description' => 'Cabang Sudirman',
                'is_active' => true,
            ],
            [
                'name' => 'VOO 1',
                'category' => 'retail',
                'address' => 'Jalan VOO 1',
                'description' => 'VOO Outlet 1',
                'is_active' => true,
            ],
            [
                'name' => 'VOO 2',
                'category' => 'retail',
                'address' => 'Jalan VOO 2',
                'description' => 'VOO Outlet 2',
                'is_active' => true,
            ],
        ];

        foreach ($outlets as $outletData) {
            Outlet::firstOrCreate(
                ['name' => $outletData['name']],
                $outletData
            );
        }

        $this->command->info('Basic outlets created successfully!');
    }
}
