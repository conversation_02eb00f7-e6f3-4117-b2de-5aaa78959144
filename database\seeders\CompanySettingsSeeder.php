<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CompanySettings;

class CompanySettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'work_period_start_date',
                'value' => 21,
                'type' => 'integer',
                'description' => 'Tanggal mulai periode kerja setiap bulan (1-31)',
                'is_active' => true,
            ],
            [
                'key' => 'work_period_end_date',
                'value' => 20,
                'type' => 'integer',
                'description' => 'Tanggal akhir periode kerja setiap bulan (1-31)',
                'is_active' => true,
            ],
            [
                'key' => 'company_name',
                'value' => 'PT. Viera Technology',
                'type' => 'string',
                'description' => 'Nama perusahaan',
                'is_active' => true,
            ],
            [
                'key' => 'company_address',
                'value' => 'Jakarta, Indonesia',
                'type' => 'string',
                'description' => '<PERSON><PERSON><PERSON> perusahaan',
                'is_active' => true,
            ],
            [
                'key' => 'working_days_per_week',
                'value' => 5,
                'type' => 'integer',
                'description' => 'Jumlah hari kerja per minggu',
                'is_active' => true,
            ],
            [
                'key' => 'working_hours_per_day',
                'value' => 8,
                'type' => 'integer',
                'description' => 'Jumlah jam kerja per hari',
                'is_active' => true,
            ],
            [
                'key' => 'overtime_multiplier',
                'value' => 1.5,
                'type' => 'float',
                'description' => 'Multiplier untuk perhitungan lembur',
                'is_active' => true,
            ],
            [
                'key' => 'dashboard_auto_refresh',
                'value' => true,
                'type' => 'boolean',
                'description' => 'Auto refresh dashboard setiap 30 detik',
                'is_active' => true,
            ],
            [
                'key' => 'dashboard_refresh_interval',
                'value' => 30,
                'type' => 'integer',
                'description' => 'Interval refresh dashboard dalam detik',
                'is_active' => true,
            ],
        ];

        foreach ($settings as $setting) {
            CompanySettings::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
