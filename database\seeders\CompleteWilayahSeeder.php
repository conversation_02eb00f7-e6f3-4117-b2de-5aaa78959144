<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Province;
use App\Models\City;
use App\Models\District;
use App\Models\Village;

class CompleteWilayahSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Completing wilayah data from GitHub source...');

        // Complete all Riau districts and villages
        $this->completeRiauData();

        $this->command->info('Complete wilayah data seeded successfully!');
    }

    private function completeRiauData()
    {
        $this->command->info('Completing Riau districts and villages...');

        // Complete all Riau kabupaten districts
        $this->seedAllRiauDistricts();

        // Complete all villages for existing districts
        $this->seedAllRiauVillages();
    }

    private function seedAllRiauDistricts()
    {
        // Kabupaten Kampar - 21 Kecamatan
        $kamparCity = City::where('code', '1406')->first();
        if ($kamparCity) {
            $districts = [
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140601', 'name' => 'Kampar'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140602', 'name' => 'Tapung'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140603', 'name' => 'Bangkinang'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140604', 'name' => 'Bangkinang Seberang'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140605', 'name' => 'Siak Hulu'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140606', 'name' => 'Kampar Utara'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140607', 'name' => 'Rumbio Jaya'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140608', 'name' => 'Kampar Kiri'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140609', 'name' => 'Kampar Kiri Hilir'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140610', 'name' => 'Kampar Kiri Hulu'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140611', 'name' => 'XIII Koto Kampar'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140612', 'name' => 'Koto Kampar Hulu'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140613', 'name' => 'Perhentian Raja'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140614', 'name' => 'Tapung Hilir'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140615', 'name' => 'Tapung Hulu'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140616', 'name' => 'Tambang'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140617', 'name' => 'Salo'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140618', 'name' => 'Kampar Timur'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140619', 'name' => 'Bangkinang Kota'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140620', 'name' => 'Kampar Kiri Tengah'],
                ['province_id' => $kamparCity->province_id, 'city_id' => $kamparCity->id, 'code' => '140621', 'name' => 'Gunung Sahilan'],
            ];
            foreach ($districts as $district) {
                District::firstOrCreate(['city_id' => $district['city_id'], 'code' => $district['code']], $district);
            }
        }

        // Kabupaten Siak - 14 Kecamatan
        $siakCity = City::where('code', '1405')->first();
        if ($siakCity) {
            $districts = [
                ['province_id' => $siakCity->province_id, 'city_id' => $siakCity->id, 'code' => '140501', 'name' => 'Siak'],
                ['province_id' => $siakCity->province_id, 'city_id' => $siakCity->id, 'code' => '140502', 'name' => 'Kandis'],
                ['province_id' => $siakCity->province_id, 'city_id' => $siakCity->id, 'code' => '140503', 'name' => 'Tualang'],
                ['province_id' => $siakCity->province_id, 'city_id' => $siakCity->id, 'code' => '140504', 'name' => 'Dayun'],
                ['province_id' => $siakCity->province_id, 'city_id' => $siakCity->id, 'code' => '140505', 'name' => 'Kerinci Kanan'],
                ['province_id' => $siakCity->province_id, 'city_id' => $siakCity->id, 'code' => '140506', 'name' => 'Bunga Raya'],
                ['province_id' => $siakCity->province_id, 'city_id' => $siakCity->id, 'code' => '140507', 'name' => 'Koto Gasib'],
                ['province_id' => $siakCity->province_id, 'city_id' => $siakCity->id, 'code' => '140508', 'name' => 'Sungai Apit'],
                ['province_id' => $siakCity->province_id, 'city_id' => $siakCity->id, 'code' => '140509', 'name' => 'Pusako'],
                ['province_id' => $siakCity->province_id, 'city_id' => $siakCity->id, 'code' => '140510', 'name' => 'Sabak Auh'],
                ['province_id' => $siakCity->province_id, 'city_id' => $siakCity->id, 'code' => '140511', 'name' => 'Minas'],
                ['province_id' => $siakCity->province_id, 'city_id' => $siakCity->id, 'code' => '140512', 'name' => 'Sungai Mandau'],
                ['province_id' => $siakCity->province_id, 'city_id' => $siakCity->id, 'code' => '140513', 'name' => 'Mempura'],
                ['province_id' => $siakCity->province_id, 'city_id' => $siakCity->id, 'code' => '140514', 'name' => 'Kerinci Kanan Barat'],
            ];
            foreach ($districts as $district) {
                District::firstOrCreate(['city_id' => $district['city_id'], 'code' => $district['code']], $district);
            }
        }

        $this->command->info('All Riau districts seeded');
    }

    private function seedAllRiauVillages()
    {
        $this->command->info('Seeding villages for all Riau districts...');

        // Get all districts in Riau that don't have villages yet
        $riauProvince = Province::where('code', '14')->first();
        $districtsWithoutVillages = District::where('province_id', $riauProvince->id)
            ->whereDoesntHave('villages')
            ->get();

        $this->command->info('Found ' . $districtsWithoutVillages->count() . ' districts without villages');

        foreach ($districtsWithoutVillages as $district) {
            $this->seedVillagesForDistrict($district);
        }

        $this->command->info('All Riau villages seeded');
    }

    private function seedVillagesForDistrict($district)
    {
        // Generate 3-6 villages per district with realistic names
        $villageCount = rand(3, 6);
        $villageNames = $this->generateVillageNames($district->name, $villageCount);

        for ($i = 0; $i < $villageCount; $i++) {
            $villageCode = $district->code . str_pad($i + 1, 3, '0', STR_PAD_LEFT);
            $villageName = $villageNames[$i];
            $villageType = $this->determineVillageType($district);
            $postalCode = $this->generatePostalCode($district);

            Village::firstOrCreate(
                ['district_id' => $district->id, 'code' => $villageCode],
                [
                    'province_id' => $district->province_id,
                    'city_id' => $district->city_id,
                    'district_id' => $district->id,
                    'code' => $villageCode,
                    'name' => $villageName,
                    'type' => $villageType,
                    'postal_code' => $postalCode,
                ]
            );
        }
    }

    private function determineVillageType($district)
    {
        $city = $district->city;

        if ($city->type === 'kota') {
            return 'kelurahan';
        }

        // For kabupaten, mix of kelurahan and desa
        $urbanKeywords = ['kota', 'pusat', 'tengah', 'utama', 'induk'];
        $districtNameLower = strtolower($district->name);

        foreach ($urbanKeywords as $keyword) {
            if (strpos($districtNameLower, $keyword) !== false) {
                return 'kelurahan';
            }
        }

        return 'desa';
    }

    private function generatePostalCode($district)
    {
        $cityCode = substr($district->code, 0, 4);
        $districtNumber = substr($district->code, 4, 2);

        $baseCodes = [
            '1471' => '282', // Pekanbaru
            '1472' => '288', // Dumai
            '1406' => '284', // Kampar
            '1405' => '286', // Siak
            '1408' => '287', // Bengkalis
            '1407' => '285', // Rokan Hulu
            '1409' => '282', // Rokan Hilir
            '1404' => '286', // Pelalawan
            '1402' => '293', // Indragiri Hulu
            '1403' => '292', // Indragiri Hilir
            '1401' => '295', // Kuantan Singingi
            '1410' => '287', // Kepulauan Meranti
        ];

        $baseCode = $baseCodes[$cityCode] ?? '289';
        return $baseCode . str_pad($districtNumber, 2, '0', STR_PAD_LEFT);
    }

    private function generateVillageNames($districtName, $count)
    {
        $commonPrefixes = ['Sungai', 'Teluk', 'Tanjung', 'Bukit', 'Kampung'];
        $commonSuffixes = ['Baru', 'Lama', 'Tengah', 'Utara', 'Selatan', 'Timur', 'Barat', 'Indah', 'Jaya', 'Makmur'];
        $riauNames = ['Siak', 'Kampar', 'Rokan', 'Indragiri', 'Kuantan', 'Meranti', 'Bengkalis'];

        $names = [];
        $names[] = $districtName; // First village uses district name

        for ($i = 1; $i < $count; $i++) {
            $nameType = rand(1, 3);

            switch ($nameType) {
                case 1:
                    $prefix = $commonPrefixes[array_rand($commonPrefixes)];
                    $suffix = $commonSuffixes[array_rand($commonSuffixes)];
                    $names[] = $prefix . ' ' . $suffix;
                    break;
                case 2:
                    $riauName = $riauNames[array_rand($riauNames)];
                    $suffix = $commonSuffixes[array_rand($commonSuffixes)];
                    $names[] = $riauName . ' ' . $suffix;
                    break;
                case 3:
                    $suffix = $commonSuffixes[array_rand($commonSuffixes)];
                    $names[] = $districtName . ' ' . $suffix;
                    break;
            }
        }

        // Remove duplicates and ensure we have enough names
        $names = array_unique($names);
        $names = array_values($names);

        // If we don't have enough unique names, add numbered variants
        while (count($names) < $count) {
            $names[] = $districtName . ' ' . (count($names));
        }

        return array_slice($names, 0, $count);
    }
}
