<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Akun;
use App\Models\Journal;
use App\Models\JournalEntry;
use Carbon\Carbon;

class ComprehensiveAccountingSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $this->command->info('Creating comprehensive accounting transactions...');

        // Clear existing journals and entries (respecting foreign key constraints)
        JournalEntry::query()->delete();
        Journal::query()->delete();

        // Update opening balances for key accounts
        $this->updateOpeningBalances();

        // Create various types of transactions
        $this->createSalesTransactions();
        $this->createPurchaseTransactions();
        $this->createExpenseTransactions();
        $this->createAssetTransactions();
        $this->createLiabilityTransactions();

        $this->command->info('Comprehensive accounting transactions created successfully!');
    }

    /**
     * Update opening balances for accounts
     */
    private function updateOpeningBalances(): void
    {
        $openingBalances = [
            '1001' => ********,    // Kas - Rp 50,000,000
            '1002' => ********,    // Bank - Rp 25,000,000
            '1101' => ********,    // Piutang Usaha - Rp 15,000,000
            '1201' => ********,    // Persediaan Barang Dagang - Rp 30,000,000
            '1301' => ********,    // Peralatan - Rp 75,000,000 (max decimal(10,2) = 99,999,999.99)
            '1302' => ********,    // Bangunan - Rp 85,000,000
            '2001' => ********,    // Hutang Usaha - Rp 10,000,000
            '2002' => 5000000,     // Hutang Bank - Rp 5,000,000
            '3001' => ********,    // Modal Saham - Rp 85,000,000
        ];

        foreach ($openingBalances as $kodeAkun => $saldo) {
            $akun = Akun::where('kode_akun', $kodeAkun)->first();
            if ($akun) {
                $akun->update(['saldo_awal' => $saldo]);
                $this->command->info("Updated opening balance for {$akun->nama_akun}: Rp " . number_format($saldo));
            }
        }
    }

    /**
     * Create sales transactions
     */
    private function createSalesTransactions(): void
    {
        $salesData = [
            [
                'date' => Carbon::now()->startOfMonth()->addDays(2),
                'description' => 'Penjualan Tunai - Toko ABC',
                'amount' => ********,
                'hpp' => 9000000,
            ],
            [
                'date' => Carbon::now()->startOfMonth()->addDays(5),
                'description' => 'Penjualan Kredit - PT. XYZ',
                'amount' => ********,
                'hpp' => ********,
                'is_credit' => true,
            ],
            [
                'date' => Carbon::now()->startOfMonth()->addDays(10),
                'description' => 'Penjualan Tunai - Customer Walk-in',
                'amount' => 8500000,
                'hpp' => 5100000,
            ],
            [
                'date' => Carbon::now()->startOfMonth()->addDays(15),
                'description' => 'Penjualan Kredit - CV. Maju Jaya',
                'amount' => 18000000,
                'hpp' => 10800000,
                'is_credit' => true,
            ],
        ];

        foreach ($salesData as $index => $sale) {
            $journal = Journal::create([
                'journal_number' => 'JRN-SALES-' . date('YmdHis') . '-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT),
                'transaction_date' => $sale['date'],
                'description' => $sale['description'],
                'status' => 'Posted',
                'posting_rule_id' => null,
            ]);

            // Debit: Kas atau Piutang
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => Akun::where('kode_akun', isset($sale['is_credit']) ? '1101' : '1001')->first()->id,
                'description' => $sale['description'],
                'debit' => $sale['amount'],
                'credit' => 0,
                'sort_order' => 1,
            ]);

            // Credit: Pendapatan Penjualan
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => Akun::where('kode_akun', '4001')->first()->id,
                'description' => $sale['description'],
                'debit' => 0,
                'credit' => $sale['amount'],
                'sort_order' => 2,
            ]);

            // Debit: HPP
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => Akun::where('kode_akun', '5001')->first()->id,
                'description' => 'HPP - ' . $sale['description'],
                'debit' => $sale['hpp'],
                'credit' => 0,
                'sort_order' => 3,
            ]);

            // Credit: Persediaan
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => Akun::where('kode_akun', '1201')->first()->id,
                'description' => 'Pengurangan Persediaan - ' . $sale['description'],
                'debit' => 0,
                'credit' => $sale['hpp'],
                'sort_order' => 4,
            ]);

            $this->command->info("Created sales transaction: {$sale['description']} - Rp " . number_format($sale['amount']));
        }
    }

    /**
     * Create purchase transactions
     */
    private function createPurchaseTransactions(): void
    {
        $purchaseData = [
            [
                'date' => Carbon::now()->startOfMonth()->addDays(3),
                'description' => 'Pembelian Barang Dagang - Supplier A',
                'amount' => ********,
                'is_credit' => true,
            ],
            [
                'date' => Carbon::now()->startOfMonth()->addDays(8),
                'description' => 'Pembelian Perlengkapan Kantor',
                'amount' => 3500000,
            ],
            [
                'date' => Carbon::now()->startOfMonth()->addDays(12),
                'description' => 'Pembelian Barang Dagang - Supplier B',
                'amount' => ********,
            ],
        ];

        foreach ($purchaseData as $index => $purchase) {
            $journal = Journal::create([
                'journal_number' => 'JRN-PURCHASE-' . date('YmdHis') . '-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT),
                'transaction_date' => $purchase['date'],
                'description' => $purchase['description'],
                'status' => 'Posted',
                'posting_rule_id' => null,
            ]);

            // Debit: Persediaan atau Peralatan Kantor (untuk perlengkapan)
            $accountCode = str_contains($purchase['description'], 'Perlengkapan') ? '1301' : '1201';
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => Akun::where('kode_akun', $accountCode)->first()->id,
                'description' => $purchase['description'],
                'debit' => $purchase['amount'],
                'credit' => 0,
                'sort_order' => 1,
            ]);

            // Credit: Kas atau Hutang
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => Akun::where('kode_akun', isset($purchase['is_credit']) ? '2001' : '1001')->first()->id,
                'description' => $purchase['description'],
                'debit' => 0,
                'credit' => $purchase['amount'],
                'sort_order' => 2,
            ]);

            $this->command->info("Created purchase transaction: {$purchase['description']} - Rp " . number_format($purchase['amount']));
        }
    }

    /**
     * Create expense transactions
     */
    private function createExpenseTransactions(): void
    {
        $expenseData = [
            [
                'date' => Carbon::now()->startOfMonth()->addDays(1),
                'description' => 'Pembayaran Gaji Karyawan',
                'amount' => ********,
                'account_code' => '5101', // Beban Gaji
            ],
            [
                'date' => Carbon::now()->startOfMonth()->addDays(4),
                'description' => 'Pembayaran Listrik dan Air',
                'amount' => 2500000,
                'account_code' => '5102', // Beban Listrik
            ],
            [
                'date' => Carbon::now()->startOfMonth()->addDays(7),
                'description' => 'Biaya Transportasi',
                'amount' => 1500000,
                'account_code' => '5105', // Beban Transportasi
            ],
            [
                'date' => Carbon::now()->startOfMonth()->addDays(14),
                'description' => 'Biaya Iklan dan Promosi',
                'amount' => 5000000,
                'account_code' => '5106', // Beban Administrasi (untuk iklan)
            ],
            [
                'date' => Carbon::now()->startOfMonth()->addDays(18),
                'description' => 'Biaya Sewa Kantor',
                'amount' => 8000000,
                'account_code' => '5104', // Beban Sewa
            ],
        ];

        foreach ($expenseData as $index => $expense) {
            $journal = Journal::create([
                'journal_number' => 'JRN-EXPENSE-' . date('YmdHis') . '-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT),
                'transaction_date' => $expense['date'],
                'description' => $expense['description'],
                'status' => 'Posted',
                'posting_rule_id' => null,
            ]);

            // Debit: Beban
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => Akun::where('kode_akun', $expense['account_code'])->first()->id,
                'description' => $expense['description'],
                'debit' => $expense['amount'],
                'credit' => 0,
                'sort_order' => 1,
            ]);

            // Credit: Kas
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => Akun::where('kode_akun', '1001')->first()->id,
                'description' => $expense['description'],
                'debit' => 0,
                'credit' => $expense['amount'],
                'sort_order' => 2,
            ]);

            $this->command->info("Created expense transaction: {$expense['description']} - Rp " . number_format($expense['amount']));
        }
    }

    /**
     * Create asset transactions
     */
    private function createAssetTransactions(): void
    {
        $assetData = [
            [
                'date' => Carbon::now()->startOfMonth()->addDays(6),
                'description' => 'Pembelian Komputer Baru',
                'amount' => ********,
                'account_code' => '1301', // Peralatan
            ],
            [
                'date' => Carbon::now()->startOfMonth()->addDays(11),
                'description' => 'Pembelian Kendaraan Operasional',
                'amount' => ********, // Reduced to fit decimal(10,2) constraint
                'account_code' => '1302', // Kendaraan
                'is_credit' => true, // Dibeli dengan kredit
            ],
        ];

        foreach ($assetData as $index => $asset) {
            $journal = Journal::create([
                'journal_number' => 'JRN-ASSET-' . date('YmdHis') . '-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT),
                'transaction_date' => $asset['date'],
                'description' => $asset['description'],
                'status' => 'Posted',
                'posting_rule_id' => null,
            ]);

            // Debit: Aset
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => Akun::where('kode_akun', $asset['account_code'])->first()->id,
                'description' => $asset['description'],
                'debit' => $asset['amount'],
                'credit' => 0,
                'sort_order' => 1,
            ]);

            // Credit: Kas atau Hutang
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => Akun::where('kode_akun', isset($asset['is_credit']) ? '2002' : '1001')->first()->id,
                'description' => $asset['description'],
                'debit' => 0,
                'credit' => $asset['amount'],
                'sort_order' => 2,
            ]);

            $this->command->info("Created asset transaction: {$asset['description']} - Rp " . number_format($asset['amount']));
        }
    }

    /**
     * Create liability transactions (payments)
     */
    private function createLiabilityTransactions(): void
    {
        $liabilityData = [
            [
                'date' => Carbon::now()->startOfMonth()->addDays(9),
                'description' => 'Pembayaran Hutang Supplier',
                'amount' => 8000000,
                'account_code' => '2001', // Hutang Usaha
            ],
            [
                'date' => Carbon::now()->startOfMonth()->addDays(16),
                'description' => 'Angsuran Pinjaman Bank',
                'amount' => 5000000,
                'account_code' => '2002', // Hutang Bank
            ],
        ];

        foreach ($liabilityData as $index => $liability) {
            $journal = Journal::create([
                'journal_number' => 'JRN-LIABILITY-' . date('YmdHis') . '-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT),
                'transaction_date' => $liability['date'],
                'description' => $liability['description'],
                'status' => 'Posted',
                'posting_rule_id' => null,
            ]);

            // Debit: Hutang (mengurangi hutang)
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => Akun::where('kode_akun', $liability['account_code'])->first()->id,
                'description' => $liability['description'],
                'debit' => $liability['amount'],
                'credit' => 0,
                'sort_order' => 1,
            ]);

            // Credit: Kas
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => Akun::where('kode_akun', '1001')->first()->id,
                'description' => $liability['description'],
                'debit' => 0,
                'credit' => $liability['amount'],
                'sort_order' => 2,
            ]);

            $this->command->info("Created liability payment: {$liability['description']} - Rp " . number_format($liability['amount']));
        }
    }
}
