<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Akun;
use Carbon\Carbon;

class ComprehensiveJournalSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating comprehensive journal entries...');

        // Get accounts for seeding
        $accounts = [
            'kas' => Akun::where('kode_akun', '1001')->first(),
            'bank' => Akun::where('kode_akun', '1002')->first(),
            'piutang' => Akun::where('kode_akun', '1101')->first(),
            'persediaan' => Akun::where('kode_akun', '1201')->first(),
            'peralatan' => Akun::where('kode_akun', '1301')->first(),
            'kendaraan' => Akun::where('kode_akun', '1302')->first(),
            'utang_usaha' => Akun::where('kode_akun', '2001')->first(),
            'utang_gaji' => Akun::where('kode_akun', '2102')->first(),
            'utang_pajak' => Akun::where('kode_akun', '2003')->first(),
            'modal' => Akun::where('kode_akun', '3001')->first(),
            'pendapatan' => Akun::where('kode_akun', '4001')->first(),
            'hpp' => Akun::where('kode_akun', '5001')->first(),
            'beban_gaji' => Akun::where('kode_akun', '5101')->first(),
            'beban_operasional' => Akun::where('kode_akun', '5108')->first(),
        ];

        // Filter out null accounts
        $accounts = array_filter($accounts);

        if (empty($accounts)) {
            $this->command->error('No accounts found! Please run AkunSeeder first.');
            return;
        }

        $this->command->info('Found ' . count($accounts) . ' accounts for seeding.');

        // Create various types of journal entries
        $this->createOpeningBalanceEntries($accounts);
        $this->createSalesTransactionEntries($accounts);
        $this->createPurchaseTransactionEntries($accounts);
        $this->createPayrollEntries($accounts);
        $this->createExpenseEntries($accounts);
        $this->createBankTransactionEntries($accounts);
        $this->createAdjustmentEntries($accounts);

        $this->command->info('Comprehensive journal seeding completed!');
    }

    private function createOpeningBalanceEntries($accounts)
    {
        $this->command->info('Creating opening balance entries...');

        $journal = Journal::create([
            'journal_number' => 'JRN-' . date('Ym') . '-0001',
            'transaction_date' => Carbon::now()->startOfMonth(),
            'reference_number' => 'OB-' . date('Y'),
            'source_type' => 'Opening_Balance',
            'description' => 'Saldo Awal Periode ' . date('Y'),
            'status' => 'Posted',
            'created_by' => 1,
        ]);

        $entries = [
            ['account' => $accounts['kas'] ?? null, 'debit' => ********, 'credit' => 0, 'desc' => 'Saldo awal kas'],
            ['account' => $accounts['bank'] ?? null, 'debit' => 1********, 'credit' => 0, 'desc' => 'Saldo awal bank'],
            ['account' => $accounts['piutang'] ?? null, 'debit' => ********, 'credit' => 0, 'desc' => 'Saldo awal piutang usaha'],
            ['account' => $accounts['persediaan'] ?? null, 'debit' => *********, 'credit' => 0, 'desc' => 'Saldo awal persediaan'],
            ['account' => $accounts['peralatan'] ?? null, 'debit' => *********, 'credit' => 0, 'desc' => 'Saldo awal peralatan'],
            ['account' => $accounts['utang_usaha'] ?? null, 'debit' => 0, 'credit' => *********, 'desc' => 'Saldo awal utang usaha'],
            ['account' => $accounts['modal'] ?? null, 'debit' => 0, 'credit' => 6********, 'desc' => 'Saldo awal modal'],
        ];

        $sortOrder = 1;
        foreach ($entries as $entry) {
            if ($entry['account']) {
                JournalEntry::create([
                    'journal_id' => $journal->id,
                    'account_id' => $entry['account']->id,
                    'description' => $entry['desc'],
                    'debit' => $entry['debit'],
                    'credit' => $entry['credit'],
                    'sort_order' => $sortOrder++,
                ]);
            }
        }
    }

    private function createSalesTransactionEntries($accounts)
    {
        $this->command->info('Creating sales transaction entries...');

        for ($i = 1; $i <= 5; $i++) {
            $amount = rand(5000000, ********);
            $hpp = $amount * 0.6; // 60% of sales as COGS

            $journal = Journal::create([
                'journal_number' => 'JRN-' . date('Ym') . '-' . str_pad(1000 + $i, 4, '0', STR_PAD_LEFT),
                'transaction_date' => Carbon::now()->subDays(rand(1, 30)),
                'reference_number' => 'INV-' . date('Y') . str_pad($i, 4, '0', STR_PAD_LEFT),
                'source_type' => 'Sale',
                'description' => 'Penjualan barang kepada pelanggan #' . $i,
                'status' => 'Posted',
                'created_by' => 1,
            ]);

            $entries = [
                ['account' => $accounts['piutang'] ?? $accounts['kas'], 'debit' => $amount, 'credit' => 0, 'desc' => 'Piutang penjualan'],
                ['account' => $accounts['pendapatan'], 'debit' => 0, 'credit' => $amount, 'desc' => 'Pendapatan penjualan'],
                ['account' => $accounts['hpp'], 'debit' => $hpp, 'credit' => 0, 'desc' => 'Harga pokok penjualan'],
                ['account' => $accounts['persediaan'], 'debit' => 0, 'credit' => $hpp, 'desc' => 'Pengurangan persediaan'],
            ];

            $sortOrder = 1;
            foreach ($entries as $entry) {
                if ($entry['account']) {
                    JournalEntry::create([
                        'journal_id' => $journal->id,
                        'account_id' => $entry['account']->id,
                        'description' => $entry['desc'],
                        'debit' => $entry['debit'],
                        'credit' => $entry['credit'],
                        'sort_order' => $sortOrder++,
                    ]);
                }
            }
        }
    }

    private function createPurchaseTransactionEntries($accounts)
    {
        $this->command->info('Creating purchase transaction entries...');

        for ($i = 1; $i <= 3; $i++) {
            $amount = rand(********, ********);

            $journal = Journal::create([
                'journal_number' => 'JRN-' . date('Ym') . '-' . str_pad(2000 + $i, 4, '0', STR_PAD_LEFT),
                'transaction_date' => Carbon::now()->subDays(rand(1, 20)),
                'reference_number' => 'PO-' . date('Y') . str_pad($i, 4, '0', STR_PAD_LEFT),
                'source_type' => 'Purchase',
                'description' => 'Pembelian barang dari supplier #' . $i,
                'status' => 'Posted',
                'created_by' => 1,
            ]);

            $entries = [
                ['account' => $accounts['persediaan'], 'debit' => $amount, 'credit' => 0, 'desc' => 'Pembelian persediaan'],
                ['account' => $accounts['utang_usaha'], 'debit' => 0, 'credit' => $amount, 'desc' => 'Utang kepada supplier'],
            ];

            $sortOrder = 1;
            foreach ($entries as $entry) {
                if ($entry['account']) {
                    JournalEntry::create([
                        'journal_id' => $journal->id,
                        'account_id' => $entry['account']->id,
                        'description' => $entry['desc'],
                        'debit' => $entry['debit'],
                        'credit' => $entry['credit'],
                        'sort_order' => $sortOrder++,
                    ]);
                }
            }
        }
    }

    private function createPayrollEntries($accounts)
    {
        $this->command->info('Creating payroll entries...');

        $payrollAmount = ********; // Total payroll
        $tax = $payrollAmount * 0.05; // 5% tax
        $netPay = $payrollAmount - $tax;

        $journal = Journal::create([
            'journal_number' => 'JRN-' . date('Ym') . '-3001',
            'transaction_date' => Carbon::now()->subDays(5),
            'reference_number' => 'PAY-' . date('Ym'),
            'source_type' => 'Payroll',
            'description' => 'Pembayaran gaji karyawan bulan ' . date('F Y'),
            'status' => 'Posted',
            'created_by' => 1,
        ]);

        $entries = [
            ['account' => $accounts['beban_gaji'], 'debit' => $payrollAmount, 'credit' => 0, 'desc' => 'Beban gaji karyawan'],
            ['account' => $accounts['kas'], 'debit' => 0, 'credit' => $netPay, 'desc' => 'Pembayaran gaji bersih'],
            ['account' => $accounts['utang_pajak'] ?? $accounts['utang_usaha'], 'debit' => 0, 'credit' => $tax, 'desc' => 'Utang pajak penghasilan'],
        ];

        $sortOrder = 1;
        foreach ($entries as $entry) {
            if ($entry['account']) {
                JournalEntry::create([
                    'journal_id' => $journal->id,
                    'account_id' => $entry['account']->id,
                    'description' => $entry['desc'],
                    'debit' => $entry['debit'],
                    'credit' => $entry['credit'],
                    'sort_order' => $sortOrder++,
                ]);
            }
        }
    }

    private function createExpenseEntries($accounts)
    {
        $this->command->info('Creating expense entries...');

        $expenses = [
            ['desc' => 'Pembayaran listrik dan air', 'amount' => 2500000],
            ['desc' => 'Biaya transportasi', 'amount' => 1800000],
            ['desc' => 'Biaya komunikasi dan internet', 'amount' => 1200000],
            ['desc' => 'Biaya pemeliharaan peralatan', 'amount' => 3500000],
        ];

        foreach ($expenses as $index => $expense) {
            $journal = Journal::create([
                'journal_number' => 'JRN-' . date('Ym') . '-' . str_pad(4000 + $index + 1, 4, '0', STR_PAD_LEFT),
                'transaction_date' => Carbon::now()->subDays(rand(1, 15)),
                'reference_number' => 'EXP-' . date('Y') . str_pad($index + 1, 4, '0', STR_PAD_LEFT),
                'source_type' => 'Expense',
                'description' => $expense['desc'],
                'status' => 'Posted',
                'created_by' => 1,
            ]);

            $entries = [
                ['account' => $accounts['beban_operasional'], 'debit' => $expense['amount'], 'credit' => 0, 'desc' => $expense['desc']],
                ['account' => $accounts['kas'], 'debit' => 0, 'credit' => $expense['amount'], 'desc' => 'Pembayaran tunai'],
            ];

            $sortOrder = 1;
            foreach ($entries as $entry) {
                if ($entry['account']) {
                    JournalEntry::create([
                        'journal_id' => $journal->id,
                        'account_id' => $entry['account']->id,
                        'description' => $entry['desc'],
                        'debit' => $entry['debit'],
                        'credit' => $entry['credit'],
                        'sort_order' => $sortOrder++,
                    ]);
                }
            }
        }
    }

    private function createBankTransactionEntries($accounts)
    {
        $this->command->info('Creating bank transaction entries...');

        // Bank deposit
        $depositAmount = ********;
        $journal = Journal::create([
            'journal_number' => 'JRN-' . date('Ym') . '-5001',
            'transaction_date' => Carbon::now()->subDays(3),
            'reference_number' => 'DEP-' . date('Ymd') . '001',
            'source_type' => 'Bank_Deposit',
            'description' => 'Setoran kas ke bank',
            'status' => 'Posted',
            'created_by' => 1,
        ]);

        $entries = [
            ['account' => $accounts['bank'], 'debit' => $depositAmount, 'credit' => 0, 'desc' => 'Setoran ke bank'],
            ['account' => $accounts['kas'], 'debit' => 0, 'credit' => $depositAmount, 'desc' => 'Pengurangan kas'],
        ];

        $sortOrder = 1;
        foreach ($entries as $entry) {
            if ($entry['account']) {
                JournalEntry::create([
                    'journal_id' => $journal->id,
                    'account_id' => $entry['account']->id,
                    'description' => $entry['desc'],
                    'debit' => $entry['debit'],
                    'credit' => $entry['credit'],
                    'sort_order' => $sortOrder++,
                ]);
            }
        }
    }

    private function createAdjustmentEntries($accounts)
    {
        $this->command->info('Creating adjustment entries...');

        $adjustmentAmount = 500000;
        $journal = Journal::create([
            'journal_number' => 'JRN-' . date('Ym') . '-6001',
            'transaction_date' => Carbon::now()->subDays(1),
            'reference_number' => 'ADJ-' . date('Ymd') . '001',
            'source_type' => 'ManualAdjust',
            'description' => 'Penyesuaian persediaan (stock opname)',
            'status' => 'Draft',
            'created_by' => 1,
        ]);

        $entries = [
            ['account' => $accounts['beban_operasional'], 'debit' => $adjustmentAmount, 'credit' => 0, 'desc' => 'Selisih persediaan'],
            ['account' => $accounts['persediaan'], 'debit' => 0, 'credit' => $adjustmentAmount, 'desc' => 'Penyesuaian persediaan'],
        ];

        $sortOrder = 1;
        foreach ($entries as $entry) {
            if ($entry['account']) {
                JournalEntry::create([
                    'journal_id' => $journal->id,
                    'account_id' => $entry['account']->id,
                    'description' => $entry['desc'],
                    'debit' => $entry['debit'],
                    'credit' => $entry['credit'],
                    'sort_order' => $sortOrder++,
                ]);
            }
        }
    }
}
