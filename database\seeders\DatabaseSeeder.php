<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Master Data Seeders
        $this->call([
            TransactionCategorySeeder::class,
            TransactionSubcategorySeeder::class,
        ]);

        // Transaction Data Seeder with New Format
        $this->call([
            NewFormatTransactionSeeder::class,
        ]);

        $this->command->info('All seeders completed successfully!');
    }
}
