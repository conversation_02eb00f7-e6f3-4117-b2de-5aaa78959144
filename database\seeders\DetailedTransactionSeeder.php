<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\DailyTransaction;
use App\Models\Outlet;
use Carbon\Carbon;

class DetailedTransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $outlets = Outlet::all();
        $currentMonth = now();

        foreach ($outlets as $outlet) {
            // Sample revenue transactions with payment methods
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(5),
                'description' => 'Penjualan Cash Harian',
                'type' => 'revenue',
                'payment_method' => 'cash',
                'amount' => 454095300,
                'notes' => 'Penjualan tunai'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(5),
                'description' => 'Penjualan Debit',
                'type' => 'revenue',
                'payment_method' => 'debit',
                'amount' => ********,
                'notes' => 'Penjualan kartu debit'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(5),
                'description' => 'Penjualan Transfer',
                'type' => 'revenue',
                'payment_method' => 'transfer',
                'amount' => ********,
                'notes' => 'Penjualan transfer bank'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(5),
                'description' => 'Penjualan QRIS',
                'type' => 'revenue',
                'payment_method' => 'qris',
                'amount' => *********,
                'notes' => 'Penjualan QRIS'
            ]);

            // Sample expense transactions with categories and subcategories
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(3),
                'description' => 'Tagihan RKV',
                'type' => 'expense',
                'expense_category' => 'beban_bahan_baku',
                'subcategory' => 'tagihan_rkv',
                'amount' => *********,
                'notes' => 'Pembayaran tagihan RKV'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(3),
                'description' => 'Tagihan Mitra Juni 2025',
                'type' => 'expense',
                'expense_category' => 'beban_bahan_baku',
                'subcategory' => 'tagihan_mitra',
                'amount' => 224142500,
                'notes' => 'Pembayaran tagihan mitra'
            ]);

            // Beban Utilitas
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(4),
                'description' => 'Bayar Listrik',
                'type' => 'expense',
                'expense_category' => 'beban_utilitas',
                'subcategory' => 'listrik',
                'amount' => 7224654,
                'notes' => 'Pembayaran listrik bulanan'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(4),
                'description' => 'Bayar Indihome dan Pulsa',
                'type' => 'expense',
                'expense_category' => 'beban_utilitas',
                'subcategory' => 'internet_pulsa',
                'amount' => 324700,
                'notes' => 'Pembayaran internet dan pulsa'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(4),
                'description' => 'Jasa Pengendalian Hama (Petsco)',
                'type' => 'expense',
                'expense_category' => 'beban_utilitas',
                'subcategory' => 'pest_control',
                'amount' => 700000,
                'notes' => 'Jasa pest control bulanan'
            ]);

            // Beban GA
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(3),
                'description' => 'Material Bangunan dan Instalasi',
                'type' => 'expense',
                'expense_category' => 'beban_ga',
                'subcategory' => 'material_bangunan',
                'amount' => 1330000,
                'notes' => 'Pembelian material bangunan'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(3),
                'description' => 'Kertas Thermal dan Label',
                'type' => 'expense',
                'expense_category' => 'beban_ga',
                'subcategory' => 'kertas_thermal',
                'amount' => 525000,
                'notes' => 'Pembelian kertas thermal'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(3),
                'description' => 'Bensin Kendaraan Operasional',
                'type' => 'expense',
                'expense_category' => 'beban_ga',
                'subcategory' => 'bensin',
                'amount' => 2867000,
                'notes' => 'Bensin untuk kendaraan operasional'
            ]);

            // Beban Promosi
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(2),
                'description' => 'Free Ultah Customer',
                'type' => 'expense',
                'expense_category' => 'beban_promosi',
                'subcategory' => 'free_ultah',
                'amount' => 855000,
                'notes' => 'Promo gratis untuk ulang tahun customer'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(2),
                'description' => 'Free Paket Bundling',
                'type' => 'expense',
                'expense_category' => 'beban_promosi',
                'subcategory' => 'free_bundling',
                'amount' => 736000,
                'notes' => 'Promo paket bundling gratis'
            ]);

            // Pajak
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(2),
                'description' => 'Bayar PPh',
                'type' => 'expense',
                'expense_category' => 'pajak',
                'subcategory' => 'pph',
                'amount' => 7468491,
                'notes' => 'Pembayaran PPh bulanan'
            ]);

            // BPJS
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(2),
                'description' => 'BPJS Kesehatan',
                'type' => 'expense',
                'expense_category' => 'bpjs',
                'subcategory' => 'bpjs_kesehatan',
                'amount' => 918985,
                'notes' => 'Pembayaran BPJS Kesehatan'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(2),
                'description' => 'BPJS TK',
                'type' => 'expense',
                'expense_category' => 'bpjs',
                'subcategory' => 'bpjs_tk',
                'amount' => 149108,
                'notes' => 'Pembayaran BPJS Tenaga Kerja'
            ]);

            // Ongkir
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(1),
                'description' => 'Ongkir Customer',
                'type' => 'expense',
                'expense_category' => 'ongkir',
                'subcategory' => 'ongkir_customer',
                'amount' => 1220000,
                'notes' => 'Biaya ongkir untuk customer'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(1),
                'description' => 'Ongkir ke Cabang Panam-Riau',
                'type' => 'expense',
                'expense_category' => 'ongkir',
                'subcategory' => 'ongkir_cabang',
                'amount' => 257000,
                'notes' => 'Ongkir pengiriman ke cabang'
            ]);

            // Komisi Bank
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(1),
                'description' => 'Komisi Bank dan Gojek',
                'type' => 'expense',
                'expense_category' => 'komisi_bank',
                'subcategory' => 'komisi_gojek',
                'amount' => ********,
                'notes' => 'Komisi untuk transaksi digital'
            ]);

            // Gaji
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(1),
                'description' => 'Gaji Karyawan Juni 2025',
                'type' => 'expense',
                'expense_category' => 'gaji',
                'subcategory' => 'gaji_tetap',
                'amount' => ********,
                'notes' => 'Pembayaran gaji karyawan'
            ]);

            // Other
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(1),
                'description' => 'Pengeluaran Point',
                'type' => 'expense',
                'expense_category' => 'other',
                'subcategory' => 'pengeluaran_point',
                'amount' => 41000,
                'notes' => 'Pengeluaran untuk point customer'
            ]);
        }

        $this->command->info('Sample detailed transactions created successfully!');
    }
}
