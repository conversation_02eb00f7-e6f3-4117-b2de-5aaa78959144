<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\StockMovement;
use App\Models\StockAdjustment;
use App\Models\StockAdjustmentItem;
use App\Models\StockTransfer;
use App\Models\StockTransferItem;
use App\Models\InventoryStock;
use App\Models\Produk;
use App\Models\Entitas;
use App\Models\Warehouse;
use Carbon\Carbon;

class EnhancedInventorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating enhanced inventory data...');

        // Get required data
        $products = Produk::limit(10)->get();
        $entitas = Entitas::limit(3)->get();
        $warehouses = Warehouse::limit(2)->get();

        if ($products->isEmpty() || $entitas->isEmpty() || $warehouses->isEmpty()) {
            $this->command->error('Required data not found! Please run ProductSeeder, EntitasSeeder, and WarehouseSeeder first.');
            return;
        }

        $this->command->info('Found ' . $products->count() . ' products, ' . $entitas->count() . ' entitas, ' . $warehouses->count() . ' warehouses.');

        // Create opening balance stock movements
        $this->createOpeningBalanceMovements($products, $entitas, $warehouses);

        // Create purchase receipt movements
        $this->createPurchaseReceiptMovements($products, $entitas, $warehouses);

        // Create sales issue movements
        $this->createSalesIssueMovements($products, $entitas, $warehouses);

        // Create stock transfers
        $this->createStockTransfers($products, $entitas, $warehouses);

        // Create stock adjustments
        $this->createStockAdjustments($products, $entitas, $warehouses);

        $this->command->info('Enhanced inventory seeding completed!');
    }

    private function createOpeningBalanceMovements($products, $entitas, $warehouses)
    {
        $this->command->info('Creating opening balance movements...');

        foreach ($products as $product) {
            foreach ($entitas as $entitas_item) {
                $warehouse = $warehouses->random();
                $quantity = rand(50, 500);
                $unitCost = rand(10000, 100000);

                StockMovement::create([
                    'movement_date' => Carbon::now()->startOfMonth(),
                    'movement_type' => 'Opening_Balance',
                    'product_id' => $product->id,
                    'warehouse_id' => $warehouse->id,
                    'entitas_id' => $entitas_item->id,
                    'quantity' => $quantity,
                    'unit_cost' => $unitCost,
                    'reference_type' => 'Opening_Balance',
                    'reference_number' => 'OB-' . date('Y') . '-' . str_pad($product->id, 3, '0', STR_PAD_LEFT),
                    'notes' => 'Saldo awal persediaan',
                    'created_by' => 1,
                ]);
            }
        }
    }

    private function createPurchaseReceiptMovements($products, $entitas, $warehouses)
    {
        $this->command->info('Creating purchase receipt movements...');

        for ($i = 1; $i <= 15; $i++) {
            $product = $products->random();
            $entitas_item = $entitas->random();
            $warehouse = $warehouses->random();
            $quantity = rand(20, 200);
            $unitCost = rand(15000, 120000);

            StockMovement::create([
                'movement_date' => Carbon::now()->subDays(rand(1, 30)),
                'movement_type' => 'Purchase_Receipt',
                'product_id' => $product->id,
                'warehouse_id' => $warehouse->id,
                'entitas_id' => $entitas_item->id,
                'quantity' => $quantity,
                'unit_cost' => $unitCost,
                'reference_type' => 'Purchase_Order',
                'reference_id' => rand(1, 10),
                'reference_number' => 'PO-' . date('Y') . str_pad($i, 4, '0', STR_PAD_LEFT),
                'notes' => 'Penerimaan barang dari supplier',
                'created_by' => 1,
            ]);
        }
    }

    private function createSalesIssueMovements($products, $entitas, $warehouses)
    {
        $this->command->info('Creating sales issue movements...');

        for ($i = 1; $i <= 20; $i++) {
            $product = $products->random();
            $entitas_item = $entitas->random();
            $warehouse = $warehouses->random();

            // Check current stock to avoid negative
            $currentStock = InventoryStock::where('product_id', $product->id)
                ->where('warehouse_id', $warehouse->id)
                ->where('entitas_id', $entitas_item->id)
                ->first();

            $maxQuantity = $currentStock ? min($currentStock->quantity, 50) : 10;
            $quantity = rand(1, max(1, $maxQuantity));
            $unitCost = rand(15000, 120000);

            StockMovement::create([
                'movement_date' => Carbon::now()->subDays(rand(1, 25)),
                'movement_type' => 'Sales_Issue',
                'product_id' => $product->id,
                'warehouse_id' => $warehouse->id,
                'entitas_id' => $entitas_item->id,
                'quantity' => $quantity,
                'unit_cost' => $unitCost,
                'reference_type' => 'Sales_Order',
                'reference_id' => rand(1, 20),
                'reference_number' => 'SO-' . date('Y') . str_pad($i, 4, '0', STR_PAD_LEFT),
                'notes' => 'Pengeluaran barang untuk penjualan',
                'created_by' => 1,
            ]);
        }
    }

    private function createStockTransfers($products, $entitas, $warehouses)
    {
        $this->command->info('Creating stock transfers...');

        for ($i = 1; $i <= 5; $i++) {
            $fromEntitas = $entitas->random();
            $toEntitas = $entitas->where('id', '!=', $fromEntitas->id)->random();
            $fromWarehouse = $warehouses->random();
            $toWarehouse = $warehouses->random();

            $transfer = StockTransfer::create([
                'transfer_date' => Carbon::now()->subDays(rand(1, 20)),
                'from_warehouse_id' => $fromWarehouse->id,
                'to_warehouse_id' => $toWarehouse->id,
                'from_entitas_id' => $fromEntitas->id,
                'to_entitas_id' => $toEntitas->id,
                'status' => 'Completed',
                'transfer_reason' => 'Transfer antar entitas',
                'notes' => 'Transfer antar entitas',
                'requested_by' => 1,
                'approved_by' => 1,
                'approved_at' => Carbon::now()->subDays(rand(1, 15)),
                'sent_by' => 1,
                'sent_at' => Carbon::now()->subDays(rand(1, 10)),
                'received_by' => 1,
                'received_at' => Carbon::now()->subDays(rand(1, 5)),
            ]);

            // Add transfer items
            $numItems = rand(1, 3);
            $usedProducts = [];
            for ($j = 0; $j < $numItems; $j++) {
                // Ensure we don't use the same product twice for the same transfer
                do {
                    $product = $products->random();
                } while (in_array($product->id, $usedProducts));

                $usedProducts[] = $product->id;
                $quantity = rand(5, 50);
                $unitCost = rand(20000, 100000);

                StockTransferItem::create([
                    'stock_transfer_id' => $transfer->id,
                    'product_id' => $product->id,
                    'quantity_requested' => $quantity,
                    'quantity_sent' => $quantity,
                    'quantity_received' => $quantity,
                    'unit_cost' => $unitCost,
                    'notes' => 'Transfer item',
                ]);

                // Create corresponding stock movements
                StockMovement::create([
                    'movement_date' => $transfer->sent_at,
                    'movement_type' => 'Transfer_Out',
                    'product_id' => $product->id,
                    'warehouse_id' => $fromWarehouse->id,
                    'entitas_id' => $fromEntitas->id,
                    'quantity' => $quantity,
                    'unit_cost' => $unitCost,
                    'reference_type' => StockTransfer::class,
                    'reference_id' => $transfer->id,
                    'reference_number' => $transfer->transfer_number,
                    'notes' => 'Transfer keluar',
                    'created_by' => 1,
                ]);

                StockMovement::create([
                    'movement_date' => $transfer->received_at,
                    'movement_type' => 'Transfer_In',
                    'product_id' => $product->id,
                    'warehouse_id' => $toWarehouse->id,
                    'entitas_id' => $toEntitas->id,
                    'quantity' => $quantity,
                    'unit_cost' => $unitCost,
                    'reference_type' => StockTransfer::class,
                    'reference_id' => $transfer->id,
                    'reference_number' => $transfer->transfer_number,
                    'notes' => 'Transfer masuk',
                    'created_by' => 1,
                ]);
            }
        }
    }

    private function createStockAdjustments($products, $entitas, $warehouses)
    {
        $this->command->info('Creating stock adjustments...');

        for ($i = 1; $i <= 3; $i++) {
            $entitas_item = $entitas->random();
            $warehouse = $warehouses->random();

            $adjustment = StockAdjustment::create([
                'adjustment_date' => Carbon::now()->subDays(rand(1, 15)),
                'adjustment_type' => ['Increase', 'Decrease'][rand(0, 1)],
                'adjustment_reason' => ['Damaged', 'Correction', 'Found'][rand(0, 2)],
                'warehouse_id' => $warehouse->id,
                'entitas_id' => $entitas_item->id,
                'description' => 'Penyesuaian stok berdasarkan stock opname',
                'status' => 'Approved',
                'notes' => 'Penyesuaian stok bulanan',
                'created_by' => 1,
                'approved_by' => 1,
                'approved_at' => Carbon::now()->subDays(rand(1, 10)),
            ]);

            // Add adjustment items
            $numItems = rand(2, 4);
            $usedProducts = [];
            for ($j = 0; $j < $numItems; $j++) {
                // Ensure we don't use the same product twice for the same adjustment
                do {
                    $product = $products->random();
                } while (in_array($product->id, $usedProducts));

                $usedProducts[] = $product->id;

                // Get current stock
                $currentStock = InventoryStock::where('product_id', $product->id)
                    ->where('warehouse_id', $warehouse->id)
                    ->where('entitas_id', $entitas_item->id)
                    ->first();

                $systemQuantity = $currentStock ? $currentStock->quantity : 0;
                $physicalQuantity = $systemQuantity + rand(-10, 10); // Small variance
                $unitCost = rand(20000, 100000);

                $adjustmentQuantity = max(0, $physicalQuantity) - $systemQuantity;

                StockAdjustmentItem::create([
                    'stock_adjustment_id' => $adjustment->id,
                    'product_id' => $product->id,
                    'system_quantity' => $systemQuantity,
                    'physical_quantity' => $physicalQuantity,
                    'quantity_adjustment' => $adjustmentQuantity,
                    'unit_cost' => $unitCost,
                    'item_notes' => 'Hasil stock opname',
                ]);
            }
        }
    }
}
