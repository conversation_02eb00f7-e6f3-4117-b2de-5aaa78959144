<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PayrollComponent;
use App\Models\EmployeePayrollComponent;
use App\Models\TaxBracket;
use App\Models\PtkpRate;
use App\Models\EmployeeTaxInfo;
use App\Models\BpjsRate;
use App\Models\EmployeeBpjsInfo;
use App\Models\Akun;
use App\Models\Karyawan;
use Carbon\Carbon;

class EnhancedPayrollSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create payroll accounts if they don't exist
        $payrollAccounts = [
            ['5101', 'Beban Gaji'],
            ['5102', 'Beban BPJS Perusahaan'],
            ['2002', 'Hutang Gaji'],
            ['2003', 'Hutang Pajak PPh 21'],
            ['2004', 'Hutang BPJS'],
        ];

        foreach ($payrollAccounts as [$code, $name]) {
            Akun::firstOrCreate(
                ['kode_akun' => $code],
                [
                    'nama_akun' => $name,
                    'kategori_akun' => str_starts_with($code, '5') ? 'Beban' : 'Kewajiban',
                    'tipe_akun' => str_starts_with($code, '5') ? 'Debit' : 'Credit',
                    'saldo_awal' => 0,
                    'created_by' => 1,
                ]
            );
        }

        // Create Tax Brackets
        $taxBrackets = TaxBracket::getDefaultBrackets();
        foreach ($taxBrackets as $bracket) {
            TaxBracket::create(array_merge($bracket, [
                'is_active' => true,
                'effective_date' => Carbon::create(2024, 1, 1),
                'created_by' => 1,
            ]));
        }

        // Create PTKP Rates
        $ptkpRates = PtkpRate::getDefaultRates();
        foreach ($ptkpRates as $rate) {
            PtkpRate::create(array_merge($rate, [
                'is_active' => true,
                'effective_date' => Carbon::create(2024, 1, 1),
                'created_by' => 1,
            ]));
        }

        // Create BPJS Rates
        $bpjsRates = [
            [
                'bpjs_type' => 'Kesehatan',
                'participant_type' => 'Pekerja',
                'rate_percentage' => 1.0,
                'max_salary_base' => 12000000,
                'min_salary_base' => 0,
                'is_active' => true,
                'effective_date' => Carbon::create(2024, 1, 1),
                'description' => 'BPJS Kesehatan - Kontribusi Pekerja 1%',
                'created_by' => 1,
            ],
            [
                'bpjs_type' => 'Kesehatan',
                'participant_type' => 'Perusahaan',
                'rate_percentage' => 4.0,
                'max_salary_base' => 12000000,
                'min_salary_base' => 0,
                'is_active' => true,
                'effective_date' => Carbon::create(2024, 1, 1),
                'description' => 'BPJS Kesehatan - Kontribusi Perusahaan 4%',
                'created_by' => 1,
            ],
            [
                'bpjs_type' => 'Ketenagakerjaan',
                'participant_type' => 'Pekerja',
                'rate_percentage' => 2.0,
                'max_salary_base' => null,
                'min_salary_base' => 0,
                'is_active' => true,
                'effective_date' => Carbon::create(2024, 1, 1),
                'description' => 'BPJS Ketenagakerjaan - Kontribusi Pekerja 2%',
                'created_by' => 1,
            ],
            [
                'bpjs_type' => 'Ketenagakerjaan',
                'participant_type' => 'Perusahaan',
                'rate_percentage' => 3.7,
                'max_salary_base' => null,
                'min_salary_base' => 0,
                'is_active' => true,
                'effective_date' => Carbon::create(2024, 1, 1),
                'description' => 'BPJS Ketenagakerjaan - Kontribusi Perusahaan 3.7%',
                'created_by' => 1,
            ],
        ];

        foreach ($bpjsRates as $rate) {
            BpjsRate::create($rate);
        }

        // Create Payroll Components
        $components = [
            // Earnings
            [
                'component_code' => 'GAPOK',
                'component_name' => 'Gaji Pokok',
                'component_type' => 'Earning',
                'calculation_type' => 'Fixed',
                'account_id' => Akun::where('kode_akun', '5101')->first()->id,
                'is_taxable' => true,
                'is_active' => true,
                'description' => 'Gaji pokok karyawan',
                'created_by' => 1,
            ],
            [
                'component_code' => 'TUNJAB',
                'component_name' => 'Tunjangan Jabatan',
                'component_type' => 'Earning',
                'calculation_type' => 'Fixed',
                'account_id' => Akun::where('kode_akun', '5101')->first()->id,
                'is_taxable' => true,
                'is_active' => true,
                'description' => 'Tunjangan berdasarkan jabatan',
                'created_by' => 1,
            ],
            [
                'component_code' => 'TUNKEL',
                'component_name' => 'Tunjangan Keluarga',
                'component_type' => 'Earning',
                'calculation_type' => 'Percentage',
                'percentage_rate' => 10,
                'account_id' => Akun::where('kode_akun', '5101')->first()->id,
                'is_taxable' => true,
                'is_active' => true,
                'description' => 'Tunjangan keluarga 10% dari gaji pokok',
                'created_by' => 1,
            ],
            [
                'component_code' => 'TUNMAKAN',
                'component_name' => 'Tunjangan Makan',
                'component_type' => 'Earning',
                'calculation_type' => 'Fixed',
                'fixed_amount' => 300000,
                'account_id' => Akun::where('kode_akun', '5101')->first()->id,
                'is_taxable' => false,
                'is_active' => true,
                'description' => 'Tunjangan makan tetap per bulan',
                'created_by' => 1,
            ],
            [
                'component_code' => 'TUNTRANS',
                'component_name' => 'Tunjangan Transport',
                'component_type' => 'Earning',
                'calculation_type' => 'Fixed',
                'fixed_amount' => 500000,
                'account_id' => Akun::where('kode_akun', '5101')->first()->id,
                'is_taxable' => false,
                'is_active' => true,
                'description' => 'Tunjangan transport tetap per bulan',
                'created_by' => 1,
            ],
            
            // Deductions
            [
                'component_code' => 'PPH21',
                'component_name' => 'PPh 21',
                'component_type' => 'Deduction',
                'calculation_type' => 'Formula',
                'calculation_formula' => 'PPH21_CALCULATION',
                'account_id' => Akun::where('kode_akun', '2003')->first()->id,
                'is_taxable' => false,
                'is_active' => true,
                'description' => 'Pajak Penghasilan Pasal 21',
                'created_by' => 1,
            ],
            [
                'component_code' => 'BPJSKES',
                'component_name' => 'BPJS Kesehatan',
                'component_type' => 'Deduction',
                'calculation_type' => 'Percentage',
                'percentage_rate' => 1.0,
                'account_id' => Akun::where('kode_akun', '2004')->first()->id,
                'is_taxable' => false,
                'is_active' => true,
                'description' => 'BPJS Kesehatan 1% dari gaji',
                'created_by' => 1,
            ],
            [
                'component_code' => 'BPJSTK',
                'component_name' => 'BPJS Ketenagakerjaan',
                'component_type' => 'Deduction',
                'calculation_type' => 'Percentage',
                'percentage_rate' => 2.0,
                'account_id' => Akun::where('kode_akun', '2004')->first()->id,
                'is_taxable' => false,
                'is_active' => true,
                'description' => 'BPJS Ketenagakerjaan 2% dari gaji',
                'created_by' => 1,
            ],
            [
                'component_code' => 'ALPHA',
                'component_name' => 'Potongan Alpha',
                'component_type' => 'Deduction',
                'calculation_type' => 'Formula',
                'calculation_formula' => '(BASIC_SALARY / WORKING_DAYS) * ALPHA_DAYS',
                'account_id' => Akun::where('kode_akun', '5101')->first()->id,
                'is_taxable' => false,
                'is_active' => true,
                'description' => 'Potongan karena tidak masuk tanpa keterangan',
                'created_by' => 1,
            ],
            [
                'component_code' => 'TERLAMBAT',
                'component_name' => 'Potongan Terlambat',
                'component_type' => 'Deduction',
                'calculation_type' => 'Formula',
                'calculation_formula' => 'LATE_PENALTY_AMOUNT',
                'account_id' => Akun::where('kode_akun', '5101')->first()->id,
                'is_taxable' => false,
                'is_active' => true,
                'description' => 'Potongan karena terlambat',
                'created_by' => 1,
            ],
        ];

        foreach ($components as $component) {
            PayrollComponent::create($component);
        }

        // Assign payroll components to employees
        $employees = Karyawan::all();
        $payrollComponents = PayrollComponent::all();

        foreach ($employees as $employee) {
            // Basic components for all employees
            $basicComponents = [
                'GAPOK' => $employee->gaji_pokok ?? 5000000,
                'TUNJAB' => 1000000,
                'TUNMAKAN' => 300000,
                'TUNTRANS' => 500000,
            ];

            foreach ($basicComponents as $code => $amount) {
                $component = $payrollComponents->where('component_code', $code)->first();
                if ($component) {
                    EmployeePayrollComponent::create([
                        'employee_id' => $employee->id,
                        'payroll_component_id' => $component->id,
                        'amount' => $amount,
                        'is_active' => true,
                        'effective_date' => Carbon::create(2024, 1, 1),
                        'created_by' => 1,
                    ]);
                }
            }

            // Deduction components (use default calculation)
            $deductionComponents = ['PPH21', 'BPJSKES', 'BPJSTK'];
            foreach ($deductionComponents as $code) {
                $component = $payrollComponents->where('component_code', $code)->first();
                if ($component) {
                    EmployeePayrollComponent::create([
                        'employee_id' => $employee->id,
                        'payroll_component_id' => $component->id,
                        'is_active' => true,
                        'effective_date' => Carbon::create(2024, 1, 1),
                        'created_by' => 1,
                    ]);
                }
            }

            // Create employee tax info
            $ptkpStatuses = ['TK/0', 'TK/1', 'K/0', 'K/1', 'K/2'];
            $randomPtkp = $ptkpStatuses[array_rand($ptkpStatuses)];
            
            EmployeeTaxInfo::create([
                'employee_id' => $employee->id,
                'npwp' => $this->generateNpwp(),
                'ptkp_status' => $randomPtkp,
                'previous_income' => 0,
                'previous_tax' => 0,
                'is_tax_exempted' => false,
                'effective_date' => Carbon::create(2024, 1, 1),
                'created_by' => 1,
            ]);

            // Create employee BPJS info
            EmployeeBpjsInfo::create([
                'employee_id' => $employee->id,
                'bpjs_kesehatan_number' => $this->generateBpjsNumber('KES'),
                'bpjs_tk_number' => $this->generateBpjsNumber('TK'),
                'bpjs_kesehatan_active' => true,
                'bpjs_tk_active' => true,
                'bpjs_kesehatan_start_date' => Carbon::create(2024, 1, 1),
                'bpjs_tk_start_date' => Carbon::create(2024, 1, 1),
                'bpjs_kesehatan_salary_base' => min($employee->gaji_pokok ?? 5000000, 12000000),
                'bpjs_tk_salary_base' => $employee->gaji_pokok ?? 5000000,
                'created_by' => 1,
            ]);
        }

        $this->command->info('Created enhanced payroll data:');
        $this->command->info('- ' . count($taxBrackets) . ' tax brackets');
        $this->command->info('- ' . count($ptkpRates) . ' PTKP rates');
        $this->command->info('- ' . count($bpjsRates) . ' BPJS rates');
        $this->command->info('- ' . count($components) . ' payroll components');
        $this->command->info('- Employee payroll components for ' . $employees->count() . ' employees');
        $this->command->info('- Employee tax info for ' . $employees->count() . ' employees');
        $this->command->info('- Employee BPJS info for ' . $employees->count() . ' employees');
    }

    private function generateNpwp()
    {
        return sprintf(
            '%02d.%03d.%03d.%d-%03d.%03d',
            rand(1, 99),
            rand(100, 999),
            rand(100, 999),
            rand(1, 9),
            rand(100, 999),
            rand(100, 999)
        );
    }

    private function generateBpjsNumber($type)
    {
        $prefix = $type === 'KES' ? '0001' : '1700';
        return $prefix . sprintf('%08d', rand(10000000, 99999999));
    }
}
