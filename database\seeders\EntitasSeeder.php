<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Entitas;

class EntitasSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $entitas = [
            [
                'nama' => 'Toko Viera Pusat',
                'alamat' => 'Jl. Sudirman No. 123, Jakarta Pusat',
                'keterangan' => 'Toko utama dan pusat distribusi',
                'is_active' => true,
                'jenis' => 'toko',
                'kode' => 'VRA001',
                'kota' => 'Jakarta',
                'manager_name' => 'Budi Santoso',
                'email' => '<EMAIL>',
                'opening_date' => '2020-01-15',
                'opening_time' => '08:00:00',
                'closing_time' => '22:00:00',
                'latitude' => -6.2088,
                'longitude' => 106.8456,
                'radius' => 100,
                'enable_geofencing' => true,
            ],
            [
                'nama' => 'Toko Viera Bandung',
                'alamat' => 'Jl. Asia Afrika No. 45, Bandung',
                'keterangan' => 'Cabang Bandung',
                'is_active' => true,
                'jenis' => 'toko',
                'kode' => 'VRA002',
                'kota' => 'Bandung',
                'manager_name' => 'Siti Nurhaliza',
                'email' => '<EMAIL>',
                'opening_date' => '2020-06-01',
                'opening_time' => '09:00:00',
                'closing_time' => '21:00:00',
                'latitude' => -6.9175,
                'longitude' => 107.6191,
                'radius' => 150,
                'enable_geofencing' => true,
            ],
            [
                'nama' => 'Toko Viera Surabaya',
                'alamat' => 'Jl. Tunjungan No. 78, Surabaya',
                'keterangan' => 'Cabang Surabaya',
                'is_active' => true,
                'jenis' => 'toko',
                'kode' => 'VRA003',
                'kota' => 'Surabaya',
                'manager_name' => 'Ahmad Wijaya',
                'email' => '<EMAIL>',
                'opening_date' => '2021-03-15',
                'opening_time' => '08:30:00',
                'closing_time' => '21:30:00',
                'latitude' => -7.2575,
                'longitude' => 112.7521,
                'radius' => 120,
                'enable_geofencing' => true,
            ],
            [
                'nama' => 'Gudang Viera Jakarta',
                'alamat' => 'Jl. Industri Raya No. 234, Cakung, Jakarta Timur',
                'keterangan' => 'Gudang pusat distribusi',
                'is_active' => true,
                'jenis' => 'gudang',
                'kode' => 'VRA004',
                'kota' => 'Jakarta',
                'manager_name' => 'Rudi Hermawan',
                'email' => '<EMAIL>',
                'opening_date' => '2020-01-01',
                'opening_time' => '07:00:00',
                'closing_time' => '17:00:00',
                'latitude' => -6.1745,
                'longitude' => 106.9442,
                'radius' => 200,
                'enable_geofencing' => true,
            ],
            [
                'nama' => 'Kantor Pusat Viera',
                'alamat' => 'Jl. HR Rasuna Said No. 567, Jakarta Selatan',
                'keterangan' => 'Kantor pusat administrasi',
                'is_active' => true,
                'jenis' => 'kantor',
                'kode' => 'VRA005',
                'kota' => 'Jakarta',
                'manager_name' => 'Dewi Sartika',
                'email' => '<EMAIL>',
                'opening_date' => '2019-12-01',
                'opening_time' => '08:00:00',
                'closing_time' => '17:00:00',
                'latitude' => -6.2297,
                'longitude' => 106.8253,
                'radius' => 50,
                'enable_geofencing' => true,
            ],
        ];

        foreach ($entitas as $data) {
            Entitas::updateOrCreate(
                ['kode' => $data['kode']],
                $data
            );
        }
    }
}
