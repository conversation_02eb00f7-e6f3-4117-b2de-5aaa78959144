<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ExpenseCategory;
use App\Models\ExpenseRequest;
use App\Models\ExpenseRequestItem;
use App\Models\PettyCashFund;
use App\Models\Akun;
use App\Models\Karyawan;
use App\Models\Entitas;
use Carbon\Carbon;

class ExpenseSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create expense accounts if they don't exist
        $expenseAccounts = [
            ['5103', 'Beban Transport'],
            ['5104', 'Beban Makan'],
            ['5105', 'Beban Perlengkapan Kantor'],
            ['5106', 'Beban Komunikasi'],
            ['5107', 'Beban Pelatihan'],
            ['5108', 'Beban Lain-lain'],
        ];

        foreach ($expenseAccounts as [$code, $name]) {
            Akun::firstOrCreate(
                ['kode_akun' => $code],
                [
                    'nama_akun' => $name,
                    'kategori_akun' => 'Beban',
                    'tipe_akun' => 'Debit',
                    'saldo_awal' => 0,
                    'created_by' => 1,
                ]
            );
        }

        // Create expense categories
        $categories = [
            [
                'name' => 'Transport',
                'code' => 'TRANS',
                'description' => 'Transportation expenses including fuel, parking, toll, etc.',
                'default_account_id' => Akun::where('kode_akun', '5103')->first()->id,
                'daily_limit' => 200000,
                'monthly_limit' => 4000000,
                'requires_receipt' => true,
                'is_active' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Meals',
                'code' => 'MEALS',
                'description' => 'Meal expenses for business purposes',
                'default_account_id' => Akun::where('kode_akun', '5104')->first()->id,
                'daily_limit' => 150000,
                'monthly_limit' => 3000000,
                'requires_receipt' => true,
                'is_active' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Office Supplies',
                'code' => 'OFFICE',
                'description' => 'Office supplies and stationery',
                'default_account_id' => Akun::where('kode_akun', '5105')->first()->id,
                'daily_limit' => 500000,
                'monthly_limit' => 5000000,
                'requires_receipt' => true,
                'is_active' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Communication',
                'code' => 'COMM',
                'description' => 'Phone, internet, and communication expenses',
                'default_account_id' => Akun::where('kode_akun', '5106')->first()->id,
                'daily_limit' => 100000,
                'monthly_limit' => 2000000,
                'requires_receipt' => false,
                'is_active' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Training',
                'code' => 'TRAIN',
                'description' => 'Training and development expenses',
                'default_account_id' => Akun::where('kode_akun', '5107')->first()->id,
                'daily_limit' => 0, // No daily limit
                'monthly_limit' => ********,
                'requires_receipt' => true,
                'is_active' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Miscellaneous',
                'code' => 'MISC',
                'description' => 'Other miscellaneous expenses',
                'default_account_id' => Akun::where('kode_akun', '5108')->first()->id,
                'daily_limit' => 300000,
                'monthly_limit' => 2000000,
                'requires_receipt' => true,
                'is_active' => true,
                'created_by' => 1,
            ],
        ];

        foreach ($categories as $categoryData) {
            ExpenseCategory::create($categoryData);
        }

        // Create petty cash funds
        $entitas = Entitas::all();
        $employees = Karyawan::all();
        $cashAccount = Akun::where('kode_akun', '1401')->first();
        
        if (!$cashAccount) {
            $cashAccount = Akun::create([
                'kode_akun' => '1401',
                'nama_akun' => 'Kas Kecil',
                'kategori_akun' => 'Aset',
                'tipe_akun' => 'Debit',
                'saldo_awal' => 0,
                'created_by' => 1,
            ]);
        }

        foreach ($entitas->take(3) as $index => $entitas) {
            $custodian = $employees->where('id_entitas', $entitas->id)->first();
            if (!$custodian) {
                $custodian = $employees->first();
            }

            PettyCashFund::create([
                'fund_name' => 'Kas Kecil ' . $entitas->nama,
                'fund_code' => 'PC-' . str_pad($index + 1, 3, '0', STR_PAD_LEFT),
                'entitas_id' => $entitas->id,
                'custodian_employee_id' => $custodian->id,
                'cash_account_id' => $cashAccount->id,
                'initial_amount' => 5000000,
                'current_balance' => 5000000,
                'maximum_amount' => ********,
                'minimum_balance' => 1000000,
                'is_active' => true,
                'created_by' => 1,
            ]);
        }

        // Create sample expense requests
        $expenseCategories = ExpenseCategory::all();
        
        for ($i = 1; $i <= 15; $i++) {
            $employee = Karyawan::inRandomOrder()->first();
            $entitas = Entitas::inRandomOrder()->first();
            
            $expenseRequest = ExpenseRequest::create([
                'request_date' => Carbon::now()->subDays(rand(1, 30)),
                'employee_id' => $employee->id,
                'entitas_id' => $entitas->id,
                'expense_type' => $this->getRandomExpenseType(),
                'total_amount' => 0, // Will be calculated
                'status' => $this->getRandomStatus(),
                'purpose' => $this->getRandomPurpose(),
                'notes' => 'Sample expense request ' . $i,
                'requested_by' => 1,
                'approved_by' => rand(0, 1) ? 1 : null,
                'approved_at' => rand(0, 1) ? Carbon::now()->subDays(rand(1, 5)) : null,
            ]);

            // Create expense request items
            $itemCount = rand(1, 4);
            for ($j = 0; $j < $itemCount; $j++) {
                $category = $expenseCategories->random();
                $amount = rand(50000, 500000);
                
                ExpenseRequestItem::create([
                    'expense_request_id' => $expenseRequest->id,
                    'expense_category_id' => $category->id,
                    'description' => $this->getRandomDescription($category->name),
                    'amount' => $amount,
                    'expense_date' => $expenseRequest->request_date->addDays(rand(0, 2)),
                    'receipt_number' => 'RCP-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
                    'account_id' => $category->default_account_id,
                    'notes' => 'Sample expense item',
                ]);
            }

            // Recalculate totals
            $expenseRequest->calculateTotals();
        }

        $this->command->info('Created ' . count($categories) . ' expense categories, ' . $entitas->count() . ' petty cash funds, and 15 expense requests.');
    }

    private function getRandomExpenseType()
    {
        $types = ['Petty_Cash', 'Reimbursement', 'Advance'];
        return $types[array_rand($types)];
    }

    private function getRandomStatus()
    {
        $statuses = ['Draft', 'Submitted', 'Approved', 'Rejected', 'Paid'];
        $weights = [20, 25, 30, 10, 15]; // Percentage weights
        
        $random = rand(1, 100);
        $cumulative = 0;
        
        foreach ($weights as $index => $weight) {
            $cumulative += $weight;
            if ($random <= $cumulative) {
                return $statuses[$index];
            }
        }
        
        return 'Draft';
    }

    private function getRandomPurpose()
    {
        $purposes = [
            'Business meeting with client',
            'Office supplies procurement',
            'Team lunch meeting',
            'Transportation for site visit',
            'Training seminar attendance',
            'Equipment maintenance',
            'Marketing event preparation',
            'Customer presentation',
            'Vendor negotiation meeting',
            'Project coordination meeting',
        ];
        
        return $purposes[array_rand($purposes)];
    }

    private function getRandomDescription($categoryName)
    {
        $descriptions = [
            'Transport' => ['Fuel for company car', 'Parking fee', 'Toll road fee', 'Taxi fare', 'Bus ticket'],
            'Meals' => ['Lunch with client', 'Team dinner', 'Coffee meeting', 'Business breakfast', 'Catering for meeting'],
            'Office Supplies' => ['Printer paper', 'Pens and pencils', 'Folders and files', 'Whiteboard markers', 'Stapler and clips'],
            'Communication' => ['Mobile phone credit', 'Internet package', 'Phone bill', 'Video conference subscription', 'Courier service'],
            'Training' => ['Seminar registration', 'Workshop materials', 'Online course subscription', 'Training books', 'Certification exam'],
            'Miscellaneous' => ['Cleaning supplies', 'First aid kit', 'Office decoration', 'Welcome gift', 'Miscellaneous expense'],
        ];
        
        $categoryDescriptions = $descriptions[$categoryName] ?? $descriptions['Miscellaneous'];
        return $categoryDescriptions[array_rand($categoryDescriptions)];
    }
}
