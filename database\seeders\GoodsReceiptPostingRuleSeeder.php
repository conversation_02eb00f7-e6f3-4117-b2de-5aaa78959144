<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PostingRule;
use App\Models\PostingRuleMapping;
use App\Models\PostingRuleCondition;
use App\Models\Akun;
use Carbon\Carbon;

class GoodsReceiptPostingRuleSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Get accounts
        $inventory = Akun::where('kode_akun', '1301')->first();
        $payable = Akun::where('kode_akun', '2001')->first();

        if (!$inventory || !$payable) {
            $this->command->error('Required accounts not found. Please ensure accounts 1301 (Inventory) and 2001 (Accounts Payable) exist.');
            return;
        }

        // Create posting rule
        $rule = PostingRule::create([
            'rule_name' => 'Goods Receipt - Inventory & Payable',
            'source_type' => 'App\\Models\\GoodsReceipt',
            'description' => 'Auto-generate journal for goods receipt: Dr. Invent<PERSON>, Cr. Accounts Payable',
            'is_conditional' => true,
            'priority' => 1,
            'is_active' => true,
            'effective_date' => Carbon::now(),
            'created_by' => 1,
        ]);

        $this->command->info('Created posting rule: ' . $rule->rule_name);

        // Create condition
        PostingRuleCondition::create([
            'posting_rule_id' => $rule->id,
            'field_name' => 'status',
            'operator' => '=',
            'value' => '"Completed"',
            'logical_operator' => 'AND',
            'group_number' => 1,
        ]);

        $this->command->info('Created condition for status = Completed');

        // Create mappings
        PostingRuleMapping::create([
            'posting_rule_id' => $rule->id,
            'mapping_type' => 'debit',
            'account_source' => 'fixed',
            'account_id' => $inventory->id,
            'amount_source' => 'field',
            'amount_field' => 'total_value',
            'description_template' => 'Goods Receipt - {receipt_number}',
            'sequence' => 1,
        ]);

        PostingRuleMapping::create([
            'posting_rule_id' => $rule->id,
            'mapping_type' => 'credit',
            'account_source' => 'fixed',
            'account_id' => $payable->id,
            'amount_source' => 'field',
            'amount_field' => 'total_value',
            'description_template' => 'Goods Receipt - {receipt_number}',
            'sequence' => 2,
        ]);

        $this->command->info('Created mappings for Debit Inventory and Credit Payable');
        $this->command->info('Posting rule setup completed!');
    }
}
