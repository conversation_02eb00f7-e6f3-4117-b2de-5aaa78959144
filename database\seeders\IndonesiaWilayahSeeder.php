<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Province;
use App\Models\City;
use App\Models\District;
use App\Models\Village;
use Illuminate\Support\Facades\DB;

class IndonesiaWilayahSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding Indonesia wilayah data from official source...');

        // Disable foreign key checks for faster insertion
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Parse and seed the data
        $this->seedWilayahData();

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $this->command->info('Indonesia wilayah data seeded successfully!');
    }

    private function seedWilayahData()
    {
        // Data wilayah from GitHub source
        $wilayahData = [
            // Provinsi
            ['11', 'Aceh'],
            ['12', 'Sumatera Utara'],
            ['13', 'Sumatera Barat'],
            ['14', '<PERSON>ia<PERSON>'],
            ['15', '<PERSON><PERSON>'],
            ['16', '<PERSON><PERSON><PERSON> Selat<PERSON>'],
            ['17', '<PERSON><PERSON><PERSON><PERSON>'],
            ['18', '<PERSON>pung'],
            ['19', '<PERSON>pulauan Bangka Belitung'],
            ['21', 'Kepulauan Riau'],
            ['31', 'DKI Jakarta'],
            ['32', 'Jawa Barat'],
            ['33', 'Jawa Tengah'],
            ['34', 'DI Yogyakarta'],
            ['35', 'Jawa Timur'],
            ['36', 'Banten'],
            ['51', 'Bali'],
            ['52', 'Nusa Tenggara Barat'],
            ['53', 'Nusa Tenggara Timur'],
            ['61', 'Kalimantan Barat'],
            ['62', 'Kalimantan Tengah'],
            ['63', 'Kalimantan Selatan'],
            ['64', 'Kalimantan Timur'],
            ['65', 'Kalimantan Utara'],
            ['71', 'Sulawesi Utara'],
            ['72', 'Sulawesi Tengah'],
            ['73', 'Sulawesi Selatan'],
            ['74', 'Sulawesi Tenggara'],
            ['75', 'Gorontalo'],
            ['76', 'Sulawesi Barat'],
            ['81', 'Maluku'],
            ['82', 'Maluku Utara'],
            ['91', 'Papua Barat'],
            ['94', 'Papua'],
            ['95', 'Papua Tengah'],
            ['96', 'Papua Barat Daya'],

            // Kota/Kabupaten Riau (14.xx)
            ['14.01', 'Kabupaten Kuantan Singingi'],
            ['14.02', 'Kabupaten Indragiri Hulu'],
            ['14.03', 'Kabupaten Indragiri Hilir'],
            ['14.04', 'Kabupaten Pelalawan'],
            ['14.05', 'Kabupaten Siak'],
            ['14.06', 'Kabupaten Kampar'],
            ['14.07', 'Kabupaten Rokan Hulu'],
            ['14.08', 'Kabupaten Bengkalis'],
            ['14.09', 'Kabupaten Rokan Hilir'],
            ['14.10', 'Kabupaten Kepulauan Meranti'],
            ['14.71', 'Kota Pekanbaru'],
            ['14.72', 'Kota Dumai'],
        ];

        $this->seedProvinces();
        $this->seedCities();
        $this->seedRiauDistricts();
        $this->seedRiauVillages();
    }

    private function seedProvinces()
    {
        $this->command->info('Seeding provinces...');

        $provinces = [
            ['code' => '11', 'name' => 'Aceh'],
            ['code' => '12', 'name' => 'Sumatera Utara'],
            ['code' => '13', 'name' => 'Sumatera Barat'],
            ['code' => '14', 'name' => 'Riau'],
            ['code' => '15', 'name' => 'Jambi'],
            ['code' => '16', 'name' => 'Sumatera Selatan'],
            ['code' => '17', 'name' => 'Bengkulu'],
            ['code' => '18', 'name' => 'Lampung'],
            ['code' => '19', 'name' => 'Kepulauan Bangka Belitung'],
            ['code' => '21', 'name' => 'Kepulauan Riau'],
            ['code' => '31', 'name' => 'DKI Jakarta'],
            ['code' => '32', 'name' => 'Jawa Barat'],
            ['code' => '33', 'name' => 'Jawa Tengah'],
            ['code' => '34', 'name' => 'DI Yogyakarta'],
            ['code' => '35', 'name' => 'Jawa Timur'],
            ['code' => '36', 'name' => 'Banten'],
            ['code' => '51', 'name' => 'Bali'],
            ['code' => '52', 'name' => 'Nusa Tenggara Barat'],
            ['code' => '53', 'name' => 'Nusa Tenggara Timur'],
            ['code' => '61', 'name' => 'Kalimantan Barat'],
            ['code' => '62', 'name' => 'Kalimantan Tengah'],
            ['code' => '63', 'name' => 'Kalimantan Selatan'],
            ['code' => '64', 'name' => 'Kalimantan Timur'],
            ['code' => '65', 'name' => 'Kalimantan Utara'],
            ['code' => '71', 'name' => 'Sulawesi Utara'],
            ['code' => '72', 'name' => 'Sulawesi Tengah'],
            ['code' => '73', 'name' => 'Sulawesi Selatan'],
            ['code' => '74', 'name' => 'Sulawesi Tenggara'],
            ['code' => '75', 'name' => 'Gorontalo'],
            ['code' => '76', 'name' => 'Sulawesi Barat'],
            ['code' => '81', 'name' => 'Maluku'],
            ['code' => '82', 'name' => 'Maluku Utara'],
            ['code' => '91', 'name' => 'Papua Barat'],
            ['code' => '94', 'name' => 'Papua'],
        ];

        foreach ($provinces as $province) {
            Province::create($province);
        }

        $this->command->info('Provinces seeded: ' . count($provinces));
    }

    private function seedCities()
    {
        $this->command->info('Seeding cities for Riau...');

        $riauProvince = Province::where('code', '14')->first();
        if (!$riauProvince) {
            $this->command->error('Riau province not found!');
            return;
        }

        $cities = [
            ['province_id' => $riauProvince->id, 'code' => '1401', 'name' => 'Kuantan Singingi', 'type' => 'kabupaten', 'slug' => 'kabupaten-kuantan-singingi'],
            ['province_id' => $riauProvince->id, 'code' => '1402', 'name' => 'Indragiri Hulu', 'type' => 'kabupaten', 'slug' => 'kabupaten-indragiri-hulu'],
            ['province_id' => $riauProvince->id, 'code' => '1403', 'name' => 'Indragiri Hilir', 'type' => 'kabupaten', 'slug' => 'kabupaten-indragiri-hilir'],
            ['province_id' => $riauProvince->id, 'code' => '1404', 'name' => 'Pelalawan', 'type' => 'kabupaten', 'slug' => 'kabupaten-pelalawan'],
            ['province_id' => $riauProvince->id, 'code' => '1405', 'name' => 'Siak', 'type' => 'kabupaten', 'slug' => 'kabupaten-siak'],
            ['province_id' => $riauProvince->id, 'code' => '1406', 'name' => 'Kampar', 'type' => 'kabupaten', 'slug' => 'kabupaten-kampar'],
            ['province_id' => $riauProvince->id, 'code' => '1407', 'name' => 'Rokan Hulu', 'type' => 'kabupaten', 'slug' => 'kabupaten-rokan-hulu'],
            ['province_id' => $riauProvince->id, 'code' => '1408', 'name' => 'Bengkalis', 'type' => 'kabupaten', 'slug' => 'kabupaten-bengkalis'],
            ['province_id' => $riauProvince->id, 'code' => '1409', 'name' => 'Rokan Hilir', 'type' => 'kabupaten', 'slug' => 'kabupaten-rokan-hilir'],
            ['province_id' => $riauProvince->id, 'code' => '1410', 'name' => 'Kepulauan Meranti', 'type' => 'kabupaten', 'slug' => 'kabupaten-kepulauan-meranti'],
            ['province_id' => $riauProvince->id, 'code' => '1471', 'name' => 'Pekanbaru', 'type' => 'kota', 'slug' => 'kota-pekanbaru'],
            ['province_id' => $riauProvince->id, 'code' => '1472', 'name' => 'Dumai', 'type' => 'kota', 'slug' => 'kota-dumai'],
        ];

        foreach ($cities as $city) {
            City::create($city);
        }

        $this->command->info('Riau cities seeded: ' . count($cities));
    }

    private function seedRiauDistricts()
    {
        $this->command->info('Seeding Riau districts...');

        // Kota Pekanbaru - 12 Kecamatan
        $pekanbaruCity = City::where('code', '1471')->first();
        if ($pekanbaruCity) {
            $districts = [
                ['province_id' => $pekanbaruCity->province_id, 'city_id' => $pekanbaruCity->id, 'code' => '147101', 'name' => 'Sukajadi'],
                ['province_id' => $pekanbaruCity->province_id, 'city_id' => $pekanbaruCity->id, 'code' => '147102', 'name' => 'Pekanbaru Kota'],
                ['province_id' => $pekanbaruCity->province_id, 'city_id' => $pekanbaruCity->id, 'code' => '147103', 'name' => 'Sail'],
                ['province_id' => $pekanbaruCity->province_id, 'city_id' => $pekanbaruCity->id, 'code' => '147104', 'name' => 'Lima Puluh'],
                ['province_id' => $pekanbaruCity->province_id, 'city_id' => $pekanbaruCity->id, 'code' => '147105', 'name' => 'Senapelan'],
                ['province_id' => $pekanbaruCity->province_id, 'city_id' => $pekanbaruCity->id, 'code' => '147106', 'name' => 'Rumbai'],
                ['province_id' => $pekanbaruCity->province_id, 'city_id' => $pekanbaruCity->id, 'code' => '147107', 'name' => 'Rumbai Pesisir'],
                ['province_id' => $pekanbaruCity->province_id, 'city_id' => $pekanbaruCity->id, 'code' => '147108', 'name' => 'Bukit Raya'],
                ['province_id' => $pekanbaruCity->province_id, 'city_id' => $pekanbaruCity->id, 'code' => '147109', 'name' => 'Marpoyan Damai'],
                ['province_id' => $pekanbaruCity->province_id, 'city_id' => $pekanbaruCity->id, 'code' => '147110', 'name' => 'Tenayan Raya'],
                ['province_id' => $pekanbaruCity->province_id, 'city_id' => $pekanbaruCity->id, 'code' => '147111', 'name' => 'Payung Sekaki'],
                ['province_id' => $pekanbaruCity->province_id, 'city_id' => $pekanbaruCity->id, 'code' => '147112', 'name' => 'Tampan'],
            ];
            foreach ($districts as $district) {
                District::create($district);
            }
        }

        // Kota Dumai - 7 Kecamatan
        $dumaiCity = City::where('code', '1472')->first();
        if ($dumaiCity) {
            $districts = [
                ['province_id' => $dumaiCity->province_id, 'city_id' => $dumaiCity->id, 'code' => '147201', 'name' => 'Dumai Timur'],
                ['province_id' => $dumaiCity->province_id, 'city_id' => $dumaiCity->id, 'code' => '147202', 'name' => 'Dumai Barat'],
                ['province_id' => $dumaiCity->province_id, 'city_id' => $dumaiCity->id, 'code' => '147203', 'name' => 'Dumai Utara'],
                ['province_id' => $dumaiCity->province_id, 'city_id' => $dumaiCity->id, 'code' => '147204', 'name' => 'Dumai Selatan'],
                ['province_id' => $dumaiCity->province_id, 'city_id' => $dumaiCity->id, 'code' => '147205', 'name' => 'Medang Kampai'],
                ['province_id' => $dumaiCity->province_id, 'city_id' => $dumaiCity->id, 'code' => '147206', 'name' => 'Bukit Kapur'],
                ['province_id' => $dumaiCity->province_id, 'city_id' => $dumaiCity->id, 'code' => '147207', 'name' => 'Sungai Sembilan'],
            ];
            foreach ($districts as $district) {
                District::create($district);
            }
        }

        $this->command->info('Riau districts seeded');
    }

    private function seedRiauVillages()
    {
        $this->command->info('Seeding Riau villages...');

        // Sample villages for Sukajadi, Pekanbaru
        $sukajadi = District::where('code', '147101')->first();
        if ($sukajadi) {
            $villages = [
                ['province_id' => $sukajadi->province_id, 'city_id' => $sukajadi->city_id, 'district_id' => $sukajadi->id, 'code' => '1471011001', 'name' => 'Kampung Melayu', 'type' => 'kelurahan', 'postal_code' => '28156'],
                ['province_id' => $sukajadi->province_id, 'city_id' => $sukajadi->city_id, 'district_id' => $sukajadi->id, 'code' => '1471011002', 'name' => 'Sukajadi', 'type' => 'kelurahan', 'postal_code' => '28122'],
                ['province_id' => $sukajadi->province_id, 'city_id' => $sukajadi->city_id, 'district_id' => $sukajadi->id, 'code' => '1471011003', 'name' => 'Kampung Tengah', 'type' => 'kelurahan', 'postal_code' => '28156'],
                ['province_id' => $sukajadi->province_id, 'city_id' => $sukajadi->city_id, 'district_id' => $sukajadi->id, 'code' => '1471011004', 'name' => 'Jadirejo', 'type' => 'kelurahan', 'postal_code' => '28122'],
            ];
            foreach ($villages as $village) {
                Village::create($village);
            }
        }

        // Sample villages for Tampan, Pekanbaru
        $tampan = District::where('code', '147112')->first();
        if ($tampan) {
            $villages = [
                ['province_id' => $tampan->province_id, 'city_id' => $tampan->city_id, 'district_id' => $tampan->id, 'code' => '1471121001', 'name' => 'Simpang Baru', 'type' => 'kelurahan', 'postal_code' => '28293'],
                ['province_id' => $tampan->province_id, 'city_id' => $tampan->city_id, 'district_id' => $tampan->id, 'code' => '1471121002', 'name' => 'Tuah Karya', 'type' => 'kelurahan', 'postal_code' => '28293'],
                ['province_id' => $tampan->province_id, 'city_id' => $tampan->city_id, 'district_id' => $tampan->id, 'code' => '1471121003', 'name' => 'Delima', 'type' => 'kelurahan', 'postal_code' => '28291'],
                ['province_id' => $tampan->province_id, 'city_id' => $tampan->city_id, 'district_id' => $tampan->id, 'code' => '1471121004', 'name' => 'Tampan', 'type' => 'kelurahan', 'postal_code' => '28293'],
                ['province_id' => $tampan->province_id, 'city_id' => $tampan->city_id, 'district_id' => $tampan->id, 'code' => '1471121005', 'name' => 'Sidomulyo Barat', 'type' => 'kelurahan', 'postal_code' => '28291'],
                ['province_id' => $tampan->province_id, 'city_id' => $tampan->city_id, 'district_id' => $tampan->id, 'code' => '1471121006', 'name' => 'Tuah Madani', 'type' => 'kelurahan', 'postal_code' => '28293'],
            ];
            foreach ($villages as $village) {
                Village::create($village);
            }
        }

        $this->command->info('Riau villages seeded');
    }
}
