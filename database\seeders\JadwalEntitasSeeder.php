<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class JadwalEntitasSeeder extends Seeder
{
    /**
     * Helper method to output info if command is available
     */
    private function info(string $message): void
    {
        if ($this->command) {
            $this->command->info($message);
        }
    }

    /**
     * Helper method to output warning if command is available
     */
    private function warn(string $message): void
    {
        if ($this->command) {
            $this->command->warn($message);
        }
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->info('🚀 Starting Jadwal Entitas Seeder...');

        // Update jadwal_kerja with entitas_id based on karyawan's entitas
        $this->updateJadwalKerjaEntitas();

        // Update jadwal_masal with entitas_id (set to first entitas or based on logic)
        $this->updateJadwalMasalEntitas();

        $this->info('✅ Jadwal Entitas Seeder completed successfully!');
    }

    /**
     * Update jadwal_kerja table with entitas_id from karyawan
     */
    private function updateJadwalKerjaEntitas(): void
    {
        $this->info('📅 Updating jadwal_kerja with entitas_id...');

        // Get all schedules that don't have entitas_id set
        $schedulesWithoutEntitas = DB::table('jadwal_kerja')
            ->whereNull('entitas_id')
            ->count();

        if ($schedulesWithoutEntitas === 0) {
            $this->info('   ✓ All jadwal_kerja records already have entitas_id set');
            return;
        }

        $this->info("   📊 Found {$schedulesWithoutEntitas} schedules without entitas_id");

        // Update schedules with entitas_id from their karyawan
        $updatedCount = DB::table('jadwal_kerja as jk')
            ->join('karyawan as k', 'jk.karyawan_id', '=', 'k.id')
            ->whereNull('jk.entitas_id')
            ->whereNotNull('k.id_entitas')
            ->update(['jk.entitas_id' => DB::raw('k.id_entitas')]);

        $this->info("   ✅ Updated {$updatedCount} jadwal_kerja records with entitas_id from karyawan");

        // Handle schedules where karyawan doesn't have entitas
        $schedulesWithoutKaryawanEntitas = DB::table('jadwal_kerja as jk')
            ->join('karyawan as k', 'jk.karyawan_id', '=', 'k.id')
            ->whereNull('jk.entitas_id')
            ->whereNull('k.id_entitas')
            ->count();

        if ($schedulesWithoutKaryawanEntitas > 0) {
            $this->warn("   ⚠️  Found {$schedulesWithoutKaryawanEntitas} schedules where karyawan has no entitas");

            // Get first available entitas as fallback
            $firstEntitas = DB::table('entitas')->first();

            if ($firstEntitas) {
                $fallbackUpdated = DB::table('jadwal_kerja as jk')
                    ->join('karyawan as k', 'jk.karyawan_id', '=', 'k.id')
                    ->whereNull('jk.entitas_id')
                    ->whereNull('k.id_entitas')
                    ->update(['jk.entitas_id' => $firstEntitas->id]);

                $this->info("   ✅ Assigned {$fallbackUpdated} schedules to default entitas: {$firstEntitas->nama}");
            } else {
                $this->warn("   ⚠️  No entitas available to assign as fallback");
            }
        }

        // Handle orphaned schedules (karyawan doesn't exist)
        $orphanedSchedules = DB::table('jadwal_kerja as jk')
            ->leftJoin('karyawan as k', 'jk.karyawan_id', '=', 'k.id')
            ->whereNull('jk.entitas_id')
            ->whereNull('k.id')
            ->count();

        if ($orphanedSchedules > 0) {
            $this->warn("   ⚠️  Found {$orphanedSchedules} orphaned schedules (karyawan not found)");

            $firstEntitas = DB::table('entitas')->first();
            if ($firstEntitas) {
                $orphanedUpdated = DB::table('jadwal_kerja as jk')
                    ->leftJoin('karyawan as k', 'jk.karyawan_id', '=', 'k.id')
                    ->whereNull('jk.entitas_id')
                    ->whereNull('k.id')
                    ->update(['jk.entitas_id' => $firstEntitas->id]);

                $this->info("   ✅ Assigned {$orphanedUpdated} orphaned schedules to default entitas: {$firstEntitas->nama}");
            } else {
                $this->warn("   ⚠️  No entitas available to assign to orphaned schedules");
            }
        }
    }

    /**
     * Update jadwal_masal table with entitas_id
     */
    private function updateJadwalMasalEntitas(): void
    {
        $this->info('📋 Updating jadwal_masal with entitas_id...');

        // Get all bulk schedules that don't have entitas_id set
        $jadwalMasalWithoutEntitas = DB::table('jadwal_masal')
            ->whereNull('entitas_id')
            ->count();

        if ($jadwalMasalWithoutEntitas === 0) {
            $this->info('   ✓ All jadwal_masal records already have entitas_id set');
            return;
        }

        $this->info("   📊 Found {$jadwalMasalWithoutEntitas} bulk schedules without entitas_id");

        // Strategy 1: Try to determine entitas from assigned karyawan
        $jadwalMasalRecords = DB::table('jadwal_masal')
            ->whereNull('entitas_id')
            ->get();

        foreach ($jadwalMasalRecords as $jadwalMasal) {
            // Get most common entitas among assigned karyawan
            $mostCommonEntitas = DB::table('jadwal_masal_karyawan as jmk')
                ->join('karyawan as k', 'jmk.karyawan_id', '=', 'k.id')
                ->where('jmk.jadwal_masal_id', $jadwalMasal->id)
                ->whereNotNull('k.id_entitas')
                ->select('k.id_entitas', DB::raw('COUNT(*) as count'))
                ->groupBy('k.id_entitas')
                ->orderBy('count', 'desc')
                ->first();

            if ($mostCommonEntitas) {
                DB::table('jadwal_masal')
                    ->where('id', $jadwalMasal->id)
                    ->update(['entitas_id' => $mostCommonEntitas->id_entitas]);

                $entitasName = DB::table('entitas')
                    ->where('id', $mostCommonEntitas->id_entitas)
                    ->value('nama');

                $this->info("   ✅ Assigned jadwal_masal '{$jadwalMasal->nama_jadwal}' to entitas: {$entitasName}");
            }
        }

        // Strategy 2: Assign remaining bulk schedules to first available entitas
        $remainingJadwalMasal = DB::table('jadwal_masal')
            ->whereNull('entitas_id')
            ->count();

        if ($remainingJadwalMasal > 0) {
            $firstEntitas = DB::table('entitas')->first();
            if ($firstEntitas) {
                $remainingUpdated = DB::table('jadwal_masal')
                    ->whereNull('entitas_id')
                    ->update(['entitas_id' => $firstEntitas->id]);

                $this->info("   ✅ Assigned {$remainingUpdated} remaining bulk schedules to default entitas: {$firstEntitas->nama}");
            } else {
                $this->warn("   ⚠️  No entitas available to assign to remaining bulk schedules");
            }
        }
    }
}
