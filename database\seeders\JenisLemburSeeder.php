<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\JenisLembur;
use App\Models\AturanLembur;

class JenisLemburSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Hapus data lama dengan cara yang aman
        AturanLembur::query()->delete();
        JenisLembur::query()->delete();

        // 1. Lembur Hari Biasa (Per Jam)
        $lemburHariBiasa = JenisLembur::create([
            'nama_jenis' => 'Lembur Hari Biasa',
            'tipe_perhitungan' => 'per_jam',
            'pembagi_upah_bulanan' => 30,
            'keterangan' => 'Lembur di hari kerja biasa (Senin-Jumat)',
            'is_active' => true,
            'urutan' => 1,
        ]);

        // Aturan untuk Lembur Hari Biasa
        AturanLembur::create([
            'jenis_lembur_id' => $lemburHariBiasa->id,
            'jam_mulai' => 1.00,
            'jam_selesai' => 1.00,
            'multiplier' => 1.50,
            'keterangan' => 'Jam pertama dengan multiplier 1.5x',
            'is_active' => true,
            'urutan' => 1,
        ]);

        AturanLembur::create([
            'jenis_lembur_id' => $lemburHariBiasa->id,
            'jam_mulai' => 2.00,
            'jam_selesai' => null, // Jam 2 dan seterusnya
            'multiplier' => 2.00,
            'keterangan' => 'Jam kedua dan seterusnya dengan multiplier 2x',
            'is_active' => true,
            'urutan' => 2,
        ]);

        // 2. Lembur Hari Libur (Per Jam)
        $lemburHariLibur = JenisLembur::create([
            'nama_jenis' => 'Lembur Hari Libur',
            'tipe_perhitungan' => 'per_jam',
            'pembagi_upah_bulanan' => 30,
            'keterangan' => 'Lembur di hari libur (Sabtu-Minggu)',
            'is_active' => true,
            'urutan' => 2,
        ]);

        // Aturan untuk Lembur Hari Libur
        AturanLembur::create([
            'jenis_lembur_id' => $lemburHariLibur->id,
            'jam_mulai' => 1.00,
            'jam_selesai' => 8.00,
            'multiplier' => 2.00,
            'keterangan' => 'Jam 1-8 dengan multiplier 2x',
            'is_active' => true,
            'urutan' => 1,
        ]);

        AturanLembur::create([
            'jenis_lembur_id' => $lemburHariLibur->id,
            'jam_mulai' => 9.00,
            'jam_selesai' => 9.00,
            'multiplier' => 3.00,
            'keterangan' => 'Jam ke-9 dengan multiplier 3x',
            'is_active' => true,
            'urutan' => 2,
        ]);

        AturanLembur::create([
            'jenis_lembur_id' => $lemburHariLibur->id,
            'jam_mulai' => 10.00,
            'jam_selesai' => 11.00,
            'multiplier' => 4.00,
            'keterangan' => 'Jam 10-11 dengan multiplier 4x',
            'is_active' => true,
            'urutan' => 3,
        ]);

        // 3. Lembur HK Hari Kerja (Per Hari)
        $lemburHKHariKerja = JenisLembur::create([
            'nama_jenis' => 'Lembur HK (Hari Kerja)',
            'tipe_perhitungan' => 'per_hari',
            'pembagi_upah_bulanan' => 26,
            'keterangan' => 'Lembur HK di hari kerja dengan perhitungan per hari',
            'is_active' => true,
            'urutan' => 3,
        ]);

        AturanLembur::create([
            'jenis_lembur_id' => $lemburHKHariKerja->id,
            'jam_mulai' => null,
            'jam_selesai' => null,
            'multiplier' => 1.00,
            'keterangan' => 'Multiplier 1x upah harian (upah bulanan ÷ 26)',
            'is_active' => true,
            'urutan' => 1,
        ]);

        // 4. Lembur HK Tanggal Merah (Per Hari)
        $lemburHKTanggalMerah = JenisLembur::create([
            'nama_jenis' => 'Lembur HK (Tanggal Merah)',
            'tipe_perhitungan' => 'per_hari',
            'pembagi_upah_bulanan' => 26,
            'keterangan' => 'Lembur HK di tanggal merah dengan perhitungan per hari',
            'is_active' => true,
            'urutan' => 4,
        ]);

        AturanLembur::create([
            'jenis_lembur_id' => $lemburHKTanggalMerah->id,
            'jam_mulai' => null,
            'jam_selesai' => null,
            'multiplier' => 2.00,
            'keterangan' => 'Multiplier 2x upah harian (upah bulanan ÷ 26)',
            'is_active' => true,
            'urutan' => 1,
        ]);

        $this->command->info('Seeder JenisLembur dan AturanLembur berhasil dijalankan!');
        $this->command->info('Total jenis lembur: 4');
        $this->command->info('Total aturan lembur: 7');
    }
}
