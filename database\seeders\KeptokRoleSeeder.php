<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class KeptokRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting Keptok (Kepala Toko) Role Seeder...');

        // Add keptok role to reference table
        $this->addKeptokRoleToReference();

        // Create keptok users if they don't exist
        $this->createKeptokUsers();

        $this->command->info('✅ Keptok Role Seeder completed successfully!');
    }

    /**
     * Add keptok role to reference table
     */
    private function addKeptokRoleToReference(): void
    {
        // Check if user_role reference table exists
        if (DB::getSchemaBuilder()->hasTable('user_role')) {
            $existingRole = DB::table('user_role')->where('kode', 'keptok')->first();
            
            if (!$existingRole) {
                DB::table('user_role')->insert([
                    'kode' => 'keptok',
                    'nama' => 'Kepala Toko',
                    'deskripsi' => 'Role kepala toko',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $this->command->info('   ✓ Added keptok role to user_role reference table');
            } else {
                $this->command->info('   ⚠ Keptok role already exists in user_role reference table');
            }
        } else {
            $this->command->info('   ⚠ user_role reference table not found, skipping...');
        }
    }

    /**
     * Create keptok users
     */
    private function createKeptokUsers(): void
    {
        $keptoks = [
            [
                'name' => 'Kepala Toko Pusat',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'keptok',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Kepala Toko Cabang A',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'keptok',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Kepala Toko Cabang B',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'keptok',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Kepala Toko Cabang C',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'keptok',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Kepala Toko Online',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'keptok',
                'email_verified_at' => now(),
            ],
        ];

        $createdCount = 0;
        $existingCount = 0;

        foreach ($keptoks as $keptokData) {
            $existingUser = User::where('email', $keptokData['email'])->first();
            
            if (!$existingUser) {
                User::create($keptokData);
                $createdCount++;
                $this->command->info("   ✓ Created keptok: {$keptokData['name']} ({$keptokData['email']})");
            } else {
                // Update existing user to keptok role if different
                if ($existingUser->role !== 'keptok') {
                    $existingUser->update(['role' => 'keptok']);
                    $this->command->info("   ↻ Updated {$existingUser->name} role to keptok");
                } else {
                    $existingCount++;
                    $this->command->info("   ⚠ Keptok already exists: {$keptokData['name']} ({$keptokData['email']})");
                }
            }
        }

        $this->command->info("   📊 Summary: {$createdCount} created, {$existingCount} already existed");
    }
}
