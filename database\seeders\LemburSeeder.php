<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Lembur;
use App\Models\Karyawan;
use App\Models\User;
use Carbon\Carbon;

class LemburSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ambil beberapa karyawan untuk contoh data
        $karyawans = Karyawan::limit(10)->get();
        $admin = User::where('role', 'admin')->first() ?? User::first();

        if ($karyawans->isEmpty()) {
            $this->command->info('Tidak ada data karyawan. Silakan jalankan seeder karyawan terlebih dahulu.');
            return;
        }

        $lemburData = [];
        
        // Generate data lembur untuk 3 bulan terakhir
        for ($i = 0; $i < 30; $i++) {
            $karyawan = $karyawans->random();
            $tanggal = Carbon::now()->subDays(rand(1, 90));
            
            // Skip weekend untuk data yang lebih realistis
            if ($tanggal->isWeekend()) {
                continue;
            }

            $lemburData[] = [
                'karyawan_id' => $karyawan->id,
                'tanggal' => $tanggal->format('Y-m-d'),
                'jumlah_jam' => rand(1, 8) + (rand(0, 1) * 0.5), // 1-8.5 jam
                'deskripsi' => $this->getRandomDescription(),
                'created_by' => $admin->id,
                'created_at' => $tanggal->addHours(rand(17, 22)), // Input setelah jam kerja
                'updated_at' => $tanggal,
            ];
        }

        foreach ($lemburData as $data) {
            Lembur::create($data);
        }

        $this->command->info('Data lembur berhasil ditambahkan: ' . count($lemburData) . ' records');
    }

    private function getRandomDescription(): string
    {
        $descriptions = [
            'Menyelesaikan laporan bulanan yang harus diserahkan besok pagi',
            'Membantu persiapan presentasi untuk klien besar',
            'Mengatasi masalah sistem yang urgent',
            'Menyelesaikan proyek yang deadline-nya mepet',
            'Membantu tim lain yang kekurangan tenaga',
            'Melakukan maintenance sistem di luar jam kerja',
            'Menyelesaikan audit internal yang mendadak',
            'Mempersiapkan dokumen untuk meeting penting',
            'Mengatasi komplain pelanggan yang harus segera ditangani',
            'Menyelesaikan inventory stock opname',
            'Membantu training karyawan baru',
            'Menyelesaikan rekonsiliasi data keuangan',
            'Mempersiapkan proposal tender yang urgent',
            'Mengatasi masalah teknis di lapangan',
            'Menyelesaikan laporan untuk manajemen pusat',
        ];

        return $descriptions[array_rand($descriptions)];
    }
}
