<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\LoyaltyProgram;

class LoyaltyProgramSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding loyalty programs...');

        // Default Loyalty Program
        LoyaltyProgram::create([
            'name' => 'Program Loyalitas Utama',
            'description' => 'Program loyalitas standar untuk semua customer dengan sistem poin yang dapat ditukar dengan diskon.',
            'type' => 'points',
            'is_active' => true,

            // Points Configuration
            'points_per_amount' => 1000, // 1 poin per 1000 rupiah
            'points_multiplier' => 1.0,
            'minimum_transaction' => 10000, // Minimum 10k untuk dapat poin
            'max_points_per_transaction' => 1000,

            // Redemption Configuration
            'redemption_rate' => 100, // 100 poin = 1000 rupiah
            'minimum_redemption' => 50,

            // Time-based Multipliers
            'time_multipliers' => [
                'happy_hour' => [
                    'start' => '15:00',
                    'end' => '17:00',
                    'multiplier' => 1.5
                ],
                'late_night' => [
                    'start' => '21:00',
                    'end' => '23:59',
                    'multiplier' => 1.3
                ]
            ],

            // Day-based Multipliers
            'day_multipliers' => [
                'monday' => 1.0,
                'tuesday' => 1.0,
                'wednesday' => 1.0,
                'thursday' => 1.0,
                'friday' => 1.2, // 20% bonus on Friday
                'saturday' => 2.0, // Double points on Saturday
                'sunday' => 2.0, // Double points on Sunday
            ],

            // Customer Tier Multipliers
            'tier_multipliers' => [
                'bronze' => [
                    'min_points' => 0,
                    'multiplier' => 1.0
                ],
                'silver' => [
                    'min_points' => 1000,
                    'multiplier' => 1.1
                ],
                'gold' => [
                    'min_points' => 5000,
                    'multiplier' => 1.2
                ],
                'platinum' => [
                    'min_points' => 10000,
                    'multiplier' => 1.5
                ],
                'diamond' => [
                    'min_points' => 25000,
                    'multiplier' => 2.0
                ]
            ],

            // Special Bonuses
            'birthday_bonus_points' => 100,
            'vip_bonus_multiplier' => 1.5,

            // Validity
            'start_date' => now(),
            'end_date' => null, // No end date
            'points_expiry_months' => 12,

            'created_by' => 1, // Admin user
        ]);

        // Weekend Special Program
        LoyaltyProgram::create([
            'name' => 'Weekend Special',
            'description' => 'Program khusus weekend dengan poin triple untuk semua transaksi.',
            'type' => 'points',
            'is_active' => false, // Inactive by default

            // Points Configuration
            'points_per_amount' => 500, // 1 poin per 500 rupiah (lebih generous)
            'points_multiplier' => 3.0, // Triple points
            'minimum_transaction' => 5000, // Lower minimum
            'max_points_per_transaction' => 2000, // Higher max

            // Redemption Configuration
            'redemption_rate' => 80, // Better redemption rate
            'minimum_redemption' => 30,

            // Day-based Multipliers (Weekend only)
            'day_multipliers' => [
                'monday' => 0.0, // No points on weekdays
                'tuesday' => 0.0,
                'wednesday' => 0.0,
                'thursday' => 0.0,
                'friday' => 0.0,
                'saturday' => 1.0,
                'sunday' => 1.0,
            ],

            // Special Bonuses
            'birthday_bonus_points' => 200,
            'vip_bonus_multiplier' => 2.0,

            // Validity
            'start_date' => now()->addWeek(),
            'end_date' => now()->addMonth(),
            'points_expiry_months' => 6,

            'created_by' => 1,
        ]);

        // VIP Exclusive Program
        LoyaltyProgram::create([
            'name' => 'VIP Exclusive',
            'description' => 'Program eksklusif untuk customer VIP dengan benefit maksimal.',
            'type' => 'points',
            'is_active' => false,

            // Points Configuration
            'points_per_amount' => 500, // 1 poin per 500 rupiah
            'points_multiplier' => 2.0,
            'minimum_transaction' => 1000, // Very low minimum
            'max_points_per_transaction' => 5000, // Very high max

            // Redemption Configuration
            'redemption_rate' => 50, // Excellent redemption rate
            'minimum_redemption' => 10,

            // Time-based Multipliers (All day bonus)
            'time_multipliers' => [
                'all_day' => [
                    'start' => '00:00',
                    'end' => '23:59',
                    'multiplier' => 1.5
                ]
            ],

            // Day-based Multipliers (Every day is special)
            'day_multipliers' => [
                'monday' => 1.5,
                'tuesday' => 1.5,
                'wednesday' => 1.5,
                'thursday' => 1.5,
                'friday' => 2.0,
                'saturday' => 3.0,
                'sunday' => 3.0,
            ],

            // Special Bonuses
            'birthday_bonus_points' => 500,
            'vip_bonus_multiplier' => 3.0,

            // Validity
            'start_date' => now(),
            'end_date' => null,
            'points_expiry_months' => 24, // Longer expiry

            'created_by' => 1,
        ]);

        $this->command->info('Loyalty programs seeded successfully!');
    }
}
