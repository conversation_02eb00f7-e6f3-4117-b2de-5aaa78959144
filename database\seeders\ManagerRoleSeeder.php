<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class ManagerRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting Manager Role Seeder...');

        // Add manager role to reference table
        $this->addManagerRoleToReference();

        // Create manager users if they don't exist
        $this->createManagerUsers();

        $this->command->info('✅ Manager Role Seeder completed successfully!');
    }

    /**
     * Add manager role to reference table
     */
    private function addManagerRoleToReference(): void
    {
        // Check if user_role reference table exists
        if (DB::getSchemaBuilder()->hasTable('user_role')) {
            $existingRole = DB::table('user_role')->where('kode', 'manager')->first();

            if (!$existingRole) {
                DB::table('user_role')->insert([
                    'kode' => 'manager',
                    'nama' => 'Manager',
                    'deskripsi' => 'Role manager',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $this->command->info('   ✓ Added manager role to user_role reference table');
            } else {
                $this->command->info('   ⚠ Manager role already exists in user_role reference table');
            }
        } else {
            $this->command->info('   ⚠ user_role reference table not found, skipping...');
        }
    }

    /**
     * Create manager users
     */
    private function createManagerUsers(): void
    {
        $managers = [
            [
                'name' => 'Manager HRD',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'manager',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Manager Operasional',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'manager',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Manager Keuangan',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'manager',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Manager IT',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'manager',
                'email_verified_at' => now(),
            ],
        ];

        $createdCount = 0;
        $existingCount = 0;

        foreach ($managers as $managerData) {
            $existingUser = User::where('email', $managerData['email'])->first();

            if (!$existingUser) {
                User::create($managerData);
                $createdCount++;
                $this->command->info("   ✓ Created manager: {$managerData['name']} ({$managerData['email']})");
            } else {
                // Update existing user to manager role if different
                if ($existingUser->role !== 'manager') {
                    $existingUser->update(['role' => 'manager']);
                    $this->command->info("   ↻ Updated {$existingUser->name} role to manager");
                } else {
                    $existingCount++;
                    $this->command->info("   ⚠ Manager already exists: {$managerData['name']} ({$managerData['email']})");
                }
            }
        }

        $this->command->info("   📊 Summary: {$createdCount} created, {$existingCount} already existed");
    }
}
