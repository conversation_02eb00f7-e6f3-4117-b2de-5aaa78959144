<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Quotation;
use App\Models\QuotationItem;
use App\Models\LoyaltyTransaction;
use App\Models\CustomerFeedback;
use App\Models\PosTransaction;
use App\Models\PosTransactionItem;
use App\Models\SalesOrder;
use App\Models\SalesOrderItem;
use App\Models\SalesInvoice;

class MarketingDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Categories
        $categories = [
            ['name' => 'Makanan <PERSON>', 'description' => 'Menu makanan utama seperti nasi, mie, dll'],
            ['name' => 'Minuman', 'description' => 'Berbagai jenis minuman'],
            ['name' => 'Snack & Appetizer', 'description' => 'Makanan ringan dan pembuka'],
            ['name' => 'Dessert', 'description' => 'Makanan penutup'],
            ['name' => 'Elektronik', 'description' => 'Produk elektronik'],
            ['name' => 'Fashion', 'description' => 'Produk fashion dan pakaian'],
        ];

        foreach ($categories as $categoryData) {
            Category::firstOrCreate(['name' => $categoryData['name']], $categoryData);
        }

        // Create Products
        $products = [
            // F&B Products
            ['name' => 'Nasi Goreng Spesial', 'category_id' => 1, 'price' => 25000, 'cost_price' => 15000, 'is_food_item' => true, 'stock_quantity' => 100],
            ['name' => 'Mie Ayam Bakso', 'category_id' => 1, 'price' => 20000, 'cost_price' => 12000, 'is_food_item' => true, 'stock_quantity' => 80],
            ['name' => 'Es Teh Manis', 'category_id' => 2, 'price' => 5000, 'cost_price' => 2000, 'is_food_item' => true, 'stock_quantity' => 200],
            ['name' => 'Jus Jeruk', 'category_id' => 2, 'price' => 12000, 'cost_price' => 7000, 'is_food_item' => true, 'stock_quantity' => 150],
            ['name' => 'Keripik Singkong', 'category_id' => 3, 'price' => 8000, 'cost_price' => 5000, 'is_food_item' => true, 'stock_quantity' => 120],
            ['name' => 'Es Krim Vanilla', 'category_id' => 4, 'price' => 15000, 'cost_price' => 8000, 'is_food_item' => true, 'stock_quantity' => 60],

            // Non-F&B Products
            ['name' => 'Smartphone Samsung A54', 'category_id' => 5, 'price' => 4500000, 'cost_price' => 4000000, 'is_food_item' => false, 'stock_quantity' => 25],
            ['name' => 'Laptop ASUS VivoBook', 'category_id' => 5, 'price' => 8500000, 'cost_price' => 7500000, 'is_food_item' => false, 'stock_quantity' => 15],
            ['name' => 'Kaos Polo Pria', 'category_id' => 6, 'price' => 150000, 'cost_price' => 100000, 'is_food_item' => false, 'stock_quantity' => 50],
            ['name' => 'Celana Jeans Wanita', 'category_id' => 6, 'price' => 250000, 'cost_price' => 180000, 'is_food_item' => false, 'stock_quantity' => 30],
        ];

        foreach ($products as $productData) {
            Product::firstOrCreate(['name' => $productData['name']], $productData);
        }

        // Create Customers
        $customers = [
            ['nama' => 'Budi Santoso', 'email' => '<EMAIL>', 'telepon' => '081234567890', 'jenis_kelamin' => 'L', 'loyalty_points' => 1500],
            ['nama' => 'Siti Nurhaliza', 'email' => '<EMAIL>', 'telepon' => '081234567891', 'jenis_kelamin' => 'P', 'loyalty_points' => 2300],
            ['nama' => 'Ahmad Wijaya', 'email' => '<EMAIL>', 'telepon' => '081234567892', 'jenis_kelamin' => 'L', 'loyalty_points' => 800],
            ['nama' => 'Dewi Sartika', 'email' => '<EMAIL>', 'telepon' => '081234567893', 'jenis_kelamin' => 'P', 'loyalty_points' => 3200],
            ['nama' => 'Rudi Hermawan', 'email' => '<EMAIL>', 'telepon' => '081234567894', 'jenis_kelamin' => 'L', 'loyalty_points' => 650],
        ];

        foreach ($customers as $customerData) {
            Customer::firstOrCreate(['email' => $customerData['email']], $customerData);
        }

        // Create Quotations
        $quotations = [
            [
                'customer_id' => 1,
                'quotation_date' => now()->subDays(5),
                'valid_until' => now()->addDays(25),
                'status' => 'accepted',
                'sub_total' => 50000,
                'total_amount' => 50000,
            ],
            [
                'customer_id' => 2,
                'quotation_date' => now()->subDays(3),
                'valid_until' => now()->addDays(27),
                'status' => 'sent',
                'sub_total' => 4500000,
                'total_amount' => 4500000,
            ],
            [
                'customer_id' => 3,
                'quotation_date' => now()->subDays(1),
                'valid_until' => now()->addDays(29),
                'status' => 'accepted',
                'sub_total' => 170000,
                'total_amount' => 170000,
            ],
        ];

        foreach ($quotations as $quotationData) {
            Quotation::create($quotationData);
        }

        // Create Quotation Items
        $quotationItems = [
            // Quotation 1 items
            ['quotation_id' => 1, 'product_id' => 1, 'quantity' => 2, 'unit_price' => 25000, 'total_price' => 50000],

            // Quotation 2 items
            ['quotation_id' => 2, 'product_id' => 7, 'quantity' => 1, 'unit_price' => 4500000, 'total_price' => 4500000],

            // Quotation 3 items
            ['quotation_id' => 3, 'product_id' => 2, 'quantity' => 1, 'unit_price' => 20000, 'total_price' => 20000],
            ['quotation_id' => 3, 'product_id' => 9, 'quantity' => 1, 'unit_price' => 150000, 'total_price' => 150000],
        ];

        foreach ($quotationItems as $itemData) {
            QuotationItem::create($itemData);
        }

        // Create Loyalty Transactions
        $loyaltyTransactions = [
            ['customer_id' => 1, 'type' => 'earn', 'points' => 500, 'description' => 'Pembelian Nasi Goreng'],
            ['customer_id' => 1, 'type' => 'earn', 'points' => 1000, 'description' => 'Bonus member baru'],
            ['customer_id' => 2, 'type' => 'earn', 'points' => 2300, 'description' => 'Pembelian smartphone'],
            ['customer_id' => 3, 'type' => 'earn', 'points' => 800, 'description' => 'Pembelian fashion'],
            ['customer_id' => 4, 'type' => 'earn', 'points' => 3200, 'description' => 'Pembelian laptop'],
        ];

        foreach ($loyaltyTransactions as $transactionData) {
            LoyaltyTransaction::create($transactionData);
        }

        // Create Customer Feedback
        $feedbacks = [
            [
                'customer_id' => 1,
                'type' => 'saran',
                'subject' => 'Tambah Variasi Menu',
                'description' => 'Mohon ditambahkan menu vegetarian',
                'status' => 'new',
            ],
            [
                'customer_id' => 2,
                'type' => 'keluhan',
                'subject' => 'Pengiriman Terlambat',
                'description' => 'Pengiriman smartphone terlambat 2 hari dari jadwal',
                'status' => 'in_progress',
            ],
            [
                'customer_id' => 3,
                'type' => 'saran',
                'subject' => 'Program Loyalitas',
                'description' => 'Saran untuk program cashback member',
                'status' => 'resolved',
                'resolution_notes' => 'Terima kasih atas sarannya, akan kami pertimbangkan',
            ],
        ];

        foreach ($feedbacks as $feedbackData) {
            CustomerFeedback::create($feedbackData);
        }

        // Get actual IDs for relationships
        $customer1 = Customer::where('email', '<EMAIL>')->first();
        $customer2 = Customer::where('email', '<EMAIL>')->first();
        $firstUser = \App\Models\User::first();
        $product1 = Product::where('name', 'Nasi Goreng Spesial')->first();
        $product2 = Product::where('name', 'Mie Ayam Bakso')->first();
        $product3 = Product::where('name', 'Es Teh Manis')->first();
        $product4 = Product::where('name', 'Jus Jeruk')->first();

        // Create POS Transactions
        $posTransaction1 = PosTransaction::create([
            'customer_id' => $customer1->id,
            'user_id' => $firstUser->id,
            'transaction_date' => now()->subDays(2),
            'total_amount' => 45000,
            'discount_amount' => 0,
            'tax_amount' => 0,
            'net_amount' => 45000,
            'payment_method' => 'cash',
            'amount_paid' => 50000,
            'change_given' => 5000,
            'loyalty_points_earned' => 45,
            'table_number' => 'T01',
        ]);

        $posTransaction2 = PosTransaction::create([
            'customer_id' => $customer2->id,
            'user_id' => $firstUser->id,
            'transaction_date' => now()->subDays(1),
            'total_amount' => 17000,
            'discount_amount' => 2000,
            'tax_amount' => 0,
            'net_amount' => 15000,
            'payment_method' => 'qris',
            'amount_paid' => 15000,
            'change_given' => 0,
            'loyalty_points_earned' => 15,
        ]);

        $posTransaction3 = PosTransaction::create([
            'customer_id' => null, // Walk-in customer
            'user_id' => $firstUser->id,
            'transaction_date' => now(),
            'total_amount' => 25000,
            'discount_amount' => 0,
            'tax_amount' => 0,
            'net_amount' => 25000,
            'payment_method' => 'card',
            'amount_paid' => 25000,
            'change_given' => 0,
            'loyalty_points_earned' => 0,
        ]);

        // Create POS Transaction Items
        PosTransactionItem::create([
            'pos_transaction_id' => $posTransaction1->id,
            'product_id' => $product1->id,
            'quantity' => 1,
            'unit_price' => 25000,
            'discount_per_item' => 0,
            'total_price' => 25000
        ]);

        PosTransactionItem::create([
            'pos_transaction_id' => $posTransaction1->id,
            'product_id' => $product2->id,
            'quantity' => 1,
            'unit_price' => 20000,
            'discount_per_item' => 0,
            'total_price' => 20000
        ]);

        PosTransactionItem::create([
            'pos_transaction_id' => $posTransaction2->id,
            'product_id' => $product3->id,
            'quantity' => 2,
            'unit_price' => 5000,
            'discount_per_item' => 0,
            'total_price' => 10000
        ]);

        PosTransactionItem::create([
            'pos_transaction_id' => $posTransaction2->id,
            'product_id' => $product4->id,
            'quantity' => 1,
            'unit_price' => 12000,
            'discount_per_item' => 2000,
            'total_price' => 10000
        ]);

        PosTransactionItem::create([
            'pos_transaction_id' => $posTransaction3->id,
            'product_id' => $product1->id,
            'quantity' => 1,
            'unit_price' => 25000,
            'discount_per_item' => 0,
            'total_price' => 25000
        ]);

        // Create Sales Orders from Quotations
        $salesOrder1 = SalesOrder::createFromQuotation(Quotation::find(1));
        $salesOrder2 = SalesOrder::createFromQuotation(Quotation::find(3));

        // Create Sales Invoices from Sales Orders
        $invoice1 = SalesInvoice::createFromSalesOrder($salesOrder1);
        $invoice2 = SalesInvoice::createFromSalesOrder($salesOrder2);

        // Mark one invoice as partially paid
        $invoice1->recordPayment(30000, 'Pembayaran sebagian via transfer');

        $this->command->info('Marketing data seeded successfully!');
        $this->command->info('Created:');
        $this->command->info('- ' . Category::count() . ' categories');
        $this->command->info('- ' . Product::count() . ' products');
        $this->command->info('- ' . Customer::count() . ' customers');
        $this->command->info('- ' . Quotation::count() . ' quotations');
        $this->command->info('- ' . PosTransaction::count() . ' POS transactions');
        $this->command->info('- ' . SalesOrder::count() . ' sales orders');
        $this->command->info('- ' . SalesInvoice::count() . ' sales invoices');
    }
}
