<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\Product;
use App\Models\Customer;
use App\Models\PosTransaction;
use App\Models\PosTransactionItem;
use Carbon\Carbon;

class MarketingSampleDataSeeder extends Seeder
{
    public function run()
    {
        // Create Categories
        $categories = [
            ['name' => 'Makanan <PERSON>', 'description' => 'Nasi, mie, dan makanan utama lainnya'],
            ['name' => 'Minuman', 'description' => 'Minuman panas dan dingin'],
            ['name' => 'Snack', 'description' => 'Makanan ringan dan cemilan'],
            ['name' => 'Dessert', 'description' => 'Makanan penutup']
        ];

        foreach ($categories as $cat) {
            Category::firstOrCreate(['name' => $cat['name']], $cat);
        }

        // Create Products
        $products = [
            // Makanan <PERSON>
            ['name' => 'Nasi <PERSON>', 'sku' => 'FD001', 'price' => 25000, 'cost_price' => 15000, 'category_id' => 1, 'is_food_item' => true],
            ['name' => 'Nasi Ayam Bakar', 'sku' => 'FD002', 'price' => 30000, 'cost_price' => 18000, 'category_id' => 1, 'is_food_item' => true],
            ['name' => 'Mie Ayam', 'sku' => 'FD003', 'price' => 20000, 'cost_price' => 12000, 'category_id' => 1, 'is_food_item' => true],
            ['name' => 'Nasi Rawon', 'sku' => 'FD004', 'price' => 28000, 'cost_price' => 16000, 'category_id' => 1, 'is_food_item' => true],
            
            // Minuman
            ['name' => 'Es Teh Manis', 'sku' => 'DR001', 'price' => 8000, 'cost_price' => 3000, 'category_id' => 2, 'is_food_item' => true],
            ['name' => 'Kopi Hitam', 'sku' => 'DR002', 'price' => 10000, 'cost_price' => 4000, 'category_id' => 2, 'is_food_item' => true],
            ['name' => 'Jus Jeruk', 'sku' => 'DR003', 'price' => 15000, 'cost_price' => 8000, 'category_id' => 2, 'is_food_item' => true],
            ['name' => 'Es Kelapa Muda', 'sku' => 'DR004', 'price' => 12000, 'cost_price' => 6000, 'category_id' => 2, 'is_food_item' => true],
            
            // Snack
            ['name' => 'Keripik Singkong', 'sku' => 'SN001', 'price' => 12000, 'cost_price' => 7000, 'category_id' => 3, 'is_food_item' => true],
            ['name' => 'Pisang Goreng', 'sku' => 'SN002', 'price' => 10000, 'cost_price' => 5000, 'category_id' => 3, 'is_food_item' => true],
            
            // Dessert
            ['name' => 'Es Krim Vanilla', 'sku' => 'DS001', 'price' => 15000, 'cost_price' => 8000, 'category_id' => 4, 'is_food_item' => true],
            ['name' => 'Puding Coklat', 'sku' => 'DS002', 'price' => 12000, 'cost_price' => 6000, 'category_id' => 4, 'is_food_item' => true],
        ];

        foreach ($products as $product) {
            Product::firstOrCreate(['sku' => $product['sku']], $product);
        }

        // Create Customers
        $customers = [
            ['nama' => 'Budi Santoso', 'email' => '<EMAIL>', 'telepon' => '081234567890', 'is_active' => true],
            ['nama' => 'Siti Nurhaliza', 'email' => '<EMAIL>', 'telepon' => '081234567891', 'is_active' => true],
            ['nama' => 'Ahmad Rahman', 'email' => '<EMAIL>', 'telepon' => '081234567892', 'is_active' => true],
            ['nama' => 'Dewi Sartika', 'email' => '<EMAIL>', 'telepon' => '081234567893', 'is_active' => true],
            ['nama' => 'Rudi Hermawan', 'email' => '<EMAIL>', 'telepon' => '081234567894', 'is_active' => true],
        ];

        foreach ($customers as $customer) {
            Customer::firstOrCreate(['email' => $customer['email']], $customer);
        }

        // Create sample POS transactions for the last 30 days
        $products = Product::where('is_food_item', true)->get();
        $customers = Customer::all();
        
        for ($i = 0; $i < 50; $i++) {
            // Random date in last 30 days
            $transactionDate = Carbon::now()->subDays(rand(0, 30))->setHour(rand(8, 21))->setMinute(rand(0, 59));
            
            $transaction = PosTransaction::create([
                'transaction_number' => 'POS' . $transactionDate->format('Ymd') . str_pad($i + 1, 4, '0', STR_PAD_LEFT),
                'customer_id' => $customers->random()->id,
                'user_id' => 1, // Assuming user ID 1 exists
                'transaction_date' => $transactionDate,
                'payment_method' => collect(['cash', 'card', 'transfer', 'ewallet'])->random(),
                'total_amount' => 0, // Will be calculated
                'discount_amount' => rand(0, 5000),
                'tax_amount' => 0,
                'net_amount' => 0, // Will be calculated
                'amount_paid' => 0, // Will be calculated
                'change_given' => 0,
                'loyalty_points_used' => 0,
                'loyalty_points_earned' => 0,
                'table_number' => rand(1, 20),
            ]);

            // Add 1-5 random items to each transaction
            $itemCount = rand(1, 5);
            $totalAmount = 0;
            
            for ($j = 0; $j < $itemCount; $j++) {
                $product = $products->random();
                $quantity = rand(1, 3);
                $unitPrice = $product->price;
                $discountPerItem = rand(0, 2000);
                $totalPrice = $quantity * ($unitPrice - $discountPerItem);
                
                PosTransactionItem::create([
                    'pos_transaction_id' => $transaction->id,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'discount_per_item' => $discountPerItem,
                    'total_price' => $totalPrice,
                ]);
                
                $totalAmount += $totalPrice;
            }
            
            // Update transaction totals
            $netAmount = $totalAmount - $transaction->discount_amount + $transaction->tax_amount;
            $amountPaid = $netAmount + rand(0, 10000); // Sometimes overpaid
            
            $transaction->update([
                'total_amount' => $totalAmount,
                'net_amount' => $netAmount,
                'amount_paid' => $amountPaid,
                'change_given' => max(0, $amountPaid - $netAmount),
            ]);
        }

        $this->command->info('Marketing sample data created successfully!');
        $this->command->info('Created:');
        $this->command->info('- ' . Category::count() . ' categories');
        $this->command->info('- ' . Product::count() . ' products');
        $this->command->info('- ' . Customer::count() . ' customers');
        $this->command->info('- ' . PosTransaction::count() . ' POS transactions');
        $this->command->info('- ' . PosTransactionItem::count() . ' POS transaction items');
    }
}
