<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Unit;
use App\Models\Warehouse;
use App\Models\InventoryStock;
use App\Models\Produk;

class MasterDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🏭 Seeding master data...');

        // Seed Units
        $this->seedUnits();

        // Seed Warehouses
        $this->seedWarehouses();

        // Seed Inventory Stocks
        $this->seedInventoryStocks();

        $this->command->info('✅ Master data seeded successfully!');
    }

    private function seedUnits(): void
    {
        $this->command->info('📏 Seeding units...');

        $units = [
            [
                'name' => 'Pieces',
                'abbreviation' => 'Pcs',
                'description' => 'Unit untuk barang yang dihitung per buah',
                'is_active' => true,
            ],
            [
                'name' => 'Kilogram',
                'abbreviation' => 'Kg',
                'description' => 'Unit untuk barang yang dihitung per kilogram',
                'is_active' => true,
            ],
            [
                'name' => 'Liter',
                'abbreviation' => 'L',
                'description' => 'Unit untuk cairan yang dihitung per liter',
                'is_active' => true,
            ],
            [
                'name' => 'Meter',
                'abbreviation' => 'M',
                'description' => 'Unit untuk barang yang dihitung per meter',
                'is_active' => true,
            ],
            [
                'name' => 'Box',
                'abbreviation' => 'Box',
                'description' => 'Unit untuk barang yang dikemas per box',
                'is_active' => true,
            ],
            [
                'name' => 'Pack',
                'abbreviation' => 'Pack',
                'description' => 'Unit untuk barang yang dikemas per pack',
                'is_active' => true,
            ],
            [
                'name' => 'Dozen',
                'abbreviation' => 'Dzn',
                'description' => 'Unit untuk barang yang dihitung per lusin (12 buah)',
                'is_active' => true,
            ],
        ];

        foreach ($units as $unit) {
            Unit::firstOrCreate(
                ['abbreviation' => $unit['abbreviation']],
                $unit
            );
        }

        $this->command->info('   ✓ ' . count($units) . ' units seeded');
    }

    private function seedWarehouses(): void
    {
        $this->command->info('🏪 Seeding warehouses...');

        $warehouses = [
            [
                'name' => 'Gudang Utama',
                'code' => 'GDG-001',
                'address' => 'Jl. Industri No. 123, Jakarta Timur',
                'phone' => '021-12345678',
                'manager_name' => 'Budi Santoso',
                'is_active' => true,
            ],
            [
                'name' => 'Gudang Cabang Jakarta',
                'code' => 'GDG-JKT',
                'address' => 'Jl. Raya Jakarta No. 456, Jakarta Selatan',
                'phone' => '021-87654321',
                'manager_name' => 'Siti Rahayu',
                'is_active' => true,
            ],
            [
                'name' => 'Gudang Cabang Surabaya',
                'code' => 'GDG-SBY',
                'address' => 'Jl. Industri Surabaya No. 789, Surabaya',
                'phone' => '031-11223344',
                'manager_name' => 'Ahmad Wijaya',
                'is_active' => true,
            ],
            [
                'name' => 'Gudang Transit',
                'code' => 'GDG-TRS',
                'address' => 'Jl. Transit No. 321, Bekasi',
                'phone' => '021-99887766',
                'manager_name' => 'Dewi Lestari',
                'is_active' => false,
            ],
        ];

        foreach ($warehouses as $warehouse) {
            Warehouse::firstOrCreate(
                ['code' => $warehouse['code']],
                $warehouse
            );
        }

        $this->command->info('   ✓ ' . count($warehouses) . ' warehouses seeded');
    }

    private function seedInventoryStocks(): void
    {
        $this->command->info('📦 Seeding inventory stocks...');

        // Get all products and warehouses
        $products = Produk::all();
        $warehouses = Warehouse::where('is_active', true)->get();

        if ($products->isEmpty() || $warehouses->isEmpty()) {
            $this->command->warn('   ⚠ No products or warehouses found. Skipping inventory stocks seeding.');
            return;
        }

        $stocksCreated = 0;

        foreach ($warehouses as $warehouse) {
            // Create stock for random products in each warehouse
            $selectedProducts = $products->random(min(10, $products->count()));

            foreach ($selectedProducts as $product) {
                $quantity = rand(10, 500);
                $averageCost = $product->unit_cost ?: rand(5000, 50000);

                InventoryStock::firstOrCreate(
                    [
                        'product_id' => $product->id,
                        'warehouse_id' => $warehouse->id,
                    ],
                    [
                        'quantity' => $quantity,
                        'average_cost' => $averageCost,
                        'total_value' => $quantity * $averageCost,
                        'minimum_stock' => rand(5, 20),
                        'maximum_stock' => rand(100, 1000),
                        'last_updated' => now(),
                    ]
                );

                $stocksCreated++;
            }
        }

        $this->command->info('   ✓ ' . $stocksCreated . ' inventory stocks seeded');
    }
}
