<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Project;
use App\Models\Task;
use App\Models\User;
use App\Models\OkrPeriod;
use App\Models\Objective;
use App\Models\KeyResult;
use App\Models\Tactic;

class OkrSampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Creating OKR Sample Data...');

        // Create admin user if not exists
        $adminUser = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Admin User',
            'password' => bcrypt('password'),
            'role' => 'admin',
        ]);

        // Create sample users
        $users = [];
        for ($i = 1; $i <= 5; $i++) {
            $users[] = User::firstOrCreate([
                'email' => "user{$i}@viera.com"
            ], [
                'name' => "User {$i}",
                'password' => bcrypt('password'),
                'role' => 'employee',
            ]);
        }

        $this->command->info('✅ Users created');

        // Create sample projects
        $projects = [
            [
                'name' => 'Website Redesign',
                'description' => 'Complete redesign of company website',
                'status' => 'active',
            ],
            [
                'name' => 'Mobile App Development',
                'description' => 'Develop mobile application for customers',
                'status' => 'active',
            ],
            [
                'name' => 'Marketing Campaign Q4',
                'description' => 'Q4 marketing campaign planning and execution',
                'status' => 'planning',
            ],
            [
                'name' => 'System Integration',
                'description' => 'Integrate various business systems',
                'status' => 'active',
            ],
        ];

        $createdProjects = [];
        foreach ($projects as $projectData) {
            $project = Project::firstOrCreate([
                'name' => $projectData['name']
            ], [
                'description' => $projectData['description'],
                'status' => $projectData['status'],
                'start_date' => now(),
                'end_date' => now()->addMonths(3),
                'created_by' => $adminUser->id,
            ]);
            $createdProjects[] = $project;
        }

        $this->command->info('✅ Projects created');

        // Create sample tasks
        $taskTemplates = [
            'Design mockups and wireframes',
            'Develop frontend components',
            'Backend API development',
            'Database schema design',
            'User testing and feedback',
            'Performance optimization',
            'Security audit and fixes',
            'Documentation writing',
            'Deployment preparation',
            'Quality assurance testing',
            'Content creation and review',
            'SEO optimization',
            'Analytics implementation',
            'User training materials',
            'System monitoring setup',
        ];

        foreach ($createdProjects as $project) {
            // Create 3-5 tasks per project
            $taskCount = rand(3, 5);
            $selectedTasks = array_slice($taskTemplates, 0, $taskCount);
            
            foreach ($selectedTasks as $index => $taskName) {
                $status = match ($project->status) {
                    'completed' => 'completed',
                    'active' => $index < 2 ? 'completed' : ($index < 3 ? 'in_progress' : 'todo'),
                    default => 'todo',
                };

                Task::firstOrCreate([
                    'project_id' => $project->id,
                    'name' => $taskName,
                ], [
                    'description' => "Task untuk {$taskName} pada proyek {$project->name}",
                    'assigned_to' => collect($users)->random()->id,
                    'status' => $status,
                    'start_date' => now()->subDays(rand(1, 30)),
                    'due_date' => now()->addDays(rand(7, 60)),
                    'created_by' => $adminUser->id,
                ]);
            }
        }

        $this->command->info('✅ Tasks created');

        // Create OKR Period if not exists
        $okrPeriod = OkrPeriod::firstOrCreate([
            'nama_periode' => 'Q4 2024'
        ], [
            'tipe_periode' => 'quarterly',
            'tahun' => 2024,
            'quarter' => 4,
            'tanggal_mulai' => now()->startOfQuarter(),
            'tanggal_selesai' => now()->endOfQuarter(),
            'status' => 'active',
            'is_active' => true,
        ]);

        $this->command->info('✅ OKR Period created');

        // Create sample Objective
        $objective = Objective::firstOrCreate([
            'nama_objective' => 'Increase Customer Satisfaction'
        ], [
            'okr_period_id' => $okrPeriod->id,
            'deskripsi' => 'Improve overall customer satisfaction through better service delivery and product quality',
            'periode_mulai' => $okrPeriod->tanggal_mulai,
            'periode_selesai' => $okrPeriod->tanggal_selesai,
            'status' => 'active',
            'progress_percentage' => 25,
            'target_completion' => $okrPeriod->tanggal_selesai,
            'owner_id' => $adminUser->id,
            'created_by' => $adminUser->id,
        ]);

        $this->command->info('✅ Sample Objective created');

        // Create sample Key Results
        $keyResults = [
            [
                'nama_key_result' => 'Achieve 90% Customer Satisfaction Score',
                'deskripsi' => 'Increase customer satisfaction score from current 75% to 90%',
                'tipe_metrik' => 'percentage',
                'target_value' => 90,
                'current_value' => 75,
                'weight' => 40,
            ],
            [
                'nama_key_result' => 'Reduce Response Time to 2 Hours',
                'deskripsi' => 'Reduce average customer support response time to under 2 hours',
                'tipe_metrik' => 'number',
                'target_value' => 2,
                'current_value' => 4.5,
                'unit_measurement' => 'hours',
                'weight' => 35,
            ],
            [
                'nama_key_result' => 'Increase NPS Score to 50',
                'deskripsi' => 'Improve Net Promoter Score from current 30 to 50',
                'tipe_metrik' => 'number',
                'target_value' => 50,
                'current_value' => 30,
                'weight' => 25,
            ],
        ];

        foreach ($keyResults as $krData) {
            KeyResult::firstOrCreate([
                'objective_id' => $objective->id,
                'nama_key_result' => $krData['nama_key_result'],
            ], array_merge($krData, [
                'status' => 'in_progress',
                'due_date' => $okrPeriod->tanggal_selesai,
                'created_by' => $adminUser->id,
            ]));
        }

        $this->command->info('✅ Sample Key Results created');

        // Create sample Tactics
        $tactics = [
            [
                'nama_tactic' => 'Implement Customer Feedback System',
                'deskripsi' => 'Deploy automated customer feedback collection system',
                'jenis_tactic' => 'operational',
                'priority' => 'high',
                'estimasi_effort' => 40,
                'skor_dampak' => 8,
            ],
            [
                'nama_tactic' => 'Train Customer Support Team',
                'deskripsi' => 'Comprehensive training program for customer support representatives',
                'jenis_tactic' => 'strategic',
                'priority' => 'high',
                'estimasi_effort' => 60,
                'skor_dampak' => 9,
            ],
            [
                'nama_tactic' => 'Optimize Support Workflow',
                'deskripsi' => 'Streamline customer support processes and workflows',
                'jenis_tactic' => 'operational',
                'priority' => 'medium',
                'estimasi_effort' => 30,
                'skor_dampak' => 7,
            ],
        ];

        foreach ($tactics as $tacticData) {
            Tactic::firstOrCreate([
                'objective_id' => $objective->id,
                'nama_tactic' => $tacticData['nama_tactic'],
            ], array_merge($tacticData, [
                'status' => 'in_progress',
                'tanggal_mulai' => now(),
                'tanggal_target' => now()->addDays(30),
                'progress_percentage' => rand(10, 60),
                'pemilik' => $adminUser->name,
                'created_by' => $adminUser->id,
            ]));
        }

        $this->command->info('✅ Sample Tactics created');

        // Connect some tasks to the objective
        $tasksToConnect = Task::take(3)->get();
        foreach ($tasksToConnect as $task) {
            $objective->tasks()->syncWithoutDetaching([
                $task->id => ['contribution_percentage' => rand(20, 50)]
            ]);
        }

        $this->command->info('✅ Tasks connected to Objective');

        $this->printSummary();
    }

    private function printSummary(): void
    {
        $this->command->info('');
        $this->command->info('📊 OKR Sample Data Summary:');
        $this->command->info('================================');
        $this->command->info('👥 Users: ' . User::count());
        $this->command->info('📁 Projects: ' . Project::count());
        $this->command->info('📋 Tasks: ' . Task::count());
        $this->command->info('🎯 Objectives: ' . Objective::count());
        $this->command->info('🔑 Key Results: ' . KeyResult::count());
        $this->command->info('⚡ Tactics: ' . Tactic::count());
        $this->command->info('📅 OKR Periods: ' . OkrPeriod::count());
        $this->command->info('');
        $this->command->info('🎉 Sample data created successfully!');
        $this->command->info('You can now test the OKR integrated form with sample data.');
    }
}
