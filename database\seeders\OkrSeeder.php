<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Objective;
use App\Models\KeyResult;
use App\Models\Tactic;
use App\Models\User;
use App\Models\Departemen;
use App\Models\Divisi;
use App\Models\Task;
use Carbon\Carbon;

class OkrSeeder extends Seeder
{
    public function run(): void
    {
        // Get sample users, departments, and divisions
        $users = User::limit(5)->get();
        $departemen = Departemen::first();
        $divisi = Divisi::first();
        
        if ($users->isEmpty() || !$departemen || !$divisi) {
            $this->command->warn('Tidak ada data user, departemen, atau divisi. Silakan jalankan seeder lain terlebih dahulu.');
            return;
        }

        // Create sample objectives
        $objectives = [
            [
                'nama_objective' => 'Meningkatkan Produktivitas Tim Development',
                'deskripsi' => 'Meningkatkan efisiensi dan kualitas output tim development melalui optimasi proses dan tools',
                'periode_mulai' => Carbon::now()->startOfQuarter(),
                'periode_selesai' => Carbon::now()->endOfQuarter(),
                'status' => 'active',
                'progress_percentage' => 65,
                'target_completion' => Carbon::now()->addMonths(2),
                'owner_id' => $users->first()->id,
                'departemen_id' => $departemen->id,
                'divisi_id' => $divisi->id,
                'created_by' => $users->first()->id,
            ],
            [
                'nama_objective' => 'Implementasi Sistem OKR Perusahaan',
                'deskripsi' => 'Mengimplementasikan sistem OKR untuk meningkatkan alignment dan tracking progress di seluruh perusahaan',
                'periode_mulai' => Carbon::now()->startOfMonth(),
                'periode_selesai' => Carbon::now()->addMonths(3),
                'status' => 'active',
                'progress_percentage' => 40,
                'target_completion' => Carbon::now()->addMonths(3),
                'owner_id' => $users->skip(1)->first()->id,
                'departemen_id' => $departemen->id,
                'divisi_id' => $divisi->id,
                'created_by' => $users->first()->id,
            ],
            [
                'nama_objective' => 'Peningkatan Customer Satisfaction',
                'deskripsi' => 'Meningkatkan kepuasan pelanggan melalui peningkatan kualitas layanan dan response time',
                'periode_mulai' => Carbon::now()->subMonth(),
                'periode_selesai' => Carbon::now()->addMonths(2),
                'status' => 'active',
                'progress_percentage' => 80,
                'target_completion' => Carbon::now()->addMonths(2),
                'owner_id' => $users->skip(2)->first()->id,
                'departemen_id' => $departemen->id,
                'created_by' => $users->first()->id,
            ]
        ];

        foreach ($objectives as $objectiveData) {
            $objective = Objective::create($objectiveData);

            // Create key results for each objective
            $this->createKeyResults($objective, $users);
            
            // Create tactics for each objective
            $this->createTactics($objective, $users);
        }

        $this->command->info('OKR sample data berhasil dibuat!');
    }

    private function createKeyResults(Objective $objective, $users): void
    {
        $keyResultsData = [
            [
                'nama_key_result' => 'Mengurangi Bug Production',
                'deskripsi' => 'Mengurangi jumlah bug yang masuk ke production environment',
                'tipe_metrik' => 'number',
                'target_value' => 5,
                'current_value' => 3,
                'unit_measurement' => 'bugs per month',
                'status' => 'in_progress',
                'due_date' => $objective->target_completion,
            ],
            [
                'nama_key_result' => 'Meningkatkan Code Coverage',
                'deskripsi' => 'Meningkatkan persentase code coverage untuk testing',
                'tipe_metrik' => 'percentage',
                'target_value' => 85,
                'current_value' => 70,
                'unit_measurement' => '%',
                'status' => 'in_progress',
                'due_date' => $objective->target_completion,
            ],
            [
                'nama_key_result' => 'Deployment Success Rate',
                'deskripsi' => 'Mencapai tingkat keberhasilan deployment yang tinggi',
                'tipe_metrik' => 'percentage',
                'target_value' => 95,
                'current_value' => 88,
                'unit_measurement' => '%',
                'status' => 'in_progress',
                'due_date' => $objective->target_completion,
            ]
        ];

        foreach ($keyResultsData as $index => $keyResultData) {
            $keyResultData['objective_id'] = $objective->id;
            $keyResultData['created_by'] = $users->random()->id;
            
            // Calculate progress percentage
            $keyResultData['progress_percentage'] = $keyResultData['target_value'] > 0 
                ? min(100, round(($keyResultData['current_value'] / $keyResultData['target_value']) * 100))
                : 0;

            KeyResult::create($keyResultData);
        }
    }

    private function createTactics(Objective $objective, $users): void
    {
        $tacticsData = [
            [
                'nama_tactic' => 'Implementasi Code Review Process',
                'deskripsi' => 'Menerapkan proses code review yang sistematis untuk semua pull request',
                'priority' => 'high',
                'status' => 'in_progress',
                'progress_percentage' => 75,
                'start_date' => Carbon::now()->subWeeks(2),
                'due_date' => Carbon::now()->addWeeks(2),
                'estimated_hours' => 40,
                'actual_hours' => 30,
            ],
            [
                'nama_tactic' => 'Setup Automated Testing Pipeline',
                'deskripsi' => 'Membangun pipeline automated testing untuk CI/CD',
                'priority' => 'critical',
                'status' => 'in_progress',
                'progress_percentage' => 60,
                'start_date' => Carbon::now()->subWeek(),
                'due_date' => Carbon::now()->addWeeks(3),
                'estimated_hours' => 60,
                'actual_hours' => 25,
            ],
            [
                'nama_tactic' => 'Team Training & Knowledge Sharing',
                'deskripsi' => 'Mengadakan sesi training dan knowledge sharing rutin',
                'priority' => 'medium',
                'status' => 'planned',
                'progress_percentage' => 20,
                'start_date' => Carbon::now()->addWeek(),
                'due_date' => Carbon::now()->addMonth(),
                'estimated_hours' => 20,
                'actual_hours' => 5,
            ]
        ];

        foreach ($tacticsData as $tacticData) {
            $tacticData['objective_id'] = $objective->id;
            $tacticData['assigned_to'] = $users->random()->id;
            $tacticData['created_by'] = $users->random()->id;

            $tactic = Tactic::create($tacticData);

            // Attach some existing tasks to tactics (if any exist)
            $existingTasks = Task::limit(2)->get();
            if ($existingTasks->isNotEmpty()) {
                foreach ($existingTasks as $task) {
                    $tactic->attachTask($task, rand(50, 100));
                }
            }
        }
    }
}
