<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Customer;
use App\Models\Product;
use App\Models\PosTransaction;
use App\Models\PosTransactionItem;
use App\Models\User;

class PosTransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first user for transactions
        $firstUser = User::first();
        if (!$firstUser) {
            $this->command->error('No users found. Please create a user first.');
            return;
        }

        // Get customers
        $customers = Customer::limit(3)->get();
        if ($customers->count() < 2) {
            $this->command->error('Not enough customers found. Please run CustomerSeeder first.');
            return;
        }

        // Get products
        $products = Product::limit(4)->get();
        if ($products->count() < 4) {
            $this->command->error('Not enough products found. Please run ProductSeeder first.');
            return;
        }

        $customer1 = $customers->get(0);
        $customer2 = $customers->get(1);
        $product1 = $products->get(0); // Nasi Goreng Spesial
        $product2 = $products->get(1); // Ayam Bakar Madu
        $product3 = $products->get(2); // Gado-Gado Jakarta
        $product4 = $products->get(3); // Soto Ayam Lamongan

        // Create POS Transactions
        $posTransaction1 = PosTransaction::create([
            'transaction_number' => 'POS-' . date('Ymd') . '-' . str_pad((PosTransaction::whereDate('created_at', today())->count() + 1), 3, '0', STR_PAD_LEFT),
            'customer_id' => $customer1->id,
            'user_id' => $firstUser->id,
            'transaction_date' => now()->subDays(2),
            'total_amount' => 45000,
            'tax_amount' => 4500,
            'discount_amount' => 0,
            'net_amount' => 49500,
            'payment_method' => 'cash',
            'amount_paid' => 50000,
            'change_given' => 500,
            'loyalty_points_earned' => 45,
            'table_number' => 'T01',
        ]);

        $posTransaction2 = PosTransaction::create([
            'transaction_number' => 'POS-' . date('Ymd') . '-' . str_pad((PosTransaction::whereDate('created_at', today())->count() + 1), 3, '0', STR_PAD_LEFT),
            'customer_id' => $customer2->id,
            'user_id' => $firstUser->id,
            'transaction_date' => now()->subDays(1),
            'total_amount' => 19000,
            'tax_amount' => 1700,
            'discount_amount' => 2000,
            'net_amount' => 18700,
            'payment_method' => 'qris',
            'amount_paid' => 18700,
            'change_given' => 0,
            'loyalty_points_earned' => 15,
        ]);

        $posTransaction3 = PosTransaction::create([
            'transaction_number' => 'POS-' . date('Ymd') . '-' . str_pad((PosTransaction::whereDate('created_at', today())->count() + 1), 3, '0', STR_PAD_LEFT),
            'customer_id' => null, // Walk-in customer
            'user_id' => $firstUser->id,
            'transaction_date' => now(),
            'total_amount' => 25000,
            'tax_amount' => 2500,
            'discount_amount' => 0,
            'net_amount' => 27500,
            'payment_method' => 'card',
            'amount_paid' => 27500,
            'change_given' => 0,
            'loyalty_points_earned' => 0,
        ]);

        // Create POS Transaction Items
        PosTransactionItem::create([
            'pos_transaction_id' => $posTransaction1->id,
            'product_id' => $product1->id,
            'quantity' => 1,
            'unit_price' => 25000,
            'discount_per_item' => 0,
            'total_price' => 25000
        ]);

        PosTransactionItem::create([
            'pos_transaction_id' => $posTransaction1->id,
            'product_id' => $product2->id,
            'quantity' => 1,
            'unit_price' => 20000,
            'discount_per_item' => 0,
            'total_price' => 20000
        ]);

        PosTransactionItem::create([
            'pos_transaction_id' => $posTransaction2->id,
            'product_id' => $product3->id,
            'quantity' => 2,
            'unit_price' => 5000,
            'discount_per_item' => 0,
            'total_price' => 10000
        ]);

        PosTransactionItem::create([
            'pos_transaction_id' => $posTransaction2->id,
            'product_id' => $product4->id,
            'quantity' => 1,
            'unit_price' => 12000,
            'discount_per_item' => 2000,
            'total_price' => 10000
        ]);

        PosTransactionItem::create([
            'pos_transaction_id' => $posTransaction3->id,
            'product_id' => $product1->id,
            'quantity' => 1,
            'unit_price' => 25000,
            'discount_per_item' => 0,
            'total_price' => 25000
        ]);

        $this->command->info('POS Transaction seeder completed successfully!');
        $this->command->info('Created:');
        $this->command->info('- ' . PosTransaction::count() . ' POS transactions');
        $this->command->info('- ' . PosTransactionItem::count() . ' POS transaction items');
    }
}
