<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PostingRule;
use App\Models\PostingRuleEntry;
use App\Models\Akun;

class PostingRulesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get accounts for reference
        $kas = Akun::where('kode_akun', '1001')->first();
        $bank = Akun::where('kode_akun', '1002')->first();
        $pendapatanPenjualan = Akun::where('kode_akun', '4001')->first();
        $utangPpn = Akun::where('kode_akun', '2101')->first();
        $hpp = Akun::where('kode_akun', '5001')->first();
        $persediaan = Akun::where('kode_akun', '1201')->first();

        // 1. Aturan Posting: Penjualan Tunai Umum
        $penjualanTunai = PostingRule::create([
            'rule_name' => 'Penjualan Tunai Umum',
            'source_type' => 'Sale',
            'trigger_condition' => ['payment_method' => 'Cash'],
            'description' => 'Aturan posting untuk penjualan tunai dengan pembayaran cash',
            'is_active' => true,
            'priority' => 1,
            'created_by' => 1,
        ]);

        // Entri jurnal untuk penjualan tunai
        PostingRuleEntry::create([
            'posting_rule_id' => $penjualanTunai->id,
            'account_id' => $kas->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_amount',
            'description_template' => 'Penjualan tunai - {source.transaction_code}',
            'sort_order' => 1,
        ]);

        PostingRuleEntry::create([
            'posting_rule_id' => $penjualanTunai->id,
            'account_id' => $pendapatanPenjualan->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'subtotal',
            'description_template' => 'Pendapatan penjualan - {source.transaction_code}',
            'sort_order' => 2,
        ]);

        PostingRuleEntry::create([
            'posting_rule_id' => $penjualanTunai->id,
            'account_id' => $utangPpn->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'tax_amount',
            'description_template' => 'PPN Keluaran - {source.transaction_code}',
            'sort_order' => 3,
        ]);

        // 2. Aturan Posting: Penjualan Transfer/Bank
        $penjualanTransfer = PostingRule::create([
            'rule_name' => 'Penjualan Transfer Bank',
            'source_type' => 'Sale',
            'trigger_condition' => ['payment_method' => 'Transfer'],
            'description' => 'Aturan posting untuk penjualan dengan pembayaran transfer bank',
            'is_active' => true,
            'priority' => 2,
            'created_by' => 1,
        ]);

        PostingRuleEntry::create([
            'posting_rule_id' => $penjualanTransfer->id,
            'account_id' => $bank->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_amount',
            'description_template' => 'Penjualan transfer - {source.transaction_code}',
            'sort_order' => 1,
        ]);

        PostingRuleEntry::create([
            'posting_rule_id' => $penjualanTransfer->id,
            'account_id' => $pendapatanPenjualan->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'subtotal',
            'description_template' => 'Pendapatan penjualan - {source.transaction_code}',
            'sort_order' => 2,
        ]);

        PostingRuleEntry::create([
            'posting_rule_id' => $penjualanTransfer->id,
            'account_id' => $utangPpn->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'tax_amount',
            'description_template' => 'PPN Keluaran - {source.transaction_code}',
            'sort_order' => 3,
        ]);

        // 3. Aturan Posting: HPP Penjualan Umum
        $hppPenjualan = PostingRule::create([
            'rule_name' => 'HPP Penjualan Umum',
            'source_type' => 'Sale',
            'trigger_condition' => [], // Berlaku untuk semua penjualan
            'description' => 'Aturan posting untuk mencatat Harga Pokok Penjualan',
            'is_active' => true,
            'priority' => 10, // Prioritas lebih rendah, dijalankan setelah aturan penjualan
            'created_by' => 1,
        ]);

        PostingRuleEntry::create([
            'posting_rule_id' => $hppPenjualan->id,
            'account_id' => $hpp->id,
            'dc_type' => 'Debit',
            'amount_type' => 'Calculated',
            'calculation_expression' => 'sale_items.sum(quantity * unit_cost)',
            'description_template' => 'HPP Penjualan - {source.transaction_code}',
            'sort_order' => 1,
        ]);

        PostingRuleEntry::create([
            'posting_rule_id' => $hppPenjualan->id,
            'account_id' => $persediaan->id,
            'dc_type' => 'Credit',
            'amount_type' => 'Calculated',
            'calculation_expression' => 'sale_items.sum(quantity * unit_cost)',
            'description_template' => 'Pengurangan Persediaan - {source.transaction_code}',
            'sort_order' => 2,
        ]);
    }
}
