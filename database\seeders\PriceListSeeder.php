<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PriceList;
use App\Models\Product;
use App\Models\Outlet;

class PriceListSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding price lists...');

        // Create a global price list
        $globalPriceList = PriceList::updateOrCreate(
            ['code' => 'GLOBAL_001'],
            [
                'name' => 'Global Price List',
                'description' => 'Default pricing for all outlets',
                'is_global' => true,
                'is_active' => true,
                'created_by' => 1, // Assuming user ID 1 exists
            ]
        );

        // Create outlet-specific price lists
        $vipPriceList = PriceList::updateOrCreate(
            ['code' => 'VIP_001'],
            [
                'name' => 'VIP Customer Pricing',
                'description' => 'Special pricing for VIP customers',
                'is_global' => false,
                'is_active' => true,
                'created_by' => 1,
            ]
        );

        $wholesalePriceList = PriceList::updateOrCreate(
            ['code' => 'WHOLESALE_001'],
            [
                'name' => 'Wholesale Pricing',
                'description' => 'Bulk purchase pricing',
                'is_global' => false,
                'is_active' => true,
                'created_by' => 1,
            ]
        );

        // Get some products to add to price lists
        $products = Product::limit(10)->get();

        if ($products->count() > 0) {
            $this->command->info('Adding products to price lists...');

            foreach ($products as $product) {
                // Add to global price list with default prices
                $globalPriceList->addProduct(
                    $product->id,
                    $product->price ?? 25000,
                    $product->cost_price ?? 15000
                );

                // Add to VIP price list with 10% discount
                $vipPrice = ($product->price ?? 25000) * 0.9;
                $vipPriceList->addProduct(
                    $product->id,
                    $vipPrice,
                    $product->cost_price ?? 15000
                );

                // Add to wholesale price list with 15% discount
                $wholesalePrice = ($product->price ?? 25000) * 0.85;
                $wholesalePriceList->addProduct(
                    $product->id,
                    $wholesalePrice,
                    $product->cost_price ?? 15000
                );
            }

            $this->command->info('Products added to price lists successfully!');
        } else {
            $this->command->warn('No products found. Please seed products first.');
        }

        // Assign price lists to outlets if any exist
        $outlets = Outlet::limit(3)->get();
        
        if ($outlets->count() > 0) {
            $this->command->info('Assigning price lists to outlets...');

            foreach ($outlets as $index => $outlet) {
                switch ($index) {
                    case 0:
                        // First outlet gets VIP pricing
                        $outlet->assignPriceList($vipPriceList->id, 1);
                        break;
                    case 1:
                        // Second outlet gets wholesale pricing
                        $outlet->assignPriceList($wholesalePriceList->id, 1);
                        break;
                    case 2:
                        // Third outlet gets both VIP (priority 1) and wholesale (priority 2)
                        $outlet->assignPriceList($vipPriceList->id, 1);
                        $outlet->assignPriceList($wholesalePriceList->id, 2);
                        break;
                }
            }

            $this->command->info('Price lists assigned to outlets successfully!');
        } else {
            $this->command->warn('No outlets found. Price lists created but not assigned.');
        }

        $this->command->info('Price list seeding completed!');
        $this->command->info('Created:');
        $this->command->info('- Global Price List (GLOBAL_001)');
        $this->command->info('- VIP Customer Pricing (VIP_001)');
        $this->command->info('- Wholesale Pricing (WHOLESALE_001)');
    }
}
