<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding products...');

        // Pastikan ada kategori terlebih dahulu
        $foodCategory = Category::firstOrCreate(['name' => 'Makanan']);
        $beverageCategory = Category::firstOrCreate(['name' => 'Minuman']);
        $snackCategory = Category::firstOrCreate(['name' => 'Snack']);

        // Sample Food Products
        $foodProducts = [
            [
                'name' => 'Nasi Goreng Spesial',
                'sku' => 'FOOD-001',
                'barcode' => '1234567890001',
                'description' => 'Nasi goreng dengan telur, ayam, dan sayuran',
                'price' => 25000,
                'cost_price' => 15000,
                'category_id' => $foodCategory->id,
                'stock_quantity' => 50,
                'is_active' => true,
                'is_food_item' => true,
            ],
            [
                'name' => 'Ayam Bakar Madu',
                'sku' => 'FOOD-002',
                'barcode' => '1234567890002',
                'description' => 'Ayam bakar dengan bumbu madu spesial',
                'price' => 35000,
                'cost_price' => 20000,
                'category_id' => $foodCategory->id,
                'stock_quantity' => 30,
                'is_active' => true,
                'is_food_item' => true,
            ],
            [
                'name' => 'Gado-Gado Jakarta',
                'sku' => 'FOOD-003',
                'barcode' => '1234567890003',
                'description' => 'Gado-gado dengan bumbu kacang khas Jakarta',
                'price' => 20000,
                'cost_price' => 12000,
                'category_id' => $foodCategory->id,
                'stock_quantity' => 40,
                'is_active' => true,
                'is_food_item' => true,
            ],
            [
                'name' => 'Soto Ayam Lamongan',
                'sku' => 'FOOD-004',
                'barcode' => '1234567890004',
                'description' => 'Soto ayam dengan kuah bening khas Lamongan',
                'price' => 22000,
                'cost_price' => 13000,
                'category_id' => $foodCategory->id,
                'stock_quantity' => 35,
                'is_active' => true,
                'is_food_item' => true,
            ],
            [
                'name' => 'Mie Ayam Bakso',
                'sku' => 'FOOD-005',
                'barcode' => '1234567890005',
                'description' => 'Mie ayam dengan bakso dan pangsit',
                'price' => 18000,
                'cost_price' => 10000,
                'category_id' => $foodCategory->id,
                'stock_quantity' => 45,
                'is_active' => true,
                'is_food_item' => true,
            ],
        ];

        // Sample Beverage Products
        $beverageProducts = [
            [
                'name' => 'Es Teh Manis',
                'sku' => 'BEV-001',
                'barcode' => '1234567890101',
                'description' => 'Es teh manis segar',
                'price' => 5000,
                'cost_price' => 2000,
                'category_id' => $beverageCategory->id,
                'stock_quantity' => 100,
                'is_active' => true,
                'is_food_item' => false,
            ],
            [
                'name' => 'Jus Jeruk Segar',
                'sku' => 'BEV-002',
                'barcode' => '1234567890102',
                'description' => 'Jus jeruk segar tanpa gula tambahan',
                'price' => 12000,
                'cost_price' => 6000,
                'category_id' => $beverageCategory->id,
                'stock_quantity' => 60,
                'is_active' => true,
                'is_food_item' => false,
            ],
            [
                'name' => 'Kopi Hitam',
                'sku' => 'BEV-003',
                'barcode' => '1234567890103',
                'description' => 'Kopi hitam robusta pilihan',
                'price' => 8000,
                'cost_price' => 3000,
                'category_id' => $beverageCategory->id,
                'stock_quantity' => 80,
                'is_active' => true,
                'is_food_item' => false,
            ],
            [
                'name' => 'Cappuccino',
                'sku' => 'BEV-004',
                'barcode' => '1234567890104',
                'description' => 'Cappuccino dengan foam susu yang lembut',
                'price' => 15000,
                'cost_price' => 7000,
                'category_id' => $beverageCategory->id,
                'stock_quantity' => 50,
                'is_active' => true,
                'is_food_item' => false,
            ],
            [
                'name' => 'Es Kelapa Muda',
                'sku' => 'BEV-005',
                'barcode' => '1234567890105',
                'description' => 'Es kelapa muda segar dengan daging kelapa',
                'price' => 10000,
                'cost_price' => 5000,
                'category_id' => $beverageCategory->id,
                'stock_quantity' => 40,
                'is_active' => true,
                'is_food_item' => false,
            ],
        ];

        // Sample Snack Products
        $snackProducts = [
            [
                'name' => 'Keripik Singkong',
                'sku' => 'SNK-001',
                'barcode' => '1234567890201',
                'description' => 'Keripik singkong renyah dengan berbagai rasa',
                'price' => 8000,
                'cost_price' => 4000,
                'category_id' => $snackCategory->id,
                'stock_quantity' => 75,
                'is_active' => true,
                'is_food_item' => false,
            ],
            [
                'name' => 'Pisang Goreng',
                'sku' => 'SNK-002',
                'barcode' => '1234567890202',
                'description' => 'Pisang goreng crispy dengan tepung spesial',
                'price' => 6000,
                'cost_price' => 3000,
                'category_id' => $snackCategory->id,
                'stock_quantity' => 60,
                'is_active' => true,
                'is_food_item' => true,
            ],
            [
                'name' => 'Tahu Isi',
                'sku' => 'SNK-003',
                'barcode' => '1234567890203',
                'description' => 'Tahu isi dengan sayuran dan tauge',
                'price' => 7000,
                'cost_price' => 3500,
                'category_id' => $snackCategory->id,
                'stock_quantity' => 50,
                'is_active' => true,
                'is_food_item' => true,
            ],
        ];

        // Insert all products
        foreach ($foodProducts as $product) {
            Product::create($product);
        }

        foreach ($beverageProducts as $product) {
            Product::create($product);
        }

        foreach ($snackProducts as $product) {
            Product::create($product);
        }

        $this->command->info('Products seeded successfully!');
        $this->command->info('Total products: ' . Product::count());
    }
}
