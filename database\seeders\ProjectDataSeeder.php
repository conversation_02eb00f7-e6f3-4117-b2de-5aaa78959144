<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Project;
use App\Models\Task;

use App\Models\User;

class ProjectDataSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('Seeding project management data...');

        // Get admin user
        $adminUser = User::where('email', '<EMAIL>')->first();
        if (!$adminUser) {
            $adminUser = User::factory()->create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ]);
        }

        // Get some users to act as customers
        $customers = User::take(5)->get();
        if ($customers->count() < 5) {
            // Create additional users if needed
            $needed = 5 - $customers->count();
            for ($i = 0; $i < $needed; $i++) {
                $customers->push(User::factory()->create([
                    'name' => 'Customer ' . ($i + 1),
                    'email' => 'customer' . ($i + 1) . '@example.com',
                ]));
            }
        }

        // Create sample projects
        $projects = [
            [
                'name' => 'Sistem Manajemen Inventori',
                'description' => 'Pengembangan sistem manajemen inventori untuk retail',
                'status' => 'active',
                'start_date' => now()->subDays(30),
                'end_date' => now()->addDays(60),
                'customer_id' => $customers[0]->id,
            ],
            [
                'name' => 'Platform E-Commerce',
                'description' => 'Membangun platform e-commerce dengan fitur lengkap',
                'status' => 'active',
                'start_date' => now()->subDays(15),
                'end_date' => now()->addDays(90),
                'customer_id' => $customers[1]->id,
            ],
            [
                'name' => 'Aplikasi Mobile Banking',
                'description' => 'Pengembangan aplikasi mobile banking yang aman',
                'status' => 'planning',
                'start_date' => now()->addDays(7),
                'end_date' => now()->addDays(120),
                'customer_id' => $customers[2]->id,
            ],
            [
                'name' => 'Dashboard Analytics',
                'description' => 'Dashboard untuk analisis data bisnis real-time',
                'status' => 'completed',
                'start_date' => now()->subDays(90),
                'end_date' => now()->subDays(10),
                'customer_id' => $customers[3]->id,
            ],
            [
                'name' => 'Sistem CRM Terintegrasi',
                'description' => 'Customer Relationship Management system',
                'status' => 'active',
                'start_date' => now()->subDays(20),
                'end_date' => now()->addDays(45),
                'customer_id' => $customers[4]->id,
            ],
        ];

        foreach ($projects as $projectData) {
            $project = Project::firstOrCreate([
                'name' => $projectData['name']
            ], array_merge($projectData, [
                'created_by' => $adminUser->id,
            ]));

            // Assign team members to project (only users with karyawan data)
            $teamMembers = User::whereNotIn('id', [$adminUser->id])
                ->whereHas('karyawan')
                ->inRandomOrder()
                ->take(rand(3, 6))
                ->get();

            foreach ($teamMembers as $member) {
                $project->members()->syncWithoutDetaching([
                    $member->id => [
                        'role' => 'member',
                        'hourly_rate' => rand(50, 150),
                        'capacity_hours_per_week' => 40,
                        'joined_at' => now(),
                        'is_active' => true,
                        'added_by' => $adminUser->id,
                    ]
                ]);
            }

            // Create tasks for each project
            $this->createTasks($project, $adminUser, $teamMembers);
        }



        $this->command->info('Project management data seeded successfully!');
    }

    private function createTasks(Project $project, User $adminUser, $teamMembers): void
    {
        $taskTemplates = [
            'Requirements Analysis',
            'System Design',
            'Database Design',
            'Frontend Development',
            'Backend Development',
            'API Development',
            'Testing',
            'Documentation',
            'Deployment',
            'User Training',
        ];

        foreach ($taskTemplates as $index => $taskName) {
            $status = match ($project->status) {
                'completed' => 'completed',
                'active' => $index < 3 ? 'completed' : ($index < 6 ? 'in_progress' : 'todo'),
                default => 'todo',
            };

            Task::firstOrCreate([
                'project_id' => $project->id,
                'name' => $taskName,
            ], [
                'description' => "Task untuk {$taskName} pada proyek {$project->name}",
                'assigned_to' => $teamMembers->random()->id,
                'status' => $status,
                'due_date' => now()->addDays(rand(7, 60)),
                'created_by' => $adminUser->id,
            ]);
        }
    }
}
