<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\DailyTransaction;
use App\Models\Outlet;
use Carbon\Carbon;

class RealisticPLReportSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing data for clean test
        DailyTransaction::truncate();

        $outlet = Outlet::where('name', 'LIKE', '%Sudirman%')->first();
        if (!$outlet) {
            $outlet = Outlet::first();
        }

        $month = Carbon::create(2025, 6, 1); // Juni 2025

        $this->createRevenueTransactions($outlet, $month);
        $this->createExpenseTransactions($outlet, $month);

        $this->command->info('Realistic P&L Report data created successfully!');
    }

    private function createRevenueTransactions(Outlet $outlet, Carbon $month)
    {
        $revenues = [
            ['payment_method' => 'pendapatan_cash', 'amount' => 500000000, 'description' => 'Pendapatan Cash'],
            ['payment_method' => 'pendapatan_debit', 'amount' => 200000000, 'description' => 'Pendapatan Debit'],
            ['payment_method' => 'pendapatan_transfer', 'amount' => 100000000, 'description' => 'Pendapatan Transfer'],
            ['payment_method' => 'pendapatan_qris', 'amount' => 100000000, 'description' => 'Pendapatan QRIS'],
            ['payment_method' => 'pendapatan_gojek', 'amount' => 36479500, 'description' => 'Pendapatan Gojek'],
            ['payment_method' => 'pendapatan_grab_ovo', 'amount' => 5038000, 'description' => 'Pendapatan Grab Ovo'],
            ['payment_method' => 'pendapatan_sewa_rak', 'amount' => 4950000, 'description' => 'Pendapatan Sewa Rak'],
        ];

        foreach ($revenues as $revenue) {
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $month->copy()->addDays(rand(1, 28)),
                'description' => $revenue['description'],
                'type' => 'revenue',
                'payment_method' => $revenue['payment_method'],
                'amount' => $revenue['amount'],
                'notes' => 'Data sesuai laporan P&L Juni 2025',
            ]);
        }
    }

    private function createExpenseTransactions(Outlet $outlet, Carbon $month)
    {
        $expenses = [
            // Beban Bahan Baku
            ['category' => 'beban_bahan_baku', 'subcategory' => 'tagihan_rkv', 'amount' => 500000000, 'description' => 'Tagihan Rkv'],
            ['category' => 'beban_bahan_baku', 'subcategory' => 'tagihan_mitra', 'amount' => 300000000, 'description' => 'Tagihan Mitra Juni 2025'],
            ['category' => 'beban_bahan_baku', 'subcategory' => 'tagihan_supplier', 'amount' => 97609607, 'description' => 'Tagihan supplier depan'],

            // Beban GA
            ['category' => 'beban_ga', 'subcategory' => 'material_bangunan', 'amount' => 8085000, 'description' => 'Material Bangunan / Kabel/ Bayar Tukang'],
            ['category' => 'beban_ga', 'subcategory' => 'service_oven_freezer_packing', 'amount' => 1000000, 'description' => 'Service Oven dan Freezer/Mobil/ AC/Mesin Packing/Genset'],
            ['category' => 'beban_ga', 'subcategory' => 'service_equipment', 'amount' => 1000000, 'description' => 'Service Freezer/Mobil/ AC/Motor/cctv/dll'],
            ['category' => 'beban_ga', 'subcategory' => 'belanja_ga', 'amount' => 1000000, 'description' => 'Belanja GA'],
            ['category' => 'beban_ga', 'subcategory' => 'cuci_mobil_oli', 'amount' => 1000000, 'description' => 'Cuci Mobil/Isi Angin/Tambal Ban/Ganti Oli'],
            ['category' => 'beban_ga', 'subcategory' => 'kertas_thermal', 'amount' => 1000000, 'description' => 'Kertas Thermal/Kertas Label'],
            ['category' => 'beban_ga', 'subcategory' => 'keperluan_genset', 'amount' => 1000000, 'description' => 'Keperluan Genset/ Dexlite Genset'],
            ['category' => 'beban_ga', 'subcategory' => 'bensin_luxio_putih', 'amount' => 1000000, 'description' => 'Bensin Luxio Putih'],

            // Beban Promosi
            ['category' => 'beban_promosi', 'subcategory' => 'free_talam_rs', 'amount' => 1440000, 'description' => 'Free talam RS. annisa'],
            ['category' => 'beban_promosi', 'subcategory' => 'free_gift_ultah', 'amount' => 1080000, 'description' => 'Free gift ultah'],
            ['category' => 'beban_promosi', 'subcategory' => 'kue_marketing', 'amount' => 836000, 'description' => 'kue keperluan marketing/Pengeluaran Marketing'],
            ['category' => 'beban_promosi', 'subcategory' => 'tester', 'amount' => 820000, 'description' => 'Tester'],
            ['category' => 'beban_promosi', 'subcategory' => 'free_bundling_kuker', 'amount' => 340000, 'description' => 'Free Bundling Kuker'],
            ['category' => 'beban_promosi', 'subcategory' => 'gift_card', 'amount' => 24000, 'description' => 'Gift Card'],
            ['category' => 'beban_promosi', 'subcategory' => 'tim_kreatif_promosi', 'amount' => 23500, 'description' => 'Keperluan tim kreatif/Promosi/ongkir marketing'],

            // Beban Utilitas
            ['category' => 'beban_utilitas', 'subcategory' => 'listrik', 'amount' => 12592784, 'description' => 'Bayar Listrik'],
            ['category' => 'beban_utilitas', 'subcategory' => 'internet_pulsa', 'amount' => 356750, 'description' => 'Bayar Indihome/Pulsa/Paket Telfon'],
            ['category' => 'beban_utilitas', 'subcategory' => 'pest_control', 'amount' => 700000, 'description' => 'Jasa Pengendalian Hama'],
            ['category' => 'beban_utilitas', 'subcategory' => 'kebersihan', 'amount' => 150000, 'description' => 'Uang Kebersihan (Angkut Sampah)/ Uang Ronda'],

            // Pajak
            ['category' => 'pajak', 'subcategory' => 'pajak_bapenda', 'amount' => 1466140, 'description' => 'Bayar PPh/PPN'],

            // BPJS
            ['category' => 'bpjs', 'subcategory' => 'bpjs_kesehatan', 'amount' => 551391, 'description' => 'BPJS Kesehatan'],
            ['category' => 'bpjs', 'subcategory' => 'bpjs_tk', 'amount' => 130469, 'description' => 'BPJS Ketenagakerjaan'],

            // Ongkir
            ['category' => 'ongkir', 'subcategory' => 'ongkir_customer_refund', 'amount' => 371000, 'description' => 'Ongkir Customer/Refund'],
            ['category' => 'ongkir', 'subcategory' => 'fee_supir_bus', 'amount' => 261900, 'description' => 'Fee supir bus'],
            ['category' => 'ongkir', 'subcategory' => 'ongkir_cabang', 'amount' => 206000, 'description' => 'Ongkir Ke Cabang (panam-sudirman)'],
            ['category' => 'ongkir', 'subcategory' => 'kue_supir_bus', 'amount' => 172000, 'description' => 'Kue untuk Supir Bus'],

            // Other
            ['category' => 'other', 'subcategory' => 'pengeluaran_point', 'amount' => 347000, 'description' => 'Pengeluaran Point'],

            // Komisi Bank
            ['category' => 'komisi_bank', 'subcategory' => 'komisi_bank_gojek', 'amount' => ********, 'description' => 'Komisi Bank dan Gojek (debit,qr,gojek,grab)'],

            // Gaji
            ['category' => 'gaji', 'subcategory' => 'gaji_karyawan', 'amount' => ********, 'description' => 'Gaji Karyawan Juni 2025'],
        ];

        foreach ($expenses as $expense) {
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $month->copy()->addDays(rand(1, 28)),
                'description' => $expense['description'],
                'type' => 'expense',
                'expense_category' => $expense['category'],
                'subcategory' => $expense['subcategory'],
                'amount' => $expense['amount'],
                'notes' => 'Data sesuai laporan P&L Juni 2025',
            ]);
        }
    }
}
