<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SalesTransaction;
use App\Models\SaleItem;
use App\Models\Produk;
use App\Services\JournalingService;
use Carbon\Carbon;

class SampleSalesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $journalingService = new JournalingService();

        // Get products
        $products = Produk::with('inventory')->take(3)->get();

        if ($products->count() < 3) {
            $this->command->error('Not enough products found. Please run AccountingSeeder first.');
            return;
        }

        // Sample Sales Transaction 1 - Cash
        $sale1 = SalesTransaction::create([
            'transaction_code' => 'POS-001-' . date('Ymd'),
            'transaction_date' => Carbon::today(),
            'customer_name' => 'John Doe',
            'payment_method' => 'Cash',
            'subtotal' => 0,
            'tax_amount' => 0,
            'discount_amount' => 0,
            'total_amount' => 0,
            'notes' => 'Sample cash transaction',
            'status' => 'Completed',
            'created_by' => 1,
        ]);

        // Add items to sale 1
        $item1 = SaleItem::create([
            'sales_transaction_id' => $sale1->id,
            'product_id' => $products[0]->id,
            'quantity' => 2,
            'unit_price' => $products[0]->selling_price,
            'unit_cost' => $products[0]->unit_cost,
            'total_price' => 2 * $products[0]->selling_price,
            'total_cost' => 2 * $products[0]->unit_cost,
        ]);

        $item2 = SaleItem::create([
            'sales_transaction_id' => $sale1->id,
            'product_id' => $products[1]->id,
            'quantity' => 1,
            'unit_price' => $products[1]->selling_price,
            'unit_cost' => $products[1]->unit_cost,
            'total_price' => 1 * $products[1]->selling_price,
            'total_cost' => 1 * $products[1]->unit_cost,
        ]);

        // Update inventory
        $products[0]->inventory->decrement('quantity', 2);
        $products[1]->inventory->decrement('quantity', 1);

        // Calculate totals
        $subtotal1 = $item1->total_price + $item2->total_price;
        $tax1 = $subtotal1 * 0.11; // 11% PPN
        $total1 = $subtotal1 + $tax1;

        $sale1->update([
            'subtotal' => $subtotal1,
            'tax_amount' => $tax1,
            'total_amount' => $total1,
        ]);

        // Post to journal
        $journalingService->postTransaction('Sale', $sale1);

        // Sample Sales Transaction 2 - Transfer
        $sale2 = SalesTransaction::create([
            'transaction_code' => 'POS-002-' . date('Ymd'),
            'transaction_date' => Carbon::today(),
            'customer_name' => 'Jane Smith',
            'payment_method' => 'Transfer',
            'subtotal' => 0,
            'tax_amount' => 0,
            'discount_amount' => 50000,
            'total_amount' => 0,
            'notes' => 'Sample transfer transaction with discount',
            'status' => 'Completed',
            'created_by' => 1,
        ]);

        // Add item to sale 2
        $item3 = SaleItem::create([
            'sales_transaction_id' => $sale2->id,
            'product_id' => $products[2]->id,
            'quantity' => 3,
            'unit_price' => $products[2]->selling_price,
            'unit_cost' => $products[2]->unit_cost,
            'total_price' => 3 * $products[2]->selling_price,
            'total_cost' => 3 * $products[2]->unit_cost,
        ]);

        // Update inventory
        $products[2]->inventory->decrement('quantity', 3);

        // Calculate totals
        $subtotal2 = $item3->total_price;
        $tax2 = $subtotal2 * 0.11; // 11% PPN
        $total2 = $subtotal2 + $tax2 - $sale2->discount_amount;

        $sale2->update([
            'subtotal' => $subtotal2,
            'tax_amount' => $tax2,
            'total_amount' => $total2,
        ]);

        // Post to journal
        $journalingService->postTransaction('Sale', $sale2);

        $this->command->info('Sample sales transactions created successfully!');
        $this->command->info('Transaction 1: ' . $sale1->transaction_code . ' - Rp ' . number_format($sale1->total_amount));
        $this->command->info('Transaction 2: ' . $sale2->transaction_code . ' - Rp ' . number_format($sale2->total_amount));
    }
}
