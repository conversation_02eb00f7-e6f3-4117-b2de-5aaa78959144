<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class ShieldRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🛡️ Setting up Shield roles and permissions...');

        // Create roles
        $this->createRoles();

        // Assign permissions to roles
        $this->assignPermissions();

        // Setup users with roles
        $this->setupUsers();

        $this->command->info('✅ Shield roles and permissions setup completed!');
    }

    private function createRoles(): void
    {
        $this->command->info('Creating roles...');

        // Super Admin role (already exists from Shield config)
        $superAdminRole = Role::firstOrCreate(['name' => 'super_admin']);

        // Manager HRD role
        $managerHrdRole = Role::firstOrCreate(['name' => 'manager_hrd']);

        // Manager Accounting role
        $managerAccountingRole = Role::firstOrCreate(['name' => 'manager_accounting']);

        // Direktur role
        $direkturRole = Role::firstOrCreate(['name' => 'direktur']);

        // Karyawan role
        $karyawanRole = Role::firstOrCreate(['name' => 'karyawan']);

        $this->command->info('✅ Roles created: super_admin, manager_hrd, manager_accounting, direktur, karyawan');
    }

    private function assignPermissions(): void
    {
        $this->command->info('Assigning permissions to roles...');

        // Get all permissions
        $allPermissions = Permission::all();

        // Super Admin gets all permissions
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $superAdminRole->syncPermissions($allPermissions);
        $this->command->info('✅ Super Admin assigned all permissions');

        // Manager HRD permissions (HR related resources + basic admin permissions)
        $managerHrdRole = Role::where('name', 'manager_hrd')->first();
        $hrdPermissions = $this->getHrdPermissions();
        $managerHrdRole->syncPermissions($hrdPermissions);
        $this->command->info('✅ Manager HRD assigned HR permissions');

        // Manager Accounting permissions (Accounting related resources + basic admin permissions)
        $managerAccountingRole = Role::where('name', 'manager_accounting')->first();
        $accountingPermissions = $this->getAccountingPermissions();
        $managerAccountingRole->syncPermissions($accountingPermissions);
        $this->command->info('✅ Manager Accounting assigned Accounting permissions');

        // Direktur permissions (All permissions like super admin but with specific role)
        $direkturRole = Role::where('name', 'direktur')->first();
        $direkturRole->syncPermissions($allPermissions);
        $this->command->info('✅ Direktur assigned all permissions');

        // Karyawan permissions (Limited permissions for employee self-service)
        $karyawanRole = Role::where('name', 'karyawan')->first();
        $karyawanPermissions = $this->getKaryawanPermissions();
        $karyawanRole->syncPermissions($karyawanPermissions);
        $this->command->info('✅ Karyawan assigned employee self-service permissions');
    }

    private function getHrdPermissions(): array
    {
        $hrdResources = [
            // HR Core Resources
            'karyawan',
            'user',
            'departemen',
            'divisi',
            'jabatan',
            'shift',
            // Attendance & Schedule
            'absensi',
            'jadwal_masal',
            'aturan_keterlambatan',
            // Payroll
            'payroll_component',
            'payroll_period',
            'payroll_transaction',
            'ptkp_rate',
            'tax_bracket',
            // Performance & Projects
            'project',
            'task',
            // Company Settings
            'company_settings',
            // SOP
            'sop_dokumen',
            'jenis_pelanggaran'
        ];

        $hrdPages = [
            'page_HRDashboard',
            'page_AbsensiDashboard',
            'page_PayrollDashboard',
            'page_PerformanceDashboard',
            'page_SupervisorDashboard',
            'page_UserKaryawanManager',
            'page_WorkPeriodSettings',
            'page_ProjectOverview',
            'page_KanbanBoard',
            'page_EnhancedKanbanBoard',
            'page_EnhancedProjectDashboard',
            'page_Timesheets'
        ];

        $hrdWidgets = [
            'widget_HROverviewWidget',
            'widget_HrStatsWidget',
            'widget_HrDepartmentChart',
            'widget_AttendanceOverview',
            'widget_AbsensiTrendChart',
            'widget_AbsensiOverviewWidget',
            'widget_PayrollStatsOverview',
            'widget_PayrollTrendChart',
            'widget_PayrollOverviewWidget',
            'widget_PerformanceGradeChart',
            'widget_PerformanceOverviewWidget',
            'widget_ProjectOverviewStats',
            'widget_ProjectHealthOverview',
            'widget_SalaryChart',
            'widget_SalaryAnalyticsWidget',
            'widget_AttendanceTrendsWidget',
            'widget_EmployeeDemographicsWidget',
            'widget_PayrollTrendsWidget',
            'widget_ProjectManagementWidget',
            'widget_QuickStatsWidget',
            'widget_ScheduleTable',
            'widget_AttendanceByDepartmentWidget',
            'widget_PayrollByDepartmentWidget',
            'widget_PerformanceAnalyticsWidget',
            'widget_ProjectTimelineWidget',
            'widget_RecentActivitiesWidget',
            'widget_TeamPerformanceChart',
            'widget_CompensationBreakdownWidget',
            'widget_HRAlertsWidget',
            'widget_LateArrivalsWidget',
            'widget_AbsensiStatusWidget',
            'widget_PayrollAlertsWidget',
            'widget_RecentProjectActivity',
            'widget_AttendanceAlertsWidget',
            'widget_AttendanceAnalyticsWidget',
            'widget_ProjectResourceAllocation'
        ];

        return $this->buildPermissionsList($hrdResources, $hrdPages, $hrdWidgets);
    }

    private function getAccountingPermissions(): array
    {
        $accountingResources = [
            // Accounting Core
            'akun',
            'journal',
            'posting_rule',
            'entitas',
            // Financial Transactions
            'sales_transaction',
            'purchase_order',
            'expense_request',
            'expense_category',
            'petty_cash_fund',
            // Inventory & Assets
            'inventory',
            'inventory_stock',
            'aset',
            'warehouse',
            'unit',
            'goods_receipt',
            'stock_adjustment',
            'stock_movement',
            'stock_opname',
            'stock_transfer',
            'produk',
            'satuan',
            // Basic HR for payroll integration
            'karyawan',
            'user',
            'departemen',
            'divisi',
            'payroll_component',
            'payroll_period',
            'payroll_transaction',
            // Company Settings
            'company_settings'
        ];

        $accountingPages = [
            'page_GeneralLedger',
            'page_BalanceSheet',
            'page_IncomeStatement',
            'page_InventoryDashboard',
            'page_ImportSales',
            'page_PayrollDashboard'
        ];

        $accountingWidgets = [
            'widget_InventoryStatsWidget',
            'widget_PayrollStatsOverview',
            'widget_PayrollTrendChart',
            'widget_PayrollOverviewWidget',
            'widget_SystemOverviewWidget',
            'widget_QuickStatsWidget',
            'widget_PayrollTrendsWidget',
            'widget_PayrollByDepartmentWidget',
            'widget_CompensationBreakdownWidget',
            'widget_PayrollAlertsWidget',
            'widget_SalaryAnalyticsWidget'
        ];

        return $this->buildPermissionsList($accountingResources, $accountingPages, $accountingWidgets);
    }

    private function getKaryawanPermissions(): array
    {
        // Karyawan resources (limited to self-service and view-only access)
        $karyawanResources = [
            // Self-service HR resources
            'karyawan', // Limited to own profile
            'absensi', // Own attendance records
            'shift', // View assigned shifts
            'task', // Own tasks
            'project', // Projects they're assigned to
            'sop_dokumen', // View SOPs
        ];

        // Karyawan pages (employee self-service pages)
        $karyawanPages = [
            'page_KaryawanProfile', // Own profile page
            'page_AbsensiDashboard', // Own attendance dashboard
            'page_Timesheets', // Own timesheets
            'page_ProjectOverview', // Projects overview
            'page_KanbanBoard', // Task management
        ];

        // Karyawan widgets (limited dashboard widgets)
        $karyawanWidgets = [
            'widget_SystemOverviewWidget',
            'widget_QuickStatsWidget',
        ];

        // Build permissions list with limited access
        $permissions = [];

        // Add limited resource permissions (mainly view permissions)
        foreach ($karyawanResources as $resource) {
            $viewPermissions = Permission::where('name', 'like', "view%{$resource}%")
                ->orWhere('name', 'like', "%{$resource}%view%")
                ->pluck('name')->toArray();
            $permissions = array_merge($permissions, $viewPermissions);
        }

        // Add page permissions
        foreach ($karyawanPages as $page) {
            if (Permission::where('name', $page)->exists()) {
                $permissions[] = $page;
            }
        }

        // Add widget permissions
        foreach ($karyawanWidgets as $widget) {
            if (Permission::where('name', $widget)->exists()) {
                $permissions[] = $widget;
            }
        }

        return array_unique($permissions);
    }

    private function buildPermissionsList(array $resources, array $pages, array $widgets): array
    {
        $permissions = [];

        // Add resource permissions
        foreach ($resources as $resource) {
            $resourcePermissions = Permission::where('name', 'like', "%{$resource}%")->pluck('name')->toArray();
            $permissions = array_merge($permissions, $resourcePermissions);
        }

        // Add page permissions
        foreach ($pages as $page) {
            if (Permission::where('name', $page)->exists()) {
                $permissions[] = $page;
            }
        }

        // Add widget permissions
        foreach ($widgets as $widget) {
            if (Permission::where('name', $widget)->exists()) {
                $permissions[] = $widget;
            }
        }

        return array_unique($permissions);
    }

    private function setupUsers(): void
    {
        $this->command->info('Setting up users with roles...');

        // Get existing admin users
        $adminUsers = User::where('role', 'admin')->take(4)->get();

        if ($adminUsers->count() >= 1) {
            // Assign super_admin to first admin user
            $superAdmin = $adminUsers->first();
            $superAdmin->assignRole('super_admin');
            $this->command->info("✅ Assigned super_admin role to: {$superAdmin->email}");

            if ($adminUsers->count() >= 2) {
                // Assign manager_hrd to second admin user
                $managerHrd = $adminUsers->skip(1)->first();
                $managerHrd->assignRole('manager_hrd');
                $this->command->info("✅ Assigned manager_hrd role to: {$managerHrd->email}");
            }

            if ($adminUsers->count() >= 3) {
                // Assign manager_accounting to third admin user
                $managerAccounting = $adminUsers->skip(2)->first();
                $managerAccounting->assignRole('manager_accounting');
                $this->command->info("✅ Assigned manager_accounting role to: {$managerAccounting->email}");
            }

            if ($adminUsers->count() >= 4) {
                // Assign direktur to fourth admin user
                $direktur = $adminUsers->skip(3)->first();
                $direktur->assignRole('direktur');
                $this->command->info("✅ Assigned direktur role to: {$direktur->email}");
            }
        }

        // If not enough admin users, create them
        if ($adminUsers->count() < 4) {
            $this->createMissingManagerUsers($adminUsers->count());
        }

        // Assign karyawan role to existing karyawan users
        $this->assignKaryawanRole();
    }

    private function createMissingManagerUsers(int $existingCount): void
    {
        $roles = ['super_admin', 'manager_hrd', 'manager_accounting', 'direktur'];

        for ($i = $existingCount; $i < 4; $i++) {
            $roleName = $roles[$i];
            $email = match ($roleName) {
                'super_admin' => '<EMAIL>',
                'manager_hrd' => '<EMAIL>',
                'manager_accounting' => '<EMAIL>',
                'direktur' => '<EMAIL>',
                default => '<EMAIL>'
            };

            $user = User::create([
                'name' => ucfirst(str_replace('_', ' ', $roleName)),
                'email' => $email,
                'password' => Hash::make('password'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]);

            $user->assignRole($roleName);
            $this->command->info("✅ Created and assigned {$roleName} role to: {$user->email}");
        }
    }

    private function assignKaryawanRole(): void
    {
        $this->command->info('Assigning karyawan role to existing karyawan users...');

        // Get all users with role 'karyawan' who don't already have the Shield karyawan role
        $karyawanUsers = User::where('role', 'karyawan')
            ->whereDoesntHave('roles', function ($query) {
                $query->where('name', 'karyawan');
            })
            ->get();

        if ($karyawanUsers->count() > 0) {
            foreach ($karyawanUsers as $user) {
                $user->assignRole('karyawan');
                $this->command->info("✅ Assigned karyawan role to: {$user->email}");
            }
            $this->command->info("✅ Assigned karyawan role to {$karyawanUsers->count()} users");
        } else {
            $this->command->info('ℹ️ No karyawan users found or all already have the karyawan role');
        }
    }
}
