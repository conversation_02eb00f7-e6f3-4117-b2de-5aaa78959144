<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Supplier;
use App\Models\Akun;

class SupplierSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Get or create Accounts Payable account
        $accountsPayable = Akun::where('kode_akun', '2001')->first();
        if (!$accountsPayable) {
            $accountsPayable = Akun::create([
                'kode_akun' => '2001',
                'nama_akun' => 'Hutang Dagang',
                'kategori_akun' => 'Kewajiban',
                'tipe_akun' => 'Credit',
                'saldo_awal' => 0,
                'created_by' => 1,
            ]);
        }

        $suppliers = [
            [
                'nama' => 'PT Sumber Makmur',
                'nama_perusahaan' => 'PT Sumber Makmur Sejahtera',
                'email' => '<EMAIL>',
                'nomor_handphone' => '021-********',
                'npwp' => '01.234.567.8-901.000',
                'alamat' => 'Jl. Industri Raya No. 123, Jakarta Timur',
                'info_lainnya' => 'Supplier utama untuk bahan baku',
                'akun_bank' => 'BCA',
                'nama_bank' => 'Bank Central Asia',
                'kantor_cabang_bank' => 'KCP Jakarta Timur',
                'nomor_rekening' => '********90',
                'pemegang_akun_bank' => 'PT Sumber Makmur Sejahtera',
                'id_akun_hutang' => $accountsPayable->id,
                'syarat_pembayaran_utama' => 'Net 30',
                'created_by' => 1,
            ],
            [
                'nama' => 'CV Mitra Jaya',
                'nama_perusahaan' => 'CV Mitra Jaya Abadi',
                'email' => '<EMAIL>',
                'nomor_handphone' => '021-********',
                'npwp' => '02.345.678.9-012.000',
                'alamat' => 'Jl. Perdagangan No. 456, Jakarta Barat',
                'info_lainnya' => 'Supplier peralatan kantor',
                'akun_bank' => 'Mandiri',
                'nama_bank' => 'Bank Mandiri',
                'kantor_cabang_bank' => 'KCP Jakarta Barat',
                'nomor_rekening' => '**********',
                'pemegang_akun_bank' => 'CV Mitra Jaya Abadi',
                'id_akun_hutang' => $accountsPayable->id,
                'syarat_pembayaran_utama' => 'Net 15',
                'created_by' => 1,
            ],
            [
                'nama' => 'PT Teknologi Maju',
                'nama_perusahaan' => 'PT Teknologi Maju Indonesia',
                'email' => '<EMAIL>',
                'nomor_handphone' => '021-********',
                'npwp' => '03.456.789.0-123.000',
                'alamat' => 'Jl. Teknologi No. 789, Jakarta Selatan',
                'info_lainnya' => 'Supplier teknologi dan elektronik',
                'akun_bank' => 'BNI',
                'nama_bank' => 'Bank Negara Indonesia',
                'kantor_cabang_bank' => 'KCP Jakarta Selatan',
                'nomor_rekening' => '**********',
                'pemegang_akun_bank' => 'PT Teknologi Maju Indonesia',
                'id_akun_hutang' => $accountsPayable->id,
                'syarat_pembayaran_utama' => 'COD',
                'created_by' => 1,
            ],
            [
                'nama' => 'UD Berkah Jaya',
                'nama_perusahaan' => 'UD Berkah Jaya Sentosa',
                'email' => '<EMAIL>',
                'nomor_handphone' => '021-********',
                'npwp' => '04.567.890.1-234.000',
                'alamat' => 'Jl. Dagang Raya No. 321, Jakarta Utara',
                'info_lainnya' => 'Supplier bahan makanan dan minuman',
                'akun_bank' => 'BRI',
                'nama_bank' => 'Bank Rakyat Indonesia',
                'kantor_cabang_bank' => 'KCP Jakarta Utara',
                'nomor_rekening' => '**********',
                'pemegang_akun_bank' => 'UD Berkah Jaya Sentosa',
                'id_akun_hutang' => $accountsPayable->id,
                'syarat_pembayaran_utama' => 'Net 45',
                'created_by' => 1,
            ],
            [
                'nama' => 'PT Global Supply',
                'nama_perusahaan' => 'PT Global Supply Chain',
                'email' => '<EMAIL>',
                'nomor_handphone' => '021-********',
                'npwp' => '05.678.901.2-345.000',
                'alamat' => 'Jl. Logistik No. 654, Tangerang',
                'info_lainnya' => 'Supplier import dan logistik',
                'akun_bank' => 'CIMB',
                'nama_bank' => 'CIMB Niaga',
                'kantor_cabang_bank' => 'KCP Tangerang',
                'nomor_rekening' => '**********',
                'pemegang_akun_bank' => 'PT Global Supply Chain',
                'id_akun_hutang' => $accountsPayable->id,
                'syarat_pembayaran_utama' => '2/10 Net 30',
                'created_by' => 1,
            ],
        ];

        foreach ($suppliers as $supplierData) {
            Supplier::create($supplierData);
        }

        $this->command->info('Created ' . count($suppliers) . ' suppliers successfully!');
    }
}
