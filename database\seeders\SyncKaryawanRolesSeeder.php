<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\DB;

class SyncKaryawanRolesSeeder extends Seeder
{
    /**
     * Sync users with role='karyawan' to have Shield karyawan role
     */
    public function run(): void
    {
        $this->command->info('Starting to sync karyawan roles...');

        // Get the Shield karyawan role
        $karyawanRole = Role::where('name', 'karyawan')->first();
        
        if (!$karyawanRole) {
            $this->command->error('Shield karyawan role not found! Please run ShieldRoleSeeder first.');
            return;
        }

        // Get all users with role='karyawan' in users table
        $karyawanUsers = User::where('role', 'karyawan')->get();
        
        $this->command->info("Found {$karyawanUsers->count()} users with role='karyawan'");

        $synced = 0;
        $alreadyHad = 0;

        foreach ($karyawanUsers as $user) {
            // Check if user already has the Shield karyawan role
            if (!$user->hasRole('karyawan')) {
                // Assign the Shield karyawan role
                $user->assignRole('karyawan');
                $synced++;
                $this->command->info("✓ Assigned Shield karyawan role to: {$user->name} ({$user->email})");
            } else {
                $alreadyHad++;
                $this->command->comment("- {$user->name} already has Shield karyawan role");
            }
        }

        $this->command->info("\n=== SYNC COMPLETE ===");
        $this->command->info("Users synced: {$synced}");
        $this->command->info("Users already had role: {$alreadyHad}");
        $this->command->info("Total karyawan users: {$karyawanUsers->count()}");

        // Verify the sync
        $this->verifySync();
    }

    /**
     * Verify that all users with role='karyawan' have Shield karyawan role
     */
    private function verifySync(): void
    {
        $this->command->info("\n=== VERIFICATION ===");
        
        $karyawanUsers = User::where('role', 'karyawan')->get();
        $missingShieldRole = [];

        foreach ($karyawanUsers as $user) {
            if (!$user->hasRole('karyawan')) {
                $missingShieldRole[] = $user->email;
            }
        }

        if (empty($missingShieldRole)) {
            $this->command->info("✅ All users with role='karyawan' now have Shield karyawan role!");
        } else {
            $this->command->error("❌ These users still missing Shield karyawan role:");
            foreach ($missingShieldRole as $email) {
                $this->command->error("  - {$email}");
            }
        }

        // Show current karyawan role permissions
        $karyawanRole = Role::where('name', 'karyawan')->first();
        if ($karyawanRole) {
            $permissions = $karyawanRole->permissions->pluck('name')->toArray();
            $this->command->info("\nKaryawan role has " . count($permissions) . " permissions:");
            
            // Show key permissions
            $keyPermissions = ['view_absensi', 'create_absensi', 'view_karyawan', 'view_schedule'];
            foreach ($keyPermissions as $perm) {
                $hasIt = in_array($perm, $permissions) ? '✅' : '❌';
                $this->command->info("  {$hasIt} {$perm}");
            }
        }
    }
}
