<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Outlet;
use App\Models\DailyTransaction;
use Carbon\Carbon;

class TestReportDataSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create basic outlets
        $outlets = [
            [
                'name' => 'Viera Oleh-oleh <PERSON>dirman',
                'category' => 'retail',
                'address' => 'Jalan Sudirman',
                'description' => 'Cabang Sudirman',
                'is_active' => true,
            ],
            [
                'name' => 'VOO 1',
                'category' => 'retail',
                'address' => 'Jalan VOO 1',
                'description' => 'VOO Outlet 1',
                'is_active' => true,
            ],
        ];

        foreach ($outlets as $outletData) {
            Outlet::firstOrCreate(
                ['name' => $outletData['name']],
                $outletData
            );
        }

        // Create some test transactions
        $outlet = Outlet::first();
        if ($outlet) {
            $month = Carbon::now();
            
            // Revenue transactions
            DailyTransaction::firstOrCreate([
                'outlet_id' => $outlet->id,
                'transaction_date' => $month->copy()->subDays(5),
                'description' => 'Test Revenue',
                'type' => 'revenue',
                'payment_method' => 'pendapatan_cash',
                'amount' => 1000000,
                'notes' => 'Test data',
            ]);

            // Expense transactions
            DailyTransaction::firstOrCreate([
                'outlet_id' => $outlet->id,
                'transaction_date' => $month->copy()->subDays(3),
                'description' => 'Test Expense',
                'type' => 'expense',
                'expense_category' => 'beban_ga',
                'subcategory' => 'belanja_ga',
                'amount' => 500000,
                'notes' => 'Test data',
            ]);
        }

        $this->command->info('Test report data created successfully!');
    }
}
