<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\TransactionCategory;
use App\Models\TransactionSubcategory;

class TransactionCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Revenue Categories
        $revenueCategories = [
            [
                'code' => 'pendapatan',
                'name' => 'Pendapatan',
                'type' => 'revenue',
                'description' => 'Semua jenis pendapatan penjualan',
            ],
        ];

        // Expense Categories
        $expenseCategories = [
            [
                'code' => 'beban_bahan_baku',
                'name' => 'Beban Bahan Baku',
                'type' => 'expense',
                'description' => 'Biaya untuk bahan baku dan supplier',
            ],
            [
                'code' => 'beban_ga',
                'name' => 'Beban GA (General Affairs)',
                'type' => 'expense',
                'description' => 'Biaya operasional umum',
            ],
            [
                'code' => 'beban_promosi',
                'name' => 'Beban Promosi',
                'type' => 'expense',
                'description' => 'Biaya marketing dan promosi',
            ],
            [
                'code' => 'beban_utilitas',
                'name' => 'Beban Utilitas',
                'type' => 'expense',
                'description' => 'Biaya listrik, air, internet, dll',
            ],
            [
                'code' => 'pajak',
                'name' => 'Pajak',
                'type' => 'expense',
                'description' => 'Pembayaran pajak',
            ],
            [
                'code' => 'ongkir',
                'name' => 'Ongkos Kirim',
                'type' => 'expense',
                'description' => 'Biaya pengiriman',
            ],
            [
                'code' => 'bpjs',
                'name' => 'BPJS',
                'type' => 'expense',
                'description' => 'Pembayaran BPJS',
            ],
            [
                'code' => 'komisi_bank',
                'name' => 'Komisi Bank',
                'type' => 'expense',
                'description' => 'Biaya komisi bank dan payment gateway',
            ],
            [
                'code' => 'gaji',
                'name' => 'Gaji Karyawan',
                'type' => 'expense',
                'description' => 'Pembayaran gaji dan tunjangan',
            ],
            [
                'code' => 'other',
                'name' => 'Lainnya',
                'type' => 'expense',
                'description' => 'Pengeluaran lain-lain',
            ],
        ];

        // Create categories
        foreach (array_merge($revenueCategories, $expenseCategories) as $categoryData) {
            TransactionCategory::updateOrCreate(
                ['code' => $categoryData['code']],
                $categoryData
            );
        }

        // Create subcategories
        $this->createSubcategories();

        $this->command->info('Transaction categories and subcategories created successfully!');
    }

    private function createSubcategories(): void
    {
        $subcategoriesData = [
            // Revenue subcategories
            'pendapatan' => [
                ['code' => 'pendapatan_cash', 'name' => 'Pendapatan Cash'],
                ['code' => 'pendapatan_debit', 'name' => 'Pendapatan Debit'],
                ['code' => 'pendapatan_transfer', 'name' => 'Pendapatan Transfer'],
                ['code' => 'pendapatan_qris', 'name' => 'Pendapatan QRIS'],
                ['code' => 'pendapatan_gojek', 'name' => 'Pendapatan Gojek'],
                ['code' => 'pendapatan_grab_ovo', 'name' => 'Pendapatan Grab Ovo'],
                ['code' => 'pendapatan_sewa_rak', 'name' => 'Pendapatan Sewa Rak'],
            ],
            // Beban Bahan Baku subcategories
            'beban_bahan_baku' => [
                ['code' => 'tagihan_rkv', 'name' => 'Tagihan RKV'],
                ['code' => 'tagihan_mitra', 'name' => 'Tagihan Mitra'],
                ['code' => 'tagihan_supplier', 'name' => 'Tagihan Supplier'],
                ['code' => 'bahan_baku_lainnya', 'name' => 'Bahan Baku Lainnya'],
            ],
            // Beban GA subcategories
            'beban_ga' => [
                ['code' => 'material_bangunan', 'name' => 'Material Bangunan/Kabel/Bayar Tukang'],
                ['code' => 'service_oven_freezer_packing', 'name' => 'Service Oven dan Freezer/Mobil/AC/Mesin Packing/Genset'],
                ['code' => 'service_equipment', 'name' => 'Service Freezer/Mobil/AC/Motor/CCTV/dll'],
                ['code' => 'belanja_ga', 'name' => 'Belanja GA'],
                ['code' => 'cuci_mobil_oli', 'name' => 'Cuci Mobil/Isi Angin/Tambal Ban/Ganti Oli'],
                ['code' => 'kertas_thermal', 'name' => 'Kertas Thermal/Kertas Label'],
                ['code' => 'keperluan_genset', 'name' => 'Keperluan Genset/Dexlite Genset'],
                ['code' => 'bensin_luxio_putih', 'name' => 'Bensin Luxio Putih'],
                ['code' => 'bensin_luxio_silver', 'name' => 'Bensin Luxio Silver'],
                ['code' => 'belanja_aset_ga', 'name' => 'Belanja Aset GA'],
                ['code' => 'harau_sticker_banner', 'name' => 'Harau Sticker/Banner'],
                ['code' => 'service_kompor_freezer', 'name' => 'Service Kompor dan Freezer/Mobil/AC/Motor'],
                ['code' => 'bensin_luxio_baru', 'name' => 'Bensin Luxio Baru'],
                ['code' => 'konten_video_foto', 'name' => 'Konten Video/Foto Produk'],
                ['code' => 'lppom_mui', 'name' => 'Biaya Pengurusan LPPOM MUI RIAU'],
                ['code' => 'konsumsi_meeting', 'name' => 'Konsumsi Meeting Direktur/Chef Deden'],
                ['code' => 'ga_lainnya', 'name' => 'GA Lainnya'],
            ],
            // Beban Promosi subcategories
            'beban_promosi' => [
                ['code' => 'free_talam_rs', 'name' => 'Free talam RS. annisa'],
                ['code' => 'free_gift_ultah', 'name' => 'Free gift ultah'],
                ['code' => 'kue_marketing', 'name' => 'kue keperluan marketing/Pengeluaran Marketing'],
                ['code' => 'tester', 'name' => 'Tester'],
                ['code' => 'free_bundling_kuker', 'name' => 'Free Bundling Kuker'],
                ['code' => 'gift_card', 'name' => 'Gift Card'],
                ['code' => 'tim_kreatif_promosi', 'name' => 'Keperluan tim kreatif/Promosi/ongkir marketing'],
                ['code' => 'free_ultah', 'name' => 'Free Ultah'],
                ['code' => 'free_bundling', 'name' => 'Free Paket Bundling'],
                ['code' => 'marketing_crm', 'name' => 'Pengeluaran Marketing/CRM'],
                ['code' => 'free_talam', 'name' => 'Free Talam'],
                ['code' => 'promosi_lainnya', 'name' => 'Promosi Lainnya'],
            ],
            // Beban Utilitas subcategories
            'beban_utilitas' => [
                ['code' => 'listrik', 'name' => 'Bayar Listrik'],
                ['code' => 'internet_pulsa', 'name' => 'Bayar Indihome/Pulsa/Paket Telepon'],
                ['code' => 'pest_control', 'name' => 'Jasa Pengendalian Hama (Petsco)'],
                ['code' => 'kebersihan', 'name' => 'Uang Kebersihan/Uang Ronda/PDAM'],
                ['code' => 'air_pdam', 'name' => 'Air PDAM'],
                ['code' => 'gas_lpg', 'name' => 'Gas LPG'],
                ['code' => 'utilitas_lainnya', 'name' => 'Utilitas Lainnya'],
            ],
            // Pajak subcategories
            'pajak' => [
                ['code' => 'pajak_bapenda', 'name' => 'Bayar Pajak/Bapenda/Fee Pajak'],
                ['code' => 'pph', 'name' => 'PPh'],
                ['code' => 'ppn', 'name' => 'PPN'],
                ['code' => 'pbb', 'name' => 'PBB'],
                ['code' => 'pajak_lainnya', 'name' => 'Pajak Lainnya'],
            ],
            // Ongkir subcategories
            'ongkir' => [
                ['code' => 'ongkir_customer_refund', 'name' => 'Ongkir Customer/Refund'],
                ['code' => 'fee_supir_bus', 'name' => 'Fee supir bus'],
                ['code' => 'ongkir_cabang', 'name' => 'Ongkir Ke Cabang (panam-sudirman)'],
                ['code' => 'kue_supir_bus', 'name' => 'Kue untuk Supir Bus'],
                ['code' => 'ongkir_customer', 'name' => 'Ongkir Customer'],
                ['code' => 'ongkir_lainnya', 'name' => 'Ongkir Lainnya'],
            ],
            // BPJS subcategories
            'bpjs' => [
                ['code' => 'bpjs_kesehatan', 'name' => 'BPJS Kesehatan'],
                ['code' => 'bpjs_tk', 'name' => 'Bayar BPJS TK'],
            ],
            // Komisi Bank subcategories
            'komisi_bank' => [
                ['code' => 'komisi_bank_gojek', 'name' => 'Komisi Bank dan Gojek (Debit,QR,Gojek)'],
                ['code' => 'komisi_debit', 'name' => 'Komisi Debit'],
                ['code' => 'komisi_qr', 'name' => 'Komisi QR'],
                ['code' => 'komisi_gojek', 'name' => 'Komisi Gojek'],
                ['code' => 'komisi_grab', 'name' => 'Komisi Grab'],
                ['code' => 'komisi_lainnya', 'name' => 'Komisi Lainnya'],
            ],
            // Gaji subcategories
            'gaji' => [
                ['code' => 'gaji_karyawan', 'name' => 'Gaji Karyawan'],
                ['code' => 'gaji_part_time', 'name' => 'Gaji Part Time/Freelance'],
                ['code' => 'gaji_tetap', 'name' => 'Gaji Tetap'],
                ['code' => 'gaji_harian', 'name' => 'Gaji Harian'],
                ['code' => 'bonus', 'name' => 'Bonus'],
                ['code' => 'tunjangan', 'name' => 'Tunjangan'],
                ['code' => 'gaji_lainnya', 'name' => 'Gaji Lainnya'],
            ],
            // Other subcategories
            'other' => [
                ['code' => 'pengeluaran_point', 'name' => 'Pengeluaran Point'],
                ['code' => 'denda_keterlambatan', 'name' => 'Denda Keterlambatan'],
                ['code' => 'biaya_administrasi', 'name' => 'Biaya Administrasi Bank'],
                ['code' => 'donasi_csr', 'name' => 'Donasi/CSR'],
                ['code' => 'training_karyawan', 'name' => 'Training Karyawan'],
                ['code' => 'lain_lain', 'name' => 'Lain-lain'],
            ],
        ];

        foreach ($subcategoriesData as $categoryCode => $subcategories) {
            $category = TransactionCategory::where('code', $categoryCode)->first();
            if ($category) {
                foreach ($subcategories as $subcategoryData) {
                    TransactionSubcategory::updateOrCreate(
                        [
                            'category_id' => $category->id,
                            'code' => $subcategoryData['code']
                        ],
                        [
                            'name' => $subcategoryData['name'],
                            'is_active' => true,
                        ]
                    );
                }
            }
        }
    }
}
