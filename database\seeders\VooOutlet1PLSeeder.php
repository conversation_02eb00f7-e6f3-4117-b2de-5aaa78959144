<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\DailyTransaction;
use App\Models\Outlet;
use Carbon\Carbon;

class VooOutlet1PLSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Find or create VOO 1 outlet
        $outlet = Outlet::firstOrCreate(
            ['name' => 'VOO 1'],
            [
                'address' => 'Jalan VOO 1',
                'phone' => '081234567890',
                'category' => 'retail',
                'is_active' => true,
            ]
        );

        $month = Carbon::create(2025, 6, 1); // Juni 2025

        // Clear existing data for this outlet
        DailyTransaction::where('outlet_id', $outlet->id)->delete();

        $this->createRevenueTransactions($outlet, $month);
        $this->createExpenseTransactions($outlet, $month);

        $this->command->info('VOO 1 Outlet P&L data created successfully!');
    }

    private function createRevenueTransactions(Outlet $outlet, Carbon $month)
    {
        $revenues = [
            ['payment_method' => 'pendapatan_cash', 'amount' => 454095300, 'description' => 'Pendapatan Cash'],
            ['payment_method' => 'pendapatan_debit', 'amount' => 83855600, 'description' => 'Pendapatan Debit'],
            ['payment_method' => 'pendapatan_transfer', 'amount' => 19327200, 'description' => 'Pendapatan Transfer'],
            ['payment_method' => 'pendapatan_qris', 'amount' => 288571350, 'description' => 'Pendapatan QRIS'],
            ['payment_method' => 'pendapatan_gojek', 'amount' => 55519500, 'description' => 'Pendapatan Gojek'],
            ['payment_method' => 'pendapatan_grab_ovo', 'amount' => 6866000, 'description' => 'Pendapatan Grab Ovo'],
            ['payment_method' => 'pendapatan_sewa_rak', 'amount' => 574000, 'description' => 'Pendapatan Sewa Rak'],
        ];

        foreach ($revenues as $revenue) {
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $month->copy()->addDays(rand(1, 28)),
                'description' => $revenue['description'],
                'type' => 'revenue',
                'payment_method' => $revenue['payment_method'],
                'amount' => $revenue['amount'],
                'notes' => 'Data VOO 1 sesuai laporan P&L Juni 2025',
            ]);
        }
    }

    private function createExpenseTransactions(Outlet $outlet, Carbon $month)
    {
        $expenses = [
            // Beban Bahan Baku
            ['category' => 'beban_bahan_baku', 'subcategory' => 'tagihan_rkv', 'amount' => 300000000, 'description' => 'Tagihan Rkv'],
            ['category' => 'beban_bahan_baku', 'subcategory' => 'tagihan_mitra', 'amount' => 224142500, 'description' => 'Tagihan Mitra Juni 2025'],
            ['category' => 'beban_bahan_baku', 'subcategory' => 'tagihan_supplier', 'amount' => 49955458, 'description' => 'Tagihan supplier depan'],

            // Beban GA
            ['category' => 'beban_ga', 'subcategory' => 'material_bangunan', 'amount' => 1330000, 'description' => 'Material Bangunan / Kabel/ Bayar Tukang /Instalasi'],
            ['category' => 'beban_ga', 'subcategory' => 'kertas_thermal', 'amount' => 525000, 'description' => 'Kertas Thermal/Kertas Label'],
            ['category' => 'beban_ga', 'subcategory' => 'belanja_ga', 'amount' => 500000, 'description' => 'Belanja GA'],
            ['category' => 'beban_ga', 'subcategory' => 'bensin_luxio_silver', 'amount' => 2867000, 'description' => 'Bensin luxio silver'],
            ['category' => 'beban_ga', 'subcategory' => 'keperluan_genset', 'amount' => 940880, 'description' => 'Keperluan Genset/ Dexlite Genset/cas aki genset'],

            // Beban Promosi
            ['category' => 'beban_promosi', 'subcategory' => 'free_ultah', 'amount' => 855000, 'description' => 'free ultah'],
            ['category' => 'beban_promosi', 'subcategory' => 'free_bundling_kuker', 'amount' => 736000, 'description' => 'Free Paket Bundling kuker'],
            ['category' => 'beban_promosi', 'subcategory' => 'marketing_crm', 'amount' => 355000, 'description' => 'Pengeluaran Marketing/CRM'],
            ['category' => 'beban_promosi', 'subcategory' => 'free_talam_rs', 'amount' => 270000, 'description' => 'Free talam RS. annisa'],
            ['category' => 'beban_promosi', 'subcategory' => 'tester', 'amount' => 215000, 'description' => 'Tester'],
            ['category' => 'beban_promosi', 'subcategory' => 'gift_card', 'amount' => 34000, 'description' => 'Gift Card'],

            // Beban Utilitas
            ['category' => 'beban_utilitas', 'subcategory' => 'listrik', 'amount' => 7224654, 'description' => 'Bayar Listrik'],
            ['category' => 'beban_utilitas', 'subcategory' => 'internet_pulsa', 'amount' => 324700, 'description' => 'Bayar Indihome/Pulsa/Paket Telfon'],
            ['category' => 'beban_utilitas', 'subcategory' => 'pest_control', 'amount' => 700000, 'description' => 'Jasa Pengendalian Hama (Petsco)'],
            ['category' => 'beban_utilitas', 'subcategory' => 'kebersihan', 'amount' => 150000, 'description' => 'Uang Kebersihan (Angkut Sampah)/ Uang Ronda/PDAM'],

            // Pajak
            ['category' => 'pajak', 'subcategory' => 'pajak_bapenda', 'amount' => 7468491, 'description' => 'Bayar PPh/PPN/PBB'],

            // Ongkir
            ['category' => 'ongkir', 'subcategory' => 'ongkir_customer', 'amount' => 1220000, 'description' => 'Ongkir customer'],
            ['category' => 'ongkir', 'subcategory' => 'ongkir_cabang', 'amount' => 257000, 'description' => 'Ongkir ke cabang (panam-riau)'],

            // BPJS
            ['category' => 'bpjs', 'subcategory' => 'bpjs_kesehatan', 'amount' => 918985, 'description' => 'BPJS Kesehatan'],
            ['category' => 'bpjs', 'subcategory' => 'bpjs_tk', 'amount' => 149108, 'description' => 'BPJS TK'],

            // Other
            ['category' => 'other', 'subcategory' => 'pengeluaran_point', 'amount' => 41000, 'description' => 'Pengeluaran Point'],

            // Komisi Bank
            ['category' => 'komisi_bank', 'subcategory' => 'komisi_bank_gojek', 'amount' => ********, 'description' => 'Komisi Bank dan Gojek (debit,qr,gojek,grab)'],

            // Gaji
            ['category' => 'gaji', 'subcategory' => 'gaji_karyawan', 'amount' => ********, 'description' => 'Gaji karyawan Juni 2025'],
        ];

        foreach ($expenses as $expense) {
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $month->copy()->addDays(rand(1, 28)),
                'description' => $expense['description'],
                'type' => 'expense',
                'expense_category' => $expense['category'],
                'subcategory' => $expense['subcategory'],
                'amount' => $expense['amount'],
                'notes' => 'Data VOO 1 sesuai laporan P&L Juni 2025',
            ]);
        }
    }
}
