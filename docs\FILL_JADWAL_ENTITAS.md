# Fill Jadwal Entitas Seeder

Seeder ini digunakan untuk mengisi `entitas_id` yang kosong di tabel `schedules` (jadwal kerja) dan `jadwal_masal` berdasarkan `entitas_id` dari karyawan yang bersangkutan.

## 🎯 Tujuan

Sebelum implementasi prioritas entitas di absensi, banyak jadwal yang tidak memiliki `entitas_id`. Seeder ini akan:

1. **Mengisi `entitas_id` di tabel `schedules`** berdasarkan `entitas_id` karyawan
2. **Mengisi `entitas_id` di tabel `jadwal_masal`** berdasarkan `entitas_id` karyawan
3. **Memberikan laporan lengkap** tentang proses update
4. **Menangani error** dengan graceful dan logging

## 🚀 Cara Penggunaan

### 1. Preview Mode (Dry Run)
Untuk melihat data yang akan diupdate tanpa melakukan perubahan:

```bash
php artisan jadwal:fill-entitas --dry-run
```

### 2. Update dengan Konfirmasi
Menjalankan update dengan konfirmasi terlebih dahulu:

```bash
php artisan jadwal:fill-entitas
```

### 3. Update Tanpa Konfirmasi
Menjalankan update langsung tanpa konfirmasi:

```bash
php artisan jadwal:fill-entitas --force
```

### 4. Menggunakan Seeder Langsung
Jika ingin menggunakan seeder langsung:

```bash
php artisan db:seed --class=FillJadwalEntitasSeeder
```

## 📊 Output yang Dihasilkan

### Statistik Awal
```
📊 Statistik Awal:
+-------------+---------------+---------------+---------------+
| Tabel       | Total Records | Tanpa Entitas | Perlu Diupdate|
+-------------+---------------+---------------+---------------+
| Schedule    | 1250          | 800           | 800           |
| JadwalMasal | 500           | 300           | 300           |
+-------------+---------------+---------------+---------------+
```

### Progress Update
```
📅 Mengisi entitas_id untuk Schedule (jadwal kerja)...
📊 Ditemukan 800 schedule tanpa entitas_id
   ⏳ Progress: 100 schedule telah diupdate...
   ⏳ Progress: 200 schedule telah diupdate...
   ...
   ✅ Schedule: 750 berhasil diupdate, 50 dilewati, 0 error
```

### Statistik Akhir
```
📈 Statistik Final:
+-------------+-------+---------------+---------------+-------------------+
| Tabel       | Total | Dengan Entitas| Tanpa Entitas | Persentase Terisi |
+-------------+-------+---------------+---------------+-------------------+
| Schedule    | 1250  | 1200          | 50            | 96.00%            |
| JadwalMasal | 500   | 470           | 30            | 94.00%            |
+-------------+-------+---------------+---------------+-------------------+
```

## 🔍 Logika Update

### Untuk Schedule (Jadwal Kerja)
```sql
UPDATE schedules 
SET entitas_id = (
    SELECT entitas_id 
    FROM karyawans 
    WHERE karyawans.id = schedules.karyawan_id
) 
WHERE schedules.entitas_id IS NULL 
AND schedules.karyawan_id IS NOT NULL;
```

### Untuk JadwalMasal
```sql
UPDATE jadwal_masal 
SET entitas_id = (
    SELECT entitas_id 
    FROM karyawans 
    WHERE karyawans.id = jadwal_masal.karyawan_id
) 
WHERE jadwal_masal.entitas_id IS NULL 
AND jadwal_masal.karyawan_id IS NOT NULL;
```

## ⚠️ Kondisi yang Dilewati

Record akan dilewati (tidak diupdate) jika:

1. **Karyawan tidak ditemukan** - `karyawan_id` tidak valid
2. **Karyawan tidak memiliki entitas** - `karyawan.entitas_id` adalah null
3. **Relasi karyawan tidak ter-load** - Ada masalah dengan relasi database

## 📝 Logging

Semua aktivitas dicatat dalam log Laravel:

### Warning Log
```php
Log::warning("Schedule ID {$schedule->id}: Karyawan tidak memiliki entitas_id", [
    'schedule_id' => $schedule->id,
    'karyawan_id' => $schedule->karyawan_id,
    'karyawan_name' => $schedule->karyawan?->nama_lengkap ?? 'Unknown'
]);
```

### Error Log
```php
Log::error("Error updating Schedule ID {$schedule->id}: " . $e->getMessage(), [
    'schedule_id' => $schedule->id,
    'karyawan_id' => $schedule->karyawan_id,
    'error' => $e->getMessage()
]);
```

## 🛡️ Safety Features

1. **Dry Run Mode** - Preview sebelum update
2. **Konfirmasi User** - Mencegah update tidak sengaja
3. **Transaction Safety** - Setiap update dalam try-catch
4. **Progress Tracking** - Menampilkan progress setiap 100 records
5. **Comprehensive Logging** - Log semua warning dan error
6. **Statistik Lengkap** - Laporan sebelum dan sesudah update

## 🔧 Troubleshooting

### Jika Ada Error "Class not found"
```bash
composer dump-autoload
```

### Jika Ada Error Database Connection
Pastikan konfigurasi database di `.env` sudah benar.

### Jika Banyak Record Dilewati
Periksa log untuk melihat karyawan mana yang tidak memiliki `entitas_id`:

```bash
tail -f storage/logs/laravel.log | grep "tidak memiliki entitas_id"
```

## 📈 Manfaat Setelah Update

1. **Absensi Multi-Lokasi** - Karyawan bisa absen di lokasi sesuai jadwal
2. **Validasi Lokasi Akurat** - Radius validasi berdasarkan entitas jadwal
3. **Reporting yang Tepat** - Data absensi terkait dengan lokasi kerja yang benar
4. **Fleksibilitas Kerja** - Mendukung sistem kerja multi-entitas

## 🎯 Rekomendasi

1. **Jalankan Dry Run** terlebih dahulu untuk melihat preview
2. **Backup Database** sebelum menjalankan update
3. **Jalankan di Off-Peak Hours** untuk menghindari gangguan
4. **Monitor Log** selama dan setelah proses update
5. **Verifikasi Hasil** dengan mengecek beberapa record secara manual
