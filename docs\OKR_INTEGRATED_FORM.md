# OKR Integrated Form Documentation

## Overview

Form Objective OKR telah diintegrasikan dengan Key Results, Tactics, dan Tasks dalam satu form yang lebih seamless dan user-friendly.

## Fitur Utama

### 1. Form Terintegrasi

-   **Objective Information**: Informasi dasar objective
-   **Key Results**: Definisi hasil kunci yang terukur
-   **Tactics**: Strategi dan taktik untuk mencapai objective
-   **Tasks**: Hubungan dengan task-task yang sudah ada

### 2. Key Results Section

-   **Validasi Bobot**: Sistem akan memberikan peringatan jika total bobot Key Results melebihi 100%
-   **Tipe Metrik**: Mendukung berbagai tipe metrik (Angka, Persentase, Mata Uang, Ya/Tidak)
-   **Progress Tracking**: Tracking progress dengan current value dan target value
-   **Milestones**: Dapat menambahkan milestone untuk setiap Key Result

### 3. Tactics Section

-   **<PERSON><PERSON> Tactic**: Kategorisasi tactic (Strategis, Opera<PERSON>, Takt<PERSON>, Pendukung)
-   **Prioritas**: Level prioritas (Rendah, Sedang, Tinggi, Kritis)
-   **Estimasi Effort**: Estimasi waktu dalam jam
-   **Skor Dampak**: Penilaian dampak 1-10
-   **Dependensi**: Tracking dependensi antar tactic

### 4. Tasks Section

-   **Create New Tasks**: Membuat task baru langsung dari form objective
-   **Project Integration**: Dapat membuat project baru atau memilih yang sudah ada
-   **Task Assignment**: Assign task ke user tertentu
-   **Contribution Percentage**: Menentukan seberapa besar kontribusi task terhadap objective
-   **Task Status Tracking**: Monitor status task (To Do, In Progress, Completed)
-   **Connect Existing Tasks**: Opsi untuk menghubungkan task yang sudah ada (hanya di edit mode)

## Cara Penggunaan

### Membuat Objective Baru

1. Isi informasi dasar objective
2. Tambahkan Key Results dengan target yang jelas
3. Definisikan Tactics yang akan digunakan
4. **Buat Tasks baru** langsung dari form dengan:
    - Nama dan deskripsi task
    - Pilih atau buat project baru
    - Assign ke user
    - Set tanggal mulai dan target
    - Tentukan contribution percentage

### Mengedit Objective

1. Semua section dapat diedit dalam satu form
2. **Edit tasks yang sudah ada** atau tambah task baru
3. **Hubungkan task existing** melalui section terpisah
4. Validasi otomatis untuk bobot Key Results
5. Progress tracking untuk semua komponen

## Validasi dan Fitur Keamanan

### Key Results

-   Total bobot tidak disarankan melebibi 100%
-   Target value harus diisi
-   Tipe metrik harus dipilih

### Tactics

-   Estimasi effort minimal 1 jam
-   Skor dampak dalam range 1-10
-   Status tracking otomatis

### Tasks

-   Contribution percentage 1-100%
-   Tidak dapat menghubungkan task yang sama berulang kali
-   Hanya tersedia pada edit mode

## Perubahan Teknis

### Model Relationships

-   Objective hasMany KeyResults
-   Objective hasMany Tactics
-   Objective belongsToMany Tasks (dengan pivot contribution_percentage)

### Form Handling

-   Custom form handling untuk task connections
-   Automatic sync untuk many-to-many relationships
-   Proper data validation dan sanitization

### Database Structure

-   Menggunakan existing pivot table `objective_tasks`
-   Mendukung soft deletes untuk semua komponen
-   Proper indexing untuk performance

## Dual Management Approach

-   **Main Form**: Form terintegrasi untuk create/edit dengan semua komponen dalam satu halaman
-   **Relation Managers**: Tetap tersedia sebagai tabs di view page untuk management detail
-   **Best of Both Worlds**: User bisa pilih approach yang sesuai dengan workflow mereka

### Kapan Menggunakan Apa?

-   **Form Terintegrasi**: Untuk create objective baru dengan semua komponen sekaligus
-   **Relation Managers**: Untuk management detail dan bulk operations pada komponen existing
    -   **Key Results Tab**: Tombol "Tambah Key Result" + Update Progress actions
    -   **Tactics Tab**: Tombol "Tambah Tactic Baru" + Edit/Delete actions
    -   **Tasks Tab**: Tombol "Hubungkan Task Existing" + "Buat Task Baru"

## Tips Penggunaan

1. **Mulai dengan Key Results**: Definisikan Key Results terlebih dahulu sebelum Tactics
2. **Bobot Seimbang**: Pastikan bobot Key Results terdistribusi dengan baik
3. **Tactics Spesifik**: Buat tactics yang spesifik dan actionable
4. **Task Connection**: Hubungkan tasks setelah objective dan tactics sudah jelas
5. **Regular Review**: Gunakan progress tracking untuk review berkala

## Troubleshooting

-   Jika form tidak menyimpan task connections, pastikan objective sudah tersimpan terlebih dahulu
-   Untuk error validasi bobot, periksa total bobot Key Results
-   Jika task tidak muncul di dropdown, pastikan task sudah dibuat di sistem
