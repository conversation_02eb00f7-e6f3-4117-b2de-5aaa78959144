# Pemisahan Outlet dari Entitas

## Overview

Dokumen ini menjelaskan pemisahan konsep **Outlet** dari **Entitas** dalam sistem Viera ERP untuk memisahkan fungsi HR & General Management dari POS & Business Operations.

## Konsep Pemisahan

### ENTITAS (HR & General Management)
- ✅ Lokasi kerja umum (kantor, gudang, cabang)
- ✅ Manajemen karyawan dan absensi
- ✅ Geofencing untuk attendance tracking
- ✅ Struktur organisasi dan departemen

### OUTLET (POS & Business Operations)
- 🆕 Lokasi bisnis operasional (toko, restoran, kafe)
- 🆕 Transaksi penjualan dan customer management
- 🆕 Inventory dan product management
- 🆕 Performance tracking dan business analytics

## Struktur Database

### Tabel Outlet (Baru)
```sql
outlets:
- id (primary key)
- name (nama outlet)
- code (kode outlet unik)
- type (toko/restoran/kafe/dll)
- address, city, phone, email
- manager_name, opening_date
- opening_time, closing_time
- latitude, longitude (untuk delivery)
- delivery_radius
- tax_rate, service_charge_rate
- status (active/inactive/maintenance/closed)
- is_active, created_at, updated_at
```

### Relationship Changes
```sql
-- Tambahan field baru:
pos_transactions: + outlet_id (foreign key)
karyawan: + outlet_id (optional, untuk karyawan outlet)

-- Relationship tetap:
karyawan: tetap id_entitas (untuk HR)
absensi: tetap menggunakan entitas
```

## Model Changes

### 1. Model Outlet (Baru)
```php
// app/Models/Outlet.php
class Outlet extends Model
{
    // Relationships
    public function posTransactions()
    public function products()
    public function karyawan()
    
    // Helper Methods
    public function getTodayRevenue(): float
    public function getTodayTransactionCount(): int
    public function getAverageTransactionValue(): float
}
```

### 2. Model PosTransaction (Updated)
```php
// Tambahan relationship
public function outlet()
{
    return $this->belongsTo(Outlet::class);
}
```

### 3. Model Karyawan (Updated)
```php
// Tambahan relationship
public function outlet()
{
    return $this->belongsTo(Outlet::class, 'outlet_id');
}
```

## Filament Resources

### 1. OutletResource (Baru)
- **Panel**: POS
- **Navigation**: Business Management > Outlets
- **Features**:
  - Form dengan sections (Basic Info, Location, Management, Business Settings)
  - Table dengan performance metrics (revenue, transactions)
  - Filters (type, status, category)
  - Relation Manager untuk POS Transactions

### 2. PosTransactionResource (Updated)
- **Tambahan**: Outlet selection dalam form
- **Table**: Kolom outlet dengan badge
- **Filter**: Filter berdasarkan outlet

## API Changes

### PosReportController (Updated)
```php
// Method locationPerformance() sekarang menggunakan Outlet
public function locationPerformance(Request $request)
{
    $outlets = Outlet::where('is_active', true)
        ->where('status', 'active')
        ->get()
        ->map(function ($outlet) use ($fromDate, $toDate) {
            $transactions = PosTransaction::where('outlet_id', $outlet->id)
                ->whereBetween('transaction_date', [$fromDate, $toDate])
                ->get();
            // ...
        });
}

// Filter berdasarkan outlet_id
if ($locationId) {
    $query->where('outlet_id', $locationId);
}
```

## Widget Changes

### LocationPerformanceWidget (Updated)
```php
// Sekarang menggunakan Outlet model
$query = Outlet::query()
    ->where('is_active', true)
    ->where('status', 'active')
    ->withCount(['karyawan as active_employees'])
    ->orderByRaw('(
        SELECT COALESCE(SUM(pos_transactions.net_amount), 0)
        FROM pos_transactions 
        WHERE pos_transactions.outlet_id = outlets.id
        AND DATE(pos_transactions.transaction_date) = CURDATE()
    ) DESC')
```

## Migration Files

### 1. enhance_outlets_table_for_pos.php
- Menambahkan field POS yang diperlukan ke tabel outlets
- Auto-generate code untuk outlet yang sudah ada

### 2. add_outlet_id_to_pos_transactions_table.php
- Menambahkan foreign key outlet_id ke pos_transactions

### 3. add_outlet_id_to_karyawan_table.php
- Menambahkan foreign key outlet_id ke karyawan (optional)

## Seeder

### OutletSeeder (Updated)
```php
// Sample data dengan field lengkap
$outlets = [
    [
        'name' => 'Viera Store Jakarta Pusat',
        'code' => 'VRA-JKT-001',
        'type' => 'toko',
        'category' => 'FnB',
        'address' => 'Jl. Sudirman No. 123, Jakarta Pusat',
        'city' => 'Jakarta',
        'phone' => '021-12345678',
        'email' => '<EMAIL>',
        'manager_name' => 'Budi Santoso',
        'opening_date' => '2020-01-15',
        'opening_time' => '08:00',
        'closing_time' => '22:00',
        'latitude' => -6.2088,
        'longitude' => 106.8456,
        'delivery_radius' => 5000,
        'tax_rate' => 11.00,
        'service_charge_rate' => 5.00,
        'status' => 'active',
        'description' => 'Outlet utama di Jakarta Pusat dengan menu lengkap',
        'is_active' => true,
    ],
    // ... more outlets
];
```

## Testing

### 1. Model Testing
```bash
# Test outlet relationships
php artisan tinker
>>> $outlet = App\Models\Outlet::first()
>>> $outlet->posTransactions
>>> $outlet->getTodayRevenue()
>>> $outlet->getTodayTransactionCount()
```

### 2. API Testing
```bash
# Test location performance API
curl -X GET "http://localhost/api/pos/reports/location-performance?from_date=2025-07-23&to_date=2025-07-23"
```

### 3. Widget Testing
- Akses panel POS
- Verifikasi LocationPerformanceWidget menampilkan data outlet
- Verifikasi performance metrics (revenue, transactions)

## Benefits

### 1. Separation of Concerns
- **HR Functions**: Tetap menggunakan Entitas
- **Business Operations**: Menggunakan Outlet yang lebih spesifik

### 2. Better Data Structure
- Field yang lebih relevan untuk business operations
- Performance metrics yang lebih akurat
- Geolocation untuk delivery management

### 3. Improved User Experience
- Navigation yang lebih jelas di panel POS
- Form yang lebih sesuai dengan kebutuhan outlet
- Performance dashboard yang lebih informatif

## Next Steps

1. **Data Population**: Populate outlet data dengan informasi yang akurat
2. **Permission Setup**: Setup permission untuk outlet management
3. **Integration Testing**: Test integrasi dengan sistem lain
4. **User Training**: Training untuk user tentang konsep baru
5. **Performance Monitoring**: Monitor performance setelah implementasi

## Notes

- Entitas dan Outlet adalah konsep yang berbeda dan tidak perlu migrasi data
- Karyawan bisa terkait dengan Entitas (untuk HR) dan Outlet (untuk operasional)
- POS Transactions sekarang terkait langsung dengan Outlet untuk tracking yang lebih akurat
