# 🔧 Troubleshooting: Filament Approvals Plugin

## 🚨 **<PERSON><PERSON><PERSON> yang <PERSON>**

Set<PERSON>h menjalankan command:
```bash
php artisan approvals:publish --all
```

Muncul error saat menjalankan:
```bash
php artisan optimize
```

**Error Message:**
```
Cannot redeclare class EightyNine\Approvals\Filament\Resources\ApprovalFlowResource 
(previously declared in C:\laragon\www\viera-filament\app\Filament\Resources\ApprovalFlowResource.php:26)
```

## 🔍 **Root Cause**

Command `php artisan approvals:publish --all` mempublish file-file resource ke direktori `app/Filament/Resources/` tetapi dengan namespace yang salah. File-file tersebut masih menggunakan namespace dari package asli (`EightyNine\Approvals\Filament\Resources`) padahal seharusnya menggunakan namespace aplikasi (`App\Filament\Resources`).

## ✅ **Solusi**

### 1. **Perbaiki Namespace di ApprovalFlowResource.php**

**File**: `app/Filament/Resources/ApprovalFlowResource.php`

```php
// BEFORE (SALAH)
<?php
namespace EightyNine\Approvals\Filament\Resources;
use EightyNine\Approvals\Filament\Resources\ApprovalFlowResource\Pages;
use EightyNine\Approvals\Filament\Resources\ApprovalFlowResource\RelationManagers;

// AFTER (BENAR)
<?php
namespace App\Filament\Resources;
use App\Filament\Resources\ApprovalFlowResource\Pages;
use App\Filament\Resources\ApprovalFlowResource\RelationManagers\StepsRelationManager;
```

### 2. **Perbaiki Namespace di Pages**

**File**: `app/Filament/Resources/ApprovalFlowResource/Pages/ListApprovalFlows.php`
```php
// BEFORE
namespace EightyNine\Approvals\Filament\Resources\ApprovalFlowResource\Pages;
use EightyNine\Approvals\Filament\Resources\ApprovalFlowResource;

// AFTER
namespace App\Filament\Resources\ApprovalFlowResource\Pages;
use App\Filament\Resources\ApprovalFlowResource;
```

**File**: `app/Filament/Resources/ApprovalFlowResource/Pages/CreateApprovalFlow.php`
```php
// BEFORE
namespace EightyNine\Approvals\Filament\Resources\ApprovalFlowResource\Pages;
use EightyNine\Approvals\Filament\Resources\ApprovalFlowResource;

// AFTER
namespace App\Filament\Resources\ApprovalFlowResource\Pages;
use App\Filament\Resources\ApprovalFlowResource;
```

**File**: `app/Filament/Resources/ApprovalFlowResource/Pages/EditApprovalFlow.php`
```php
// BEFORE
namespace EightyNine\Approvals\Filament\Resources\ApprovalFlowResource\Pages;
use EightyNine\Approvals\Filament\Resources\ApprovalFlowResource;

// AFTER
namespace App\Filament\Resources\ApprovalFlowResource\Pages;
use App\Filament\Resources\ApprovalFlowResource;
```

### 3. **Perbaiki Namespace di RelationManagers**

**File**: `app/Filament/Resources/ApprovalFlowResource/RelationManagers/StepsRelationManager.php`
```php
// BEFORE
namespace EightyNine\Approvals\Filament\Resources\ApprovalFlowResource\RelationManagers;

// AFTER
namespace App\Filament\Resources\ApprovalFlowResource\RelationManagers;
```

### 4. **Clear dan Optimize**

Setelah semua namespace diperbaiki, jalankan:
```bash
php artisan optimize:clear
php artisan optimize
```

## 🎯 **Hasil**

Setelah perbaikan:
- ✅ `php artisan optimize:clear` berhasil
- ✅ `php artisan optimize` berhasil
- ✅ Tidak ada conflict class
- ✅ ApprovalFlowResource dapat diakses di admin panel

## 📝 **Files yang Dipublish**

Command `php artisan approvals:publish --all` mempublish file-file berikut:

### **Resources:**
- `app/Filament/Resources/ApprovalFlowResource.php`
- `app/Filament/Resources/ApprovalFlowResource/Pages/ListApprovalFlows.php`
- `app/Filament/Resources/ApprovalFlowResource/Pages/CreateApprovalFlow.php`
- `app/Filament/Resources/ApprovalFlowResource/Pages/EditApprovalFlow.php`
- `app/Filament/Resources/ApprovalFlowResource/RelationManagers/StepsRelationManager.php`

### **Views:**
- `resources/views/vendor/filament-approvals/`
  - `tables/columns/approval-status-column.blade.php`
  - `forms/components/approval-status-field.blade.php`
  - Dan file view lainnya

### **Config:**
- `config/filament-approvals.php`

### **Migrations:**
- Migration files untuk approval tables

## 🚨 **Prevention**

Untuk menghindari masalah serupa di masa depan:

1. **Selalu periksa namespace** setelah publish package
2. **Jalankan `php artisan optimize:clear`** setelah publish
3. **Test dengan `php artisan optimize`** untuk memastikan tidak ada conflict
4. **Backup project** sebelum publish package baru

## 🔧 **Alternative Solution**

Jika masalah masih terjadi, Anda bisa:

1. **Hapus file yang dipublish**:
   ```bash
   rm -rf app/Filament/Resources/ApprovalFlowResource*
   ```

2. **Publish ulang dengan selective**:
   ```bash
   php artisan approvals:publish --views
   php artisan approvals:publish --config
   ```

3. **Gunakan resource dari package** tanpa publish ke app directory

## 📚 **References**

- [EightyNine Approvals Documentation](https://github.com/eighty9nine/filament-approvals)
- [Filament Plugin Development](https://filamentphp.com/docs/3.x/plugins/overview)
- [Laravel Package Development](https://laravel.com/docs/10.x/packages)

---

**Status**: ✅ RESOLVED  
**Solution**: Namespace correction  
**Prevention**: Always check namespaces after publishing packages
