# Database Structure Documentation

## Stock Opname System

### Table: `stock_opnames`

| Field                | Type                                                | Null | Default | Description                                     |
| -------------------- | --------------------------------------------------- | ---- | ------- | ----------------------------------------------- |
| id                   | bigint(20) unsigned                                 | NO   | -       | Primary key                                     |
| opname_number        | varchar(255)                                        | NO   | -       | Unique opname number (auto-generated)           |
| opname_date          | date                                                | NO   | -       | Date of stock opname                            |
| entitas_id           | bigint(20) unsigned                                 | NO   | -       | Reference to entitas table                      |
| warehouse_id         | bigint(20) unsigned                                 | NO   | -       | Reference to warehouse (nullable in some cases) |
| status               | enum('Draft','In_Progress','Completed','Cancelled') | NO   | 'Draft' | Current status of opname                        |
| description          | text                                                | YES  | NULL    | Description of the opname                       |
| notes                | text                                                | YES  | NULL    | Additional notes                                |
| total_variance_value | decimal(15,2)                                       | NO   | 0.00    | Total value of variances                        |
| total_items_counted  | int(11)                                             | NO   | 0       | Number of items counted                         |
| total_variance_items | int(11)                                             | NO   | 0       | Number of items with variances                  |
| created_by           | bigint(20) unsigned                                 | NO   | -       | User who created the opname                     |
| approved_by          | bigint(20) unsigned                                 | YES  | NULL    | User who approved the opname                    |
| approved_at          | timestamp                                           | YES  | NULL    | Timestamp when approved                         |
| completed_by         | bigint(20) unsigned                                 | YES  | NULL    | User who completed the opname                   |
| completed_at         | timestamp                                           | YES  | NULL    | Timestamp when completed                        |
| created_at           | timestamp                                           | YES  | NULL    | Record creation timestamp                       |
| updated_at           | timestamp                                           | YES  | NULL    | Record update timestamp                         |
| deleted_at           | timestamp                                           | YES  | NULL    | Soft delete timestamp                           |

**Indexes:**

-   PRIMARY KEY (id)
-   UNIQUE KEY (opname_number)
-   INDEX (entitas_id, warehouse_id, opname_date)
-   INDEX (status, opname_date)

### Table: `stock_opname_items`

| Field             | Type                               | Null | Default | Description                      |
| ----------------- | ---------------------------------- | ---- | ------- | -------------------------------- |
| id                | bigint(20) unsigned                | NO   | -       | Primary key                      |
| stock_opname_id   | bigint(20) unsigned                | NO   | -       | Reference to stock_opnames table |
| product_id        | bigint(20) unsigned                | NO   | -       | Reference to produk table        |
| system_quantity   | int(11)                            | NO   | 0       | Quantity according to system     |
| physical_quantity | int(11)                            | YES  | NULL    | Actual counted quantity          |
| variance_quantity | int(11)                            | NO   | 0       | Difference (physical - system)   |
| unit_cost         | decimal(15,2)                      | NO   | 0.00    | Cost per unit                    |
| variance_value    | decimal(15,2)                      | NO   | 0.00    | Financial impact of variance     |
| variance_type     | enum('Match','Surplus','Shortage') | NO   | 'Match' | Type of variance                 |
| variance_reason   | text                               | YES  | NULL    | Reason for variance              |
| notes             | text                               | YES  | NULL    | Additional notes for this item   |
| is_counted        | tinyint(1)                         | NO   | 0       | Whether item has been counted    |
| counted_at        | timestamp                          | YES  | NULL    | When item was counted            |
| counted_by        | bigint(20) unsigned                | YES  | NULL    | User who counted this item       |
| created_at        | timestamp                          | YES  | NULL    | Record creation timestamp        |
| updated_at        | timestamp                          | YES  | NULL    | Record update timestamp          |

**Indexes:**

-   PRIMARY KEY (id)
-   UNIQUE KEY (stock_opname_id, product_id)
-   INDEX (stock_opname_id, variance_type)
-   INDEX (is_counted, variance_type)

## Stock Opname Workflow

### Status Flow

1. **Draft** - Initial creation, can add/remove products
2. **In_Progress** - Counting in progress, load system quantities
3. **Completed** - All items counted, generate stock adjustments
4. **Cancelled** - Opname cancelled

### Field Relationships

-   `entitas_id` → `entitas.id`
-   `warehouse_id` → `warehouses.id` (if warehouse system is used)
-   `product_id` → `produk.id`
-   `created_by`, `approved_by`, `completed_by`, `counted_by` → `users.id`

### Business Rules

-   Each product can only appear once per opname (unique constraint)
-   Variance calculations are automatic: `variance_quantity = physical_quantity - system_quantity`
-   Variance value: `variance_value = variance_quantity * unit_cost`
-   Variance type determined by variance_quantity (positive=Surplus, negative=Shortage, zero=Match)

## Migration Files

### Current Clean Migrations

-   `2025_06_23_191353_create_stock_opnames_table_final.php` - Creates stock_opnames table
-   `2025_06_23_190109_create_stock_opname_items_table_clean.php` - Creates stock_opname_items table
-   `2025_06_17_184418_add_pricing_columns_to_produk_table.php` - Adds unit_cost, selling_price to produk (with safe checks)
-   `2025_06_17_200001_add_average_cost_to_produk.php` - Adds average_cost to produk (with safe checks)
-   `2025_06_23_000001_create_procurement_tables.php` - Creates procurement system tables
-   `2025_06_23_000002_create_expense_tables.php` - Creates expense management tables
-   `2025_06_23_000003_create_enhanced_payroll_tables.php` - Creates enhanced payroll tables

### Notes for Team Collaboration

1. **Always run migrations in order** - Check `php artisan migrate:status` before pulling
2. **Never edit existing migrations** - Create new migrations for changes
3. **Use descriptive migration names** - Include date and clear description
4. **Test migrations on fresh database** - Ensure they work from scratch
5. **Document field changes** - Update this file when adding new fields

## Field Naming Conventions

-   Use snake_case for all field names
-   Use descriptive names (e.g., `physical_quantity` not `qty_physical`)
-   Include units in field names when relevant (e.g., `unit_cost`, `total_value`)
-   Use consistent suffixes:
    -   `_id` for foreign keys
    -   `_at` for timestamps
    -   `_by` for user references
    -   `_count` for counters
