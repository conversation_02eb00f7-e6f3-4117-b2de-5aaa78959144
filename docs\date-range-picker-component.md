# Date Range Picker Component

Ko<PERSON>nen Blade Laravel yang modern dan responsif untuk memilih rentang tanggal dengan fitur lengkap menggunakan TailwindCSS dan Alpine.js.

## 🎯 Fitur

- ✅ **Kalender Dua Kolom**: Menampilkan bulan sebelumnya dan bulan sekarang
- ✅ **Preset Filter Cepat**: 8 pilihan preset (Hari <PERSON>, Kemarin, 7 Hari <PERSON>, dll.)
- ✅ **Pemilihan Manual**: Rentang tanggal dapat dipilih secara manual
- ✅ **Kontrol Waktu**: Jam dan menit mulai/berakhir dapat diatur
- ✅ **Event Dispatch**: Trigger event `daterange-selected` dengan Alpine.js
- ✅ **Responsive Design**: UI yang modern dan mobile-friendly
- ✅ **No External Dependencies**: Tidak memerlukan plugin eksternal

## 📦 Instalasi

1. Pastikan Anda sudah menginstall TailwindCSS dan Alpine.js
2. Copy file komponen ke `resources/views/components/date-range-picker.blade.php`
3. Komponen siap digunakan!

## 🚀 Pen<PERSON><PERSON>an <PERSON>ar

```blade
{{-- Pengg<PERSON><PERSON> se<PERSON> --}}
<x-date-range-picker 
    placeholder="Pilih rentang tanggal"
    @daterange-selected="handleDateRange($event.detail)"
/>

{{-- Dengan nilai awal --}}
<x-date-range-picker 
    start-date="2024-01-01"
    end-date="2024-01-31"
    placeholder="Periode laporan"
    @daterange-selected="handleDateRange($event.detail)"
/>
```

## 🔧 Props

| Prop | Type | Default | Deskripsi |
|------|------|---------|-----------|
| `start-date` | String | `null` | Tanggal mulai awal (format: YYYY-MM-DD) |
| `end-date` | String | `null` | Tanggal selesai awal (format: YYYY-MM-DD) |
| `placeholder` | String | `'Pilih rentang tanggal'` | Placeholder text untuk input |
| `id` | String | `auto-generated` | ID unik untuk komponen |

## 📡 Events

### `daterange-selected`

Event ini di-dispatch ketika user mengklik tombol "Proses". Event detail berisi:

```javascript
{
    start: "2024-01-01",        // Tanggal mulai (YYYY-MM-DD)
    end: "2024-01-31",          // Tanggal selesai (YYYY-MM-DD)
    timeStart: "08:00",         // Waktu mulai (HH:MM)
    timeEnd: "17:00"            // Waktu selesai (HH:MM)
}
```

## 🎨 Preset Filter

Komponen menyediakan 8 preset filter cepat:

1. **Hari Ini** - Tanggal hari ini
2. **Kemarin** - Tanggal kemarin
3. **7 Hari Terakhir** - 7 hari terakhir termasuk hari ini
4. **Minggu Ini** - Dari awal minggu sampai hari ini
5. **Minggu Lalu** - Minggu sebelumnya (Minggu-Sabtu)
6. **30 Hari Terakhir** - 30 hari terakhir termasuk hari ini
7. **Bulan Ini** - Dari tanggal 1 bulan ini sampai hari ini
8. **Bulan Lalu** - Bulan sebelumnya (tanggal 1 sampai akhir bulan)

## 💡 Contoh Implementasi

### 1. Filter Dashboard

```blade
<div x-data="{ dateRange: null }">
    <x-date-range-picker 
        placeholder="Filter periode dashboard"
        @daterange-selected="dateRange = $event.detail; loadDashboardData()"
    />
    
    <div x-show="dateRange" class="mt-4">
        <p>Periode: <span x-text="dateRange?.start"></span> - <span x-text="dateRange?.end"></span></p>
    </div>
</div>

<script>
function loadDashboardData() {
    // Load data berdasarkan dateRange
    console.log('Loading dashboard data for:', this.dateRange);
}
</script>
```

### 2. Form Filter Laporan

```blade
<form x-data="{ reportFilter: {} }" @submit.prevent="submitReport()">
    <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
            Periode Laporan
        </label>
        <x-date-range-picker 
            @daterange-selected="reportFilter.dateRange = $event.detail"
        />
    </div>
    
    <button type="submit" class="btn btn-primary">
        Generate Laporan
    </button>
</form>

<script>
function submitReport() {
    if (!this.reportFilter.dateRange) {
        alert('Pilih periode laporan terlebih dahulu');
        return;
    }
    
    // Submit form dengan data periode
    console.log('Generating report for:', this.reportFilter.dateRange);
}
</script>
```

### 3. Multiple Date Pickers

```blade
<div x-data="{ 
    salesPeriod: null, 
    inventoryPeriod: null 
}">
    <div class="grid grid-cols-2 gap-4">
        <div>
            <label>Periode Penjualan</label>
            <x-date-range-picker 
                @daterange-selected="salesPeriod = $event.detail"
            />
        </div>
        
        <div>
            <label>Periode Inventory</label>
            <x-date-range-picker 
                @daterange-selected="inventoryPeriod = $event.detail"
            />
        </div>
    </div>
    
    <button 
        @click="compareData()"
        :disabled="!salesPeriod || !inventoryPeriod"
        class="mt-4 btn btn-primary"
    >
        Bandingkan Data
    </button>
</div>
```

## 🎨 Kustomisasi Style

Komponen menggunakan TailwindCSS classes yang dapat dikustomisasi:

```blade
{{-- Mengubah lebar komponen --}}
<div class="w-96">
    <x-date-range-picker />
</div>

{{-- Mengubah style input --}}
<style>
.date-range-input {
    @apply border-2 border-blue-300 focus:border-blue-500;
}
</style>
```

## 🔧 Troubleshooting

### Komponen tidak muncul
- Pastikan TailwindCSS dan Alpine.js sudah terload
- Periksa console browser untuk error JavaScript

### Event tidak ter-trigger
- Pastikan menggunakan `@daterange-selected` bukan `@click`
- Periksa nama event handler function

### Style tidak sesuai
- Pastikan TailwindCSS classes tersedia
- Periksa apakah ada CSS conflicts

## 📱 Mobile Responsiveness

Komponen sudah dioptimasi untuk mobile dengan:
- Dropdown yang menyesuaikan lebar layar
- Touch-friendly button sizes
- Responsive grid layout untuk kalender

## 🚀 Performance Tips

1. **Lazy Loading**: Komponen hanya render kalender saat dropdown dibuka
2. **Event Debouncing**: Gunakan debouncing untuk API calls
3. **Memory Management**: Komponen otomatis cleanup saat destroyed

## 📄 License

Komponen ini bebas digunakan untuk proyek komersial maupun non-komersial.
