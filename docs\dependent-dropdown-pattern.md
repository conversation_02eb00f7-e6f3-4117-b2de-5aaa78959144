# Dependent Dropdown Pattern in Filament

## Overview
Dependent dropdown adalah pattern dimana pilihan di dropdown kedua bergantung pada pilihan di dropdown pertama. Contoh: ketika memilih Departemen, dropdown Divisi hanya menampilkan divisi yang ada di departemen tersebut.

## Implementation Pattern

### 1. Parent Dropdown (Departemen)
```php
Select::make('id_departemen')
    ->label('Departemen')
    ->options(\App\Models\Departemen::pluck('nama_departemen', 'id'))
    ->searchable()
    ->preload()
    ->required()
    ->reactive() // ⭐ WAJIB: Make it reactive
    ->afterStateUpdated(function (callable $set) {
        // ⭐ WAJIB: Reset child dropdown when parent changes
        $set('id_divisi', null);
    })
    ->dehydrated()
```

### 2. Child Dropdown (Divisi)
```php
Select::make('id_divisi')
    ->label('Divisi')
    ->options(function (callable $get) {
        // ⭐ WAJIB: Get parent value
        $departemenId = $get('id_departemen');
        if (!$departemenId) {
            return []; // Return empty if no parent selected
        }
        // ⭐ WAJIB: Filter based on parent
        return \App\Models\Divisi::where('departemen_id', $departemenId)
            ->pluck('nama_divisi', 'id');
    })
    ->searchable()
    ->required()
    ->disabled(fn(callable $get) => !$get('id_departemen')) // ⭐ OPTIONAL: Disable until parent selected
    ->placeholder(fn(callable $get) => $get('id_departemen') ? 'Pilih divisi...' : 'Pilih departemen terlebih dahulu')
    ->helperText('Divisi akan muncul setelah memilih departemen') // ⭐ OPTIONAL: Helper text
    ->dehydrated()
```

## Key Components Explained

### 1. `reactive()`
- **Purpose**: Makes the field reactive to state changes
- **Required**: YES for parent dropdown
- **Effect**: Triggers re-evaluation when field value changes

### 2. `afterStateUpdated()`
- **Purpose**: Execute callback when field value changes
- **Required**: YES for parent dropdown
- **Common use**: Reset child dropdown values

### 3. `options(function (callable $get))`
- **Purpose**: Dynamic options based on other field values
- **Required**: YES for child dropdown
- **Pattern**: Always check if parent has value first

### 4. `disabled(fn(callable $get) => !$get('parent_field'))`
- **Purpose**: Disable child until parent is selected
- **Required**: NO (optional UX improvement)
- **Effect**: Better user experience

### 5. `placeholder(fn(callable $get) => ...)`
- **Purpose**: Dynamic placeholder based on parent state
- **Required**: NO (optional UX improvement)
- **Effect**: Clearer user guidance

## Database Requirements

### 1. Parent Model (Departemen)
```php
class Departemen extends Model
{
    protected $fillable = ['nama_departemen'];
    
    public function divisi()
    {
        return $this->hasMany(Divisi::class);
    }
}
```

### 2. Child Model (Divisi)
```php
class Divisi extends Model
{
    protected $fillable = ['nama_divisi', 'departemen_id'];
    
    public function departemen()
    {
        return $this->belongsTo(Departemen::class);
    }
}
```

### 3. Migration Structure
```php
// departemen table
Schema::create('departemen', function (Blueprint $table) {
    $table->id();
    $table->string('nama_departemen');
    $table->timestamps();
});

// divisi table
Schema::create('divisi', function (Blueprint $table) {
    $table->id();
    $table->string('nama_divisi');
    $table->unsignedBigInteger('departemen_id'); // ⭐ FOREIGN KEY
    $table->timestamps();
    
    $table->foreign('departemen_id')->references('id')->on('departemen');
});
```

## Common Patterns

### 1. Three-Level Dropdown (Departemen → Divisi → Jabatan)
```php
// Level 1: Departemen (same as above)

// Level 2: Divisi (same as above)

// Level 3: Jabatan
Select::make('id_jabatan')
    ->options(function (callable $get) {
        $divisiId = $get('id_divisi');
        if (!$divisiId) return [];
        return \App\Models\Jabatan::where('divisi_id', $divisiId)
            ->pluck('nama_jabatan', 'id');
    })
    ->disabled(fn(callable $get) => !$get('id_divisi'))
```

### 2. Multiple Child Dropdowns
```php
// Parent: Category
Select::make('category_id')->reactive()
    ->afterStateUpdated(function (callable $set) {
        $set('subcategory_id', null);
        $set('product_id', null); // Reset both children
    })

// Child 1: Subcategory
Select::make('subcategory_id')->reactive()
    ->options(fn(callable $get) => /* filter by category */)
    ->afterStateUpdated(fn(callable $set) => $set('product_id', null))

// Child 2: Product
Select::make('product_id')
    ->options(fn(callable $get) => /* filter by subcategory */)
```

## Best Practices

### 1. Performance
- Use `preload()` on parent dropdown for better UX
- Consider caching for large datasets
- Use database indexes on foreign key columns

### 2. User Experience
- Always provide clear placeholder text
- Use helper text to guide users
- Disable child dropdowns until parent is selected
- Reset child values when parent changes

### 3. Error Handling
- Always check if parent value exists before filtering
- Return empty array if no parent selected
- Handle null/empty states gracefully

### 4. Validation
- Add validation rules for both parent and child
- Ensure child value is valid for selected parent
- Use custom validation rules if needed

## Testing Checklist

- [ ] Parent dropdown loads correctly
- [ ] Child dropdown is disabled initially
- [ ] Child dropdown enables when parent is selected
- [ ] Child options filter correctly based on parent
- [ ] Child dropdown resets when parent changes
- [ ] Form submission works with both values
- [ ] Validation works for both fields
- [ ] Edit form loads existing values correctly
- [ ] Create option forms work (if implemented)

## Common Issues & Solutions

### Issue: Child dropdown doesn't update
**Solution**: Ensure parent has `reactive()` method

### Issue: Child dropdown shows all options
**Solution**: Check filtering logic in `options()` callback

### Issue: Form doesn't save child value
**Solution**: Ensure child dropdown has `dehydrated()` method

### Issue: Edit form doesn't load child options
**Solution**: Child dropdown should load options based on existing parent value
