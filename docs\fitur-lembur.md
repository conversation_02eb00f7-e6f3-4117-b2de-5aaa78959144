# Dokumentasi Fitur Lembur

## Overview
Fitur lembur adalah sistem untuk mengelola data lembur karyawan dalam aplikasi Viera Filament. Fitur ini memungkinkan admin dan supervisor untuk mencatat, mengelola, dan memantau jam lembur karyawan.

## Komponen yang Dibuat

### 1. Model Lembur (`app/Models/Lembur.php`)
- **Tabel**: `lembur`
- **Fields**:
  - `id` - Primary key
  - `karyawan_id` - Foreign key ke tabel karyawan
  - `tanggal` - <PERSON><PERSON> lembur
  - `jumlah_jam` - <PERSON><PERSON><PERSON> jam lembur (decimal 5,2)
  - `deskripsi` - Deskripsi pekerjaan lembur (optional)
  - `created_by` - User yang menginput data
  - `created_at`, `updated_at` - Timestamps
  - `deleted_at` - Soft delete

- **Relationships**:
  - `belongsTo(Karyawan::class)` - <PERSON><PERSON><PERSON> ke karyawan
  - `belongsTo(User::class, 'created_by')` - <PERSON><PERSON><PERSON> ke user pembuat

- **Scopes**:
  - `filterByMonth($month, $year)` - Filter berdasarkan bulan
  - `filterByKaryawan($karyawanId)` - Filter berdasarkan karyawan

### 2. Migrasi Database (`database/migrations/2025_07_21_152316_create_lembur_table.php`)
- Membuat tabel `lembur` dengan struktur yang sesuai
- Menambahkan foreign key constraints
- Menambahkan indeks untuk optimasi query

### 3. Filament Resource (`app/Filament/Resources/LemburResource.php`)
- **Navigation**: Berada di grup "Jadwal & Absensi"
- **Icon**: `heroicon-o-clock`
- **Label**: "Data Lembur"

#### Form Components:
- **Karyawan**: Select dengan search dan preload
- **Tanggal**: DatePicker dengan validasi maksimal hari ini
- **Jumlah Jam**: TextInput numeric dengan validasi 0.5-12 jam
- **Deskripsi**: Textarea untuk deskripsi pekerjaan

#### Table Columns:
- Nama Karyawan (dengan NIP)
- Departemen
- Jabatan
- Tanggal
- Jam Lembur
- Deskripsi (dengan tooltip)
- Dibuat Oleh
- Tanggal Input

#### Filters:
- **Filter Karyawan**: Select dengan search
- **Filter Departemen**: Select berdasarkan departemen
- **Filter Bulan**: Kombinasi bulan dan tahun dengan indicator
- **Trash Filter**: Untuk soft deleted records

### 4. Pages
- `ListLemburs.php` - Halaman daftar lembur
- `CreateLembur.php` - Halaman tambah lembur
- `ViewLembur.php` - Halaman detail lembur
- `EditLembur.php` - Halaman edit lembur

### 5. Seeder (`database/seeders/LemburSeeder.php`)
- Membuat data contoh lembur untuk testing
- Generate 30 record dengan data realistis
- Menggunakan tanggal 3 bulan terakhir
- Skip weekend untuk data yang lebih realistis

### 6. Relasi di Model Karyawan
Ditambahkan relasi `lembur()` di model Karyawan untuk mengakses data lembur karyawan.

## Fitur Utama

### 1. Pencarian Canggih
- Pencarian berdasarkan nama karyawan dan NIP
- Filter berdasarkan departemen
- Filter berdasarkan bulan dan tahun

### 2. Validasi Data
- Jumlah jam minimal 0.5 jam, maksimal 12 jam
- Tanggal tidak boleh lebih dari hari ini
- Karyawan wajib dipilih

### 3. User Experience
- Form yang user-friendly dengan helper text
- Tooltip untuk deskripsi yang panjang
- Notifikasi sukses saat create/update
- Empty state yang informatif

### 4. Security & Audit
- Soft delete untuk data integrity
- Tracking user yang menginput data
- Timestamps untuk audit trail

## Cara Penggunaan

### 1. Akses Menu
- Login ke admin panel
- Navigasi ke "Jadwal & Absensi" > "Data Lembur"

### 2. Tambah Data Lembur
- Klik tombol "Tambah Lembur"
- Pilih karyawan
- Isi tanggal lembur
- Masukkan jumlah jam (0.5 - 12 jam)
- Isi deskripsi pekerjaan (optional)
- Klik "Simpan"

### 3. Filter Data
- Gunakan filter karyawan untuk melihat lembur karyawan tertentu
- Gunakan filter departemen untuk melihat lembur per departemen
- Gunakan filter bulan untuk melihat data periode tertentu

### 4. Export/Import
- Fitur export dan import dapat ditambahkan sesuai kebutuhan

## Fitur Tambahan yang Telah Diimplementasikan

### 1. Widget Statistik (`LemburStatsWidget`)
- **Total Jam Lembur Bulan Ini**: Dengan perbandingan bulan lalu
- **Total Record Lembur**: Jumlah record lembur bulan ini
- **Rata-rata Jam per Hari**: Berdasarkan hari berjalan
- **Karyawan Lembur Terbanyak**: Karyawan dengan jam lembur tertinggi

### 2. Modal Statistik Detail
- **Statistik Komprehensif**: 4 card statistik utama
- **Top 5 Karyawan**: Ranking karyawan dengan lembur terbanyak
- **Chart Placeholder**: Siap untuk implementasi chart
- **Informasi Tambahan**: Tips dan informasi berguna

### 3. Action "Total Jam" per Karyawan
- **Modal Detail**: Menampilkan total jam lembur per karyawan
- **Breakdown Periode**: Total bulan ini dan tahun ini
- **Warning System**: Peringatan jika melebihi 40 jam per bulan
- **Informasi Rata-rata**: Rata-rata jam lembur per bulan

### 4. Bulk Actions
- **Hitung Total Jam**: Menghitung total jam dari record yang dipilih
- **Export CSV**: Placeholder untuk fitur export (akan dikembangkan)
- **Notifikasi**: Feedback langsung ke user

### 5. Validasi Lanjutan
- **Validasi Weekend**: Tidak boleh input lembur di weekend
- **Validasi Periode**: Tidak boleh lebih dari 30 hari yang lalu
- **Validasi Batas Jam**: Maksimal 40 jam per bulan per karyawan
- **Live Feedback**: Menampilkan total jam lembur saat memilih karyawan

### 6. User Experience Improvements
- **Live Updates**: Form yang responsif dengan live validation
- **Helper Text**: Informasi kontekstual di setiap field
- **Tooltips**: Tooltip untuk deskripsi yang panjang
- **Color Coding**: Warna yang konsisten untuk berbagai status

## Pengembangan Selanjutnya

### Fitur yang Dapat Ditambahkan:
1. **Approval Workflow**: Sistem persetujuan lembur
2. **Reporting**: Laporan lembur per periode dengan chart
3. **Export**: Export data ke Excel/PDF dengan template
4. **Notification**: Email/SMS notifikasi untuk lembur berlebihan
5. **Integration**: Integrasi dengan sistem payroll
6. **Mobile App**: API untuk aplikasi mobile
7. **Dashboard Chart**: Chart interaktif untuk tren lembur
8. **Automated Alerts**: Alert otomatis untuk manager

### Optimasi:
1. **Caching**: Cache untuk data yang sering diakses
2. **Indexing**: Tambahan indeks untuk query yang kompleks
3. **Pagination**: Optimasi pagination untuk data besar
4. **API**: REST API untuk mobile app
5. **Background Jobs**: Queue untuk proses yang berat
6. **Database Optimization**: Query optimization dan indexing

## Troubleshooting

### Masalah Umum:
1. **Migrasi Gagal**: Pastikan database connection benar
2. **Resource Tidak Muncul**: Pastikan resource terdaftar di AdminPanelProvider
3. **Permission Error**: Pastikan user memiliki akses yang sesuai

### Log Location:
- Laravel logs: `storage/logs/laravel.log`
- Database queries dapat di-debug dengan Laravel Debugbar
