# 🔒 Update Permission Filter - Fitur Lembur

## 📋 Overview
Dokumentasi ini menjelaskan perubahan yang dilakukan untuk menerapkan filter permission pada fitur lembur menggunakan `PermissionService::getAccessibleKaryawanIds('manage_overtime')`.

## ✅ Perubahan yang Dilakukan

### 1. Form Select Karyawan (`LemburResource.php`)
**Lokasi**: `app/Filament/Resources/LemburResource.php` - method `form()`

**Perubahan**:
```php
// BEFORE
->relationship('karyawan', 'nama_lengkap')

// AFTER
->relationship(
    'karyawan', 
    'nama_lengkap',
    modifyQueryUsing: function ($query) {
        $accessibleIds = \App\Services\PermissionService::getAccessibleKaryawanIds('manage_overtime');
        return $query->whereIn('id', $accessibleIds);
    }
)
```

**Dampak**: Dropdown karyawan di form create/edit hanya menampilkan karyawan yang accessible berdasarkan permission.

### 2. Filter Karyawan di Tabel (`LemburResource.php`)
**Lokasi**: `app/Filament/Resources/LemburResource.php` - method `table()` - filters

**Perubahan**:
```php
// BEFORE
SelectFilter::make('karyawan_id')
    ->relationship('karyawan', 'nama_lengkap')

// AFTER
SelectFilter::make('karyawan_id')
    ->relationship(
        'karyawan', 
        'nama_lengkap',
        modifyQueryUsing: function ($query) {
            $accessibleIds = \App\Services\PermissionService::getAccessibleKaryawanIds('manage_overtime');
            return $query->whereIn('id', $accessibleIds);
        }
    )
```

**Dampak**: Filter karyawan di tabel hanya menampilkan karyawan yang accessible.

### 3. Widget Statistik (`LemburStatsWidget.php`)
**Lokasi**: `app/Filament/Resources/LemburResource/Widgets/LemburStatsWidget.php`

**Perubahan**:
```php
// BEFORE
$totalJamBulanIni = Lembur::whereMonth('tanggal', $currentMonth)
    ->whereYear('tanggal', $currentYear)
    ->sum('jumlah_jam');

// AFTER
$accessibleIds = \App\Services\PermissionService::getAccessibleKaryawanIds('manage_overtime');
$totalJamBulanIni = Lembur::whereMonth('tanggal', $currentMonth)
    ->whereYear('tanggal', $currentYear)
    ->whereIn('karyawan_id', $accessibleIds)
    ->sum('jumlah_jam');
```

**Dampak**: Semua statistik di widget hanya menghitung data dari karyawan yang accessible.

### 4. Method getStatistik() (`LemburResource.php`)
**Lokasi**: `app/Filament/Resources/LemburResource.php` - method `getStatistik()`

**Perubahan**: Menambahkan filter `->whereIn('karyawan_id', $accessibleIds)` pada semua query.

**Dampak**: Modal statistik detail hanya menampilkan data dari karyawan yang accessible.

### 5. Modal Statistik View (`statistik-modal.blade.php`)
**Lokasi**: `resources/views/filament/resources/lembur/statistik-modal.blade.php`

**Perubahan**:
```php
// BEFORE
$topKaryawan = \App\Models\Lembur::with('karyawan.departemen')
    ->whereMonth('tanggal', now()->month)
    ->whereYear('tanggal', now()->year)

// AFTER
$accessibleIds = \App\Services\PermissionService::getAccessibleKaryawanIds('manage_overtime');
$topKaryawan = \App\Models\Lembur::with('karyawan.departemen')
    ->whereMonth('tanggal', now()->month)
    ->whereYear('tanggal', now()->year)
    ->whereIn('karyawan_id', $accessibleIds)
```

**Dampak**: Top 5 karyawan hanya menampilkan karyawan yang accessible.

### 6. Action "Total Jam" (`LemburResource.php`)
**Lokasi**: `app/Filament/Resources/LemburResource.php` - action `lihat_total_jam`

**Perubahan**: Menambahkan validasi akses sebelum menampilkan modal:
```php
->modalContent(function ($record) {
    // Verify that the karyawan is accessible
    $accessibleIds = \App\Services\PermissionService::getAccessibleKaryawanIds('manage_overtime');
    if (!in_array($record->karyawan_id, $accessibleIds)) {
        return view('filament.resources.lembur.access-denied-modal');
    }
    // ... rest of the code
})
```

**Dampak**: Jika user mencoba mengakses data karyawan yang tidak accessible, akan muncul modal access denied.

### 7. Access Denied Modal (NEW)
**Lokasi**: `resources/views/filament/resources/lembur/access-denied-modal.blade.php`

**Fitur Baru**: Modal yang menampilkan pesan akses ditolak dengan design yang konsisten.

## 🔧 Query Utama yang Sudah Ada
**Lokasi**: `app/Filament/Resources/LemburResource.php` - method `getEloquentQuery()`

Query utama sudah menggunakan `PermissionService::applyPermissionFilter()`:
```php
return PermissionService::applyPermissionFilter(
    parent::getEloquentQuery(),
    'manage_overtime',
    'karyawan_id'
);
```

**Dampak**: Tabel utama sudah otomatis hanya menampilkan data lembur dari karyawan yang accessible.

## 🎯 Hasil Implementasi

### ✅ Yang Sudah Difilter:
1. **Form Select Karyawan** - Hanya karyawan accessible
2. **Filter Karyawan di Tabel** - Hanya karyawan accessible  
3. **Data Tabel Utama** - Hanya lembur dari karyawan accessible
4. **Widget Statistik** - Hanya data dari karyawan accessible
5. **Modal Statistik** - Hanya data dari karyawan accessible
6. **Top 5 Karyawan** - Hanya karyawan accessible
7. **Action Total Jam** - Dengan validasi akses

### 🔒 Security Features:
- **Double Protection**: Filter di query utama + filter di form/filter
- **Access Validation**: Validasi tambahan di action modal
- **User Feedback**: Modal access denied yang informatif
- **Consistent Filtering**: Semua query menggunakan permission yang sama

## 🚀 Testing

### Test Cases:
1. **Login sebagai user dengan permission terbatas**
   - Pastikan dropdown karyawan hanya menampilkan karyawan yang accessible
   - Pastikan filter karyawan hanya menampilkan karyawan yang accessible
   - Pastikan statistik hanya menghitung data dari karyawan yang accessible

2. **Login sebagai super admin**
   - Pastikan semua karyawan muncul di dropdown dan filter
   - Pastikan statistik menghitung semua data

3. **Test Action "Total Jam"**
   - Pastikan modal muncul untuk karyawan yang accessible
   - Pastikan modal access denied muncul untuk karyawan yang tidak accessible

## 📝 Notes

### Permission Key:
- **`manage_overtime`**: Key permission yang digunakan untuk mengakses data lembur

### Service Used:
- **`PermissionService::getAccessibleKaryawanIds()`**: Method untuk mendapatkan ID karyawan yang accessible
- **`PermissionService::applyPermissionFilter()`**: Method untuk menerapkan filter permission pada query

### Consistency:
Semua query yang mengakses data lembur sekarang konsisten menggunakan filter permission yang sama, memastikan data security yang ketat.

---

**Status**: ✅ COMPLETE  
**Security Level**: 🔒 HIGH  
**Tested**: ✅ READY FOR TESTING
