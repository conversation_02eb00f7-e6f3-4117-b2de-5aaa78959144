<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * SAFE MIGRATION TEMPLATE FOR TEAM COLLABORATION
 * 
 * This template ensures migrations are safe to run multiple times
 * and won't cause conflicts in team environments.
 * 
 * Usage:
 * 1. Copy this template
 * 2. Replace TEMPLATE_NAME with your migration name
 * 3. Replace table_name with your actual table name
 * 4. Add your columns with proper checks
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // EXAMPLE 1: Adding columns to existing table
        Schema::table('table_name', function (Blueprint $table) {
            // Always check if column exists before adding
            if (!Schema::hasColumn('table_name', 'new_column')) {
                $table->string('new_column')->nullable()->after('existing_column');
            }
            
            if (!Schema::hasColumn('table_name', 'another_column')) {
                $table->decimal('another_column', 12, 2)->default(0)->after('new_column');
            }
        });

        // EXAMPLE 2: Creating new table
        if (!Schema::hasTable('new_table_name')) {
            Schema::create('new_table_name', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->text('description')->nullable();
                $table->timestamps();
                
                // Add indexes
                $table->index('name');
            });
        }

        // EXAMPLE 3: Adding indexes safely
        if (Schema::hasTable('table_name')) {
            Schema::table('table_name', function (Blueprint $table) {
                // Check if index doesn't exist before adding
                $indexes = Schema::getConnection()
                    ->getDoctrineSchemaManager()
                    ->listTableIndexes('table_name');
                
                if (!isset($indexes['table_name_column_index'])) {
                    $table->index('column_name', 'table_name_column_index');
                }
            });
        }

        // EXAMPLE 4: Adding foreign keys safely
        Schema::table('table_name', function (Blueprint $table) {
            // Check if foreign key column exists
            if (!Schema::hasColumn('table_name', 'foreign_id')) {
                $table->unsignedBigInteger('foreign_id')->nullable();
            }
            
            // Add foreign key constraint if it doesn't exist
            $foreignKeys = Schema::getConnection()
                ->getDoctrineSchemaManager()
                ->listTableForeignKeys('table_name');
            
            $hasForeignKey = false;
            foreach ($foreignKeys as $foreignKey) {
                if (in_array('foreign_id', $foreignKey->getLocalColumns())) {
                    $hasForeignKey = true;
                    break;
                }
            }
            
            if (!$hasForeignKey) {
                $table->foreign('foreign_id')->references('id')->on('other_table');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // EXAMPLE 1: Dropping columns safely
        Schema::table('table_name', function (Blueprint $table) {
            // Check if column exists before dropping
            if (Schema::hasColumn('table_name', 'new_column')) {
                $table->dropColumn('new_column');
            }
            
            if (Schema::hasColumn('table_name', 'another_column')) {
                $table->dropColumn('another_column');
            }
        });

        // EXAMPLE 2: Dropping table safely
        Schema::dropIfExists('new_table_name');

        // EXAMPLE 3: Dropping foreign keys safely
        Schema::table('table_name', function (Blueprint $table) {
            // Drop foreign key if it exists
            $foreignKeys = Schema::getConnection()
                ->getDoctrineSchemaManager()
                ->listTableForeignKeys('table_name');
            
            foreach ($foreignKeys as $foreignKey) {
                if (in_array('foreign_id', $foreignKey->getLocalColumns())) {
                    $table->dropForeign($foreignKey->getName());
                    break;
                }
            }
            
            // Drop column if it exists
            if (Schema::hasColumn('table_name', 'foreign_id')) {
                $table->dropColumn('foreign_id');
            }
        });
    }
};

/**
 * MIGRATION BEST PRACTICES FOR TEAM COLLABORATION:
 * 
 * 1. ALWAYS CHECK EXISTENCE
 *    - Use Schema::hasColumn() before adding columns
 *    - Use Schema::hasTable() before creating tables
 *    - Check indexes and foreign keys before adding
 * 
 * 2. NAMING CONVENTIONS
 *    - Use descriptive migration names with date prefix
 *    - Use snake_case for all database names
 *    - Include table name in migration file name
 * 
 * 3. ROLLBACK SAFETY
 *    - Always implement proper down() method
 *    - Check existence before dropping
 *    - Handle dependencies properly
 * 
 * 4. TESTING
 *    - Test migration on fresh database
 *    - Test rollback functionality
 *    - Verify no conflicts with existing data
 * 
 * 5. DOCUMENTATION
 *    - Comment complex migrations
 *    - Update database documentation
 *    - Communicate breaking changes to team
 * 
 * 6. TEAM COORDINATION
 *    - Pull latest changes before creating migrations
 *    - Run php artisan migrate:status to check pending migrations
 *    - Coordinate with team on structural changes
 */
