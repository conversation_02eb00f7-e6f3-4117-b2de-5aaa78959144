# Sistem Permission Management untuk HR

Sistem permission management yang fleksibel untuk mengatur akses karyawan berdasarkan hierarki organisasi (entitas, departemen, divisi, atau custom).

## 🎯 Fitur Utama

### **1. Permission Management Resource**
- ✅ **CRUD Permission**: Create, Read, Update, Delete permission untuk karyawan
- ✅ **Flexible Scope**: Berdasarkan semua data, entitas, departemen, divisi, atau custom
- ✅ **Multiple Permission Types**: Approve cuti, lihat absensi, kelola jadwal, dll
- ✅ **Status Management**: Aktif/nonaktif permission
- ✅ **Audit Trail**: Track siapa yang membuat permission

### **2. Permission Summary Page**
- ✅ **Visual Overview**: Lihat semua permission karyawan dalam satu halaman
- ✅ **Interactive Selection**: Pilih karyawan untuk melihat permission detail
- ✅ **Quick Actions**: Tambah permission atau kelola existing permission
- ✅ **Color-coded Status**: Visual indicator untuk permission yang aktif

### **3. Dashboard Widgets**
- ✅ **Permission Overview**: Statistik permission di dashboard admin
- ✅ **Clickable Stats**: Navigasi langsung ke tab terkait
- ✅ **Real-time Counts**: Update otomatis jumlah permission

## 📋 Jenis Permission

### **Permission Types:**
- **approve_cuti**: Approve Cuti/Izin/Sakit
- **view_absensi**: Lihat Absensi
- **manage_jadwal**: Kelola Jadwal
- **view_payroll**: Lihat Payroll
- **manage_karyawan**: Kelola Data Karyawan
- **view_reports**: Lihat Laporan
- **manage_overtime**: Kelola Lembur
- **approve_overtime**: Approve Lembur

### **Scope Types:**
- **all**: Semua Data (akses penuh)
- **entitas**: Berdasarkan Entitas (multi-store support)
- **departemen**: Berdasarkan Departemen
- **divisi**: Berdasarkan Divisi
- **custom**: Custom (pilih karyawan tertentu)

## 🔧 Cara Penggunaan

### **1. Menambah Permission Baru**
1. Masuk ke **HR Management > Permission Management**
2. Klik **"Tambah Permission"**
3. Pilih **Karyawan** yang akan diberi permission
4. Pilih **Jenis Permission** (approve_cuti, view_absensi, dll)
5. Pilih **Ruang Lingkup**:
   - **Semua Data**: Akses ke semua data dalam sistem
   - **Entitas**: Pilih entitas/toko tertentu
   - **Departemen**: Pilih departemen tertentu
   - **Divisi**: Pilih divisi tertentu
   - **Custom**: Pilih karyawan tertentu
6. Tambahkan **Deskripsi** (opsional)
7. Set **Status Aktif**
8. **Simpan**

### **2. Melihat Permission Summary**
1. Masuk ke **HR Management > Permission Summary**
2. Pilih **Karyawan** dari dropdown
3. Lihat **Visual Overview** semua permission
4. Klik **"Tambah Permission"** untuk menambah
5. Klik **"Kelola Permission"** untuk edit existing

### **3. Mengelola Permission Existing**
1. Masuk ke **Permission Management**
2. Gunakan **Filter** atau **Tab** untuk mencari permission
3. Klik **"Edit"** untuk mengubah permission
4. Klik **"Toggle Status"** untuk aktif/nonaktif
5. Klik **"Delete"** untuk menghapus permission

## 🔍 Contoh Skenario Penggunaan

### **Skenario 1: Manager HRD Multi-Entitas**
```
Karyawan: John Doe (Manager HRD)
Permission: approve_cuti
Scope: entitas
Values: [Toko A, Toko B, Toko C]
Result: John bisa approve cuti untuk karyawan di 3 toko tersebut
```

### **Skenario 2: Supervisor Departemen**
```
Karyawan: Jane Smith (Supervisor IT)
Permission: view_absensi
Scope: departemen
Values: [IT Department]
Result: Jane bisa lihat absensi semua karyawan IT
```

### **Skenario 3: Kepala Divisi**
```
Karyawan: Bob Wilson (Kepala Sales)
Permission: manage_jadwal
Scope: divisi
Values: [Sales Division]
Result: Bob bisa kelola jadwal semua karyawan sales
```

### **Skenario 4: Custom Permission**
```
Karyawan: Alice Brown (Team Lead)
Permission: approve_cuti
Scope: custom
Values: [Karyawan A, Karyawan B, Karyawan C]
Result: Alice hanya bisa approve cuti untuk 3 karyawan tertentu
```

## 🛠️ Implementasi Teknis

### **Database Structure:**
```sql
karyawan_permissions:
- id
- karyawan_id (FK to karyawan)
- permission_type (enum)
- scope_type (enum)
- scope_values (JSON array)
- is_active (boolean)
- description (text)
- created_by (FK to users)
- timestamps
```

### **Service Methods:**
```php
// Check permission
PermissionService::hasPermission('approve_cuti', $cutiRecord);

// Get accessible karyawan IDs
PermissionService::getAccessibleKaryawanIds('view_absensi');

// Apply filter to query
PermissionService::applyPermissionFilter($query, 'manage_jadwal');

// Specific permission checks
PermissionService::canApproveCuti($cutiRecord);
PermissionService::canViewAbsensi($absensiRecord);
```

### **Model Methods:**
```php
// Check permission for specific data
KaryawanPermission::hasPermission($karyawanId, 'approve_cuti', $targetData);

// Get accessible data IDs
KaryawanPermission::getAccessibleDataIds($karyawanId, 'view_absensi');
```

## 🔒 Security Features

### **Access Control:**
- ✅ **Role-based Access**: Super admin bypass semua permission
- ✅ **Hierarchical Permissions**: Permission berdasarkan struktur organisasi
- ✅ **Scope Validation**: Validasi akses berdasarkan scope yang ditetapkan
- ✅ **Audit Trail**: Track siapa yang membuat/mengubah permission

### **Data Protection:**
- ✅ **Filtered Queries**: Query otomatis difilter berdasarkan permission
- ✅ **Secure Checks**: Validasi permission sebelum aksi sensitif
- ✅ **Granular Control**: Control akses sampai level individual karyawan

## 📊 Integration dengan Existing Resources

### **CutiIzinResource:**
```php
// Filter berdasarkan permission
public static function getEloquentQuery(): Builder
{
    return PermissionService::applyPermissionFilter(
        parent::getEloquentQuery(),
        'approve_cuti',
        'karyawan_id'
    );
}

// Check permission untuk approve action
->visible(fn ($record) => PermissionService::canApproveCuti($record))
```

### **AbsensiResource:**
```php
// Filter berdasarkan permission
public static function getEloquentQuery(): Builder
{
    return PermissionService::applyPermissionFilter(
        parent::getEloquentQuery(),
        'view_absensi',
        'karyawan_id'
    );
}
```

## 🎯 Benefits

### **Untuk HR:**
- **Flexible Management**: Atur permission sesuai struktur organisasi
- **Visual Overview**: Lihat semua permission dalam satu tempat
- **Easy Maintenance**: Aktif/nonaktif permission tanpa delete
- **Audit Capability**: Track perubahan permission

### **Untuk Manager:**
- **Appropriate Access**: Akses sesuai dengan tanggung jawab
- **Multi-level Control**: Bisa manage berdasarkan entitas/departemen/divisi
- **Secure Operations**: Hanya bisa akses data yang diizinkan

### **Untuk System:**
- **Scalable Architecture**: Mudah ditambah permission type baru
- **Performance Optimized**: Query efficient dengan proper indexing
- **Maintainable Code**: Clean separation of concerns

## 🚀 Future Enhancements

### **Possible Improvements:**
- **Time-based Permissions**: Permission dengan masa berlaku
- **Conditional Permissions**: Permission berdasarkan kondisi tertentu
- **Permission Templates**: Template permission untuk role tertentu
- **Bulk Permission Management**: Assign permission ke multiple karyawan
- **Permission History**: Track history perubahan permission
- **API Integration**: REST API untuk external system integration

---

**Catatan**: Sistem ini memberikan fleksibilitas penuh untuk HR dalam mengatur akses karyawan sesuai dengan struktur dan kebutuhan organisasi.
