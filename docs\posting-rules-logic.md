# Logic Posting Rules untuk Sistem Akuntansi Komprehensif

## Overview
Dokumen ini menjelaskan logic posting rules untuk semua modul dalam sistem akuntansi yang akan diimplementasikan. Setiap transaksi akan secara otomatis mengh<PERSON>lkan jurnal berdasarkan aturan posting yang telah didefinisikan.

## 1. <PERSON><PERSON><PERSON> (Procurement)

### 1.1 Purchase Order (PO)
**Source Type**: `PurchaseOrder`
**Trigger**: Saat PO di-approve
**Logic**: Tidak ada jurnal untuk PO, hanya commitment accounting (opsional)

### 1.2 Goods Receipt
**Source Type**: `GoodsReceipt`
**Trigger**: Saat barang diterima dan goods receipt di-complete
**Jurnal**:
```
Dr. Inventory (Persediaan)           [total_cost]
    Cr. Goods Received Not Invoiced      [total_cost]
```

### 1.3 Purchase Invoice
**Source Type**: `PurchaseInvoice`
**Trigger**: Saat invoice di-approve

#### A. Invoice dengan Goods Receipt (3-way matching)
```
Dr. Goods Received Not Invoiced      [invoice_amount]
Dr. Tax Input (jika ada)             [tax_amount]
    Cr. Accounts Payable                 [total_amount]
```

#### B. Invoice tanpa Goods Receipt (Direct Purchase)
```
Dr. Inventory/Expense Account        [subtotal]
Dr. Tax Input (jika ada)             [tax_amount]
    Cr. Accounts Payable                 [total_amount]
```

### 1.4 Purchase Payment
**Source Type**: `PurchasePayment`
**Trigger**: Saat pembayaran di-complete
**Jurnal**:
```
Dr. Accounts Payable                 [payment_amount]
    Cr. Cash/Bank Account                [payment_amount]
```

## 2. Modul Pengeluaran (Expense Management)

### 2.1 Expense Request Approval
**Source Type**: `ExpenseRequest`
**Trigger**: Saat expense request di-approve
**Logic**: Tidak ada jurnal, hanya approval workflow

### 2.2 Cash Disbursement
**Source Type**: `CashDisbursement`
**Trigger**: Saat disbursement di-complete
**Jurnal**:
```
Dr. Expense Accounts (per item)      [item_amounts]
    Cr. Cash/Bank Account                [total_amount]
```

### 2.3 Petty Cash Replenishment
**Source Type**: `PettyCashTransaction`
**Trigger**: Saat replenishment di-complete
**Jurnal**:
```
Dr. Petty Cash Account               [amount]
    Cr. Cash/Bank Account                [amount]
```

### 2.4 Petty Cash Disbursement
**Source Type**: `PettyCashTransaction`
**Trigger**: Saat disbursement di-complete
**Jurnal**:
```
Dr. Expense Account                  [amount]
    Cr. Petty Cash Account               [amount]
```

## 3. Modul Payroll Enhancement

### 3.1 Payroll Transaction
**Source Type**: `PayrollTransaction`
**Trigger**: Saat payroll di-approve
**Jurnal**:
```
Dr. Salary Expense                   [gross_salary]
Dr. BPJS Company Expense             [company_bpjs]
    Cr. Salary Payable                   [net_salary]
    Cr. Tax Payable (PPh 21)             [income_tax]
    Cr. BPJS Payable (Employee)          [employee_bpjs]
    Cr. BPJS Payable (Company)           [company_bpjs]
    Cr. Other Deductions Payable         [other_deductions]
```

### 3.2 Salary Payment
**Source Type**: `SalaryPayment`
**Trigger**: Saat gaji dibayar
**Jurnal**:
```
Dr. Salary Payable                   [net_salary]
    Cr. Cash/Bank Account                [net_salary]
```

### 3.3 Tax Payment (PPh 21)
**Source Type**: `TaxPayment`
**Trigger**: Saat pajak dibayar ke negara
**Jurnal**:
```
Dr. Tax Payable (PPh 21)             [tax_amount]
    Cr. Cash/Bank Account                [tax_amount]
```

## 4. Modul Inventaris Enhancement

### 4.1 Stock Transfer
**Source Type**: `StockTransfer`
**Trigger**: Saat transfer di-complete
**Jurnal**: Tidak ada jurnal antar gudang dalam entitas yang sama, hanya update inventory_stocks

### 4.2 Stock Transfer Antar Entitas
**Source Type**: `StockTransfer`
**Trigger**: Saat transfer antar entitas di-complete
**Jurnal**:
```
# Di Entitas Pengirim:
Dr. Inter-Entity Receivable          [transfer_value]
    Cr. Inventory                        [transfer_value]

# Di Entitas Penerima:
Dr. Inventory                        [transfer_value]
    Cr. Inter-Entity Payable             [transfer_value]
```

### 4.3 Stock Adjustment
**Source Type**: `StockAdjustment`
**Trigger**: Saat adjustment di-approve

#### A. Stock Increase
```
Dr. Inventory                        [adjustment_value]
    Cr. Inventory Adjustment Gain        [adjustment_value]
```

#### B. Stock Decrease
```
Dr. Inventory Adjustment Loss        [adjustment_value]
    Cr. Inventory                        [adjustment_value]
```

### 4.4 Stock Opname Adjustment
**Source Type**: `StockOpname`
**Trigger**: Saat opname di-complete dan ada variance
**Jurnal**: Sama dengan Stock Adjustment berdasarkan variance

## 5. Conditional Logic untuk Posting Rules

### 5.1 Trigger Conditions
Setiap posting rule dapat memiliki kondisi pemicu:
```json
{
    "status": "Approved",
    "total_amount": ">0",
    "payment_method": "Bank_Transfer"
}
```

### 5.2 Calculation Expressions
Untuk perhitungan dinamis:
```json
{
    "calculation_expression": "source.subtotal * 0.11",
    "description": "PPN 11%"
}
```

### 5.3 Account Mapping
Mapping akun berdasarkan kondisi:
```json
{
    "account_mapping": {
        "expense_category_id": {
            "1": "5101", // Transport -> Transport Expense
            "2": "5102", // Meals -> Meals Expense
            "3": "5103"  // Office Supplies -> Office Expense
        }
    }
}
```

## 6. Implementation Strategy

### 6.1 Enhanced PostingRuleEntry Model
Tambahkan field untuk conditional logic:
- `condition_field`: Field yang akan dievaluasi
- `condition_operator`: Operator (=, >, <, !=, in, not_in)
- `condition_value`: Nilai pembanding
- `account_mapping`: JSON untuk mapping akun dinamis

### 6.2 PostingRuleEngine Service
Service untuk:
- Evaluasi kondisi kompleks
- Perhitungan expression dinamis
- Mapping akun berdasarkan kondisi
- Validasi balance jurnal (Debit = Credit)

### 6.3 Integration Points
- Model Events (created, updated, approved)
- Observer Pattern untuk auto-posting
- Queue Jobs untuk posting yang kompleks
- Rollback mechanism untuk error handling

## 7. Account Structure Requirements

### 7.1 Asset Accounts
- 1001: Kas
- 1002: Bank
- 1101: Piutang Dagang
- 1102: Piutang Karyawan
- 1103: Inter-Entity Receivable
- 1201: Persediaan Barang Dagang
- 1301: Peralatan Kantor
- 1401: Kas Kecil

### 7.2 Liability Accounts
- 2001: Hutang Dagang
- 2002: Hutang Gaji
- 2003: Hutang Pajak PPh 21
- 2004: Hutang BPJS
- 2005: Inter-Entity Payable
- 2101: Goods Received Not Invoiced

### 7.3 Equity Accounts
- 3001: Modal
- 3002: Laba Ditahan

### 7.4 Revenue Accounts
- 4001: Penjualan
- 4002: Pendapatan Lain-lain
- 4003: Inventory Adjustment Gain

### 7.5 Expense Accounts
- 5001: Harga Pokok Penjualan
- 5101: Beban Gaji
- 5102: Beban BPJS Perusahaan
- 5103: Beban Transport
- 5104: Beban Makan
- 5105: Beban Perlengkapan Kantor
- 5106: Inventory Adjustment Loss
- 5201: Beban Pajak Input

## 8. Testing Strategy

### 8.1 Unit Tests
- Test setiap posting rule secara individual
- Test conditional logic
- Test calculation expressions

### 8.2 Integration Tests
- Test end-to-end flow dari transaksi ke jurnal
- Test rollback scenarios
- Test dengan data volume besar

### 8.3 Validation Tests
- Validasi balance jurnal
- Validasi account mapping
- Validasi business rules
