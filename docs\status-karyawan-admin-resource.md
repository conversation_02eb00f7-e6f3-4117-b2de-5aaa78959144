# Status Karyawan Resource - Panel Admin

Resource untuk monitoring real-time status karyawan di panel admin dengan fitur lengkap untuk manajemen dan supervisi kehadiran.

## 🎯 Fitur Utama

### **1. Monitoring Komprehensif**
- ✅ Status real-time semua karyawan hari ini
- ✅ Auto-refresh setiap 30 detik
- ✅ Filter multi-level (status, entitas, departemen, divisi)
- ✅ Tab dengan badge count untuk setiap kategori
- ✅ Modal detail dengan informasi lengkap

### **2. Dashboard Widget**
- ✅ StatusKaryawanOverview widget di dashboard admin
- ✅ Clickable stats yang langsung ke tab terkait
- ✅ Real-time counts untuk monitoring cepat

### **3. Informasi Detail**
- ✅ Data personal lengkap (foto, nama, NIP, jabatan, departemen, divisi, entitas)
- ✅ Status dan keterangan detail
- ✅ Jadwal kerja dengan shift dan waktu
- ✅ Kontak langsung (telepon/email)
- ✅ Info approval untuk cuti/izin/sakit

## 📊 Status yang Dimonitor

### **Kategori Status:**
- 🟢 **Hadir** - <PERSON><PERSON><PERSON> yang sudah absen hadir
- 🟡 **Terlambat** - Karyawan yang absen terlambat
- 🔴 **Alpha** - Karyawan yang ada jadwal tapi tidak absen
- 🤒 **Sakit** - Karyawan yang sedang sakit (approved)
- 🏖️ **Cuti** - Karyawan yang sedang cuti (approved)
- 📝 **Izin** - Karyawan yang sedang izin (approved)
- 🌙 **Libur** - Karyawan yang tidak ada jadwal hari ini

### **Prioritas Logika Status:**
1. **Cuti/Izin/Sakit** (tertinggi) - Jika ada permohonan approved hari ini
2. **Status Absensi** - Jika sudah absen (hadir/terlambat)
3. **Alpha** - Jika ada jadwal tapi belum absen > 2 jam
4. **Tidak Ada Jadwal** (terendah) - Jika tidak ada jadwal hari ini

## 🔍 Fitur Filter & Search

### **Filter Options:**
- **Status Filter**: Dropdown untuk filter berdasarkan status tertentu
- **Entitas Filter**: Filter berdasarkan lokasi/toko
- **Departemen Filter**: Filter berdasarkan departemen
- **Divisi Filter**: Filter berdasarkan divisi

### **Tab Navigation:**
- **Semua Karyawan**: Menampilkan semua karyawan aktif
- **Cuti/Izin/Sakit**: Karyawan yang sedang tidak masuk dengan alasan
- **Libur/Tidak Ada Jadwal**: Karyawan yang tidak dijadwalkan hari ini
- **Hadir**: Karyawan yang sudah absen hadir
- **Terlambat**: Karyawan yang absen terlambat
- **Alpha**: Karyawan yang tidak hadir tanpa keterangan

## 📱 Modal Detail

### **Informasi yang Ditampilkan:**
- **Header**: Foto profil, nama, NIP, jabatan, departemen, divisi, entitas
- **Status Hari Ini**: Badge dengan icon dan keterangan detail
- **Kontak**: Nomor HP (clickable untuk telepon) dan email
- **Jadwal**: Shift dan waktu kerja hari ini
- **Absensi**: Waktu masuk/keluar dan keterangan
- **Cuti/Izin/Sakit**: Detail periode, alasan, dan approver

### **Fitur Kontak:**
- ✅ **Nomor HP**: Click to call dan copy to clipboard
- ✅ **Email**: Click to open email client
- ✅ **Copy notification**: Feedback saat berhasil copy

## 🎛️ Dashboard Widget

### **StatusKaryawanOverview Widget:**
```php
// Stats yang ditampilkan:
- Total Karyawan Aktif (clickable ke semua)
- Hadir Hari Ini (clickable ke tab hadir)
- Cuti/Izin/Sakit (clickable ke tab cuti_izin_sakit)
- Terlambat (clickable ke tab terlambat)
- Alpha (clickable ke tab alpha)
- Libur/Tidak Ada Jadwal (clickable ke tab libur)
```

### **Widget Features:**
- ✅ Real-time data update
- ✅ Clickable stats untuk navigasi cepat
- ✅ Color-coded untuk visual clarity
- ✅ Icon yang sesuai dengan status

## 🔧 Implementasi Teknis

### **File Structure:**
```
app/Filament/Resources/
├── StatusKaryawanResource.php
└── StatusKaryawanResource/
    └── Pages/
        └── ListStatusKaryawan.php

app/Filament/Widgets/
└── StatusKaryawanOverview.php

resources/views/filament/admin/modals/
└── status-detail.blade.php
```

### **Database Queries Optimization:**
- **Efficient joins** dengan proper eager loading
- **Indexed queries** untuk performance
- **Cached badge counts** untuk tabs
- **Minimal database calls** dengan smart caching

### **Real-time Features:**
- **Auto-refresh** setiap 30 detik
- **Manual refresh** button
- **Live badge updates** di tabs
- **Real-time status calculation**

## 📋 Use Cases untuk Admin

### **HR Management:**
- Monitor kehadiran real-time seluruh karyawan
- Identifikasi karyawan yang alpha untuk follow-up
- Tracking cuti/izin yang sedang berlangsung
- Kontak langsung karyawan yang bermasalah
- Laporan status harian untuk management

### **Operational Monitoring:**
- Cek ketersediaan staff per departemen/divisi
- Monitor coverage untuk setiap shift
- Identifikasi bottleneck operasional
- Koordinasi replacement untuk yang absent

### **Compliance & Reporting:**
- Dokumentasi kehadiran untuk payroll
- Tracking absensi untuk performance review
- Compliance dengan regulasi ketenagakerjaan
- Historical data untuk trend analysis

## 🔒 Security & Access Control

### **Admin Privileges:**
- Full access ke semua data karyawan
- Dapat melihat semua entitas/departemen/divisi
- Access ke informasi kontak lengkap
- View approval history untuk cuti/izin

### **Data Privacy:**
- Secure access dengan proper authentication
- Role-based data filtering (jika diperlukan)
- Audit trail untuk access monitoring
- Proper data encryption untuk sensitive info

## 🚀 Performance Optimization

### **Database Optimization:**
- Proper indexing pada tanggal dan karyawan_id
- Eager loading untuk relationships
- Query optimization untuk large datasets
- Caching untuk frequently accessed data

### **UI/UX Optimization:**
- Lazy loading untuk large tables
- Pagination untuk better performance
- Responsive design untuk mobile access
- Fast search dengan debouncing

## 📊 Monitoring & Analytics

### **Real-time Metrics:**
- Total karyawan aktif
- Percentage kehadiran hari ini
- Trend absensi per departemen
- Alert untuk high absence rate

### **Reporting Capabilities:**
- Export data ke Excel/PDF
- Custom date range reports
- Departmental attendance reports
- Individual employee attendance history

## 🎯 Benefits untuk Admin

### **Operational Efficiency:**
- Quick overview status seluruh karyawan
- Fast identification of attendance issues
- Direct contact untuk immediate action
- Real-time decision making support

### **Management Insights:**
- Attendance patterns analysis
- Department-wise performance monitoring
- Resource allocation optimization
- Proactive absence management

### **Compliance Support:**
- Accurate attendance records
- Proper documentation for audits
- Regulatory compliance tracking
- Evidence-based HR decisions

---

**Catatan**: Resource ini memberikan admin kontrol penuh untuk monitoring dan manajemen kehadiran karyawan dengan interface yang user-friendly dan data real-time yang akurat.
