# Status Karyawan Resource - Panel Karyawan

Resource untuk menampilkan status real-time karyawan yang sedang cuti, izin, sakit, libur, atau tidak ada jadwal pada hari tertentu.

## 🎯 Fitur Utama

### **1. Monitoring Status Real-time**
- ✅ Status karyawan hari ini (hadir, terlambat, alpha, cuti, izin, sakit, libur)
- ✅ Auto-refresh setiap 30 detik
- ✅ Filter berdasarkan status, entitas, departemen, divisi
- ✅ Tab untuk kategori status yang berbeda

### **2. Informasi Lengkap**
- ✅ Foto profil karyawan
- ✅ Data personal (nama, NIP, jabatan, divisi, entitas)
- ✅ Status dan keterangan detail
- ✅ Jadwal kerja hari ini
- ✅ Kontak (nomor HP dengan fitur call/copy)

### **3. Modal Detail**
- ✅ Informasi lengkap status karyawan
- ✅ Detail jadwal dan absensi
- ✅ Informasi cuti/izin/sakit yang sedang berlangsung
- ✅ Kontak langsung (telepon/email)

## 📊 Widget Dashboard

### **StatusKaryawanOverview Widget**
Menampilkan ringkasan status karyawan di dashboard:
- Total karyawan aktif
- Hadir hari ini
- Cuti/Izin/Sakit
- Terlambat
- Alpha
- Libur/Tidak ada jadwal

## 🔍 Logika Status

### **Prioritas Penentuan Status:**
1. **Cuti/Izin/Sakit** - Jika ada permohonan yang approved dan tanggal hari ini dalam range
2. **Tidak Ada Jadwal** - Jika tidak ada jadwal kerja hari ini
3. **Status Absensi** - Jika sudah absen (hadir/terlambat)
4. **Alpha** - Jika ada jadwal tapi belum absen dan sudah lewat 2 jam dari waktu masuk

### **Keterangan Status:**
- **Cuti/Izin/Sakit**: Alasan + periode tanggal
- **Absensi**: Keterangan dari record absensi
- **Tidak Ada Jadwal**: "Tidak ada jadwal kerja hari ini"
- **Menunggu**: "Menunggu absensi"

## 📱 Fitur Kontak

### **Nomor HP**
- ✅ Clickable untuk langsung telepon
- ✅ Copy to clipboard
- ✅ Notifikasi saat berhasil copy

### **Email**
- ✅ Clickable untuk buka email client

## 🎨 UI/UX Features

### **Badge Status**
- 🟢 **Hadir**: Hijau dengan ikon check-circle
- 🟡 **Terlambat**: Kuning dengan ikon clock
- 🔴 **Alpha/Sakit**: Merah dengan ikon x-circle/heart
- 🔵 **Cuti/Izin**: Biru dengan ikon calendar/document
- ⚪ **Libur**: Abu-abu dengan ikon moon

### **Tabs dengan Badge Count**
- Semua Karyawan (total)
- Cuti/Izin/Sakit (count)
- Libur/Tidak Ada Jadwal (count)
- Hadir (count)
- Terlambat (count)
- Alpha (count)

### **Auto-refresh**
- Polling setiap 30 detik
- Manual refresh button
- Real-time data update

## 🔧 Implementasi Teknis

### **Files Structure**
```
app/Filament/Karyawan/Resources/
├── StatusKaryawanResource.php
└── StatusKaryawanResource/
    └── Pages/
        └── ListStatusKaryawan.php

app/Filament/Karyawan/Widgets/
└── StatusKaryawanOverview.php

resources/views/filament/karyawan/modals/
└── status-detail.blade.php
```

### **Database Queries**
- **Cuti/Izin/Sakit**: Query `cuti_izin` dengan status approved dan tanggal range
- **Jadwal**: Query `jadwal_kerja` untuk hari ini
- **Absensi**: Query `absensi` untuk hari ini
- **Alpha**: Difference antara yang ada jadwal vs yang sudah absen vs yang cuti

### **Performance Optimization**
- Efficient queries dengan proper indexing
- Eager loading relationships
- Cached badge counts
- Minimal database calls

## 📋 Cara Penggunaan

### **Akses Resource**
1. Login ke panel karyawan
2. Klik menu "Status Karyawan Hari Ini"
3. Lihat daftar karyawan dengan status real-time

### **Filter Data**
1. Gunakan dropdown "Filter Status" untuk status tertentu
2. Filter berdasarkan Entitas/Departemen/Divisi
3. Gunakan tabs untuk kategori cepat

### **Lihat Detail**
1. Klik tombol "Detail" pada baris karyawan
2. Modal akan menampilkan informasi lengkap
3. Klik nomor HP untuk telepon langsung

### **Dashboard Widget**
1. Widget otomatis muncul di dashboard karyawan
2. Klik angka untuk langsung ke tab terkait
3. Data update otomatis

## 🎯 Use Cases

### **HR/Admin**
- Monitor kehadiran karyawan real-time
- Identifikasi karyawan yang alpha
- Kontak langsung karyawan yang bermasalah
- Laporan status harian

### **Supervisor**
- Cek tim yang sedang cuti/izin
- Monitor kehadiran anak buah
- Koordinasi jadwal kerja
- Follow up absensi

### **Karyawan**
- Lihat status rekan kerja
- Cek siapa yang sedang cuti/izin
- Koordinasi dengan tim
- Informasi kontak kolega

## 🔒 Security & Permissions

### **Access Control**
- Hanya untuk panel karyawan
- Data sesuai dengan entitas/divisi user (jika ada pembatasan)
- Read-only resource (tidak bisa create/edit/delete)

### **Data Privacy**
- Nomor HP hanya visible untuk yang berhak
- Email sesuai dengan privacy setting
- Foto profil dengan proper storage access

## 🚀 Future Enhancements

### **Possible Improvements**
- Export data ke Excel/PDF
- Notifikasi push untuk status tertentu
- Integration dengan WhatsApp untuk kontak
- Historical data dan trends
- Mobile responsive optimization
- Bulk actions untuk admin

### **Advanced Features**
- Geolocation tracking
- Attendance analytics
- Predictive absence alerts
- Integration dengan payroll system
- Custom status categories

---

**Catatan**: Resource ini bersifat read-only dan fokus pada monitoring real-time status karyawan untuk memudahkan koordinasi dan manajemen kehadiran.
