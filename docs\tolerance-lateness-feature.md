# Fitur Toleransi Keterlambatan Absensi

## Deskripsi

Fitur ini memungkinkan pengguna dengan permission `manage_absensi` untuk memberikan toleransi kepada karyawan yang terlambat absen. Toleransi ini akan mencatat alasan, siapa yang memberikan, dan kapan toleransi diberikan.

## Fitur yang Ditambahkan

### 1. Kolom Database Baru di Tabel Absensi

-   `is_tolerance_given` (boolean): Status apakah toleransi diberikan
-   `tolerance_reason` (text): Alasan pemberian toleransi
-   `tolerance_approved_by` (foreign key): User yang memberikan toleransi
-   `tolerance_approved_at` (timestamp): Waktu pemberian toleransi

### 2. Kolom Tampilan di Tabel Absensi

-   **Kolom Toleransi**: Icon check/X dengan tooltip yang menampilkan alasan toleransi
-   **Kolom Toleransi oleh**: Nama user yang memberikan toleransi (tersembunyi secara default)
-   **Kolom Waktu Toleransi**: Waktu pemberian toleransi (tersembunyi secara default)

### 3. Action Buttons

-   **Berikan Toleransi**: Tombol untuk memberikan toleransi dengan form input alasan
-   **Hapus Toleransi**: Tombol untuk menghapus toleransi yang sudah diberikan

### 4. Form Edit Toleransi

-   **Section Toleransi Keterlambatan**: Section khusus di form edit absensi
-   **Toggle Berikan Toleransi**: Switch untuk mengaktifkan/menonaktifkan toleransi
-   **Field Alasan Toleransi**: Textarea untuk input alasan (muncul jika toggle aktif)
-   **Informasi Toleransi**: Placeholder yang menampilkan detail toleransi yang sudah diberikan

### 4. Filter Tambahan

-   **Status Toleransi**: Filter berdasarkan status toleransi (Diberikan/Tidak Ada)
-   **Hanya yang Diberi Toleransi**: Toggle filter untuk menampilkan hanya yang diberi toleransi

## Permission dan Akses

### Syarat Akses

Fitur ini hanya dapat diakses oleh user yang memiliki:

-   Permission `manage_absensi` yang aktif di tabel `karyawan_permissions`
-   User harus terhubung dengan record karyawan (`karyawan_id` tidak null)

### Cara Cek Permission

```php
$hasPermission = $user->karyawan && $user->karyawan->permissions()
    ->where('permission_type', 'manage_absensi')
    ->where('is_active', true)
    ->exists();
```

## Cara Kerja

### 1. Memberikan Toleransi

-   Tombol "Berikan Toleransi" hanya muncul jika:
    -   User memiliki permission `manage_absensi`
    -   Status absensi adalah 'terlambat'
    -   Belum diberi toleransi sebelumnya
-   User mengisi alasan toleransi di form
-   Sistem mencatat siapa yang memberikan dan kapan

### 2. Menghapus Toleransi

-   Tombol "Hapus Toleransi" hanya muncul jika:
    -   User memiliki permission `manage_absensi`
    -   Sudah diberi toleransi sebelumnya
-   Memerlukan konfirmasi sebelum menghapus
-   Menghapus semua data toleransi

### 3. Model Methods

```php
// Memberikan toleransi
$absensi->giveTolerance($reason, $approvedBy);

// Menghapus toleransi
$absensi->removeTolerance();

// Cek apakah diberi toleransi
$absensi->hasToleranceGiven();
```

## Relasi Database

### Model Absensi

```php
// Relasi ke user yang memberikan toleransi
public function toleranceApprovedBy()
{
    return $this->belongsTo(User::class, 'tolerance_approved_by');
}
```

## Penggunaan

### 1. Memberikan Toleransi

1. Buka halaman Data Absensi
2. Cari record dengan status 'terlambat'
3. Klik tombol "Berikan Toleransi" (icon jam)
4. Isi alasan toleransi di form
5. Klik "Simpan"

### 2. Melihat Status Toleransi

-   Kolom "Toleransi" menampilkan icon check (hijau) jika diberi toleransi
-   Hover pada icon untuk melihat alasan toleransi
-   Kolom tersembunyi dapat diaktifkan untuk melihat detail

### 3. Filter Data

-   Gunakan filter "Status Toleransi" untuk melihat berdasarkan status
-   Gunakan toggle "Hanya yang Diberi Toleransi" untuk fokus pada yang diberi toleransi

### 4. Menghapus Toleransi

1. Cari record yang sudah diberi toleransi
2. Klik tombol "Hapus Toleransi" (icon X merah)
3. Konfirmasi penghapusan

### 5. Menggunakan Form Edit

1. Buka halaman Data Absensi
2. Klik tombol "Edit" pada record absensi
3. Scroll ke section "Toleransi Keterlambatan" (hanya muncul jika ada permission dan status terlambat)
4. Aktifkan toggle "Berikan Toleransi"
5. Isi field "Alasan Toleransi"
6. Klik "Simpan" untuk menyimpan perubahan

### 6. Melihat Informasi Toleransi di Form Edit

-   Jika toleransi sudah diberikan, akan muncul box informasi berwarna biru
-   Menampilkan alasan, siapa yang memberikan, dan kapan diberikan
-   Toggle dapat dimatikan untuk menghapus toleransi

## Notifikasi

### Berhasil Memberikan Toleransi

-   Judul: "Toleransi Berhasil Diberikan"
-   Pesan: "Toleransi keterlambatan untuk [Nama Karyawan] telah diberikan."

### Berhasil Menghapus Toleransi

-   Judul: "Toleransi Berhasil Dihapus"
-   Pesan: "Toleransi keterlambatan untuk [Nama Karyawan] telah dihapus."

## Keamanan

1. **Permission-based Access**: Hanya user dengan permission yang tepat yang dapat mengakses
2. **Validation**: Form memerlukan alasan yang tidak boleh kosong
3. **Confirmation**: Penghapusan toleransi memerlukan konfirmasi
4. **Audit Trail**: Semua perubahan tercatat dengan timestamp dan user

## Catatan Penting

-   Toleransi hanya bisa diberikan untuk absensi dengan status 'terlambat'
-   Satu absensi hanya bisa memiliki satu toleransi aktif
-   Toleransi dapat dihapus dan diberikan kembali jika diperlukan
-   Data toleransi tersimpan permanen untuk audit trail
-   Permission `manage_absensi` harus aktif di tabel `karyawan_permissions`
