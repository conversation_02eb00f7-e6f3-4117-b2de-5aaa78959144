# WhatsApp Integration dengan Starsender API

Dokumentasi ini menjelaskan cara menggunakan fitur WhatsApp notification yang telah diintegrasikan dengan Starsender API.

## Konfigurasi

### 1. Environment Variables

Tambahkan konfigurasi berikut ke file `.env`:

```env
# WhatsApp Starsender Configuration
STARSENDER_API_URL=https://api.starsender.online
STARSENDER_API_TOKEN=your_starsender_token_here
STARSENDER_ENABLED=true
```

### 2. Mendapatkan Token Starsender

1. Daftar di [Starsender](https://app.starsender.online)
2. Buat device/instance WhatsApp
3. Dapatkan API token dari dashboard
4. Masukkan token ke file `.env`

## Penggunaan

### 1. Menggunakan WhatsAppService Langsung

```php
use App\Services\WhatsAppService;

$whatsAppService = app(WhatsAppService::class);

// Kirim pesan sederhana
$result = $whatsAppService->sendMessage(
    phoneNumber: '6281234567890',
    message: 'Halo, ini pesan dari sistem HR.'
);

// Kirim pesan dengan media
$result = $whatsAppService->sendMediaMessage(
    phoneNumber: '6281234567890',
    mediaUrl: 'https://example.com/image.jpg',
    caption: 'Ini adalah gambar penting'
);

// Kirim pesan massal
$recipients = [
    ['phone' => '6281234567890', 'message' => 'Pesan untuk user 1'],
    ['phone' => '6281234567891', 'message' => 'Pesan untuk user 2'],
];
$results = $whatsAppService->sendBulkMessages($recipients);
```

### 2. Menggunakan Trait di Filament Resource

#### Tambahkan Trait ke Resource Page

```php
<?php

namespace App\Filament\Resources\KaryawanResource\Pages;

use App\Filament\Resources\KaryawanResource;
use App\Traits\HasWhatsAppNotifications;
use Filament\Resources\Pages\ListRecords;

class ListKaryawan extends ListRecords
{
    use HasWhatsAppNotifications;

    protected static string $resource = KaryawanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Action untuk kirim WhatsApp individual
            $this->whatsAppNotificationAction(
                phoneField: 'no_hp',
                defaultMessage: 'Halo {nama_lengkap}, ini pesan dari HR.'
            ),
            
            // Action untuk kirim WhatsApp massal
            $this->bulkWhatsAppNotificationAction(
                phoneField: 'no_hp',
                nameField: 'nama_lengkap',
                defaultMessage: 'Halo {name}, ini pengumuman penting dari HR.'
            ),
        ];
    }
}
```

#### Menambahkan Action ke Table

```php
// Di dalam method table() di Resource
->actions([
    // Action WhatsApp untuk record individual
    $this->getRecordWhatsAppAction(
        record: $record,
        phoneField: 'no_hp',
        messageTemplate: 'Halo, ini pesan khusus untuk Anda.'
    ),
    
    // Action lainnya...
    Tables\Actions\ViewAction::make(),
    Tables\Actions\EditAction::make(),
])
```

### 3. Implementasi Custom Actions

#### Contoh: Action Approval dengan WhatsApp

```php
use App\Filament\Resources\CutiIzinResource\Actions\WhatsAppCutiActions;

// Di dalam method table() di CutiIzinResource
->actions([
    // Gunakan action yang sudah include WhatsApp notification
    WhatsAppCutiActions::approveWithWhatsApp(),
    WhatsAppCutiActions::rejectWithWhatsApp(),
    WhatsAppCutiActions::manualWhatsAppAction(),
])
```

## Fitur yang Tersedia

### 1. WhatsAppService

- `sendMessage()` - Kirim pesan teks
- `sendMediaMessage()` - Kirim pesan dengan media
- `sendBulkMessages()` - Kirim pesan massal
- `isEnabled()` - Cek status service
- `getStatus()` - Dapatkan informasi status

### 2. HasWhatsAppNotifications Trait

- `whatsAppNotificationAction()` - Action untuk kirim WhatsApp individual
- `bulkWhatsAppNotificationAction()` - Action untuk kirim WhatsApp massal
- `getRecordWhatsAppAction()` - Action WhatsApp untuk record tertentu

### 3. Custom Actions

- `WhatsAppCutiActions` - Actions khusus untuk approval cuti dengan notifikasi WhatsApp

## Format Nomor Telepon

Service akan otomatis memformat nomor telepon:
- Input: `081234567890` → Output: `6281234567890`
- Input: `6281234567890` → Output: `6281234567890` (tidak berubah)
- Input: `+6281234567890` → Output: `6281234567890`

## Error Handling

Service menggunakan try-catch dan logging untuk menangani error:
- Error akan dicatat di log Laravel
- Notifikasi Filament akan ditampilkan untuk user
- Proses utama tidak akan terganggu jika WhatsApp gagal

## Best Practices

1. **Selalu cek status service** sebelum mengirim pesan
2. **Gunakan delay** untuk pesan massal (sudah diimplementasi)
3. **Validasi nomor telepon** sebelum mengirim
4. **Handle error gracefully** - jangan biarkan error WhatsApp mengganggu proses utama
5. **Personalisasi pesan** dengan menggunakan placeholder seperti `{name}`

## Troubleshooting

### WhatsApp tidak terkirim
1. Cek konfigurasi token di `.env`
2. Pastikan `STARSENDER_ENABLED=true`
3. Cek log Laravel untuk error details
4. Verifikasi nomor telepon format Indonesia

### Token tidak valid
1. Login ke dashboard Starsender
2. Regenerate token jika perlu
3. Update token di `.env`
4. Clear cache: `php artisan config:clear`

### Rate limiting
Service sudah mengimplementasi delay 0.5 detik antar pesan untuk menghindari rate limiting.
