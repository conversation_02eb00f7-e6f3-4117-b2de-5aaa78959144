<?php

/**
 * Contoh testing WhatsApp notification untuk cuti/izin
 * 
 * Jalankan di tinker:
 * php artisan tinker
 * include 'examples/test-cuti-whatsapp.php';
 */

use App\Models\CutiIzin;
use App\Models\Karyawan;
use App\Services\WhatsAppService;
use Carbon\Carbon;

echo "🧪 Testing WhatsApp Notification untuk Cuti/Izin\n";
echo "================================================\n\n";

// 1. Cek status WhatsApp service
echo "1. Checking WhatsApp Service Status...\n";
$whatsAppService = app(WhatsAppService::class);
$status = $whatsAppService->getStatus();

echo "   - Enabled: " . ($status['enabled'] ? 'Yes' : 'No') . "\n";
echo "   - Configured: " . ($status['configured'] ? 'Yes' : 'No') . "\n";
echo "   - API URL: " . $status['api_url'] . "\n\n";

if (!$whatsAppService->isEnabled()) {
    echo "❌ WhatsApp service is not enabled. Please check your .env configuration.\n";
    return;
}

// 2. Get test karyawan
echo "2. Getting test karyawan...\n";
$karyawan = Karyawan::first();
if (!$karyawan) {
    echo "❌ No karyawan found. Please create a karyawan first.\n";
    return;
}

echo "   - Using: {$karyawan->nama_lengkap} (ID: {$karyawan->id})\n\n";

// 3. Test pengajuan cuti baru
echo "3. Testing new cuti submission...\n";
echo "   - Creating test cuti request...\n";

$cutiIzin = CutiIzin::create([
    'karyawan_id' => $karyawan->id,
    'jenis_permohonan' => 'cuti',
    'tanggal_mulai' => Carbon::tomorrow(),
    'tanggal_selesai' => Carbon::tomorrow()->addDays(1),
    'jumlah_hari' => 2,
    'alasan' => 'Test cuti untuk keperluan keluarga',
    'keterangan_tambahan' => 'Testing WhatsApp notification system',
    'status' => 'pending'
]);

echo "   ✅ Cuti created with ID: {$cutiIzin->id}\n";
echo "   📱 WhatsApp notification should be sent to: 085272726519\n\n";

// 4. Test approval notification
echo "4. Testing approval notification...\n";
sleep(2); // Wait a moment

$cutiIzin->update([
    'status' => 'approved',
    'approved_by' => 1, // Assuming user ID 1 exists
    'approved_at' => now()
]);

echo "   ✅ Cuti approved\n";
echo "   📱 WhatsApp notifications should be sent to:\n";
echo "      - Admin: 085272726519\n";
echo "      - Karyawan: " . ($karyawan->no_hp ?? 'No phone number') . "\n\n";

// 5. Test rejection notification
echo "5. Testing rejection notification...\n";
sleep(2); // Wait a moment

$cutiIzin->update([
    'status' => 'rejected',
    'approved_by' => 1,
    'approved_at' => now(),
    'rejection_reason' => 'Test rejection - insufficient leave balance'
]);

echo "   ❌ Cuti rejected\n";
echo "   📱 WhatsApp notifications should be sent to:\n";
echo "      - Admin: 085272726519\n";
echo "      - Karyawan: " . ($karyawan->no_hp ?? 'No phone number') . "\n\n";

// 6. Cleanup
echo "6. Cleaning up test data...\n";
$cutiIzin->delete();
echo "   🗑️ Test cuti record deleted\n\n";

echo "✅ Test completed!\n";
echo "📋 Check your WhatsApp messages at 085272726519\n";
echo "📊 Check logs: tail -f storage/logs/laravel.log | grep WhatsApp\n";

// 7. Show recent WhatsApp logs
echo "\n7. Recent WhatsApp logs:\n";
echo "========================\n";

$logFile = storage_path('logs/laravel.log');
if (file_exists($logFile)) {
    $logs = file($logFile);
    $whatsappLogs = array_filter($logs, function($line) {
        return strpos($line, 'WhatsApp') !== false;
    });
    
    $recentLogs = array_slice($whatsappLogs, -5); // Last 5 logs
    
    if (empty($recentLogs)) {
        echo "No WhatsApp logs found.\n";
    } else {
        foreach ($recentLogs as $log) {
            echo trim($log) . "\n";
        }
    }
} else {
    echo "Log file not found.\n";
}

echo "\n🎉 Testing script completed!\n";
