#!/bin/bash

echo "🔧 Fixing PHP Compatibility for Hostinger Server..."

# Backup original composer files
echo "📋 Backing up original composer files..."
cp composer.json composer.json.backup
cp composer.lock composer.lock.backup

echo "🔄 Updating composer.json for PHP 8.1 compatibility..."

# Remove composer.lock to force fresh resolution
rm -f composer.lock

echo "📦 Installing compatible packages..."
# Install with PHP 8.1 compatible versions
composer install --no-dev --optimize-autoloader --ignore-platform-reqs

if [ $? -eq 0 ]; then
    echo "✅ Packages installed successfully!"
    
    echo "📁 Creating vendor archive for server upload..."
    
    # Create minimal vendor archive
    tar -czf vendor-php81-compatible.tar.gz \
        vendor/maatwebsite/ \
        vendor/phpoffice/ \
        vendor/barryvdh/ \
        vendor/dompdf/ \
        vendor/phenx/ \
        vendor/sabberworm/ \
        vendor/psr/ \
        vendor/composer/ \
        vendor/autoload.php \
        2>/dev/null
    
    echo "✅ Vendor archive created: vendor-php81-compatible.tar.gz"
    echo "📊 Archive size: $(du -h vendor-php81-compatible.tar.gz | cut -f1)"
    
    echo ""
    echo "📤 Upload Instructions for Server:"
    echo "=================================="
    echo "1. Upload these files to server:"
    echo "   - vendor-php81-compatible.tar.gz"
    echo "   - composer.json"
    echo "   - composer.lock"
    echo ""
    echo "2. On server, extract vendor:"
    echo "   tar -xzf vendor-php81-compatible.tar.gz"
    echo ""
    echo "3. Run optimization:"
    echo "   composer dump-autoload --optimize --no-dev"
    echo "   php artisan config:clear"
    echo "   php artisan cache:clear"
    
else
    echo "❌ Failed to install packages. Trying alternative approach..."
    
    echo "🔄 Trying with --ignore-platform-reqs..."
    composer update --no-dev --optimize-autoloader --ignore-platform-reqs
    
    if [ $? -eq 0 ]; then
        echo "✅ Packages updated with platform requirements ignored!"
        
        # Create vendor archive
        tar -czf vendor-ignore-platform.tar.gz \
            vendor/ \
            composer.json \
            composer.lock
        
        echo "✅ Full vendor archive created: vendor-ignore-platform.tar.gz"
        echo "📊 Archive size: $(du -h vendor-ignore-platform.tar.gz | cut -f1)"
    else
        echo "❌ Still failed. Using CSV-only fallback approach..."
        
        # Restore original files
        cp composer.json.backup composer.json
        cp composer.lock.backup composer.lock
        
        echo "📝 Creating CSV-only configuration..."
        echo "The system will automatically use CSV export without Excel packages."
    fi
fi

echo ""
echo "🎯 Alternative Solutions:"
echo "========================"
echo "1. Update PHP version in Hostinger cPanel to 8.3+"
echo "2. Use the generated vendor archive"
echo "3. Use CSV export only (no additional packages needed)"
echo ""
echo "💡 CSV export works without any additional packages and supports:"
echo "   - UTF-8 encoding"
echo "   - Excel compatibility"
echo "   - All export features"
