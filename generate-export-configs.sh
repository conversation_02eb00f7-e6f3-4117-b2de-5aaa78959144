#!/bin/bash

echo "📁 Generating Export Configuration Files..."

# Create config directory if not exists
mkdir -p config

echo "📊 Creating Excel config file..."
cat > config/excel.php << 'EOF'
<?php

use Maatwebsite\Excel\Excel;

return [
    'exports' => [
        'chunk_size' => 1000,
        'pre_calculate_formulas' => false,
        'strict_null_comparison' => false,
        'csv' => [
            'delimiter' => ',',
            'enclosure' => '"',
            'escape_character' => '\\',
            'contiguous' => false,
            'input_encoding' => 'UTF-8',
        ],
        'properties' => [
            'creator' => '',
            'lastModifiedBy' => '',
            'title' => '',
            'description' => '',
            'subject' => '',
            'keywords' => '',
            'category' => '',
            'manager' => '',
            'company' => '',
        ],
    ],

    'imports' => [
        'read_only' => true,
        'ignore_empty' => false,
        'heading_row' => [
            'formatter' => 'slug'
        ],
        'csv' => [
            'delimiter' => ',',
            'enclosure' => '"',
            'escape_character' => '\\',
            'contiguous' => false,
            'input_encoding' => 'UTF-8',
        ],
        'properties' => [
            'creator' => '',
            'lastModifiedBy' => '',
            'title' => '',
            'description' => '',
            'subject' => '',
            'keywords' => '',
            'category' => '',
            'manager' => '',
            'company' => '',
        ],
    ],

    'extension_detector' => [
        'xlsx' => Excel::XLSX,
        'xlsm' => Excel::XLSX,
        'xltx' => Excel::XLSX,
        'xltm' => Excel::XLSX,
        'xls' => Excel::XLS,
        'xlt' => Excel::XLS,
        'ods' => Excel::ODS,
        'ots' => Excel::ODS,
        'slk' => Excel::SLK,
        'xml' => Excel::XML,
        'gnumeric' => Excel::GNUMERIC,
        'htm' => Excel::HTML,
        'html' => Excel::HTML,
        'csv' => Excel::CSV,
        'tsv' => Excel::TSV,

        'pdf' => Excel::DOMPDF,
    ],

    'value_binder' => [
        'default' => Maatwebsite\Excel\DefaultValueBinder::class,
    ],

    'cache' => [
        'driver' => 'memory',
        'batch' => [
            'memory_limit' => 60000,
        ],
        'illuminate' => [
            'store' => null,
        ],
    ],

    'transactions' => [
        'handler' => 'db',
        'db' => [
            'connection' => null,
        ],
    ],

    'temporary_files' => [
        'local_path' => storage_path('app'),
        'remote_disk' => null,
        'remote_prefix' => null,
        'force_resync_remote' => null,
    ],
];
EOF

echo "📄 Creating DomPDF config file..."
cat > config/dompdf.php << 'EOF'
<?php

return [
    'show_warnings' => false,
    'public_path' => null,
    'convert_entities' => true,
    'options' => [
        'font_dir' => storage_path('fonts'),
        'font_cache' => storage_path('fonts'),
        'temp_dir' => sys_get_temp_dir(),
        'chroot' => realpath(base_path()),
        'allowed_protocols' => [
            'file://' => ['rules' => []],
            'http://' => ['rules' => []],
            'https://' => ['rules' => []],
        ],
        'log_output_file' => null,
        'enable_font_subsetting' => false,
        'pdf_backend' => 'CPDF',
        'default_media_type' => 'screen',
        'default_paper_size' => 'a4',
        'default_paper_orientation' => 'portrait',
        'default_font' => 'serif',
        'dpi' => 96,
        'enable_php' => false,
        'enable_javascript' => true,
        'enable_remote' => true,
        'font_height_ratio' => 1.1,
        'enable_html5_parser' => true,
    ],
];
EOF

echo "⚙️ Creating service provider registration script..."
cat > register-providers.php << 'EOF'
<?php

// Script to check and register service providers
$configPath = 'config/app.php';

if (!file_exists($configPath)) {
    echo "❌ config/app.php not found!\n";
    exit(1);
}

$config = file_get_contents($configPath);

// Check if Excel provider is registered
if (strpos($config, 'Maatwebsite\Excel\ExcelServiceProvider::class') === false) {
    echo "📊 Adding Excel Service Provider...\n";
    
    // Find the providers array and add Excel provider
    $pattern = '/\'providers\'\s*=>\s*\[([^]]+)\]/s';
    if (preg_match($pattern, $config, $matches)) {
        $providers = $matches[1];
        if (strpos($providers, 'App\Providers\RouteServiceProvider::class') !== false) {
            $newProviders = str_replace(
                'App\Providers\RouteServiceProvider::class,',
                "App\Providers\RouteServiceProvider::class,\n        Maatwebsite\Excel\ExcelServiceProvider::class,",
                $providers
            );
            $config = str_replace($providers, $newProviders, $config);
        }
    }
}

// Check if DomPDF provider is registered
if (strpos($config, 'Barryvdh\DomPDF\ServiceProvider::class') === false) {
    echo "📄 Adding DomPDF Service Provider...\n";
    
    // Find the providers array and add DomPDF provider
    $pattern = '/\'providers\'\s*=>\s*\[([^]]+)\]/s';
    if (preg_match($pattern, $config, $matches)) {
        $providers = $matches[1];
        if (strpos($providers, 'Maatwebsite\Excel\ExcelServiceProvider::class') !== false) {
            $newProviders = str_replace(
                'Maatwebsite\Excel\ExcelServiceProvider::class,',
                "Maatwebsite\Excel\ExcelServiceProvider::class,\n        Barryvdh\DomPDF\ServiceProvider::class,",
                $providers
            );
            $config = str_replace($providers, $newProviders, $config);
        }
    }
}

// Write back the config
file_put_contents($configPath, $config);

echo "✅ Service providers registered successfully!\n";
EOF

echo "📦 Creating archive with all necessary files..."
tar -czf export-configs.tar.gz \
    config/excel.php \
    config/dompdf.php \
    register-providers.php

echo ""
echo "✅ Export configuration files generated!"
echo ""
echo "📤 Upload Instructions:"
echo "======================"
echo "1. Upload export-configs.tar.gz to server"
echo "2. Extract: tar -xzf export-configs.tar.gz"
echo "3. Run: php register-providers.php"
echo "4. Clear cache:"
echo "   php artisan config:clear"
echo "   php artisan cache:clear"
echo "   php artisan config:cache"
echo ""
echo "📁 Files created:"
echo "   - config/excel.php"
echo "   - config/dompdf.php"
echo "   - register-providers.php"
echo "   - export-configs.tar.gz"
