<?php

/**
 * Generate sample photos with metadata overlay for testing
 * Run with: php generate_sample_photos.php
 */

// Create sample photos with metadata overlay
function generateSamplePhoto($width = 640, $height = 480, $metadata = []) {
    // Create a blank image
    $image = imagecreatetruecolor($width, $height);
    
    // Colors
    $blue = imagecolorallocate($image, 70, 130, 180);
    $white = imagecolorallocate($image, 255, 255, 255);
    $black = imagecolorallocate($image, 0, 0, 0);
    $green = imagecolorallocate($image, 16, 185, 129);
    $yellow = imagecolorallocate($image, 245, 158, 11);
    $gray = imagecolorallocate($image, 107, 114, 128);
    
    // Fill background with gradient-like effect
    for ($y = 0; $y < $height; $y++) {
        $color = imagecolorallocate($image, 
            70 + ($y / $height) * 50,
            130 + ($y / $height) * 50, 
            180 + ($y / $height) * 50
        );
        imageline($image, 0, $y, $width, $y, $color);
    }
    
    // Add a face-like circle (representing selfie)
    $centerX = $width / 2;
    $centerY = $height / 2 - 50;
    $radius = 80;
    
    // Face
    imagefilledellipse($image, $centerX, $centerY, $radius * 2, $radius * 2, $white);
    imageellipse($image, $centerX, $centerY, $radius * 2, $radius * 2, $black);
    
    // Eyes
    imagefilledellipse($image, $centerX - 25, $centerY - 20, 10, 10, $black);
    imagefilledellipse($image, $centerX + 25, $centerY - 20, 10, 10, $black);
    
    // Smile
    imagearc($image, $centerX, $centerY, 40, 30, 0, 180, $black);
    
    // Add metadata overlay at bottom
    $overlayHeight = 100;
    $overlayY = $height - $overlayHeight;
    
    // Semi-transparent overlay
    $overlay = imagecolorallocatealpha($image, 0, 0, 0, 50);
    imagefilledrectangle($image, 0, $overlayY, $width, $height, $overlay);
    
    // Font path (use built-in font)
    $font = 3;
    $smallFont = 2;
    
    // Add metadata text
    $y = $overlayY + 10;
    
    // Camera info (top right)
    if (isset($metadata['camera'])) {
        $text = $metadata['camera'];
        $textWidth = imagefontwidth($font) * strlen($text);
        imagefilledrectangle($image, $width - $textWidth - 20, 10, $width - 10, 30, $white);
        imagestring($image, $font, $width - $textWidth - 15, 15, $text, $black);
    }
    
    // Coordinates
    if (isset($metadata['coordinates'])) {
        imagestring($image, $font, 15, $y, "📍 " . $metadata['coordinates'], $white);
        $y += 20;
    }
    
    // DateTime
    if (isset($metadata['datetime'])) {
        imagestring($image, $font, 15, $y, "🕐 " . $metadata['datetime'], $white);
        $y += 20;
    }
    
    // Status
    if (isset($metadata['status'])) {
        $statusColor = $white;
        $emoji = "ℹ️";
        
        if ($metadata['status'] === 'Tepat Waktu') {
            $statusColor = $green;
            $emoji = "✅";
        } elseif ($metadata['status'] === 'Telat') {
            $statusColor = $yellow;
            $emoji = "⏰";
        }
        
        imagestring($image, $font, 15, $y, $emoji . " " . $metadata['status'], $statusColor);
    }
    
    return $image;
}

// Sample metadata sets
$metadataSets = [
    [
        'coordinates' => '-6.200000°, 106.816666°',
        'datetime' => '20/01/2025 08:00:00',
        'status' => 'Tepat Waktu',
        'camera' => 'XIAOMI 13T'
    ],
    [
        'coordinates' => '-6.175110°, 106.865036°',
        'datetime' => '20/01/2025 08:25:00',
        'status' => 'Telat',
        'camera' => 'SAMSUNG S23'
    ],
    [
        'coordinates' => '-6.261493°, 106.810600°',
        'datetime' => '20/01/2025 07:55:00',
        'status' => 'Tepat Waktu',
        'camera' => 'IPHONE 14 PRO'
    ],
    [
        'coordinates' => '-6.138414°, 106.813446°',
        'datetime' => '20/01/2025 17:00:00',
        'status' => 'Checkout Normal',
        'camera' => 'OPPO RENO 8'
    ]
];

// Create directories if they don't exist
$dirs = [
    'storage/app/public/absensi/masuk',
    'storage/app/public/absensi/keluar'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "Created directory: $dir\n";
    }
}

// Generate sample photos
for ($i = 1; $i <= 20; $i++) {
    $metadata = $metadataSets[array_rand($metadataSets)];
    
    // Generate check-in photo
    $checkinImage = generateSamplePhoto(640, 480, $metadata);
    $checkinPath = "storage/app/public/absensi/masuk/sample_{$i}_" . date('Ymd') . ".jpg";
    imagejpeg($checkinImage, $checkinPath, 85);
    imagedestroy($checkinImage);
    
    // Generate check-out photo
    $checkoutMetadata = $metadata;
    $checkoutMetadata['status'] = 'Checkout Normal';
    $checkoutMetadata['datetime'] = date('d/m/Y H:i:s', strtotime($metadata['datetime']) + 8 * 3600); // +8 hours
    
    $checkoutImage = generateSamplePhoto(640, 480, $checkoutMetadata);
    $checkoutPath = "storage/app/public/absensi/keluar/sample_{$i}_" . date('Ymd') . ".jpg";
    imagejpeg($checkoutImage, $checkoutPath, 85);
    imagedestroy($checkoutImage);
    
    echo "Generated photos for employee $i: $checkinPath, $checkoutPath\n";
}

echo "\n✅ Sample photos generated successfully!\n";
echo "📁 Check-in photos: storage/app/public/absensi/masuk/\n";
echo "📁 Check-out photos: storage/app/public/absensi/keluar/\n";
echo "\n💡 You can now test the photo metadata feature in the admin panel.\n";
