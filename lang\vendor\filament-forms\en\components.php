<?php

return [

    'builder' => [

        'actions' => [

            'clone' => [
                'label' => 'Clone',
            ],

            'add' => [
                'label' => 'Add to :label',
            ],

            'add_between' => [
                'label' => 'Insert between blocks',
            ],

            'delete' => [
                'label' => 'Delete',
            ],

            'reorder' => [
                'label' => 'Move',
            ],

            'move_down' => [
                'label' => 'Move down',
            ],

            'move_up' => [
                'label' => 'Move up',
            ],

            'collapse' => [
                'label' => 'Collapse',
            ],

            'expand' => [
                'label' => 'Expand',
            ],

            'collapse_all' => [
                'label' => 'Collapse all',
            ],

            'expand_all' => [
                'label' => 'Expand all',
            ],

        ],

    ],

    'checkbox_list' => [

        'actions' => [

            'deselect_all' => [
                'label' => 'Deselect all',
            ],

            'select_all' => [
                'label' => 'Select all',
            ],

        ],

    ],

    'file_upload' => [

        'editor' => [

            'actions' => [

                'cancel' => [
                    'label' => 'Cancel',
                ],

                'drag_crop' => [
                    'label' => 'Drag mode "crop"',
                ],

                'drag_move' => [
                    'label' => 'Drag mode "move"',
                ],

                'flip_horizontal' => [
                    'label' => 'Flip image horizontally',
                ],

                'flip_vertical' => [
                    'label' => 'Flip image vertically',
                ],

                'move_down' => [
                    'label' => 'Move image down',
                ],

                'move_left' => [
                    'label' => 'Move image to left',
                ],

                'move_right' => [
                    'label' => 'Move image to right',
                ],

                'move_up' => [
                    'label' => 'Move image up',
                ],

                'reset' => [
                    'label' => 'Reset',
                ],

                'rotate_left' => [
                    'label' => 'Rotate image to left',
                ],

                'rotate_right' => [
                    'label' => 'Rotate image to right',
                ],

                'set_aspect_ratio' => [
                    'label' => 'Set aspect ratio to :ratio',
                ],

                'save' => [
                    'label' => 'Save',
                ],

                'zoom_100' => [
                    'label' => 'Zoom image to 100%',
                ],

                'zoom_in' => [
                    'label' => 'Zoom in',
                ],

                'zoom_out' => [
                    'label' => 'Zoom out',
                ],

            ],

            'fields' => [

                'height' => [
                    'label' => 'Height',
                    'unit' => 'px',
                ],

                'rotation' => [
                    'label' => 'Rotation',
                    'unit' => 'deg',
                ],

                'width' => [
                    'label' => 'Width',
                    'unit' => 'px',
                ],

                'x_position' => [
                    'label' => 'X',
                    'unit' => 'px',
                ],

                'y_position' => [
                    'label' => 'Y',
                    'unit' => 'px',
                ],

            ],

            'aspect_ratios' => [

                'label' => 'Aspect ratios',

                'no_fixed' => [
                    'label' => 'Free',
                ],

            ],

            'svg' => [

                'messages' => [
                    'confirmation' => 'Editing SVG files is not recommended as it can result in quality loss when scaling.\n Are you sure you want to continue?',
                    'disabled' => 'Editing SVG files is disabled as it can result in quality loss when scaling.',
                ],

            ],

        ],

    ],

    'key_value' => [

        'actions' => [

            'add' => [
                'label' => 'Add row',
            ],

            'delete' => [
                'label' => 'Delete row',
            ],

            'reorder' => [
                'label' => 'Reorder row',
            ],

        ],

        'fields' => [

            'key' => [
                'label' => 'Key',
            ],

            'value' => [
                'label' => 'Value',
            ],

        ],

    ],

    'markdown_editor' => [

        'toolbar_buttons' => [
            'attach_files' => 'Attach files',
            'blockquote' => 'Blockquote',
            'bold' => 'Bold',
            'bullet_list' => 'Bullet list',
            'code_block' => 'Code block',
            'heading' => 'Heading',
            'italic' => 'Italic',
            'link' => 'Link',
            'ordered_list' => 'Numbered list',
            'redo' => 'Redo',
            'strike' => 'Strikethrough',
            'table' => 'Table',
            'undo' => 'Undo',
        ],

    ],

    'radio' => [

        'boolean' => [
            'true' => 'Yes',
            'false' => 'No',
        ],

    ],

    'repeater' => [

        'actions' => [

            'add' => [
                'label' => 'Add to :label',
            ],

            'add_between' => [
                'label' => 'Insert between',
            ],

            'delete' => [
                'label' => 'Delete',
            ],

            'clone' => [
                'label' => 'Clone',
            ],

            'reorder' => [
                'label' => 'Move',
            ],

            'move_down' => [
                'label' => 'Move down',
            ],

            'move_up' => [
                'label' => 'Move up',
            ],

            'collapse' => [
                'label' => 'Collapse',
            ],

            'expand' => [
                'label' => 'Expand',
            ],

            'collapse_all' => [
                'label' => 'Collapse all',
            ],

            'expand_all' => [
                'label' => 'Expand all',
            ],

        ],

    ],

    'rich_editor' => [

        'dialogs' => [

            'link' => [

                'actions' => [
                    'link' => 'Link',
                    'unlink' => 'Unlink',
                ],

                'label' => 'URL',

                'placeholder' => 'Enter a URL',

            ],

        ],

        'toolbar_buttons' => [
            'attach_files' => 'Attach files',
            'blockquote' => 'Blockquote',
            'bold' => 'Bold',
            'bullet_list' => 'Bullet list',
            'code_block' => 'Code block',
            'h1' => 'Title',
            'h2' => 'Heading',
            'h3' => 'Subheading',
            'italic' => 'Italic',
            'link' => 'Link',
            'ordered_list' => 'Numbered list',
            'redo' => 'Redo',
            'strike' => 'Strikethrough',
            'underline' => 'Underline',
            'undo' => 'Undo',
        ],

    ],

    'select' => [

        'actions' => [

            'create_option' => [

                'modal' => [

                    'heading' => 'Create',

                    'actions' => [

                        'create' => [
                            'label' => 'Create',
                        ],

                        'create_another' => [
                            'label' => 'Create & create another',
                        ],

                    ],

                ],

            ],

            'edit_option' => [

                'modal' => [

                    'heading' => 'Edit',

                    'actions' => [

                        'save' => [
                            'label' => 'Save',
                        ],

                    ],

                ],

            ],

        ],

        'boolean' => [
            'true' => 'Yes',
            'false' => 'No',
        ],

        'loading_message' => 'Loading...',

        'max_items_message' => 'Only :count can be selected.',

        'no_search_results_message' => 'No options match your search.',

        'placeholder' => 'Select an option',

        'searching_message' => 'Searching...',

        'search_prompt' => 'Start typing to search...',

    ],

    'tags_input' => [
        'placeholder' => 'New tag',
    ],

    'text_input' => [

        'actions' => [

            'hide_password' => [
                'label' => 'Hide password',
            ],

            'show_password' => [
                'label' => 'Show password',
            ],

        ],

    ],

    'toggle_buttons' => [

        'boolean' => [
            'true' => 'Yes',
            'false' => 'No',
        ],

    ],

    'wizard' => [

        'actions' => [

            'previous_step' => [
                'label' => 'Back',
            ],

            'next_step' => [
                'label' => 'Next',
            ],

        ],

    ],

];
