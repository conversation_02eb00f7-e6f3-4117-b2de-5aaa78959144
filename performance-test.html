<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM vs Canvas Performance Test - Organizational Chart</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .test-area {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .test-section {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .canvas-container, .dom-container {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
            position: relative;
            overflow: hidden;
            background: #fff;
        }
        .performance-results {
            margin-top: 20px;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 5px;
        }
        .node {
            position: absolute;
            width: 120px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: transform 0.2s;
        }
        .node:hover {
            transform: scale(1.05);
        }
        .connection-line {
            position: absolute;
            background: #6b7280;
            z-index: 1;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .metric {
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .metric-label {
            font-weight: bold;
            color: #666;
            font-size: 12px;
        }
        .metric-value {
            font-size: 18px;
            color: #333;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DOM vs Canvas Performance Test - Organizational Chart</h1>
        <p>This test compares the performance of DOM-based vs Canvas-based rendering for organizational charts with varying numbers of nodes.</p>
        
        <div class="test-controls">
            <label for="nodeCount">Number of Nodes:</label>
            <select id="nodeCount">
                <option value="25">25 nodes (Small org)</option>
                <option value="50">50 nodes (Medium org)</option>
                <option value="100" selected>100 nodes (Large org)</option>
                <option value="200">200 nodes (Very large org)</option>
                <option value="500">500 nodes (Enterprise org)</option>
            </select>
            
            <button onclick="runPerformanceTest()">Run Performance Test</button>
            <button onclick="clearTests()">Clear All</button>
        </div>

        <div class="test-area">
            <div class="test-section">
                <h3>DOM-based Rendering</h3>
                <div id="domContainer" class="dom-container"></div>
            </div>
            
            <div class="test-section">
                <h3>Canvas-based Rendering</h3>
                <div class="canvas-container">
                    <canvas id="canvasChart" width="500" height="400"></canvas>
                </div>
            </div>
        </div>

        <div class="performance-results">
            <h3>Performance Results</h3>
            <div class="metrics" id="performanceMetrics">
                <div class="metric">
                    <div class="metric-label">DOM Render Time</div>
                    <div class="metric-value" id="domRenderTime">-</div>
                </div>
                <div class="metric">
                    <div class="metric-label">Canvas Render Time</div>
                    <div class="metric-value" id="canvasRenderTime">-</div>
                </div>
                <div class="metric">
                    <div class="metric-label">DOM Memory Usage</div>
                    <div class="metric-value" id="domMemoryUsage">-</div>
                </div>
                <div class="metric">
                    <div class="metric-label">Canvas Memory Usage</div>
                    <div class="metric-value" id="canvasMemoryUsage">-</div>
                </div>
                <div class="metric">
                    <div class="metric-label">DOM Elements Created</div>
                    <div class="metric-value" id="domElementCount">-</div>
                </div>
                <div class="metric">
                    <div class="metric-label">Performance Winner</div>
                    <div class="metric-value" id="performanceWinner">-</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Generate test data for organizational chart
        function generateOrgData(nodeCount) {
            const data = {
                entitas: { id: 1, nama: 'Test Company' },
                departemen: []
            };
            
            const deptCount = Math.max(1, Math.floor(nodeCount / 20));
            const nodesPerDept = Math.floor(nodeCount / deptCount);
            
            for (let d = 0; d < deptCount; d++) {
                const dept = {
                    id: d + 1,
                    nama: `Department ${d + 1}`,
                    divisi: []
                };
                
                const divisiCount = Math.max(1, Math.floor(nodesPerDept / 5));
                const nodesPerDivisi = Math.floor(nodesPerDept / divisiCount);
                
                for (let div = 0; div < divisiCount; div++) {
                    const divisi = {
                        id: div + 1,
                        nama: `Division ${div + 1}`,
                        karyawan: []
                    };
                    
                    for (let k = 0; k < nodesPerDivisi; k++) {
                        divisi.karyawan.push({
                            id: k + 1,
                            nama: `Employee ${k + 1}`,
                            jabatan: { nama: `Position ${k + 1}` }
                        });
                    }
                    
                    dept.divisi.push(divisi);
                }
                
                data.departemen.push(dept);
            }
            
            return data;
        }

        // DOM-based rendering (similar to current implementation)
        function renderDOMChart(data, container) {
            const startTime = performance.now();
            const startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            
            container.innerHTML = '';
            let elementCount = 0;
            
            const config = {
                nodeWidth: 120,
                nodeHeight: 60,
                entitasY: 20,
                deptY: 120,
                divisiY: 220,
                karyawanStartY: 320,
                deptSpacing: 200,
                divisiSpacing: 150,
                karyawanSpacing: 130
            };
            
            // Render entitas
            const entitasNode = document.createElement('div');
            entitasNode.className = 'node';
            entitasNode.style.left = '200px';
            entitasNode.style.top = config.entitasY + 'px';
            entitasNode.textContent = data.entitas.nama;
            container.appendChild(entitasNode);
            elementCount++;
            
            // Render departments
            data.departemen.forEach((dept, deptIndex) => {
                const deptX = 50 + (deptIndex * config.deptSpacing);
                
                const deptNode = document.createElement('div');
                deptNode.className = 'node';
                deptNode.style.left = deptX + 'px';
                deptNode.style.top = config.deptY + 'px';
                deptNode.textContent = dept.nama;
                container.appendChild(deptNode);
                elementCount++;
                
                // Connection line from entitas to dept
                const line1 = document.createElement('div');
                line1.className = 'connection-line';
                line1.style.left = '260px';
                line1.style.top = (config.entitasY + config.nodeHeight) + 'px';
                line1.style.width = '2px';
                line1.style.height = (config.deptY - config.entitasY - config.nodeHeight) + 'px';
                container.appendChild(line1);
                elementCount++;
                
                // Render divisions
                dept.divisi.forEach((divisi, divIndex) => {
                    const divX = deptX + (divIndex * config.divisiSpacing) - 50;
                    
                    const divNode = document.createElement('div');
                    divNode.className = 'node';
                    divNode.style.left = divX + 'px';
                    divNode.style.top = config.divisiY + 'px';
                    divNode.textContent = divisi.nama;
                    container.appendChild(divNode);
                    elementCount++;
                    
                    // Render employees
                    divisi.karyawan.forEach((karyawan, kIndex) => {
                        const empX = divX + (kIndex * config.karyawanSpacing) - 100;
                        
                        const empNode = document.createElement('div');
                        empNode.className = 'node';
                        empNode.style.left = empX + 'px';
                        empNode.style.top = config.karyawanStartY + 'px';
                        empNode.textContent = karyawan.nama;
                        empNode.style.fontSize = '10px';
                        empNode.style.width = '100px';
                        empNode.style.height = '50px';
                        container.appendChild(empNode);
                        elementCount++;
                    });
                });
            });
            
            const endTime = performance.now();
            const endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            
            return {
                renderTime: endTime - startTime,
                memoryUsed: endMemory - startMemory,
                elementCount: elementCount
            };
        }

        // Canvas-based rendering
        function renderCanvasChart(data, canvas) {
            const startTime = performance.now();
            const startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const config = {
                nodeWidth: 120,
                nodeHeight: 60,
                entitasY: 20,
                deptY: 120,
                divisiY: 220,
                karyawanStartY: 320,
                deptSpacing: 200,
                divisiSpacing: 150,
                karyawanSpacing: 130
            };
            
            // Helper function to draw a node
            function drawNode(x, y, width, height, text, fontSize = 12) {
                // Create gradient
                const gradient = ctx.createLinearGradient(x, y, x + width, y + height);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                
                // Draw node
                ctx.fillStyle = gradient;
                ctx.fillRect(x, y, width, height);
                
                // Draw text
                ctx.fillStyle = 'white';
                ctx.font = `bold ${fontSize}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(text, x + width/2, y + height/2);
            }
            
            // Draw connection line
            function drawLine(x1, y1, x2, y2) {
                ctx.strokeStyle = '#6b7280';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            }
            
            // Render entitas
            drawNode(200, config.entitasY, config.nodeWidth, config.nodeHeight, data.entitas.nama);
            
            // Render departments
            data.departemen.forEach((dept, deptIndex) => {
                const deptX = 50 + (deptIndex * config.deptSpacing);
                
                drawNode(deptX, config.deptY, config.nodeWidth, config.nodeHeight, dept.nama);
                
                // Connection line from entitas to dept
                drawLine(260, config.entitasY + config.nodeHeight, 260, config.deptY);
                
                // Render divisions
                dept.divisi.forEach((divisi, divIndex) => {
                    const divX = deptX + (divIndex * config.divisiSpacing) - 50;
                    
                    drawNode(divX, config.divisiY, config.nodeWidth, config.nodeHeight, divisi.nama);
                    
                    // Render employees
                    divisi.karyawan.forEach((karyawan, kIndex) => {
                        const empX = divX + (kIndex * config.karyawanSpacing) - 100;
                        
                        drawNode(empX, config.karyawanStartY, 100, 50, karyawan.nama, 10);
                    });
                });
            });
            
            const endTime = performance.now();
            const endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            
            return {
                renderTime: endTime - startTime,
                memoryUsed: endMemory - startMemory,
                elementCount: 1 // Only canvas element
            };
        }

        // Run performance test
        function runPerformanceTest() {
            const nodeCount = parseInt(document.getElementById('nodeCount').value);
            const data = generateOrgData(nodeCount);
            
            // Test DOM rendering
            const domContainer = document.getElementById('domContainer');
            const domResults = renderDOMChart(data, domContainer);
            
            // Test Canvas rendering
            const canvas = document.getElementById('canvasChart');
            const canvasResults = renderCanvasChart(data, canvas);
            
            // Update results
            document.getElementById('domRenderTime').textContent = domResults.renderTime.toFixed(2) + ' ms';
            document.getElementById('canvasRenderTime').textContent = canvasResults.renderTime.toFixed(2) + ' ms';
            document.getElementById('domMemoryUsage').textContent = (domResults.memoryUsed / 1024).toFixed(2) + ' KB';
            document.getElementById('canvasMemoryUsage').textContent = (canvasResults.memoryUsed / 1024).toFixed(2) + ' KB';
            document.getElementById('domElementCount').textContent = domResults.elementCount;
            
            // Determine winner
            const winner = domResults.renderTime < canvasResults.renderTime ? 'DOM' : 'Canvas';
            const improvement = Math.abs(domResults.renderTime - canvasResults.renderTime).toFixed(2);
            document.getElementById('performanceWinner').textContent = `${winner} (${improvement}ms faster)`;
        }

        // Clear all tests
        function clearTests() {
            document.getElementById('domContainer').innerHTML = '';
            const canvas = document.getElementById('canvasChart');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Reset metrics
            document.querySelectorAll('.metric-value').forEach(el => el.textContent = '-');
        }

        // Run initial test
        window.addEventListener('load', () => {
            runPerformanceTest();
        });
    </script>
</body>
</html>
