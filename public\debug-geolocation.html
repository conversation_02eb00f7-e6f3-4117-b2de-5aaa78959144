<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Geolocation Debug - Viera Attendance System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .status.success { background: #e8f5e8; border-color: #4caf50; color: #2e7d32; }
        .status.error { background: #ffebee; border-color: #f44336; color: #c62828; }
        .status.warning { background: #fff3e0; border-color: #ff9800; color: #ef6c00; }
        .status.info { background: #e3f2fd; border-color: #2196f3; color: #1976d2; }
        
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1976d2; }
        button.success { background: #4caf50; }
        button.success:hover { background: #45a049; }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .info-box {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        
        .console-output {
            background: #1e1e1e;
            color: #fff;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .coordinates {
            font-family: monospace;
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            margin: 5px 0;
        }
        
        .step {
            background: #f0f8ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .step h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Geolocation Debug Tool</h1>
        <p>This tool will help diagnose geolocation issues in your development environment.</p>
        
        <div class="info-grid">
            <div class="info-box">
                <h3>Environment Info</h3>
                <p><strong>Protocol:</strong> <span id="protocol"></span></p>
                <p><strong>Host:</strong> <span id="host"></span></p>
                <p><strong>User Agent:</strong> <span id="userAgent" style="font-size: 11px;"></span></p>
                <p><strong>Geolocation Support:</strong> <span id="geoSupport"></span></p>
                <p><strong>Permissions API:</strong> <span id="permissionsSupport"></span></p>
            </div>
            
            <div class="info-box">
                <h3>Quick Actions</h3>
                <button onclick="runFullDiagnostic()">🔍 Run Full Diagnostic</button>
                <button onclick="testBasicGeolocation()">📍 Test Basic Geolocation</button>
                <button onclick="checkPermissions()">🔐 Check Permissions</button>
                <button onclick="clearConsole()">🗑️ Clear Console</button>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🧪 Diagnostic Steps</h2>
        
        <div class="step">
            <h4>Step 1: Check Browser Support</h4>
            <div id="step1-result">Click "Run Full Diagnostic" to start</div>
        </div>
        
        <div class="step">
            <h4>Step 2: Check Permissions</h4>
            <div id="step2-result">Waiting for Step 1...</div>
        </div>
        
        <div class="step">
            <h4>Step 3: Test Geolocation Request</h4>
            <div id="step3-result">Waiting for Step 2...</div>
        </div>
        
        <div class="step">
            <h4>Step 4: Analyze Results</h4>
            <div id="step4-result">Waiting for Step 3...</div>
        </div>
    </div>

    <div class="container">
        <h2>📊 Results</h2>
        <div id="results-container">
            <p>No results yet. Run the diagnostic to see detailed information.</p>
        </div>
    </div>

    <div class="container">
        <h2>🖥️ Console Output</h2>
        <div id="console-output" class="console-output">
            Console output will appear here...
        </div>
    </div>

    <script>
        // Initialize page
        document.getElementById('protocol').textContent = location.protocol;
        document.getElementById('host').textContent = location.host;
        document.getElementById('userAgent').textContent = navigator.userAgent;
        document.getElementById('geoSupport').textContent = navigator.geolocation ? '✅ Supported' : '❌ Not Supported';
        document.getElementById('permissionsSupport').textContent = 'permissions' in navigator ? '✅ Supported' : '❌ Not Supported';

        // Console capture
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function logToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type.toUpperCase()}: ${args.join(' ')}`;
            const consoleOutput = document.getElementById('console-output');
            consoleOutput.textContent += message + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToConsole('log', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToConsole('error', ...args);
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToConsole('warn', ...args);
        };

        function updateStep(stepNumber, content, status = 'info') {
            const stepElement = document.getElementById(`step${stepNumber}-result`);
            stepElement.innerHTML = `<div class="status ${status}">${content}</div>`;
        }

        function addResult(title, data, status = 'info') {
            const container = document.getElementById('results-container');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${status}`;
            resultDiv.innerHTML = `<h4>${title}</h4><pre>${JSON.stringify(data, null, 2)}</pre>`;
            container.appendChild(resultDiv);
        }

        function clearConsole() {
            document.getElementById('console-output').textContent = 'Console cleared...\n';
            document.getElementById('results-container').innerHTML = '<p>Results cleared.</p>';
        }

        async function runFullDiagnostic() {
            console.log('🔍 Starting full geolocation diagnostic...');
            clearConsole();
            
            // Step 1: Browser Support
            updateStep(1, 'Checking browser support...', 'info');
            const hasGeolocation = !!navigator.geolocation;
            const hasPermissions = 'permissions' in navigator;
            
            if (hasGeolocation) {
                updateStep(1, '✅ Geolocation API is supported', 'success');
                console.log('✅ Geolocation API supported');
            } else {
                updateStep(1, '❌ Geolocation API is not supported', 'error');
                console.error('❌ Geolocation API not supported');
                return;
            }

            // Step 2: Check Permissions
            updateStep(2, 'Checking permissions...', 'info');
            if (hasPermissions) {
                try {
                    const permission = await navigator.permissions.query({name: 'geolocation'});
                    updateStep(2, `✅ Permission status: ${permission.state}`, permission.state === 'granted' ? 'success' : 'warning');
                    console.log('🔐 Permission status:', permission.state);
                    addResult('Permission Status', {state: permission.state});
                } catch (error) {
                    updateStep(2, '⚠️ Could not check permissions', 'warning');
                    console.warn('⚠️ Permission check failed:', error);
                }
            } else {
                updateStep(2, '⚠️ Permissions API not available', 'warning');
                console.warn('⚠️ Permissions API not available');
            }

            // Step 3: Test Geolocation
            updateStep(3, 'Testing geolocation request...', 'info');
            testGeolocationWithCallback((success, data) => {
                if (success) {
                    updateStep(3, '✅ Geolocation request successful', 'success');
                    updateStep(4, '🎉 Geolocation is working correctly!', 'success');
                    addResult('Geolocation Success', data, 'success');
                } else {
                    updateStep(3, `❌ Geolocation failed: ${data.message}`, 'error');
                    updateStep(4, '🔧 Geolocation failed - use manual input', 'warning');
                    addResult('Geolocation Error', data, 'error');
                }
            });
        }

        function testGeolocationWithCallback(callback) {
            const options = {
                enableHighAccuracy: true,
                timeout: 15000,
                maximumAge: 60000
            };

            console.log('📍 Requesting geolocation with options:', options);

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const data = {
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                        accuracy: position.coords.accuracy,
                        timestamp: new Date(position.timestamp).toLocaleString()
                    };
                    console.log('🎉 Geolocation success:', data);
                    callback(true, data);
                },
                function(error) {
                    const data = {
                        code: error.code,
                        message: error.message,
                        codeDescription: getErrorDescription(error.code)
                    };
                    console.error('❌ Geolocation error:', data);
                    callback(false, data);
                },
                options
            );
        }

        function getErrorDescription(code) {
            switch(code) {
                case 1: return 'PERMISSION_DENIED - User denied the request';
                case 2: return 'POSITION_UNAVAILABLE - Location information unavailable';
                case 3: return 'TIMEOUT - Request timed out';
                default: return 'UNKNOWN_ERROR';
            }
        }

        function testBasicGeolocation() {
            console.log('🎯 Testing basic geolocation...');
            testGeolocationWithCallback((success, data) => {
                if (success) {
                    addResult('Basic Geolocation Test - SUCCESS', data, 'success');
                } else {
                    addResult('Basic Geolocation Test - FAILED', data, 'error');
                }
            });
        }

        async function checkPermissions() {
            console.log('🔐 Checking permissions...');
            if ('permissions' in navigator) {
                try {
                    const permission = await navigator.permissions.query({name: 'geolocation'});
                    addResult('Permission Check', {state: permission.state}, 
                        permission.state === 'granted' ? 'success' : 'warning');
                } catch (error) {
                    addResult('Permission Check - ERROR', {error: error.message}, 'error');
                }
            } else {
                addResult('Permission Check', {message: 'Permissions API not supported'}, 'warning');
            }
        }

        // Auto-run basic checks on load
        window.addEventListener('load', function() {
            console.log('🌍 Geolocation Debug Tool loaded');
            console.log('Environment:', {
                protocol: location.protocol,
                hostname: location.hostname,
                port: location.port,
                userAgent: navigator.userAgent.substring(0, 100) + '...'
            });
        });
    </script>
</body>
</html>
