<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Geolocation Test - Viera Attendance System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .status.loading {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }
        .status.success {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }
        .status.error {
            background: #ffebee;
            border-color: #f44336;
            color: #c62828;
        }
        .status.warning {
            background: #fff3e0;
            border-color: #ff9800;
            color: #ef6c00;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
        .info-box {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .coordinates {
            font-family: monospace;
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 Geolocation Test - Viera Attendance System</h1>
        
        <div class="info-box">
            <h3>Environment Information:</h3>
            <p><strong>Protocol:</strong> <span id="protocol"></span></p>
            <p><strong>Host:</strong> <span id="host"></span></p>
            <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
            <p><strong>Geolocation Support:</strong> <span id="geoSupport"></span></p>
        </div>

        <div id="status-container">
            <div class="status loading">
                🔄 Ready to test geolocation...
            </div>
        </div>

        <div style="margin: 20px 0;">
            <button onclick="testGeolocation()">🎯 Test Geolocation</button>
            <button onclick="testPermissions()">🔐 Check Permissions</button>
            <button onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div id="results"></div>

        <div class="info-box">
            <h3>Expected Behavior:</h3>
            <ul>
                <li><strong>HTTPS:</strong> Should work normally</li>
                <li><strong>HTTP + localhost/127.0.0.1:</strong> Should work in most browsers</li>
                <li><strong>HTTP + other domains:</strong> Will likely fail</li>
                <li><strong>Permission denied:</strong> User needs to allow location access</li>
            </ul>
        </div>

        <div class="info-box">
            <h3>Jakarta Test Coordinates:</h3>
            <div class="coordinates">
                Latitude: -6.200000<br>
                Longitude: 106.816666
            </div>
        </div>
    </div>

    <script>
        // Initialize page info
        document.getElementById('protocol').textContent = location.protocol;
        document.getElementById('host').textContent = location.host;
        document.getElementById('userAgent').textContent = navigator.userAgent;
        document.getElementById('geoSupport').textContent = navigator.geolocation ? '✅ Supported' : '❌ Not Supported';

        function updateStatus(type, message) {
            const container = document.getElementById('status-container');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            container.appendChild(statusDiv);
        }

        function addResult(title, data) {
            const results = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'info-box';
            resultDiv.innerHTML = `<h4>${title}</h4><pre>${JSON.stringify(data, null, 2)}</pre>`;
            results.appendChild(resultDiv);
        }

        function testGeolocation() {
            updateStatus('loading', '🔄 Testing geolocation...');

            if (!navigator.geolocation) {
                updateStatus('error', '❌ Geolocation is not supported by this browser');
                return;
            }

            const options = {
                enableHighAccuracy: true,
                timeout: 15000,
                maximumAge: 60000
            };

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const coords = {
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                        accuracy: position.coords.accuracy,
                        timestamp: new Date(position.timestamp).toLocaleString()
                    };

                    updateStatus('success', `✅ Geolocation successful!<br>
                        Lat: ${coords.latitude.toFixed(6)}<br>
                        Lng: ${coords.longitude.toFixed(6)}<br>
                        Accuracy: ${Math.round(coords.accuracy)}m`);
                    
                    addResult('Geolocation Success', coords);
                },
                function(error) {
                    let errorMessage = '';
                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMessage = '❌ Permission denied by user';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMessage = '❌ Position information unavailable';
                            break;
                        case error.TIMEOUT:
                            errorMessage = '❌ Request timeout';
                            break;
                        default:
                            errorMessage = '❌ Unknown error';
                            break;
                    }

                    updateStatus('error', errorMessage);
                    addResult('Geolocation Error', {
                        code: error.code,
                        message: error.message
                    });
                },
                options
            );
        }

        function testPermissions() {
            updateStatus('loading', '🔐 Checking permissions...');

            if ('permissions' in navigator) {
                navigator.permissions.query({name: 'geolocation'}).then(function(result) {
                    const status = result.state;
                    let message = '';
                    let type = '';

                    switch(status) {
                        case 'granted':
                            message = '✅ Geolocation permission granted';
                            type = 'success';
                            break;
                        case 'denied':
                            message = '❌ Geolocation permission denied';
                            type = 'error';
                            break;
                        case 'prompt':
                            message = '⚠️ Geolocation permission will be prompted';
                            type = 'warning';
                            break;
                        default:
                            message = `ℹ️ Geolocation permission status: ${status}`;
                            type = 'warning';
                    }

                    updateStatus(type, message);
                    addResult('Permission Status', {state: status});
                });
            } else {
                updateStatus('warning', '⚠️ Permissions API not supported');
            }
        }

        function clearResults() {
            document.getElementById('status-container').innerHTML = `
                <div class="status loading">
                    🔄 Ready to test geolocation...
                </div>
            `;
            document.getElementById('results').innerHTML = '';
        }

        // Auto-test on page load
        window.addEventListener('load', function() {
            setTimeout(testPermissions, 1000);
        });
    </script>
</body>
</html>
