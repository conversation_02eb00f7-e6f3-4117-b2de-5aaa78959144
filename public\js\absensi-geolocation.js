// Absensi Geolocation JavaScript
document.addEventListener('DOMContentLoaded', function () {
    // For karyawan panel, only enable geolocation validation, disable form blocking
    const isKaryawanPanel = window.location.href.includes('/karyawan/');
    if (isKaryawanPanel) {
        console.log("🔧 Karyawan panel: enabling geolocation validation only");
        // Skip the aggressive form blocking for karyawan panel
        window.skipFormBlocking = true;
    }

    console.log('🚀 Absensi Geolocation script loaded');

    const demo = document.getElementById("location-demo");

    // Initialize geolocation functions
    window.getLocation = function () {
        if (navigator.geolocation) {
            demo.innerHTML = "🔍 Mengambil lokasi Anda...<br><small>Pastikan GPS aktif dan izinkan akses lokasi</small>";
            demo.className = "p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-800 mb-3";

            const options = {
                enableHighAccuracy: true,
                timeout: 15000,
                maximumAge: 300000
            };

            navigator.geolocation.getCurrentPosition(success, error, options);
        } else {
            demo.innerHTML = "❌ Geolocation tidak didukung browser ini.<br><PERSON><PERSON><PERSON> gunakan tombol Jakarta.";
            demo.className = "p-3 bg-red-50 border border-red-200 rounded-lg text-red-800 mb-3";
        }
    };

    function success(position) {
        const lat = position.coords.latitude.toFixed(6);
        const lng = position.coords.longitude.toFixed(6);
        const accuracy = Math.round(position.coords.accuracy);

        demo.innerHTML = "✅ Lokasi berhasil dideteksi!<br>" +
            "Latitude: " + lat + "<br>" +
            "Longitude: " + lng + "<br>" +
            "Akurasi: ±" + accuracy + " meter<br>" +
            "<small>🔍 Memvalidasi lokasi...</small>";
        demo.className = "p-3 bg-green-50 border border-green-200 rounded-lg text-green-800 mb-3";

        // Store coordinates globally
        window.currentCoordinates = {
            latitude: lat,
            longitude: lng,
            isValid: true
        };

        setCoordinates(lat, lng);
        validateLocationDistance(lat, lng);
        hideManualInput();
        console.log("🎉 Geolocation success:", { lat: lat, lng: lng, accuracy: accuracy });
    }

    function error(err) {
        let message = "❌ Gagal mendapatkan lokasi. ";

        if (err) {
            switch (err.code) {
                case err.PERMISSION_DENIED:
                    message += "Izin akses lokasi ditolak.<br>Silakan aktifkan izin lokasi di browser atau gunakan tombol Jakarta.";
                    break;
                case err.POSITION_UNAVAILABLE:
                    message += "Lokasi tidak tersedia.<br>Pastikan GPS aktif atau gunakan tombol Jakarta.";
                    break;
                case err.TIMEOUT:
                    message += "Waktu permintaan habis.<br>Silakan coba lagi atau gunakan tombol Jakarta.";
                    break;
                default:
                    message += "Terjadi kesalahan.<br>Silakan gunakan tombol Jakarta.";
                    break;
            }
        } else {
            message += "Silakan gunakan tombol Jakarta.";
        }

        demo.innerHTML = message;
        demo.className = "p-3 bg-red-50 border border-red-200 rounded-lg text-red-800 mb-3";
        console.log("❌ Geolocation error:", err);
    }

    window.useJakarta = function () {
        const lat = -6.200000;
        const lng = 106.816666;

        demo.innerHTML = "✅ Menggunakan koordinat Jakarta<br>Latitude: " + lat + "<br>Longitude: " + lng + "<br><small>🔍 Memvalidasi lokasi...</small>";
        demo.className = "p-3 bg-green-50 border border-green-200 rounded-lg text-green-800 mb-3";

        // Store coordinates globally
        window.currentCoordinates = {
            latitude: lat,
            longitude: lng,
            isValid: true
        };

        setCoordinates(lat, lng);
        validateLocationDistance(lat, lng);
        hideManualInput();
    };

    window.showManualInput = function () {
        document.getElementById("manual-input-container").style.display = "block";
        document.getElementById("manual-lat").focus();
    };

    window.hideManualInput = function () {
        document.getElementById("manual-input-container").style.display = "none";
        document.getElementById("manual-lat").value = "";
        document.getElementById("manual-lng").value = "";
    };

    window.useManualCoords = function () {
        const lat = parseFloat(document.getElementById("manual-lat").value);
        const lng = parseFloat(document.getElementById("manual-lng").value);

        if (isNaN(lat) || isNaN(lng)) {
            Swal.fire({
                icon: "error",
                title: "Input Tidak Valid! ❌",
                text: "Silakan masukkan koordinat yang valid (angka)",
                confirmButtonText: "OK",
                confirmButtonColor: "#EF4444"
            });
            return;
        }

        if (lat < -90 || lat > 90) {
            Swal.fire({
                icon: "error",
                title: "Latitude Tidak Valid! ❌",
                text: "Latitude harus antara -90 dan 90",
                confirmButtonText: "OK",
                confirmButtonColor: "#EF4444"
            });
            return;
        }

        if (lng < -180 || lng > 180) {
            Swal.fire({
                icon: "error",
                title: "Longitude Tidak Valid! ❌",
                text: "Longitude harus antara -180 dan 180",
                confirmButtonText: "OK",
                confirmButtonColor: "#EF4444"
            });
            return;
        }

        demo.innerHTML = "✅ Menggunakan koordinat manual<br>Latitude: " + lat + "<br>Longitude: " + lng + "<br><small>🔍 Memvalidasi lokasi...</small>";
        demo.className = "p-3 bg-green-50 border border-green-200 rounded-lg text-green-800 mb-3";

        // Store coordinates globally
        window.currentCoordinates = {
            latitude: lat,
            longitude: lng,
            isValid: true
        };

        setCoordinates(lat, lng);
        validateLocationDistance(lat, lng);
        hideManualInput();
    };

    function setCoordinates(lat, lng) {
        console.log("🔍 Setting coordinates:", { lat, lng });

        // Try multiple approaches to find and set the inputs
        const approaches = [
            // Approach 1: Direct name attribute
            () => {
                const latInput = document.querySelector("input[name=latitude]");
                const lngInput = document.querySelector("input[name=longitude]");
                return { latInput, lngInput, method: "name attribute" };
            },
            // Approach 2: Wire model (fixed selector)
            () => {
                const allInputs = document.querySelectorAll("input");
                let latInput = null, lngInput = null;

                allInputs.forEach(input => {
                    const wireModel = input.getAttribute("wire:model");
                    if (wireModel && wireModel.includes("latitude")) latInput = input;
                    if (wireModel && wireModel.includes("longitude")) lngInput = input;
                });

                return { latInput, lngInput, method: "wire:model search" };
            },
            // Approach 3: ID contains
            () => {
                const latInput = document.querySelector("input[id*=latitude]");
                const lngInput = document.querySelector("input[id*=longitude]");
                return { latInput, lngInput, method: "id contains" };
            },
            // Approach 4: Look for any input with latitude/longitude in various attributes
            () => {
                const allInputs = document.querySelectorAll("input");
                let latInput = null, lngInput = null;

                allInputs.forEach(input => {
                    const attrs = [input.name, input.id, input.getAttribute("wire:model"), input.getAttribute("x-model")].join(" ").toLowerCase();
                    if (attrs.includes("latitude") && !latInput) latInput = input;
                    if (attrs.includes("longitude") && !lngInput) lngInput = input;
                });

                return { latInput, lngInput, method: "attribute search" };
            }
        ];

        let latInput = null, lngInput = null, successMethod = null;

        // Try each approach until we find the inputs
        for (const approach of approaches) {
            const result = approach();
            if (result.latInput && result.lngInput) {
                latInput = result.latInput;
                lngInput = result.lngInput;
                successMethod = result.method;
                console.log("✅ Found inputs using:", successMethod);
                break;
            }
        }

        if (latInput && lngInput) {
            // Set values
            latInput.value = lat;
            lngInput.value = lng;

            // Trigger multiple events for maximum compatibility
            const events = ["input", "change", "blur", "keyup"];
            events.forEach(eventType => {
                latInput.dispatchEvent(new Event(eventType, { bubbles: true }));
                lngInput.dispatchEvent(new Event(eventType, { bubbles: true }));
            });

            // Try Alpine.js if available
            if (window.Alpine) {
                try {
                    latInput.dispatchEvent(new CustomEvent("input", { bubbles: true }));
                    lngInput.dispatchEvent(new CustomEvent("input", { bubbles: true }));
                } catch (e) {
                    console.log("Alpine events failed:", e);
                }
            }

            console.log("📍 Coordinates set successfully:", { lat, lng, method: successMethod });
            console.log("📍 Final values:", {
                latValue: latInput.value,
                lngValue: lngInput.value
            });

            // Store coordinates globally for validation
            window.currentCoordinates = {
                latitude: lat,
                longitude: lng,
                isValid: true
            };

            return true;
        } else {
            console.error("❌ Could not find latitude/longitude inputs");
            debugAbsensiLocation();
            return false;
        }
    }

    // Validate location distance against workplace
    function validateLocationDistance(lat, lng) {
        console.log("🔍 Validating location distance...");

        // Make AJAX request to validate geofencing
        fetch("/karyawan/validate-geofencing", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document.querySelector("meta[name=csrf-token]")?.getAttribute("content") || ""
            },
            body: JSON.stringify({
                latitude: lat,
                longitude: lng
            })
        })
            .then(response => response.json())
            .then(data => {
                console.log("📍 Geofencing validation result:", data);

                const demo = document.getElementById("location-demo");
                if (!demo) return;

                if (data.allowed) {
                    const sourceIcon = data.source === 'jadwal kerja hari ini' ? '📅' : '👤';
                    const sourceText = data.source === 'jadwal kerja hari ini' ? 'berdasarkan jadwal hari ini' : 'berdasarkan entitas default';

                    demo.innerHTML = "✅ Lokasi valid untuk absensi!<br>" +
                        "Latitude: " + lat + "<br>" +
                        "Longitude: " + lng + "<br>" +
                        "Jarak dari kantor: " + Math.round(data.distance || 0) + "m<br>" +
                        "<small class=\"text-green-600\">✓ Dalam radius " + (data.radius || 0) + "m yang diperbolehkan</small><br>" +
                        "<small class=\"text-gray-600\">" + sourceIcon + " " + (data.entitas_name || "Lokasi Kerja") + " (" + sourceText + ")</small>";
                    demo.className = "p-3 bg-green-50 border border-green-200 rounded-lg text-green-800 mb-3";

                    // Store validation result for form submission
                    window.lastLocationValidation = {
                        allowed: true,
                        distance: Math.round(data.distance || 0),
                        radius: data.radius || 0,
                        entitas: data.entitas_name || "Lokasi Kerja",
                        source: data.source || "unknown"
                    };
                } else {
                    const sourceIcon = data.source === 'jadwal kerja hari ini' ? '📅' : '👤';
                    const sourceText = data.source === 'jadwal kerja hari ini' ? 'berdasarkan jadwal hari ini' : 'berdasarkan entitas default';

                    demo.innerHTML = "❌ Lokasi tidak valid untuk absensi!<br>" +
                        "Latitude: " + lat + "<br>" +
                        "Longitude: " + lng + "<br>" +
                        "Jarak dari kantor: " + Math.round(data.distance || 0) + "m<br>" +
                        "<small class=\"text-red-600\">✗ Melebihi radius " + (data.radius || 0) + "m yang diperbolehkan</small><br>" +
                        "<strong>Pesan:</strong> " + (data.message || "Lokasi tidak valid") + "<br>" +
                        "<small class=\"text-gray-600\">" + sourceIcon + " " + (data.entitas_name || "Lokasi Kerja") + " (" + sourceText + ")</small>";
                    demo.className = "p-3 bg-red-50 border border-red-200 rounded-lg text-red-800 mb-3";

                    // Store validation result for form submission
                    window.lastLocationValidation = {
                        allowed: false,
                        distance: Math.round(data.distance || 0),
                        radius: data.radius || 0,
                        entitas: data.entitas_name || "Lokasi Kerja",
                        source: data.source || "unknown",
                        message: data.message || "Anda berada di luar radius yang diperbolehkan untuk melakukan absensi."
                    };
                }
            })
            .catch(error => {
                console.error("❌ Error validating geofencing:", error);
                const demo = document.getElementById("location-demo");
                if (demo) {
                    demo.innerHTML = "⚠️ Tidak dapat memvalidasi lokasi<br>" +
                        "Latitude: " + lat + "<br>" +
                        "Longitude: " + lng + "<br>" +
                        "<small>Silakan coba lagi atau hubungi administrator</small>";
                    demo.className = "p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-800 mb-3";
                }
            });
    }

    // Auto-start geolocation when page loads
    setTimeout(function () {
        console.log("🚀 Auto-starting geolocation...");
        if (window.getLocation) {
            getLocation();
        }
    }, 1000);

    // Store coordinates globally for validation
    window.currentCoordinates = {
        latitude: null,
        longitude: null,
        isValid: false
    };

    // Add simple debug function
    window.debugAbsensiLocation = function () {
        console.log("=== DEBUG: Looking for inputs ===");
        const allInputs = document.querySelectorAll("input");
        console.log("All inputs found:", allInputs.length);
        allInputs.forEach((input, index) => {
            console.log(`Input ${index}:`, {
                name: input.name,
                id: input.id,
                type: input.type,
                value: input.value,
                wireModel: input.getAttribute("wire:model"),
                placeholder: input.placeholder
            });
        });

        // Also check for specific latitude/longitude inputs
        const latByName = document.querySelector("input[name=latitude]");
        const lngByName = document.querySelector("input[name=longitude]");
        const latById = document.querySelector("input[id*=latitude]");
        const lngById = document.querySelector("input[id*=longitude]");

        console.log("Specific searches:", {
            latByName: latByName ? { name: latByName.name, value: latByName.value } : null,
            lngByName: lngByName ? { name: lngByName.name, value: lngByName.value } : null,
            latById: latById ? { id: latById.id, value: latById.value } : null,
            lngById: lngById ? { id: lngById.id, value: lngById.value } : null
        });

        console.log("=== END DEBUG ===");
    };

    // Make debug function available globally for manual testing
    window.testDebug = debugAbsensiLocation;

    // Function to validate before submission
    window.validateBeforeSubmit = function () {
        console.log("🔍 Validating before submission...");

        // Try multiple approaches to find latitude/longitude inputs
        let latInput = null, lngInput = null;

        // Approach 1: Direct name attribute
        latInput = document.querySelector("input[name='latitude']");
        lngInput = document.querySelector("input[name='longitude']");

        if (!latInput || !lngInput) {
            // Approach 2: Wire model - more specific search
            const allInputs = document.querySelectorAll("input");
            allInputs.forEach(input => {
                const wireModel = input.getAttribute("wire:model");
                const name = input.name || '';
                const id = input.id || '';

                // Check for latitude
                if ((wireModel && wireModel.includes("latitude")) ||
                    name.includes("latitude") ||
                    id.includes("latitude")) {
                    latInput = input;
                }

                // Check for longitude
                if ((wireModel && wireModel.includes("longitude")) ||
                    name.includes("longitude") ||
                    id.includes("longitude")) {
                    lngInput = input;
                }
            });
        }

        console.log("🔍 Input search result:", {
            latInput: latInput ? {
                name: latInput.name,
                id: latInput.id,
                value: latInput.value,
                wireModel: latInput.getAttribute("wire:model")
            } : "NOT FOUND",
            lngInput: lngInput ? {
                name: lngInput.name,
                id: lngInput.id,
                value: lngInput.value,
                wireModel: lngInput.getAttribute("wire:model")
            } : "NOT FOUND"
        });

        // Check coordinates - prioritize input values, fallback to global
        let hasValidCoords = false;
        let currentLat = null;
        let currentLng = null;

        // First check input values
        if (latInput?.value && lngInput?.value &&
            latInput.value !== "0" && lngInput.value !== "0" &&
            latInput.value !== "" && lngInput.value !== "") {
            hasValidCoords = true;
            currentLat = latInput.value;
            currentLng = lngInput.value;
            console.log("✅ Using coordinates from inputs:", { lat: currentLat, lng: currentLng });
        }
        // Fallback to global coordinates
        else if (window.currentCoordinates?.isValid &&
            window.currentCoordinates.latitude &&
            window.currentCoordinates.longitude) {
            hasValidCoords = true;
            currentLat = window.currentCoordinates.latitude;
            currentLng = window.currentCoordinates.longitude;
            console.log("✅ Using global coordinates:", { lat: currentLat, lng: currentLng });

            // Try to set global coordinates to inputs
            if (latInput && lngInput) {
                console.log("🔄 Setting global coordinates to inputs...");
                setCoordinates(currentLat, currentLng);
            }
        }

        // If no valid coordinates at all
        if (!hasValidCoords) {
            console.error("❌ No valid coordinates found!", {
                inputValues: {
                    latValue: latInput?.value || 'null',
                    lngValue: lngInput?.value || 'null',
                    latExists: !!latInput,
                    lngExists: !!lngInput
                },
                globalCoords: window.currentCoordinates
            });

            Swal.fire({
                icon: "warning",
                title: "Lokasi Belum Terdeteksi! 📍",
                html: `
                    <div class="text-left">
                        <p class="mb-3">Koordinat GPS Anda belum terdeteksi. Silakan:</p>
                        <ol class="list-decimal list-inside space-y-2 text-sm">
                            <li>Aktifkan GPS/Location di perangkat Anda</li>
                            <li>Izinkan akses lokasi di browser</li>
                            <li>Klik tombol "📍 Dapatkan Lokasi"</li>
                            <li>Atau gunakan "🏢 Gunakan Jakarta" untuk testing</li>
                        </ol>
                        <div class="mt-3 p-2 bg-gray-100 rounded text-xs">
                            Debug: Input(${latInput?.value || 'null'}, ${lngInput?.value || 'null'}) | Global(${window.currentCoordinates?.latitude || 'null'}, ${window.currentCoordinates?.longitude || 'null'})
                        </div>
                    </div>
                `,
                confirmButtonText: "Dapatkan Lokasi",
                confirmButtonColor: "#3B82F6",
                showCancelButton: true,
                cancelButtonText: "Batal",
                cancelButtonColor: "#6B7280"
            }).then((result) => {
                if (result.isConfirmed) {
                    getLocation();
                }
            });
            return false;
        }

        // Check location validation status (geofencing)
        const demo = document.getElementById("location-demo");
        if (demo && demo.className.includes("bg-red-50")) {
            console.error("❌ Location outside radius!");

            Swal.fire({
                icon: "error",
                title: "Absensi Ditolak! ❌",
                html: `
                    <div class="text-left">
                        <p class="mb-3"><strong>Anda berada di luar radius yang diperbolehkan untuk melakukan absensi.</strong></p>
                        <div class="bg-red-50 p-3 rounded-lg border border-red-200 mb-3">
                            <div class="text-sm text-red-700">
                                <div>📍 Jarak Anda: <strong>563m dari lokasi kerja</strong></div>
                                <div>⚠️ Maksimal radius: <strong>100m</strong></div>
                                <div>🏢 Silakan pindah lebih dekat ke lokasi kerja</div>
                            </div>
                        </div>
                        <div class="text-sm text-gray-600">
                            💡 <strong>Tips:</strong> Pastikan Anda berada dalam area kantor sebelum melakukan absensi.
                        </div>
                    </div>
                `,
                confirmButtonText: "Mengerti",
                confirmButtonColor: "#EF4444",
                showCancelButton: true,
                cancelButtonText: "Lihat Info Lokasi",
                cancelButtonColor: "#6B7280",
                allowOutsideClick: false,
                allowEscapeKey: false
            }).then((result) => {
                if (result.dismiss === Swal.DismissReason.cancel) {
                    // Scroll to workplace location info
                    const workplaceInfo = document.querySelector("[data-field-wrapper=workplace_location_info]");
                    if (workplaceInfo) {
                        workplaceInfo.scrollIntoView({ behavior: "smooth", block: "center" });
                        workplaceInfo.style.animation = "pulse 2s infinite";
                        setTimeout(() => {
                            workplaceInfo.style.animation = "";
                        }, 4000);
                    }
                }
            });
            return false;
        }

        console.log("✅ All validation passed - coordinates valid and within radius!");
        return true;
    };

    // Multiple approaches to intercept form submission
    function addFormInterceptors() {
        // Approach 1: Standard form submit event
        const forms = document.querySelectorAll("form");
        forms.forEach(form => {
            form.addEventListener("submit", function (e) {
                console.log("🚀 Form submit event detected!");
                if (!validateBeforeSubmit()) {
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    return false;
                }
            }, true); // Use capture phase
        });

        // Approach 2: Intercept submit buttons
        const submitButtons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
        submitButtons.forEach(button => {
            button.addEventListener("click", function (e) {
                console.log("🚀 Submit button clicked!");
                if (!validateBeforeSubmit()) {
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    return false;
                }
            }, true); // Use capture phase
        });

        // Approach 3: Livewire specific - intercept wire:submit
        document.addEventListener('livewire:submit', function (e) {
            console.log("🚀 Livewire submit detected!");
            if (!validateBeforeSubmit()) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                return false;
            }
        }, true);

        // Approach 4: Watch for any button with wire:click that might submit
        const wireButtons = document.querySelectorAll('[wire\\:click]');
        wireButtons.forEach(button => {
            const wireClick = button.getAttribute('wire:click');
            if (wireClick && (wireClick.includes('create') || wireClick.includes('submit') || wireClick.includes('save'))) {
                button.addEventListener("click", function (e) {
                    console.log("🚀 Wire:click submit detected!");
                    if (!validateBeforeSubmit()) {
                        e.preventDefault();
                        e.stopPropagation();
                        e.stopImmediatePropagation();
                        return false;
                    }
                }, true);
            }
        });

        console.log("📝 All form interceptors added");
    }

    // Add interceptors with multiple attempts
    setTimeout(addFormInterceptors, 1000);
    setTimeout(addFormInterceptors, 3000);
    setTimeout(addFormInterceptors, 5000);

    // Also try when DOM changes (for dynamic forms)
    const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
            if (mutation.type === 'childList') {
                setTimeout(addFormInterceptors, 500);
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Also add on Livewire updates
    document.addEventListener('livewire:navigated', addFormInterceptors);
    document.addEventListener('livewire:load', addFormInterceptors);

    // AGGRESSIVE PREVENTION: Override form submission globally (skip for karyawan)
    if (!window.skipFormBlocking) {
        const originalSubmit = HTMLFormElement.prototype.submit;
        HTMLFormElement.prototype.submit = function () {
            console.log("🚨 AGGRESSIVE: Form.submit() called!");
            if (!validateBeforeSubmit()) {
                console.log("🚨 AGGRESSIVE: Blocking form.submit()!");
                return false;
            }
            return originalSubmit.call(this);
        };
    }

    // AGGRESSIVE PREVENTION: Block any fetch/XMLHttpRequest that looks like form submission (skip for karyawan)
    if (!window.skipFormBlocking) {
        const originalFetch = window.fetch;
        window.fetch = function (url, options) {
            if (options && options.method && options.method.toUpperCase() === 'POST') {
                if (url.includes('absensi') || url.includes('create') || url.includes('livewire')) {
                    console.log("🚨 AGGRESSIVE: POST request detected!", { url, options });
                    if (!validateBeforeSubmit()) {
                        console.log("🚨 AGGRESSIVE: Blocking POST request!");
                        // Return a rejected promise to prevent the request
                        return Promise.reject(new Error('Client-side validation failed - submission blocked'));
                    }
                }
            }
            return originalFetch.apply(this, arguments);
        };
    }

    // AGGRESSIVE PREVENTION: Block XMLHttpRequest (skip for karyawan)
    if (!window.skipFormBlocking) {
        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function (method, url, ...args) {
            this._url = url;
            this._method = method;
            return originalXHROpen.apply(this, [method, url, ...args]);
        };

        const originalXHRSend = XMLHttpRequest.prototype.send;
        XMLHttpRequest.prototype.send = function (data) {
            if (this._method && this._method.toUpperCase() === 'POST') {
                if (this._url && (this._url.includes('absensi') || this._url.includes('create') || this._url.includes('livewire'))) {
                    console.log("🚨 AGGRESSIVE: XHR POST request detected!", { url: this._url, method: this._method });
                    if (!validateBeforeSubmit()) {
                        console.log("🚨 AGGRESSIVE: Blocking XHR request!");
                        // Abort the request
                        this.abort();
                        return;
                    }
                }
            }
            return originalXHRSend.apply(this, [data]);
        };
    }

    console.log("🛡️ AGGRESSIVE form submission prevention activated!");

    // Add CSS for SweetAlert buttons
    const style = document.createElement('style');
    style.textContent = `
        .swal2-popup .swal2-actions {
            margin: 20px 0 0 0 !important;
            padding: 16px !important;
            display: flex !important;
            justify-content: center !important;
            gap: 12px !important;
            flex-wrap: wrap !important;
        }

        .swal2-popup .swal2-confirm,
        .swal2-popup .swal2-cancel {
            min-height: 44px !important;
            padding: 12px 24px !important;
            font-size: 16px !important;
            font-weight: 600 !important;
            border-radius: 8px !important;
            margin: 4px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            visibility: visible !important;
            opacity: 1 !important;
            border: 2px solid transparent !important;
            cursor: pointer !important;
            text-decoration: none !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }

        .swal2-popup .swal2-confirm {
            background-color: #EF4444 !important;
            color: white !important;
            border-color: #DC2626 !important;
        }

        .swal2-popup .swal2-cancel {
            background-color: #6B7280 !important;
            color: white !important;
            border-color: #4B5563 !important;
        }

        .swal2-popup .swal2-confirm:hover {
            background-color: #DC2626 !important;
            transform: translateY(-1px) !important;
        }

        .swal2-popup .swal2-cancel:hover {
            background-color: #4B5563 !important;
            transform: translateY(-1px) !important;
        }

        .swal2-popup {
            padding: 24px !important;
            width: 90% !important;
            max-width: 600px !important;
        }
    `;
    document.head.appendChild(style);
});
