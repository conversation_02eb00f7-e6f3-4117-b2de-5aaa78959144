/**
 * Enhanced Camera Upload with Metadata for Attendance
 * Captures photo with geolocation and timestamp metadata
 */

// Prevent infinite loops and multiple loads
if (window.cameraMetadataLoaded) {
    console.log("📸 Camera metadata script already loaded, skipping...");
} else {
    window.cameraMetadataLoaded = true;

    document.addEventListener("DOMContentLoaded", function () {
        // Initialize camera interface after DOM is ready
        setTimeout(function () {
            if (isAttendancePage()) {
                initializeCameraInterface();
            }
        }, 1000);
    });
}

function isAttendancePage() {
    return window.location.href.includes('/karyawan/absensis/create') ||
        window.location.href.includes('/karyawan/attendance/create') ||
        document.getElementById('camera-interface-container') !== null;
}

function initializeCameraInterface() {
    console.log("🚀 Initializing camera interface...");

    // Check if elements exist
    const requiredElements = [
        "camera-interface-container",
        "start-camera-btn",
        "capture-photo-btn",
        "stop-camera-btn",
        "camera-container",
        "camera-video",
        "preview-container",
        "photo-preview",
        "use-photo-btn",
        "retake-photo-btn"
    ];

    let missingElements = [];
    requiredElements.forEach(id => {
        if (!document.getElementById(id)) {
            missingElements.push(id);
        }
    });

    if (missingElements.length > 0) {
        console.error("❌ Missing elements:", missingElements);
        return;
    }

    console.log("✅ All camera elements found");

    let stream = null;
    let currentLocation = null;
    let capturedImageData = null;
    let metadata = {};

    // Nonaktifkan tombol kamera saat inisialisasi
    const startButton = document.getElementById("start-camera-btn");
    if (startButton) {
        startButton.disabled = true;
        startButton.style.opacity = "0.5";
        startButton.style.background = "#6b7280";
        startButton.style.cursor = "not-allowed";
        startButton.title = "Menunggu validasi lokasi...";
    }

    // Get current location
    getCurrentLocation();

    // Start live clock
    startLiveClock();

    // Event listeners
    document.getElementById("start-camera-btn").addEventListener("click", startCamera);
    document.getElementById("capture-photo-btn").addEventListener("click", capturePhoto);
    document.getElementById("stop-camera-btn").addEventListener("click", stopCamera);
    document.getElementById("use-photo-btn").addEventListener("click", usePhoto);
    document.getElementById("retake-photo-btn").addEventListener("click", retakePhoto);

    // Monitor location validation changes
    monitorLocationValidation();

    console.log("✅ Camera interface initialized successfully");

    function isLocationValid() {
        // Cek apakah ada hasil validasi lokasi
        if (!window.lastLocationValidation) {
            console.log("❌ No location validation result found");
            return false;
        }

        // Cek apakah lokasi diperbolehkan
        if (!window.lastLocationValidation.allowed) {
            console.log("❌ Location validation failed:", window.lastLocationValidation);
            return false;
        }

        console.log("✅ Location validation passed:", window.lastLocationValidation);
        return true;
    }

    function showLocationValidationError() {
        const validation = window.lastLocationValidation;

        if (!validation) {
            alert("❌ Lokasi belum divalidasi!\n\nSilakan tunggu proses validasi lokasi selesai atau refresh halaman untuk mencoba lagi.");
            return;
        }

        const message = `❌ Lokasi Tidak Valid untuk Absensi!\n\n` +
            `📍 Jarak dari ${validation.entitas || 'kantor'}: ${validation.distance || 0}m\n` +
            `📏 Radius maksimal: ${validation.radius || 0}m\n\n` +
            `${validation.message || 'Anda berada di luar radius yang diperbolehkan.'}\n\n` +
            `Silakan mendekati lokasi kerja untuk melakukan absensi.`;

        alert(message);
    }

    function monitorLocationValidation() {
        // Cek status validasi lokasi setiap 2 detik
        setInterval(function () {
            updateCameraButtonState();
        }, 2000);

        // Cek status awal
        setTimeout(function () {
            updateCameraButtonState();
        }, 3000); // Tunggu 3 detik untuk validasi awal
    }

    function updateCameraButtonState() {
        const startButton = document.getElementById("start-camera-btn");
        if (!startButton) return;

        const isValid = isLocationValid();

        if (isValid) {
            // Lokasi valid - aktifkan tombol
            startButton.disabled = false;
            startButton.style.opacity = "1";
            startButton.style.background = "#059669";
            startButton.style.cursor = "pointer";
            startButton.title = "Klik untuk membuka kamera";
        } else {
            // Lokasi tidak valid - nonaktifkan tombol
            startButton.disabled = true;
            startButton.style.opacity = "0.5";
            startButton.style.background = "#6b7280";
            startButton.style.cursor = "not-allowed";
            startButton.title = "Lokasi tidak valid - kamera tidak dapat dibuka";
        }
    }

    function getCurrentLocation() {
        console.log("🔍 Checking for existing GPS location from geolocation script...");

        // Cek apakah sudah ada koordinat dari script geolocation
        if (window.currentCoordinates && window.currentCoordinates.isValid) {
            console.log("✅ Using existing GPS location from geolocation script:", window.currentCoordinates);

            currentLocation = {
                latitude: parseFloat(window.currentCoordinates.latitude),
                longitude: parseFloat(window.currentCoordinates.longitude),
                accuracy: window.currentCoordinates.accuracy || 50, // Default accuracy jika tidak ada
                timestamp: Date.now()
            };

            // Get distance to office if validation data is available
            const distanceText = window.lastLocationValidation && window.lastLocationValidation.distance !== undefined
                ? `Jarak kantor: ${window.lastLocationValidation.distance}m`
                : `Akurasi: ${Math.round(currentLocation.accuracy)}m`;
            updateCameraStatus(`📍 GPS ditemukan! ${distanceText}`, "success");
            updateLiveCoordinates();
            return;
        }

        console.log("🔍 No existing GPS location found, requesting new location...");

        if (!navigator.geolocation) {
            console.error("❌ Geolocation not supported by this browser");
            updateCameraStatus("GPS tidak didukung oleh browser ini", "warning");
            return;
        }

        // Show loading status
        updateCameraStatus("📍 Mencari lokasi GPS...", "info");

        navigator.geolocation.getCurrentPosition(
            function (position) {
                currentLocation = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                    accuracy: position.coords.accuracy,
                    timestamp: position.timestamp
                };
                console.log("✅ GPS Location obtained:", currentLocation);
                // Get distance to office if validation data is available
                const distanceText = window.lastLocationValidation && window.lastLocationValidation.distance !== undefined
                    ? `Jarak kantor: ${window.lastLocationValidation.distance}m`
                    : `Akurasi: ${Math.round(currentLocation.accuracy)}m`;
                updateCameraStatus(`📍 GPS ditemukan! ${distanceText}`, "success");

                // Update live coordinates immediately
                updateLiveCoordinates();

                // Sync dengan window.currentCoordinates
                window.currentCoordinates = {
                    latitude: currentLocation.latitude,
                    longitude: currentLocation.longitude,
                    accuracy: currentLocation.accuracy,
                    isValid: true
                };
            },
            function (error) {
                console.error("❌ Failed to get location:", error);
                let errorMessage = "Gagal mendapatkan lokasi GPS: ";

                switch (error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage += "Izin lokasi ditolak";
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage += "Lokasi tidak tersedia";
                        break;
                    case error.TIMEOUT:
                        errorMessage += "Timeout mencari lokasi";
                        break;
                    default:
                        errorMessage += "Error tidak diketahui";
                        break;
                }

                console.warn(errorMessage);
                updateCameraStatus(errorMessage + ". Foto tetap bisa diambil tanpa GPS.", "warning");
            },
            {
                enableHighAccuracy: true,
                timeout: 20000,
                maximumAge: 60000
            }
        );

        // Monitor perubahan dari script geolocation
        setInterval(function () {
            if (window.currentCoordinates && window.currentCoordinates.isValid) {
                const newLat = parseFloat(window.currentCoordinates.latitude);
                const newLng = parseFloat(window.currentCoordinates.longitude);
                const newAccuracy = window.currentCoordinates.accuracy || 50;

                // Update jika ada perubahan signifikan
                if (!currentLocation ||
                    Math.abs(currentLocation.latitude - newLat) > 0.0001 ||
                    Math.abs(currentLocation.longitude - newLng) > 0.0001 ||
                    Math.abs(currentLocation.accuracy - newAccuracy) > 10) {

                    currentLocation = {
                        latitude: newLat,
                        longitude: newLng,
                        accuracy: newAccuracy,
                        timestamp: Date.now()
                    };

                    console.log("🔄 GPS Location synced from geolocation script:", currentLocation);
                    updateLiveCoordinates();
                }
            }
        }, 2000);
    }

    function updateLiveCoordinates() {
        const coordsElement = document.getElementById("live-coordinates");
        if (coordsElement && currentLocation) {
            coordsElement.textContent = `📍 ${currentLocation.latitude.toFixed(6)}, ${currentLocation.longitude.toFixed(6)} (±${Math.round(currentLocation.accuracy)}m)`;
            coordsElement.style.color = currentLocation.accuracy < 50 ? '#00FF88' : '#FFAA00';
        }
    }

    function startLiveClock() {
        setInterval(function () {
            const now = new Date();
            const timeElement = document.getElementById("current-time");
            if (timeElement) {
                // Format time in Indonesia timezone
                const indonesiaTimeDisplay = now.toLocaleString("id-ID", {
                    timeZone: "Asia/Jakarta",
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                }) + ' WIB';
                timeElement.textContent = indonesiaTimeDisplay;
            }

            // Update live coordinates using the new function
            updateLiveCoordinates();
        }, 1000);
    }

    async function startCamera() {
        try {
            // Validasi lokasi sebelum membuka kamera
            if (!isLocationValid()) {
                updateCameraStatus("❌ Lokasi tidak valid! Kamera tidak dapat dibuka.", "error");
                showLocationValidationError();
                return;
            }

            updateCameraStatus("Memulai kamera...", "info");

            const constraints = {
                video: {
                    width: { ideal: 1280 },
                    height: { ideal: 720 },
                    facingMode: "user" // Front camera for selfie
                }
            };

            stream = await navigator.mediaDevices.getUserMedia(constraints);

            const video = document.getElementById("camera-video");
            video.srcObject = stream;

            // Show camera container
            document.getElementById("camera-container").style.display = "block";

            // Update button states
            document.getElementById("start-camera-btn").disabled = true;
            document.getElementById("start-camera-btn").style.opacity = "0.5";
            document.getElementById("capture-photo-btn").disabled = false;
            document.getElementById("capture-photo-btn").style.opacity = "1";
            document.getElementById("stop-camera-btn").disabled = false;
            document.getElementById("stop-camera-btn").style.opacity = "1";

            updateCameraStatus("Kamera aktif. Posisikan wajah Anda dan klik \"Ambil Foto\"", "success");

        } catch (error) {
            console.error("Error accessing camera:", error);
            updateCameraStatus("Gagal mengakses kamera. Pastikan Anda memberikan izin akses kamera.", "error");
        }
    }

    function capturePhoto() {
        // Validasi lokasi sebelum mengambil foto
        if (!isLocationValid()) {
            updateCameraStatus("❌ Lokasi tidak valid! Foto tidak dapat diambil.", "error");
            showLocationValidationError();
            return;
        }

        const video = document.getElementById("camera-video");
        if (!video || !stream) return;

        // Create canvas
        const canvas = document.getElementById("photo-canvas");
        const context = canvas.getContext("2d");

        // Set canvas size
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        // Draw video frame to canvas
        context.drawImage(video, 0, 0);

        // Capture metadata first
        captureMetadata();

        // Add metadata overlay to the photo
        addMetadataOverlay(context, canvas.width, canvas.height);

        // Get image data with overlay
        capturedImageData = canvas.toDataURL("image/jpeg", 0.8);

        // Show preview
        showPreview(capturedImageData);

        updateCameraStatus("Foto berhasil diambil dengan metadata! Periksa preview dan pilih \"Gunakan Foto\" jika sudah sesuai.", "success");
    }

    function captureMetadata() {
        const now = new Date();

        // Determine attendance status based on time
        const attendanceStatus = getAttendanceStatus(now);

        // Format datetime for Indonesia timezone
        const indonesiaTime = new Date(now.getTime() + (7 * 60 * 60 * 1000)); // UTC+7 for WIB
        const indonesiaDateTimeDisplay = indonesiaTime.toLocaleString("id-ID", {
            timeZone: "Asia/Jakarta",
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        }) + ' WIB';

        metadata = {
            timestamp: now.toISOString(),
            datetime_display: indonesiaDateTimeDisplay,
            latitude: currentLocation?.latitude || null,
            longitude: currentLocation?.longitude || null,
            coordinates_display: currentLocation ?
                `${currentLocation.latitude.toFixed(6)}°, ${currentLocation.longitude.toFixed(6)}°` :
                "Lokasi tidak tersedia",
            status_kehadiran: attendanceStatus,
            camera_info: getCameraInfo(),
            user_agent: navigator.userAgent
        };
    }

    function getAttendanceStatus(now) {
        const hour = now.getHours();
        const minute = now.getMinutes();
        const timeInMinutes = hour * 60 + minute;

        // Define work hours (can be customized based on shift)
        // This is a simplified version - in real implementation,
        // this should get shift data from the server

        // Regular shift times
        const workStartTime = 8 * 60; // 08:00
        const lateThreshold = 8 * 60 + 15; // 08:15
        const workEndTime = 17 * 60; // 17:00

        // Split shift times (example: 08:00-12:00 & 18:00-22:00)
        const splitPeriod1Start = 8 * 60; // 08:00
        const splitPeriod1End = 12 * 60; // 12:00
        const splitPeriod2Start = 18 * 60; // 18:00
        const splitPeriod2End = 22 * 60; // 22:00

        // Check if it's within split shift periods
        if ((timeInMinutes >= splitPeriod1Start - 60 && timeInMinutes <= splitPeriod1End + 60) ||
            (timeInMinutes >= splitPeriod2Start - 60 && timeInMinutes <= splitPeriod2End + 60)) {

            // Determine which period we're in
            let currentPeriodStart, currentPeriodEnd;
            if (timeInMinutes <= splitPeriod1End + 60) {
                currentPeriodStart = splitPeriod1Start;
                currentPeriodEnd = splitPeriod1End;
            } else {
                currentPeriodStart = splitPeriod2Start;
                currentPeriodEnd = splitPeriod2End;
            }

            // Check status for current period
            if (timeInMinutes <= currentPeriodStart) {
                return "✅ TEPAT WAKTU";
            } else if (timeInMinutes <= currentPeriodStart + 15) {
                return "⚠️ HAMPIR TERLAMBAT";
            } else if (timeInMinutes <= currentPeriodEnd) {
                return "❌ TERLAMBAT";
            } else {
                return "🏠 PULANG";
            }
        }

        // Regular shift logic (fallback)
        if (timeInMinutes <= workEndTime) {
            if (timeInMinutes <= workStartTime) {
                return "✅ TEPAT WAKTU";
            } else if (timeInMinutes <= lateThreshold) {
                return "⚠️ HAMPIR TERLAMBAT";
            } else {
                return "❌ TERLAMBAT";
            }
        } else {
            return "🏠 PULANG";
        }
    }

    function getCameraInfo() {
        const ua = navigator.userAgent;

        if (ua.includes("iPhone")) {
            return "APPLE IPHONE";
        } else if (ua.includes("iPad")) {
            return "APPLE IPAD";
        } else if (ua.includes("Android")) {
            const androidMatch = ua.match(/Android.*?;\s*([^)]+)/);
            if (androidMatch) {
                return androidMatch[1].toUpperCase();
            }
            return "ANDROID DEVICE";
        } else if (ua.includes("Windows")) {
            return "WINDOWS DEVICE";
        } else if (ua.includes("Mac")) {
            return "MAC DEVICE";
        }

        return "UNKNOWN DEVICE";
    }

    function addMetadataOverlay(context, width, height) {
        console.log("🎨 Adding metadata overlay to photo...");
        console.log("📊 Current metadata:", metadata);
        console.log("📍 Current location:", currentLocation);

        // Get current time in Indonesia timezone for consistency across all overlays
        const now = new Date();
        const indonesiaTimeDisplay = now.toLocaleString("id-ID", {
            timeZone: "Asia/Jakarta",
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        }) + ' WIB';

        // Calculate overlay dimensions
        const overlayHeight = Math.min(height * 0.3, 180); // Increased height for more info
        const overlayY = height - overlayHeight;

        // Create gradient background
        const gradient = context.createLinearGradient(0, overlayY, 0, height);
        gradient.addColorStop(0, 'rgba(0, 0, 0, 0.1)');
        gradient.addColorStop(0.3, 'rgba(0, 0, 0, 0.8)');
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0.95)');

        // Draw overlay background
        context.fillStyle = gradient;
        context.fillRect(0, overlayY, width, overlayHeight);

        // Set text properties
        context.fillStyle = '#FFFFFF';
        context.strokeStyle = '#000000';
        context.lineWidth = 3;
        context.textAlign = 'left';
        context.textBaseline = 'top';

        // Calculate font sizes based on image width
        const baseFontSize = Math.max(width * 0.02, 14); // Minimum 14px
        const titleFontSize = baseFontSize * 1.3;
        const textFontSize = baseFontSize;

        // Padding
        const padding = width * 0.025;
        let currentY = overlayY + padding;

        // Title
        context.font = `bold ${titleFontSize}px Arial, sans-serif`;
        context.strokeText('📸 FOTO ABSENSI', padding, currentY);
        context.fillText('📸 FOTO ABSENSI', padding, currentY);
        currentY += titleFontSize + padding * 0.5;

        // Metadata lines with better formatting
        context.font = `${textFontSize}px Arial, sans-serif`;

        // Get real-time location info
        const locationText = currentLocation ?
            `📍 ${currentLocation.latitude.toFixed(6)}, ${currentLocation.longitude.toFixed(6)}` :
            `📍 Menunggu GPS... (${metadata.coordinates_display || 'Lokasi tidak tersedia'})`;

        const metadataLines = [
            `🕐 ${indonesiaTimeDisplay}`,
            locationText,
            `📱 ${metadata.camera_info}`,
            `⏰ ${metadata.status_kehadiran}`,
            `🎯 ${(() => {
                if (window.lastLocationValidation && window.lastLocationValidation.distance !== undefined) {
                    return `Jarak kantor: ${window.lastLocationValidation.distance}m`;
                } else if (currentLocation) {
                    return `Akurasi: ${Math.round(currentLocation.accuracy)}m`;
                } else {
                    return 'Jarak: N/A';
                }
            })()}`
        ];

        metadataLines.forEach((line, index) => {
            if (currentY + textFontSize < height - padding) {
                // Add different colors for different types of info
                if (line.includes('📍')) {
                    context.fillStyle = currentLocation ? '#00FF88' : '#FF8800'; // Green if GPS, orange if not
                } else if (line.includes('⏰')) {
                    if (line.includes('✅')) context.fillStyle = '#00FF88';
                    else if (line.includes('⚠️')) context.fillStyle = '#FFAA00';
                    else if (line.includes('❌')) context.fillStyle = '#FF4444';
                    else context.fillStyle = '#FFFFFF';
                } else {
                    context.fillStyle = '#FFFFFF';
                }

                context.strokeText(line, padding, currentY);
                context.fillText(line, padding, currentY);
                currentY += textFontSize + padding * 0.4;
            }
        });

        // Add timestamp in top-right corner (Indonesia timezone)
        context.fillStyle = '#FFFFFF';
        context.textAlign = 'right';
        context.font = `${textFontSize * 0.7}px monospace`;

        // Use the same time as the overlay for consistency
        const timestampText = now.toLocaleString("id-ID", {
            timeZone: "Asia/Jakarta",
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        }).replace(/\//g, '-').replace(',', '') + ' WIB';

        context.strokeText(timestampText, width - padding, padding);
        context.fillText(timestampText, width - padding, padding);

        // Add GPS status indicator in top-left
        context.textAlign = 'left';
        context.font = `${textFontSize * 0.8}px Arial, sans-serif`;
        const gpsStatus = currentLocation ? '🟢 GPS AKTIF' : '🔴 GPS TIDAK AKTIF';
        context.fillStyle = currentLocation ? '#00FF88' : '#FF4444';
        context.strokeText(gpsStatus, padding, padding);
        context.fillText(gpsStatus, padding, padding);

        console.log("✅ Metadata overlay added successfully with location:", currentLocation);
    }

    function showPreview(dataUrl) {
        const preview = document.getElementById("photo-preview");
        preview.src = dataUrl;
        document.getElementById("preview-container").style.display = "block";
    }

    function usePhoto() {
        if (!capturedImageData) {
            updateCameraStatus("❌ Tidak ada foto untuk digunakan", "error");
            return;
        }

        updateCameraStatus("📤 Memproses foto untuk upload...", "info");

        try {
            // Convert data URL to File object
            const file = dataURLtoFile(capturedImageData, `attendance-${Date.now()}.jpg`);

            console.log("📸 Photo processed:", {
                name: file.name,
                size: file.size,
                type: file.type,
                lastModified: file.lastModified
            });

            // Store metadata for form submission
            storeMetadataForSubmission();

            // Set to file input
            setFileToInput(file);

            // Stop camera and cleanup
            stopCamera();

        } catch (error) {
            console.error("❌ Error processing photo:", error);
            updateCameraStatus("❌ Gagal memproses foto: " + error.message, "error");
        }
    }

    function storeMetadataForSubmission() {
        // Store metadata in hidden inputs or form data
        const form = document.querySelector("form");
        if (form) {
            // Create hidden input for metadata
            let metadataInput = document.getElementById("photo-metadata");
            if (!metadataInput) {
                metadataInput = document.createElement("input");
                metadataInput.type = "hidden";
                metadataInput.id = "photo-metadata";
                metadataInput.name = "photo_metadata";
                form.appendChild(metadataInput);
            }
            metadataInput.value = JSON.stringify(metadata);
        }
    }

    function retakePhoto() {
        document.getElementById("preview-container").style.display = "none";
        capturedImageData = null;
        updateCameraStatus("Siap mengambil foto ulang", "info");
    }

    function stopCamera() {
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
            stream = null;
        }

        document.getElementById("camera-container").style.display = "none";
        document.getElementById("preview-container").style.display = "none";

        // Reset button states
        document.getElementById("start-camera-btn").disabled = false;
        document.getElementById("start-camera-btn").style.opacity = "1";
        document.getElementById("capture-photo-btn").disabled = true;
        document.getElementById("capture-photo-btn").style.opacity = "0.5";
        document.getElementById("stop-camera-btn").disabled = true;
        document.getElementById("stop-camera-btn").style.opacity = "0.5";

        updateCameraStatus("Kamera ditutup", "info");
    }

    function dataURLtoFile(dataurl, filename) {
        try {
            console.log("🔄 Converting data URL to file...");

            if (!dataurl || !dataurl.includes(',')) {
                throw new Error("Invalid data URL format");
            }

            const arr = dataurl.split(",");
            if (arr.length !== 2) {
                throw new Error("Invalid data URL structure");
            }

            const mimeMatch = arr[0].match(/:(.*?);/);
            if (!mimeMatch) {
                throw new Error("Could not extract MIME type");
            }

            const mime = mimeMatch[1];
            const bstr = atob(arr[1]);
            let n = bstr.length;
            const u8arr = new Uint8Array(n);

            while (n--) {
                u8arr[n] = bstr.charCodeAt(n);
            }

            const file = new File([u8arr], filename, {
                type: mime,
                lastModified: Date.now()
            });

            console.log("✅ File conversion successful:", {
                name: file.name,
                size: file.size,
                type: file.type,
                mime: mime
            });

            return file;

        } catch (error) {
            console.error("❌ Error converting data URL to file:", error);
            throw error;
        }
    }

    function simpleUpload(file) {
        console.log("📤 Using simple base64 upload...");
        updateCameraStatus("📤 Menyimpan foto...", "info");

        // Convert file to base64
        const reader = new FileReader();
        reader.onload = function (e) {
            const base64Data = e.target.result;

            // Set to hidden field
            const hiddenField = document.querySelector('input[name="foto_absensi_base64"]');
            if (hiddenField) {
                hiddenField.value = base64Data;
                console.log("✅ Base64 data set to hidden field");

                // Show preview
                const previewContainer = document.getElementById('simple-photo-preview');
                const previewImg = document.getElementById('simple-preview-img');
                if (previewContainer && previewImg) {
                    previewImg.src = base64Data;
                    previewContainer.style.display = 'block';
                    console.log("✅ Preview shown");
                }

                updateCameraStatus("✅ Foto siap! Klik tombol Buat untuk menyimpan.", "success");
            } else {
                console.error("❌ Hidden field not found");
                updateCameraStatus("❌ Error: Hidden field tidak ditemukan", "error");
            }
        };

        reader.onerror = function () {
            console.error("❌ Error reading file");
            updateCameraStatus("❌ Error membaca file", "error");
        };

        reader.readAsDataURL(file);
    }

    function setFileToInput(file) {
        console.log("🔍 Searching for file input...");

        // Try multiple selectors for Filament file input
        const selectors = [
            'input[type="file"][wire\\:model="data.foto_absensi"]',
            'input[type="file"][name="foto_absensi"]',
            'input[type="file"][data-field-wrapper-id="foto_absensi"]',
            'input[type="file"][wire\\:model="data.foto_masuk"]',
            'input[type="file"][wire\\:model="data.foto_keluar"]',
            'input[type="file"][name="foto_masuk"]',
            'input[type="file"][name="foto_keluar"]',
            'input[type="file"][data-field-wrapper-id="foto_masuk"]',
            'input[type="file"][data-field-wrapper-id="foto_keluar"]',
            'input[type="file"]',
            '[data-field-wrapper="foto_absensi"] input[type="file"]',
            '[data-field-wrapper="foto_masuk"] input[type="file"]',
            '[data-field-wrapper="foto_keluar"] input[type="file"]',
            '.fi-fo-file-upload input[type="file"]'
        ];

        let fileInput = null;
        for (let selector of selectors) {
            fileInput = document.querySelector(selector);
            if (fileInput) {
                console.log(`📁 Found file input with selector: ${selector}`, fileInput);
                break;
            }
        }

        if (!fileInput) {
            console.error("❌ File input not found with any selector");
            console.log("Available file inputs:", document.querySelectorAll('input[type="file"]'));
            updateCameraStatus("Error: File input tidak ditemukan. Coba refresh halaman.", "error");
            return;
        }

        try {
            console.log("📁 File to upload:", file);
            console.log("📁 File size:", file.size, "bytes");
            console.log("📁 File type:", file.type);

            // Create a new FileList
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);

            // Set the files
            fileInput.files = dataTransfer.files;

            console.log("📁 Files set:", fileInput.files);

            // Trigger multiple events for better compatibility
            const events = ['change', 'input', 'blur'];
            events.forEach(eventType => {
                const event = new Event(eventType, {
                    bubbles: true,
                    cancelable: true
                });
                fileInput.dispatchEvent(event);
                console.log(`📁 Triggered ${eventType} event`);
            });

            // Special handling for Livewire/Alpine.js
            if (fileInput.hasAttribute('wire:model')) {
                console.log("📁 Detected Livewire input, triggering Livewire events");

                const wireModel = fileInput.getAttribute('wire:model');
                console.log("📁 Wire model:", wireModel);

                // Trigger Livewire change
                if (window.Livewire) {
                    const component = window.Livewire.find(fileInput.closest('[wire\\:id]')?.getAttribute('wire:id'));
                    if (component) {
                        // Set the correct field based on wire:model
                        if (wireModel.includes('foto_absensi')) {
                            component.set('data.foto_absensi', file);
                            console.log("📁 Set Livewire foto_absensi data");
                        } else if (wireModel.includes('foto_masuk')) {
                            component.set('data.foto_masuk', file);
                            console.log("📁 Set Livewire foto_masuk data");
                        } else if (wireModel.includes('foto_keluar')) {
                            component.set('data.foto_keluar', file);
                            console.log("📁 Set Livewire foto_keluar data");
                        } else {
                            component.set(wireModel, file);
                            console.log("📁 Set Livewire component data:", wireModel);
                        }
                    }
                }
            }

            // Try FilePond API for Filament FileUpload
            const fileInputContainer = fileInput.closest('.filepond--root');
            if (fileInputContainer && window.FilePond) {
                console.log("📁 Found FilePond container, using FilePond API");

                // Get FilePond instance
                const pond = window.FilePond.find(fileInputContainer);
                if (pond) {
                    console.log("📁 Found FilePond instance, adding file");

                    // Add file to FilePond
                    pond.addFile(file).then((fileItem) => {
                        console.log("✅ FilePond file added successfully:", fileItem);

                        // Force Alpine.js to reset upload state
                        setTimeout(() => {
                            const submitButton = document.querySelector('button[type="submit"]');
                            if (submitButton) {
                                console.log("🔍 Found submit button:", submitButton);

                                // Reset Alpine.js data directly
                                if (submitButton._x_dataStack) {
                                    submitButton._x_dataStack.forEach(data => {
                                        if (data.isUploadingFile !== undefined) {
                                            data.isUploadingFile = false;
                                            console.log("🔄 Reset Alpine isUploadingFile to false");
                                        }
                                    });
                                }

                                // Trigger file-upload-finished event to reset state
                                const form = submitButton.closest('form');
                                if (form) {
                                    const finishedEvent = new CustomEvent('file-upload-finished');
                                    form.dispatchEvent(finishedEvent);
                                    console.log("🔄 Triggered file-upload-finished event");
                                }

                                // Force remove disabled attribute
                                submitButton.removeAttribute('disabled');
                                submitButton.disabled = false;
                                console.log("🔄 Enabled submit button");
                            }
                        }, 1500);

                        updateCameraStatus("✅ Foto berhasil diupload! Silakan submit form.", "success");
                    }).catch((error) => {
                        console.error("❌ FilePond add file failed:", error);
                        updateCameraStatus("❌ Gagal mengupload ke FilePond: " + error.message, "error");
                    });

                    return;
                }
            }

            // Check if file was actually set (fallback)
            setTimeout(() => {
                if (fileInput.files.length > 0) {
                    console.log("✅ File set successfully:", fileInput.files[0]);
                    updateCameraStatus("✅ Foto berhasil diupload! Silakan submit form.", "success");
                } else {
                    console.error("❌ File was not set properly");
                    updateCameraStatus("❌ Gagal mengupload foto. Coba lagi atau upload manual.", "error");
                }
            }, 500);

        } catch (error) {
            console.error("❌ Error setting file:", error);
            updateCameraStatus("❌ Error mengupload foto: " + error.message, "error");
        }
    }

    function updateCameraStatus(message, type = "info") {
        const statusElement = document.getElementById("camera-status");
        if (!statusElement) return;

        const colors = {
            info: { bg: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", text: "white" },
            success: { bg: "linear-gradient(135deg, #10b981 0%, #059669 100%)", text: "white" },
            error: { bg: "linear-gradient(135deg, #ef4444 0%, #dc2626 100%)", text: "white" },
            warning: { bg: "linear-gradient(135deg, #f59e0b 0%, #d97706 100%)", text: "white" }
        };

        const color = colors[type] || colors.info;

        statusElement.style.background = color.bg;
        statusElement.style.color = color.text;
        statusElement.innerHTML = `
            <div style="font-size: 20px; margin-bottom: 8px;">📸</div>
            <div style="font-weight: 600; font-size: 14px;">${message}</div>
        `;
    }
}
