<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Leaflet Maps</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 20px;
            background: #f8fafc;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .map-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .map {
            height: 400px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        .info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 16px;
        }
        .success {
            background: #f0fdf4;
            border-color: #bbf7d0;
            color: #166534;
        }
        .error {
            background: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ Test Leaflet Maps</h1>
        
        <div id="status" class="info">
            ⏳ Loading Leaflet.js...
        </div>
        
        <div class="map-container">
            <h3>📍 Jakarta Pusat</h3>
            <div id="map1" class="map"></div>
        </div>
        
        <div class="map-container">
            <h3>📍 Jakarta Timur</h3>
            <div id="map2" class="map"></div>
        </div>
        
        <div class="map-container">
            <h3>📍 Jakarta Selatan</h3>
            <div id="map3" class="map"></div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
    
    <script>
        const statusDiv = document.getElementById('status');
        
        function updateStatus(message, type = 'info') {
            statusDiv.textContent = message;
            statusDiv.className = `info ${type}`;
        }
        
        function initMaps() {
            if (typeof L === 'undefined') {
                updateStatus('❌ Leaflet.js gagal dimuat', 'error');
                return;
            }
            
            updateStatus('✅ Leaflet.js berhasil dimuat, membuat peta...', 'success');
            
            try {
                // Map 1: Jakarta Pusat
                const map1 = L.map('map1').setView([-6.200000, 106.816666], 15);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(map1);
                L.marker([-6.200000, 106.816666]).addTo(map1)
                    .bindPopup('🟢 Jakarta Pusat<br>Lat: -6.200000<br>Lng: 106.816666')
                    .openPopup();
                L.circle([-6.200000, 106.816666], {
                    color: 'green',
                    fillColor: '#059669',
                    fillOpacity: 0.1,
                    radius: 50
                }).addTo(map1);
                
                // Map 2: Jakarta Timur
                const map2 = L.map('map2').setView([-6.175110, 106.865036], 15);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(map2);
                L.marker([-6.175110, 106.865036]).addTo(map2)
                    .bindPopup('🔵 Jakarta Timur<br>Lat: -6.175110<br>Lng: 106.865036')
                    .openPopup();
                L.circle([-6.175110, 106.865036], {
                    color: 'blue',
                    fillColor: '#3b82f6',
                    fillOpacity: 0.1,
                    radius: 50
                }).addTo(map2);
                
                // Map 3: Jakarta Selatan
                const map3 = L.map('map3').setView([-6.208763, 106.845599], 15);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(map3);
                L.marker([-6.208763, 106.845599]).addTo(map3)
                    .bindPopup('🔴 Jakarta Selatan<br>Lat: -6.208763<br>Lng: 106.845599')
                    .openPopup();
                L.circle([-6.208763, 106.845599], {
                    color: 'red',
                    fillColor: '#dc2626',
                    fillOpacity: 0.1,
                    radius: 50
                }).addTo(map3);
                
                updateStatus('🎉 Semua peta berhasil dibuat!', 'success');
                
            } catch (error) {
                updateStatus('❌ Error membuat peta: ' + error.message, 'error');
                console.error('Map creation error:', error);
            }
        }
        
        // Wait for DOM and Leaflet to load
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(initMaps, 100);
            });
        } else {
            setTimeout(initMaps, 100);
        }
    </script>
</body>
</html>
