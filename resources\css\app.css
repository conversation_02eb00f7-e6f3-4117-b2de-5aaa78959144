@tailwind base;
@tailwind components;
@tailwind utilities;
@tailwind variants;

@import "custom-tabs.css";
@import "kanban.css";

/* Custom styling for Filament tabs to prevent overflow and improve readability */

/* Tab container styling */
.fi-tabs {
    overflow-x: auto !important;
    scrollbar-width: thin !important;
    -webkit-overflow-scrolling: touch !important;
    padding-bottom: 4px !important;
}

/* Webkit scrollbar styling */
.fi-tabs::-webkit-scrollbar {
    height: 4px !important;
}

.fi-tabs::-webkit-scrollbar-track {
    background: #f1f5f9 !important;
    border-radius: 2px !important;
}

.fi-tabs::-webkit-scrollbar-thumb {
    background: #cbd5e1 !important;
    border-radius: 2px !important;
}

.fi-tabs::-webkit-scrollbar-thumb:hover {
    background: #94a3b8 !important;
}

/* Individual tab styling */
.fi-tabs-tab {
    min-width: auto !important;
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
    white-space: nowrap !important;
    flex-shrink: 0 !important;
}

/* Tab label styling */
.fi-tabs-tab-label {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 120px !important;
    display: inline-block !important;
}

/* Badge styling */
.fi-badge {
    font-size: 0.75rem !important;
    padding: 0.125rem 0.375rem !important;
    margin-left: 0.25rem !important;
    border-radius: 0.375rem !important;
    font-weight: 600 !important;
}

/* Mobile responsive styling */
@media (max-width: 768px) {
    .fi-tabs-tab {
        padding: 0.375rem 0.5rem !important;
        font-size: 0.8rem !important;
    }

    .fi-tabs-tab-label {
        max-width: 80px !important;
    }

    .fi-badge {
        font-size: 0.7rem !important;
        padding: 0.1rem 0.3rem !important;
    }
}

/* Active tab styling enhancement */
.fi-tabs-tab[aria-selected="true"] {
    font-weight: 600 !important;
}

.fi-tabs-tab[aria-selected="true"] .fi-badge {
    font-weight: 700 !important;
}
