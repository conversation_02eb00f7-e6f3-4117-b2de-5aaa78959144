@import "/vendor/filament/filament/resources/css/theme.css";

@config 'tailwind.config.js';

@layer components {
    .fi-sidebar-nav-groups {
        @apply !gap-y-0;
    }


    /* Modern Date Range Picker Styles */
    .modern-date-range-picker {
        z-index: 10;
        position: relative;
    }

    .modern-date-range-picker .dropdown-panel {
        z-index: 10 !important;
        position: absolute !important;
        backdrop-filter: blur(10px);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        top: 100% !important;
        left: 0 !important;
        margin-top: 8px !important;
        min-width: 700px !important;
    }

    /* Dark mode support */
    .dark .modern-date-range-picker .dropdown-panel {
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    }

    /* Smooth animations for calendar dates */
    .modern-date-range-picker button {
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Preset button hover effects */
    .modern-date-range-picker .group:hover .opacity-0 {
        opacity: 1;
    }



    /* Ensure dropdown is not clipped by parent containers */
    .modern-date-range-picker * {
        overflow: visible !important;
    }



    .modern-date-range-picker select {
        scrollbar-width: thin;
        scrollbar-color: #10b981 #f3f4f6;
    }

    .dark .modern-date-range-picker select {
        scrollbar-color: #10b981 #374151;
    }

    .modern-date-range-picker select::-webkit-scrollbar {
        width: 6px;
    }

    .modern-date-range-picker select::-webkit-scrollbar-track {
        background: #f3f4f6;
        border-radius: 3px;
    }

    .dark .modern-date-range-picker select::-webkit-scrollbar-track {
        background: #374151;
    }

    .modern-date-range-picker select::-webkit-scrollbar-thumb {
        background: #10b981;
        border-radius: 3px;
    }

    .modern-date-range-picker select::-webkit-scrollbar-thumb:hover {
        background: #059669;
    }
}
