/* Kanban Board Styles */
.kanban-board {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
}

.kanban-column {
    background: #f8fafc;
    border-radius: 0.5rem;
    padding: 1rem;
    min-height: 400px;
    transition: all 0.2s ease;
}

.kanban-column.drag-over {
    background: #e0f2fe;
    border: 2px dashed #0284c7;
    transform: scale(1.02);
}

.kanban-task {
    background: white;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    cursor: grab;
    transition: all 0.2s ease;
    position: relative;
}

.kanban-task:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.kanban-task:active {
    cursor: grabbing;
}

.kanban-task.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

/* SortableJS Styles */
.sortable-ghost {
    opacity: 0.4;
    background: #f3f4f6;
    border: 2px dashed #9ca3af;
}

.sortable-chosen {
    transform: rotate(2deg);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 1000;
}

.sortable-drag {
    transform: rotate(5deg);
    opacity: 0.8;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

/* Global dragging state */
body.dragging {
    cursor: grabbing !important;
    user-select: none;
}

body.dragging * {
    cursor: grabbing !important;
}

/* Task content styles */
.kanban-task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.kanban-task-title {
    font-weight: 500;
    color: #1f2937;
    font-size: 0.875rem;
    line-height: 1.25rem;
    flex: 1;
}

.kanban-task-actions {
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.kanban-task:hover .kanban-task-actions {
    opacity: 1;
}

.kanban-task-description {
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.kanban-task-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: #6b7280;
}

.kanban-task-tags {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.kanban-task-tag {
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.625rem;
    font-weight: 500;
}

.kanban-task-due-date {
    font-size: 0.75rem;
}

.kanban-task-due-date.overdue {
    color: #dc2626;
    font-weight: 600;
}

/* Column header styles */
.kanban-column-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.kanban-column-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #1f2937;
}

.kanban-column-indicator {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.kanban-column-count {
    background: #e5e7eb;
    color: #374151;
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    margin-left: 0.5rem;
}

/* Empty state */
.kanban-empty {
    text-align: center;
    color: #9ca3af;
    font-size: 0.875rem;
    padding: 2rem 1rem;
    border: 2px dashed #d1d5db;
    border-radius: 0.5rem;
    margin-top: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .kanban-board {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .kanban-column {
        min-height: 300px;
    }
    
    .kanban-task {
        padding: 0.75rem;
    }
}

/* Animation for task movement */
@keyframes taskMove {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.kanban-task.moving {
    animation: taskMove 0.3s ease;
}

/* Success notification styles */
.kanban-notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    background: #10b981;
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.kanban-notification.show {
    transform: translateX(0);
}

.kanban-notification .icon {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
}

/* Loading state */
.kanban-loading {
    opacity: 0.6;
    pointer-events: none;
}

.kanban-loading .kanban-task {
    cursor: not-allowed;
}

/* Focus styles for accessibility */
.kanban-task:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .kanban-task-actions {
        display: none;
    }
    
    .kanban-task {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
}
