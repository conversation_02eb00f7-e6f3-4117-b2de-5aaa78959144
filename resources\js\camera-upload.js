/**
 * Enhanced Camera Upload Component for Attendance System
 * Provides camera access, image preview, compression, and validation
 */

class AttendanceCameraUpload {
    constructor() {
        this.stream = null;
        this.video = null;
        this.canvas = null;
        this.isInitialized = false;
        this.compressionQuality = 0.8;
        this.maxWidth = 1024;
        this.maxHeight = 768;
        
        this.init();
    }

    init() {
        // Check if we're on the attendance create page
        if (this.isAttendancePage()) {
            this.setupCameraInterface();
        }
    }

    isAttendancePage() {
        return window.location.href.includes('/karyawan/absensis/create') ||
               window.location.href.includes('/karyawan/attendance/create');
    }

    setupCameraInterface() {
        // Find the file upload component
        const fileUploadContainer = document.querySelector('[data-field-wrapper="foto"]');
        if (!fileUploadContainer) return;

        // Create camera interface
        this.createCameraInterface(fileUploadContainer);
    }

    createCameraInterface(container) {
        // Create camera section
        const cameraSection = document.createElement('div');
        cameraSection.id = 'camera-section';
        cameraSection.className = 'mb-4 p-4 border rounded-lg bg-gray-50';
        cameraSection.innerHTML = `
            <div class="mb-3">
                <h4 class="text-sm font-medium text-gray-700 mb-2">📷 Ambil Foto dengan Kamera</h4>
                <div class="flex gap-2 mb-3">
                    <button type="button" id="start-camera" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">
                        📹 Buka Kamera
                    </button>
                    <button type="button" id="capture-photo" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm" disabled>
                        📸 Ambil Foto
                    </button>
                    <button type="button" id="stop-camera" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 text-sm" disabled>
                        ⏹️ Tutup Kamera
                    </button>
                </div>
            </div>
            
            <div id="camera-container" class="hidden">
                <video id="camera-video" autoplay playsinline class="w-full max-w-md mx-auto rounded border"></video>
            </div>
            
            <div id="preview-container" class="hidden mt-3">
                <h5 class="text-sm font-medium text-gray-700 mb-2">Preview Foto:</h5>
                <div class="flex items-start gap-3">
                    <img id="photo-preview" class="w-32 h-32 object-cover rounded border">
                    <div class="flex-1">
                        <p class="text-sm text-gray-600 mb-2">Foto berhasil diambil!</p>
                        <div class="flex gap-2">
                            <button type="button" id="use-photo" class="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-sm">
                                ✅ Gunakan Foto
                            </button>
                            <button type="button" id="retake-photo" class="px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600 text-sm">
                                🔄 Ambil Ulang
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <canvas id="photo-canvas" class="hidden"></canvas>
        `;

        // Insert camera section before the file upload
        container.insertBefore(cameraSection, container.firstChild);

        // Setup event listeners
        this.setupEventListeners();
    }

    setupEventListeners() {
        document.getElementById('start-camera').onclick = () => this.startCamera();
        document.getElementById('capture-photo').onclick = () => this.capturePhoto();
        document.getElementById('stop-camera').onclick = () => this.stopCamera();
        document.getElementById('use-photo').onclick = () => this.usePhoto();
        document.getElementById('retake-photo').onclick = () => this.retakePhoto();
    }

    async startCamera() {
        try {
            this.updateCameraStatus('Memulai kamera...', 'info');
            
            const constraints = {
                video: {
                    width: { ideal: 1280 },
                    height: { ideal: 720 },
                    facingMode: 'user' // Front camera for selfie
                }
            };

            this.stream = await navigator.mediaDevices.getUserMedia(constraints);
            
            this.video = document.getElementById('camera-video');
            this.video.srcObject = this.stream;
            
            // Show camera container
            document.getElementById('camera-container').classList.remove('hidden');
            
            // Update button states
            document.getElementById('start-camera').disabled = true;
            document.getElementById('capture-photo').disabled = false;
            document.getElementById('stop-camera').disabled = false;
            
            this.updateCameraStatus('Kamera aktif. Posisikan wajah Anda dan klik "Ambil Foto"', 'success');
            
        } catch (error) {
            console.error('Error accessing camera:', error);
            this.updateCameraStatus('Gagal mengakses kamera. Pastikan Anda memberikan izin akses kamera.', 'error');
        }
    }

    capturePhoto() {
        if (!this.video || !this.stream) return;

        // Create canvas if not exists
        this.canvas = document.getElementById('photo-canvas');
        const context = this.canvas.getContext('2d');
        
        // Set canvas size
        this.canvas.width = this.video.videoWidth;
        this.canvas.height = this.video.videoHeight;
        
        // Draw video frame to canvas
        context.drawImage(this.video, 0, 0);
        
        // Get image data and compress
        const compressedDataUrl = this.compressImage(this.canvas);
        
        // Show preview
        this.showPreview(compressedDataUrl);
        
        this.updateCameraStatus('Foto berhasil diambil! Periksa preview dan pilih "Gunakan Foto" jika sudah sesuai.', 'success');
    }

    compressImage(canvas) {
        // Calculate new dimensions while maintaining aspect ratio
        let { width, height } = canvas;
        
        if (width > this.maxWidth || height > this.maxHeight) {
            const ratio = Math.min(this.maxWidth / width, this.maxHeight / height);
            width *= ratio;
            height *= ratio;
        }
        
        // Create new canvas for compression
        const compressCanvas = document.createElement('canvas');
        const compressContext = compressCanvas.getContext('2d');
        
        compressCanvas.width = width;
        compressCanvas.height = height;
        
        // Draw compressed image
        compressContext.drawImage(canvas, 0, 0, width, height);
        
        // Return compressed data URL
        return compressCanvas.toDataURL('image/jpeg', this.compressionQuality);
    }

    showPreview(dataUrl) {
        const preview = document.getElementById('photo-preview');
        preview.src = dataUrl;
        
        document.getElementById('preview-container').classList.remove('hidden');
        
        // Store the data URL for later use
        this.capturedImageData = dataUrl;
    }

    usePhoto() {
        if (!this.capturedImageData) return;

        // Convert data URL to File object
        const file = this.dataURLtoFile(this.capturedImageData, 'selfie-' + Date.now() + '.jpg');
        
        // Find the file input and set the file
        this.setFileToInput(file);
        
        // Stop camera and cleanup
        this.stopCamera();
        
        this.updateCameraStatus('Foto berhasil digunakan untuk absensi!', 'success');
    }

    retakePhoto() {
        document.getElementById('preview-container').classList.add('hidden');
        this.capturedImageData = null;
        this.updateCameraStatus('Silakan ambil foto ulang.', 'info');
    }

    stopCamera() {
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }
        
        // Hide camera container
        document.getElementById('camera-container').classList.add('hidden');
        document.getElementById('preview-container').classList.add('hidden');
        
        // Reset button states
        document.getElementById('start-camera').disabled = false;
        document.getElementById('capture-photo').disabled = true;
        document.getElementById('stop-camera').disabled = true;
        
        this.updateCameraStatus('Kamera ditutup.', 'info');
    }

    dataURLtoFile(dataurl, filename) {
        const arr = dataurl.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        
        return new File([u8arr], filename, { type: mime });
    }

    setFileToInput(file) {
        // Find the file input
        const fileInput = document.querySelector('input[type="file"][name="foto"]');
        if (!fileInput) return;

        // Create a new FileList containing our file
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        fileInput.files = dataTransfer.files;
        
        // Trigger change event
        fileInput.dispatchEvent(new Event('change', { bubbles: true }));
    }

    updateCameraStatus(message, type) {
        let statusElement = document.getElementById('camera-status');
        
        if (!statusElement) {
            statusElement = document.createElement('div');
            statusElement.id = 'camera-status';
            statusElement.className = 'mt-2 p-2 rounded text-sm';
            document.getElementById('camera-section').appendChild(statusElement);
        }
        
        // Update styling based on type
        statusElement.className = 'mt-2 p-2 rounded text-sm ';
        switch (type) {
            case 'success':
                statusElement.className += 'bg-green-100 text-green-800 border border-green-200';
                break;
            case 'error':
                statusElement.className += 'bg-red-100 text-red-800 border border-red-200';
                break;
            case 'info':
                statusElement.className += 'bg-blue-100 text-blue-800 border border-blue-200';
                break;
            default:
                statusElement.className += 'bg-gray-100 text-gray-800 border border-gray-200';
        }
        
        statusElement.innerHTML = message;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new AttendanceCameraUpload();
});

// Also initialize if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        new AttendanceCameraUpload();
    });
} else {
    new AttendanceCameraUpload();
}
