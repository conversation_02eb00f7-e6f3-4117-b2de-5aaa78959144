// Simple Geolocation for Karyawan Panel
window.geolocationSystem = null;

// Global functions for button clicks
window.refreshLocation = function() {
    if (window.geolocationSystem) {
        window.geolocationSystem.getCurrentLocation();
    }
};

window.showManualLocation = function() {
    if (window.geolocationSystem) {
        window.geolocationSystem.showManualInput();
    }
};

// Initialize geolocation when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait for elements to be available
    setTimeout(initializeGeolocation, 1000);
});

// Also try on Livewire events
document.addEventListener('livewire:navigated', function() {
    setTimeout(initializeGeolocation, 500);
});

function initializeGeolocation() {
    console.log('🌍 Checking for geolocation elements...');
    
    const latInput = document.querySelector('input[name="latitude"]');
    const lngInput = document.querySelector('input[name="longitude"]');
    const statusElement = document.getElementById('geolocation-status');
    
    if (latInput && lngInput && statusElement && !window.geolocationSystem) {
        console.log('✅ Initializing geolocation system');
        window.geolocationSystem = new SimpleGeolocation(latInput, lngInput, statusElement);
        window.geolocationSystem.getCurrentLocation();
    } else {
        console.log('⚠️ Geolocation elements not found or already initialized');
    }
}

class SimpleGeolocation {
    constructor(latInput, lngInput, statusElement) {
        this.latInput = latInput;
        this.lngInput = lngInput;
        this.statusElement = statusElement;
        console.log('✅ SimpleGeolocation initialized');
    }
    
    getCurrentLocation() {
        console.log('📍 Getting current location...');
        this.updateStatus('loading', 'Mengambil lokasi Anda...');
        
        if (!navigator.geolocation) {
            console.error('❌ Geolocation not supported');
            this.updateStatus('error', 'Browser Anda tidak mendukung geolocation');
            this.showManualInput();
            return;
        }
        
        const options = {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 300000 // 5 minutes
        };
        
        navigator.geolocation.getCurrentPosition(
            (position) => this.onSuccess(position),
            (error) => this.onError(error),
            options
        );
    }
    
    onSuccess(position) {
        const lat = position.coords.latitude.toFixed(6);
        const lng = position.coords.longitude.toFixed(6);
        const accuracy = Math.round(position.coords.accuracy);
        
        console.log('🎉 Location success:', { lat, lng, accuracy });
        
        this.setCoordinates(lat, lng);
        this.updateStatus('success', 'Lokasi berhasil dideteksi: ' + lat + ', ' + lng + ' (±' + accuracy + 'm)');
    }
    
    onError(error) {
        console.error('❌ Location error:', error);
        
        let message = '';
        switch(error.code) {
            case error.PERMISSION_DENIED:
                message = 'Izin akses lokasi ditolak. Silakan aktifkan izin lokasi atau gunakan input manual.';
                break;
            case error.POSITION_UNAVAILABLE:
                message = 'Lokasi tidak tersedia. Pastikan GPS aktif atau gunakan input manual.';
                break;
            case error.TIMEOUT:
                message = 'Waktu permintaan lokasi habis. Silakan coba lagi atau gunakan input manual.';
                break;
            default:
                message = 'Terjadi kesalahan saat mengambil lokasi. Silakan gunakan input manual.';
                break;
        }
        
        this.updateStatus('error', message);
        this.showManualInput();
    }
    
    setCoordinates(lat, lng) {
        this.latInput.value = lat;
        this.lngInput.value = lng;
        
        // Trigger Livewire update
        this.latInput.dispatchEvent(new Event('input', { bubbles: true }));
        this.lngInput.dispatchEvent(new Event('input', { bubbles: true }));
        
        console.log('📍 Coordinates set:', { lat, lng });
    }
    
    updateStatus(type, message) {
        const colors = {
            loading: 'bg-blue-50 border-blue-200 text-blue-800',
            success: 'bg-green-50 border-green-200 text-green-800',
            error: 'bg-red-50 border-red-200 text-red-800'
        };
        
        const icons = {
            loading: '⏳',
            success: '✅',
            error: '❌'
        };
        
        this.statusElement.className = 'p-3 border rounded-lg ' + colors[type];
        this.statusElement.innerHTML = 
            '<div class="flex items-center">' +
                '<div class="mr-2">' + icons[type] + '</div>' +
                '<span>' + message + '</span>' +
            '</div>';
    }
    
    showManualInput() {
        console.log('📝 Showing manual input');
        
        const container = document.getElementById('geolocation-container');
        if (!container) return;
        
        // Remove existing manual input
        const existing = container.querySelector('.manual-input');
        if (existing) existing.remove();
        
        const manualDiv = document.createElement('div');
        manualDiv.className = 'manual-input p-3 bg-gray-50 border border-gray-200 rounded-lg mt-3';
        manualDiv.innerHTML = 
            '<div class="mb-2 text-sm font-medium text-gray-700">Input Koordinat Manual</div>' +
            '<div class="grid grid-cols-2 gap-2 mb-2">' +
                '<input type="number" id="manual-lat" placeholder="Latitude" value="-6.200000" step="any" class="px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500">' +
                '<input type="number" id="manual-lng" placeholder="Longitude" value="106.816666" step="any" class="px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500">' +
            '</div>' +
            '<div class="flex gap-2">' +
                '<button type="button" id="use-manual-coords" class="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-sm">✅ Gunakan Koordinat</button>' +
                '<button type="button" id="use-jakarta-coords" class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">🏢 Jakarta</button>' +
            '</div>' +
            '<div class="text-xs text-gray-500 mt-1">Default: Jakarta Pusat (-6.200000, 106.816666)</div>';
        
        container.appendChild(manualDiv);
        
        // Set up event listeners
        document.getElementById('use-manual-coords').onclick = () => {
            const lat = document.getElementById('manual-lat').value;
            const lng = document.getElementById('manual-lng').value;
            
            if (lat && lng) {
                this.setCoordinates(lat, lng);
                this.updateStatus('success', 'Koordinat manual berhasil digunakan: ' + lat + ', ' + lng);
                manualDiv.remove();
            } else {
                alert('Silakan masukkan koordinat yang valid');
            }
        };
        
        document.getElementById('use-jakarta-coords').onclick = () => {
            this.setCoordinates('-6.200000', '106.816666');
            this.updateStatus('success', 'Koordinat Jakarta berhasil digunakan: -6.200000, 106.816666');
            manualDiv.remove();
        };
    }
}

// Debug function
window.debugGeolocation = function() {
    console.log('=== GEOLOCATION DEBUG ===');
    console.log('System:', window.geolocationSystem);
    console.log('Latitude input:', document.querySelector('input[name="latitude"]'));
    console.log('Longitude input:', document.querySelector('input[name="longitude"]'));
    console.log('Status element:', document.getElementById('geolocation-status'));
    console.log('Container:', document.getElementById('geolocation-container'));
    console.log('Navigator geolocation:', !!navigator.geolocation);
    console.log('=== END DEBUG ===');
};
