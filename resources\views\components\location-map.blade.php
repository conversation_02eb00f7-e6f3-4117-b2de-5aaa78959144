@props(['latitude', 'longitude', 'height' => '300px', 'width' => '100%', 'zoom' => 15])

@once
    @push('styles')
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
              integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
              crossorigin=""/>
    @endpush

    @push('scripts')
        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
                integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
                crossorigin=""></script>
    @endpush
@endonce

@php
    $mapId = 'map-' . uniqid();
@endphp

<div class="location-map-container" style="width: {{ $width }}; height: {{ $height }};">
    @if($latitude && $longitude)
        <div id="{{ $mapId }}" class="w-full h-full rounded-lg border border-gray-200 dark:border-gray-600"></div>

        <script>
            (function() {
                const mapId = '{{ $mapId }}';
                const lat = {{ $latitude }};
                const lng = {{ $longitude }};
                const zoom = {{ $zoom }};

                function initializeMap() {
                    // Check if Leaflet is loaded
                    if (typeof L === 'undefined') {
                        console.log('Leaflet not loaded yet, retrying...');
                        setTimeout(initializeMap, 100);
                        return;
                    }

                    // Check if map container exists
                    const mapContainer = document.getElementById(mapId);
                    if (!mapContainer) {
                        console.log('Map container not found, retrying...');
                        setTimeout(initializeMap, 100);
                        return;
                    }

                    // Check if map is already initialized
                    if (mapContainer._leaflet_id) {
                        console.log('Map already initialized for', mapId);
                        return;
                    }

                    try {
                        console.log('Initializing map for', mapId, 'with coordinates:', lat, lng);

                        // Initialize the map
                        const map = L.map(mapId).setView([lat, lng], zoom);

                        // Add OpenStreetMap tiles
                        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                            attribution: '© OpenStreetMap contributors'
                        }).addTo(map);

                        // Add marker
                        const marker = L.marker([lat, lng]).addTo(map);

                        // Add popup with coordinates
                        marker.bindPopup(`
                            <div style="text-align: center;">
                                <strong>Lokasi Absensi</strong><br>
                                Latitude: ${lat}<br>
                                Longitude: ${lng}
                            </div>
                        `).openPopup();

                        // Add circle to show accuracy area
                        L.circle([lat, lng], {
                            color: 'blue',
                            fillColor: '#3b82f6',
                            fillOpacity: 0.1,
                            radius: 50 // 50 meter radius
                        }).addTo(map);

                        // Force map to resize after a short delay
                        setTimeout(() => {
                            map.invalidateSize();
                        }, 250);

                        console.log('Map initialized successfully for', mapId);
                    } catch (error) {
                        console.error('Error initializing map:', error);
                    }
                }

                // Try to initialize immediately
                initializeMap();

                // Also try when DOM is ready
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', initializeMap);
                } else {
                    setTimeout(initializeMap, 100);
                }

                // Listen for modal events (Filament uses Alpine.js)
                document.addEventListener('alpine:init', () => {
                    setTimeout(initializeMap, 500);
                });

                // Listen for any modal open events
                document.addEventListener('modal-opened', () => {
                    setTimeout(initializeMap, 300);
                });

                // MutationObserver to detect when the map container becomes visible
                if (typeof MutationObserver !== 'undefined') {
                    const observer = new MutationObserver((mutations) => {
                        mutations.forEach((mutation) => {
                            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                                const target = mutation.target;
                                if (target.id === mapId || target.contains(document.getElementById(mapId))) {
                                    setTimeout(initializeMap, 100);
                                }
                            }
                        });
                    });

                    // Start observing
                    setTimeout(() => {
                        const container = document.getElementById(mapId);
                        if (container) {
                            observer.observe(container.parentElement || container, {
                                attributes: true,
                                subtree: true,
                                attributeFilter: ['style', 'class']
                            });
                        }
                    }, 100);
                }
            })();
        </script>
    @else
        <div class="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
            <div class="text-center text-gray-500 dark:text-gray-400">
                <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <p>Lokasi tidak tersedia</p>
            </div>
        </div>
    @endif
</div>
