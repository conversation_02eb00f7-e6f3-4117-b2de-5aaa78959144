@props(['photoUrl' => null, 'metadata' => [], 'type' => 'masuk'])

@php
    use App\Services\PhotoMetadataService;
    $formatted = PhotoMetadataService::formatMetadataForDisplay($metadata);
@endphp

<div class="photo-metadata-column" style="position: relative; width: 60px; height: 60px;">
    @if($photoUrl)
        <img
            src="{{ $photoUrl }}"
            alt="Foto {{ ucfirst($type) }}"
            style="
                width: 60px;
                height: 60px;
                object-fit: cover;
                border-radius: 8px;
                cursor: pointer;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            "
            onclick="showPhotoModal('{{ $photoUrl }}', {{ json_encode($formatted) }}, '{{ $type }}')"
        >

        <!-- Metadata indicator -->
        @if(!empty($metadata))
            <div style="
                position: absolute;
                top: -4px;
                right: -4px;
                width: 16px;
                height: 16px;
                background: #10B981;
                border: 2px solid white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 8px;
                color: white;
                font-weight: bold;
            " title="Foto dengan metadata">
                ✓
            </div>
        @endif
    @else
        <div class="w-[60px] h-[60px] bg-gray-100 dark:bg-gray-700 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center text-gray-400 dark:text-gray-500 text-xs">
            No Photo
        </div>
    @endif
</div>

@once
<script>
function showPhotoModal(photoUrl, metadata, type) {
    // Create modal
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        padding: 20px;
    `;

    modal.innerHTML = `
        <div style="
            background: var(--modal-bg, white);
            border-radius: 12px;
            max-width: 500px;
            max-height: 90vh;
            overflow: auto;
            position: relative;
        " class="bg-white dark:bg-gray-800">
            <div style="position: relative;">
                <img src="${photoUrl}" style="
                    width: 100%;
                    height: auto;
                    border-radius: 12px 12px 0 0;
                    display: block;
                ">

                <!-- Close button -->
                <button onclick="this.closest('.modal-overlay').remove()" style="
                    position: absolute;
                    top: 12px;
                    right: 12px;
                    width: 32px;
                    height: 32px;
                    background: rgba(0,0,0,0.6);
                    color: white;
                    border: none;
                    border-radius: 50%;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 18px;
                ">×</button>

                <!-- Metadata overlay -->
                <div style="
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
                    color: white;
                    padding: 20px;
                    border-radius: 0 0 12px 12px;
                ">
                    <h3 style="margin: 0 0 12px 0; font-size: 18px; font-weight: 600;">
                        📸 Foto ${type.charAt(0).toUpperCase() + type.slice(1)}
                    </h3>

                    ${metadata.coordinates ? `
                        <div style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                            <span style="font-size: 16px;">📍</span>
                            <span style="font-weight: 500;">${metadata.coordinates}</span>
                        </div>
                    ` : ''}

                    ${metadata.datetime ? `
                        <div style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                            <span style="font-size: 16px;">🕐</span>
                            <span>${metadata.datetime}</span>
                        </div>
                    ` : ''}

                    ${metadata.status_kehadiran ? `
                        <div style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                            <span style="font-size: 16px;">
                                ${metadata.status_kehadiran === 'Tepat Waktu' ? '✅' :
                                  metadata.status_kehadiran === 'Telat' ? '⏰' : 'ℹ️'}
                            </span>
                            <span style="
                                font-weight: 600;
                                color: ${metadata.status_kehadiran === 'Tepat Waktu' ? '#10B981' :
                                         metadata.status_kehadiran === 'Telat' ? '#F59E0B' : '#6B7280'};
                                background: rgba(255,255,255,0.2);
                                padding: 4px 8px;
                                border-radius: 4px;
                            ">${metadata.status_kehadiran}</span>
                        </div>
                    ` : ''}

                    ${metadata.camera ? `
                        <div style="margin-top: 12px; font-size: 12px; opacity: 0.8;">
                            📱 ${metadata.camera}
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;

    modal.className = 'modal-overlay';
    modal.onclick = (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    };

    document.body.appendChild(modal);
}
</script>
@endonce
