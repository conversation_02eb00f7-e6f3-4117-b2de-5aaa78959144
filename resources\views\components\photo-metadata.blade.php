@props(['metadata', 'photoUrl' => null])

@php
    use App\Services\PhotoMetadataService;
    $formatted = PhotoMetadataService::formatMetadataForDisplay($metadata ?? []);
@endphp

<div class="photo-metadata-container" style="position: relative; display: inline-block; max-width: 100%;">
    @if($photoUrl)
        <img src="{{ $photoUrl }}" alt="Foto Absensi" style="width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
    @endif
    
    <!-- Metadata Overlay -->
    <div class="metadata-overlay" style="
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0.4), transparent);
        color: white;
        padding: 16px;
        border-radius: 0 0 8px 8px;
        font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
        font-size: 13px;
        line-height: 1.4;
    ">
        <!-- Camera Model (Top Left) -->
        @if(isset($formatted['camera']))
            <div style="
                position: absolute;
                top: -40px;
                left: 16px;
                background: rgba(255,255,255,0.9);
                color: #333;
                padding: 4px 8px;
                border-radius: 4px;
                font-weight: 600;
                font-size: 11px;
                backdrop-filter: blur(10px);
            ">
                {{ $formatted['camera'] }}
            </div>
        @endif

        <!-- Main Metadata -->
        <div style="display: flex; flex-direction: column; gap: 4px;">
            <!-- Coordinates -->
            @if(isset($formatted['coordinates']))
                <div style="display: flex; align-items: center; gap: 6px;">
                    <span style="font-size: 14px;">📍</span>
                    <span style="font-weight: 500;">{{ $formatted['coordinates'] }}</span>
                </div>
            @endif

            <!-- DateTime -->
            @if(isset($formatted['datetime']))
                <div style="display: flex; align-items: center; gap: 6px;">
                    <span style="font-size: 14px;">🕐</span>
                    <span>{{ $formatted['datetime'] }}</span>
                </div>
            @endif

            <!-- Status Kehadiran -->
            @if(isset($formatted['status_kehadiran']))
                <div style="display: flex; align-items: center; gap: 6px;">
                    <span style="font-size: 14px;">
                        @if($formatted['status_kehadiran'] === 'Tepat Waktu')
                            ✅
                        @elseif($formatted['status_kehadiran'] === 'Telat')
                            ⏰
                        @else
                            ℹ️
                        @endif
                    </span>
                    <span style="
                        font-weight: 600;
                        color: {{ $formatted['status_kehadiran'] === 'Tepat Waktu' ? '#10B981' : ($formatted['status_kehadiran'] === 'Telat' ? '#F59E0B' : '#6B7280') }};
                        background: rgba(255,255,255,0.2);
                        padding: 2px 6px;
                        border-radius: 3px;
                        backdrop-filter: blur(5px);
                    ">
                        {{ $formatted['status_kehadiran'] }}
                    </span>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
    .photo-metadata-container:hover .metadata-overlay {
        background: linear-gradient(to top, rgba(0,0,0,0.9), rgba(0,0,0,0.6), transparent);
    }
    
    .metadata-overlay {
        transition: all 0.3s ease;
    }
</style>
