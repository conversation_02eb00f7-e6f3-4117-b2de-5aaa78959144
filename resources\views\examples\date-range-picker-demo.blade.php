<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Date Range Picker Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Date Range Picker Demo</h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Contoh Penggunaan</h2>
            
            {{-- Demo 1: Basic Usage --}}
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Filter <PERSON>an
                </label>
                <div class="max-w-md">
                    <x-date-range-picker 
                        placeholder="Pilih periode laporan"
                        @daterange-selected="handleDateRangeSelected($event.detail)"
                    />
                </div>
            </div>
            
            {{-- Demo 2: With Initial Values --}}
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Filter dengan Nilai Awal
                </label>
                <div class="max-w-md">
                    <x-date-range-picker 
                        start-date="2024-01-01"
                        end-date="2024-01-31"
                        placeholder="Periode dengan nilai awal"
                        @daterange-selected="handleDateRangeSelected($event.detail)"
                    />
                </div>
            </div>
            
            {{-- Demo 3: Multiple Instances --}}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Periode Penjualan
                    </label>
                    <x-date-range-picker 
                        placeholder="Pilih periode penjualan"
                        @daterange-selected="handleSalesDateRange($event.detail)"
                    />
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Periode Inventory
                    </label>
                    <x-date-range-picker 
                        placeholder="Pilih periode inventory"
                        @daterange-selected="handleInventoryDateRange($event.detail)"
                    />
                </div>
            </div>
        </div>
        
        {{-- Results Display --}}
        <div 
            x-data="{ 
                selectedRange: null,
                salesRange: null,
                inventoryRange: null
            }"
            @daterange-selected.window="selectedRange = $event.detail"
            @sales-daterange-selected.window="salesRange = $event.detail"
            @inventory-daterange-selected.window="inventoryRange = $event.detail"
            class="bg-white rounded-lg shadow-lg p-6"
        >
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Hasil Seleksi</h2>
            
            {{-- General Selection --}}
            <div class="mb-4 p-4 bg-gray-50 rounded-lg" x-show="selectedRange">
                <h3 class="font-medium text-gray-800 mb-2">Filter Tanggal Laporan:</h3>
                <div class="text-sm text-gray-600" x-show="selectedRange">
                    <p><strong>Tanggal Mulai:</strong> <span x-text="selectedRange?.start"></span> <span x-text="selectedRange?.timeStart"></span></p>
                    <p><strong>Tanggal Selesai:</strong> <span x-text="selectedRange?.end"></span> <span x-text="selectedRange?.timeEnd"></span></p>
                </div>
            </div>
            
            {{-- Sales Selection --}}
            <div class="mb-4 p-4 bg-blue-50 rounded-lg" x-show="salesRange">
                <h3 class="font-medium text-blue-800 mb-2">Periode Penjualan:</h3>
                <div class="text-sm text-blue-600" x-show="salesRange">
                    <p><strong>Tanggal Mulai:</strong> <span x-text="salesRange?.start"></span> <span x-text="salesRange?.timeStart"></span></p>
                    <p><strong>Tanggal Selesai:</strong> <span x-text="salesRange?.end"></span> <span x-text="salesRange?.timeEnd"></span></p>
                </div>
            </div>
            
            {{-- Inventory Selection --}}
            <div class="mb-4 p-4 bg-green-50 rounded-lg" x-show="inventoryRange">
                <h3 class="font-medium text-green-800 mb-2">Periode Inventory:</h3>
                <div class="text-sm text-green-600" x-show="inventoryRange">
                    <p><strong>Tanggal Mulai:</strong> <span x-text="inventoryRange?.start"></span> <span x-text="inventoryRange?.timeStart"></span></p>
                    <p><strong>Tanggal Selesai:</strong> <span x-text="inventoryRange?.end"></span> <span x-text="inventoryRange?.timeEnd"></span></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Event handlers for different date range pickers
        function handleDateRangeSelected(detail) {
            console.log('General Date Range Selected:', detail);
            // You can process the selected date range here
            // detail contains: { start, end, timeStart, timeEnd }
        }
        
        function handleSalesDateRange(detail) {
            console.log('Sales Date Range Selected:', detail);
            // Dispatch custom event for sales
            window.dispatchEvent(new CustomEvent('sales-daterange-selected', { detail }));
        }
        
        function handleInventoryDateRange(detail) {
            console.log('Inventory Date Range Selected:', detail);
            // Dispatch custom event for inventory
            window.dispatchEvent(new CustomEvent('inventory-daterange-selected', { detail }));
        }
    </script>
</body>
</html>
