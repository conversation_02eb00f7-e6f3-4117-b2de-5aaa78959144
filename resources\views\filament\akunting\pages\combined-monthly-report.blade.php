<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Form -->
        <x-filament::section>
            <x-slot name="heading">
                Filter Report
            </x-slot>

            <form wire:submit.prevent="generateReport">
                {{ $this->form }}

                <div class="mt-4">
                    <x-filament::button type="submit" color="primary">
                        Generate Report
                    </x-filament::button>
                </div>
            </form>
        </x-filament::section>

        <!-- Report Results -->
        @if (!empty($reportData))
            <x-filament::section>
                <x-slot name="heading">
                    Combined Report - {{ $reportData['period'] }}
                </x-slot>

                <!-- Overall Summary Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                        </path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-green-900">Total Revenue</h3>
                                <p class="text-2xl font-bold text-green-600">
                                    Rp {{ number_format($reportData['summary']['total_revenue'], 0, ',', '.') }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M20 12H4"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-red-900">Total Expense</h3>
                                <p class="text-2xl font-bold text-red-600">
                                    Rp {{ number_format($reportData['summary']['total_expense'], 0, ',', '.') }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-blue-900">Net Profit</h3>
                                <p
                                    class="text-2xl font-bold {{ $reportData['summary']['net_profit'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                    Rp {{ number_format($reportData['summary']['net_profit'], 0, ',', '.') }}
                                </p>
                                @php
                                    $profitMargin =
                                        $reportData['summary']['total_revenue'] > 0
                                            ? ($reportData['summary']['net_profit'] /
                                                    $reportData['summary']['total_revenue']) *
                                                100
                                            : 0;
                                @endphp
                                <p class="text-sm text-gray-600">
                                    {{ number_format($profitMargin, 2) }}% margin
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Category Breakdown -->
                @if (!empty($reportData['by_category']))
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-4">Breakdown by Category</h3>

                        @foreach ($reportData['by_category'] as $categoryName => $categoryData)
                            <div
                                class="mb-6 p-4 border rounded-lg {{ $categoryName === 'FnB' ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50' }}">
                                <h4
                                    class="text-lg font-medium mb-3 {{ $categoryName === 'FnB' ? 'text-green-800' : 'text-yellow-800' }}">
                                    {{ $categoryName }} Category
                                </h4>

                                <!-- Category Summary -->
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                    <div class="text-center">
                                        <p class="text-sm text-gray-600">Revenue</p>
                                        <p class="text-lg font-semibold text-green-600">
                                            Rp {{ number_format($categoryData['revenue'], 0, ',', '.') }}
                                        </p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-sm text-gray-600">Expense</p>
                                        <p class="text-lg font-semibold text-red-600">
                                            Rp {{ number_format($categoryData['expense'], 0, ',', '.') }}
                                        </p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-sm text-gray-600">Net Profit</p>
                                        <p
                                            class="text-lg font-semibold {{ $categoryData['net_profit'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                            Rp {{ number_format($categoryData['net_profit'], 0, ',', '.') }}
                                        </p>
                                        @php
                                            $categoryMargin =
                                                $categoryData['revenue'] > 0
                                                    ? ($categoryData['net_profit'] / $categoryData['revenue']) * 100
                                                    : 0;
                                        @endphp
                                        <p class="text-xs text-gray-500">
                                            {{ number_format($categoryMargin, 1) }}% margin
                                        </p>
                                    </div>
                                </div>

                                <!-- Outlets in this category -->
                                @if (!empty($categoryData['outlets']))
                                    <div class="overflow-x-auto">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead class="bg-gray-50">
                                                <tr>
                                                    <th
                                                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                                        Outlet</th>
                                                    <th
                                                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                                        Revenue</th>
                                                    <th
                                                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                                        Expense</th>
                                                    <th
                                                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                                        Net Profit</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                                @foreach ($categoryData['outlets'] as $outletName => $outletData)
                                                    <tr>
                                                        <td class="px-4 py-2 text-sm font-medium text-gray-900">
                                                            {{ $outletName }}</td>
                                                        <td class="px-4 py-2 text-sm text-green-600">Rp
                                                            {{ number_format($outletData['revenue'], 0, ',', '.') }}
                                                        </td>
                                                        <td class="px-4 py-2 text-sm text-red-600">Rp
                                                            {{ number_format($outletData['expense'], 0, ',', '.') }}
                                                        </td>
                                                        <td
                                                            class="px-4 py-2 text-sm font-medium {{ $outletData['net_profit'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                                            Rp
                                                            {{ number_format($outletData['net_profit'], 0, ',', '.') }}
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @endif
            </x-filament::section>
        @endif
    </div>
</x-filament-panels::page>
