<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Form -->
        <x-filament::section>
            <x-slot name="heading">
                Filter Report
            </x-slot>

            <div class="space-y-4">
                {{ $this->form }}

                <div class="mt-4">
                    <x-filament::button wire:click="generateReport" color="primary">
                        Generate Report
                    </x-filament::button>
                </div>
            </div>
        </x-filament::section>

        <!-- Report Results -->
        @if (!empty($reportData))
            <x-filament::section>
                <x-slot name="heading">
                    {{ $reportData['outlet']->name }} - {{ $reportData['period'] }}
                </x-slot>

                @if (isset($reportData['has_data']) && !$reportData['has_data'])
                    <!-- No Data Message -->
                    <div class="text-center py-12">
                        <div class="mx-auto h-12 w-12 text-gray-400">
                            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
                        <p class="mt-1 text-sm text-gray-500">
                            No transaction data available for {{ $reportData['outlet']->name }} in
                            {{ $reportData['period'] }}.
                        </p>
                        <div class="mt-6">
                            <a href="{{ route('filament.akunting.resources.daily-transactions.create') }}"
                                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                Add Transaction
                            </a>
                        </div>
                    </div>
                @else
                    <!-- Detailed P&L Report -->

                      <!-- Category Breakdown -->
                    <!-- Summary Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <!-- Revenue Card -->
                        <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                            </path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-green-900">Total Revenue</h3>
                                    <p class="text-2xl font-bold text-green-600">
                                        {{ $this->formatCurrency($reportData['summary']['total_revenue']) }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Expense Card -->
                        <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M20 12H4"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-red-900">Total Expense</h3>
                                    <p class="text-2xl font-bold text-red-600">
                                        {{ $this->formatCurrency($reportData['summary']['total_expense']) }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Net Profit Card -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-blue-900">Net Profit</h3>
                                    <p
                                        class="text-2xl font-bold {{ $reportData['summary']['laba_bersih'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                        {{ $this->formatCurrency($reportData['summary']['laba_bersih']) }}
                                    </p>
                                    <p class="text-sm text-gray-600">
                                        {{ $this->getPercentageDisplay($reportData['summary']['laba_bersih'], $reportData['summary']['total_revenue']) }}
                                        of revenue
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/2">
                                            KETERANGAN
                                        </th>
                                        <th
                                            class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-1/3">
                                            TOTAL
                                        </th>
                                        <th
                                            class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                                            RASIO
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- I. PENDAPATAN -->
                                    <tr class="bg-blue-50">
                                        <td class="px-6 py-3 text-sm font-bold text-gray-900">I. PENDAPATAN</td>
                                        <td class="px-6 py-3"></td>
                                        <td class="px-6 py-3"></td>
                                    </tr>
                                    @if ($reportData['revenue_breakdown']['cash'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Cash</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['revenue_breakdown']['cash'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif
                                    @if ($reportData['revenue_breakdown']['debit'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Debit</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['revenue_breakdown']['debit'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif
                                    @if ($reportData['revenue_breakdown']['transfer'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Transfer</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['revenue_breakdown']['transfer'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif
                                    @if ($reportData['revenue_breakdown']['qris'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan QRIS</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['revenue_breakdown']['qris'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif
                                    @if ($reportData['revenue_breakdown']['gojek'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Gojek</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['revenue_breakdown']['gojek'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif
                                    @if ($reportData['revenue_breakdown']['grab_ovo'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Grab Ovo</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['revenue_breakdown']['grab_ovo'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif
                                    @if ($reportData['revenue_breakdown']['sewa_rak'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Sewa Rak</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['revenue_breakdown']['sewa_rak'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif
                                    <tr class="bg-green-50 font-semibold">
                                        <td class="px-6 py-3 text-sm text-gray-900 pl-12">Total Pendapatan</td>
                                        <td class="px-6 py-3 text-sm text-gray-900 text-right">Rp
                                            {{ number_format($reportData['summary']['total_revenue'], 0, ',', '.') }}
                                        </td>
                                        <td class="px-6 py-3"></td>
                                    </tr>

                                    <!-- II. BEBAN PENJUALAN -->
                                    <tr class="bg-red-50">
                                        <td class="px-6 py-3 text-sm font-bold text-gray-900">II. BEBAN PENJUALAN</td>
                                        <td class="px-6 py-3"></td>
                                        <td class="px-6 py-3"></td>
                                    </tr>
                                    @if ($reportData['expense_breakdown']['beban_bahan_baku']['total'] > 0)
                                        @foreach ($reportData['expense_breakdown']['beban_bahan_baku']['details'] as $key => $amount)
                                            @if ($amount > 0)
                                                <tr>
                                                    <td class="px-6 py-2 text-sm text-gray-700 pl-12">
                                                        @switch($key)
                                                            @case('tagihan_rkv')
                                                                Tagihan RKV
                                                            @break

                                                            @case('tagihan_mitra')
                                                                Tagihan Mitra
                                                            @break

                                                            @case('tagihan_supplier')
                                                                Tagihan Supplier
                                                            @break

                                                            @case('bahan_baku_lainnya')
                                                                Bahan Baku Lainnya
                                                            @break
                                                        @endswitch
                                                    </td>
                                                    <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                        {{ number_format($amount, 0, ',', '.') }}</td>
                                                    <td class="px-6 py-2 text-sm text-gray-600 text-right">
                                                        {{ $this->getPercentageDisplay($amount, $reportData['summary']['total_revenue']) }}
                                                    </td>
                                                </tr>
                                            @endif
                                        @endforeach
                                        <tr class="bg-red-100 font-semibold">
                                            <td class="px-6 py-3 text-sm text-gray-900 pl-12">Total Beban Bahan Baku
                                            </td>
                                            <td class="px-6 py-3 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['expense_breakdown']['beban_bahan_baku']['total'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-3 text-sm text-gray-600 text-right font-semibold">
                                                {{ $this->getPercentageDisplay($reportData['expense_breakdown']['beban_bahan_baku']['total'], $reportData['summary']['total_revenue']) }}
                                            </td>
                                        </tr>
                                    @endif
                                    <tr class="bg-green-100 font-bold">
                                        <td class="px-6 py-3 text-sm text-gray-900">Laba Kotor</td>
                                        <td class="px-6 py-3 text-sm text-gray-900 text-right">Rp
                                            {{ number_format($reportData['summary']['laba_kotor'], 0, ',', '.') }}</td>
                                        <td class="px-6 py-3 text-sm text-gray-600 text-right font-bold">
                                            {{ $this->getPercentageDisplay($reportData['summary']['laba_kotor'], $reportData['summary']['total_revenue']) }}
                                        </td>
                                    </tr>

                                    <!-- III. BEBAN OPERASIONAL -->
                                    <tr class="bg-orange-50">
                                        <td class="px-6 py-3 text-sm font-bold text-gray-900">III. BEBAN OPERASIONAL
                                        </td>
                                        <td class="px-6 py-3"></td>
                                        <td class="px-6 py-3"></td>
                                    </tr>

                                    <!-- Beban GA -->
                                    @if ($reportData['expense_breakdown']['beban_ga']['total'] > 0)
                                        <tr class="bg-blue-25">
                                            <td class="px-6 py-2 text-sm font-semibold text-gray-800 pl-8">Total Biaya
                                                Belanja GA</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right font-semibold">Rp
                                                {{ number_format($reportData['expense_breakdown']['beban_ga']['total'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2 text-sm text-gray-600 text-right">
                                                {{ $this->getPercentageDisplay($reportData['expense_breakdown']['beban_ga']['total'], $reportData['summary']['total_revenue']) }}
                                            </td>
                                        </tr>
                                        @foreach ($reportData['expense_breakdown']['beban_ga']['details'] as $key => $amount)
                                            @if ($amount > 0)
                                                <tr>
                                                    <td class="px-6 py-1 text-sm text-gray-600 pl-16">
                                                        @switch($key)
                                                            @case('material_bangunan')
                                                                Material Bangunan/Kabel/Bayar Tukang
                                                            @break

                                                            @case('service_oven_freezer_packing')
                                                                Service Oven dan Freezer/Mobil/AC/Mesin Packing/Genset
                                                            @break

                                                            @case('service_equipment')
                                                                Service Freezer/Mobil/AC/Motor/CCTV/dll
                                                            @break

                                                            @case('belanja_ga')
                                                                Belanja GA
                                                            @break

                                                            @case('cuci_mobil_oli')
                                                                Cuci Mobil/Isi Angin/Tambal Ban/Ganti Oli
                                                            @break

                                                            @case('kertas_thermal')
                                                                Kertas Thermal/Kertas Label
                                                            @break

                                                            @case('keperluan_genset')
                                                                Keperluan Genset/Dexlite Genset
                                                            @break

                                                            @case('bensin_luxio_putih')
                                                                Bensin Luxio Putih
                                                            @break

                                                            @case('bensin_luxio_silver')
                                                                Bensin Luxio Silver
                                                            @break

                                                            @default
                                                                {{ ucwords(str_replace('_', ' ', $key)) }}
                                                        @endswitch
                                                    </td>
                                                    <td class="px-6 py-1 text-sm text-gray-700 text-right">Rp
                                                        {{ number_format($amount, 0, ',', '.') }}</td>
                                                    <td class="px-6 py-1"></td>
                                                </tr>
                                            @endif
                                        @endforeach
                                    @endif

                                    <!-- Beban Promosi -->
                                    @if ($reportData['expense_breakdown']['beban_promosi']['total'] > 0)
                                        <tr class="bg-purple-25">
                                            <td class="px-6 py-2 text-sm font-semibold text-gray-800 pl-8">Total Beban
                                                Promosi</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right font-semibold">Rp
                                                {{ number_format($reportData['expense_breakdown']['beban_promosi']['total'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2 text-sm text-gray-600 text-right">
                                                {{ $this->getPercentageDisplay($reportData['expense_breakdown']['beban_promosi']['total'], $reportData['summary']['total_revenue']) }}
                                            </td>
                                        </tr>
                                        @foreach ($reportData['expense_breakdown']['beban_promosi']['details'] as $key => $amount)
                                            @if ($amount > 0)
                                                <tr>
                                                    <td class="px-6 py-1 text-sm text-gray-600 pl-16">
                                                        @switch($key)
                                                            @case('free_talam_rs')
                                                                Free talam RS. annisa
                                                            @break

                                                            @case('free_gift_ultah')
                                                                Free gift ultah
                                                            @break

                                                            @case('kue_marketing')
                                                                kue keperluan marketing/Pengeluaran Marketing
                                                            @break

                                                            @case('tester')
                                                                Tester
                                                            @break

                                                            @case('free_bundling_kuker')
                                                                Free Bundling Kuker
                                                            @break

                                                            @case('gift_card')
                                                                Gift Card
                                                            @break

                                                            @default
                                                                {{ ucwords(str_replace('_', ' ', $key)) }}
                                                        @endswitch
                                                    </td>
                                                    <td class="px-6 py-1 text-sm text-gray-700 text-right">Rp
                                                        {{ number_format($amount, 0, ',', '.') }}</td>
                                                    <td class="px-6 py-1"></td>
                                                </tr>
                                            @endif
                                        @endforeach
                                    @endif

                                    <!-- Beban Utilitas -->
                                    @if ($reportData['expense_breakdown']['beban_utilitas']['total'] > 0)
                                        <tr class="bg-yellow-25">
                                            <td class="px-6 py-2 text-sm font-semibold text-gray-800 pl-8">Total Beban
                                                Utilitas</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right font-semibold">Rp
                                                {{ number_format($reportData['expense_breakdown']['beban_utilitas']['total'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2 text-sm text-gray-600 text-right">
                                                {{ $this->getPercentageDisplay($reportData['expense_breakdown']['beban_utilitas']['total'], $reportData['summary']['total_revenue']) }}
                                            </td>
                                        </tr>
                                        @foreach ($reportData['expense_breakdown']['beban_utilitas']['details'] as $key => $amount)
                                            @if ($amount > 0)
                                                <tr>
                                                    <td class="px-6 py-1 text-sm text-gray-600 pl-16">
                                                        @switch($key)
                                                            @case('listrik')
                                                                Bayar Listrik
                                                            @break

                                                            @case('internet_pulsa')
                                                                Bayar Indihome/Pulsa/Paket Telepon
                                                            @break

                                                            @case('pest_control')
                                                                Jasa Pengendalian Hama (Petsco)
                                                            @break

                                                            @case('kebersihan')
                                                                Uang Kebersihan (Angkut Sampah)/Uang Ronda/PDAM
                                                            @break

                                                            @default
                                                                {{ ucwords(str_replace('_', ' ', $key)) }}
                                                        @endswitch
                                                    </td>
                                                    <td class="px-6 py-1 text-sm text-gray-700 text-right">Rp
                                                        {{ number_format($amount, 0, ',', '.') }}</td>
                                                    <td class="px-6 py-1"></td>
                                                </tr>
                                            @endif
                                        @endforeach
                                    @endif

                                    <!-- Other Operational Expenses -->
                                    <!-- Pajak -->
                                    @if ($reportData['expense_breakdown']['pajak']['total'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Bayar PPh/PPN/PBB</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['expense_breakdown']['pajak']['total'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif

                                    <!-- BPJS -->
                                    @if ($reportData['expense_breakdown']['bpjs']['total'] > 0)
                                        @foreach ($reportData['expense_breakdown']['bpjs']['details'] as $key => $amount)
                                            @if ($amount > 0)
                                                <tr>
                                                    <td class="px-6 py-2 text-sm text-gray-700 pl-12">
                                                        @switch($key)
                                                            @case('bpjs_kesehatan')
                                                                BPJS Kesehatan
                                                            @break

                                                            @case('bpjs_tk')
                                                                BPJS TK
                                                            @break

                                                            @default
                                                                {{ ucwords(str_replace('_', ' ', $key)) }}
                                                        @endswitch
                                                    </td>
                                                    <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                        {{ number_format($amount, 0, ',', '.') }}</td>
                                                    <td class="px-6 py-2"></td>
                                                </tr>
                                            @endif
                                        @endforeach
                                    @endif

                                    <!-- Ongkir -->
                                    @if ($reportData['expense_breakdown']['ongkir']['total'] > 0)
                                        @foreach ($reportData['expense_breakdown']['ongkir']['details'] as $key => $amount)
                                            @if ($amount > 0)
                                                <tr>
                                                    <td class="px-6 py-2 text-sm text-gray-700 pl-12">
                                                        @switch($key)
                                                            @case('ongkir_customer_refund')
                                                                Ongkir Customer/Refund
                                                            @break

                                                            @case('fee_supir_bus')
                                                                Fee supir bus
                                                            @break

                                                            @case('ongkir_cabang')
                                                                Ongkir ke cabang
                                                            @break

                                                            @default
                                                                {{ ucwords(str_replace('_', ' ', $key)) }}
                                                        @endswitch
                                                    </td>
                                                    <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                        {{ number_format($amount, 0, ',', '.') }}</td>
                                                    <td class="px-6 py-2"></td>
                                                </tr>
                                            @endif
                                        @endforeach
                                    @endif

                                    <!-- Other -->
                                    @if ($reportData['expense_breakdown']['other']['total'] > 0)
                                        @foreach ($reportData['expense_breakdown']['other']['details'] as $key => $amount)
                                            @if ($amount > 0)
                                                <tr>
                                                    <td class="px-6 py-2 text-sm text-gray-700 pl-12">
                                                        @switch($key)
                                                            @case('pengeluaran_point')
                                                                Pengeluaran Point
                                                            @break

                                                            @default
                                                                {{ ucwords(str_replace('_', ' ', $key)) }}
                                                        @endswitch
                                                    </td>
                                                    <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                        {{ number_format($amount, 0, ',', '.') }}</td>
                                                    <td class="px-6 py-2"></td>
                                                </tr>
                                            @endif
                                        @endforeach
                                    @endif

                                    <!-- Komisi Bank -->
                                    @if ($reportData['expense_breakdown']['komisi_bank']['total'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Komisi Bank dan Gojek
                                                (debit,qr,gojek,grab)</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['expense_breakdown']['komisi_bank']['total'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2 text-sm text-gray-600 text-right">
                                                {{ $this->getPercentageDisplay($reportData['expense_breakdown']['komisi_bank']['total'], $reportData['summary']['total_revenue']) }}
                                            </td>
                                        </tr>
                                    @endif

                                    <!-- Gaji -->
                                    @if ($reportData['expense_breakdown']['gaji']['total'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Gaji karyawan</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['expense_breakdown']['gaji']['total'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2 text-sm text-gray-600 text-right">
                                                {{ $this->getPercentageDisplay($reportData['expense_breakdown']['gaji']['total'], $reportData['summary']['total_revenue']) }}
                                            </td>
                                        </tr>
                                    @endif

                                    <!-- Total Pengeluaran Operasional -->
                                    <tr class="bg-orange-100 font-semibold">
                                        <td class="px-6 py-3 text-sm text-gray-900 pl-8">TOTAL BEBAN OPERASIONAL</td>
                                        <td class="px-6 py-3 text-sm text-gray-900 text-right">Rp
                                            {{ number_format($reportData['summary']['total_beban_operasional'], 0, ',', '.') }}
                                        </td>
                                        <td class="px-6 py-3 text-sm text-gray-600 text-right font-semibold">
                                            {{ $this->getPercentageDisplay($reportData['summary']['total_beban_operasional'], $reportData['summary']['total_revenue']) }}
                                        </td>
                                    </tr>

                                    <!-- LABA BERSIH -->
                                    <tr class="bg-green-200 font-bold text-lg">
                                        <td class="px-6 py-4 text-gray-900">LABA BERSIH</td>
                                        <td class="px-6 py-4 text-gray-900 text-right">Rp
                                            {{ number_format($reportData['summary']['laba_bersih'], 0, ',', '.') }}
                                        </td>
                                        <td class="px-6 py-4 text-gray-600 text-right font-bold">
                                            {{ $this->getPercentageDisplay($reportData['summary']['laba_bersih'], $reportData['summary']['total_revenue']) }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>




                @endif
            </x-filament::section>
        @endif
    </div>
</x-filament-panels::page>
