<x-filament-panels::page>
    <div class="space-y-6">
        <x-filament::section>
            <x-slot name="heading">
                Test Report Data
            </x-slot>

            <div class="space-y-4">
                <p>Outlets: {{ $this->getOutletCount() }}</p>
                <p>Transactions: {{ $this->getTransactionCount() }}</p>
                
                <div class="mt-4">
                    <h3 class="font-semibold">Available Outlets:</h3>
                    <ul class="list-disc list-inside">
                        @foreach(\App\Models\Outlet::all() as $outlet)
                            <li>{{ $outlet->name }} ({{ $outlet->category }}) - Active: {{ $outlet->is_active ? 'Yes' : 'No' }}</li>
                        @endforeach
                    </ul>
                </div>

                <div class="mt-4">
                    <h3 class="font-semibold">Recent Transactions:</h3>
                    <ul class="list-disc list-inside">
                        @foreach(\App\Models\DailyTransaction::latest()->limit(5)->get() as $transaction)
                            <li>{{ $transaction->description }} - {{ $transaction->type }} - Rp {{ number_format($transaction->amount) }}</li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </x-filament::section>
    </div>
</x-filament-panels::page>
