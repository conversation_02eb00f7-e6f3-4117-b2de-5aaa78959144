<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Form -->
        <x-filament::section>
            <x-slot name="heading">
                Filter Report
            </x-slot>

            <form wire:submit.prevent="generateReport">
                {{ $this->form }}

                <div class="mt-4">
                    <x-filament::button type="submit" color="primary">
                        Generate Report
                    </x-filament::button>
                </div>
            </form>
        </x-filament::section>

        <!-- Report Results -->
        @if (!empty($reportData))
            <x-filament::section>
                <x-slot name="heading">
                    VOO Report - {{ $reportData['period'] }}
                </x-slot>

                <!-- Summary Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-yellow-800">Total Revenue</h3>
                        <p class="text-2xl font-bold text-yellow-900">
                            Rp {{ number_format($reportData['summary']['total_revenue'], 0, ',', '.') }}
                        </p>
                    </div>

                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-red-800">Total Expense</h3>
                        <p class="text-2xl font-bold text-red-900">
                            Rp {{ number_format($reportData['summary']['total_expense'], 0, ',', '.') }}
                        </p>
                    </div>

                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-orange-800">Total Receivable</h3>
                        <p class="text-2xl font-bold text-orange-900">
                            Rp {{ number_format($reportData['summary']['total_receivable'], 0, ',', '.') }}
                        </p>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-blue-800">Net Profit</h3>
                        <p
                            class="text-2xl font-bold {{ $reportData['summary']['net_profit'] >= 0 ? 'text-green-900' : 'text-red-900' }}">
                            Rp {{ number_format($reportData['summary']['net_profit'], 0, ',', '.') }}
                        </p>
                    </div>
                </div>

                <!-- Outlet Breakdown -->
                @if (!empty($reportData['by_outlet']))
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-4">Breakdown by VOO Outlet</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Outlet</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Revenue</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Expense</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Receivable</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Cash Deficit</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Net</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach ($reportData['by_outlet'] as $outletName => $outletData)
                                        @php
                                            $net = $outletData['revenue'] - $outletData['expense'];
                                        @endphp
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {{ $outletName }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-yellow-600">
                                                Rp {{ number_format($outletData['revenue'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                                                Rp {{ number_format($outletData['expense'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-orange-600">
                                                Rp {{ number_format($outletData['receivable'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                                Rp {{ number_format($outletData['cash_deficit'], 0, ',', '.') }}
                                            </td>
                                            <td
                                                class="px-6 py-4 whitespace-nowrap text-sm font-medium {{ $net >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                                Rp {{ number_format($net, 0, ',', '.') }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                @endif
            </x-filament::section>
        @endif
    </div>
</x-filament-panels::page>
