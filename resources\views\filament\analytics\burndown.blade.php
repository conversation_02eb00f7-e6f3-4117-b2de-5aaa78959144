<!-- Burndown Chart Analytics -->
<div class="space-y-6">
    <!-- Burndown Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Total Tasks -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                        <x-heroicon-m-list-bullet class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Tasks</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $data['total_tasks'] }}</p>
                </div>
            </div>
        </div>

        <!-- Project Duration -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                        <x-heroicon-m-calendar-days class="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Duration</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $data['project_duration'] }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">days</p>
                </div>
            </div>
        </div>

        <!-- Completion Rate -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                        <x-heroicon-m-chart-pie class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Completion</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $data['completion_rate'] }}%</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-purple-500 h-2 rounded-full transition-all duration-300" 
                         style="width: {{ $data['completion_rate'] }}%"></div>
                </div>
            </div>
        </div>

        <!-- Current Status -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    @php
                        $currentRemaining = end($data['actual_burndown'])['remaining'] ?? 0;
                        $idealRemaining = end($data['ideal_burndown'])['remaining'] ?? 0;
                        $isAhead = $currentRemaining < $idealRemaining;
                        $statusColor = $isAhead ? 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400' : 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400';
                    @endphp
                    <div class="w-8 h-8 {{ $statusColor }} rounded-lg flex items-center justify-center">
                        @if($isAhead)
                            <x-heroicon-m-arrow-trending-up class="w-5 h-5" />
                        @else
                            <x-heroicon-m-arrow-trending-down class="w-5 h-5" />
                        @endif
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Status</p>
                    <p class="text-lg font-bold text-gray-900 dark:text-white">
                        {{ $isAhead ? 'Ahead' : 'Behind' }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                        {{ abs($currentRemaining - $idealRemaining) }} tasks {{ $isAhead ? 'ahead' : 'behind' }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Burndown Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Burndown Chart</h3>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">Ideal Burndown</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">Actual Burndown</span>
                </div>
            </div>
        </div>
        
        <div class="relative h-96">
            <canvas id="burndownChart"></canvas>
        </div>
    </div>

    <!-- Burndown Analysis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Performance Analysis -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Performance Analysis</h3>
            
            @php
                $currentRemaining = end($data['actual_burndown'])['remaining'] ?? 0;
                $idealRemaining = end($data['ideal_burndown'])['remaining'] ?? 0;
                $isAhead = $currentRemaining < $idealRemaining;
                $variance = abs($currentRemaining - $idealRemaining);
                $variancePercentage = $data['total_tasks'] > 0 ? round(($variance / $data['total_tasks']) * 100) : 0;
            @endphp

            <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Current Position</span>
                    <span class="text-sm font-bold {{ $isAhead ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                        {{ $isAhead ? 'Ahead of Schedule' : 'Behind Schedule' }}
                    </span>
                </div>

                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Variance</span>
                    <span class="text-sm font-bold text-gray-900 dark:text-white">
                        {{ $variance }} tasks ({{ $variancePercentage }}%)
                    </span>
                </div>

                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Remaining Tasks</span>
                    <span class="text-sm font-bold text-gray-900 dark:text-white">{{ $currentRemaining }}</span>
                </div>

                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Expected Remaining</span>
                    <span class="text-sm font-bold text-gray-900 dark:text-white">{{ $idealRemaining }}</span>
                </div>
            </div>
        </div>

        <!-- Recommendations -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Recommendations</h3>
            
            <div class="space-y-3">
                @if($isAhead)
                    <div class="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <div class="flex items-start space-x-2">
                            <x-heroicon-m-check-circle class="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
                            <div>
                                <p class="text-sm font-medium text-green-800 dark:text-green-200">Great Progress!</p>
                                <p class="text-sm text-green-700 dark:text-green-300">Team is ahead of schedule. Consider:</p>
                                <ul class="text-sm text-green-700 dark:text-green-300 mt-1 ml-4 list-disc">
                                    <li>Adding additional features or improvements</li>
                                    <li>Conducting thorough testing and quality assurance</li>
                                    <li>Documenting lessons learned for future projects</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <div class="flex items-start space-x-2">
                            <x-heroicon-m-exclamation-triangle class="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5" />
                            <div>
                                <p class="text-sm font-medium text-red-800 dark:text-red-200">Action Required</p>
                                <p class="text-sm text-red-700 dark:text-red-300">Team is behind schedule. Consider:</p>
                                <ul class="text-sm text-red-700 dark:text-red-300 mt-1 ml-4 list-disc">
                                    <li>Increasing team capacity or working hours</li>
                                    <li>Removing non-essential features from scope</li>
                                    <li>Identifying and resolving blockers</li>
                                    <li>Improving team communication and coordination</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- General Recommendations -->
                <div class="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <div class="flex items-start space-x-2">
                        <x-heroicon-m-light-bulb class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                        <div>
                            <p class="text-sm font-medium text-blue-800 dark:text-blue-200">General Tips</p>
                            <ul class="text-sm text-blue-700 dark:text-blue-300 mt-1 ml-4 list-disc">
                                <li>Review burndown chart weekly in team meetings</li>
                                <li>Update task estimates based on actual completion times</li>
                                <li>Celebrate milestones to maintain team motivation</li>
                                <li>Use burndown insights for future project planning</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Burndown Metrics Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Detailed Burndown Data</h3>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-900">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Ideal Remaining</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actual Remaining</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Variance</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach(array_slice($data['ideal_burndown'], -10) as $index => $idealPoint)
                        @php
                            $actualPoint = $data['actual_burndown'][$index + count($data['actual_burndown']) - 10] ?? ['remaining' => 0];
                            $variance = $actualPoint['remaining'] - $idealPoint['remaining'];
                            $isAheadPoint = $variance < 0;
                        @endphp
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ \Carbon\Carbon::parse($idealPoint['date'])->format('M j, Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ $idealPoint['remaining'] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ $actualPoint['remaining'] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm {{ $isAheadPoint ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                {{ $isAheadPoint ? '' : '+' }}{{ $variance }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $isAheadPoint ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300' }}">
                                    {{ $isAheadPoint ? 'Ahead' : 'Behind' }}
                                </span>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
