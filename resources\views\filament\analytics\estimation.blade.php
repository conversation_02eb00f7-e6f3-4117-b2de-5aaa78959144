<!-- Estimation Accuracy Analytics -->
<div class="space-y-6">
    <!-- Estimation Summary Cards -->
    <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
        <!-- Average Accuracy -->
        <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg dark:bg-blue-900/20">
                        {{-- <x-heroicon-m-target class="w-5 h-5 text-blue-600 dark:text-blue-400" /> --}}
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Accuracy</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $data['average_accuracy'] }}%</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700">
                    <div class="h-2 transition-all duration-300 bg-blue-500 rounded-full"
                         style="width: {{ $data['average_accuracy'] }}%"></div>
                </div>
            </div>
        </div>

        <!-- Accuracy Rate -->
        <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="flex items-center justify-center w-8 h-8 bg-green-100 rounded-lg dark:bg-green-900/20">
                        <x-heroicon-m-check-circle class="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Accuracy Rate</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $data['accuracy_rate'] }}%</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">within ±20%</p>
                </div>
            </div>
        </div>

        <!-- Tasks Analyzed -->
        <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="flex items-center justify-center w-8 h-8 bg-purple-100 rounded-lg dark:bg-purple-900/20">
                        <x-heroicon-m-list-bullet class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Tasks Analyzed</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $data['total_tasks_analyzed'] }}</p>
                </div>
            </div>
        </div>

        <!-- Estimation Quality -->
        <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    @php
                        $qualityScore = $data['average_accuracy'];
                        $qualityColor = $qualityScore >= 80 ? 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400' :
                                       ($qualityScore >= 60 ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400' :
                                       'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400');
                        $qualityLabel = $qualityScore >= 80 ? 'Excellent' : ($qualityScore >= 60 ? 'Good' : 'Needs Work');
                    @endphp
                    <div class="w-8 h-8 {{ $qualityColor }} rounded-lg flex items-center justify-center">
                        <x-heroicon-m-star class="w-5 h-5" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Quality</p>
                    <p class="text-lg font-bold text-gray-900 dark:text-white">{{ $qualityLabel }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Estimation Insights -->
    @if(!empty($data['estimation_insights']))
        <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <h3 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">Estimation Insights</h3>

            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                @foreach($data['estimation_insights'] as $insight)
                    <div class="p-4 border border-blue-200 rounded-lg bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800">
                        <div class="flex items-start space-x-2">
                            <x-heroicon-m-light-bulb class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                            <p class="text-sm text-blue-800 dark:text-blue-200">{{ $insight }}</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Estimation vs Actual Analysis -->
    @if(!empty($data['estimation_data']))
        <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <h3 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">Estimation vs Actual Hours</h3>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-900">
                        <tr>
                            <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-400">Task</th>
                            <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-400">Estimated</th>
                            <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-400">Actual</th>
                            <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-400">Variance</th>
                            <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-400">Accuracy</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                        @foreach(array_slice($data['estimation_data'], 0, 20) as $task)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="max-w-xs px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                                    <div class="truncate" title="{{ $task['task_name'] }}">
                                        {{ $task['task_name'] }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap dark:text-white">
                                    {{ $task['estimated_hours'] }}h
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap dark:text-white">
                                    {{ $task['actual_hours'] }}h
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm {{ $task['variance'] > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400' }}">
                                    {{ $task['variance'] > 0 ? '+' : '' }}{{ $task['variance'] }}%
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $task['accuracy'] >= 80 ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300' : ($task['accuracy'] >= 60 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300') }}">
                                        {{ $task['accuracy'] }}%
                                    </span>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            @if(count($data['estimation_data']) > 20)
                <div class="mt-4 text-center">
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        Showing first 20 of {{ count($data['estimation_data']) }} tasks
                    </p>
                </div>
            @endif
        </div>

        <!-- Estimation Distribution -->
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <!-- Accuracy Distribution -->
            <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                <h3 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">Accuracy Distribution</h3>

                @php
                    $accuracyRanges = [
                        '90-100%' => collect($data['estimation_data'])->whereBetween('accuracy', [90, 100])->count(),
                        '80-89%' => collect($data['estimation_data'])->whereBetween('accuracy', [80, 89])->count(),
                        '70-79%' => collect($data['estimation_data'])->whereBetween('accuracy', [70, 79])->count(),
                        '60-69%' => collect($data['estimation_data'])->whereBetween('accuracy', [60, 69])->count(),
                        'Below 60%' => collect($data['estimation_data'])->where('accuracy', '<', 60)->count(),
                    ];
                    $totalTasks = collect($accuracyRanges)->sum();
                @endphp

                <div class="space-y-3">
                    @foreach($accuracyRanges as $range => $count)
                        @php
                            $percentage = $totalTasks > 0 ? round(($count / $totalTasks) * 100) : 0;
                            $barColor = match($range) {
                                '90-100%' => 'bg-green-500',
                                '80-89%' => 'bg-blue-500',
                                '70-79%' => 'bg-yellow-500',
                                '60-69%' => 'bg-orange-500',
                                default => 'bg-red-500',
                            };
                        @endphp
                        <div class="flex items-center justify-between">
                            <span class="w-20 text-sm font-medium text-gray-700 dark:text-gray-300">{{ $range }}</span>
                            <div class="flex-1 mx-4">
                                <div class="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700">
                                    <div class="{{ $barColor }} h-2 rounded-full transition-all duration-300"
                                         style="width: {{ $percentage }}%"></div>
                                </div>
                            </div>
                            <span class="w-16 text-sm text-right text-gray-600 dark:text-gray-400">{{ $count }} ({{ $percentage }}%)</span>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Variance Analysis -->
            <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                <h3 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">Variance Analysis</h3>

                @php
                    $varianceRanges = [
                        'Underestimated >50%' => collect($data['estimation_data'])->where('variance', '>', 50)->count(),
                        'Underestimated 20-50%' => collect($data['estimation_data'])->whereBetween('variance', [20, 50])->count(),
                        'Accurate ±20%' => collect($data['estimation_data'])->whereBetween('variance', [-20, 20])->count(),
                        'Overestimated 20-50%' => collect($data['estimation_data'])->whereBetween('variance', [-50, -20])->count(),
                        'Overestimated >50%' => collect($data['estimation_data'])->where('variance', '<', -50)->count(),
                    ];
                @endphp

                <div class="space-y-3">
                    @foreach($varianceRanges as $range => $count)
                        @php
                            $percentage = $totalTasks > 0 ? round(($count / $totalTasks) * 100) : 0;
                            $barColor = match(true) {
                                str_contains($range, 'Accurate') => 'bg-green-500',
                                str_contains($range, '20-50%') => 'bg-yellow-500',
                                default => 'bg-red-500',
                            };
                        @endphp
                        <div class="flex items-center justify-between">
                            <span class="w-32 text-sm font-medium text-gray-700 dark:text-gray-300">{{ $range }}</span>
                            <div class="flex-1 mx-4">
                                <div class="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700">
                                    <div class="{{ $barColor }} h-2 rounded-full transition-all duration-300"
                                         style="width: {{ $percentage }}%"></div>
                                </div>
                            </div>
                            <span class="w-16 text-sm text-right text-gray-600 dark:text-gray-400">{{ $count }} ({{ $percentage }}%)</span>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Estimation Improvement Recommendations -->
        <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <h3 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">Estimation Improvement Recommendations</h3>

            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <!-- For Teams with Low Accuracy -->
                @if($data['average_accuracy'] < 70)
                    <div class="space-y-3">
                        <h4 class="font-medium text-red-800 dark:text-red-200">🎯 Improving Accuracy</h4>
                        <ul class="ml-4 space-y-1 text-sm text-gray-700 list-disc dark:text-gray-300">
                            <li>Break down large tasks into smaller, more predictable pieces</li>
                            <li>Use historical data from similar tasks for reference</li>
                            <li>Include buffer time for unexpected complications</li>
                            <li>Involve the entire team in estimation discussions</li>
                            <li>Track and review estimation accuracy regularly</li>
                        </ul>
                    </div>
                @endif

                <!-- For Teams with Good Accuracy -->
                @if($data['average_accuracy'] >= 70)
                    <div class="space-y-3">
                        <h4 class="font-medium text-green-800 dark:text-green-200">✅ Maintaining Excellence</h4>
                        <ul class="ml-4 space-y-1 text-sm text-gray-700 list-disc dark:text-gray-300">
                            <li>Continue using current estimation practices</li>
                            <li>Share estimation techniques with other teams</li>
                            <li>Focus on consistency across different task types</li>
                            <li>Document estimation patterns for future reference</li>
                        </ul>
                    </div>
                @endif

                <!-- General Best Practices -->
                <div class="space-y-3">
                    <h4 class="font-medium text-blue-800 dark:text-blue-200">💡 Best Practices</h4>
                    <ul class="ml-4 space-y-1 text-sm text-gray-700 list-disc dark:text-gray-300">
                        <li>Use relative estimation techniques (story points, t-shirt sizes)</li>
                        <li>Consider complexity, uncertainty, and effort separately</li>
                        <li>Account for dependencies and external factors</li>
                        <li>Regular retrospectives on estimation accuracy</li>
                        <li>Use multiple estimation techniques for validation</li>
                        <li>Track actual time spent for continuous improvement</li>
                    </ul>
                </div>
            </div>
        </div>
    @else
        <!-- No Data Available -->
        <div class="p-12 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <div class="text-center">
                <x-heroicon-o-chart-bar class="w-16 h-16 mx-auto mb-4 text-gray-400" />
                <h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">No Estimation Data Available</h3>
                <p class="mb-6 text-gray-500 dark:text-gray-400">
                    To see estimation accuracy analysis, you need completed tasks with time tracking data.
                </p>
                <div class="p-4 border border-blue-200 rounded-lg bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800">
                    <h4 class="mb-2 font-medium text-blue-900 dark:text-blue-100">Getting Started with Estimation Tracking</h4>
                    <ul class="ml-4 space-y-1 text-sm text-left text-blue-800 list-disc dark:text-blue-200">
                        <li>Add estimated hours to your tasks</li>
                        <li>Track actual time spent using timesheets</li>
                        <li>Complete tasks to generate comparison data</li>
                        <li>Review this page after completing several tasks</li>
                    </ul>
                </div>
            </div>
        </div>
    @endif
</div>
