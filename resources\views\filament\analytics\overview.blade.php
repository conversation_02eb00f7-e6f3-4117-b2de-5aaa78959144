<!-- Project Overview Analytics -->
<div class="space-y-6">
    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Progress Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                        <x-heroicon-m-chart-pie class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Progress</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $data['overview']['progress_percentage'] }}%</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full transition-all duration-300" 
                         style="width: {{ $data['overview']['progress_percentage'] }}%"></div>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {{ $data['overview']['completed_tasks'] }} of {{ $data['overview']['total_tasks'] }} tasks completed
                </p>
            </div>
        </div>

        <!-- Team Activity Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                        <x-heroicon-m-users class="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Team</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $data['overview']['active_members'] }}/{{ $data['overview']['team_members'] }}</p>
                </div>
            </div>
            <div class="mt-4">
                <p class="text-xs text-gray-500 dark:text-gray-400">
                    {{ round(($data['overview']['active_members'] / max($data['overview']['team_members'], 1)) * 100) }}% team engagement
                </p>
            </div>
        </div>

        <!-- Time Tracking Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                        <x-heroicon-m-clock class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Recent Hours</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $data['overview']['recent_hours'] }}h</p>
                </div>
            </div>
            <div class="mt-4">
                <p class="text-xs text-gray-500 dark:text-gray-400">
                    {{ $data['overview']['total_hours'] }}h total logged
                </p>
            </div>
        </div>

        <!-- Project Health Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    @php
                        $healthColors = [
                            'excellent' => 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400',
                            'good' => 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400',
                            'warning' => 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400',
                            'critical' => 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400',
                        ];
                        $healthColor = $healthColors[$data['overview']['project_health']['status']] ?? $healthColors['warning'];
                    @endphp
                    <div class="w-8 h-8 {{ $healthColor }} rounded-lg flex items-center justify-center">
                        <x-heroicon-m-heart class="w-5 h-5" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Health Score</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $data['overview']['project_health']['score'] }}/100</p>
                </div>
            </div>
            <div class="mt-4">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $healthColor }}">
                    {{ ucfirst($data['overview']['project_health']['status']) }}
                </span>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Task Distribution Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Task Distribution</h3>
            <div class="relative h-64">
                <canvas id="taskDistributionChart"></canvas>
            </div>
            <div class="mt-4 grid grid-cols-3 gap-4 text-center">
                <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $data['task_distribution']['todo'] ?? 0 }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">To Do</p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $data['task_distribution']['in_progress'] ?? 0 }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">In Progress</p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $data['task_distribution']['completed'] ?? 0 }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">Completed</p>
                </div>
            </div>
        </div>

        <!-- Time Distribution Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Time Distribution by Team Member</h3>
            <div class="relative h-64">
                <canvas id="timeDistributionChart"></canvas>
            </div>
            @if(empty($data['time_distribution']))
                <div class="mt-4 text-center">
                    <p class="text-sm text-gray-500 dark:text-gray-400">No time tracking data available</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Recent Activity</h3>
        
        @if(!empty($data['recent_activity']))
            <div class="space-y-4">
                @foreach($data['recent_activity'] as $activity)
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            @if($activity['type'] === 'task_completed')
                                <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                                    <x-heroicon-m-check-circle class="w-4 h-4 text-green-600 dark:text-green-400" />
                                </div>
                            @elseif($activity['type'] === 'time_logged')
                                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                                    <x-heroicon-m-clock class="w-4 h-4 text-blue-600 dark:text-blue-400" />
                                </div>
                            @else
                                <div class="w-8 h-8 bg-gray-100 dark:bg-gray-900/20 rounded-full flex items-center justify-center">
                                    <x-heroicon-m-bell class="w-4 h-4 text-gray-600 dark:text-gray-400" />
                                </div>
                            @endif
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $activity['title'] }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $activity['description'] }}</p>
                            <div class="flex items-center mt-1 space-x-2">
                                <span class="text-xs text-gray-500 dark:text-gray-400">by {{ $activity['user'] }}</span>
                                <span class="text-xs text-gray-400 dark:text-gray-500">•</span>
                                <time class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ \Carbon\Carbon::parse($activity['time'])->diffForHumans() }}
                                </time>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-8">
                <x-heroicon-o-clock class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-500 dark:text-gray-400">No recent activity</p>
            </div>
        @endif
    </div>

    <!-- Project Health Details -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Project Health Analysis</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Progress Score -->
            <div>
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Progress Score</span>
                    <span class="text-sm font-bold text-gray-900 dark:text-white">{{ $data['overview']['project_health']['progress_score'] }}%</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full transition-all duration-300" 
                         style="width: {{ $data['overview']['project_health']['progress_score'] }}%"></div>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Based on task completion rate</p>
            </div>

            <!-- Schedule Score -->
            <div>
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Schedule Score</span>
                    <span class="text-sm font-bold text-gray-900 dark:text-white">{{ $data['overview']['project_health']['overdue_score'] }}%</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full transition-all duration-300" 
                         style="width: {{ $data['overview']['project_health']['overdue_score'] }}%"></div>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Based on on-time delivery</p>
            </div>
        </div>

        <!-- Health Recommendations -->
        <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">💡 Health Recommendations</h4>
            <div class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                @if($data['overview']['project_health']['status'] === 'excellent')
                    <p>• Project is performing excellently! Keep up the great work.</p>
                    <p>• Consider documenting best practices for future projects.</p>
                @elseif($data['overview']['project_health']['status'] === 'good')
                    <p>• Project is on track with good performance.</p>
                    <p>• Monitor for any potential bottlenecks or delays.</p>
                @elseif($data['overview']['project_health']['status'] === 'warning')
                    <p>• Project needs attention to prevent delays.</p>
                    <p>• Review task assignments and resource allocation.</p>
                    <p>• Consider increasing team communication frequency.</p>
                @else
                    <p>• Project requires immediate intervention.</p>
                    <p>• Conduct urgent team meeting to address blockers.</p>
                    <p>• Consider scope reduction or deadline extension.</p>
                @endif
            </div>
        </div>
    </div>
</div>
