<!-- Velocity Analytics -->
<div class="space-y-6">
    <!-- Velocity Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Average Velocity -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                        <x-heroicon-m-bolt class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Velocity</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $data['average_velocity'] }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">tasks/week</p>
                </div>
            </div>
        </div>

        <!-- Average Hours -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                        <x-heroicon-m-clock class="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Hours</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $data['average_hours'] }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">hours/week</p>
                </div>
            </div>
        </div>

        <!-- Velocity Trend -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    @php
                        $trendColors = [
                            'improving' => 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400',
                            'declining' => 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400',
                            'stable' => 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400',
                        ];
                        $trendColor = $trendColors[$data['velocity_trend']] ?? $trendColors['stable'];

                        $trendIcons = [
                            'improving' => 'heroicon-m-arrow-trending-up',
                            'declining' => 'heroicon-m-arrow-trending-down',
                            'stable' => 'heroicon-m-minus',
                        ];
                        $trendIcon = $trendIcons[$data['velocity_trend']] ?? $trendIcons['stable'];
                    @endphp
                    <div class="w-8 h-8 {{ $trendColor }} rounded-lg flex items-center justify-center">
                        <x-dynamic-component :component="$trendIcon" class="w-5 h-5" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Trend</p>
                    <p class="text-lg font-bold text-gray-900 dark:text-white">{{ ucfirst($data['velocity_trend']) }}</p>
                </div>
            </div>
        </div>

        <!-- Efficiency Score -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                        <x-heroicon-m-chart-bar class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Efficiency</p>
                    @php
                        $efficiency = $data['average_hours'] > 0 ? round(($data['average_velocity'] / ($data['average_hours'] / 8)) * 100) : 0;
                    @endphp
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $efficiency }}%</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">tasks per 8h</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Velocity Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Team Velocity (Last 12 Weeks)</h3>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">Completed Tasks</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">Hours Logged</span>
                </div>
            </div>
        </div>

        <div class="relative h-96">
            <canvas id="velocityChart"></canvas>
        </div>
    </div>

    <!-- Velocity Analysis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Weekly Performance -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Weekly Performance</h3>

            <div class="space-y-3">
                @foreach(array_slice($data['weekly_data'], -6) as $week)
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ $week['week'] }}</span>
                                <div class="flex items-center space-x-4">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">{{ $week['completed_tasks'] }} tasks</span>
                                    <span class="text-sm text-gray-600 dark:text-gray-400">{{ $week['hours_logged'] }}h</span>
                                    <span class="text-xs font-medium px-2 py-1 rounded-full {{ $week['velocity_score'] >= 80 ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300' : ($week['velocity_score'] >= 60 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300') }}">
                                        {{ $week['velocity_score'] }}%
                                    </span>
                                </div>
                            </div>
                            <div class="mt-2">
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                         style="width: {{ min($week['velocity_score'], 100) }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Velocity Insights -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Velocity Insights</h3>

            @php
                $recentWeeks = array_slice($data['weekly_data'], -4);
                $bestWeek = collect($data['weekly_data'])->sortByDesc('completed_tasks')->first();
                $worstWeek = collect($data['weekly_data'])->sortBy('completed_tasks')->first();
                $consistencyScore = $this->calculateConsistencyScore($data['weekly_data']);
            @endphp

            <div class="space-y-4">
                <!-- Best Performance -->
                <div class="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                    <div class="flex items-center space-x-2">
                        <x-heroicon-m-trophy class="w-5 h-5 text-green-600 dark:text-green-400" />
                        <div>
                            <p class="text-sm font-medium text-green-800 dark:text-green-200">Best Week</p>
                            <p class="text-sm text-green-700 dark:text-green-300">
                                {{ $bestWeek['week'] ?? 'N/A' }} - {{ $bestWeek['completed_tasks'] ?? 0 }} tasks completed
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Improvement Opportunity -->
                <div class="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <div class="flex items-center space-x-2">
                        <x-heroicon-m-arrow-up class="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                        <div>
                            <p class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Improvement Opportunity</p>
                            <p class="text-sm text-yellow-700 dark:text-yellow-300">
                                {{ $worstWeek['week'] ?? 'N/A' }} - {{ $worstWeek['completed_tasks'] ?? 0 }} tasks completed
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Consistency Score -->
                <div class="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <div class="flex items-center space-x-2">
                        <x-heroicon-m-chart-bar class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        <div>
                            <p class="text-sm font-medium text-blue-800 dark:text-blue-200">Consistency Score</p>
                            <p class="text-sm text-blue-700 dark:text-blue-300">
                                {{ $consistencyScore }}% - {{ $consistencyScore >= 80 ? 'Very Consistent' : ($consistencyScore >= 60 ? 'Moderately Consistent' : 'Needs Improvement') }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Trend Analysis -->
                <div class="p-3 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                    <div class="flex items-center space-x-2">
                        <x-dynamic-component :component="$trendIcon" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                        <div>
                            <p class="text-sm font-medium text-purple-800 dark:text-purple-200">Velocity Trend</p>
                            <p class="text-sm text-purple-700 dark:text-purple-300">
                                Team velocity is {{ $data['velocity_trend'] }}
                                @if($data['velocity_trend'] === 'improving')
                                    - Keep up the great work!
                                @elseif($data['velocity_trend'] === 'declining')
                                    - Consider addressing blockers
                                @else
                                    - Maintaining steady pace
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Velocity Recommendations -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Velocity Improvement Recommendations</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- For Improving Teams -->
            @if($data['velocity_trend'] === 'improving')
                <div class="space-y-3">
                    <h4 class="font-medium text-green-800 dark:text-green-200">🚀 Sustaining Momentum</h4>
                    <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1 ml-4 list-disc">
                        <li>Document what's working well for future reference</li>
                        <li>Share best practices with other teams</li>
                        <li>Consider taking on more challenging tasks</li>
                        <li>Maintain current team composition and processes</li>
                    </ul>
                </div>
            @endif

            <!-- For Declining Teams -->
            @if($data['velocity_trend'] === 'declining')
                <div class="space-y-3">
                    <h4 class="font-medium text-red-800 dark:text-red-200">⚠️ Addressing Decline</h4>
                    <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1 ml-4 list-disc">
                        <li>Identify and remove blockers or impediments</li>
                        <li>Review task complexity and estimation accuracy</li>
                        <li>Check team workload and prevent burnout</li>
                        <li>Improve communication and collaboration</li>
                    </ul>
                </div>
            @endif

            <!-- General Recommendations -->
            <div class="space-y-3">
                <h4 class="font-medium text-blue-800 dark:text-blue-200">💡 General Improvements</h4>
                <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1 ml-4 list-disc">
                    <li>Regular retrospectives to identify improvement areas</li>
                    <li>Break down large tasks into smaller, manageable pieces</li>
                    <li>Invest in team training and skill development</li>
                    <li>Use velocity data for better sprint planning</li>
                    <li>Celebrate achievements and milestones</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Detailed Velocity Data -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Detailed Velocity Data</h3>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-900">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Week</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Completed Tasks</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Hours Logged</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Velocity Score</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Efficiency</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($data['weekly_data'] as $week)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                {{ $week['week'] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ $week['completed_tasks'] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ $week['hours_logged'] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $week['velocity_score'] >= 80 ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300' : ($week['velocity_score'] >= 60 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300') }}">
                                    {{ $week['velocity_score'] }}%
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ $week['hours_logged'] > 0 ? round($week['completed_tasks'] / ($week['hours_logged'] / 8), 2) : 0 }} tasks/day
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>


