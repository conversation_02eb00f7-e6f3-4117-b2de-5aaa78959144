<div class="space-y-4">
    <div class="prose-sm prose max-w-none">
        {!! \Illuminate\Support\Str::markdown($content) !!}
    </div>

    @if($record->properties && count($record->properties) > 0)
        <div class="p-4 mt-4 rounded-lg bg-gray-50">
            <h4 class="mb-2 font-semibold text-gray-900">Detail Teknis:</h4>
            <dl class="space-y-1">
                @foreach($record->properties as $key => $value)
                    <div class="flex">
                        <dt class="w-1/3 font-medium text-gray-600">{{ ucfirst(str_replace('_', ' ', $key)) }}:</dt>
                        <dd class="text-gray-900">{{ is_array($value) ? json_encode($value) : $value }}</dd>
                    </div>
                @endforeach
            </dl>
        </div>
    @endif

    <div class="mt-4 text-xs text-gray-500">
        <p><strong>Waktu:</strong> {{ $record->created_at->format('d M Y H:i:s') }}</p>
        <p><strong>Pengguna:</strong> {{ $record->user->name ?? 'System' }}</p>
        <p><strong>Objek:</strong> {{ class_basename($record->subject_type) }} #{{ $record->subject_id }}</p>
    </div>
</div>
