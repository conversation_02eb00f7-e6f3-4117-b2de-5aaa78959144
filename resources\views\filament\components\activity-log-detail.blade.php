<div class="space-y-4">
    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Informasi Aktivitas
        </h3>
        
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Event:</span>
                <span class="ml-2 px-2 py-1 rounded text-xs font-medium
                    @if($record->event === 'created') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                    @elseif($record->event === 'updated') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                    @elseif($record->event === 'deleted') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                    @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                    @endif">
                    {{ match($record->event) {
                        'created' => 'Dibuat',
                        'updated' => 'Diupdate', 
                        'deleted' => 'Dihapus',
                        default => ucfirst($record->event)
                    } }}
                </span>
            </div>
            
            <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Waktu:</span>
                <span class="ml-2 text-gray-900 dark:text-gray-100">
                    {{ $record->created_at->format('d M Y H:i:s') }}
                </span>
            </div>
            
            <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Oleh:</span>
                <span class="ml-2 text-gray-900 dark:text-gray-100">
                    {{ $record->causer?->name ?? 'System' }}
                </span>
            </div>
            
            <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Model:</span>
                <span class="ml-2 text-gray-900 dark:text-gray-100">
                    {{ class_basename($record->subject_type) }}
                </span>
            </div>
        </div>
        
        @if($record->description)
        <div class="mt-3">
            <span class="font-medium text-gray-700 dark:text-gray-300">Deskripsi:</span>
            <p class="ml-2 text-gray-900 dark:text-gray-100">{{ $record->description }}</p>
        </div>
        @endif
    </div>

    @if(!empty($properties))
    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
            Detail Perubahan
        </h3>
        
        @if(isset($properties['attributes']) && isset($properties['old']))
        <div class="space-y-3">
            @foreach($properties['attributes'] as $key => $newValue)
                @php
                    $oldValue = $properties['old'][$key] ?? null;
                    $hasChanged = $oldValue != $newValue;
                @endphp
                
                @if($hasChanged)
                <div class="border-l-4 border-blue-400 pl-4">
                    <div class="font-medium text-gray-700 dark:text-gray-300 capitalize">
                        {{ str_replace('_', ' ', $key) }}
                    </div>
                    <div class="text-sm space-y-1">
                        <div class="text-red-600 dark:text-red-400">
                            <span class="font-medium">Sebelum:</span> 
                            {{ is_null($oldValue) ? 'null' : $oldValue }}
                        </div>
                        <div class="text-green-600 dark:text-green-400">
                            <span class="font-medium">Sesudah:</span> 
                            {{ is_null($newValue) ? 'null' : $newValue }}
                        </div>
                    </div>
                </div>
                @endif
            @endforeach
        </div>
        @else
        <div class="text-gray-600 dark:text-gray-400">
            <pre class="whitespace-pre-wrap text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded">{{ json_encode($properties, JSON_PRETTY_PRINT) }}</pre>
        </div>
        @endif
    </div>
    @endif
</div>
