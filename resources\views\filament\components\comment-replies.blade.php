@if($getRecord()->replies && $getRecord()->replies->count() > 0)
    <div class="ml-6 border-l-2 border-gray-200 pl-4 space-y-3">
        @foreach($getRecord()->replies as $reply)
            <div class="bg-gray-50 rounded-lg p-3 space-y-2">
                <div class="flex justify-between items-start">
                    <div class="flex items-center space-x-2">
                        <span class="font-medium text-sm text-primary-600">
                            {{ $reply->user->name ?? 'Unknown User' }}
                        </span>
                        <span class="text-xs text-gray-500">
                            {{ $reply->created_at->diffForHumans() }}
                        </span>
                    </div>
                    @if($reply->attachments && count($reply->attachments) > 0)
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ count($reply->attachments) }} file(s)
                        </span>
                    @endif
                </div>

                <div class="text-sm text-gray-700">
                    {{ $reply->comment }}
                </div>

                @if($reply->attachments && count($reply->attachments) > 0)
                    <div class="flex flex-wrap gap-2 mt-2">
                        @foreach($reply->attachments as $attachment)
                            <a href="{{ \Illuminate\Support\Facades\Storage::url($attachment) }}"
                               target="_blank"
                               class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clip-rule="evenodd"></path>
                                </svg>
                                {{ basename($attachment) }}
                            </a>
                        @endforeach
                    </div>
                @endif
            </div>
        @endforeach
    </div>
@endif
