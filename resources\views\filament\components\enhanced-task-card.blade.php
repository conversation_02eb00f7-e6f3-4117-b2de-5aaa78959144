@php
    $isCompact = $compact ?? false;
    $cardClasses = $isCompact
        ? 'enhanced-task-card bg-white dark:bg-gray-800 p-2 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 cursor-move hover:shadow-md transition-all duration-200'
        : 'enhanced-task-card bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 cursor-move hover:shadow-md hover:scale-[1.02] transition-all duration-200';
@endphp

<div class="{{ $cardClasses }}" data-task-id="{{ $task->id }}">
    <!-- Task Header -->
    <div class="flex justify-between items-start mb-2">
        <div class="flex items-center space-x-2">
            <span
                class="text-xs font-semibold text-blue-600 dark:text-blue-400 px-2 py-1 bg-blue-50 dark:bg-blue-900/30 rounded">
                TASK
            </span>

        </div>

        <!-- Priority Indicator -->
        @php
            $priority = $task->priority ?? 'medium';
            $priorityColors = [
                'high' => 'bg-red-500',
                'medium' => 'bg-yellow-500',
                'low' => 'bg-green-500',
            ];
        @endphp
        <div class="w-2 h-2 rounded-full {{ $priorityColors[$priority] ?? 'bg-gray-400' }}"></div>
    </div>

    <!-- Task Title -->
    <h4 class="{{ $isCompact ? 'text-sm' : 'text-base' }} font-medium text-gray-900 dark:text-white mb-2 line-clamp-2">
        {{ $task->name }}
    </h4>

    @if (!$isCompact)
        <!-- Task Description -->
        @if ($task->description)
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                {{ $task->description }}
            </p>
        @endif
    @endif

    <!-- Task Meta Information -->
    <div class="space-y-2">
        <!-- Assignee and Due Date -->
        <div class="flex items-center justify-between">
            <!-- Assignee -->
            @if ($task->assignedUser)
                <div class="flex items-center space-x-2">
                    <div
                        class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                        {{ substr($task->assignedUser->name, 0, 2) }}
                    </div>
                    @if (!$isCompact)
                        <span class="text-xs text-gray-600 dark:text-gray-400">{{ $task->assignedUser->name }}</span>
                    @endif
                </div>
            @else
                <div class="flex items-center space-x-2">
                    <div class="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center">
                        <x-heroicon-m-user class="w-3 h-3 text-white" />
                    </div>
                    @if (!$isCompact)
                        <span class="text-xs text-gray-500 dark:text-gray-400">Unassigned</span>
                    @endif
                </div>
            @endif

            <!-- Due Date -->
            @if ($task->due_date)
                @php
                    $isOverdue = $task->due_date->isPast() && $task->status !== 'completed';
                    $isDueToday = $task->due_date->isToday();
                    $isDueSoon = $task->due_date->isBetween(now(), now()->addDays(3));
                @endphp
                <span
                    class="text-xs px-2 py-1 rounded-full font-medium
                    {{ $isOverdue
                        ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        : ($isDueToday
                            ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'
                            : ($isDueSoon
                                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                                : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300')) }}">
                    @if ($isOverdue)
                        {{ $task->due_date->diffForHumans() }}
                    @elseif($isDueToday)
                        Today
                    @else
                        {{ $task->due_date->format('M j') }}
                    @endif
                </span>
            @endif
        </div>

        @if (!$isCompact)
            <!-- Progress Bar -->
            @php
                $progress = $task->task_progress ?? 0;
            @endphp
            @if ($progress > 0)
                <div class="space-y-1">
                    <div class="flex items-center justify-between">
                        <span class="text-xs text-gray-500 dark:text-gray-400">Progress</span>
                        <span class="text-xs font-medium text-gray-700 dark:text-gray-300">{{ $progress }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                        <div class="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                            style="width: {{ $progress }}%"></div>
                    </div>
                </div>
            @endif

            <!-- Task Stats -->
            <div class="flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700">
                <div class="flex items-center space-x-3">
                    <!-- Comments -->
                    @if ($task->comments_count > 0)
                        <div class="flex items-center space-x-1">
                            <x-heroicon-m-chat-bubble-left class="w-3 h-3 text-gray-400" />
                            <span class="text-xs text-gray-500 dark:text-gray-400">{{ $task->comments_count }}</span>
                        </div>
                    @endif

                    <!-- Time Logged -->
                    @if ($task->timesheets_sum_hours > 0)
                        <div class="flex items-center space-x-1">
                            <x-heroicon-m-clock class="w-3 h-3 text-gray-400" />
                            <span
                                class="text-xs text-gray-500 dark:text-gray-400">{{ $task->timesheets_sum_hours }}h</span>
                        </div>
                    @endif
                </div>

                <!-- Quick Actions -->
                <div class="flex items-center space-x-1">
                    <!-- View Task -->
                    <a href="{{ \App\Filament\Resources\TaskResource::getUrl('view', ['record' => $task->id]) }}"
                        class="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                        title="View task">
                        <x-heroicon-m-eye class="w-3 h-3" />
                    </a>

                    <!-- Edit Task -->
                    <a href="{{ \App\Filament\Resources\TaskResource::getUrl('edit', ['record' => $task->id]) }}"
                        class="p-1 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors"
                        title="Edit task">
                        <x-heroicon-m-pencil class="w-3 h-3" />
                    </a>
                </div>
            </div>
        @endif
    </div>

    <!-- Task Tags/Labels -->
    @if (!$isCompact && $task->tags)
        <div class="flex flex-wrap gap-1 mt-2">
            @foreach (explode(',', $task->tags) as $tag)
                <span class="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded">
                    {{ trim($tag) }}
                </span>
            @endforeach
        </div>
    @endif

    <!-- Hover Actions Overlay -->
    <div
        class="absolute inset-0 bg-blue-500/5 rounded-lg opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
    </div>
</div>

<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .enhanced-task-card {
        position: relative;
    }

    .enhanced-task-card:hover {
        transform: translateY(-1px);
    }

    .enhanced-task-card.dragging {
        transform: rotate(5deg) scale(1.05);
        z-index: 1000;
    }
</style>
