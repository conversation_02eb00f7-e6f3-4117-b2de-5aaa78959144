<div class="space-y-4">
    <!-- Header Info -->
    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            {{ $keyResult->nama_key_result }}
        </h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Target:</span>
                <span class="ml-2 text-gray-900 dark:text-gray-100">
                    {{ number_format($keyResult->target_value, 2) }}
                    {{ match($keyResult->tipe_metrik) {
                        'percentage' => '%',
                        'currency' => ' Rupiah',
                        default => ($keyResult->unit_measurement ?? '')
                    } }}
                </span>
            </div>
            <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Saat Ini:</span>
                <span class="ml-2 text-gray-900 dark:text-gray-100">
                    {{ number_format($keyResult->current_value, 2) }}
                    {{ match($keyResult->tipe_metrik) {
                        'percentage' => '%',
                        'currency' => ' Rupiah',
                        default => ($keyResult->unit_measurement ?? '')
                    } }}
                </span>
            </div>
            <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Progress:</span>
                <span class="ml-2 text-gray-900 dark:text-gray-100">{{ $keyResult->progress_percentage }}%</span>
            </div>
            <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Status:</span>
                <span class="ml-2 px-2 py-1 rounded text-xs font-medium
                    @if($keyResult->status === 'completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                    @elseif($keyResult->status === 'in_progress') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                    @elseif($keyResult->status === 'at_risk') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                    @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                    @endif">
                    {{ $keyResult->status_label }}
                </span>
            </div>
        </div>
    </div>

    <!-- Activity Timeline -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Riwayat Aktivitas ({{ $activities->count() }} aktivitas)
            </h4>
        </div>

        @if($activities->count() > 0)
        <div class="p-4">
            <div class="flow-root">
                <ul role="list" class="-mb-8">
                    @foreach($activities as $activity)
                    <li>
                        <div class="relative pb-8">
                            @if(!$loop->last)
                            <span class="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-600" aria-hidden="true"></span>
                            @endif
                            
                            <div class="relative flex space-x-3">
                                <div>
                                    <span class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white dark:ring-gray-800
                                        @if($activity->event === 'created') bg-green-500
                                        @elseif($activity->event === 'updated') bg-yellow-500
                                        @elseif($activity->event === 'deleted') bg-red-500
                                        @else bg-gray-500
                                        @endif">
                                        @if($activity->event === 'created')
                                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                                            </svg>
                                        @elseif($activity->event === 'updated')
                                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                            </svg>
                                        @elseif($activity->event === 'deleted')
                                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9zM4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
                                            </svg>
                                        @else
                                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                            </svg>
                                        @endif
                                    </span>
                                </div>
                                
                                <div class="flex-1 min-w-0">
                                    <div>
                                        <div class="text-sm">
                                            <span class="font-medium text-gray-900 dark:text-gray-100">
                                                {{ $activity->causer?->name ?? 'System' }}
                                            </span>
                                            <span class="text-gray-500 dark:text-gray-400">
                                                {{ match($activity->event) {
                                                    'created' => 'membuat',
                                                    'updated' => 'mengupdate',
                                                    'deleted' => 'menghapus',
                                                    default => $activity->event
                                                } }}
                                                key result
                                            </span>
                                        </div>
                                        <p class="mt-0.5 text-sm text-gray-500 dark:text-gray-400">
                                            {{ $activity->created_at->format('d M Y H:i:s') }}
                                            ({{ $activity->created_at->diffForHumans() }})
                                        </p>
                                    </div>
                                    
                                    @if($activity->properties && $activity->properties->count() > 0)
                                    <div class="mt-2 text-sm text-gray-700 dark:text-gray-300">
                                        @php
                                            $properties = $activity->properties->toArray();
                                        @endphp
                                        
                                        @if(isset($properties['attributes']) && isset($properties['old']))
                                        <div class="bg-gray-50 dark:bg-gray-700 rounded p-3 space-y-2">
                                            <div class="font-medium text-gray-900 dark:text-gray-100">Perubahan:</div>
                                            @foreach($properties['attributes'] as $key => $newValue)
                                                @php
                                                    $oldValue = $properties['old'][$key] ?? null;
                                                    $hasChanged = $oldValue != $newValue;
                                                @endphp
                                                
                                                @if($hasChanged)
                                                <div class="text-xs">
                                                    <span class="font-medium capitalize">{{ str_replace('_', ' ', $key) }}:</span>
                                                    <span class="text-red-600 dark:text-red-400">{{ is_null($oldValue) ? 'null' : $oldValue }}</span>
                                                    →
                                                    <span class="text-green-600 dark:text-green-400">{{ is_null($newValue) ? 'null' : $newValue }}</span>
                                                </div>
                                                @endif
                                            @endforeach
                                        </div>
                                        @endif
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </li>
                    @endforeach
                </ul>
            </div>
        </div>
        @else
        <div class="p-8 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Belum ada aktivitas</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Log aktivitas akan muncul ketika ada perubahan pada key result ini.</p>
        </div>
        @endif
    </div>
</div>
