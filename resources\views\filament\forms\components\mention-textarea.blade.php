<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    <div class="relative"
         x-data="{
             init() {
                 Livewire.on('mention-textarea-updated', (event) => {
                     const data = event[0];
                     if (data.name === '{{ $getName() }}') {
                         @this.set('{{ $getStatePath() }}', data.value);
                     }
                 });
             }
         }">
        @livewire('mention-textarea', [
            'value' => $getState(),
            'placeholder' => $getPlaceholder(),
            'rows' => $getRows(),
            'name' => $getName(),
            'label' => '',
            'required' => $isRequired(),
            'projectId' => $getProjectId(),
            'contextType' => $getContextType(),
            'contextId' => $getContextId(),
        ], key($getName() . '-' . $getRecord()?->id))
    </div>
</x-dynamic-component>
