{{-- Filament Geolocation Integration --}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🌍 Initializing Filament Geolocation System');

    // Wait for Filament to fully load
    setTimeout(initializeGeolocation, 1000);

    // Also initialize on Livewire events
    document.addEventListener('livewire:navigated', initializeGeolocation);
    document.addEventListener('livewire:load', initializeGeolocation);
});

function initializeGeolocation() {
    console.log('🔍 Setting up geolocation...');

    // Find the form inputs
    const latInput = document.querySelector('input[name="latitude"]');
    const lngInput = document.querySelector('input[name="longitude"]');
    const statusElement = document.getElementById('geolocation-status');
    const refreshButton = document.getElementById('refresh-location');
    const manualButton = document.getElementById('manual-location');

    console.log('Found elements:', {
        latitude: !!latInput,
        longitude: !!lngInput,
        status: !!statusElement,
        refresh: !!refreshButton,
        manual: !!manualButton
    });

    if (!latInput || !lngInput) {
        console.warn('⚠️ Form inputs not found, retrying...');
        setTimeout(initializeGeolocation, 2000);
        return;
    }

    // Set up the geolocation system
    const geoSystem = new FilamentGeolocation(latInput, lngInput, statusElement);

    // Set up button events
    if (refreshButton) {
        refreshButton.onclick = () => geoSystem.getCurrentLocation();
    }

    if (manualButton) {
        manualButton.onclick = () => geoSystem.showManualInput();
    }

    // Auto-start geolocation
    geoSystem.getCurrentLocation();
}

class FilamentGeolocation {
    constructor(latInput, lngInput, statusElement) {
        this.latInput = latInput;
        this.lngInput = lngInput;
        this.statusElement = statusElement;
        this.retryCount = 0;
        this.maxRetries = 3;

        console.log('✅ FilamentGeolocation initialized');
    }

    getCurrentLocation() {
        console.log('📍 Requesting geolocation...');
        this.updateStatus('loading', 'Mengambil lokasi Anda...');

        if (!navigator.geolocation) {
            console.error('❌ Geolocation not supported');
            this.updateStatus('error', 'Browser Anda tidak mendukung geolocation');
            this.showManualInput();
            return;
        }

        const options = {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 300000 // 5 minutes cache
        };

        navigator.geolocation.getCurrentPosition(
            (position) => this.onLocationSuccess(position),
            (error) => this.onLocationError(error),
            options
        );
    }

    onLocationSuccess(position) {
        const lat = position.coords.latitude.toFixed(6);
        const lng = position.coords.longitude.toFixed(6);
        const accuracy = Math.round(position.coords.accuracy);

        console.log('🎉 Location success:', { lat, lng, accuracy });

        // Update form inputs
        this.latInput.value = lat;
        this.lngInput.value = lng;

        // Trigger Livewire update
        this.latInput.dispatchEvent(new Event('input', { bubbles: true }));
        this.lngInput.dispatchEvent(new Event('input', { bubbles: true }));

        // Update status
        this.updateStatus('success', `Lokasi berhasil dideteksi<br><small>Koordinat: ${lat}, ${lng}<br>Akurasi: ${accuracy}m</small>`);

        this.retryCount = 0;
    }

    onLocationError(error) {
        console.error('❌ Location error:', error);

        let message = '';
        let shouldRetry = false;

        switch(error.code) {
            case error.PERMISSION_DENIED:
                message = 'Izin akses lokasi ditolak. Silakan aktifkan izin lokasi atau gunakan input manual.';
                break;
            case error.POSITION_UNAVAILABLE:
                message = 'Lokasi tidak tersedia. Pastikan GPS aktif atau gunakan input manual.';
                break;
            case error.TIMEOUT:
                message = 'Waktu permintaan lokasi habis.';
                shouldRetry = this.retryCount < this.maxRetries;
                break;
            default:
                message = 'Terjadi kesalahan saat mengambil lokasi.';
                break;
        }

        if (shouldRetry) {
            this.retryCount++;
            console.log(`🔄 Retrying... (${this.retryCount}/${this.maxRetries})`);
            this.updateStatus('warning', `${message} Mencoba lagi...`);
            setTimeout(() => this.getCurrentLocation(), 2000);
        } else {
            this.updateStatus('error', message);
            this.showManualInput();
        }
    }

    updateStatus(type, message) {
        if (!this.statusElement) return;

        const colors = {
            loading: 'bg-blue-50 border-blue-200 text-blue-800',
            success: 'bg-green-50 border-green-200 text-green-800',
            error: 'bg-red-50 border-red-200 text-red-800',
            warning: 'bg-yellow-50 border-yellow-200 text-yellow-800'
        };

        const icons = {
            loading: '<div class="animate-spin mr-2">⏳</div>',
            success: '<div class="mr-2">✅</div>',
            error: '<div class="mr-2">❌</div>',
            warning: '<div class="mr-2">⚠️</div>'
        };

        this.statusElement.className = `p-3 border rounded-lg ${colors[type]}`;
        this.statusElement.innerHTML = `
            <div class="flex items-center">
                ${icons[type]}
                <div>${message}</div>
            </div>
        `;
    }

    showManualInput() {
        console.log('📝 Showing manual input');

        const container = document.getElementById('geolocation-container');
        if (!container) return;

        // Remove existing manual input if any
        const existing = container.querySelector('.manual-input');
        if (existing) existing.remove();

        const manualDiv = document.createElement('div');
        manualDiv.className = 'manual-input p-3 bg-gray-50 border border-gray-200 rounded-lg';
        manualDiv.innerHTML = `
            <div class="space-y-3">
                <div class="text-sm font-medium text-gray-700">Input Koordinat Manual</div>
                <div class="grid grid-cols-2 gap-2">
                    <input type="number" id="manual-lat" placeholder="Latitude" step="any" value="-6.200000"
                           class="px-3 py-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500">
                    <input type="number" id="manual-lng" placeholder="Longitude" step="any" value="106.816666"
                           class="px-3 py-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="flex gap-2 d-none">
                    <button type="button" id="use-manual" class="px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm">
                        ✅ Gunakan Koordinat
                    </button>
                    <button type="button" id="use-jakarta" class="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">
                        🏢 Gunakan Jakarta
                    </button>
                </div>
                <div class="text-xs text-gray-500">Default: Jakarta Pusat (-6.200000, 106.816666)</div>
            </div>
        `;

        container.appendChild(manualDiv);

        // Set up manual input events
        document.getElementById('use-manual').onclick = () => {
            const lat = document.getElementById('manual-lat').value;
            const lng = document.getElementById('manual-lng').value;

            if (lat && lng) {
                this.setCoordinates(lat, lng, 'manual');
                manualDiv.remove();
            } else {
                alert('Silakan masukkan koordinat yang valid');
            }
        };

        document.getElementById('use-jakarta').onclick = () => {
            this.setCoordinates('-6.200000', '106.816666', 'Jakarta');
            manualDiv.remove();
        };
    }

    setCoordinates(lat, lng, source) {
        console.log(`📍 Setting coordinates from ${source}:`, { lat, lng });

        this.latInput.value = lat;
        this.lngInput.value = lng;

        // Trigger Livewire update
        this.latInput.dispatchEvent(new Event('input', { bubbles: true }));
        this.lngInput.dispatchEvent(new Event('input', { bubbles: true }));

        this.updateStatus('success', `Koordinat ${source} berhasil digunakan<br><small>Lat: ${lat}, Lng: ${lng}</small>`);
    }
}

// Make available globally for debugging
window.FilamentGeolocation = FilamentGeolocation;
</script>

{{-- Debug Information --}}
@if(config('app.debug'))
<script>
// Debug helper for development
window.debugGeolocation = function() {
    console.log('=== GEOLOCATION DEBUG ===');
    console.log('Current URL:', window.location.href);
    console.log('Latitude input:', document.querySelector('input[name="latitude"]'));
    console.log('Longitude input:', document.querySelector('input[name="longitude"]'));
    console.log('Status element:', document.getElementById('geolocation-status'));
    console.log('Container:', document.getElementById('geolocation-container'));
    console.log('FilamentGeolocation class:', window.FilamentGeolocation);

    // Test geolocation availability
    console.log('Navigator geolocation:', !!navigator.geolocation);
    if (navigator.geolocation && 'permissions' in navigator) {
        navigator.permissions.query({name: 'geolocation'}).then(result => {
            console.log('Geolocation permission:', result.state);
        });
    }
    console.log('=== END DEBUG ===');
};

// Auto-run debug
setTimeout(window.debugGeolocation, 3000);
</script>
@endif
