<div class="space-y-4">
    <div class="text-center">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            📄 {{ $record->nama_dokumen }}
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $record->jenis_dokumen }}</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Informasi Dokumen -->
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-3">📋 Informasi Dokumen</h4>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Jenis Dokumen:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        {{ $record->jenis_dokumen == 'KTP' ? 'bg-green-100 text-green-800' : 
                           ($record->jenis_dokumen == 'Ijazah' ? 'bg-purple-100 text-purple-800' : 
                           ($record->jenis_dokumen == 'NPWP' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800')) }}">
                        {{ $record->jenis_dokumen }}
                    </span>
                </div>
                
                @if($record->nomor_dokumen)
                <div class="flex justify-between">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Nomor Dokumen:</span>
                    <span class="text-sm font-medium text-blue-800 dark:text-blue-200 font-mono">
                        {{ $record->nomor_dokumen }}
                    </span>
                </div>
                @endif
                
                <div class="flex justify-between">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Status Wajib:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full {{ $record->is_wajib ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800' }}">
                        {{ $record->is_wajib ? 'Wajib' : 'Opsional' }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Status Upload & Verifikasi -->
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-3">📤 Status Upload & Verifikasi</h4>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Status Upload:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full {{ $record->is_uploaded ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ $record->is_uploaded ? '✅ Sudah Upload' : '❌ Belum Upload' }}
                    </span>
                </div>
                
                @if($record->tanggal_upload)
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Tanggal Upload:</span>
                    <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                        {{ \Carbon\Carbon::parse($record->tanggal_upload)->format('d F Y H:i') }}
                    </span>
                </div>
                @endif
                
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Status Verifikasi:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        {{ $record->status_verifikasi == 'Terverifikasi' ? 'bg-green-100 text-green-800' : 
                           ($record->status_verifikasi == 'Ditolak' ? 'bg-red-100 text-red-800' : 
                           ($record->status_verifikasi == 'Menunggu' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800')) }}">
                        {{ $record->status_verifikasi }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Tanggal Expired -->
    @if($record->tanggal_expired)
    <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-orange-800 dark:text-orange-200 mb-2">⏰ Informasi Expired</h4>
        <div class="flex justify-between items-center">
            <span class="text-sm text-orange-700 dark:text-orange-300">Tanggal Expired:</span>
            @php
                $expiredDate = \Carbon\Carbon::parse($record->tanggal_expired);
                $now = \Carbon\Carbon::now();
                $isExpired = $expiredDate->isPast();
                $daysLeft = $isExpired ? 0 : $expiredDate->diffInDays($now);
            @endphp
            <div class="text-right">
                <span class="text-sm font-medium text-orange-800 dark:text-orange-200">
                    {{ $expiredDate->format('d F Y') }}
                </span>
                <div class="text-xs {{ $isExpired ? 'text-red-600' : ($daysLeft <= 30 ? 'text-orange-600' : 'text-green-600') }}">
                    @if($isExpired)
                        ❌ Sudah expired
                    @elseif($daysLeft <= 30)
                        ⚠️ {{ $daysLeft }} hari lagi
                    @else
                        ✅ Masih berlaku
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- File Information -->
    @if($record->is_uploaded)
    <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-green-800 dark:text-green-200 mb-3">📁 Informasi File</h4>
        <div class="space-y-2">
            @if($record->ukuran_file)
            <div class="flex justify-between">
                <span class="text-sm text-green-700 dark:text-green-300">Ukuran File:</span>
                <span class="text-sm font-medium text-green-800 dark:text-green-200">
                    @php
                        $size = $record->ukuran_file;
                        if ($size >= 1073741824) {
                            $formatted = number_format($size / 1073741824, 2) . ' GB';
                        } elseif ($size >= 1048576) {
                            $formatted = number_format($size / 1048576, 2) . ' MB';
                        } elseif ($size >= 1024) {
                            $formatted = number_format($size / 1024, 2) . ' KB';
                        } else {
                            $formatted = $size . ' bytes';
                        }
                    @endphp
                    {{ $formatted }}
                </span>
            </div>
            @endif
            
            @if($record->file_path)
            <div class="flex justify-between">
                <span class="text-sm text-green-700 dark:text-green-300">File Path:</span>
                <span class="text-sm font-medium text-green-800 dark:text-green-200 font-mono">
                    {{ basename($record->file_path) }}
                </span>
            </div>
            @endif
        </div>
    </div>
    @endif

    <!-- Keterangan -->
    @if($record->keterangan)
    <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">📝 Keterangan</h4>
        <p class="text-sm text-yellow-700 dark:text-yellow-300">{{ $record->keterangan }}</p>
    </div>
    @endif

    <!-- Actions -->
    @if($record->is_uploaded && $record->file_path)
    <div class="flex justify-center space-x-3">
        <a href="{{ route('dokumen.download', $record->id) }}" 
           target="_blank"
           class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Download File
        </a>
    </div>
    @endif

    <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Dibuat: {{ $record->created_at->format('d F Y H:i') }}</span>
            @if($record->updated_at != $record->created_at)
                <span>Diupdate: {{ $record->updated_at->format('d F Y H:i') }}</span>
            @endif
        </div>
    </div>
</div>
