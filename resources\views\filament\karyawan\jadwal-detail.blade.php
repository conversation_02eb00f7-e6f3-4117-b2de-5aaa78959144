<div class="space-y-4">
    <div class="text-center">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            📅 {{ \Carbon\Carbon::parse($record->tanggal_jadwal)->locale('id')->isoFormat('dddd, D MMMM Y') }}
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400">Detail Jadwal Kerja</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Informasi Shift -->
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-3">⏰ Informasi Shift</h4>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Nama Shift:</span>
                    <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        {{ $record->shift->nama_shift ?? 'Tidak ada shift' }}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Waktu Masuk:</span>
                    <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        {{ $record->waktu_masuk ? \Carbon\Carbon::parse($record->waktu_masuk)->format('H:i') : '-' }}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Waktu Keluar:</span>
                    <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        {{ $record->waktu_keluar ? \Carbon\Carbon::parse($record->waktu_keluar)->format('H:i') : '-' }}
                    </span>
                </div>
                @if($record->shift)
                <div class="flex justify-between">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Toleransi Terlambat:</span>
                    <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        {{ $record->shift->toleransi_keterlambatan ?? 0 }} menit
                    </span>
                </div>
                @endif
            </div>
        </div>

        <!-- Status & Approval -->
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-3">📋 Status & Persetujuan</h4>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        @if($record->status == 'Hadir') bg-green-100 text-green-800
                        @elseif($record->status == 'Tidak Hadir') bg-red-100 text-red-800
                        @elseif($record->status == 'Terlambat') bg-yellow-100 text-yellow-800
                        @elseif($record->status == 'Izin') bg-blue-100 text-blue-800
                        @elseif($record->status == 'Sakit') bg-purple-100 text-purple-800
                        @elseif($record->status == 'Cuti') bg-indigo-100 text-indigo-800
                        @elseif($record->status == 'Libur') bg-orange-100 text-orange-800
                        @else bg-gray-100 text-gray-800 @endif">
                        {{ $record->status ?? 'Belum Ditentukan' }}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Disetujui:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full {{ $record->is_approved ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                        {{ $record->is_approved ? '✅ Ya' : '⏳ Menunggu' }}
                    </span>
                </div>
                @if($record->supervisor)
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Supervisor:</span>
                    <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                        {{ $record->supervisor->name }}
                    </span>
                </div>
                @endif
            </div>
        </div>
    </div>

    @if($record->keterangan)
    <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">📝 Keterangan</h4>
        <p class="text-sm text-yellow-700 dark:text-yellow-300">{{ $record->keterangan }}</p>
    </div>
    @endif

    <!-- Durasi Kerja -->
    @if($record->waktu_masuk && $record->waktu_keluar)
    <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-green-800 dark:text-green-200 mb-2">⏱️ Durasi Kerja</h4>
        <div class="text-center">
            @php
                $masuk = \Carbon\Carbon::parse($record->waktu_masuk);
                $keluar = \Carbon\Carbon::parse($record->waktu_keluar);
                
                // Handle overnight shift
                if ($keluar->lt($masuk)) {
                    $keluar->addDay();
                }
                
                $durasi = $masuk->diff($keluar);
                $jam = $durasi->h;
                $menit = $durasi->i;
            @endphp
            <span class="text-2xl font-bold text-green-800 dark:text-green-200">
                {{ $jam }} jam {{ $menit }} menit
            </span>
            <p class="text-sm text-green-600 dark:text-green-400 mt-1">
                {{ $masuk->format('H:i') }} - {{ \Carbon\Carbon::parse($record->waktu_keluar)->format('H:i') }}
            </p>
        </div>
    </div>
    @endif

    <!-- Informasi Tambahan -->
    <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="grid grid-cols-2 gap-4 text-xs text-gray-500 dark:text-gray-400">
            <div>
                <span class="font-medium">Dibuat:</span><br>
                {{ $record->created_at->format('d F Y H:i') }}
            </div>
            @if($record->updated_at != $record->created_at)
            <div>
                <span class="font-medium">Diupdate:</span><br>
                {{ $record->updated_at->format('d F Y H:i') }}
            </div>
            @endif
        </div>
    </div>
</div>
