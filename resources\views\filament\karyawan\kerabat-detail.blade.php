<div class="space-y-4">
    <div class="text-center">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            👤 {{ $record->nama_kerabat }}
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $record->hubungan }}</p>
        @if($record->is_primary)
        <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800 mt-2">
            ⭐ Kontak Utama
        </span>
        @endif
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Informasi Kontak -->
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-3">📞 Informasi Kontak</h4>
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-blue-700 dark:text-blue-300"><PERSON><PERSON>gkap:</span>
                    <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        {{ $record->nama_kerabat }}
                    </span>
                </div>
                
                <div class="flex justify-between items-center">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Hubungan:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        {{ $record->hubungan == 'Ayah' ? 'bg-blue-100 text-blue-800' : 
                           ($record->hubungan == 'Ibu' ? 'bg-pink-100 text-pink-800' : 
                           ($record->hubungan == 'Suami' || $record->hubungan == 'Istri' ? 'bg-purple-100 text-purple-800' : 
                           ($record->hubungan == 'Anak' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'))) }}">
                        {{ $record->hubungan }}
                    </span>
                </div>
                
                <div class="flex justify-between items-center">
                    <span class="text-sm text-blue-700 dark:text-blue-300">No. Telepon:</span>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm font-medium text-blue-800 dark:text-blue-200 font-mono">
                            {{ $record->no_telepon }}
                        </span>
                        <button onclick="navigator.clipboard.writeText('{{ $record->no_telepon }}')" 
                                class="text-blue-600 hover:text-blue-800 transition-colors">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informasi Tambahan -->
        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <h4 class="font-semibold text-green-800 dark:text-green-200 mb-3">ℹ️ Informasi Tambahan</h4>
            <div class="space-y-3">
                @if($record->pekerjaan)
                <div class="flex justify-between items-center">
                    <span class="text-sm text-green-700 dark:text-green-300">Pekerjaan:</span>
                    <span class="text-sm font-medium text-green-800 dark:text-green-200">
                        {{ $record->pekerjaan }}
                    </span>
                </div>
                @endif
                
                <div class="flex justify-between items-center">
                    <span class="text-sm text-green-700 dark:text-green-300">Status:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        {{ $record->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ $record->is_active ? '✅ Aktif' : '❌ Tidak Aktif' }}
                    </span>
                </div>
                
                <div class="flex justify-between items-center">
                    <span class="text-sm text-green-700 dark:text-green-300">Kontak Utama:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        {{ $record->is_primary ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800' }}">
                        {{ $record->is_primary ? '⭐ Ya' : '○ Tidak' }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Alamat -->
    @if($record->alamat)
    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">🏠 Alamat</h4>
        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $record->alamat }}</p>
    </div>
    @endif

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-3">🚀 Aksi Cepat</h4>
        <div class="flex flex-wrap gap-3">
            <!-- Call Button -->
            <a href="tel:{{ $record->no_telepon }}" 
               class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                Telepon
            </a>

            <!-- WhatsApp Button -->
            <a href="https://wa.me/{{ preg_replace('/[^0-9]/', '', $record->no_telepon) }}" 
               target="_blank"
               class="inline-flex items-center px-4 py-2 bg-green-500 text-white text-sm font-medium rounded-lg hover:bg-green-600 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                </svg>
                WhatsApp
            </a>

            <!-- SMS Button -->
            <a href="sms:{{ $record->no_telepon }}" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                SMS
            </a>
        </div>
    </div>

    <!-- Keterangan -->
    @if($record->keterangan)
    <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">📝 Keterangan</h4>
        <p class="text-sm text-yellow-700 dark:text-yellow-300">{{ $record->keterangan }}</p>
    </div>
    @endif

    <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Dibuat: {{ $record->created_at->format('d F Y H:i') }}</span>
            @if($record->updated_at != $record->created_at)
                <span>Diupdate: {{ $record->updated_at->format('d F Y H:i') }}</span>
            @endif
        </div>
    </div>
</div>
