<div class="space-y-4">
    <div class="grid grid-cols-2 gap-4">
        <div>
            <h4 class="font-semibold text-gray-900 dark:text-white">Informasi Kontrak</h4>
            <div class="mt-2 space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400"><PERSON><PERSON>:</span>
                    <span class="text-sm font-medium">{{ $record->jenis_kontrak }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">No. Kontrak:</span>
                    <span class="text-sm font-medium">{{ $record->no_kontrak ?? '-' }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full {{ $record->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ $record->is_active ? 'Aktif' : 'Tidak Aktif' }}
                    </span>
                </div>
            </div>
        </div>

        <div>
            <h4 class="font-semibold text-gray-900 dark:text-white">Periode Kontrak</h4>
            <div class="mt-2 space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Tanggal Mulai:</span>
                    <span class="text-sm font-medium">{{ $record->tgl_mulai ? \Carbon\Carbon::parse($record->tgl_mulai)->format('d F Y') : '-' }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Tanggal Selesai:</span>
                    <span class="text-sm font-medium">{{ $record->tgl_selesai ? \Carbon\Carbon::parse($record->tgl_selesai)->format('d F Y') : '-' }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Mulai Kerja:</span>
                    <span class="text-sm font-medium">{{ $record->tanggal_mulai_kerja ? \Carbon\Carbon::parse($record->tanggal_mulai_kerja)->format('d F Y') : '-' }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Durasi:</span>
                    <span class="text-sm font-medium">
                        @if($record->tgl_mulai && $record->tgl_selesai)
                            {{ \Carbon\Carbon::parse($record->tgl_mulai)->diffInMonths(\Carbon\Carbon::parse($record->tgl_selesai)) }} bulan
                        @else
                            -
                        @endif
                    </span>
                </div>
            </div>
        </div>
    </div>

    @if($record->keterangan)
    <div>
        <h4 class="font-semibold text-gray-900 dark:text-white">Keterangan</h4>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">{{ $record->keterangan }}</p>
    </div>
    @endif

    <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Dibuat: {{ $record->created_at->format('d F Y H:i') }}</span>
            @if($record->updated_at != $record->created_at)
                <span>Diupdate: {{ $record->updated_at->format('d F Y H:i') }}</span>
            @endif
        </div>
    </div>
</div>
