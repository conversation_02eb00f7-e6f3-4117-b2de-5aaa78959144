<div class="space-y-6">
    <div class="text-center">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            📊 Penilaian KPI - {{ $record->periode }}
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $record->karyawan->nama_lengkap ?? 'Karyawan' }}</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Target vs Realisasi -->
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-3">🎯 Target vs Realisasi</h4>
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Target KPI:</span>
                    <span class="text-lg font-bold text-blue-800 dark:text-blue-200">
                        {{ number_format($record->target_kpi, 1) }}%
                    </span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Realisasi KPI:</span>
                    <span class="text-lg font-bold text-blue-800 dark:text-blue-200">
                        {{ number_format($record->realisasi_kpi, 1) }}%
                    </span>
                </div>
                
                <!-- Progress Bar -->
                <div class="w-full bg-gray-200 rounded-full h-3 dark:bg-gray-700">
                    @php
                        $percentage = $record->target_kpi > 0 ? ($record->realisasi_kpi / $record->target_kpi) * 100 : 0;
                        $percentage = min($percentage, 100); // Cap at 100%
                        $color = $percentage >= 100 ? 'bg-green-500' : ($percentage >= 80 ? 'bg-yellow-500' : 'bg-red-500');
                    @endphp
                    <div class="{{ $color }} h-3 rounded-full transition-all duration-300" style="width: {{ $percentage }}%"></div>
                </div>
                
                <div class="text-center">
                    <span class="inline-flex items-center px-3 py-1 text-sm font-medium rounded-full 
                        {{ $percentage >= 100 ? 'bg-green-100 text-green-800' : 
                           ($percentage >= 80 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                        {{ number_format($percentage, 1) }}% Tercapai
                    </span>
                </div>
            </div>
        </div>

        <!-- Nilai & Status -->
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-3">📋 Penilaian & Status</h4>
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Nilai Akhir:</span>
                    <span class="inline-flex items-center px-3 py-1 text-lg font-bold rounded-full 
                        {{ $record->nilai_akhir == 'A' ? 'bg-green-100 text-green-800' : 
                           ($record->nilai_akhir == 'B' ? 'bg-blue-100 text-blue-800' : 
                           ($record->nilai_akhir == 'C' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800')) }}">
                        {{ $record->nilai_akhir }}
                    </span>
                </div>
                
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Status Penilaian:</span>
                    <span class="inline-flex items-center px-2 py-1 text-sm font-medium rounded-full 
                        {{ $record->status_penilaian == 'Selesai' ? 'bg-green-100 text-green-800' : 
                           ($record->status_penilaian == 'Proses' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                        {{ $record->status_penilaian }}
                    </span>
                </div>
                
                @if($record->penilai)
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Penilai:</span>
                    <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                        {{ $record->penilai->name }}
                    </span>
                </div>
                @endif
                
                @if($record->tanggal_penilaian)
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Tanggal Penilaian:</span>
                    <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                        {{ \Carbon\Carbon::parse($record->tanggal_penilaian)->format('d F Y') }}
                    </span>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Keterangan -->
    @if($record->keterangan)
    <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">📝 Keterangan</h4>
        <p class="text-sm text-yellow-700 dark:text-yellow-300">{{ $record->keterangan }}</p>
    </div>
    @endif

    <!-- Kategori Penilaian -->
    @if($record->kategori_penilaian)
    <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-purple-800 dark:text-purple-200 mb-3">📊 Detail Kategori Penilaian</h4>
        @php
            $kategori = json_decode($record->kategori_penilaian, true);
        @endphp
        @if($kategori && is_array($kategori))
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                @foreach($kategori as $key => $value)
                <div class="flex justify-between items-center p-2 bg-white dark:bg-gray-700 rounded">
                    <span class="text-sm text-purple-700 dark:text-purple-300">{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                    <span class="text-sm font-medium text-purple-800 dark:text-purple-200">{{ $value }}</span>
                </div>
                @endforeach
            </div>
        @endif
    </div>
    @endif

    <!-- Grade Scale -->
    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-3">📏 Skala Penilaian</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
            <div class="text-center p-2 bg-green-100 text-green-800 rounded">
                <div class="font-bold">A</div>
                <div>≥ 90%</div>
                <div>Sangat Baik</div>
            </div>
            <div class="text-center p-2 bg-blue-100 text-blue-800 rounded">
                <div class="font-bold">B</div>
                <div>80-89%</div>
                <div>Baik</div>
            </div>
            <div class="text-center p-2 bg-yellow-100 text-yellow-800 rounded">
                <div class="font-bold">C</div>
                <div>70-79%</div>
                <div>Cukup</div>
            </div>
            <div class="text-center p-2 bg-red-100 text-red-800 rounded">
                <div class="font-bold">D</div>
                <div>< 70%</div>
                <div>Kurang</div>
            </div>
        </div>
    </div>

    <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Dibuat: {{ $record->created_at->format('d F Y H:i') }}</span>
            @if($record->updated_at != $record->created_at)
                <span>Diupdate: {{ $record->updated_at->format('d F Y H:i') }}</span>
            @endif
        </div>
    </div>
</div>
