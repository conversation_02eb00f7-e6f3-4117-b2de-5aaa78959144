<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>{{ config('app.name', 'Laravel') }} - Karyawan Panel</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/js/karyawan-geolocation.js'])
    
    <!-- Filament Styles -->
    @filamentStyles
    
    <!-- Additional Styles -->
    <style>
        /* Custom styles for karyawan panel */
        .geolocation-loading {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
    </style>
</head>
<body class="font-sans antialiased">
    <div id="app">
        {{ $slot }}
    </div>
    
    <!-- Filament Scripts -->
    @filamentScripts
    
    <!-- Geolocation Auto-initialization -->
    <script>
        // Auto-initialize geolocation for attendance pages
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌍 Karyawan panel loaded');
            
            // Check if we're on an attendance page
            if (window.location.href.includes('/absensis/create') || 
                window.location.href.includes('/attendance/create')) {
                console.log('📍 Attendance page detected, initializing geolocation');
                
                // Initialize after a short delay to ensure DOM is ready
                setTimeout(function() {
                    if (typeof initializeGeolocation === 'function') {
                        initializeGeolocation();
                    }
                }, 1500);
            }
        });
        
        // Also initialize on Livewire navigation
        document.addEventListener('livewire:navigated', function() {
            if (window.location.href.includes('/absensis/create') || 
                window.location.href.includes('/attendance/create')) {
                console.log('📍 Livewire navigated to attendance page');
                setTimeout(function() {
                    if (typeof initializeGeolocation === 'function') {
                        initializeGeolocation();
                    }
                }, 500);
            }
        });
    </script>
</body>
</html>
