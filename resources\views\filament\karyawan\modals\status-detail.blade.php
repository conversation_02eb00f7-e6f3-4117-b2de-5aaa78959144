<div class="space-y-6">
    <!-- Header <PERSON> -->
    <div class="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div class="flex-shrink-0">
            @if($karyawan->foto_profil)
                <img src="{{ Storage::url($karyawan->foto_profil) }}" 
                     alt="{{ $karyawan->nama_lengkap }}" 
                     class="w-16 h-16 rounded-full object-cover">
            @else
                <div class="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            @endif
        </div>
        <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $karyawan->nama_lengkap }}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $karyawan->nip ?? 'NIP tidak tersedia' }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ $karyawan->jabatan->nama_jabatan ?? 'Jabatan tidak tersedia' }}
                @if($karyawan->divisi)
                    - {{ $karyawan->divisi->nama_divisi }}
                @endif
            </p>
        </div>
    </div>

    <!-- Status Hari Ini -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Status Utama -->
        <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Status Hari Ini</h4>
            <div class="flex items-center space-x-2">
                @php
                    $status = $statusInfo['status'];
                    $statusConfig = [
                        'hadir' => ['color' => 'green', 'icon' => '✅', 'label' => 'Hadir'],
                        'terlambat' => ['color' => 'yellow', 'icon' => '⏰', 'label' => 'Terlambat'],
                        'alpha' => ['color' => 'red', 'icon' => '❌', 'label' => 'Alpha'],
                        'sakit' => ['color' => 'red', 'icon' => '🤒', 'label' => 'Sakit'],
                        'cuti' => ['color' => 'blue', 'icon' => '🏖️', 'label' => 'Cuti'],
                        'izin' => ['color' => 'blue', 'icon' => '📝', 'label' => 'Izin'],
                        'libur' => ['color' => 'gray', 'icon' => '🌙', 'label' => 'Libur'],
                        'tidak_ada_jadwal' => ['color' => 'gray', 'icon' => '➖', 'label' => 'Tidak Ada Jadwal'],
                    ];
                    $config = $statusConfig[$status] ?? ['color' => 'gray', 'icon' => '❓', 'label' => 'Tidak Diketahui'];
                @endphp
                
                <span class="text-2xl">{{ $config['icon'] }}</span>
                <span class="px-3 py-1 rounded-full text-sm font-medium
                    @if($config['color'] === 'green') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                    @elseif($config['color'] === 'yellow') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                    @elseif($config['color'] === 'red') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                    @elseif($config['color'] === 'blue') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                    @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                    @endif">
                    {{ $config['label'] }}
                </span>
            </div>
            @if($statusInfo['keterangan'])
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">{{ $statusInfo['keterangan'] }}</p>
            @endif
        </div>

        <!-- Kontak -->
        <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Kontak</h4>
            @if($karyawan->no_hp)
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">HP:</span>
                    <a href="tel:{{ $karyawan->no_hp }}" 
                       class="text-blue-600 dark:text-blue-400 hover:underline">
                        {{ $karyawan->no_hp }}
                    </a>
                </div>
            @endif
            @if($karyawan->email)
                <div class="flex items-center space-x-2 mt-1">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Email:</span>
                    <a href="mailto:{{ $karyawan->email }}" 
                       class="text-blue-600 dark:text-blue-400 hover:underline">
                        {{ $karyawan->email }}
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Detail Informasi -->
    <div class="space-y-4">
        <!-- Jadwal Hari Ini -->
        @if($statusInfo['jadwal'])
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Jadwal Hari Ini</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Shift:</span>
                        <span class="ml-2 font-medium">{{ $statusInfo['jadwal']->shift->nama_shift ?? 'Tidak tersedia' }}</span>
                    </div>
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Waktu:</span>
                        <span class="ml-2 font-medium">
                            {{ $statusInfo['jadwal']->waktu_masuk ? \Carbon\Carbon::parse($statusInfo['jadwal']->waktu_masuk)->format('H:i') : '-' }} - 
                            {{ $statusInfo['jadwal']->waktu_keluar ? \Carbon\Carbon::parse($statusInfo['jadwal']->waktu_keluar)->format('H:i') : '-' }}
                        </span>
                    </div>
                </div>
            </div>
        @else
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Jadwal Hari Ini</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">Tidak ada jadwal kerja hari ini</p>
            </div>
        @endif

        <!-- Absensi Hari Ini -->
        @if($statusInfo['absensi'])
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Absensi Hari Ini</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Waktu Masuk:</span>
                        <span class="ml-2 font-medium">
                            {{ $statusInfo['absensi']->waktu_masuk ? \Carbon\Carbon::parse($statusInfo['absensi']->waktu_masuk)->format('H:i:s') : 'Belum absen' }}
                        </span>
                    </div>
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Waktu Keluar:</span>
                        <span class="ml-2 font-medium">
                            {{ $statusInfo['absensi']->waktu_keluar ? \Carbon\Carbon::parse($statusInfo['absensi']->waktu_keluar)->format('H:i:s') : 'Belum absen' }}
                        </span>
                    </div>
                </div>
                @if($statusInfo['absensi']->keterangan)
                    <div class="mt-2">
                        <span class="text-gray-600 dark:text-gray-400">Keterangan:</span>
                        <span class="ml-2">{{ $statusInfo['absensi']->keterangan }}</span>
                    </div>
                @endif
            </div>
        @endif

        <!-- Cuti/Izin/Sakit -->
        @if($statusInfo['cutiIzin'])
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h4 class="font-semibold text-gray-900 dark:text-white mb-2">
                    {{ ucfirst($statusInfo['cutiIzin']->jenis_permohonan) }} yang Sedang Berlangsung
                </h4>
                <div class="space-y-2 text-sm">
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Periode:</span>
                        <span class="ml-2 font-medium">
                            {{ $statusInfo['cutiIzin']->tanggal_mulai->format('d M Y') }} - 
                            {{ $statusInfo['cutiIzin']->tanggal_selesai->format('d M Y') }}
                        </span>
                        <span class="ml-2 text-gray-500">({{ $statusInfo['cutiIzin']->jumlah_hari }} hari)</span>
                    </div>
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Alasan:</span>
                        <span class="ml-2">{{ $statusInfo['cutiIzin']->alasan }}</span>
                    </div>
                    @if($statusInfo['cutiIzin']->keterangan_tambahan)
                        <div>
                            <span class="text-gray-600 dark:text-gray-400">Keterangan Tambahan:</span>
                            <span class="ml-2">{{ $statusInfo['cutiIzin']->keterangan_tambahan }}</span>
                        </div>
                    @endif
                </div>
            </div>
        @endif
    </div>

    <!-- Footer Info -->
    <div class="text-xs text-gray-500 dark:text-gray-400 text-center pt-4 border-t border-gray-200 dark:border-gray-700">
        Data diperbarui secara real-time • {{ \Carbon\Carbon::now()->format('d M Y H:i:s') }}
    </div>
</div>
