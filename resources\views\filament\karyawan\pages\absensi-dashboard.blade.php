<x-filament-panels::page>
    <div class="absensi-dashboard-container">
        <div class="space-y-6">
            {{-- Header Section --}}
            <div class="bg-white border border-gray-100 shadow-md dark:bg-gray-900 rounded-3xl dark:border-gray-700">
                {{-- Header --}}
                <div class="px-8 py-6 bg-blue-300 border-b border-gray-100 rounded-t-3xl bg-gradient-to-br from-blue-300 via-blue-300 to-indigo-400 dark:from-gray-800 dark:to-gray-700 dark:border-gray-600">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center justify-center w-16 h-16 bg-white shadow-lg dark:bg-gray-700 rounded-2xl">
                                <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Dashboard Absensi</h1>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Monitoring kehadiran dan absensi Anda</p>
                            </div>
                        </div>

                        {{-- Current Status Info --}}
                        <div class="text-right">
                            @php
                                $currentDate = now()->format('d M Y');
                                $currentTime = now()->format('H:i');
                                $dayName = now()->locale('id')->dayName;
                            @endphp
                            <div class="px-4 py-3 text-right bg-white border border-gray-100 shadow-sm dark:bg-gray-700 dark:border-gray-600 rounded-xl">
                                <div class="text-xs font-semibold tracking-wide uppercase text-blue-600 dark:text-blue-400">
                                    {{ $dayName }}
                                </div>
                                <div class="text-lg font-bold text-gray-900 dark:text-white">
                                    {{ $currentDate }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    Pukul {{ $currentTime }} WIB
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Quick Actions --}}
                <div class="p-6 bg-gradient-to-br from-gray-50 to-white dark:from-gray-800 dark:to-gray-900">
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                        {{-- Absensi Hari Ini --}}
                        <a href="{{ route('filament.karyawan.resources.absensis.create') }}"
                           class="flex items-center p-4 transition-all duration-200 bg-white border border-gray-200 rounded-xl hover:shadow-md hover:border-blue-300 dark:bg-gray-800 dark:border-gray-700 dark:hover:border-blue-500">
                            <div class="flex items-center justify-center w-12 h-12 mr-4 bg-green-100 rounded-lg dark:bg-green-900">
                                <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 dark:text-white">Absensi Hari Ini</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Catat kehadiran Anda</p>
                            </div>
                        </a>

                        {{-- Riwayat Absensi --}}
                        <a href="{{ route('filament.karyawan.resources.absensis.index') }}"
                           class="flex items-center p-4 transition-all duration-200 bg-white border border-gray-200 rounded-xl hover:shadow-md hover:border-blue-300 dark:bg-gray-800 dark:border-gray-700 dark:hover:border-blue-500">
                            <div class="flex items-center justify-center w-12 h-12 mr-4 bg-orange-100 rounded-lg dark:bg-orange-900">
                                <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 dark:text-white">Riwayat Absensi</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Lihat riwayat kehadiran</p>
                            </div>
                        </a>

                        {{-- Jadwal Saya --}}
                        <a href="{{ route('filament.karyawan.resources.schedules.index') }}"
                           class="flex items-center p-4 transition-all duration-200 bg-white border border-gray-200 rounded-xl hover:shadow-md hover:border-blue-300 dark:bg-gray-800 dark:border-gray-700 dark:hover:border-blue-500">
                            <div class="flex items-center justify-center w-12 h-12 mr-4 bg-purple-100 rounded-lg dark:bg-purple-900">
                                <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 dark:text-white">Jadwal Saya</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Lihat jadwal kerja</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <div class="space-y-6">
                @foreach ($this->getWidgets() as $widget)
                    @livewire($widget, ['lazy' => false], key($widget))
                @endforeach
            </div>
        </div>
    </div>
</x-filament-panels::page>
