<x-filament-panels::page>
    <div class="karyawan-dashboard-container">
        <div class="space-y-6">
            {{-- Welcome Section --}}
            <div class="bg-white border border-gray-100 shadow-md dark:bg-gray-900 rounded-3xl dark:border-gray-700">
                {{-- Header --}}
                <div class="px-8 py-6 bg-amber-300 border-b border-gray-100 rounded-t-3xl bg-gradient-to-br from-amber-300 via-amber-300 to-orange-400 dark:from-gray-800 dark:to-gray-700 dark:border-gray-600">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center justify-center w-16 h-16 bg-white shadow-lg dark:bg-gray-700 rounded-2xl">
                                <svg class="w-8 h-8 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Dashboard Karyawan</h1>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Selamat datang di sistem manajemen karyawan</p>
                            </div>
                        </div>

                        {{-- Current Date/Time Info --}}
                        <div class="text-right">
                            @php
                                $currentDate = now()->format('d M Y');
                                $currentTime = now()->format('H:i');
                            @endphp
                            <div class="px-4 py-3 text-right bg-white border border-gray-100 shadow-sm dark:bg-gray-700 dark:border-gray-600 rounded-xl">
                                <div class="text-xs font-semibold tracking-wide uppercase text-amber-600 dark:text-amber-400">
                                    Hari Ini
                                </div>
                                <div class="text-lg font-bold text-gray-900 dark:text-white">
                                    {{ $currentDate }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    Pukul {{ $currentTime }} WIB
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Quick Actions --}}
                <div class="p-6 bg-gradient-to-br from-gray-50 to-white dark:from-gray-800 dark:to-gray-900">
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                        {{-- Absensi Quick Action --}}
                        <a href="{{ route('filament.karyawan.pages.absensi-dashboard') }}"
                           class="flex items-center p-4 transition-all duration-200 bg-white border border-gray-200 rounded-xl hover:shadow-md hover:border-amber-300 dark:bg-gray-800 dark:border-gray-700 dark:hover:border-amber-500">
                            <div class="flex items-center justify-center w-12 h-12 mr-4 bg-blue-100 rounded-lg dark:bg-blue-900">
                                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 dark:text-white">Absensi</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Kelola kehadiran Anda</p>
                            </div>
                        </a>

                        {{-- Payroll Quick Action --}}
                        <a href="{{ route('filament.karyawan.pages.payroll-dashboard') }}"
                           class="flex items-center p-4 transition-all duration-200 bg-white border border-gray-200 rounded-xl hover:shadow-md hover:border-amber-300 dark:bg-gray-800 dark:border-gray-700 dark:hover:border-amber-500">
                            <div class="flex items-center justify-center w-12 h-12 mr-4 bg-green-100 rounded-lg dark:bg-green-900">
                                <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 dark:text-white">Payroll</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Lihat slip gaji Anda</p>
                            </div>
                        </a>

                        {{-- Profile Quick Action --}}
                        <a href="{{ route('filament.karyawan.pages.profile') }}"
                           class="flex items-center p-4 transition-all duration-200 bg-white border border-gray-200 rounded-xl hover:shadow-md hover:border-amber-300 dark:bg-gray-800 dark:border-gray-700 dark:hover:border-amber-500">
                            <div class="flex items-center justify-center w-12 h-12 mr-4 bg-purple-100 rounded-lg dark:bg-purple-900">
                                <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 dark:text-white">Profil</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Kelola profil Anda</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            {{-- Widgets Section --}}
            <div class="space-y-6">
                @foreach ($this->getWidgets() as $widget)
                    @livewire($widget, ['lazy' => false], key($widget))
                @endforeach
            </div>
        </div>
    </div>
</x-filament-panels::page>
