<div class="space-y-4">
    <div class="text-center">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            ⚠️ Detail Pelanggaran
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ \Carbon\Carbon::parse($record->tanggal_pelanggaran)->format('d F Y') }}
        </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Informasi Pelanggaran -->
        <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
            <h4 class="font-semibold text-red-800 dark:text-red-200 mb-3">📋 Informasi Pelanggaran</h4>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-red-700 dark:text-red-300"><PERSON><PERSON>:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        {{ $record->jenis_pelanggaran == 'Keterlambatan' ? 'bg-yellow-100 text-yellow-800' : 
                           ($record->jenis_pelanggaran == 'Tidak Masuk' ? 'bg-red-100 text-red-800' : 
                           ($record->jenis_pelanggaran == 'Pelanggaran SOP' ? 'bg-orange-100 text-orange-800' : 'bg-gray-100 text-gray-800')) }}">
                        {{ $record->jenis_pelanggaran }}
                    </span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-sm text-red-700 dark:text-red-300">Tingkat:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        {{ $record->tingkat_pelanggaran == 'Ringan' ? 'bg-green-100 text-green-800' : 
                           ($record->tingkat_pelanggaran == 'Sedang' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                        {{ $record->tingkat_pelanggaran }}
                    </span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-sm text-red-700 dark:text-red-300">Tanggal:</span>
                    <span class="text-sm font-medium text-red-800 dark:text-red-200">
                        {{ \Carbon\Carbon::parse($record->tanggal_pelanggaran)->format('d F Y') }}
                    </span>
                </div>
                
                @if($record->pelapor)
                <div class="flex justify-between">
                    <span class="text-sm text-red-700 dark:text-red-300">Pelapor:</span>
                    <span class="text-sm font-medium text-red-800 dark:text-red-200">
                        {{ $record->pelapor->name }}
                    </span>
                </div>
                @endif
            </div>
        </div>

        <!-- Sanksi -->
        <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
            <h4 class="font-semibold text-orange-800 dark:text-orange-200 mb-3">⚖️ Sanksi</h4>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-orange-700 dark:text-orange-300">Jenis Sanksi:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        {{ $record->sanksi == 'Teguran Lisan' ? 'bg-blue-100 text-blue-800' : 
                           ($record->sanksi == 'Teguran Tertulis' ? 'bg-yellow-100 text-yellow-800' : 
                           ($record->sanksi == 'Skorsing' ? 'bg-orange-100 text-orange-800' : 'bg-red-100 text-red-800')) }}">
                        {{ $record->sanksi }}
                    </span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-sm text-orange-700 dark:text-orange-300">Status Sanksi:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        {{ $record->status_sanksi == 'Selesai' ? 'bg-green-100 text-green-800' : 
                           ($record->status_sanksi == 'Berlangsung' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                        {{ $record->status_sanksi }}
                    </span>
                </div>
                
                @if($record->tanggal_sanksi)
                <div class="flex justify-between">
                    <span class="text-sm text-orange-700 dark:text-orange-300">Tanggal Sanksi:</span>
                    <span class="text-sm font-medium text-orange-800 dark:text-orange-200">
                        {{ \Carbon\Carbon::parse($record->tanggal_sanksi)->format('d F Y') }}
                    </span>
                </div>
                @endif
                
                @if($record->durasi_sanksi)
                <div class="flex justify-between">
                    <span class="text-sm text-orange-700 dark:text-orange-300">Durasi Sanksi:</span>
                    <span class="text-sm font-medium text-orange-800 dark:text-orange-200">
                        {{ $record->durasi_sanksi }} hari
                    </span>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Deskripsi Pelanggaran -->
    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">📝 Deskripsi Pelanggaran</h4>
        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $record->deskripsi_pelanggaran }}</p>
    </div>

    <!-- Keterangan Tambahan -->
    @if($record->keterangan)
    <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">💬 Keterangan Tambahan</h4>
        <p class="text-sm text-yellow-700 dark:text-yellow-300">{{ $record->keterangan }}</p>
    </div>
    @endif

    <!-- Status Aktif -->
    <div class="text-center">
        <span class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-full 
            {{ $record->is_active ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800' }}">
            @if($record->is_active)
                🔴 Sanksi Masih Berlaku
            @else
                ✅ Sanksi Sudah Selesai
            @endif
        </span>
    </div>

    <!-- Timeline -->
    @if($record->tanggal_sanksi)
    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-3">📅 Timeline</h4>
        <div class="space-y-2">
            <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <div class="text-sm">
                    <span class="font-medium">{{ \Carbon\Carbon::parse($record->tanggal_pelanggaran)->format('d M Y') }}</span>
                    - Pelanggaran terjadi
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                <div class="text-sm">
                    <span class="font-medium">{{ \Carbon\Carbon::parse($record->tanggal_sanksi)->format('d M Y') }}</span>
                    - Sanksi diberikan
                </div>
            </div>
            @if($record->durasi_sanksi && $record->tanggal_sanksi)
            <div class="flex items-center space-x-3">
                @php
                    $endDate = \Carbon\Carbon::parse($record->tanggal_sanksi)->addDays($record->durasi_sanksi);
                    $isEnded = $endDate->isPast();
                @endphp
                <div class="w-3 h-3 {{ $isEnded ? 'bg-green-500' : 'bg-yellow-500' }} rounded-full"></div>
                <div class="text-sm">
                    <span class="font-medium">{{ $endDate->format('d M Y') }}</span>
                    - {{ $isEnded ? 'Sanksi berakhir' : 'Sanksi akan berakhir' }}
                </div>
            </div>
            @endif
        </div>
    </div>
    @endif

    <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Dibuat: {{ $record->created_at->format('d F Y H:i') }}</span>
            @if($record->updated_at != $record->created_at)
                <span>Diupdate: {{ $record->updated_at->format('d F Y H:i') }}</span>
            @endif
        </div>
    </div>
</div>
