<div class="space-y-4">
    <div class="text-center">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            🎓 {{ $record->nama_institusi }}
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $record->jenjang_pendidikan }} - {{ $record->jurusan }}</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Informasi Institusi -->
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-3">🏫 Informasi Institusi</h4>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Jenjang:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        {{ $record->jenjang_pendidikan == 'S1' ? 'bg-indigo-100 text-indigo-800' : 
                           ($record->jenjang_pendidikan == 'S2' ? 'bg-pink-100 text-pink-800' : 
                           ($record->jenjang_pendidikan == 'S3' ? 'bg-red-100 text-red-800' : 
                           ($record->jenjang_pendidikan == 'D3' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'))) }}">
                        {{ $record->jenjang_pendidikan }}
                    </span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Nama Institusi:</span>
                    <span class="text-sm font-medium text-blue-800 dark:text-blue-200 text-right">
                        {{ $record->nama_institusi }}
                    </span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Jurusan:</span>
                    <span class="text-sm font-medium text-blue-800 dark:text-blue-200 text-right">
                        {{ $record->jurusan }}
                    </span>
                </div>
                
                @if($record->kota)
                <div class="flex justify-between">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Kota:</span>
                    <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        {{ $record->kota }}
                    </span>
                </div>
                @endif
            </div>
        </div>

        <!-- Periode & Status -->
        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <h4 class="font-semibold text-green-800 dark:text-green-200 mb-3">📅 Periode & Status</h4>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-green-700 dark:text-green-300">Tahun Masuk:</span>
                    <span class="text-sm font-medium text-green-800 dark:text-green-200">
                        {{ $record->tahun_masuk }}
                    </span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-sm text-green-700 dark:text-green-300">Tahun Lulus:</span>
                    <span class="text-sm font-medium text-green-800 dark:text-green-200">
                        {{ $record->tahun_lulus ?? 'Belum lulus' }}
                    </span>
                </div>
                
                @if($record->tahun_masuk && $record->tahun_lulus)
                <div class="flex justify-between">
                    <span class="text-sm text-green-700 dark:text-green-300">Durasi:</span>
                    <span class="text-sm font-medium text-green-800 dark:text-green-200">
                        {{ $record->tahun_lulus - $record->tahun_masuk }} tahun
                    </span>
                </div>
                @endif
                
                <div class="flex justify-between">
                    <span class="text-sm text-green-700 dark:text-green-300">Status:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        {{ $record->status_pendidikan == 'Lulus' ? 'bg-green-100 text-green-800' : 
                           ($record->status_pendidikan == 'Sedang Berjalan' ? 'bg-yellow-100 text-yellow-800' : 
                           ($record->status_pendidikan == 'Tidak Lulus' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800')) }}">
                        {{ $record->status_pendidikan }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- IPK/Nilai -->
    @if($record->ipk)
    <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-purple-800 dark:text-purple-200 mb-3">📊 Prestasi Akademik</h4>
        <div class="text-center">
            <div class="inline-flex items-center space-x-4">
                <div class="text-center">
                    <div class="text-3xl font-bold text-purple-800 dark:text-purple-200">
                        {{ number_format($record->ipk, 2) }}
                    </div>
                    <div class="text-sm text-purple-600 dark:text-purple-400">IPK/Nilai</div>
                </div>
                
                <div class="text-center">
                    @php
                        $grade = '';
                        $color = '';
                        if ($record->ipk >= 3.5) {
                            $grade = 'Cum Laude';
                            $color = 'bg-green-100 text-green-800';
                        } elseif ($record->ipk >= 3.0) {
                            $grade = 'Sangat Baik';
                            $color = 'bg-blue-100 text-blue-800';
                        } elseif ($record->ipk >= 2.5) {
                            $grade = 'Baik';
                            $color = 'bg-yellow-100 text-yellow-800';
                        } else {
                            $grade = 'Cukup';
                            $color = 'bg-orange-100 text-orange-800';
                        }
                    @endphp
                    <span class="inline-flex items-center px-3 py-1 text-sm font-medium rounded-full {{ $color }}">
                        {{ $grade }}
                    </span>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- No. Ijazah -->
    @if($record->no_ijazah)
    <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">📜 Nomor Ijazah</h4>
        <div class="text-center">
            <span class="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-700 border border-yellow-200 dark:border-yellow-700 rounded-lg font-mono text-sm">
                {{ $record->no_ijazah }}
            </span>
        </div>
    </div>
    @endif

    <!-- Status Verifikasi -->
    <div class="text-center">
        <span class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-full 
            {{ $record->is_verified ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
            @if($record->is_verified)
                ✅ Terverifikasi
            @else
                ⏳ Belum Terverifikasi
            @endif
        </span>
    </div>

    <!-- Timeline Pendidikan -->
    @if($record->tahun_masuk)
    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-3">📅 Timeline Pendidikan</h4>
        <div class="space-y-2">
            <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                <div class="text-sm">
                    <span class="font-medium">{{ $record->tahun_masuk }}</span>
                    - Mulai pendidikan di {{ $record->nama_institusi }}
                </div>
            </div>
            
            @if($record->tahun_lulus)
            <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <div class="text-sm">
                    <span class="font-medium">{{ $record->tahun_lulus }}</span>
                    - Lulus dengan {{ $record->status_pendidikan }}
                    @if($record->ipk)
                        (IPK: {{ number_format($record->ipk, 2) }})
                    @endif
                </div>
            </div>
            @else
            <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                <div class="text-sm">
                    <span class="font-medium">Sekarang</span>
                    - {{ $record->status_pendidikan }}
                </div>
            </div>
            @endif
        </div>
    </div>
    @endif

    <!-- Keterangan -->
    @if($record->keterangan)
    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">📝 Keterangan</h4>
        <p class="text-sm text-blue-700 dark:text-blue-300">{{ $record->keterangan }}</p>
    </div>
    @endif

    <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Dibuat: {{ $record->created_at->format('d F Y H:i') }}</span>
            @if($record->updated_at != $record->created_at)
                <span>Diupdate: {{ $record->updated_at->format('d F Y H:i') }}</span>
            @endif
        </div>
    </div>
</div>
