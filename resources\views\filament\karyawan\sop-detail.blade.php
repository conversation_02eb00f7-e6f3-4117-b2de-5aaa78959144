<div class="space-y-6">
    <!-- Header SOP -->
    <div class="text-center border-b pb-4">
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">
            📋 {{ $record->judul_sop }}
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Versi {{ $record->versi }} • 
            {{ $record->scope_type === 'departemen' ? 'Departemen' : 'Divisi' }}: {{ $record->scope_name }}
        </p>
    </div>

    <!-- Status dan Informasi Umum -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Status SOP -->
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-3">📊 Status SOP</h4>
            <div class="space-y-2">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Status:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        {{ $record->status === 'aktif' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300' }}">
                        {{ $record->status === 'aktif' ? '✅ Aktif' : '❌ Tidak Aktif' }}
                    </span>
                </div>
                
                <div class="flex justify-between items-center">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Berlaku:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        {{ $record->isBerlaku() ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300' }}">
                        {{ $record->isBerlaku() ? '✅ Berlaku' : '⏳ Tidak Berlaku' }}
                    </span>
                </div>

                <div class="flex justify-between">
                    <span class="text-sm text-blue-700 dark:text-blue-300">Berlaku Untuk:</span>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full 
                        {{ $record->scope_type === 'departemen' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300' : 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300' }}">
                        {{ $record->scope_type === 'departemen' ? '🏢 Departemen' : '👥 Divisi' }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Informasi Periode -->
        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <h4 class="font-semibold text-green-800 dark:text-green-200 mb-3">📅 Periode Berlaku</h4>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-green-700 dark:text-green-300">Mulai Berlaku:</span>
                    <span class="text-sm font-medium text-green-800 dark:text-green-200">
                        {{ $record->tanggal_berlaku ? $record->tanggal_berlaku->format('d/m/Y') : 'Tidak ditentukan' }}
                    </span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-sm text-green-700 dark:text-green-300">Berakhir:</span>
                    <span class="text-sm font-medium text-green-800 dark:text-green-200">
                        {{ $record->tanggal_berakhir ? $record->tanggal_berakhir->format('d/m/Y') : 'Tidak terbatas' }}
                    </span>
                </div>

                <div class="flex justify-between">
                    <span class="text-sm text-green-700 dark:text-green-300">Versi:</span>
                    <span class="text-sm font-medium text-green-800 dark:text-green-200">
                        {{ $record->versi }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Deskripsi -->
    @if($record->deskripsi)
    <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">📝 Deskripsi</h4>
        <p class="text-sm text-yellow-700 dark:text-yellow-300 leading-relaxed">
            {{ $record->deskripsi }}
        </p>
    </div>
    @endif

    <!-- Informasi Unit -->
    <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-purple-800 dark:text-purple-200 mb-3">🏢 Informasi Unit</h4>
        <div class="space-y-2">
            <div class="flex justify-between">
                <span class="text-sm text-purple-700 dark:text-purple-300">Scope:</span>
                <span class="text-sm font-medium text-purple-800 dark:text-purple-200">
                    {{ $record->scope_type === 'departemen' ? 'Departemen' : 'Divisi' }}
                </span>
            </div>
            
            <div class="flex justify-between">
                <span class="text-sm text-purple-700 dark:text-purple-300">Nama Unit:</span>
                <span class="text-sm font-medium text-purple-800 dark:text-purple-200">
                    {{ $record->scope_name }}
                </span>
            </div>

            @if($record->scope_type === 'divisi' && $record->divisi && $record->divisi->departemen)
            <div class="flex justify-between">
                <span class="text-sm text-purple-700 dark:text-purple-300">Departemen:</span>
                <span class="text-sm font-medium text-purple-800 dark:text-purple-200">
                    {{ $record->divisi->departemen->nama_departemen }}
                </span>
            </div>
            @endif
        </div>
    </div>

    <!-- Informasi File -->
    @if($record->file_path)
    <div class="bg-gray-50 dark:bg-gray-900/20 p-4 rounded-lg">
        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-3">📄 Informasi File</h4>
        <div class="space-y-2">
            <div class="flex justify-between">
                <span class="text-sm text-gray-700 dark:text-gray-300">Nama File:</span>
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200 font-mono">
                    {{ basename($record->file_path) }}
                </span>
            </div>
            
            <div class="flex justify-between">
                <span class="text-sm text-gray-700 dark:text-gray-300">Format:</span>
                <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300">
                    📄 PDF
                </span>
            </div>
        </div>
    </div>
    @endif

    <!-- Actions -->
    @if($record->file_path)
    <div class="flex justify-center space-x-3 pt-4 border-t">
        <a href="{{ asset('storage/' . $record->file_path) }}" 
           target="_blank"
           class="inline-flex items-center px-6 py-3 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors shadow-sm">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
            Lihat PDF
        </a>
        
        <a href="{{ asset('storage/' . $record->file_path) }}" 
           download
           class="inline-flex items-center px-6 py-3 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors shadow-sm">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Download PDF
        </a>
    </div>
    @endif

    <!-- Footer Info -->
    <div class="text-center text-xs text-gray-500 dark:text-gray-400 pt-4 border-t">
        <p>SOP ini dibuat pada {{ $record->created_at->format('d/m/Y H:i') }}</p>
        @if($record->creator)
        <p>Dibuat oleh: {{ $record->creator->name }}</p>
        @endif
    </div>
</div>
