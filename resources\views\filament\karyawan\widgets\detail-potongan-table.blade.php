<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            Detail Potongan Bulan Ini
        </x-slot>

        <x-slot name="description">
            Rincian potongan yang akan mempeng<PERSON>hi gaji <PERSON>
        </x-slot>

        @if($potonganData->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Tanggal
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                <PERSON><PERSON>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Keterangan
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Ju<PERSON><PERSON>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Status
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($potonganData->sortByDesc('tanggal') as $potongan)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ \Carbon\Carbon::parse($potongan->tanggal)->format('d M Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {{ $potongan->jenis === 'Keterlambatan' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                    {{ $potongan->jenis === 'Pelanggaran' ? 'bg-red-100 text-red-800' : '' }}
                                    {{ $potongan->jenis === 'BPJS Kesehatan' ? 'bg-blue-100 text-blue-800' : '' }}
                                    {{ $potongan->jenis === 'BPJS Ketenagakerjaan' ? 'bg-green-100 text-green-800' : '' }}
                                    {{ $potongan->jenis === 'Lainnya' ? 'bg-gray-100 text-gray-800' : '' }}
                                ">
                                    {{ $potongan->jenis }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                                <div class="max-w-xs truncate" title="{{ $potongan->keterangan }}">
                                    {{ $potongan->keterangan }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-red-600">
                                - Rp {{ number_format($potongan->jumlah, 0, ',', '.') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {{ $potongan->status === 'Akan Dipotong' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                    {{ $potongan->status === 'Sudah Dipotong' ? 'bg-green-100 text-green-800' : '' }}
                                    {{ $potongan->status === 'Pending' ? 'bg-gray-100 text-gray-800' : '' }}
                                ">
                                    {{ $potongan->status }}
                                </span>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                    <tfoot class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <td colspan="3" class="px-6 py-4 text-sm font-bold text-gray-900 dark:text-gray-100">
                                Total Potongan
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-red-600">
                                - Rp {{ number_format($totalPotongan, 0, ',', '.') }}
                            </td>
                            <td class="px-6 py-4"></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Tidak Ada Potongan</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Tidak ada potongan untuk bulan ini.</p>
            </div>
        @endif

        @if($potonganData->count() > 0)
        <div class="mt-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Informasi Penting</h3>
                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Potongan dengan status "Akan Dipotong" akan diproses pada payroll berikutnya</li>
                            <li>Potongan dengan status "Sudah Dipotong" telah diproses dalam payroll bulan ini</li>
                            <li>Untuk menghindari potongan keterlambatan, pastikan hadir tepat waktu</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </x-filament::section>
</x-filament-widgets::widget>
