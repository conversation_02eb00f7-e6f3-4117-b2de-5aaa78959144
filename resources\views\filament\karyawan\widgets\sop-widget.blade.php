<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            📋 SOP Terbaru
        </x-slot>

        <x-slot name="description">
            Standard Operating Procedure yang berlaku untuk Anda
        </x-slot>

        @php
            $sopData = $this->getSopData();
            $karyawan = $this->getKaryawanData();
        @endphp

        <div class="space-y-4">
            @if($sopData->isEmpty())
                <div class="py-8 text-center">
                    <div class="flex items-center justify-center w-12 h-12 mx-auto bg-gray-100 rounded-full dark:bg-gray-800">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Belum Ada SOP</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Belum ada SOP yang tersedia untuk departemen atau divisi Anda.
                    </p>
                </div>
            @else
                @if($karyawan)
                    <div class="p-3 mb-4 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-blue-700 dark:text-blue-300">
                                    <span class="font-medium">{{ $karyawan->nama_lengkap }}</span> -
                                    @if($karyawan->departemen)
                                        Departemen: {{ $karyawan->departemen->nama_departemen }}
                                    @endif
                                    @if($karyawan->divisi)
                                        @if($karyawan->departemen), @endif
                                        Divisi: {{ $karyawan->divisi->nama_divisi }}
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="space-y-3">
                    @foreach($sopData as $sop)
                        <div class="p-4 transition-colors border border-gray-200 rounded-lg dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50">
                            <div class="flex items-start justify-between">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center mb-2 space-x-2">
                                        <h4 class="text-sm font-medium text-gray-900 truncate dark:text-gray-100">
                                            {{ $sop->judul_sop }}
                                        </h4>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                                            {{ $sop->scope_type === 'departemen' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300' : 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300' }}">
                                            {{ $sop->scope_type === 'departemen' ? '🏢 Dept' : '👥 Div' }}
                                        </span>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                                            v{{ $sop->versi }}
                                        </span>
                                    </div>

                                    <p class="mb-2 text-xs text-gray-600 dark:text-gray-400">
                                        {{ $sop->scope_name }}
                                    </p>

                                    @if($sop->deskripsi)
                                        <p class="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
                                            {{ Str::limit($sop->deskripsi, 100) }}
                                        </p>
                                    @endif

                                    <div class="flex items-center mt-2 space-x-4 text-xs text-gray-500 dark:text-gray-400">
                                        <span>📅 {{ $sop->tanggal_berlaku->format('d/m/Y') }}</span>
                                        @if($sop->tanggal_berakhir)
                                            <span>⏰ s/d {{ $sop->tanggal_berakhir->format('d/m/Y') }}</span>
                                        @else
                                            <span>⏰ Tidak terbatas</span>
                                        @endif
                                    </div>
                                </div>

                                <div class="flex-shrink-0 ml-4">
                                    <div class="flex space-x-2">
                                        <a href="{{ asset('storage/' . $sop->file_path) }}"
                                           target="_blank"
                                           class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 transition-colors bg-blue-100 border border-transparent rounded hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/40">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            Lihat
                                        </a>

                                        <a href="{{ asset('storage/' . $sop->file_path) }}"
                                           download
                                           class="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 transition-colors bg-green-100 border border-transparent rounded hover:bg-green-200 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/40">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            Download
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="mt-4 text-center">
                    <a href="{{ route('filament.karyawan.resources.sops.index') }}"
                       class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-700 transition-colors bg-blue-100 border border-transparent rounded-md hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/40">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Lihat Semua SOP
                    </a>
                </div>
            @endif
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
