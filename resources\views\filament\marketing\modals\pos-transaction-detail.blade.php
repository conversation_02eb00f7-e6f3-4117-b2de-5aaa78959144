<div class="space-y-6">
    <!-- Transaction Header -->
    <div class="bg-gray-50 rounded-lg p-4">
        <div class="grid grid-cols-2 gap-4">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">{{ $record->transaction_number }}</h3>
                <p class="text-sm text-gray-600">{{ $record->transaction_date->format('d/m/Y H:i:s') }}</p>
            </div>
            <div class="text-right">
                <p class="text-2xl font-bold text-green-600">Rp {{ number_format($record->net_amount, 0, ',', '.') }}</p>
                <p class="text-sm text-gray-600">{{ $record->payment_method_label }}</p>
            </div>
        </div>
    </div>

    <!-- Customer & Cashier Info -->
    <div class="grid grid-cols-2 gap-6">
        <div>
            <h4 class="font-medium text-gray-900 mb-2">Informasi Pelanggan</h4>
            <div class="space-y-1">
                <p class="text-sm"><span class="font-medium">Nama:</span> {{ $record->customer?->nama ?? 'Walk-in Customer' }}</p>
                @if($record->customer)
                    <p class="text-sm"><span class="font-medium">Email:</span> {{ $record->customer->email ?? 'N/A' }}</p>
                    <p class="text-sm"><span class="font-medium">Telepon:</span> {{ $record->customer->telepon ?? 'N/A' }}</p>
                @endif
                @if($record->table_number)
                    <p class="text-sm"><span class="font-medium">No. Meja:</span> {{ $record->table_number }}</p>
                @endif
            </div>
        </div>
        
        <div>
            <h4 class="font-medium text-gray-900 mb-2">Informasi Kasir</h4>
            <div class="space-y-1">
                <p class="text-sm"><span class="font-medium">Kasir:</span> {{ $record->user->name }}</p>
                <p class="text-sm"><span class="font-medium">Email:</span> {{ $record->user->email }}</p>
            </div>
        </div>
    </div>

    <!-- Transaction Items -->
    <div>
        <h4 class="font-medium text-gray-900 mb-3">Item Transaksi</h4>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Produk</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Harga Satuan</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Diskon</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($record->posTransactionItems as $item)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $item->product->name }}</div>
                                <div class="text-sm text-gray-500">{{ $item->product->category->name ?? 'N/A' }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item->quantity }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Rp {{ number_format($item->unit_price, 0, ',', '.') }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Rp {{ number_format($item->discount_per_item, 0, ',', '.') }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Rp {{ number_format($item->total_price, 0, ',', '.') }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Transaction Summary -->
    <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="font-medium text-gray-900 mb-3">Ringkasan Transaksi</h4>
        <div class="space-y-2">
            <div class="flex justify-between">
                <span class="text-sm text-gray-600">Sub Total:</span>
                <span class="text-sm font-medium">Rp {{ number_format($record->total_amount, 0, ',', '.') }}</span>
            </div>
            @if($record->discount_amount > 0)
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Diskon:</span>
                    <span class="text-sm font-medium text-red-600">-Rp {{ number_format($record->discount_amount, 0, ',', '.') }}</span>
                </div>
            @endif
            @if($record->tax_amount > 0)
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Pajak:</span>
                    <span class="text-sm font-medium">Rp {{ number_format($record->tax_amount, 0, ',', '.') }}</span>
                </div>
            @endif
            <hr class="my-2">
            <div class="flex justify-between">
                <span class="text-base font-medium text-gray-900">Total Bersih:</span>
                <span class="text-base font-bold text-green-600">Rp {{ number_format($record->net_amount, 0, ',', '.') }}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-sm text-gray-600">Dibayar:</span>
                <span class="text-sm font-medium">Rp {{ number_format($record->amount_paid, 0, ',', '.') }}</span>
            </div>
            @if($record->change_given > 0)
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Kembalian:</span>
                    <span class="text-sm font-medium">Rp {{ number_format($record->change_given, 0, ',', '.') }}</span>
                </div>
            @endif
        </div>
    </div>

    <!-- Loyalty Points -->
    @if($record->loyalty_points_used > 0 || $record->loyalty_points_earned > 0)
        <div class="bg-blue-50 rounded-lg p-4">
            <h4 class="font-medium text-gray-900 mb-3">Poin Loyalitas</h4>
            <div class="space-y-2">
                @if($record->loyalty_points_used > 0)
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Poin Digunakan:</span>
                        <span class="text-sm font-medium text-red-600">-{{ number_format($record->loyalty_points_used) }}</span>
                    </div>
                @endif
                @if($record->loyalty_points_earned > 0)
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Poin Didapat:</span>
                        <span class="text-sm font-medium text-green-600">+{{ number_format($record->loyalty_points_earned) }}</span>
                    </div>
                @endif
            </div>
        </div>
    @endif
</div>
