<x-filament-panels::page>
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Product Selection Panel -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Pilih Produk</h3>
                    <div class="flex space-x-2">
                        <input type="text" 
                               placeholder="Cari produk..." 
                               class="px-3 py-2 border border-gray-300 rounded-md text-sm"
                               wire:model.live="searchProduct">
                        <button class="px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                            Scan Barcode
                        </button>
                    </div>
                </div>
                
                <!-- Product Grid -->
                <div class="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
                    @foreach($this->getProducts() as $product)
                    <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md cursor-pointer transition-shadow"
                         wire:click="addProduct({{ $product->id }})">
                        <div class="aspect-square bg-gray-100 rounded-md mb-2 flex items-center justify-center">
                            @if($product->image)
                                <img src="{{ Storage::url($product->image) }}" alt="{{ $product->name }}" class="w-full h-full object-cover rounded-md">
                            @else
                                <span class="text-gray-400 text-xs">{{ $product->name }}</span>
                            @endif
                        </div>
                        <h4 class="font-medium text-sm text-gray-900 truncate">{{ $product->name }}</h4>
                        <p class="text-blue-600 font-semibold text-sm">{{ $product->formatted_price }}</p>
                        <p class="text-xs {{ $product->isLowStock() ? 'text-red-500' : ($product->isOutOfStock() ? 'text-red-600 font-semibold' : 'text-gray-500') }}">
                            Stok: {{ $product->stock_quantity ?? 'N/A' }}
                            @if($product->isOutOfStock())
                                <span class="text-red-600 font-bold">(Habis)</span>
                            @elseif($product->isLowStock())
                                <span class="text-orange-600">(Rendah)</span>
                            @endif
                        </p>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Transaction Panel -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Transaksi</h3>
                
                <!-- Customer Selection -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Customer</label>
                    <select wire:model="customer_id" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option value="">Walk-in Customer</option>
                        @foreach($this->getCustomers() as $customer)
                            <option value="{{ $customer->id }}">
                                {{ $customer->nama }} 
                                @if($customer->loyalty_points > 0)
                                    ({{ $customer->loyalty_points }} pts)
                                @endif
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Selected Products -->
                <div class="mb-4">
                    <h4 class="font-medium text-gray-900 mb-2">Items ({{ count($selectedProducts) }})</h4>
                    <div class="max-h-48 overflow-y-auto space-y-2">
                        @forelse($selectedProducts as $key => $item)
                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">{{ $item['name'] }}</p>
                                <p class="text-xs text-gray-500">
                                    Rp {{ number_format($item['price'], 0, ',', '.') }}
                                    @if(isset($item['stock_available']) && $item['stock_available'] !== null)
                                        <span class="ml-2 text-xs {{ $item['stock_available'] <= 10 ? 'text-orange-600' : 'text-gray-400' }}">
                                            (Stok: {{ $item['stock_available'] }})
                                        </span>
                                    @endif
                                </p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button wire:click="updateQuantity({{ $key }}, {{ $item['quantity'] - 1 }})"
                                        class="w-6 h-6 bg-red-100 text-red-600 rounded-full text-xs hover:bg-red-200">-</button>
                                <span class="text-sm font-medium w-8 text-center">{{ $item['quantity'] }}</span>
                                <button wire:click="updateQuantity({{ $key }}, {{ $item['quantity'] + 1 }})"
                                        class="w-6 h-6 bg-green-100 text-green-600 rounded-full text-xs hover:bg-green-200">+</button>
                                <button wire:click="removeProduct({{ $key }})"
                                        class="w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600">×</button>
                            </div>
                        </div>
                        @empty
                        <p class="text-gray-500 text-sm text-center py-4">Belum ada produk dipilih</p>
                        @endforelse
                    </div>
                </div>

                <!-- Transaction Details -->
                <div class="border-t pt-4 space-y-2">
                    <div class="flex justify-between text-sm">
                        <span>Subtotal:</span>
                        <span>Rp {{ number_format(collect($selectedProducts)->sum('total'), 0, ',', '.') }}</span>
                    </div>
                    
                    <!-- Discount -->
                    <div class="flex justify-between items-center text-sm">
                        <span>Diskon:</span>
                        <input type="number" 
                               wire:model.live="discount_amount" 
                               class="w-20 px-2 py-1 border border-gray-300 rounded text-right text-xs"
                               min="0">
                    </div>
                    
                    <!-- Tax -->
                    <div class="flex justify-between text-sm">
                        <span>Pajak (11%):</span>
                        <span>Rp {{ number_format($tax_amount, 0, ',', '.') }}</span>
                    </div>
                    
                    <div class="flex justify-between font-semibold text-lg border-t pt-2">
                        <span>Total:</span>
                        <span class="text-blue-600">Rp {{ number_format($this->getTotalAmount(), 0, ',', '.') }}</span>
                    </div>
                    
                    <!-- Loyalty Points Preview -->
                    @if($customer_id)
                    <div class="bg-yellow-50 p-2 rounded-md">
                        <p class="text-xs text-yellow-800">
                            Poin yang akan didapat: <strong>{{ $this->calculateLoyaltyPoints() }} pts</strong>
                        </p>
                    </div>
                    @endif
                </div>

                <!-- Payment Section -->
                <div class="mt-6 space-y-4">
                    <!-- Payment Method -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Metode Pembayaran</label>
                        <select wire:model="payment_method" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <option value="cash">Tunai</option>
                            <option value="card">Kartu</option>
                            <option value="transfer">Transfer</option>
                            <option value="ewallet">E-Wallet</option>
                            <option value="qris">QRIS</option>
                        </select>
                    </div>

                    <!-- Amount Paid -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Jumlah Bayar</label>
                        <input type="number" 
                               wire:model.live="amount_paid" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                               min="0"
                               step="1000">
                    </div>

                    <!-- Change -->
                    @if($amount_paid > 0)
                    <div class="flex justify-between text-sm">
                        <span>Kembalian:</span>
                        <span class="font-semibold {{ $this->getChangeAmount() >= 0 ? 'text-green-600' : 'text-red-600' }}">
                            Rp {{ number_format($this->getChangeAmount(), 0, ',', '.') }}
                        </span>
                    </div>
                    @endif

                    <!-- Table Number -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">No. Meja (Opsional)</label>
                        <input type="text" 
                               wire:model="table_number" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                               placeholder="Contoh: A1, B5">
                    </div>

                    <!-- Process Transaction Button -->
                    <button wire:click="processTransaction"
                            @disabled(empty($selectedProducts) || $amount_paid < $this->getTotalAmount())
                            class="w-full bg-green-600 text-white py-3 rounded-md font-semibold hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed">
                        Proses Transaksi
                    </button>

                    <!-- Quick Amount Buttons -->
                    @if($payment_method === 'cash')
                    <div class="grid grid-cols-3 gap-2">
                        @foreach([50000, 100000, 200000] as $amount)
                        <button wire:click="$set('amount_paid', {{ $this->getTotalAmount() + $amount }})"
                                class="px-3 py-2 bg-gray-100 text-gray-700 rounded-md text-xs hover:bg-gray-200">
                            +{{ number_format($amount, 0, ',', '.') }}
                        </button>
                        @endforeach
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div wire:loading.flex class="fixed inset-0 bg-black bg-opacity-50 items-center justify-center z-50">
        <div class="bg-white p-6 rounded-lg">
            <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span>Memproses transaksi...</span>
            </div>
        </div>
    </div>
</x-filament-panels::page>
