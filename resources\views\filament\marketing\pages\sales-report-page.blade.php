<x-filament-panels::page>
    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    @endpush
    <div class="space-y-6">
        <!-- Filter Form -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Filter Laporan POS</h3>
                <button
                    wire:click="refreshData"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                    <x-heroicon-o-arrow-path class="w-4 h-4 mr-2" />
                    Refresh Data
                </button>
            </div>

            {{ $this->form }}

            @if(!empty($this->getFilterSummary()))
            <div class="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div class="flex items-center justify-between">
                    <h4 class="text-sm font-medium text-blue-800 mb-2">Filter Aktif:</h4>
                    <span class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                        {{ count($this->getFilterSummary()) }} filter
                    </span>
                </div>
                <div class="flex flex-wrap gap-2">
                    @foreach($this->getFilterSummary() as $filter)
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                        <x-heroicon-o-funnel class="w-3 h-3 mr-1" />
                        {{ $filter }}
                    </span>
                    @endforeach
                </div>
            </div>
            @endif
        </div>

        <!-- Loading State -->
        <div wire:loading wire:target="generateReport,applyDatePreset,refreshData" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-gray-900 font-medium">Memproses data...</span>
            </div>
        </div>

        @if(!empty($reportData))
        <!-- Summary Cards (5 widgets) -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            <!-- 1. Total Transaksi -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <x-heroicon-o-document-text class="w-6 h-6 text-blue-600" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Transaksi</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($reportData['totalTransactions'] ?? 0) }}</p>
                    </div>
                </div>
            </div>

            <!-- 2. Total Pendapatan -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <x-heroicon-o-banknotes class="w-6 h-6 text-green-600" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Pendapatan</p>
                        <p class="text-2xl font-bold text-gray-900">Rp {{ number_format($reportData['totalRevenue'] ?? 0, 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <!-- 3. Rata-rata Nilai Order -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <x-heroicon-o-calculator class="w-6 h-6 text-yellow-600" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Rata-rata Nilai Order</p>
                        <p class="text-2xl font-bold text-gray-900">Rp {{ number_format($reportData['averageOrderValue'] ?? 0, 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <!-- 4. Total Diskon -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 rounded-lg">
                        <x-heroicon-o-receipt-percent class="w-6 h-6 text-red-600" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Diskon</p>
                        <p class="text-2xl font-bold text-gray-900">Rp {{ number_format($reportData['totalDiscount'] ?? 0, 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <!-- 5. Pendapatan Bersih -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <x-heroicon-o-currency-dollar class="w-6 h-6 text-purple-600" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pendapatan Bersih</p>
                        <p class="text-2xl font-bold text-gray-900">Rp {{ number_format($reportData['totalNetAmount'] ?? 0, 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6"
             wire:key="charts-container-{{ $datePreset }}-{{ md5(json_encode($reportData)) }}"
             x-data="{
                 chartKey: '{{ $datePreset }}-{{ time() }}',
                 productData: @js($reportData['topProducts'] ?? []),
                 categoryData: @js($reportData['categorySales'] ?? []),
                 hourlyData: @js($reportData['hourlyProductAnalysis'] ?? [])
             }"
             x-init="
                 $nextTick(() => {
                     window.currentChartData = {
                         products: @js($reportData['topProducts'] ?? []),
                         categories: @js($reportData['categorySales'] ?? []),
                         hourly: @js($reportData['hourlyProductAnalysis'] ?? [])
                     };
                     initCharts();
                 });
             ">
            <!-- 6. Bar Chart Penjualan Produk -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Bar Chart Penjualan Produk</h3>
                <div class="h-80">
                    <canvas x-ref="productChart" width="400" height="300"></canvas>
                </div>
            </div>

            <!-- 7. Bar Chart Penjualan per Kategori -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Bar Chart Penjualan per Kategori</h3>
                <div class="h-80">
                    <canvas x-ref="categoryChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- 8. Line Chart Penjualan Produk per Jam -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Line Chart Penjualan Produk per Jam</h3>

                <!-- Product Filter Dropdown -->
                <div class="relative" x-data="{
                    open: false,
                    selectedProducts: @js($selectedProducts),
                    availableProducts: @js($availableProducts),
                    get productOptions() {
                        return this.availableProducts;
                    },
                    toggleProduct(productId) {
                        const index = this.selectedProducts.indexOf(productId);
                        if (index === -1) {
                            this.selectedProducts.push(productId);
                        } else {
                            this.selectedProducts.splice(index, 1);
                        }
                        this.updateProductFilter();
                    },
                    selectAll() {
                        this.selectedProducts = this.availableProducts.map(p => p.id);
                        this.updateProductFilter();
                    },
                    deselectAll() {
                        this.selectedProducts = [];
                        this.updateProductFilter();
                    },
                    updateProductFilter() {
                        $wire.updateSelectedProducts(this.selectedProducts).then((hourlyData) => {
                            window.currentChartData.hourly = hourlyData;
                            window.updateHourlyChart();
                        });
                    },
                    getProductName(productId) {
                        const product = this.availableProducts.find(p => p.id === productId);
                        return product ? product.name : '';
                    }
                }"
                    <button
                        @click="open = !open"
                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        <span x-text="selectedProducts.length ? `${selectedProducts.length} Produk Dipilih` : 'Filter Produk'"></span>
                        <svg class="ml-2 -mr-0.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div
                        x-show="open"
                        @click.away="open = false"
                        class="origin-top-right absolute right-0 mt-2 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 divide-y divide-gray-100 focus:outline-none z-10"
                        x-transition:enter="transition ease-out duration-100"
                        x-transition:enter-start="transform opacity-0 scale-95"
                        x-transition:enter-end="transform opacity-100 scale-100"
                        x-transition:leave="transition ease-in duration-75"
                        x-transition:leave-start="transform opacity-100 scale-100"
                        x-transition:leave-end="transform opacity-0 scale-95"
                    >
                        <div class="p-2">
                            <div class="flex items-center justify-between p-2 border-b border-gray-200">
                                <span class="text-sm font-medium text-gray-700">Filter Produk</span>
                                <div class="flex space-x-2">
                                    <button
                                        @click="selectAll()"
                                        class="text-xs text-green-600 hover:text-green-800"
                                    >
                                        Pilih Semua
                                    </button>
                                    <button
                                        @click="deselectAll()"
                                        class="text-xs text-red-600 hover:text-red-800"
                                    >
                                        Reset
                                    </button>
                                </div>
                            </div>

                            <!-- Category grouping -->
                            <div class="mt-2 max-h-60 overflow-y-auto">
                                <!-- Group products by category -->
                                <template x-for="category in [...new Set(productOptions.map(p => p.category))]" :key="category">
                                    <div class="mb-2">
                                        <div class="px-2 py-1 bg-gray-100 text-xs font-semibold text-gray-700 uppercase tracking-wider">
                                            <span x-text="category"></span>
                                        </div>

                                        <template x-for="product in productOptions.filter(p => p.category === category)" :key="product.id">
                                            <div class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer" @click="toggleProduct(product.id)">
                                                <input
                                                    type="checkbox"
                                                    :id="'product-' + product.id"
                                                    :checked="selectedProducts.includes(product.id)"
                                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded pointer-events-none"
                                                >
                                                <label
                                                    :for="'product-' + product.id"
                                                    class="ml-2 block text-sm text-gray-900 truncate cursor-pointer flex-1"
                                                    x-text="product.name"
                                                ></label>
                                                <span
                                                    x-show="selectedProducts.includes(product.id)"
                                                    class="ml-2 text-xs text-blue-600"
                                                >
                                                    ✓
                                                </span>
                                            </div>
                                        </template>
                                    </div>
                                </template>

                                <div x-show="productOptions.length === 0" class="p-2 text-sm text-gray-500 text-center">
                                    Tidak ada produk tersedia
                                </div>
                            </div>

                            <div class="mt-2 p-2 border-t border-gray-200 text-xs text-gray-500">
                                <span x-text="`${selectedProducts.length} dari ${productOptions.length} produk dipilih`"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="h-96">
                <canvas x-ref="hourlyChart" width="800" height="400"></canvas>
            </div>
        </div>

        <!-- 9. Tabel Top 10 Customer -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Top 10 Customer</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaksi</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loyalty Points</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($reportData['topCustomers'] ?? [] as $index => $customer)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-bold text-blue-600">{{ $index + 1 }}</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $customer['name'] }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $customer['email'] }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($customer['count']) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Rp {{ number_format($customer['revenue'], 0, ',', '.') }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($customer['loyalty_points']) }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">Tidak ada data customer</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 10. Data Transaksi -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Data Transaksi (Periode Dipilih)</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal & Jam</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Diskon</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Net Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pembayaran</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($reportData['transactions'] ?? [] as $transaction)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                #{{ $transaction->id }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $transaction->transaction_date->format('d/m/Y H:i:s') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $transaction->customer->nama ?? 'Walk-in Customer' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                Rp {{ number_format($transaction->total_amount, 0, ',', '.') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                                Rp {{ number_format($transaction->discount_amount, 0, ',', '.') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                                Rp {{ number_format($transaction->net_amount, 0, ',', '.') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 capitalize">
                                {{ $transaction->payment_method }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    {{ $transaction->status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                    {{ ucfirst($transaction->status) }}
                                </span>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-gray-500">Belum ada transaksi dalam periode ini</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        @endif
    </div>

    @if(!empty($reportData))
    <script>
        let productChart, categoryChart, hourlyChart;

        // Global variables for chart instances and filtered products
        let filteredProducts = [];

        // Function to update hourly chart based on selected products
        function updateHourlyChart() {
            if (!window.currentChartData || !window.currentChartData.hourly) {
                console.error('No hourly data available');
                return;
            }

            console.log('Updating hourly chart with current data:', window.currentChartData.hourly);

            // Recreate hourly chart with current data (already filtered by server)
            if (hourlyChart) {
                hourlyChart.destroy();
                hourlyChart = null;
            }

            createHourlyChart();
        }

        function initializeCharts() {
            console.log('Initializing charts with current data...');

            // Destroy existing charts if they exist
            if (productChart) {
                productChart.destroy();
                productChart = null;
            }
            if (categoryChart) {
                categoryChart.destroy();
                categoryChart = null;
            }
            if (hourlyChart) {
                hourlyChart.destroy();
                hourlyChart = null;
            }

            // Initialize with current data
            console.log('Initializing charts with data:', window.currentChartData);

            // Get current data (updated by Livewire)
            const currentData = window.currentChartData || {
                products: @json($reportData['topProducts'] ?? []),
                categories: @json($reportData['categorySales'] ?? []),
                hourly: @json($reportData['hourlyProductAnalysis'] ?? [])
            };

            // Product Sales Bar Chart
            const productCtx = document.querySelector('[x-ref="productChart"]');
            if (productCtx && currentData.products) {
                console.log('Product data:', currentData.products);

                try {
                    productChart = new Chart(productCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: currentData.products.map(item => item.name),
                        datasets: [{
                            label: 'Quantity Terjual',
                            data: currentData.products.map(item => item.quantity),
                            backgroundColor: 'rgba(59, 130, 246, 0.8)',
                            borderColor: 'rgba(59, 130, 246, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Top 10 Produk Terlaris'
                            },
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Quantity'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Produk'
                                }
                            }
                        }
                    }
                });
                console.log('Product chart created successfully');
                } catch (error) {
                    console.error('Error creating product chart:', error);
                }
            }

            // Category Sales Bar Chart
            const categoryCtx = document.querySelector('[x-ref="categoryChart"]');
            if (categoryCtx && currentData.categories) {
                console.log('Category data:', currentData.categories);

                categoryChart = new Chart(categoryCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: currentData.categories.map(item => item.name),
                        datasets: [{
                            label: 'Quantity Terjual',
                            data: currentData.categories.map(item => item.quantity),
                            backgroundColor: 'rgba(34, 197, 94, 0.8)',
                            borderColor: 'rgba(34, 197, 94, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Penjualan per Kategori'
                            },
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Quantity'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Kategori'
                                }
                            }
                        }
                    }
                });
            }

            // Create hourly chart
            createHourlyChart();
        }

        // Separate function to create hourly chart with filtering
        function createHourlyChart() {
            const hourlyCtx = document.querySelector('[x-ref="hourlyChart"]');
            const currentData = window.currentChartData;

            if (!hourlyCtx || !currentData || !currentData.hourly) {
                console.error('Hourly chart context or data not available');
                return;
            }

            console.log('Creating hourly chart with filtered products:', filteredProducts);

            // Generate colors for different products
            const colors = [
                'rgba(59, 130, 246, 1)',    // Blue
                'rgba(34, 197, 94, 1)',     // Green
                'rgba(239, 68, 68, 1)',     // Red
                'rgba(245, 158, 11, 1)',    // Yellow
                'rgba(139, 92, 246, 1)',    // Purple
                'rgba(236, 72, 153, 1)',    // Pink
                'rgba(20, 184, 166, 1)',    // Teal
                'rgba(251, 146, 60, 1)',    // Orange
                'rgba(156, 163, 175, 1)',   // Gray
                'rgba(99, 102, 241, 1)'     // Indigo
            ];

            const datasets = [];

            // Use products from server-side filtering
            const productsToShow = Object.keys(currentData.hourly.products || {});

            productsToShow.forEach((productName, index) => {
                const productHourlyData = currentData.hourly.products[productName] || {};
                const data = currentData.hourly.hours.map(hour => productHourlyData[hour] || 0);

                datasets.push({
                    label: productName,
                    data: data,
                    borderColor: colors[index % colors.length],
                    backgroundColor: colors[index % colors.length].replace('1)', '0.1)'),
                    tension: 0.4,
                    fill: false,
                    pointRadius: 4,
                    pointHoverRadius: 6
                });
            });

            hourlyChart = new Chart(hourlyCtx.getContext('2d'), {
                type: 'line',
                data: {
                    labels: currentData.hourly.hours || [],
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `Penjualan Produk per Jam (${productsToShow.length} produk)`
                        },
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15
                            }
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                title: function(context) {
                                    return 'Jam ' + context[0].label;
                                },
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y + ' qty';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Jumlah Penjualan (qty)'
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Jam Operasional'
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    elements: {
                        line: {
                            borderWidth: 2
                        }
                    }
                }
            });
        }

        // Make updateHourlyChart globally available
        window.updateHourlyChart = updateHourlyChart;

        // Initialize charts on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
        });

        // Force refresh charts function
        window.refreshCharts = function() {
            console.log('Refreshing charts with new data...');

            // Update current chart data with latest from server
            window.currentChartData = {
                products: @json($reportData['topProducts'] ?? []),
                categories: @json($reportData['categorySales'] ?? []),
                hourly: @json($reportData['hourlyProductAnalysis'] ?? [])
            };

            console.log('Updated chart data:', window.currentChartData);

            setTimeout(function() {
                initializeCharts();
            }, 300);
        };

        // Listen for Livewire updates to refresh charts
        document.addEventListener('livewire:updated', function() {
            // Update data before refreshing charts
            window.currentChartData = {
                products: @json($reportData['topProducts'] ?? []),
                categories: @json($reportData['categorySales'] ?? []),
                hourly: @json($reportData['hourlyProductAnalysis'] ?? [])
            };
            window.refreshCharts();
        });

        // Listen for custom charts-updated event
        window.addEventListener('charts-updated', function() {
            window.refreshCharts();
        });

        // Also listen for Livewire navigated event
        document.addEventListener('livewire:navigated', function() {
            window.refreshCharts();
        });

        // Listen for Livewire component updated
        Livewire.on('charts-updated', () => {
            window.refreshCharts();
        });
    </script>
    @endif
</x-filament-panels::page>
