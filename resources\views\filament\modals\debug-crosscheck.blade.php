<div class="space-y-6">
    {{-- <PERSON><PERSON><PERSON> Masal Info --}}
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">J<PERSON><PERSON> Masal Info</h3>
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div><strong>ID:</strong> {{ $debugData['jadwal_masal_info']['id'] }}</div>
            <div><strong>Nama:</strong> {{ $debugData['jadwal_masal_info']['nama_jadwal'] }}</div>
            <div><strong>Tanggal Mulai:</strong> {{ $debugData['jadwal_masal_info']['tanggal_mulai'] }}</div>
            <div><strong>Tanggal Selesai:</strong> {{ $debugData['jadwal_masal_info']['tanggal_selesai'] }}</div>
            <div><strong>Shift ID:</strong> {{ $debugData['jadwal_masal_info']['shift_id'] }}</div>
            <div><strong>Entitas ID:</strong> {{ $debugData['jadwal_masal_info']['entitas_id'] }}</div>
        </div>
    </div>

    {{-- Karyawan Assigned --}}
    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Karyawan yang Ditugaskan ({{ count($debugData['karyawan_assigned']) }})
        </h3>
        <div class="text-sm text-gray-700 dark:text-gray-300">
            {{ implode(', ', $debugData['karyawan_assigned']) }}
        </div>
    </div>

    {{-- Total Schedules Found --}}
    <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Total Jadwal Ditemukan: {{ $debugData['total_schedules_found'] }}
        </h3>
    </div>

    {{-- Schedules by Date --}}
    <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Jadwal Per Tanggal</h3>
        </div>
        <div class="p-4">
            @if(count($debugData['schedules_by_date']) > 0)
                @foreach($debugData['schedules_by_date'] as $date => $schedules)
                    <div class="mb-4 p-3 border border-gray-200 dark:border-gray-700 rounded">
                        <h4 class="font-medium text-gray-900 dark:text-white mb-2">
                            {{ $date }} ({{ count($schedules) }} jadwal)
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 text-sm">
                            @foreach($schedules as $schedule)
                                <div class="bg-gray-50 dark:bg-gray-800 p-2 rounded">
                                    <div><strong>Karyawan:</strong> {{ $schedule['nama_karyawan'] }} (ID: {{ $schedule['karyawan_id'] }})</div>
                                    <div><strong>Shift:</strong> {{ $schedule['shift_id'] }}</div>
                                    <div><strong>Entitas:</strong> {{ $schedule['entitas_id'] }}</div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            @else
                <p class="text-gray-500 dark:text-gray-400">Tidak ada jadwal ditemukan</p>
            @endif
        </div>
    </div>

    {{-- Schedules by Employee --}}
    <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Jadwal Per Karyawan</h3>
        </div>
        <div class="p-4">
            @if(count($debugData['schedules_by_employee']) > 0)
                @foreach($debugData['schedules_by_employee'] as $karyawanId => $employeeData)
                    <div class="mb-4 p-3 border border-gray-200 dark:border-gray-700 rounded">
                        <h4 class="font-medium text-gray-900 dark:text-white mb-2">
                            {{ $employeeData['nama_karyawan'] }} (ID: {{ $karyawanId }}) - {{ count($employeeData['schedules']) }} jadwal
                        </h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2 text-sm">
                            @foreach($employeeData['schedules'] as $schedule)
                                <div class="bg-gray-50 dark:bg-gray-800 p-2 rounded text-center">
                                    <div class="font-medium">{{ $schedule['tanggal_jadwal'] }}</div>
                                    <div class="text-xs text-gray-600 dark:text-gray-400">
                                        S:{{ $schedule['shift_id'] }} E:{{ $schedule['entitas_id'] }}
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            @else
                <p class="text-gray-500 dark:text-gray-400">Tidak ada jadwal ditemukan</p>
            @endif
        </div>
    </div>

    {{-- Raw Data (Collapsible) --}}
    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
        <div class="px-4 py-3 border-b border-yellow-200 dark:border-yellow-800">
            <button onclick="toggleRawData()" class="flex items-center justify-between w-full text-left">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Raw Debug Data</h3>
                <svg id="rawDataIcon" class="w-5 h-5 transform transition-transform" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
        <div id="rawDataContent" class="hidden p-4">
            <pre class="text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-auto max-h-96">{{ json_encode($debugData, JSON_PRETTY_PRINT) }}</pre>
        </div>
    </div>

    <script>
        function toggleRawData() {
            const content = document.getElementById('rawDataContent');
            const icon = document.getElementById('rawDataIcon');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.classList.add('rotate-180');
            } else {
                content.classList.add('hidden');
                icon.classList.remove('rotate-180');
            }
        }
    </script>
</div>
