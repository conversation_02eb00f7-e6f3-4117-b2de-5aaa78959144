<div class="space-y-6">
    {{-- Header --}}
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Debug Status Dinamis</h3>
        <div class="text-sm text-gray-600 dark:text-gray-400">
            Informasi debug untuk troubleshooting status dinamis jadwal kerja
        </div>
    </div>

    {{-- Jadwal Info --}}
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h4 class="text-md font-semibold text-blue-900 dark:text-blue-100 mb-3">Informasi Jadwal</h4>
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div><strong>ID Jadwal:</strong> {{ $debugData['jadwal_info']['id'] }}</div>
            <div><strong>Karyawan ID:</strong> {{ $debugData['jadwal_info']['karyawan_id'] }}</div>
            <div><strong>Tanggal Jadwal:</strong> {{ $debugData['jadwal_info']['tanggal_jadwal'] }}</div>
            <div><strong>Shift ID:</strong> {{ $debugData['jadwal_info']['shift_id'] }}</div>
            <div><strong>Waktu Masuk:</strong> {{ $debugData['jadwal_info']['waktu_masuk'] }}</div>
            <div><strong>Waktu Keluar:</strong> {{ $debugData['jadwal_info']['waktu_keluar'] }}</div>
        </div>
    </div>

    {{-- Absensi Info --}}
    <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
        <h4 class="text-md font-semibold text-green-900 dark:text-green-100 mb-3">Informasi Absensi</h4>
        
        @if($debugData['absensi_info'])
            <div class="grid grid-cols-2 gap-4 text-sm mb-4">
                <div><strong>ID Absensi:</strong> {{ $debugData['absensi_info']['id'] }}</div>
                <div><strong>Jadwal ID:</strong> {{ $debugData['absensi_info']['jadwal_id'] ?? 'NULL' }}</div>
                <div><strong>Karyawan ID:</strong> {{ $debugData['absensi_info']['karyawan_id'] }}</div>
                <div><strong>Tanggal Absensi:</strong> {{ $debugData['absensi_info']['tanggal_absensi'] }}</div>
                <div><strong>Waktu Masuk:</strong> {{ $debugData['absensi_info']['waktu_masuk'] ?? 'NULL' }}</div>
                <div><strong>Waktu Keluar:</strong> {{ $debugData['absensi_info']['waktu_keluar'] ?? 'NULL' }}</div>
                <div><strong>Status DB:</strong> 
                    <span class="px-2 py-1 rounded text-xs font-medium 
                        {{ $debugData['absensi_info']['status'] === 'terlambat' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 
                           ($debugData['absensi_info']['status'] === 'hadir' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 
                            'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200') }}">
                        {{ $debugData['absensi_info']['status'] }}
                    </span>
                </div>
                <div><strong>Periode:</strong> {{ $debugData['absensi_info']['periode'] }}</div>
            </div>
        @else
            <div class="text-red-600 dark:text-red-400 font-medium">
                ❌ Tidak ada data absensi ditemukan
            </div>
        @endif
    </div>

    {{-- Status Calculation --}}
    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <h4 class="text-md font-semibold text-yellow-900 dark:text-yellow-100 mb-3">Perhitungan Status</h4>
        
        @if(isset($debugData['status_calculation']['no_absensi_found']))
            <div class="space-y-2 text-sm">
                <div class="text-red-600 dark:text-red-400">
                    <strong>❌ Tidak ada absensi:</strong> Tidak ditemukan data absensi
                </div>
                <div>
                    <strong>Status Final:</strong> 
                    <span class="px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                        {{ $debugData['status_calculation']['final_status'] }}
                    </span>
                </div>
            </div>
        @else
            <div class="space-y-3 text-sm">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <strong>Ada Waktu Masuk:</strong> 
                        <span class="{{ $debugData['status_calculation']['has_waktu_masuk'] ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                            {{ $debugData['status_calculation']['has_waktu_masuk'] ? '✓ Ya' : '✗ Tidak' }}
                        </span>
                    </div>
                    <div>
                        <strong>Ada Waktu Keluar:</strong> 
                        <span class="{{ $debugData['status_calculation']['has_waktu_keluar'] ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                            {{ $debugData['status_calculation']['has_waktu_keluar'] ? '✓ Ya' : '✗ Tidak' }}
                        </span>
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <strong>Status dari DB:</strong> 
                        <span class="px-2 py-1 rounded text-xs font-medium 
                            {{ $debugData['status_calculation']['status_from_db'] === 'terlambat' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 
                               ($debugData['status_calculation']['status_from_db'] === 'hadir' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 
                                'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200') }}">
                            {{ $debugData['status_calculation']['status_from_db'] }}
                        </span>
                    </div>
                    <div>
                        <strong>Terlambat (Hitung):</strong> 
                        <span class="{{ $debugData['status_calculation']['is_late_calculated'] ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400' }}">
                            {{ $debugData['status_calculation']['is_late_calculated'] ? '✓ Ya' : '✗ Tidak' }}
                        </span>
                    </div>
                </div>
                
                <div class="pt-2 border-t border-yellow-200 dark:border-yellow-700">
                    <strong>Status Final:</strong> 
                    <span class="px-3 py-1 rounded-full text-sm font-medium 
                        {{ str_contains($debugData['status_calculation']['final_status'], 'Terlambat') ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 
                           (str_contains($debugData['status_calculation']['final_status'], 'Tepat Waktu') ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 
                            'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200') }}">
                        {{ $debugData['status_calculation']['final_status'] }}
                    </span>
                </div>
            </div>
        @endif
    </div>

    {{-- Logic Flow --}}
    <div class="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
        <h4 class="text-md font-semibold text-purple-900 dark:text-purple-100 mb-3">Alur Logika</h4>
        <div class="space-y-2 text-sm">
            @if($debugData['absensi_info'])
                <div class="flex items-center space-x-2">
                    <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                    <span>1. ✓ Data absensi ditemukan</span>
                </div>
                
                @if($debugData['status_calculation']['has_waktu_masuk'])
                    <div class="flex items-center space-x-2">
                        <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                        <span>2. ✓ Ada waktu masuk</span>
                    </div>
                    
                    @if($debugData['status_calculation']['status_from_db'] === 'terlambat')
                        <div class="flex items-center space-x-2">
                            <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                            <span>3. Status DB = 'terlambat' → Menggunakan status dari database</span>
                        </div>
                    @elseif($debugData['status_calculation']['status_from_db'] === 'hadir')
                        <div class="flex items-center space-x-2">
                            <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                            <span>3. Status DB = 'hadir' → Menggunakan status dari database</span>
                        </div>
                    @else
                        <div class="flex items-center space-x-2">
                            <span class="w-2 h-2 bg-yellow-500 rounded-full"></span>
                            <span>3. Status DB tidak jelas → Menghitung keterlambatan</span>
                        </div>
                        <div class="flex items-center space-x-2 ml-4">
                            <span class="w-2 h-2 bg-{{ $debugData['status_calculation']['is_late_calculated'] ? 'red' : 'green' }}-500 rounded-full"></span>
                            <span>Hasil perhitungan: {{ $debugData['status_calculation']['is_late_calculated'] ? 'Terlambat' : 'Tepat Waktu' }}</span>
                        </div>
                    @endif
                    
                    @if($debugData['status_calculation']['has_waktu_keluar'])
                        <div class="flex items-center space-x-2">
                            <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                            <span>4. ✓ Ada waktu keluar → Status lengkap</span>
                        </div>
                    @else
                        <div class="flex items-center space-x-2">
                            <span class="w-2 h-2 bg-yellow-500 rounded-full"></span>
                            <span>4. Belum ada waktu keluar → Status "(Belum Keluar)"</span>
                        </div>
                    @endif
                @else
                    <div class="flex items-center space-x-2">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>2. ✗ Tidak ada waktu masuk → Cek kondisi waktu</span>
                    </div>
                @endif
            @else
                <div class="flex items-center space-x-2">
                    <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                    <span>1. ✗ Tidak ada data absensi → Cek cuti/izin/sakit</span>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="w-2 h-2 bg-yellow-500 rounded-full"></span>
                    <span>2. Cek kondisi waktu vs jadwal</span>
                </div>
            @endif
        </div>
    </div>

    {{-- Troubleshooting Tips --}}
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <h4 class="text-md font-semibold text-red-900 dark:text-red-100 mb-3">Tips Troubleshooting</h4>
        <div class="space-y-2 text-sm text-red-800 dark:text-red-200">
            @if(!$debugData['absensi_info'])
                <div>• Tidak ada data absensi: Periksa apakah karyawan sudah melakukan absensi</div>
                <div>• Periksa relasi jadwal_id di tabel absensi</div>
                <div>• Periksa pencarian berdasarkan karyawan_id + tanggal_absensi</div>
            @elseif(!$debugData['status_calculation']['has_waktu_masuk'])
                <div>• Tidak ada waktu masuk: Karyawan belum absen masuk</div>
            @elseif($debugData['status_calculation']['status_from_db'] !== 'terlambat' && $debugData['status_calculation']['is_late_calculated'])
                <div>• Status DB tidak sesuai dengan perhitungan keterlambatan</div>
                <div>• Periksa AbsensiObserver dan AttendanceService</div>
                <div>• Periksa toleransi keterlambatan di shift</div>
            @endif
            
            <div class="pt-2 border-t border-red-200 dark:border-red-700">
                <strong>Langkah selanjutnya:</strong>
                <div>1. Periksa data di tabel absensi secara manual</div>
                <div>2. Periksa konfigurasi shift dan toleransi</div>
                <div>3. Periksa log AttendanceService</div>
            </div>
        </div>
    </div>

    {{-- Raw Data --}}
    <div class="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <button onclick="toggleDebugRawData()" class="flex items-center justify-between w-full text-left">
                <h4 class="text-md font-semibold text-gray-900 dark:text-white">Raw Debug Data</h4>
                <svg id="debugRawDataIcon" class="w-5 h-5 transform transition-transform" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
        <div id="debugRawDataContent" class="hidden p-4">
            <pre class="text-xs bg-gray-100 dark:bg-gray-900 p-3 rounded overflow-auto max-h-96">{{ json_encode($debugData, JSON_PRETTY_PRINT) }}</pre>
        </div>
    </div>

    <script>
        function toggleDebugRawData() {
            const content = document.getElementById('debugRawDataContent');
            const icon = document.getElementById('debugRawDataIcon');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.classList.add('rotate-180');
            } else {
                content.classList.add('hidden');
                icon.classList.remove('rotate-180');
            }
        }
    </script>
</div>
