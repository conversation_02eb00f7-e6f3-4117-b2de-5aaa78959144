<div class="space-y-6">
    {{-- Error <PERSON> and Title --}}
    <div class="text-center">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20">
            <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
        </div>
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                {{ $title ?? '<PERSON><PERSON><PERSON><PERSON>' }}
            </h3>
        </div>
    </div>

    {{-- Error Message --}}
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                    Detail Error
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                    <p>{{ $message ?? 'Terjadi kesalahan yang tidak diketahui.' }}</p>
                </div>
            </div>
        </div>
    </div>

    {{-- Suggestion --}}
    @if(isset($suggestion))
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                    Saran
                </h3>
                <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                    <p>{{ $suggestion }}</p>
                </div>
            </div>
        </div>
    </div>
    @endif

    {{-- Troubleshooting Steps --}}
    <div class="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">
            Langkah Troubleshooting:
        </h3>
        <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-2">
            <li class="flex items-start">
                <span class="flex-shrink-0 w-5 h-5 text-gray-400 mr-2">1.</span>
                <span>Pastikan jadwal masal sudah di-generate dengan benar</span>
            </li>
            <li class="flex items-start">
                <span class="flex-shrink-0 w-5 h-5 text-gray-400 mr-2">2.</span>
                <span>Periksa apakah karyawan memiliki entitas yang valid</span>
            </li>
            <li class="flex items-start">
                <span class="flex-shrink-0 w-5 h-5 text-gray-400 mr-2">3.</span>
                <span>Pastikan shift dan entitas tidak dihapus setelah jadwal dibuat</span>
            </li>
            <li class="flex items-start">
                <span class="flex-shrink-0 w-5 h-5 text-gray-400 mr-2">4.</span>
                <span>Refresh halaman dan coba lagi</span>
            </li>
            <li class="flex items-start">
                <span class="flex-shrink-0 w-5 h-5 text-gray-400 mr-2">5.</span>
                <span>Jika masalah berlanjut, hubungi administrator sistem</span>
            </li>
        </ul>
    </div>

    {{-- Contact Information --}}
    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    Perlu Bantuan?
                </h3>
                <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                    <p>Jika Anda terus mengalami masalah ini, silakan screenshot error ini dan hubungi tim IT atau administrator sistem untuk mendapatkan bantuan lebih lanjut.</p>
                </div>
            </div>
        </div>
    </div>
</div>
