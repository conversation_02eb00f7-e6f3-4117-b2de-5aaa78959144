<div class="space-y-6">
    {{-- Header Information --}}
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Informasi Karyawan
                </h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Nama:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $record->karyawan->nama_lengkap }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">NIP:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $record->karyawan->nip }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Departemen:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $record->karyawan->departemen->nama_departemen ?? '-' }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Divisi:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $record->karyawan->divisi->nama_divisi ?? '-' }}
                        </span>
                    </div>
                </div>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Informasi Jadwal
                </h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Tanggal:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $record->tanggal_jadwal->format('d M Y') }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Shift:</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            {{ $record->shift->nama_shift }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Waktu Kerja:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $record->waktu_masuk }} - {{ $record->waktu_keluar }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Lokasi:</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            {{ $record->entitas->nama_entitas }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Status and Approval --}}
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">Status Jadwal</h4>
            <div class="space-y-2">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                    @if($record->status)
                        @php
                            $statusColors = [
                                'Hadir' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                                'Libur' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                                'Cuti' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
                                'Izin' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
                            ];
                            $colorClass = $statusColors[$record->status] ?? 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
                        @endphp
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $colorClass }}">
                            {{ $record->status }}
                        </span>
                    @else
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                            Belum Diatur
                        </span>
                    @endif
                </div>
                
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Disetujui:</span>
                    @if($record->is_approved)
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            ✓ Ya
                        </span>
                    @else
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            ✗ Tidak
                        </span>
                    @endif
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">Informasi Tambahan</h4>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Supervisor:</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                        {{ $record->supervisor->name ?? '-' }}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Dibuat:</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                        {{ $record->created_at->format('d M Y H:i') }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    {{-- Keterangan --}}
    @if($record->keterangan)
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h4 class="text-md font-semibold text-blue-900 dark:text-blue-100 mb-2">Keterangan</h4>
        <p class="text-sm text-blue-800 dark:text-blue-200">{{ $record->keterangan }}</p>
    </div>
    @endif

    {{-- Absensi Information (if exists) --}}
    @if($record->absensi)
    <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
        <h4 class="text-md font-semibold text-green-900 dark:text-green-100 mb-3">Data Absensi</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-green-700 dark:text-green-300">Jam Masuk:</span>
                    <span class="text-sm font-medium text-green-900 dark:text-green-100">
                        {{ $record->absensi->jam_masuk ? $record->absensi->jam_masuk->format('H:i') : '-' }}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-green-700 dark:text-green-300">Jam Keluar:</span>
                    <span class="text-sm font-medium text-green-900 dark:text-green-100">
                        {{ $record->absensi->jam_keluar ? $record->absensi->jam_keluar->format('H:i') : '-' }}
                    </span>
                </div>
            </div>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-green-700 dark:text-green-300">Status Absensi:</span>
                    <span class="text-sm font-medium text-green-900 dark:text-green-100">
                        {{ $record->absensi->status_absensi ?? '-' }}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-green-700 dark:text-green-300">Lokasi:</span>
                    <span class="text-sm font-medium text-green-900 dark:text-green-100">
                        {{ $record->absensi->lokasi ?? '-' }}
                    </span>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
