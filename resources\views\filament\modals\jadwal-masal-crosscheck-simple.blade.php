<div class="space-y-6">
    {{-- Header Summary --}}
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    <PERSON><PERSON><PERSON> Crosscheck
                </h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Nama Jadwal:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $jadwalMasal->nama_jadwal }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Periode:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $jadwalMasal->tanggal_mulai->format('d M Y') }} -
                            {{ $jadwalMasal->tanggal_selesai->format('d M Y') }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Total Karyawan:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $crosscheckData['summary']['total_karyawan'] }} orang
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Total Hari:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $crosscheckData['summary']['total_days'] }} hari
                        </span>
                    </div>
                </div>
            </div>

            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Status Crosscheck
                </h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Jadwal Diharapkan:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ number_format($crosscheckData['summary']['expected_total']) }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Jadwal Aktual:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ number_format($crosscheckData['summary']['actual_total']) }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Jadwal Hilang:</span>
                        <span
                            class="text-sm font-medium {{ $crosscheckData['summary']['missing_total'] > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400' }}">
                            {{ number_format($crosscheckData['summary']['missing_total']) }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Persentase Lengkap:</span>
                        <span
                            class="text-sm font-medium {{ $crosscheckData['summary']['completion_percentage'] == 100 ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400' }}">
                            {{ $crosscheckData['summary']['completion_percentage'] }}%
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Status Badge --}}
    <div class="text-center">
        @if ($crosscheckData['summary']['is_complete'])
            <div
                class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"></path>
                </svg>
                ✅ Crosscheck BERHASIL - Semua jadwal lengkap
            </div>
        @else
            <div
                class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clip-rule="evenodd"></path>
                </svg>
                ❌ Crosscheck GAGAL - Ada jadwal yang hilang
            </div>
        @endif
    </div>

    {{-- Employee Breakdown Section --}}
    <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-blue-50 dark:bg-blue-900/20">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path
                        d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z">
                    </path>
                </svg>
                Breakdown Per Karyawan
                @if (collect($crosscheckData['employee_breakdown'])->where('is_complete', false)->count() > 0)
                    <span
                        class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                        {{ collect($crosscheckData['employee_breakdown'])->where('is_complete', false)->count() }}
                        bermasalah
                    </span>
                @endif
            </h3>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Karyawan</th>
                        <th
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Diharapkan</th>
                        <th
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Aktual</th>
                        <th
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Hilang</th>
                        <th
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Status</th>
                        <th
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Tanggal Hilang</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach ($crosscheckData['employee_breakdown'] as $employee)
                        <tr
                            class="hover:bg-gray-50 dark:hover:bg-gray-800 {{ !$employee['is_complete'] ? 'bg-red-50 dark:bg-red-900/10' : '' }}">
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="flex flex-col">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $employee['nama_karyawan'] }}
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ $employee['nip'] }}
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ $employee['expected_schedules'] }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ $employee['actual_schedules'] }}
                            </td>
                            <td
                                class="px-4 py-3 whitespace-nowrap text-sm {{ $employee['missing_schedules'] > 0 ? 'text-red-600 dark:text-red-400 font-medium' : 'text-gray-900 dark:text-white' }}">
                                {{ $employee['missing_schedules'] }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                @if ($employee['is_complete'])
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                        ✓ Lengkap
                                    </span>
                                @else
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                        ✗ Tidak Lengkap
                                    </span>
                                @endif
                            </td>
                            <td class="px-4 py-3">
                                @if (count($employee['missing_dates']) > 0)
                                    <div class="space-y-1">
                                        @foreach ($employee['missing_dates'] as $missingDate)
                                            <div class="flex items-center justify-between text-xs">
                                                <span class="text-red-600 dark:text-red-400">
                                                    {{ $missingDate['tanggal_formatted'] }}
                                                    ({{ $missingDate['day_name'] }})
                                                </span>
                                                <button
                                                    onclick="generateSingleSchedule({{ $jadwalMasal->id }}, {{ $employee['karyawan_id'] }}, '{{ $missingDate['tanggal'] }}')"
                                                    class="ml-2 px-2 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors duration-200 generate-btn"
                                                    title="Generate jadwal untuk tanggal ini">
                                                    Generate
                                                </button>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <span class="text-xs text-gray-500 dark:text-gray-400">-</span>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    {{-- Date Breakdown Section --}}
    <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-green-50 dark:bg-green-900/20">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <svg class="w-5 h-5 mr-2 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                        clip-rule="evenodd"></path>
                </svg>
                Breakdown Per Tanggal
                @if (collect($crosscheckData['date_breakdown'])->where('is_complete', false)->count() > 0)
                    <span
                        class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                        {{ collect($crosscheckData['date_breakdown'])->where('is_complete', false)->count() }}
                        bermasalah
                    </span>
                @endif
            </h3>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Tanggal</th>
                        <th
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Diharapkan</th>
                        <th
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Aktual</th>
                        <th
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Hilang</th>
                        <th
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Status</th>
                        <th
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Karyawan Hilang</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach ($crosscheckData['date_breakdown'] as $date)
                        <tr
                            class="hover:bg-gray-50 dark:hover:bg-gray-800 {{ !$date['is_complete'] ? 'bg-red-50 dark:bg-red-900/10' : '' }}">
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="flex flex-col">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $date['tanggal_formatted'] }}
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ $date['day_name'] }}
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ $date['expected_schedules'] }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ $date['actual_schedules'] }}
                            </td>
                            <td
                                class="px-4 py-3 whitespace-nowrap text-sm {{ $date['missing_schedules'] > 0 ? 'text-red-600 dark:text-red-400 font-medium' : 'text-gray-900 dark:text-white' }}">
                                {{ $date['missing_schedules'] }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                @if ($date['is_complete'])
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                        ✓ Lengkap
                                    </span>
                                @else
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                        ✗ Tidak Lengkap
                                    </span>
                                @endif
                            </td>
                            <td class="px-4 py-3">
                                @if (count($date['missing_employees']) > 0)
                                    <div class="space-y-1">
                                        @foreach ($date['missing_employees'] as $missingEmployee)
                                            <div class="flex items-center justify-between text-xs">
                                                <span class="text-red-600 dark:text-red-400">
                                                    {{ $missingEmployee['nama_karyawan'] }}
                                                    ({{ $missingEmployee['nip'] }})
                                                </span>
                                                <button
                                                    onclick="generateSingleSchedule({{ $jadwalMasal->id }}, {{ $missingEmployee['karyawan_id'] }}, '{{ $date['tanggal'] }}')"
                                                    class="ml-2 px-2 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors duration-200 generate-btn"
                                                    title="Generate jadwal untuk karyawan ini">
                                                    Generate
                                                </button>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <span class="text-xs text-gray-500 dark:text-gray-400">-</span>
                                @endif

                                {{-- Debug info - show existing employees --}}
                                @if (app()->environment('local') && isset($date['existing_employees']))
                                    <div class="mt-1 text-xs text-blue-600 dark:text-blue-400">
                                        <strong>Ada:</strong> {{ implode(', ', $date['existing_employees']) }}
                                    </div>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    {{-- Footer Information --}}
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="min-w-0 flex-1">
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Informasi Crosscheck</h3>
                <div class="mt-1 text-sm text-blue-700 dark:text-blue-300">
                    <p>Crosscheck dilakukan pada {{ now()->format('d M Y H:i') }}. Data dibandingkan antara jadwal yang
                        seharusnya dibuat vs jadwal yang benar-benar ter-generate.</p>
                    <p class="mt-1">Jika ada jadwal yang hilang, silakan periksa log generate atau hubungi
                        administrator.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function generateSingleSchedule(jadwalMasalId, karyawanId, tanggalJadwal) {
        const button = event.target;
        const originalText = button.textContent;

        // Disable button and show loading
        button.disabled = true;
        button.textContent = 'Loading...';
        button.classList.remove('bg-blue-500', 'hover:bg-blue-600');
        button.classList.add('bg-gray-400', 'cursor-not-allowed');

        // Get CSRF token
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        fetch(`/jadwal-masal/${jadwalMasalId}/generate-single`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': token,
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    karyawan_id: karyawanId,
                    tanggal_jadwal: tanggalJadwal
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Success - change button to success state
                    button.textContent = '✓ Berhasil';
                    button.classList.remove('bg-gray-400');
                    button.classList.add('bg-green-500');

                    // Show success notification
                    if (window.filament) {
                        window.filament.notifications.send({
                            title: 'Berhasil',
                            body: data.message,
                            color: 'success',
                            duration: 3000
                        });
                    }

                    // Remove the row after a delay
                    setTimeout(() => {
                        button.closest('.flex').style.opacity = '0.5';
                        button.closest('.flex').style.pointerEvents = 'none';
                    }, 1000);

                } else {
                    // Error - restore button and show error
                    button.disabled = false;
                    button.textContent = originalText;
                    button.classList.remove('bg-gray-400', 'cursor-not-allowed');
                    button.classList.add('bg-blue-500', 'hover:bg-blue-600');

                    // Show error notification
                    if (window.filament) {
                        window.filament.notifications.send({
                            title: 'Error',
                            body: data.message,
                            color: 'danger',
                            duration: 5000
                        });
                    } else {
                        alert('Error: ' + data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);

                // Restore button
                button.disabled = false;
                button.textContent = originalText;
                button.classList.remove('bg-gray-400', 'cursor-not-allowed');
                button.classList.add('bg-blue-500', 'hover:bg-blue-600');

                // Show error notification
                if (window.filament) {
                    window.filament.notifications.send({
                        title: 'Error',
                        body: 'Terjadi kesalahan saat generate jadwal',
                        color: 'danger',
                        duration: 5000
                    });
                } else {
                    alert('Terjadi kesalahan saat generate jadwal');
                }
            });
    }
</script>
