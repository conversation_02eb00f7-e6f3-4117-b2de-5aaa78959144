<div class="space-y-6">
    {{-- Header Summary --}}
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    <PERSON><PERSON><PERSON> Crosscheck
                </h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Nama Jadwal:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $jadwalMasal->nama_jadwal }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Periode:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $jadwalMasal->tanggal_mulai->format('d M Y') }} - {{ $jadwalMasal->tanggal_selesai->format('d M Y') }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Total Karyawan:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $crosscheckData['summary']['total_karyawan'] }} orang
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Total Hari:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $crosscheckData['summary']['total_days'] }} hari
                        </span>
                    </div>
                </div>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Status Crosscheck
                </h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Jadwal Diharapkan:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ number_format($crosscheckData['summary']['expected_total']) }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Jadwal Aktual:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ number_format($crosscheckData['summary']['actual_total']) }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Jadwal Hilang:</span>
                        <span class="text-sm font-medium {{ $crosscheckData['summary']['missing_total'] > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400' }}">
                            {{ number_format($crosscheckData['summary']['missing_total']) }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Persentase Lengkap:</span>
                        <span class="text-sm font-medium {{ $crosscheckData['summary']['completion_percentage'] == 100 ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400' }}">
                            {{ $crosscheckData['summary']['completion_percentage'] }}%
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Status Badge --}}
    <div class="text-center">
        @if($crosscheckData['summary']['is_complete'])
            <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                ✅ Crosscheck BERHASIL - Semua jadwal lengkap
            </div>
        @else
            <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                ❌ Crosscheck GAGAL - Ada jadwal yang hilang
            </div>
        @endif
    </div>

    {{-- Tabs for different views - Using CSS-only approach --}}
    <div class="w-full">
        <!-- Hidden radio buttons for tab state -->
        <input type="radio" id="tab-employee" name="crosscheck-tabs" class="hidden" checked>
        <input type="radio" id="tab-date" name="crosscheck-tabs" class="hidden">

        <div class="border-b border-gray-200 dark:border-gray-700">
            <nav class="-mb-px flex space-x-8">
                <label for="tab-employee"
                       class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm cursor-pointer transition-colors duration-200 border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                    Per Karyawan
                    @if(collect($crosscheckData['employee_breakdown'])->where('is_complete', false)->count() > 0)
                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            {{ collect($crosscheckData['employee_breakdown'])->where('is_complete', false)->count() }}
                        </span>
                    @endif
                </label>
                <label for="tab-date"
                       class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm cursor-pointer transition-colors duration-200 border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                    Per Tanggal
                    @if(collect($crosscheckData['date_breakdown'])->where('is_complete', false)->count() > 0)
                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            {{ collect($crosscheckData['date_breakdown'])->where('is_complete', false)->count() }}
                        </span>
                    @endif
                </label>
            </nav>
        </div>

        <style>
            /* Active tab styling */
            #tab-employee:checked ~ div nav label[for="tab-employee"] {
                border-color: rgb(59 130 246) !important;
                color: rgb(37 99 235) !important;
            }

            #tab-date:checked ~ div nav label[for="tab-date"] {
                border-color: rgb(59 130 246) !important;
                color: rgb(37 99 235) !important;
            }

            /* Dark mode active tab styling */
            .dark #tab-employee:checked ~ div nav label[for="tab-employee"] {
                color: rgb(96 165 250) !important;
            }

            .dark #tab-date:checked ~ div nav label[for="tab-date"] {
                color: rgb(96 165 250) !important;
            }

            /* Tab content visibility */
            .tab-content {
                display: none;
            }

            #tab-employee:checked ~ .employee-content {
                display: block !important;
            }

            #tab-date:checked ~ .date-content {
                display: block !important;
            }
        </style>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Ensure the first tab is active by default
                const employeeTab = document.getElementById('tab-employee');
                if (employeeTab) {
                    employeeTab.checked = true;
                }

                // Add click event listeners to prevent any modal closing
                const tabLabels = document.querySelectorAll('label[for^="tab-"]');
                tabLabels.forEach(label => {
                    label.addEventListener('click', function(e) {
                        e.stopPropagation();
                        e.preventDefault();

                        // Get the target radio button
                        const targetId = this.getAttribute('for');
                        const targetRadio = document.getElementById(targetId);

                        if (targetRadio) {
                            targetRadio.checked = true;
                        }
                    });
                });
            });
        </script>

        {{-- Employee Breakdown Tab --}}
        <div class="employee-content tab-content mt-6">
            <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Breakdown Per Karyawan</h3>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-800">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Karyawan</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Diharapkan</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Aktual</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Hilang</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Tanggal Hilang</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($crosscheckData['employee_breakdown'] as $employee)
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex flex-col">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                {{ $employee['nama_karyawan'] }}
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                {{ $employee['nip'] }}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        {{ $employee['expected_schedules'] }}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        {{ $employee['actual_schedules'] }}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm {{ $employee['missing_schedules'] > 0 ? 'text-red-600 dark:text-red-400 font-medium' : 'text-gray-900 dark:text-white' }}">
                                        {{ $employee['missing_schedules'] }}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        @if($employee['is_complete'])
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                ✓ Lengkap
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                                ✗ Tidak Lengkap
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-4 py-3">
                                        @if(count($employee['missing_dates']) > 0)
                                            <div class="text-xs text-red-600 dark:text-red-400">
                                                @foreach($employee['missing_dates'] as $missingDate)
                                                    <div>{{ $missingDate['tanggal_formatted'] }} ({{ $missingDate['day_name'] }})</div>
                                                @endforeach
                                            </div>
                                        @else
                                            <span class="text-xs text-gray-500 dark:text-gray-400">-</span>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        {{-- Date Breakdown Tab --}}
        <div class="date-content tab-content mt-6">
            <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Breakdown Per Tanggal</h3>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-800">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Tanggal</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Diharapkan</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Aktual</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Hilang</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Karyawan Hilang</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($crosscheckData['date_breakdown'] as $date)
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex flex-col">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                {{ $date['tanggal_formatted'] }}
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                {{ $date['day_name'] }}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        {{ $date['expected_schedules'] }}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        {{ $date['actual_schedules'] }}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm {{ $date['missing_schedules'] > 0 ? 'text-red-600 dark:text-red-400 font-medium' : 'text-gray-900 dark:text-white' }}">
                                        {{ $date['missing_schedules'] }}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        @if($date['is_complete'])
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                ✓ Lengkap
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                                ✗ Tidak Lengkap
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-4 py-3">
                                        @if(count($date['missing_employees']) > 0)
                                            <div class="text-xs text-red-600 dark:text-red-400">
                                                @foreach($date['missing_employees'] as $missingEmployee)
                                                    <div>{{ $missingEmployee['nama_karyawan'] }} ({{ $missingEmployee['nip'] }})</div>
                                                @endforeach
                                            </div>
                                        @else
                                            <span class="text-xs text-gray-500 dark:text-gray-400">-</span>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    {{-- Footer Information --}}
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="min-w-0 flex-1">
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Informasi Crosscheck</h3>
                <div class="mt-1 text-sm text-blue-700 dark:text-blue-300">
                    <p>Crosscheck dilakukan pada {{ now()->format('d M Y H:i') }}. Data dibandingkan antara jadwal yang seharusnya dibuat vs jadwal yang benar-benar ter-generate.</p>
                    <p class="mt-1">Jika ada jadwal yang hilang, silakan periksa log generate atau hubungi administrator.</p>
                </div>
            </div>
        </div>
    </div>
</div>
