<div class="space-y-6">
    {{-- Header Information --}}
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400"><PERSON><PERSON></h3>
                <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $jadwalMasal->nama_jadwal }}</p>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400">Periode</h3>
                <p class="text-lg font-semibold text-gray-900 dark:text-white">
                    {{ $jadwalMasal->tanggal_mulai->format('d M Y') }} - {{ $jadwalMasal->tanggal_selesai->format('d M Y') }}
                </p>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Jadwal</h3>
                <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $schedules->count() }} jadwal</p>
            </div>
        </div>
    </div>

    {{-- Statistics --}}
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
            <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Karyawan</div>
            <div class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                {{ $schedules->unique('karyawan_id')->count() }}
            </div>
        </div>
        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
            <div class="text-green-600 dark:text-green-400 text-sm font-medium">Hari Kerja</div>
            <div class="text-2xl font-bold text-green-900 dark:text-green-100">
                {{ $schedules->unique('tanggal_jadwal')->count() }}
            </div>
        </div>
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
            <div class="text-yellow-600 dark:text-yellow-400 text-sm font-medium">Shift</div>
            <div class="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                @if($jadwalMasal->shift)
                    {{ $jadwalMasal->shift->nama_shift }}
                @else
                    <span class="text-red-600 dark:text-red-400">Tidak ada shift</span>
                @endif
            </div>
        </div>
        <div class="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-3">
            <div class="text-purple-600 dark:text-purple-400 text-sm font-medium">Lokasi</div>
            <div class="text-2xl font-bold text-purple-900 dark:text-purple-100">
                @if($jadwalMasal->entitas)
                    {{ $jadwalMasal->entitas->nama }}
                @else
                    <span class="text-red-600 dark:text-red-400">Tidak ada entitas</span>
                @endif
            </div>
        </div>
    </div>

    {{-- Schedules Table --}}
    <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Daftar Jadwal Kerja</h3>
        </div>
        
        @if($schedules->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Karyawan
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Tanggal
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Waktu Kerja
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Supervisor
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($schedules->sortBy(['karyawan.nama_lengkap', 'tanggal_jadwal']) as $schedule)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="flex flex-col">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            @if($schedule->karyawan)
                                                {{ $schedule->karyawan->nama_lengkap }}
                                            @else
                                                <span class="text-red-500">Karyawan tidak ditemukan</span>
                                            @endif
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $schedule->karyawan->nip ?? '-' }}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 dark:text-white">
                                        {{ $schedule->tanggal_jadwal->format('d M Y') }}
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ $schedule->tanggal_jadwal->format('l') }}
                                    </div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 dark:text-white">
                                        {{ $schedule->waktu_masuk }} - {{ $schedule->waktu_keluar }}
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ $schedule->shift->nama_shift ?? 'Tidak ada shift' }}
                                    </div>
                                    @if($schedule->entitas)
                                        <div class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                            📍 {{ $schedule->entitas->nama }}
                                        </div>
                                    @else
                                        <div class="text-xs text-red-400 dark:text-red-500 mt-1">
                                            📍 Tidak ada lokasi
                                        </div>
                                    @endif
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    @php
                                        $statusDinamis = $schedule->getStatusDinamis();
                                        $statusColor = $schedule->getStatusColor();

                                        $colorClasses = [
                                            'success' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                                            'warning' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                                            'danger' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
                                            'info' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
                                            'secondary' => 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
                                        ];

                                        $colorClass = $colorClasses[$statusColor] ?? $colorClasses['secondary'];
                                    @endphp

                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $colorClass }}">
                                        {{ $statusDinamis }}
                                    </span>

                                    <div class="mt-1">
                                        @if($schedule->is_approved)
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                ✓ Disetujui
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                                ✗ Belum Disetujui
                                            </span>
                                        @endif
                                    </div>

                                    {{-- Additional info for attendance details --}}
                                    @if($schedule->absensi)
                                        <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                            @if($schedule->absensi->waktu_masuk)
                                                Masuk: {{ $schedule->absensi->waktu_masuk->format('H:i') }}
                                            @endif
                                            @if($schedule->absensi->waktu_keluar)
                                                | Keluar: {{ $schedule->absensi->waktu_keluar->format('H:i') }}
                                            @endif
                                            @if(app()->environment('local'))
                                                <br>Status DB: {{ $schedule->absensi->status }}
                                            @endif
                                        </div>
                                    @endif
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    {{ $schedule->supervisor->name ?? '-' }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="px-4 py-8 text-center">
                <div class="text-gray-500 dark:text-gray-400">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6-4h6" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Belum ada jadwal kerja</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Jadwal masal belum di-generate atau tidak ada jadwal yang sesuai kriteria.
                    </p>
                </div>
            </div>
        @endif
    </div>

    {{-- Footer Information --}}
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="min-w-0 flex-1">
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Informasi</h3>
                <div class="mt-1 text-sm text-blue-700 dark:text-blue-300">
                    <p>Jadwal kerja ini dibuat otomatis dari jadwal masal pada {{ $jadwalMasal->generated_at?->format('d M Y H:i') ?? 'belum di-generate' }}.</p>
                    <p class="mt-1">Untuk mengubah jadwal individual, silakan gunakan menu Jadwal Kerja.</p>
                </div>
            </div>
        </div>
    </div>
</div>
