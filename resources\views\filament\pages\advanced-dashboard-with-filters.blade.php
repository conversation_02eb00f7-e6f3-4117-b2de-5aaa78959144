<x-filament-panels::page>
    <div class="dashboard-container">
        <div class="space-y-6">
            {{-- Modern Filter Section --}}
            <div class="bg-white border border-gray-100 shadow-md dark:bg-gray-900 rounded-3xl dark:border-gray-700"
                style="overflow: visible;">
                {{-- Header --}}
                <div
                    class="px-8 py-6 bg-yellow-300 border-b border-gray-100 rounded-t-3xl bg-gradient-to-br from-yellow-300 via-yellow-300 to-green-600 dark:from-gray-800 dark:to-gray-700 dark:border-gray-600">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div
                                class="flex items-center justify-center w-16 h-16 bg-white shadow-lg dark:bg-gray-700 rounded-2xl">
                                <svg class="w-8 h-8 text-emerald-600 dark:text-emerald-400" fill="none"
                                    stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                    </path>
                                </svg>
                            </div>
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                                    Filter Periode Dashboard
                                </h1>
                                <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">
                                    Pilih periode untuk melihat data dashboard payroll & gaji
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            @php
                                $workPeriodInfo = \App\Models\CompanySettings::getWorkPeriodInfo();
                                $dateRange = $this->getFilteredDateRange();
                            @endphp
                            <div
                                class="px-4 py-3 text-right bg-white border border-gray-100 shadow-sm dark:bg-gray-700 dark:border-gray-600 rounded-xl">
                                <div
                                    class="text-xs font-semibold tracking-wide uppercase text-emerald-600 dark:text-emerald-400">
                                    Periode Aktif
                                </div>
                                <div class="text-lg font-bold text-gray-900 dark:text-white">
                                    {{ $dateRange['start']->format('d M Y') }} -
                                    {{ $dateRange['end']->format('d M Y') }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    Sistem: {{ $workPeriodInfo['start_date'] }}-{{ $workPeriodInfo['end_date'] }} setiap
                                    bulan
                                </div>
                            </div>
                            <button wire:click="$dispatch('updateCharts')"
                                class="inline-flex items-center px-6 py-3 text-sm font-bold text-white transition-all duration-300 transform border border-transparent shadow-lg bg-gradient-to-r from-emerald-600 to-teal-600 rounded-2xl hover:from-emerald-700 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 hover:scale-105">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                                    </path>
                                </svg>
                                Refresh Data
                            </button>
                        </div>
                    </div>
                </div>

                {{-- Filter Content --}}
                <div class="p-6 ">
                    <div class="max-w-2xl">
                        <div class="mb-4">
                            <h3 class="mb-2 text-lg font-bold text-gray-900 dark:text-white">Pilih Periode</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Tentukan rentang tanggal untuk melihat
                                data dashboard</p>
                        </div>
                        <div x-data="{
                            handleDateRangeSelected(detail) {
                                console.log('Date range selected:', detail);
                                $wire.set('filters.start_date', detail.start);
                                $wire.set('filters.end_date', detail.end);
                                $wire.set('filters.time_start', detail.timeStart);
                                $wire.set('filters.time_end', detail.timeEnd);
                                $wire.set('filters.period_type', 'custom');
                            }
                        }" @daterange-selected="handleDateRangeSelected($event.detail)">
                            <x-modern-date-range-picker :start-date="$filters['start_date'] ?? null" :end-date="$filters['end_date'] ?? null"
                                placeholder="Pilih rentang tanggal custom" />
                        </div>
                    </div>
                </div>
            </div>

            {{-- Widgets Section --}}
            <div class="space-y-6">
                @foreach ($this->getWidgets() as $widget)
                    @livewire($widget, ['lazy' => false], key($widget))
                @endforeach
            </div>

            {{-- Auto-refresh and interaction scripts --}}
            <script>
                document.addEventListener('livewire:init', () => {
                    Livewire.on('updateCharts', () => {
                        // Enhanced loading animation for refresh button
                        const refreshButton = document.querySelector('button[wire\\:click*="updateCharts"]');
                        if (refreshButton) {
                            const icon = refreshButton.querySelector('svg');
                            if (icon) {
                                icon.classList.add('loading-spin');
                                setTimeout(() => {
                                    icon.classList.remove('loading-spin');
                                }, 1000);
                            }

                            // Add pulse effect
                            refreshButton.style.transform = 'scale(0.95)';
                            refreshButton.style.opacity = '0.8';
                            setTimeout(() => {
                                refreshButton.style.transform = 'scale(1)';
                                refreshButton.style.opacity = '1';
                            }, 200);
                        }

                        // Trigger refresh for all widgets
                        Livewire.dispatch('$refresh');
                    });

                    // Add ripple effect to filter buttons
                    document.addEventListener('click', function(e) {
                        if (e.target.closest('button[wire\\:click*="period_type"]')) {
                            const button = e.target.closest('button');
                            const ripple = document.createElement('span');
                            const rect = button.getBoundingClientRect();
                            const size = Math.max(rect.width, rect.height);
                            const x = e.clientX - rect.left - size / 2;
                            const y = e.clientY - rect.top - size / 2;

                            ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.3);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;

                            button.style.position = 'relative';
                            button.style.overflow = 'hidden';
                            button.appendChild(ripple);

                            setTimeout(() => {
                                ripple.remove();
                            }, 600);
                        }
                    });

                    // Auto-refresh every 30 seconds if enabled
                    @if (\App\Models\CompanySettings::get('dashboard_auto_refresh', false))
                        setInterval(() => {
                            Livewire.dispatch('$refresh');
                        }, {{ \App\Models\CompanySettings::get('dashboard_refresh_interval', 30) * 1000 }});
                    @endif
                });
            </script>

            {{-- Enhanced CSS for modern dashboard --}}
            <style>
                /* Enhanced ripple effect */
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }

                /* Gradient animation for refresh button */
                @keyframes gradientShift {
                    0% {
                        background-position: 0% 50%;
                    }

                    50% {
                        background-position: 100% 50%;
                    }

                    100% {
                        background-position: 0% 50%;
                    }
                }

                /* Enhanced button animations */
                button[class*="bg-gradient-to-r"] {
                    background-size: 200% 200%;
                    animation: gradientShift 3s ease infinite;
                    position: relative;
                    overflow: hidden;
                }

                button[class*="bg-gradient-to-r"]:before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                    transition: left 0.5s;
                }

                button[class*="bg-gradient-to-r"]:hover:before {
                    left: 100%;
                }

                /* Enhanced card shadows */
                .shadow-2xl {
                    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
                }

                /* Dark mode shadow adjustments */
                @media (prefers-color-scheme: dark) {
                    .shadow-2xl {
                        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1);
                    }
                }

                /* Smooth transitions for all interactive elements */
                button,
                .transition-all {
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                /* Enhanced focus states */
                button:focus {
                    outline: 2px solid #10b981;
                    outline-offset: 2px;
                }

                /* Loading animation for refresh button */
                .loading-spin {
                    animation: spin 1s linear infinite;
                }

                @keyframes spin {
                    from {
                        transform: rotate(0deg);
                    }

                    to {
                        transform: rotate(360deg);
                    }
                }

                /* Enhanced gradient backgrounds */
                .bg-gradient-to-r {
                    background-image: linear-gradient(to right, var(--tw-gradient-stops));
                }

                .bg-gradient-to-br {
                    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
                }

                /* Modern glass effect */
                .glass-effect {
                    backdrop-filter: blur(10px);
                    background: rgba(255, 255, 255, 0.8);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                }

                /* Dark mode glass effect */
                @media (prefers-color-scheme: dark) {
                    .glass-effect {
                        background: rgba(31, 41, 55, 0.8);
                        border: 1px solid rgba(255, 255, 255, 0.1);
                    }
                }

                /* Enhanced hover effects for cards */
                .hover-lift:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
                }

                /* Dark mode hover effects */
                @media (prefers-color-scheme: dark) {
                    .hover-lift:hover {
                        box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.6);
                    }
                }
            </style>
        </div>
</x-filament-panels::page>
