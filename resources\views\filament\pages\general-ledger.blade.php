<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            {{ $this->form }}
        </div>

        <!-- Account Summary -->
        @if($this->getSelectedAccount())
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    Ringkasan Akun: {{ $this->getSelectedAccount()->kode_akun }} - {{ $this->getSelectedAccount()->nama_akun }}
                </h3>

                @php
                    $summary = $this->getAccountSummary();
                @endphp

                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                        <div class="text-sm font-medium text-blue-600 dark:text-blue-400"><PERSON><PERSON></div>
                        <div class="text-lg font-semibold text-blue-900 dark:text-blue-100">
                            Rp {{ number_format($summary['opening_balance'] ?? 0, 0, ',', '.') }}
                        </div>
                    </div>

                    <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                        <div class="text-sm font-medium text-green-600 dark:text-green-400">Total Debit</div>
                        <div class="text-lg font-semibold text-green-900 dark:text-green-100">
                            Rp {{ number_format($summary['total_debit'] ?? 0, 0, ',', '.') }}
                        </div>
                    </div>

                    <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                        <div class="text-sm font-medium text-red-600 dark:text-red-400">Total Credit</div>
                        <div class="text-lg font-semibold text-red-900 dark:text-red-100">
                            Rp {{ number_format($summary['total_credit'] ?? 0, 0, ',', '.') }}
                        </div>
                    </div>

                    <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                        <div class="text-sm font-medium text-purple-600 dark:text-purple-400">Saldo Akhir</div>
                        <div class="text-lg font-semibold text-purple-900 dark:text-purple-100">
                            Rp {{ number_format($summary['ending_balance'] ?? 0, 0, ',', '.') }}
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Journal Entries Table -->
        @if($this->account_id && $this->start_date && $this->end_date)
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                        Detail Transaksi
                    </h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        Periode: {{ \Carbon\Carbon::parse($this->start_date)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($this->end_date)->format('d/m/Y') }}
                    </p>

                </div>

                <div class="p-6" wire:key="table-{{ $this->account_id }}-{{ $this->start_date }}-{{ $this->end_date }}">
                    {{ $this->table }}
                </div>
                </div>
            </div>
        @else
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="text-center py-8">
                    <div class="text-gray-400 dark:text-gray-500 mb-2">
                        <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-1">
                        Pilih Akun dan Periode
                    </h3>
                    <p class="text-gray-500 dark:text-gray-400">
                        Silakan pilih akun dan periode tanggal untuk melihat laporan buku besar
                    </p>
                </div>
            </div>
        @endif
    </div>
</x-filament-panels::page>
