<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Import Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            {{ $this->form }}

            <div class="mt-6 flex justify-end">
                <x-filament::button
                    wire:click="import"
                    color="primary"
                    size="lg"
                    icon="heroicon-o-arrow-up-tray">
                    Import Data Penjualan
                </x-filament::button>
            </div>
        </div>

        <!-- Recent Imports -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Import Terakhir
            </h3>

            <div class="text-sm text-gray-500 dark:text-gray-400">
                <p>Fitur riwayat import akan segera tersedia.</p>
                <p>Untuk saat ini, Anda dapat melihat hasil import di menu Transaksi Penjualan.</p>
            </div>
        </div>

        <!-- Help Section -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
            <h3 class="text-lg font-medium text-blue-900 dark:text-blue-100 mb-4">
                <x-heroicon-o-information-circle class="w-5 h-5 inline mr-2"/>
                Panduan Import
            </h3>

            <div class="text-sm text-blue-800 dark:text-blue-200 space-y-2">
                <p><strong>Langkah-langkah import:</strong></p>
                <ol class="list-decimal list-inside space-y-1 ml-4">
                    <li>Pilih gudang asal penjualan</li>
                    <li>Upload file CSV atau Excel dengan format yang sesuai</li>
                    <li>Klik tombol "Import Data Penjualan"</li>
                    <li>Tunggu notifikasi konfirmasi selesai</li>
                </ol>

                <p class="mt-4"><strong>Catatan penting:</strong></p>
                <ul class="list-disc list-inside space-y-1 ml-4">
                    <li>File akan diproses secara background untuk performa yang lebih baik</li>
                    <li>Sistem akan otomatis membuat jurnal akuntansi untuk setiap transaksi</li>
                    <li>Stok inventory akan otomatis dikurangi sesuai penjualan</li>
                    <li>Pastikan kode produk di file sesuai dengan data master produk</li>
                </ul>
            </div>
        </div>
    </div>
</x-filament-panels::page>
