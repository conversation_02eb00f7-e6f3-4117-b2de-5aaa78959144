
<x-filament::page>
    <div class="grid grid-cols-1 gap-6 p-4 mb-6 lg:grid-cols-3">
        <div class="flex flex-col gap-2">
            <label class="text-sm font-semibold text-gray-700 dark:text-gray-300">Bulan:</label>
            <input type="month" wire:model="bulan" class="w-full p-2 border border-gray-300 rounded-md dark:border-gray-600 focus:outline-none focus:ring focus:ring-blue-300 dark:bg-gray-700 dark:text-white" />
        </div>

        <div class="flex flex-col gap-2">
            <label class="text-sm font-semibold text-gray-700 dark:text-gray-300">Karyawan:</label>
            <select wire:model="karyawan_id" class="w-full p-2 border border-gray-300 rounded-md dark:border-gray-600 focus:outline-none focus:ring focus:ring-blue-300 dark:bg-gray-700 dark:text-white">
                <option value=""><PERSON><PERSON><PERSON></option>
                @foreach (App\Models\Karyawan::all() as $karyawan)
                    <option value="{{ $karyawan->id }}">{{ $karyawan->nama_lengkap }}</option>
                @endforeach
            </select>
        </div>

        <div class="flex flex-col gap-2">
            <label class="text-sm font-semibold text-gray-700 dark:text-gray-300">Waktu Masuk Default:</label>
            <input type="time" wire:model="waktu_masuk_default" class="w-full p-2 border border-gray-300 rounded-md dark:border-gray-600 focus:outline-none focus:ring focus:ring-blue-300 dark:bg-gray-700 dark:text-white" />
        </div>

        <div class="flex flex-col gap-2">
            <label class="text-sm font-semibold text-gray-700 dark:text-gray-300">Waktu Keluar Default:</label>
            <input type="time" wire:model="waktu_keluar_default" class="w-full p-2 border border-gray-300 rounded-md dark:border-gray-600 focus:outline-none focus:ring focus:ring-blue-300 dark:bg-gray-700 dark:text-white" />
        </div>

        <div class="flex items-center justify-end mt-4 lg:mt-0">
            <button wire:click="generateGrid" class="px-4 py-2 font-semibold text-white transition duration-150 bg-blue-600 rounded-md hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800">Generate Jadwal</button>
        </div>
    </div>

    @if (count($jadwal) > 0)
        <div class="mt-6 overflow-x-auto">
            <table class="w-full border border-gray-300 dark:border-gray-600">
                <thead class="bg-gray-100 dark:bg-gray-700">
                    <tr>
                        <th class="p-2 text-left border border-gray-300 dark:border-gray-600 dark:text-white">Tanggal</th>
                        <th class="p-2 text-left border border-gray-300 dark:border-gray-600 dark:text-white">Waktu Masuk</th>
                        <th class="p-2 text-left border border-gray-300 dark:border-gray-600 dark:text-white">Waktu Keluar</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800">
                    @foreach ($jadwal as $index => $item)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="p-2 border border-gray-300 dark:border-gray-600 dark:text-white">{{ $item['tanggal_jadwal'] }}</td>
                            <td class="p-2 border border-gray-300 dark:border-gray-600">
                                <input type="time" wire:model="jadwal.{{ $index }}.waktu_masuk" class="w-full p-1 border border-gray-300 rounded-md dark:border-gray-600 focus:outline-none focus:ring focus:ring-blue-300 dark:bg-gray-700 dark:text-white" />
                            </td>
                            <td class="p-2 border border-gray-300 dark:border-gray-600">
                                <input type="time" wire:model="jadwal.{{ $index }}.waktu_keluar" class="w-full p-1 border border-gray-300 rounded-md dark:border-gray-600 focus:outline-none focus:ring focus:ring-blue-300 dark:bg-gray-700 dark:text-white" />
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <div class="flex justify-end mt-6">
            <button wire:click="save" class="px-4 py-2 font-semibold text-white transition duration-150 bg-green-600 rounded-md hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800">Simpan Jadwal</button>
        </div>
    @endif
</x-filament::page>
