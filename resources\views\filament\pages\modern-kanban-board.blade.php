<x-filament-panels::page>
    <!-- Header with Project Selection and Filters -->
    <div class="space-y-6">
        <!-- Project Selection -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex items-center space-x-4">
                    <div class="min-w-0 flex-1">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Pilih Kegiatan
                        </label>
                        <select wire:model.live="selectedProjectId"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">Pilih Kegiatan...</option>
                            @foreach($projects as $project)
                                <option value="{{ $project->id }}">{{ $project->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                @if($selectedProject)
                    <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                        <x-heroicon-m-briefcase class="w-4 h-4" />
                        <span>{{ $selectedProject->name }}</span>
                        <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded text-xs">
                            {{ $selectedProject->tasks->count() }} tugas
                        </span>
                    </div>
                @endif
            </div>
        </div>

        <!-- Advanced Filters -->
        @if($selectedProject)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div class="flex flex-col lg:flex-row lg:items-center gap-4">
                    <!-- Search -->
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Cari Tugas</label>
                        <input type="text"
                               wire:model.live.debounce.300ms="searchTerm"
                               placeholder="Cari berdasarkan nama atau deskripsi..."
                               class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                    </div>

                    <!-- Assignee Filter -->
                    <div class="min-w-0 lg:w-48">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Assignee</label>
                        <select wire:model.live="filterAssignee"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">Semua assignee</option>
                            @foreach($teamMembers as $member)
                                <option value="{{ $member->id }}">{{ $member->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Due Date Filter -->
                    <div class="min-w-0 lg:w-48">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Batas Waktu</label>
                        <select wire:model.live="filterDueDate"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">Semua tanggal</option>
                            <option value="overdue">Terlambat</option>
                            <option value="today">Hari ini</option>
                            <option value="this_week">Minggu ini</option>
                            <option value="no_due_date">Tidak ada batas waktu</option>
                        </select>
                    </div>

                    <!-- Swimlane Toggle -->
                    <div class="min-w-0 lg:w-48">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Group By</label>
                        <select wire:model.live="swimlaneBy"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="none">Tidak ada pengelompokan</option>
                            <option value="assignee">Berdasarkan assignee</option>
                            <option value="priority">Berdasarkan prioritas</option>
                        </select>
                    </div>

                    <!-- Clear Filters -->
                    <div class="flex items-end">
                        <button wire:click="clearFilters"
                                class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500">
                            Bersihkan
                        </button>
                    </div>
                </div>
            </div>
        @endif

        <!-- Kanban Board -->
        @if($selectedProject)
            @if($swimlaneBy === 'none')
                <!-- Standard Kanban View -->
                <div x-data="enhancedDragDropHandler()"
                     x-init="init()"
                     @task-moved.window="init()"
                     @task-updated.window="init()"
                     @refresh-board.window="init()"
                     class="relative overflow-x-auto pb-6">

                    <div class="inline-flex gap-6 pb-2 min-w-full">
                        @foreach ($taskStatuses as $status)
                            <div class="kanban-column bg-gray-50 dark:bg-gray-900 rounded-xl border border-gray-200 dark:border-gray-700 flex flex-col"
                                 style="width: calc(85vw - 2rem); min-width: 320px; max-width: 380px; @media (min-width: 640px) { width: calc((100vw - 6rem) / 2); } @media (min-width: 1024px) { width: calc((100vw - 8rem) / 3); } @media (min-width: 1280px) { width: calc((100vw - 10rem) / 4); }"
                                 data-status-id="{{ $status->id }}">

                                <!-- Column Header -->
                                <div class="px-4 py-3 rounded-t-xl border-b border-gray-200 dark:border-gray-700"
                                     style="background: linear-gradient(135deg, {{ $status->color }}15, {{ $status->color }}25);">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-3 h-3 rounded-full" style="background-color: {{ $status->color }};"></div>
                                            <h3 class="font-semibold text-gray-900 dark:text-white">{{ $status->name }}</h3>
                                            <span class="px-2 py-1 text-xs font-medium bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full">
                                                {{ $status->current_count }}
                                            </span>
                                        </div>

                                        @if($showWipLimits && $status->wip_limit < 999)
                                            <div class="flex items-center space-x-1">
                                                <span class="text-xs text-gray-500 dark:text-gray-400">WIP:</span>
                                                <span class="text-xs font-medium {{ $status->wip_status === 'exceeded' ? 'text-red-600 dark:text-red-400' : 'text-gray-600 dark:text-gray-400' }}">
                                                    {{ $status->current_count }}/{{ $status->wip_limit }}
                                                </span>
                                            </div>
                                        @endif
                                    </div>

                                    @if($showWipLimits && $status->wip_limit < 999)
                                        <div class="mt-2">
                                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                                                <div class="h-1 rounded-full transition-all duration-300 {{ $status->wip_status === 'exceeded' ? 'bg-red-500' : 'bg-blue-500' }}"
                                                     style="width: {{ min(($status->current_count / $status->wip_limit) * 100, 100) }}%"></div>
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <!-- Tasks Container -->
                                <div class="p-3 flex flex-col gap-3 h-[calc(100vh-24rem)] overflow-y-auto">
                                    @forelse ($status->tasks as $task)
                                        @include('filament.components.enhanced-task-card', ['task' => $task])
                                    @empty
                                        <div class="flex flex-col items-center justify-center py-8 text-gray-400 dark:text-gray-500">
                                            <x-heroicon-o-plus class="w-8 h-8 mb-2" />
                                            <p class="text-sm">Drop tugas di sini</p>
                                        </div>
                                    @endforelse
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @else
                <!-- Swimlane View -->
                <div class="space-y-6">
                    @foreach($this->getFilteredTasksByAssignee() as $swimlane)
                        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                            <!-- Swimlane Header -->
                            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 rounded-t-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                        {{ substr($swimlane->user->name, 0, 2) }}
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900 dark:text-white">{{ $swimlane->user->name }}</h3>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $swimlane->user->email }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Swimlane Columns -->
                            <div class="p-4">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    @foreach($swimlane->statuses as $status)
                                        <div class="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
                                            <div class="px-3 py-2 border-b border-gray-200 dark:border-gray-700">
                                                <div class="flex items-center justify-between">
                                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ $status->name }}</h4>
                                                    <span class="text-xs text-gray-500 dark:text-gray-400">{{ $status->current_count }}</span>
                                                </div>
                                            </div>
                                            <div class="p-2 space-y-2 max-h-64 overflow-y-auto">
                                                @foreach($status->tasks as $task)
                                                    @include('filament.components.enhanced-task-card', ['task' => $task, 'compact' => true])
                                                @endforeach
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif
        @else
            <!-- No Project Selected -->
            <div class="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400 gap-4">
                <div class="flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 p-6">
                    <x-heroicon-o-squares-2x2 class="w-16 h-16 text-gray-400 dark:text-gray-500" />
                </div>
                <h2 class="text-xl font-medium text-gray-600 dark:text-gray-300">Pilih Kegiatan</h2>
                <p class="text-sm text-gray-500 dark:text-gray-400 text-center">
                    Pilih kegiatan dari dropdown di atas untuk melihat papan kanban yang ditingkatkan
                </p>
            </div>
        @endif
    </div>

    <!-- Enhanced Drag and Drop Script -->
    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('enhancedDragDropHandler', () => ({
                draggingTask: null,
                dragOverColumn: null,

                init() {
                    this.$nextTick(() => {
                        this.removeAllEventListeners();
                        this.attachAllEventListeners();
                    });
                },

                removeAllEventListeners() {
                    const tasks = document.querySelectorAll('.enhanced-task-card');
                    tasks.forEach(task => {
                        task.removeAttribute('draggable');
                        const newTask = task.cloneNode(true);
                        task.parentNode.replaceChild(newTask, task);
                    });
                },

                attachAllEventListeners() {
                    const tasks = document.querySelectorAll('.enhanced-task-card');
                    const columns = document.querySelectorAll('.kanban-column');

                    // Task drag events
                    tasks.forEach(task => {
                        task.setAttribute('draggable', 'true');

                        task.addEventListener('dragstart', (e) => {
                            this.draggingTask = task.getAttribute('data-task-id');
                            task.classList.add('opacity-50', 'scale-95');
                            e.dataTransfer.effectAllowed = 'move';
                        });

                        task.addEventListener('dragend', (e) => {
                            task.classList.remove('opacity-50', 'scale-95');
                            this.draggingTask = null;
                            this.dragOverColumn = null;
                        });
                    });

                    // Column drop events
                    columns.forEach(column => {
                        column.addEventListener('dragover', (e) => {
                            e.preventDefault();
                            e.dataTransfer.dropEffect = 'move';

                            if (this.dragOverColumn !== column) {
                                // Remove highlight from previous column
                                if (this.dragOverColumn) {
                                    this.dragOverColumn.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                                }

                                // Highlight current column
                                column.classList.add('ring-2', 'ring-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                                this.dragOverColumn = column;
                            }
                        });

                        column.addEventListener('dragleave', (e) => {
                            // Only remove highlight if leaving the column entirely
                            if (!column.contains(e.relatedTarget)) {
                                column.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                                this.dragOverColumn = null;
                            }
                        });

                        column.addEventListener('drop', (e) => {
                            e.preventDefault();
                            column.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');

                            if (this.draggingTask) {
                                const statusId = column.getAttribute('data-status-id');
                                const taskId = this.draggingTask;
                                this.draggingTask = null;
                                this.dragOverColumn = null;

                                // Use Alpine.js $wire magic method
                                this.$wire.call('moveTask', parseInt(taskId), statusId);
                            }
                        });
                    });
                }
            }));
        });
    </script>
</x-filament-panels::page>
