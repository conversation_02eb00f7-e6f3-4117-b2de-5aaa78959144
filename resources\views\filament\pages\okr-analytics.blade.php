<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Period</label>
                    <select wire:model="selectedPeriod" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                        <option value="current_quarter">Current Quarter</option>
                        <option value="last_quarter">Last Quarter</option>
                        <option value="current_year">Current Year</option>
                        <option value="last_year">Last Year</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Department</label>
                    <select wire:model="selectedDepartment" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                        <option value="">All Departments</option>
                    </select>
                </div>
                
                <div class="flex items-end">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        Export Report
                    </button>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                        <x-heroicon-o-flag class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div class="ml-4">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $keyMetrics['total_objectives'] }}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Total Objectives</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                        <x-heroicon-o-chart-bar class="w-6 h-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div class="ml-4">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $keyMetrics['avg_progress'] }}%</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Average Progress</p>
                        <div class="flex items-center mt-1">
                            @if($trends['trend_direction'] === 'up')
                                <x-heroicon-s-arrow-trending-up class="w-4 h-4 text-green-500 mr-1" />
                                <span class="text-xs text-green-600">+{{ $trends['trend'] }}%</span>
                            @elseif($trends['trend_direction'] === 'down')
                                <x-heroicon-s-arrow-trending-down class="w-4 h-4 text-red-500 mr-1" />
                                <span class="text-xs text-red-600">{{ $trends['trend'] }}%</span>
                            @else
                                <span class="text-xs text-gray-500">No change</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                        <x-heroicon-o-key class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div class="ml-4">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $keyMetrics['completed_key_results'] }}/{{ $keyMetrics['total_key_results'] }}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Key Results Completed</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                        <x-heroicon-o-puzzle-piece class="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div class="ml-4">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $keyMetrics['completed_tactics'] }}/{{ $keyMetrics['total_tactics'] }}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Tactics Completed</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Achievement Rates -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Achievement Rates</h3>
            </div>
            <div class="p-6">
                @if(!empty($achievementRates))
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Completed</span>
                            <div class="flex items-center">
                                <div class="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-3">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: {{ $achievementRates['completed']['percentage'] }}%"></div>
                                </div>
                                <span class="text-sm font-semibold text-green-600">{{ $achievementRates['completed']['percentage'] }}%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">On Track</span>
                            <div class="flex items-center">
                                <div class="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-3">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $achievementRates['on_track']['percentage'] }}%"></div>
                                </div>
                                <span class="text-sm font-semibold text-blue-600">{{ $achievementRates['on_track']['percentage'] }}%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">At Risk</span>
                            <div class="flex items-center">
                                <div class="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-3">
                                    <div class="bg-red-600 h-2 rounded-full" style="width: {{ $achievementRates['at_risk']['percentage'] }}%"></div>
                                </div>
                                <span class="text-sm font-semibold text-red-600">{{ $achievementRates['at_risk']['percentage'] }}%</span>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="text-center py-8">
                        <p class="text-gray-600 dark:text-gray-400">No data available</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Department Performance -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Department Performance</h3>
            </div>
            <div class="p-6">
                @if(!empty($departmentPerformance))
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Department</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Objectives</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Avg Progress</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Completion Rate</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($departmentPerformance as $dept)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $dept['department'] }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $dept['total_objectives'] }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $dept['avg_progress'] }}%</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $dept['completion_rate'] }}%</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-8">
                        <p class="text-gray-600 dark:text-gray-400">No department data available</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Bottlenecks Analysis -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">At Risk Key Results</h3>
                </div>
                <div class="p-6">
                    @if($bottlenecks['at_risk_key_results']->count() > 0)
                        <div class="space-y-3">
                            @foreach($bottlenecks['at_risk_key_results']->take(5) as $keyResult)
                                <div class="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900 rounded-lg">
                                    <div>
                                        <p class="text-sm font-medium text-red-900 dark:text-red-100">{{ $keyResult->nama_key_result }}</p>
                                        <p class="text-xs text-red-600 dark:text-red-300">{{ $keyResult->objective->nama_objective }}</p>
                                    </div>
                                    <span class="text-xs font-semibold text-red-600 dark:text-red-400">{{ $keyResult->progress_percentage }}%</span>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-600 dark:text-gray-400 text-center py-4">No at-risk key results</p>
                    @endif
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Blocked Tactics</h3>
                </div>
                <div class="p-6">
                    @if($bottlenecks['blocked_tactics']->count() > 0)
                        <div class="space-y-3">
                            @foreach($bottlenecks['blocked_tactics']->take(5) as $tactic)
                                <div class="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900 rounded-lg">
                                    <div>
                                        <p class="text-sm font-medium text-yellow-900 dark:text-yellow-100">{{ $tactic->nama_tactic }}</p>
                                        <p class="text-xs text-yellow-600 dark:text-yellow-300">{{ $tactic->objective->nama_objective }}</p>
                                    </div>
                                    <span class="text-xs font-semibold text-yellow-600 dark:text-yellow-400">Blocked</span>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-600 dark:text-gray-400 text-center py-4">No blocked tactics</p>
                    @endif
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Overdue Objectives</h3>
                </div>
                <div class="p-6">
                    @if($bottlenecks['overdue_objectives']->count() > 0)
                        <div class="space-y-3">
                            @foreach($bottlenecks['overdue_objectives']->take(5) as $objective)
                                <div class="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900 rounded-lg">
                                    <div>
                                        <p class="text-sm font-medium text-red-900 dark:text-red-100">{{ $objective->nama_objective }}</p>
                                        <p class="text-xs text-red-600 dark:text-red-300">Due: {{ $objective->target_completion?->format('M d, Y') }}</p>
                                    </div>
                                    <span class="text-xs font-semibold text-red-600 dark:text-red-400">{{ $objective->days_remaining }} days</span>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-600 dark:text-gray-400 text-center py-4">No overdue objectives</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
