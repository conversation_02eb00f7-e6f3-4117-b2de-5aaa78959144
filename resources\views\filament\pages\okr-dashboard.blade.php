<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Header Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                        <x-heroicon-o-flag class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $totalObjectives }}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Total Objectives</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                        <x-heroicon-o-chart-bar class="w-6 h-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $overallProgress }}%</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Overall Progress</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                        <x-heroicon-o-key class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $completedKeyResults }}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Completed Key Results</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                        <x-heroicon-o-puzzle-piece class="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $completedTactics }}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Completed Tactics</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Recent Objectives -->
            <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Objectives</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        @forelse($recentObjectives as $objective)
                            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-900 dark:text-white">{{ $objective->nama_objective }}</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        {{ $objective->departemen->nama_departemen ?? 'No Department' }}
                                        @if($objective->divisi)
                                            - {{ $objective->divisi->nama_divisi }}
                                        @endif
                                    </p>
                                    <div class="mt-2 flex items-center space-x-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($objective->status === 'active') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                            @elseif($objective->status === 'completed') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                            @elseif($objective->status === 'cancelled') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                            @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                                            @endif">
                                            {{ ucfirst($objective->status) }}
                                        </span>
                                        <span class="text-sm text-gray-600 dark:text-gray-400">
                                            {{ $objective->progress_percentage }}% complete
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="w-16 h-16">
                                        <svg class="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                                            <path class="text-gray-300 dark:text-gray-600" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                            <path class="text-blue-600 dark:text-blue-400" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="{{ $objective->progress_percentage }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                        </svg>
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <span class="text-xs font-semibold text-gray-700 dark:text-gray-300">{{ $objective->progress_percentage }}%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-8">
                                <x-heroicon-o-flag class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                <p class="text-gray-600 dark:text-gray-400">Belum ada objectives</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- Status Summary -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Status Summary</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Objectives</span>
                            <span class="text-sm font-semibold text-green-600 dark:text-green-400">{{ $activeObjectives }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Completed Objectives</span>
                            <span class="text-sm font-semibold text-blue-600 dark:text-blue-400">{{ $completedObjectives }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">At Risk Key Results</span>
                            <span class="text-sm font-semibold text-red-600 dark:text-red-400">{{ $atRiskKeyResults }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Blocked Tactics</span>
                            <span class="text-sm font-semibold text-red-600 dark:text-red-400">{{ $blockedTactics }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Quick Actions</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="{{ route('filament.admin.resources.objectives.create') }}" 
                       class="flex items-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-800 transition-colors">
                        <x-heroicon-o-plus class="w-6 h-6 text-blue-600 dark:text-blue-400 mr-3" />
                        <span class="font-medium text-blue-900 dark:text-blue-100">Create New Objective</span>
                    </a>
                    
                    <a href="{{ route('filament.admin.resources.objectives.index') }}" 
                       class="flex items-center p-4 bg-green-50 dark:bg-green-900 rounded-lg hover:bg-green-100 dark:hover:bg-green-800 transition-colors">
                        <x-heroicon-o-eye class="w-6 h-6 text-green-600 dark:text-green-400 mr-3" />
                        <span class="font-medium text-green-900 dark:text-green-100">View All Objectives</span>
                    </a>
                    
                    <a href="#" 
                       class="flex items-center p-4 bg-purple-50 dark:bg-purple-900 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-800 transition-colors">
                        <x-heroicon-o-chart-bar class="w-6 h-6 text-purple-600 dark:text-purple-400 mr-3" />
                        <span class="font-medium text-purple-900 dark:text-purple-100">View Analytics</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
