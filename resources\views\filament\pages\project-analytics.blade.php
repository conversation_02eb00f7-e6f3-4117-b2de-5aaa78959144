<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Controls -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">Project Analytics</h2>
                    <p class="text-sm text-gray-600">{{ $periodLabel }}</p>
                </div>

                <div class="flex items-center space-x-4">
                    <select wire:model.live="selectedPeriod" class="px-3 py-2 border border-gray-300 rounded-md">
                        <option value="7">Last 7 days</option>
                        <option value="30">Last 30 days</option>
                        <option value="90">Last 3 months</option>
                        <option value="365">Last year</option>
                    </select>

                    <select wire:model.live="selectedProject" class="px-3 py-2 border border-gray-300 rounded-md">
                        <option value="">All Projects</option>
                        @foreach ($availableProjects as $project)
                            <option value="{{ $project->id }}">{{ $project->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                </path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Projects</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $summary['total_projects'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Tasks Completed</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $summary['total_tasks_completed'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>



            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Avg Velocity</dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ number_format($summary['average_velocity'], 2) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Performance -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Project Performance</h3>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    @foreach ($projects as $project)
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h4 class="font-medium text-gray-900">{{ $project['name'] }}</h4>
                                    <div class="flex items-center space-x-4 text-sm text-gray-600">
                                        <span
                                            class="px-2 py-1 bg-{{ $project['status'] === 'active' ? 'green' : 'gray' }}-100 text-{{ $project['status'] === 'active' ? 'green' : 'gray' }}-800 rounded">
                                            {{ ucfirst($project['status']) }}
                                        </span>
                                        <span>{{ $project['active_members_count'] }} members</span>
                                        <span>Velocity: {{ $project['velocity'] }}/day</span>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-gray-900">{{ $project['efficiency_score'] }}
                                    </div>
                                    <div class="text-sm text-gray-500">Efficiency Score</div>
                                </div>
                            </div>

                            <!-- Progress Bars -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                                        <span>Task Completion</span>
                                        <span>{{ $project['task_completion_rate'] }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-500 h-2 rounded-full"
                                            style="width: {{ $project['task_completion_rate'] }}%"></div>
                                    </div>
                                    <div class="text-xs text-gray-500 mt-1">
                                        {{ $project['completed_tasks_count'] }} / {{ $project['tasks_count'] }} tasks
                                    </div>
                                </div>


                            </div>
                        </div>
                    @endforeach

                    @if ($projects->isEmpty())
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                </path>
                            </svg>
                            <p class="text-gray-500">No projects found for the selected criteria</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Team Performance -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Team Performance</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    @foreach ($teamPerformance->take(10) as $member)
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900">{{ $member['name'] }}</h4>
                                <div class="text-sm text-gray-600">
                                    {{ $member['completed_tasks'] }} tasks completed •
                                    {{ $member['total_tasks'] }} total tasks
                                    @if ($member['overdue_tasks'] > 0)
                                        • <span class="text-red-600">{{ $member['overdue_tasks'] }} overdue</span>
                                    @endif
                                </div>
                            </div>

                            <div class="flex items-center space-x-4">
                                <!-- Completion Rate -->
                                <div class="text-center">
                                    <div class="text-lg font-semibold text-gray-900">{{ $member['completion_rate'] }}%
                                    </div>
                                    <div class="text-xs text-gray-500">Completion</div>
                                </div>

                                <!-- Productivity Score -->
                                <div class="text-center">
                                    <div class="text-lg font-semibold text-blue-600">
                                        {{ $member['productivity_score'] }}</div>
                                    <div class="text-xs text-gray-500">Score</div>
                                </div>
                            </div>
                        </div>
                    @endforeach

                    @if ($teamPerformance->isEmpty())
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                                </path>
                            </svg>
                            <p class="text-gray-500">No team performance data available</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
