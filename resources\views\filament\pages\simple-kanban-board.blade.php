<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Project Selection -->
        @if (!$selectedProject)
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4"><PERSON><PERSON><PERSON></h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach ($projects as $project)
                        <div wire:click="selectProject({{ $project->id }})"
                            class="cursor-pointer p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:shadow-md transition-all">
                            <h4 class="font-medium text-gray-900">{{ $project->name }}</h4>
                            <p class="text-sm text-gray-600 mt-1">{{ Str::limit($project->description, 60) }}</p>
                            <div class="mt-2 flex items-center justify-between text-xs text-gray-500">
                                <span
                                    class="px-2 py-1 bg-{{ $project->status === 'active' ? 'green' : 'gray' }}-100 text-{{ $project->status === 'active' ? 'green' : 'gray' }}-800 rounded">
                                    {{ ucfirst($project->status) }}
                                </span>
                                <span>{{ $project->tasks_count ?? 0 }} tasks</span>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @else
            <!-- Project Info -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900">{{ $selectedProject->name }}</h2>
                        <p class="text-gray-600">{{ $selectedProject->description }}</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span
                            class="px-3 py-1 bg-{{ $selectedProject->status === 'active' ? 'green' : 'gray' }}-100 text-{{ $selectedProject->status === 'active' ? 'green' : 'gray' }}-800 rounded-full text-sm">
                            {{ ucfirst($selectedProject->status) }}
                        </span>
                        <button wire:click="selectProject(null)" class="text-gray-500 hover:text-gray-700">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex flex-wrap items-center gap-4">
                    <div class="flex-1 min-w-64">
                        <input wire:model.live="searchFilter" type="text" placeholder="Cari task..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <select wire:model.live="assigneeFilter" class="px-3 py-2 border border-gray-300 rounded-md">
                        <option value="">Semua Assignee</option>
                        @foreach ($teamMembers as $member)
                            <option value="{{ $member->id }}">{{ $member->name }}</option>
                        @endforeach
                    </select>
                    <label class="flex items-center">
                        <input wire:model.live="showCompletedTasks" type="checkbox" class="mr-2">
                        <span class="text-sm text-gray-700">Tampilkan task selesai</span>
                    </label>
                    <button wire:click="clearFilters" class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800">
                        Clear Filters
                    </button>
                </div>
            </div>

            <!-- Simple Kanban Board with HTML5 Drag-Drop -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6" id="kanban-board">
                @foreach ($tasksByStatus as $statusGroup)
                    <div class="bg-gray-50 rounded-lg p-4 kanban-column" data-status="{{ $statusGroup['status']->id }}"
                        ondrop="dropTask(event)" ondragover="allowDrop(event)" ondragenter="dragEnter(event)"
                        ondragleave="dragLeave(event)">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-semibold text-gray-900 flex items-center">
                                <span class="w-3 h-3 rounded-full mr-2"
                                    style="background-color: {{ $statusGroup['status']->color }}"></span>
                                {{ $statusGroup['status']->name }}
                                <span class="ml-2 bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
                                    {{ $statusGroup['count'] }}
                                </span>
                            </h3>
                        </div>

                        <div class="space-y-3 min-h-96">
                            @foreach ($statusGroup['tasks'] as $task)
                                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-move kanban-task"
                                    data-task-id="{{ $task->id }}" data-status="{{ $task->status }}"
                                    draggable="true" ondragstart="dragStart(event)">
                                    <div class="flex items-start justify-between mb-2">
                                        <h4 class="font-medium text-gray-900 text-sm leading-tight">{{ $task->name }}
                                        </h4>
                                        <div class="flex space-x-1">
                                            <button wire:click.stop="editTask({{ $task->id }})"
                                                class="text-gray-400 hover:text-gray-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                                    </path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>

                                    @if ($task->description)
                                        <p class="text-xs text-gray-600 mb-3 line-clamp-2">
                                            {{ Str::limit($task->description, 80) }}</p>
                                    @endif

                                    <div class="flex items-center justify-between text-xs text-gray-500">
                                        <div class="flex items-center space-x-2">
                                            @if ($task->assignedUser)
                                                <span
                                                    class="bg-blue-100 text-blue-800 px-2 py-1 rounded">{{ $task->assignedUser->name }}</span>
                                            @endif
                                            @if ($task->comments_count > 0)
                                                <span class="flex items-center">
                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.544-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z">
                                                        </path>
                                                    </svg>
                                                    {{ $task->comments_count }}
                                                </span>
                                            @endif

                                        </div>
                                        @if ($task->due_date)
                                            <span
                                                class="text-xs {{ $task->due_date->isPast() && $task->status !== 'completed' ? 'text-red-600' : 'text-gray-500' }}">
                                                {{ $task->due_date->format('M d') }}
                                            </span>
                                        @endif
                                    </div>

                                    <!-- Click to view task -->
                                    <div class="absolute inset-0 cursor-pointer"
                                        wire:click="viewTask({{ $task->id }})"
                                        style="z-index: 1; pointer-events: none;"></div>
                                </div>
                            @endforeach

                            @if ($statusGroup['tasks']->isEmpty())
                                <div class="text-center text-gray-400 text-sm py-8 kanban-empty">
                                    Tidak ada task
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        @endif
    </div>

    <style>
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .kanban-task {
            position: relative;
        }

        .kanban-task.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .kanban-column.drag-over {
            background-color: #e0f2fe;
            border: 2px dashed #0284c7;
        }

        .kanban-task:hover {
            transform: translateY(-2px);
        }
    </style>

    <script>
        let draggedElement = null;

        function dragStart(event) {
            draggedElement = event.target;
            event.target.classList.add('dragging');
            event.dataTransfer.effectAllowed = 'move';
            event.dataTransfer.setData('text/html', event.target.outerHTML);
        }

        function allowDrop(event) {
            event.preventDefault();
        }

        function dragEnter(event) {
            event.preventDefault();
            if (event.target.classList.contains('kanban-column')) {
                event.target.classList.add('drag-over');
            }
        }

        function dragLeave(event) {
            if (event.target.classList.contains('kanban-column') && !event.target.contains(event.relatedTarget)) {
                event.target.classList.remove('drag-over');
            }
        }

        function dropTask(event) {
            event.preventDefault();

            const column = event.target.closest('.kanban-column');
            if (!column || !draggedElement) return;

            column.classList.remove('drag-over');

            const taskId = draggedElement.dataset.taskId;
            const newStatus = column.dataset.status;
            const oldStatus = draggedElement.dataset.status;

            if (newStatus !== oldStatus) {
                // Move element visually
                const tasksContainer = column.querySelector('.space-y-3');
                tasksContainer.appendChild(draggedElement);
                draggedElement.dataset.status = newStatus;

                // Hide empty message if exists
                const emptyMessage = column.querySelector('.kanban-empty');
                if (emptyMessage) {
                    emptyMessage.style.display = 'none';
                }

                // Update via Livewire
                @this.call('moveTask', taskId, newStatus);

                // Show success notification
                showSuccessNotification(`Task moved to ${newStatus.replace('_', ' ')}`);
            }

            draggedElement.classList.remove('dragging');
            draggedElement = null;
        }

        function showSuccessNotification(message) {
            const notification = document.createElement('div');
            notification.className =
                'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 10);

            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }

        // Clean up on drag end
        document.addEventListener('dragend', function(event) {
            if (event.target.classList.contains('kanban-task')) {
                event.target.classList.remove('dragging');
                document.querySelectorAll('.kanban-column').forEach(col => {
                    col.classList.remove('drag-over');
                });
                draggedElement = null;
            }
        });
    </script>
</x-filament-panels::page>
