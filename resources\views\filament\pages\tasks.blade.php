<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Statistics Overview -->
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-gray-900">{{ $statistics['total_tasks'] }}</div>
                <div class="text-sm text-gray-600">Total Tasks</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-blue-600">{{ $statistics['todo_tasks'] }}</div>
                <div class="text-sm text-gray-600">To Do</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-yellow-600">{{ $statistics['in_progress_tasks'] }}</div>
                <div class="text-sm text-gray-600">In Progress</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-green-600">{{ $statistics['completed_tasks'] }}</div>
                <div class="text-sm text-gray-600">Completed</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-red-600">{{ $statistics['overdue_tasks'] }}</div>
                <div class="text-sm text-gray-600">Overdue</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-purple-600">{{ $statistics['my_tasks'] }}</div>
                <div class="text-sm text-gray-600">My Tasks</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex flex-wrap items-center gap-4">
                <button wire:click="showMyTasks"
                    class="px-4 py-2 bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors">
                    My Tasks
                </button>
                <button wire:click="showActiveTasks"
                    class="px-4 py-2 bg-yellow-100 text-yellow-800 rounded-lg hover:bg-yellow-200 transition-colors">
                    Active Tasks
                </button>
                <button wire:click="showCompletedTasks"
                    class="px-4 py-2 bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors">
                    Completed Tasks
                </button>
                <button wire:click="showOverdueTasks"
                    class="px-4 py-2 bg-red-100 text-red-800 rounded-lg hover:bg-red-200 transition-colors">
                    Overdue Tasks
                </button>
                <button wire:click="clearFilters"
                    class="px-4 py-2 bg-gray-100 text-gray-800 rounded-lg hover:bg-gray-200 transition-colors">
                    Clear Filters
                </button>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                    <input wire:model.live="searchQuery" type="text" placeholder="Search tasks..."
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Project</label>
                    <select wire:model.live="selectedProject"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <option value="">All Projects</option>
                        @foreach ($projects as $project)
                            <option value="{{ $project->id }}">{{ $project->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select wire:model.live="selectedStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <option value="">All Statuses</option>
                        @foreach ($statuses as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Assignee</label>
                    <select wire:model.live="selectedAssignee"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <option value="">All Assignees</option>
                        @foreach ($users as $user)
                            <option value="{{ $user->id }}">{{ $user->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>

        <!-- Tasks List -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Tasks</h3>
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <span>Sort by:</span>
                        <button wire:click="sortBy('name')"
                            class="hover:text-gray-900 {{ $sorting['by'] === 'name' ? 'font-semibold' : '' }}">
                            Name
                            @if ($sorting['by'] === 'name')
                                <span class="ml-1">{{ $sorting['direction'] === 'asc' ? '↑' : '↓' }}</span>
                            @endif
                        </button>
                        <button wire:click="sortBy('due_date')"
                            class="hover:text-gray-900 {{ $sorting['by'] === 'due_date' ? 'font-semibold' : '' }}">
                            Due Date
                            @if ($sorting['by'] === 'due_date')
                                <span class="ml-1">{{ $sorting['direction'] === 'asc' ? '↑' : '↓' }}</span>
                            @endif
                        </button>
                        <button wire:click="sortBy('created_at')"
                            class="hover:text-gray-900 {{ $sorting['by'] === 'created_at' ? 'font-semibold' : '' }}">
                            Created
                            @if ($sorting['by'] === 'created_at')
                                <span class="ml-1">{{ $sorting['direction'] === 'asc' ? '↑' : '↓' }}</span>
                            @endif
                        </button>
                    </div>
                </div>
            </div>

            <div class="divide-y divide-gray-200">
                @foreach ($tasks as $task)
                    <div class="p-6 hover:bg-gray-50 transition-colors">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3">
                                    <div
                                        class="w-3 h-3 rounded-full bg-{{ $task->status === 'completed' ? 'green' : ($task->status === 'in_progress' ? 'yellow' : 'gray') }}-500">
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-medium text-gray-900">
                                            <a href="{{ \App\Filament\Resources\TaskResource::getUrl('view', ['record' => $task->id]) }}"
                                                class="hover:text-blue-600">
                                                {{ $task->name }}
                                            </a>
                                        </h4>
                                        <p class="text-sm text-gray-600 mt-1">{{ Str::limit($task->description, 120) }}
                                        </p>
                                    </div>
                                </div>

                                <div class="mt-4 flex flex-wrap items-center gap-4 text-sm">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-gray-500">Project:</span>
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                                            {{ $task->project->name }}
                                        </span>
                                    </div>

                                    @if ($task->assignedUser)
                                        <div class="flex items-center space-x-2">
                                            <span class="text-gray-500">Assigned to:</span>
                                            <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                                                {{ $task->assignedUser->name }}
                                            </span>
                                        </div>
                                    @endif



                                    @if ($task->due_date)
                                        <div class="flex items-center space-x-2">
                                            <span class="text-gray-500">Due:</span>
                                            <span
                                                class="text-xs {{ $task->due_date->isPast() && $task->status !== 'completed' ? 'text-red-600 font-semibold' : 'text-gray-700' }}">
                                                {{ $task->due_date->format('M j, Y') }}
                                                @if ($task->due_date->isPast() && $task->status !== 'completed')
                                                    (Overdue)
                                                @endif
                                            </span>
                                        </div>
                                    @endif

                                    <div class="flex items-center space-x-4 text-xs text-gray-500">
                                        @if ($task->comments_count > 0)
                                            <span class="flex items-center">
                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.544-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z">
                                                    </path>
                                                </svg>
                                                {{ $task->comments_count }} comments
                                            </span>
                                        @endif


                                    </div>
                                </div>
                            </div>

                            <div class="ml-6 flex items-center space-x-2">
                                <span
                                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-{{ $task->status === 'completed' ? 'green' : ($task->status === 'in_progress' ? 'yellow' : 'gray') }}-100 text-{{ $task->status === 'completed' ? 'green' : ($task->status === 'in_progress' ? 'yellow' : 'gray') }}-800">
                                    {{ $statuses[$task->status] ?? ucfirst($task->status) }}
                                </span>

                                <a href="{{ \App\Filament\Resources\TaskResource::getUrl('edit', ['record' => $task->id]) }}"
                                    class="text-gray-400 hover:text-gray-600">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach

                @if ($tasks->isEmpty())
                    <div class="p-12 text-center">
                        <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2">
                            </path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No tasks found</h3>
                        <p class="text-gray-500">No tasks match your current filters.</p>
                    </div>
                @endif
            </div>

            <!-- Pagination -->
            @if ($tasks->hasPages())
                <div class="px-6 py-4 border-t border-gray-200">
                    {{ $tasks->links() }}
                </div>
            @endif
        </div>
    </div>
</x-filament-panels::page>
