<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Price List Info -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">
                        {{ $this->getRecord()->name }}
                    </h2>
                    <p class="text-sm text-gray-600">
                        Code: {{ $this->getRecord()->code }}
                    </p>
                    @if($this->getRecord()->description)
                        <p class="text-sm text-gray-600 mt-1">
                            {{ $this->getRecord()->description }}
                        </p>
                    @endif
                </div>
                <div class="text-right">
                    <div class="flex items-center space-x-2">
                        @if($this->getRecord()->is_global)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Global
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                Outlet-Specific
                            </span>
                        @endif
                        
                        @if($this->getRecord()->is_active)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Active
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Inactive
                            </span>
                        @endif
                    </div>
                    <p class="text-sm text-gray-600 mt-2">
                        {{ $this->getRecord()->activeItems()->count() }} products in this price list
                    </p>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">
                        How to manage products in this price list:
                    </h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Click on the price fields to edit prices directly in the table</li>
                            <li>Use the "Add" or "Remove" buttons to include/exclude products</li>
                            <li>Use bulk actions to add or remove multiple products at once</li>
                            <li>Use "Add All Products" to quickly add all active products</li>
                            @if(!$this->getRecord()->is_global)
                                <li>Use "Copy from Global" to copy prices from the global price list</li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Table -->
        <div class="bg-white rounded-lg shadow">
            {{ $this->table }}
        </div>
    </div>
</x-filament-panels::page>
