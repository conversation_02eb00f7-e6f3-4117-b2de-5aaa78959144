<x-filament-widgets::widget>
    <div class="pos-welcome">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">
                    {{ $greeting }}, {{ $user->name }}! 👋
                </h1>
                <p class="text-lg opacity-90">
                    Welcome to POS Backoffice Dashboard. Here's your business overview for today.
                </p>
            </div>
            <div class="hidden md:block">
                <div class="text-right">
                    <div class="text-sm opacity-75">{{ now()->format('l, F j, Y') }}</div>
                    <div class="text-lg font-semibold">{{ now()->format('H:i') }}</div>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-white bg-opacity-20 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-2xl font-bold">{{ number_format($todayStats['transactions']) }}</div>
                        <div class="text-sm opacity-75">Transaksi Bulan Ini</div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-white bg-opacity-20 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-2xl font-bold">Rp {{ number_format($todayStats['revenue'], 0, ',', '.') }}</div>
                        <div class="text-sm opacity-75">Revenue This Month</div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-white bg-opacity-20 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-2xl font-bold">{{ number_format($todayStats['customers']) }}</div>
                        <div class="text-sm opacity-75">Kostumer Bulan Ini</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-widgets::widget>
