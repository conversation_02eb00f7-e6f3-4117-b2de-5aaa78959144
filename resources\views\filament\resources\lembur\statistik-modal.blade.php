<div class="space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Total Jam Lembur Bulan Ini -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-sm font-medium opacity-90">Total Jam Bulan Ini</h3>
                    <p class="text-2xl font-bold">{{ number_format($total_lembur_bulan_ini, 1) }}</p>
                    <p class="text-xs opacity-75">jam</p>
                </div>
            </div>
        </div>

        <!-- Total Record Bulan Ini -->
        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-sm font-medium opacity-90">Total Record</h3>
                    <p class="text-2xl font-bold">{{ $total_record_bulan_ini }}</p>
                    <p class="text-xs opacity-75">record</p>
                </div>
            </div>
        </div>

        <!-- Rata-rata Jam per Hari -->
        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-sm font-medium opacity-90">Rata-rata per Hari</h3>
                    <p class="text-2xl font-bold">{{ number_format($rata_rata_jam_per_hari, 1) }}</p>
                    <p class="text-xs opacity-75">jam/hari</p>
                </div>
            </div>
        </div>

        <!-- Karyawan Terbanyak -->
        <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-sm font-medium opacity-90">Lembur Terbanyak</h3>
                    @if($karyawan_terbanyak_lembur)
                        <p class="text-lg font-bold">{{ $karyawan_terbanyak_lembur->karyawan->nama_lengkap }}</p>
                        <p class="text-xs opacity-75">{{ number_format($karyawan_terbanyak_lembur->total_jam, 1) }} jam</p>
                    @else
                        <p class="text-lg font-bold">Belum ada data</p>
                        <p class="text-xs opacity-75">-</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Chart Section (Placeholder) -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Tren Lembur 7 Hari Terakhir
        </h3>
        <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Chart akan ditampilkan di sini
                </p>
                <p class="text-xs text-gray-400 dark:text-gray-500">
                    (Fitur chart akan segera tersedia)
                </p>
            </div>
        </div>
    </div>

    <!-- Top 5 Karyawan dengan Lembur Terbanyak -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Top 5 Karyawan Lembur Bulan Ini
        </h3>
        <div class="space-y-3">
            @php
                $accessibleIds = \App\Services\PermissionService::getAccessibleKaryawanIds('manage_overtime');
                $topKaryawan = \App\Models\Lembur::with('karyawan.departemen')
                    ->whereMonth('tanggal', now()->month)
                    ->whereYear('tanggal', now()->year)
                    ->whereIn('karyawan_id', $accessibleIds)
                    ->selectRaw('karyawan_id, SUM(jumlah_jam) as total_jam, COUNT(*) as total_record')
                    ->groupBy('karyawan_id')
                    ->orderByDesc('total_jam')
                    ->limit(5)
                    ->get();
            @endphp
            
            @forelse($topKaryawan as $index => $item)
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium text-blue-600 dark:text-blue-400">
                                    {{ $index + 1 }}
                                </span>
                            </div>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-900 dark:text-white">
                                {{ $item->karyawan->nama_lengkap }}
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                {{ $item->karyawan->departemen->nama_departemen ?? 'Tidak ada departemen' }}
                            </p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-semibold text-gray-900 dark:text-white">
                            {{ number_format($item->total_jam, 1) }} jam
                        </p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            {{ $item->total_record }} record
                        </p>
                    </div>
                </div>
            @empty
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        Belum ada data lembur bulan ini
                    </p>
                </div>
            @endforelse
        </div>
    </div>

    <!-- Informasi Tambahan -->
    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                    Informasi Statistik
                </h3>
                <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                    <ul class="list-disc list-inside space-y-1">
                        <li>Data statistik dihitung berdasarkan bulan berjalan ({{ now()->format('F Y') }})</li>
                        <li>Rata-rata jam per hari dihitung dari total jam dibagi hari berjalan</li>
                        <li>Data diperbarui secara real-time setiap kali ada perubahan data lembur</li>
                        <li>Untuk melihat detail per karyawan, gunakan action "Total Jam" pada tabel</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
