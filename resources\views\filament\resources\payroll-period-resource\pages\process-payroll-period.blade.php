<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Informasi Periode -->
        <div class="p-6 bg-white rounded-lg shadow dark:bg-gray-800">
            <h3 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">Informasi Periode Payroll</h3>
            <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Nama Periode</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $record->nama_periode }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Periode</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $record->formatted_periode }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Tanggal Cutoff</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $record->tanggal_cutoff->format('d M Y') }}</dd>
                </div>
            </div>
        </div>

        <!-- Form Pilih Karyawan -->
        <div class="bg-white rounded-lg shadow dark:bg-gray-800">
            {{ $this->form }}
        </div>

        <!-- Informasi Tambahan -->
        <div class="p-4 border border-blue-200 rounded-lg bg-blue-50 dark:bg-blue-900/20 dark:border-blue-700">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-blue-400 dark:text-blue-300" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Informasi Proses Payroll</h3>
                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                        <ul class="space-y-1 list-disc list-inside">
                            <li>Sistem akan menghitung gaji berdasarkan basis gaji terbaru setiap karyawan</li>
                            <li>Potongan keterlambatan akan dihitung otomatis berdasarkan data absensi</li>
                            <li>Potongan pelanggaran akan dihitung berdasarkan data pelanggaran dalam periode</li>
                            <li>Potongan BPJS akan diambil dari pengaturan basis gaji</li>
                            <li>Take home pay akan dihitung otomatis setelah semua potongan</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
