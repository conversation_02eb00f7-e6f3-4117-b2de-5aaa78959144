<div class="space-y-4">
    <div class="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <x-heroicon-m-chat-bubble-left-right class="w-5 h-5 mr-2 text-blue-500" />
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                        Comments & Discussions
                    </h3>
                    <span class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {{ $task->comments()->count() }} comments
                    </span>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    Use @username to mention team members
                </div>
            </div>
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                Collaborate with your team using enhanced comments with @mentions, reactions, and file attachments
            </p>
        </div>
        
        <div class="p-6">
            @livewire('enhanced-comments', ['task' => $task], key('enhanced-comments-' . $task->id))
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <div class="flex items-center">
                <x-heroicon-m-chat-bubble-left class="w-5 h-5 text-blue-600 mr-2" />
                <div>
                    <p class="text-sm font-medium text-blue-900 dark:text-blue-100">Total Comments</p>
                    <p class="text-2xl font-bold text-blue-600">{{ $task->comments()->count() }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
            <div class="flex items-center">
                <x-heroicon-m-at-symbol class="w-5 h-5 text-green-600 mr-2" />
                <div>
                    <p class="text-sm font-medium text-green-900 dark:text-green-100">Mentions</p>
                    <p class="text-2xl font-bold text-green-600">
                        {{ \App\Models\TeamMention::where('mentionable_type', \App\Models\TaskComment::class)
                            ->whereIn('mentionable_id', $task->comments()->pluck('id'))
                            ->count() }}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
            <div class="flex items-center">
                <x-heroicon-m-face-smile class="w-5 h-5 text-purple-600 mr-2" />
                <div>
                    <p class="text-sm font-medium text-purple-900 dark:text-purple-100">Reactions</p>
                    <p class="text-2xl font-bold text-purple-600">
                        {{ \App\Models\CommentReaction::whereIn('comment_id', $task->comments()->pluck('id'))->count() }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Enhanced mention styles for Filament */
.mention {
    @apply bg-blue-100 text-blue-800 px-2 py-1 rounded-md font-medium cursor-pointer transition-colors duration-150;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.mention:hover {
    @apply bg-blue-200 text-blue-900;
}

.dark .mention {
    @apply bg-blue-900/30 text-blue-200 border-blue-700/30;
}

.dark .mention:hover {
    @apply bg-blue-800/50 text-blue-100;
}

.comment-content {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.comment-content .mention {
    display: inline-block;
    margin: 0 1px;
}

/* Mention suggestions animation */
.mention-suggestions {
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced mention highlighting */
.mention[data-mention-type="user"] {
    position: relative;
}

.mention[data-mention-type="user"]:before {
    content: '';
    position: absolute;
    left: -2px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #3B82F6, #1D4ED8);
    border-radius: 1px;
}

/* Filament integration styles */
.fi-section-content-ctn .mention {
    @apply inline-flex items-center;
}
</style>
