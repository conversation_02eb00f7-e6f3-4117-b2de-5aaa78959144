<x-filament-panels::page>
    <div class="space-y-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">Stock Report</h2>
                    <p class="text-sm text-gray-600">
                        Comprehensive stock report with filtering and export capabilities
                    </p>
                </div>
                <div class="flex space-x-2">
                    {{ $this->getHeaderActions() }}
                </div>
            </div>
            
            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                @php
                    $totalItems = \App\Models\InventoryStock::count();
                    $inStockItems = \App\Models\InventoryStock::where('quantity', '>', 0)->count();
                    $lowStockItems = \App\Models\InventoryStock::whereColumn('available_quantity', '<=', 'minimum_stock')->where('minimum_stock', '>', 0)->count();
                    $outOfStockItems = \App\Models\InventoryStock::where('quantity', '<=', 0)->count();
                    $totalValue = \App\Models\InventoryStock::sum('total_value');
                @endphp
                
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-blue-600">Total Items</p>
                            <p class="text-2xl font-semibold text-blue-900">{{ number_format($totalItems) }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-green-600">In Stock</p>
                            <p class="text-2xl font-semibold text-green-900">{{ number_format($inStockItems) }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-yellow-600">Low Stock</p>
                            <p class="text-2xl font-semibold text-yellow-900">{{ number_format($lowStockItems) }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-red-50 p-4 rounded-lg">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-red-600">Out of Stock</p>
                            <p class="text-2xl font-semibold text-red-900">{{ number_format($outOfStockItems) }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-purple-600">Total Value</p>
                            <p class="text-2xl font-semibold text-purple-900">Rp {{ number_format($totalValue, 0, ',', '.') }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Current Filters Display -->
            @if(array_filter($this->filters))
                <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Active Filters:</h4>
                    <div class="flex flex-wrap gap-2">
                        @if($this->filters['warehouse_id'])
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Warehouse: {{ \App\Models\Warehouse::find($this->filters['warehouse_id'])->name ?? 'Unknown' }}
                            </span>
                        @endif
                        @if($this->filters['category_id'])
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Category: {{ \App\Models\ProdukKategori::find($this->filters['category_id'])->nama ?? 'Unknown' }}
                            </span>
                        @endif
                        @if($this->filters['stock_status'])
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Status: {{ ucfirst(str_replace('_', ' ', $this->filters['stock_status'])) }}
                            </span>
                        @endif
                        @if($this->filters['date_from'] || $this->filters['date_to'])
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                Date: {{ $this->filters['date_from'] ?? 'Start' }} - {{ $this->filters['date_to'] ?? 'End' }}
                            </span>
                        @endif
                    </div>
                </div>
            @endif
        </div>
        
        {{ $this->table }}
    </div>
</x-filament-panels::page>
