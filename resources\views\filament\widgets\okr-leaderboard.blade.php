<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            🏆 Leaderboard Performa OKR
        </x-slot>

        <x-slot name="description">
            Top 5 departemen dan divisi dengan performa terbaik berdasarkan progress dan tingkat penyelesaian
        </x-slot>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Departemen Leaderboard -->
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    <x-heroicon-o-building-office class="w-5 h-5 mr-2 text-blue-500" />
                    Top Departemen
                </h3>
                
                <div class="space-y-3">
                    @forelse($departemenLeaderboard as $dept)
                    <div class="flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                        <!-- Rank Badge -->
                        <div class="flex-shrink-0 mr-4">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center font-bold text-white
                                @if($dept['rank'] == 1) bg-yellow-500
                                @elseif($dept['rank'] == 2) bg-gray-400
                                @elseif($dept['rank'] == 3) bg-amber-600
                                @else bg-blue-500
                                @endif">
                                @if($dept['rank'] <= 3)
                                    @if($dept['rank'] == 1) 🥇
                                    @elseif($dept['rank'] == 2) 🥈
                                    @else 🥉
                                    @endif
                                @else
                                    {{ $dept['rank'] }}
                                @endif
                            </div>
                        </div>

                        <!-- Department Info -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    {{ $dept['nama'] }}
                                </h4>
                                <span class="text-lg font-bold text-gray-900 dark:text-white">
                                    {{ $dept['performance_score'] }}
                                </span>
                            </div>
                            
                            <div class="mt-1 flex items-center text-xs text-gray-500 dark:text-gray-400 space-x-4">
                                <span>{{ $dept['objectives_count'] }} objectives</span>
                                <span>{{ $dept['completed_count'] }} selesai</span>
                                <span>{{ $dept['avg_progress'] }}% progress</span>
                                <span>{{ $dept['completion_rate'] }}% completion</span>
                            </div>

                            <!-- Progress Bar -->
                            <div class="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="h-2 rounded-full transition-all duration-300
                                    @if($dept['performance_score'] >= 80) bg-green-500
                                    @elseif($dept['performance_score'] >= 60) bg-yellow-500
                                    @elseif($dept['performance_score'] >= 40) bg-blue-500
                                    @else bg-red-500
                                    @endif"
                                    style="width: {{ $dept['performance_score'] }}%">
                                </div>
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                        <x-heroicon-o-building-office class="w-12 h-12 mx-auto mb-2 opacity-50" />
                        <p>Belum ada data departemen</p>
                    </div>
                    @endforelse
                </div>
            </div>

            <!-- Divisi Leaderboard -->
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    <x-heroicon-o-user-group class="w-5 h-5 mr-2 text-green-500" />
                    Top Divisi
                </h3>
                
                <div class="space-y-3">
                    @forelse($divisiLeaderboard as $divisi)
                    <div class="flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                        <!-- Rank Badge -->
                        <div class="flex-shrink-0 mr-4">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center font-bold text-white
                                @if($divisi['rank'] == 1) bg-yellow-500
                                @elseif($divisi['rank'] == 2) bg-gray-400
                                @elseif($divisi['rank'] == 3) bg-amber-600
                                @else bg-green-500
                                @endif">
                                @if($divisi['rank'] <= 3)
                                    @if($divisi['rank'] == 1) 🥇
                                    @elseif($divisi['rank'] == 2) 🥈
                                    @else 🥉
                                    @endif
                                @else
                                    {{ $divisi['rank'] }}
                                @endif
                            </div>
                        </div>

                        <!-- Divisi Info -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                        {{ $divisi['nama'] }}
                                    </h4>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $divisi['departemen'] }}
                                    </p>
                                </div>
                                <span class="text-lg font-bold text-gray-900 dark:text-white">
                                    {{ $divisi['performance_score'] }}
                                </span>
                            </div>
                            
                            <div class="mt-1 flex items-center text-xs text-gray-500 dark:text-gray-400 space-x-4">
                                <span>{{ $divisi['objectives_count'] }} objectives</span>
                                <span>{{ $divisi['completed_count'] }} selesai</span>
                                <span>{{ $divisi['avg_progress'] }}% progress</span>
                                <span>{{ $divisi['completion_rate'] }}% completion</span>
                            </div>

                            <!-- Progress Bar -->
                            <div class="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="h-2 rounded-full transition-all duration-300
                                    @if($divisi['performance_score'] >= 80) bg-green-500
                                    @elseif($divisi['performance_score'] >= 60) bg-yellow-500
                                    @elseif($divisi['performance_score'] >= 40) bg-blue-500
                                    @else bg-red-500
                                    @endif"
                                    style="width: {{ $divisi['performance_score'] }}%">
                                </div>
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                        <x-heroicon-o-user-group class="w-12 h-12 mx-auto mb-2 opacity-50" />
                        <p>Belum ada data divisi</p>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Performance Score Legend -->
        <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                📊 Performance Score Formula
            </h4>
            <p class="text-xs text-gray-600 dark:text-gray-400">
                Performance Score = (Progress Rata-rata × 60%) + (Completion Rate × 40%)
            </p>
            <div class="mt-2 flex items-center space-x-4 text-xs">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-500 rounded mr-1"></div>
                    <span class="text-gray-600 dark:text-gray-400">Excellent (80-100)</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-yellow-500 rounded mr-1"></div>
                    <span class="text-gray-600 dark:text-gray-400">Good (60-79)</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-blue-500 rounded mr-1"></div>
                    <span class="text-gray-600 dark:text-gray-400">Average (40-59)</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-red-500 rounded mr-1"></div>
                    <span class="text-gray-600 dark:text-gray-400">Needs Improvement (<40)</span>
                </div>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
