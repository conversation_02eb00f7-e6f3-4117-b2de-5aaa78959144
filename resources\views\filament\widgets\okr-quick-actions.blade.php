<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            Quick Actions & Recent Activity
        </x-slot>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Quick Stats -->
            <div class="space-y-4">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white">Quick Stats</h3>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="flex items-center">
                            <x-heroicon-o-calendar class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
                            <span class="text-sm text-blue-900 dark:text-blue-100">Due This Week</span>
                        </div>
                        <span class="text-lg font-semibold text-blue-600 dark:text-blue-400">{{ $quickStats['objectives_due_this_week'] }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                        <div class="flex items-center">
                            <x-heroicon-o-exclamation-triangle class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" />
                            <span class="text-sm text-yellow-900 dark:text-yellow-100">At Risk KRs</span>
                        </div>
                        <span class="text-lg font-semibold text-yellow-600 dark:text-yellow-400">{{ $quickStats['key_results_at_risk'] }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                        <div class="flex items-center">
                            <x-heroicon-o-x-circle class="w-5 h-5 text-red-600 dark:text-red-400 mr-2" />
                            <span class="text-sm text-red-900 dark:text-red-100">Blocked Tactics</span>
                        </div>
                        <span class="text-lg font-semibold text-red-600 dark:text-red-400">{{ $quickStats['blocked_tactics'] }}</span>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Quick Actions</h4>
                    <div class="space-y-2">
                        <a href="{{ route('filament.admin.resources.objectives.create') }}" 
                           class="flex items-center w-full p-2 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-colors">
                            <x-heroicon-o-plus class="w-4 h-4 mr-2" />
                            Create New Objective
                        </a>
                        
                        <button onclick="window.dispatchEvent(new CustomEvent('refresh-okr-data'))" 
                                class="flex items-center w-full p-2 text-sm text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-md transition-colors">
                            <x-heroicon-o-arrow-path class="w-4 h-4 mr-2" />
                            Refresh All Progress
                        </button>
                        
                        <a href="{{ route('filament.admin.resources.objectives.index') }}" 
                           class="flex items-center w-full p-2 text-sm text-purple-600 dark:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded-md transition-colors">
                            <x-heroicon-o-eye class="w-4 h-4 mr-2" />
                            View All Objectives
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="space-y-4">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white">Recent Activity</h3>
                
                <div class="space-y-3">
                    @forelse($recentObjectives as $objective)
                        <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div class="flex items-start justify-between">
                                <div class="flex-1 min-w-0">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                        {{ $objective->nama_objective }}
                                    </h4>
                                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                        Updated {{ $objective->updated_at->diffForHumans() }}
                                    </p>
                                    <div class="flex items-center mt-2">
                                        <div class="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mr-2">
                                            <div class="bg-blue-600 h-1.5 rounded-full" style="width: {{ $objective->progress_percentage }}%"></div>
                                        </div>
                                        <span class="text-xs text-gray-600 dark:text-gray-400">{{ $objective->progress_percentage }}%</span>
                                    </div>
                                </div>
                                <a href="{{ route('filament.admin.resources.objectives.edit', $objective) }}" 
                                   class="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200">
                                    <x-heroicon-o-arrow-top-right-on-square class="w-4 h-4" />
                                </a>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-6">
                            <x-heroicon-o-document-text class="w-8 h-8 text-gray-400 mx-auto mb-2" />
                            <p class="text-sm text-gray-600 dark:text-gray-400">No recent activity</p>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Needs Attention -->
            <div class="space-y-4">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white">Needs Attention</h3>
                
                <div class="space-y-3">
                    @forelse($needsAttention as $objective)
                        <div class="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                            <div class="flex items-start justify-between">
                                <div class="flex-1 min-w-0">
                                    <h4 class="text-sm font-medium text-red-900 dark:text-red-100 truncate">
                                        {{ $objective->nama_objective }}
                                    </h4>
                                    <p class="text-xs text-red-600 dark:text-red-400 mt-1">
                                        @if($objective->is_overdue)
                                            Overdue by {{ abs($objective->days_remaining) }} days
                                        @else
                                            {{ $objective->days_remaining }} days remaining
                                        @endif
                                    </p>
                                    <div class="flex items-center mt-2">
                                        <div class="w-16 bg-red-200 dark:bg-red-800 rounded-full h-1.5 mr-2">
                                            <div class="bg-red-600 h-1.5 rounded-full" style="width: {{ $objective->progress_percentage }}%"></div>
                                        </div>
                                        <span class="text-xs text-red-600 dark:text-red-400">{{ $objective->progress_percentage }}%</span>
                                    </div>
                                </div>
                                <a href="{{ route('filament.admin.resources.objectives.edit', $objective) }}" 
                                   class="ml-2 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200">
                                    <x-heroicon-o-arrow-top-right-on-square class="w-4 h-4" />
                                </a>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-6">
                            <x-heroicon-o-check-circle class="w-8 h-8 text-green-400 mx-auto mb-2" />
                            <p class="text-sm text-green-600 dark:text-green-400">All objectives on track!</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </x-filament::section>

    <script>
        // Listen for refresh event
        window.addEventListener('refresh-okr-data', function() {
            // Show loading state
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Refreshing...';
            button.disabled = true;
            
            // Simulate API call (replace with actual refresh logic)
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                
                // Show success message
                window.dispatchEvent(new CustomEvent('show-notification', {
                    detail: { message: 'OKR data refreshed successfully!', type: 'success' }
                }));
                
                // Refresh the page or specific components
                window.location.reload();
            }, 2000);
        });
    </script>
</x-filament-widgets::widget>
