<x-filament-widgets::widget>
    <div @if(!$isVisible) style="display: none;" @endif>
    <x-filament::section>
        <x-slot name="heading">
            Project Calendar
        </x-slot>

        <x-slot name="headerEnd">
            <div class="flex gap-2">
                <button
                    wire:click="previousMonth"
                    class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                    <x-heroicon-o-chevron-left class="w-4 h-4" />
                </button>

                <button
                    wire:click="goToToday"
                    class="px-3 py-1 text-sm bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300 rounded"
                >
                    Today
                </button>

                <button
                    wire:click="nextMonth"
                    class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                    <x-heroicon-o-chevron-right class="w-4 h-4" />
                </button>
            </div>
        </x-slot>

        <div class="overflow-x-auto">
            {{-- Month Header --}}
            <div class="text-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {{ $currentMonth }}
                </h3>
            </div>

            {{-- Calendar Grid --}}
            <div class="min-w-full">
                {{-- Day Headers --}}
                <div class="grid grid-cols-7 gap-px bg-gray-200 dark:bg-gray-700 rounded-t-lg overflow-hidden">
                    @foreach(['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'] as $day)
                        <div class="bg-gray-50 dark:bg-gray-800 p-2 text-center">
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $day }}</span>
                        </div>
                    @endforeach
                </div>

                {{-- Calendar Days --}}
                <div class="grid grid-cols-7 gap-px bg-gray-200 dark:bg-gray-700 rounded-b-lg overflow-hidden">
                    @foreach($calendarData as $week)
                        @foreach($week as $day)
                            <div class="bg-white dark:bg-gray-800 min-h-[120px] p-2
                                @if(!$day['isCurrentMonth']) opacity-50 @endif
                                @if($day['isToday']) ring-2 ring-primary-500 @endif">

                                {{-- Day Number --}}
                                <div class="flex justify-between items-start mb-1">
                                    <span class="text-sm font-medium
                                        @if($day['isToday']) text-primary-600 dark:text-primary-400
                                        @elseif($day['isCurrentMonth']) text-gray-900 dark:text-white
                                        @else text-gray-400 dark:text-gray-600
                                        @endif">
                                        {{ $day['day'] }}
                                    </span>
                                </div>

                                {{-- Projects --}}
                                @foreach($day['projects'] as $project)
                                    <div class="mb-1">
                                        <div class="text-xs px-2 py-1 rounded
                                            @if($project['status'] === 'active') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                            @elseif($project['status'] === 'completed') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                            @elseif($project['status'] === 'on_hold') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300
                                            @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300
                                            @endif truncate"
                                            title="{{ $project['name'] }} ({{ $project['progress'] }}%)"
                                        >
                                            @if($project['isStart'])
                                                <x-heroicon-o-play class="w-3 h-3 inline mr-1" />
                                            @elseif($project['isEnd'])
                                                <x-heroicon-o-stop class="w-3 h-3 inline mr-1" />
                                            @else
                                                <x-heroicon-o-minus class="w-3 h-3 inline mr-1" />
                                            @endif
                                            {{ Str::limit($project['name'], 15) }}
                                        </div>
                                    </div>
                                @endforeach

                                {{-- Tasks --}}
                                @foreach($day['tasks'] as $task)
                                    <div class="mb-1">
                                        <div class="text-xs px-2 py-1 rounded border-l-2
                                            @if($task['is_overdue']) border-red-500 bg-red-50 text-red-800 dark:bg-red-900 dark:text-red-300
                                            @elseif($task['status'] === 'completed') border-green-500 bg-green-50 text-green-800 dark:bg-green-900 dark:text-green-300
                                            @elseif($task['status'] === 'in_progress') border-blue-500 bg-blue-50 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                            @else border-gray-500 bg-gray-50 text-gray-800 dark:bg-gray-900 dark:text-gray-300
                                            @endif"
                                            title="{{ $task['name'] }} - {{ $task['project_name'] }}"
                                        >
                                            @if($task['is_overdue'])
                                                <x-heroicon-o-exclamation-triangle class="w-3 h-3 inline mr-1" />
                                            @elseif($task['status'] === 'completed')
                                                <x-heroicon-o-check-circle class="w-3 h-3 inline mr-1" />
                                            @else
                                                <x-heroicon-o-clock class="w-3 h-3 inline mr-1" />
                                            @endif
                                            {{ Str::limit($task['name'], 12) }}
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endforeach
                    @endforeach
                </div>
            </div>

            {{-- Legend --}}
            <div class="mt-4 flex flex-wrap gap-4 text-xs">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-100 border-l-2 border-green-500 mr-2"></div>
                    <span class="text-gray-600 dark:text-gray-400">Active Project</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-blue-100 border-l-2 border-blue-500 mr-2"></div>
                    <span class="text-gray-600 dark:text-gray-400">Task Due</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-red-100 border-l-2 border-red-500 mr-2"></div>
                    <span class="text-gray-600 dark:text-gray-400">Overdue Task</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-100 border-l-2 border-green-500 mr-2"></div>
                    <span class="text-gray-600 dark:text-gray-400">Completed Task</span>
                </div>
            </div>
        </div>
    </x-filament::section>
    </div>
</x-filament-widgets::widget>
