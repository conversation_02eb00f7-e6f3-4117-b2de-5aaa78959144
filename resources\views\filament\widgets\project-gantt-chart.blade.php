<x-filament-widgets::widget>
    <div @if (!$isVisible) style="display: none;" @endif>
        <x-filament::section>
            <x-slot name="heading">
                Project Gantt Chart
            </x-slot>

            <x-slot name="headerEnd">
                <div class="flex gap-2">
                    <select wire:model.live="selectedProjectId"
                        class="text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded-md">
                        <option value="">All Projects</option>
                        @foreach (\App\Models\Project::all() as $project)
                            <option value="{{ $project->id }}">{{ $project->name }}</option>
                        @endforeach
                    </select>
                </div>
            </x-slot>

            <div class="overflow-x-auto bg-white dark:bg-gray-900 rounded-lg shadow-sm">
                @if ($projects->isEmpty())
                    <div class="text-center py-12 text-gray-500 dark:text-gray-400">
                        <x-heroicon-o-chart-bar class="w-16 h-16 mx-auto mb-4 opacity-50" />
                        <p class="text-lg font-medium">No projects found</p>
                        <p class="text-sm mt-1">Create a project to see the Gantt chart</p>
                    </div>
                @else
                    <div class="min-w-[800px]">
                        {{-- Timeline Header --}}
                        <div
                            class="flex border-b-2 border-gray-200 dark:border-gray-700 mb-6 bg-gray-50 dark:bg-gray-800 rounded-t">
                            <div class="w-64 p-3 font-semibold text-gray-900 dark:text-white flex-shrink-0">
                                Project / Task
                            </div>
                            <div class="flex-1 flex">
                                @foreach ($timelineData['months'] as $month)
                                    <div class="flex-1 text-center p-3 border-l border-gray-200 dark:border-gray-700">
                                        <div class="text-sm font-semibold text-gray-900 dark:text-white">
                                            {{ $month['name'] }}
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        {{-- Projects and Tasks --}}
                        @foreach ($timelineData['projectsData'] as $projectData)
                            {{-- Project Row --}}
                            <div
                                class="flex items-center mb-4 group border-b border-gray-100 dark:border-gray-700 pb-2">
                                <div class="w-64 p-2 flex-shrink-0">
                                    <div class="flex items-center">
                                        <div
                                            class="w-3 h-3 rounded-full mr-2
                                        @if ($projectData['status'] === 'active') bg-green-500
                                        @elseif($projectData['status'] === 'completed') bg-blue-500
                                        @elseif($projectData['status'] === 'on_hold') bg-yellow-500
                                        @else bg-gray-500 @endif">
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <div class="font-semibold text-gray-900 dark:text-white truncate">
                                                {{ $projectData['name'] }}
                                            </div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                {{ $projectData['progress'] }}% complete
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex-1 relative h-8 bg-gray-50 dark:bg-gray-800 rounded">
                                    {{-- Project Timeline Bar --}}
                                    <div class="absolute top-1 h-6 rounded
                                        @if ($projectData['status'] === 'active') bg-green-400
                                        @elseif($projectData['status'] === 'completed') bg-blue-400
                                        @elseif($projectData['status'] === 'on_hold') bg-yellow-400
                                        @else bg-gray-400 @endif opacity-80"
                                        style="left: {{ ($projectData['startOffset'] / $timelineData['totalDays']) * 100 }}%;
                                           width: {{ ($projectData['duration'] / $timelineData['totalDays']) * 100 }}%;">
                                        {{-- Progress Bar --}}
                                        <div class="h-full rounded
                                            @if ($projectData['status'] === 'active') bg-green-600
                                            @elseif($projectData['status'] === 'completed') bg-blue-600
                                            @elseif($projectData['status'] === 'on_hold') bg-yellow-600
                                            @else bg-gray-600 @endif"
                                            style="width: {{ $projectData['progress'] }}%;"></div>
                                    </div>
                                </div>
                            </div>

                            {{-- Task Rows --}}
                            @foreach ($projectData['tasks'] as $taskData)
                                <div class="flex items-start mb-3 ml-4 min-h-[3rem]">
                                    <div class="w-64 p-2 flex-shrink-0">
                                        <div class="flex items-start">
                                            <div
                                                class="w-2 h-2 rounded-full mr-2 mt-1 flex-shrink-0
                                            @if ($taskData['status'] === 'completed') bg-green-400
                                            @elseif($taskData['status'] === 'in_progress') bg-blue-400
                                            @elseif($taskData['status'] === 'todo') bg-gray-400
                                            @else bg-yellow-400 @endif">
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <div
                                                    class="text-sm font-medium text-gray-700 dark:text-gray-300 truncate">
                                                    {{ $taskData['name'] }}
                                                </div>
                                                @if ($taskData['start_date'] || $taskData['due_date'])
                                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                        @if ($taskData['start_date'])
                                                            <div>Start: {{ $taskData['start_date'] }}</div>
                                                        @endif
                                                        @if ($taskData['due_date'])
                                                            <div>Due: {{ $taskData['due_date'] }}</div>
                                                        @endif
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex-1 relative h-6 bg-gray-50 dark:bg-gray-800 rounded mt-1">
                                        {{-- Task Timeline Bar --}}
                                        <div class="absolute top-1 h-4 rounded cursor-pointer
                                            @if ($taskData['status'] === 'completed') bg-green-300
                                            @elseif($taskData['status'] === 'in_progress') bg-blue-300
                                            @elseif($taskData['status'] === 'todo') bg-gray-300
                                            @else bg-yellow-300 @endif opacity-80"
                                            style="left: {{ ($taskData['startOffset'] / $timelineData['totalDays']) * 100 }}%;
                                               width: {{ ($taskData['duration'] / $timelineData['totalDays']) * 100 }}%;"
                                            title="Task: {{ $taskData['name'] }}
@if ($taskData['start_date']) Start: {{ $taskData['start_date'] }} @endif
@if ($taskData['due_date']) Due: {{ $taskData['due_date'] }} @endif
Status: {{ ucfirst(str_replace('_', ' ', $taskData['status'])) }}
Progress: {{ $taskData['progress'] }}%">
                                            {{-- Task Progress --}}
                                            <div class="h-full rounded
                                                @if ($taskData['status'] === 'completed') bg-green-500
                                                @elseif($taskData['status'] === 'in_progress') bg-blue-500
                                                @elseif($taskData['status'] === 'todo') bg-gray-500
                                                @else bg-yellow-500 @endif"
                                                style="width: {{ $taskData['progress'] }}%;"></div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach

                            <div class="mb-6"></div>
                        @endforeach
                    </div>

                    {{-- Legend --}}
                    <div class="mt-6 flex flex-wrap gap-4 text-sm">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">Completed</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-blue-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">Active</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-yellow-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">On Hold</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-gray-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">Planned</span>
                        </div>
                    </div>
                @endif
            </div>
        </x-filament::section>
    </div>
</x-filament-widgets::widget>
