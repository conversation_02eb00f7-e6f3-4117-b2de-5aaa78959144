<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center space-x-2">
                <x-heroicon-m-calendar-days class="w-5 h-5 text-blue-500" />
                <span>Timeline Kegiatan</span>
            </div>
        </x-slot>

        @if (!empty($timelineData['projectsData']))
            <div class="space-y-4">
                <!-- Timeline Header -->
                <div class="relative">
                    <div class="flex border-b border-gray-200 dark:border-gray-700 pb-2">
                        @foreach ($timelineData['months'] as $month)
                            <div class="text-center text-sm font-medium text-gray-600 dark:text-gray-400"
                                style="width: {{ $month['widthPercent'] }}%">
                                {{ $month['name'] }}
                            </div>
                        @endforeach
                    </div>

                    <!-- Current Date Indicator -->
                    @php
                        $currentDateOffset = $currentDate->diffInDays($timelineData['startDate']);
                        $currentDatePercent = ($currentDateOffset / $timelineData['totalDays']) * 100;
                    @endphp
                    @if ($currentDatePercent >= 0 && $currentDatePercent <= 100)
                        <div class="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10"
                            style="left: {{ $currentDatePercent }}%">
                            <div class="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full"></div>
                            <div class="absolute -top-8 -left-8 text-xs text-red-600 font-medium whitespace-nowrap">
                                Hari ini
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Project Rows -->
                <div class="space-y-3">
                    @foreach ($timelineData['projectsData'] as $project)
                        <div class="flex items-center space-x-4">
                            <!-- Project Info -->
                            <div class="w-64 flex-shrink-0">
                                <div class="flex items-center space-x-2">
                                    @php
                                        $healthIcon = match ($project['health']) {
                                            'on_track' => 'heroicon-m-check-circle',
                                            'at_risk' => 'heroicon-m-exclamation-triangle',
                                            'behind' => 'heroicon-m-clock',
                                            'overdue' => 'heroicon-m-x-circle',
                                            'completed' => 'heroicon-m-check-badge',
                                            default => 'heroicon-m-question-mark-circle',
                                        };

                                        $healthColor = match ($project['health']) {
                                            'on_track' => 'text-green-600',
                                            'at_risk' => 'text-yellow-600',
                                            'behind' => 'text-orange-600',
                                            'overdue' => 'text-red-600',
                                            'completed' => 'text-blue-600',
                                            default => 'text-gray-600',
                                        };
                                    @endphp
                                    <x-dynamic-component :component="$healthIcon" class="w-4 h-4 {{ $healthColor }}" />
                                    <div class="min-w-0 flex-1">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                            {{ $project['name'] }}
                                        </h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                                            {{ $project['customer'] }}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Timeline Bar -->
                            <div class="flex-1 relative h-8">
                                <div class="absolute inset-0 bg-gray-100 dark:bg-gray-800 rounded"></div>

                                <!-- Project Duration Bar -->
                                <div class="absolute top-1 bottom-1 {{ $project['color']['bg'] }} rounded {{ $project['color']['border'] }} border"
                                    style="left: {{ $project['startPercent'] }}%; width: {{ $project['widthPercent'] }}%">

                                    <!-- Progress Bar -->
                                    <div class="h-full {{ $project['color']['progress'] }} rounded-l"
                                        style="width: {{ $project['progress'] }}%"></div>

                                    <!-- Progress Text -->
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <span class="text-xs font-medium text-white">
                                            {{ $project['progress'] }}%
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Project Stats -->
                            <div class="w-32 flex-shrink-0 text-right">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ $project['completed_tasks'] }}/{{ $project['tasks_count'] }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    @if ($project['daysRemaining'] < 0)
                                        {{ abs($project['daysRemaining']) }} hari terlambat
                                    @elseif($project['daysRemaining'] == 0)
                                        Deadline hari ini
                                    @else
                                        {{ $project['daysRemaining'] }} hari tersisa
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Legend -->
                <div
                    class="flex items-center justify-center space-x-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-500 rounded"></div>
                        <span class="text-xs text-gray-600 dark:text-gray-400">On Track</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-yellow-500 rounded"></div>
                        <span class="text-xs text-gray-600 dark:text-gray-400">At Risk</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-orange-500 rounded"></div>
                        <span class="text-xs text-gray-600 dark:text-gray-400">Behind</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-red-500 rounded"></div>
                        <span class="text-xs text-gray-600 dark:text-gray-400">Overdue</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-0.5 h-3 bg-red-500"></div>
                        <span class="text-xs text-gray-600 dark:text-gray-400">Hari ini</span>
                    </div>
                </div>
            </div>
        @else
            <div class="text-center py-8">
                <x-heroicon-o-calendar-days class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-500 dark:text-gray-400">Tidak ada kegiatan untuk ditampilkan</p>
            </div>
        @endif
    </x-filament::section>
</x-filament-widgets::widget>
