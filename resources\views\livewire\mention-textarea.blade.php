<div class="mention-textarea-component space-y-2">
    @if($label)
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {{ $label }}
            @if($required)
                <span class="text-red-500">*</span>
            @endif
        </label>
    @endif

    <div class="relative">
        <textarea
            wire:model.live="value"
            x-data="{
                handleKeyup(event) {
                    const value = event.target.value;
                    const cursorPos = event.target.selectionStart;
                    const textBeforeCursor = value.substring(0, cursorPos);
                    const mentionMatch = textBeforeCursor.match(/@(\w*)$/);

                    if (mentionMatch) {
                        $wire.searchMentions(mentionMatch[1]);
                    } else {
                        $wire.searchMentions('');
                    }
                }
            }"
            x-on:keyup="handleKeyup($event)"
            placeholder="{{ $placeholder }}"
            rows="{{ $rows }}"
            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 resize-none"
            @if($required) required @endif
        ></textarea>

        <!-- Mention Suggestions -->
        @if($showMentions && !empty($mentionSuggestions))
            <div class="mention-suggestions">
                @foreach($mentionSuggestions as $user)
                    <button
                        type="button"
                        wire:click="insertMention('{{ $user['name'] }}')"
                        class="mention-suggestion"
                    >
                        <div class="mention-avatar">
                            {{ substr($user['name'], 0, 1) }}
                        </div>
                        <div class="mention-info">
                            <div class="mention-name">{{ $user['name'] }}</div>
                            <div class="mention-email">{{ $user['email'] }}</div>
                        </div>
                    </button>
                @endforeach
            </div>
        @endif
    </div>

    @if($name)
        <input type="hidden" name="{{ $name }}" value="{{ $value }}">
    @endif
</div>
