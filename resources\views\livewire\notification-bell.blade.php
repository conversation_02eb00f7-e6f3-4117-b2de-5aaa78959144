<div class="relative" x-data="{ open: @entangle('showDropdown') }">
    <!-- Notification Bell Button -->
    <button @click="open = !open" 
            class="relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg transition-colors duration-150">
        <x-heroicon-o-bell class="w-6 h-6" />
        
        <!-- Unread Count Badge -->
        @if($unreadCount > 0)
            <span class="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full min-w-[1.25rem] h-5">
                {{ $unreadCount > 99 ? '99+' : $unreadCount }}
            </span>
        @endif
    </button>

    <!-- Notification Dropdown -->
    <div x-show="open" 
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
         @click.away="open = false"
         class="absolute right-0 z-50 mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 max-h-96 overflow-hidden">
        
        <!-- Header -->
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white">Notifications</h3>
                <div class="flex items-center space-x-2">
                    @if($unreadCount > 0)
                        <button wire:click="markAllAsRead" 
                                class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-500">
                            Mark all read
                        </button>
                    @endif
                    <button wire:click="viewAll" 
                            class="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                        View all
                    </button>
                </div>
            </div>
        </div>

        <!-- Notifications List -->
        <div class="max-h-80 overflow-y-auto">
            @forelse($notifications as $notification)
                <div class="px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150 {{ !$notification['read_at'] ? 'bg-blue-50/50 dark:bg-blue-900/10' : '' }}">
                    <div class="flex items-start space-x-3">
                        <!-- Notification Icon -->
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 rounded-lg flex items-center justify-center {{ !$notification['read_at'] ? 'bg-blue-100 dark:bg-blue-900/20' : 'bg-gray-100 dark:bg-gray-700' }}">
                                <x-dynamic-component 
                                    :component="$notification['icon']" 
                                    class="w-4 h-4 {{ $this->getNotificationColor($notification) }}" 
                                />
                            </div>
                        </div>

                        <!-- Notification Content -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                        {{ $notification['title'] }}
                                    </p>
                                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                                        {{ $notification['message'] }}
                                    </p>
                                    
                                    <!-- Action Buttons -->
                                    <div class="flex items-center mt-2 space-x-2">
                                        @if($notification['action_url'])
                                            <a href="{{ $notification['action_url'] }}" 
                                               class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-500">
                                                View
                                            </a>
                                        @endif
                                        
                                        @if(!$notification['read_at'])
                                            <button wire:click="markAsRead('{{ $notification['id'] }}')" 
                                                    class="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                                                Mark read
                                            </button>
                                        @endif
                                    </div>
                                </div>

                                <!-- Time and Unread Indicator -->
                                <div class="flex flex-col items-end ml-2">
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $this->formatNotificationTime(\Carbon\Carbon::parse($notification['created_at'])) }}
                                    </span>
                                    
                                    @if(!$notification['read_at'])
                                        <div class="w-2 h-2 bg-blue-500 rounded-full mt-1"></div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="px-4 py-8 text-center">
                    <x-heroicon-o-bell-slash class="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p class="text-sm text-gray-500 dark:text-gray-400">No notifications</p>
                    <p class="text-xs text-gray-400 dark:text-gray-500">You're all caught up!</p>
                </div>
            @endforelse
        </div>

        <!-- Footer -->
        @if(!empty($notifications))
            <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
                <button wire:click="viewAll" 
                        class="w-full text-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500 font-medium">
                    View All Notifications
                </button>
            </div>
        @endif
    </div>
</div>

<!-- Auto-refresh notifications every 30 seconds -->
<script>
    document.addEventListener('livewire:navigated', function () {
        setInterval(() => {
            if (typeof Livewire !== 'undefined') {
                Livewire.dispatch('loadNotifications');
            }
        }, 30000); // 30 seconds
    });
</script>
