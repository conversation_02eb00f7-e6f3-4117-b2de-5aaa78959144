<div class="space-y-6">
    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Activity Type Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Activity Type</label>
                <select wire:model.live="filterType" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    @foreach($activityTypes as $value => $label)
                        <option value="{{ $value }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Project Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Project</label>
                <select wire:model.live="filterProject" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">All Projects</option>
                    @foreach($projects as $project)
                        <option value="{{ $project['id'] }}">{{ $project['name'] }}</option>
                    @endforeach
                </select>
            </div>

            <!-- User Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">User</label>
                <select wire:model.live="filterUser" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">All Users</option>
                    @foreach($users as $user)
                        <option value="{{ $user['id'] }}">{{ $user['name'] }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Time Range Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Time Range</label>
                <select wire:model.live="timeRange" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    @foreach($timeRanges as $value => $label)
                        <option value="{{ $value }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>
        </div>

        <!-- Clear Filters -->
        <div class="mt-4 flex justify-end">
            <button 
                wire:click="clearFilters"
                class="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
            >
                Clear Filters
            </button>
        </div>
    </div>

    <!-- Activity Feed -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Team Activity Feed</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Recent activities from your team</p>
        </div>

        <div class="divide-y divide-gray-200 dark:divide-gray-700">
            @forelse($activities as $activity)
                <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150">
                    <div class="flex items-start space-x-3">
                        <!-- Activity Icon -->
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center bg-gray-100 dark:bg-gray-700">
                                <x-dynamic-component 
                                    :component="$activity['icon']" 
                                    class="w-5 h-5 {{ $activity['color'] }}" 
                                />
                            </div>
                        </div>

                        <!-- Activity Content -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <!-- Activity Description -->
                                    <p class="text-sm text-gray-900 dark:text-white">
                                        {{ $activity['description'] }}
                                    </p>

                                    <!-- Activity Data -->
                                    @if(!empty($activity['data']))
                                        <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                            @if(isset($activity['data']['project_name']))
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300">
                                                    {{ $activity['data']['project_name'] }}
                                                </span>
                                            @endif
                                            
                                            @if(isset($activity['data']['file_name']))
                                                <span class="ml-2">📎 {{ $activity['data']['file_name'] }}</span>
                                            @endif
                                            
                                            @if(isset($activity['data']['comment_preview']))
                                                <div class="mt-1 italic">"{{ $activity['data']['comment_preview'] }}"</div>
                                            @endif
                                        </div>
                                    @endif

                                    <!-- Action Link -->
                                    @if($activity['action_url'])
                                        <a 
                                            href="{{ $activity['action_url'] }}" 
                                            class="inline-flex items-center mt-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500"
                                        >
                                            View Details
                                            <x-heroicon-m-arrow-top-right-on-square class="w-4 h-4 ml-1" />
                                        </a>
                                    @endif
                                </div>

                                <!-- Timestamp -->
                                <div class="flex-shrink-0 ml-4">
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $activity['created_at']->diffForHumans() }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="p-8 text-center">
                    <x-heroicon-o-rss class="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No activities found</h3>
                    <p class="text-gray-500 dark:text-gray-400">
                        @if($filterType !== 'all' || $filterProject || $filterUser)
                            Try adjusting your filters to see more activities.
                        @else
                            No recent activities to display. Start collaborating to see activities here!
                        @endif
                    </p>
                </div>
            @endforelse
        </div>

        <!-- Load More (if needed) -->
        @if(count($activities) >= 50)
            <div class="p-4 border-t border-gray-200 dark:border-gray-700 text-center">
                <button 
                    wire:click="loadMore"
                    class="text-blue-600 dark:text-blue-400 hover:text-blue-500 text-sm font-medium"
                >
                    Load More Activities
                </button>
            </div>
        @endif
    </div>

    <!-- Activity Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Today's Activities -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                        <x-heroicon-m-calendar-days class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Today</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">
                        {{ collect($activities)->where('created_at', '>=', now()->startOfDay())->count() }}
                    </p>
                </div>
            </div>
        </div>

        <!-- This Week's Activities -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                        <x-heroicon-m-chart-bar class="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">This Week</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">
                        {{ collect($activities)->where('created_at', '>=', now()->startOfWeek())->count() }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Most Active User -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                        <x-heroicon-m-user class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Most Active</p>
                    @php
                        $mostActive = collect($activities)->groupBy('user.name')->map->count()->sortDesc()->first();
                        $mostActiveUser = collect($activities)->groupBy('user.name')->sortByDesc->count()->keys()->first();
                    @endphp
                    <p class="text-lg font-bold text-gray-900 dark:text-white">
                        {{ $mostActiveUser ?? 'N/A' }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
