<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slip <PERSON>i - {{ $payroll->karyawan->nama_lengkap }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .slip-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .slip-title {
            font-size: 18px;
            color: #666;
            margin-bottom: 10px;
        }

        .period {
            font-size: 14px;
            color: #888;
        }

        .employee-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 6px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .info-label {
            font-weight: bold;
            color: #555;
        }

        .info-value {
            color: #333;
        }

        .salary-section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #ddd;
        }

        .salary-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .salary-table th,
        .salary-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .salary-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #555;
        }

        .amount {
            text-align: right;
            font-family: 'Courier New', monospace;
        }

        .total-row {
            font-weight: bold;
            background-color: #f8f9fa;
        }

        .take-home-pay {
            background-color: #e8f5e8;
            font-size: 18px;
            font-weight: bold;
            color: #2d5a2d;
        }

        .detail-section {
            margin-top: 20px;
            font-size: 13px;
        }

        .detail-section th {
            background-color: #f8f9fa;
            font-size: 12px;
            padding: 8px;
        }

        .detail-section td {
            padding: 6px 8px;
            font-size: 12px;
        }

        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #888;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }

        @media print {
            body {
                background-color: white;
                padding: 0;
            }

            .slip-container {
                box-shadow: none;
                padding: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="slip-container">
        <!-- Header -->
        <div class="header">
            <div class="company-name">PT. VIERA INDONESIA</div>
            <div class="slip-title">SLIP GAJI KARYAWAN</div>
            <div class="period">Periode: {{ $payroll->payrollPeriod->formatted_periode }}</div>
        </div>

        <!-- Employee Information -->
        <div class="employee-info">
            <div>
                <div class="info-item">
                    <span class="info-label">Nama Karyawan:</span>
                    <span class="info-value">{{ $payroll->karyawan->nama_lengkap }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">NIP:</span>
                    <span class="info-value">{{ $payroll->karyawan->nip }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Jabatan:</span>
                    <span class="info-value">{{ $payroll->karyawan->jabatan->nama_jabatan ?? '-' }}</span>
                </div>
            </div>
            <div>
                <div class="info-item">
                    <span class="info-label">No. Payroll:</span>
                    <span class="info-value">{{ $payroll->no_payroll }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Departemen:</span>
                    <span class="info-value">{{ $payroll->karyawan->departemen->nama_departemen ?? '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Tanggal Cetak:</span>
                    <span class="info-value">{{ now()->format('d M Y H:i') }}</span>
                </div>
            </div>
        </div>

        <!-- Salary Components -->
        <div class="salary-section">
            <div class="section-title">KOMPONEN GAJI</div>
            <table class="salary-table">
                <thead>
                    <tr>
                        <th>Keterangan</th>
                        <th class="amount">Jumlah (Rp)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Gaji Pokok</td>
                        <td class="amount">{{ number_format($payroll->gaji_pokok, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Tunjangan Jabatan</td>
                        <td class="amount">{{ number_format($payroll->tunjangan_jabatan, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Tunjangan Umum</td>
                        <td class="amount">{{ number_format($payroll->tunjangan_umum, 0, ',', '.') }}</td>
                    </tr>
                    @if ($payroll->insentif > 0)
                        <tr>
                            <td>Insentif</td>
                            <td class="amount">{{ number_format($payroll->insentif, 0, ',', '.') }}</td>
                        </tr>
                    @endif
                    <tr>
                        <td>Tunjangan Sembako</td>
                        <td class="amount">{{ number_format($payroll->tunjangan_sembako, 0, ',', '.') }}</td>
                    </tr>
                    @if ($payroll->lembur_biasa > 0)
                        <tr>
                            <td>Lembur Biasa</td>
                            <td class="amount">{{ number_format($payroll->lembur_biasa, 0, ',', '.') }}</td>
                        </tr>
                    @endif
                    @if ($payroll->lembur_tanggal_merah > 0)
                        <tr>
                            <td>Lembur Tanggal Merah</td>
                            <td class="amount">{{ number_format($payroll->lembur_tanggal_merah, 0, ',', '.') }}</td>
                        </tr>
                    @endif
                    @if ($payroll->lembur_tambah_hk > 0)
                        <tr>
                            <td>Lembur Tambah HK</td>
                            <td class="amount">{{ number_format($payroll->lembur_tambah_hk, 0, ',', '.') }}</td>
                        </tr>
                    @endif
                    @if ($payroll->kekurangan_gaji_bulan_sebelum > 0)
                        <tr>
                            <td>Kekurangan Gaji Bulan Sebelum</td>
                            <td class="amount">
                                {{ number_format($payroll->kekurangan_gaji_bulan_sebelum, 0, ',', '.') }}</td>
                        </tr>
                    @endif
                    @if ($payroll->claim_sakit_dengan_surat > 0)
                        <tr>
                            <td>Claim Sakit Dengan Surat</td>
                            <td class="amount">{{ number_format($payroll->claim_sakit_dengan_surat, 0, ',', '.') }}
                            </td>
                        </tr>
                    @endif
                    @if ($payroll->pesangon > 0)
                        <tr>
                            <td>Pesangon</td>
                            <td class="amount">{{ number_format($payroll->pesangon, 0, ',', '.') }}</td>
                        </tr>
                    @endif
                    <tr class="total-row">
                        <td><strong>Total Gaji Kotor</strong></td>
                        <td class="amount">
                            <strong>{{ number_format($payroll->total_gaji_kotor, 0, ',', '.') }}</strong>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Deductions -->
        <div class="salary-section">
            <div class="section-title">POTONGAN</div>
            <table class="salary-table">
                <thead>
                    <tr>
                        <th>Keterangan</th>
                        <th class="amount">Jumlah (Rp)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>BPJS Kesehatan</td>
                        <td class="amount">{{ number_format($payroll->potongan_bpjs_kesehatan, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td>BPJS Tenaga Kerja</td>
                        <td class="amount">{{ number_format($payroll->potongan_bpjs_tk, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Potongan Keterlambatan</td>
                        <td class="amount">{{ number_format($payroll->potongan_keterlambatan, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Potongan Pelanggaran</td>
                        <td class="amount">{{ number_format($payroll->potongan_pelanggaran, 0, ',', '.') }}</td>
                    </tr>
                    @php
                        // Group potongan by jenis
                        $potonganByJenis = $payroll->payrollDeductions->groupBy('jenis_potongan');

                        // Hitung total potongan karyawan (kasir, stok_opname, retur, kasbon)
                        $potonganKaryawan = $potonganByJenis->filter(function ($group, $jenis) {
                            return in_array($jenis, ['kasir', 'stok_opname', 'retur', 'kasbon']);
                        });

                        // Hitung total potongan absensi (sakit tanpa surat, alpha, cuti melebihi kuota)
                        $potonganAbsensi = $potonganByJenis->filter(function ($group, $jenis) {
                            return in_array($jenis, ['sakit_tanpa_surat', 'alpha', 'cuti_melebihi_kuota']);
                        });

                        // Hitung potongan lainnya yang bukan dari jenis karyawan dan absensi
                        $potonganLainnyaActual = $payroll->potongan_lainnya;
                        $totalPotonganKaryawan = $potonganKaryawan->flatten()->sum('nominal');
                        $totalPotonganAbsensi = $potonganAbsensi->flatten()->sum('nominal');
                        $sisaPotonganLainnya = $potonganLainnyaActual - $totalPotonganKaryawan - $totalPotonganAbsensi;
                    @endphp

                    {{-- Tampilkan potongan karyawan berdasarkan jenis --}}
                    @foreach ($potonganKaryawan as $jenis => $deductions)
                        <tr>
                            <td>{{ $deductions->first()->jenis_label }}</td>
                            <td class="amount">{{ number_format($deductions->sum('nominal'), 0, ',', '.') }}</td>
                        </tr>
                    @endforeach

                    {{-- Tampilkan potongan absensi berdasarkan jenis --}}
                    @foreach ($potonganAbsensi as $jenis => $deductions)
                        <tr>
                            <td>{{ $deductions->first()->jenis_label }}</td>
                            <td class="amount">{{ number_format($deductions->sum('nominal'), 0, ',', '.') }}</td>
                        </tr>
                    @endforeach

                    {{-- Tampilkan sisa potongan lainnya jika ada --}}
                    @if ($sisaPotonganLainnya > 0)
                        <tr>
                            <td>Potongan Lainnya</td>
                            <td class="amount">{{ number_format($sisaPotonganLainnya, 0, ',', '.') }}</td>
                        </tr>
                    @endif
                    <tr class="total-row">
                        <td><strong>Total Potongan</strong></td>
                        <td class="amount"><strong>{{ number_format($payroll->total_potongan, 0, ',', '.') }}</strong>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        {{-- Detail Potongan Karyawan jika ada --}}
        @if ($potonganKaryawan->count() > 0)
            <div class="salary-section detail-section">
                <div class="section-title">DETAIL POTONGAN KARYAWAN</div>
                <table class="salary-table">
                    <thead>
                        <tr>
                            <th>Jenis Potongan</th>
                            <th>Tanggal</th>
                            <th>Keterangan</th>
                            <th class="amount">Jumlah (Rp)</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($potonganKaryawan->flatten() as $deduction)
                            <tr>
                                <td>{{ $deduction->jenis_label }}</td>
                                <td>{{ $deduction->tanggal_kejadian ? $deduction->tanggal_kejadian->format('d M Y') : '-' }}
                                </td>
                                <td>{{ $deduction->keterangan ?: $deduction->deskripsi }}</td>
                                <td class="amount">{{ number_format($deduction->nominal, 0, ',', '.') }}</td>
                            </tr>
                        @endforeach
                        <tr class="total-row">
                            <td colspan="3"><strong>Total Potongan Karyawan</strong></td>
                            <td class="amount">
                                <strong>{{ number_format($totalPotonganKaryawan, 0, ',', '.') }}</strong>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        @endif

        {{-- Detail Potongan Absensi jika ada --}}
        @if ($potonganAbsensi->count() > 0)
            <div class="salary-section detail-section">
                <div class="section-title">DETAIL POTONGAN ABSENSI</div>
                <table class="salary-table">
                    <thead>
                        <tr>
                            <th>Jenis Potongan</th>
                            <th>Tanggal</th>
                            <th>Keterangan</th>
                            <th class="amount">Jumlah (Rp)</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($potonganAbsensi->flatten() as $deduction)
                            <tr>
                                <td>{{ $deduction->jenis_label }}</td>
                                <td>{{ $deduction->tanggal_kejadian ? $deduction->tanggal_kejadian->format('d M Y') : '-' }}
                                </td>
                                <td>{{ $deduction->keterangan ?: $deduction->deskripsi }}</td>
                                <td class="amount">{{ number_format($deduction->nominal, 0, ',', '.') }}</td>
                            </tr>
                        @endforeach
                        <tr class="total-row">
                            <td colspan="3"><strong>Total Potongan Absensi</strong></td>
                            <td class="amount"><strong>{{ number_format($totalPotonganAbsensi, 0, ',', '.') }}</strong>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        @endif

        <!-- Take Home Pay -->
        <div class="salary-section">
            <table class="salary-table">
                <tbody>
                    <tr class="take-home-pay">
                        <td><strong>TAKE HOME PAY</strong></td>
                        <td class="amount"><strong>{{ number_format($payroll->take_home_pay, 0, ',', '.') }}</strong>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Attendance Summary -->
        <div class="salary-section">
            <div class="section-title">RINGKASAN ABSENSI</div>
            <table class="salary-table">
                <tbody>
                    <tr>
                        <td>Total Hari Kerja</td>
                        <td class="amount">{{ $payroll->total_hari_kerja }} hari</td>
                    </tr>
                    <tr>
                        <td>Total Hari Hadir</td>
                        <td class="amount">{{ $payroll->total_hari_hadir }} hari</td>
                    </tr>
                    <tr>
                        <td>Total Menit Terlambat</td>
                        <td class="amount">{{ $payroll->total_menit_terlambat }} menit</td>
                    </tr>
                    <tr>
                        <td>Total Pelanggaran</td>
                        <td class="amount">{{ $payroll->total_pelanggaran }} kasus</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Slip gaji ini digenerate secara otomatis oleh sistem pada {{ now()->format('d M Y H:i:s') }}</p>
            <p>Untuk pertanyaan terkait gaji, silakan hubungi bagian HR</p>
        </div>
    </div>

    <script>
        // Auto print when opened
        window.onload = function() {
            window.print();
        }
    </script>
</body>

</html>
