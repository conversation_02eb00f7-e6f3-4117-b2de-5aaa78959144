<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Date Range Picker</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Test Date Range Picker</h1>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <div 
                x-data="{ 
                    selectedData: null,
                    handleDateRange(detail) {
                        this.selectedData = detail;
                        console.log('Date range selected:', detail);
                    }
                }"
                @daterange-selected="handleDateRange($event.detail)"
            >
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Test Date Range Picker
                </label>
                
                <div class="max-w-md">
                    <x-date-range-picker 
                        placeholder="Pilih rentang tanggal test"
                    />
                </div>
                
                <div class="mt-4 p-4 bg-gray-50 rounded" x-show="selectedData">
                    <h3 class="font-medium mb-2">Selected Data:</h3>
                    <pre x-text="JSON.stringify(selectedData, null, 2)"></pre>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
