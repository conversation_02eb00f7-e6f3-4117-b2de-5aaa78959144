<div>
    @if ($getRecord()->approvalStatus)
        <p class="px-3">
            <small>
                @if ($getRecord()->isApprovalCompleted())
                    @php
                        $lastApproval = $getRecord()->lastApproval;
                        $approvalAction = $lastApproval?->approval_action ?? 'unknown';

                        $statusClasses = [
                            'Approved' => 'bg-green-500 text-white',
                            'Rejected' => 'bg-red-500 text-white',
                            'Discarded' => 'bg-red-500 text-white',
                            'Pending' => 'bg-warning-500 text-yellow-800',
                            'Submitted' => 'bg-blue-500 text-white',
                        ];
                        $classes = $statusClasses[$approvalAction] ?? 'bg-gray-500 text-white';
                    @endphp
                    <div class="flex gap-x-4 ml-auto">
                        <span class="px-3 py-1 rounded-full text-xs {{ $classes }}">
                            {{ __('filament-approvals::approvals.actions.history.' . $approvalAction) }}
                        </span>
                    </div>
                    {{-- {{ __('filament-approvals::approvals.status_column.approval_by_prefix') }}
                    @if ($getRecord()->lastApproval)
                        {{ $getRecord()->lastApproval->approver_name }}
                    @else
                        {{ $getRecord()->createdBy()->name }}
                    @endif --}}
                @else
                    @if ($getRecord()->isSubmitted())
                        @php
                            $lastApproval = $getRecord()->lastApproval;
                            $approvalAction = $lastApproval?->approval_action ?? 'Submitted';

                            $statusClasses = [
                                'Approved' => 'bg-green-500 text-white',
                                'Rejected' => 'bg-red-500 text-white',
                                'Discarded' => 'bg-red-500 text-white',
                                'Pending' => 'bg-warning-500 text-yellow-800',
                                'Submitted' => 'bg-blue-500 text-white',
                            ];
                            $classes = $statusClasses[$approvalAction] ?? 'bg-blue-500 text-white';
                        @endphp
                        <div class="flex gap-x-4 ml-auto">
                            <span class="px-3 py-1 rounded-full text-xs {{ $classes }}">
                                {{ __('filament-approvals::approvals.actions.history.' . $approvalAction) }}
                            </span>
                        </div>
                    @else
                        <span class="px-3 py-1 bg-gray-200 text-gray-800 rounded-full text-xs">
                            {{ 'Belum Dikirim' }}
                        </span>
                    @endif
                    {{-- {{ $getRecord()->approvalStatus->status }}
                    {{ __('filament-approvals::approvals.status_column.approval_by_prefix') }}
                    @if ($getRecord()->nextApprover)
                        {{ $getRecord()->nextApprover->name }}
                    @else
                        {{ $getRecord()->createdBy()->name }}
                    @endif --}}
                @endif
            </small>
        </p>
        {{-- <p class="px-3 text-xs">
            <small>
                {{ $getRecord()->isApprovalCompleted()
                    ? __('filament-approvals::approvals.status_column.approval_complete')
                    : __('filament-approvals::approvals.status_column.approval_in_process') }}
            </small>
        </p> --}}
    @else
        <span class="px-3 py-1 bg-gray-200 text-gray-800 rounded-full text-xs">
            {{ __('filament-approvals::approvals.status_column.approval_status_does_not_exist') }}
        </span>
    @endif
</div>
