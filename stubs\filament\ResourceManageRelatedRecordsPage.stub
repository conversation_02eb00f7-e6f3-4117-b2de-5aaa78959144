<?php

namespace {{ namespace }};

use {{ resource }};
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Form;
use {{ baseResourcePage }};
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class {{ resourcePageClass }} extends {{ baseResourcePageClass }}
{
    protected static string $resource = {{ resourceClass }}::class;

    protected static string $relationship = '{{ relationship }}';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationLabel(): string
    {
        return '{{ title }}';
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('{{ recordTitleAttribute }}')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('{{ recordTitleAttribute }}')
            ->columns([
                Tables\Columns\TextColumn::make('{{ recordTitleAttribute }}'),
            ])
            ->filters([
{{ tableFilters }}
            ])
            ->headerActions([
{{ tableHeaderActions }}
            ])
            ->actions([
{{ tableActions }}
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
{{ tableBulkActions }}
                ]),
            ]){{ modifyQueryUsing }};
    }
}
