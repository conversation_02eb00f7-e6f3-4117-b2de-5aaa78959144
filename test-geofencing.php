<?php

// Test script untuk geofencing functionality
require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🌍 Testing Geofencing Implementation...\n\n";

// Test 1: Check if migration ran successfully
echo "📋 1. Checking Database Schema:\n";
echo "==============================\n";

try {
    $entitas = \App\Models\Entitas::first();
    
    if ($entitas) {
        $hasLatitude = \Schema::hasColumn('entitas', 'latitude');
        $hasLongitude = \Schema::hasColumn('entitas', 'longitude');
        $hasRadius = \Schema::hasColumn('entitas', 'radius');
        $hasGeofencing = \Schema::hasColumn('entitas', 'enable_geofencing');
        
        echo "✅ Latitude column: " . ($hasLatitude ? 'EXISTS' : 'MISSING') . "\n";
        echo "✅ Longitude column: " . ($hasLongitude ? 'EXISTS' : 'MISSING') . "\n";
        echo "✅ Radius column: " . ($hasRadius ? 'EXISTS' : 'MISSING') . "\n";
        echo "✅ Enable_geofencing column: " . ($hasGeofencing ? 'EXISTS' : 'MISSING') . "\n";
        
        if ($hasLatitude && $hasLongitude && $hasRadius && $hasGeofencing) {
            echo "🎉 All geofencing columns exist!\n";
        } else {
            echo "❌ Some columns are missing. Please run migration.\n";
        }
    } else {
        echo "⚠️ No entitas found. Please create some entitas first.\n";
    }
} catch (Exception $e) {
    echo "❌ Error checking schema: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Test Haversine formula
echo "📐 2. Testing Distance Calculation:\n";
echo "===================================\n";

// Jakarta coordinates
$jakartaLat = -6.2088;
$jakartaLon = 106.8456;

// Test points
$testPoints = [
    ['name' => 'Same location', 'lat' => -6.2088, 'lon' => 106.8456],
    ['name' => '100m away', 'lat' => -6.2079, 'lon' => 106.8456],
    ['name' => '500m away', 'lat' => -6.2043, 'lon' => 106.8456],
    ['name' => 'Surabaya', 'lat' => -7.2575, 'lon' => 112.7521],
];

foreach ($testPoints as $point) {
    $distance = \App\Models\Entitas::calculateDistance(
        $jakartaLat, $jakartaLon,
        $point['lat'], $point['lon']
    );
    
    $formatted = \App\Services\GeofencingService::formatDistance($distance);
    echo "📍 {$point['name']}: {$formatted}\n";
}

echo "\n";

// Test 3: Create sample entitas with geofencing
echo "🏢 3. Creating Sample Entitas:\n";
echo "==============================\n";

try {
    // Check if sample entitas exists
    $sampleEntitas = \App\Models\Entitas::where('nama', 'Kantor Pusat (Test)')->first();
    
    if (!$sampleEntitas) {
        $sampleEntitas = \App\Models\Entitas::create([
            'nama' => 'Kantor Pusat (Test)',
            'alamat' => 'Jakarta Pusat, DKI Jakarta',
            'keterangan' => 'Sample entitas untuk testing geofencing',
            'latitude' => -6.2088,
            'longitude' => 106.8456,
            'radius' => 100,
            'enable_geofencing' => true,
        ]);
        
        echo "✅ Created sample entitas: {$sampleEntitas->nama}\n";
    } else {
        echo "✅ Sample entitas already exists: {$sampleEntitas->nama}\n";
    }
    
    echo "📍 Coordinates: {$sampleEntitas->coordinates}\n";
    echo "📏 Radius: {$sampleEntitas->radius}m\n";
    echo "🔒 Geofencing: " . ($sampleEntitas->enable_geofencing ? 'ENABLED' : 'DISABLED') . "\n";
    
} catch (Exception $e) {
    echo "❌ Error creating sample entitas: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Test geofencing validation
echo "🎯 4. Testing Geofencing Validation:\n";
echo "====================================\n";

if (isset($sampleEntitas)) {
    $testLocations = [
        ['name' => 'Inside radius (50m)', 'lat' => -6.2084, 'lon' => 106.8456],
        ['name' => 'Edge of radius (100m)', 'lat' => -6.2079, 'lon' => 106.8456],
        ['name' => 'Outside radius (200m)', 'lat' => -6.2070, 'lon' => 106.8456],
        ['name' => 'Far outside (1km)', 'lat' => -6.2000, 'lon' => 106.8456],
    ];
    
    foreach ($testLocations as $location) {
        $validation = $sampleEntitas->isWithinRadius($location['lat'], $location['lon']);
        
        $status = $validation['allowed'] ? '✅ ALLOWED' : '❌ BLOCKED';
        $distance = \App\Services\GeofencingService::formatDistance($validation['distance']);
        
        echo "📍 {$location['name']}: {$status} ({$distance})\n";
        echo "   Message: {$validation['message']}\n";
    }
} else {
    echo "❌ No sample entitas available for testing\n";
}

echo "\n";

// Test 5: Test GeofencingService
echo "🔧 5. Testing GeofencingService:\n";
echo "===============================\n";

try {
    // Test coordinate validation
    $validCoords = \App\Services\GeofencingService::validateCoordinates(-6.2088, 106.8456);
    $invalidCoords = \App\Services\GeofencingService::validateCoordinates(91, 181);
    
    echo "✅ Valid coordinates (-6.2088, 106.8456): " . ($validCoords ? 'VALID' : 'INVALID') . "\n";
    echo "❌ Invalid coordinates (91, 181): " . ($invalidCoords ? 'VALID' : 'INVALID') . "\n";
    
    // Test Google Maps URL generation
    $mapsUrl = \App\Services\GeofencingService::getGoogleMapsUrl(-6.2088, 106.8456);
    echo "🗺️ Google Maps URL: {$mapsUrl}\n";
    
    // Test distance formatting
    $distances = [50.5, 150.75, 1500.25, 5000];
    foreach ($distances as $dist) {
        $formatted = \App\Services\GeofencingService::formatDistance($dist);
        echo "📏 {$dist}m → {$formatted}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing GeofencingService: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 6: Check if karyawan has entitas
echo "👥 6. Checking Karyawan-Entitas Relations:\n";
echo "==========================================\n";

try {
    $karyawanWithEntitas = \App\Models\Karyawan::with('entitas')->whereNotNull('id_entitas')->limit(5)->get();
    
    if ($karyawanWithEntitas->count() > 0) {
        echo "✅ Found {$karyawanWithEntitas->count()} karyawan with entitas:\n";
        
        foreach ($karyawanWithEntitas as $karyawan) {
            $entitas = $karyawan->entitas;
            $geofencingStatus = $entitas && $entitas->enable_geofencing ? 'ENABLED' : 'DISABLED';
            $entitasName = $entitas ? $entitas->nama : 'No Entitas';
            
            echo "   - {$karyawan->nama_lengkap} → {$entitasName} ({$geofencingStatus})\n";
        }
    } else {
        echo "⚠️ No karyawan found with entitas. Please assign karyawan to entitas.\n";
    }
    
    $karyawanWithoutEntitas = \App\Models\Karyawan::whereNull('id_entitas')->count();
    if ($karyawanWithoutEntitas > 0) {
        echo "⚠️ {$karyawanWithoutEntitas} karyawan without entitas (geofencing won't work for them)\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error checking karyawan relations: " . $e->getMessage() . "\n";
}

echo "\n";

// Summary
echo "📊 7. Implementation Summary:\n";
echo "=============================\n";
echo "✅ Database schema updated with geofencing fields\n";
echo "✅ Entitas model enhanced with geofencing methods\n";
echo "✅ GeofencingService created for validation logic\n";
echo "✅ Absensi model integrated with geofencing\n";
echo "✅ Admin interface updated (EntitasResource & AbsensiResource)\n";
echo "✅ Distance calculation using Haversine formula\n";
echo "✅ Coordinate validation and error handling\n";

echo "\n🎯 Next Steps:\n";
echo "==============\n";
echo "1. Setup entitas coordinates via admin panel\n";
echo "2. Test absensi with different locations\n";
echo "3. Monitor geofencing status in AbsensiResource\n";
echo "4. Adjust radius based on real-world testing\n";
echo "5. Integrate with mobile/web absensi form\n";

echo "\n🎉 Geofencing implementation completed successfully!\n";
?>
