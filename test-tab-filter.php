<?php

// Simple test untuk tab filter
require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 Testing Tab Filter Logic...\n\n";

// Test the exact same logic as in ListKaryawans
$jenisKontrak = [
    'PKWTT' => ['label' => 'Tetap', 'color' => 'success'],
    'PKWT' => ['label' => 'Kontrak', 'color' => 'warning'],
    'Probation' => ['label' => 'Probation', 'color' => 'info'],
    'Freelance' => ['label' => 'Freelance', 'color' => 'gray'],
];

echo "📊 Tab Filter Results:\n";
echo "=====================\n";

$tabs = [];

foreach ($jenisKontrak as $jenis => $config) {
    // Hitung jumlah karyawan dengan kontrak aktif per jenis
    $jumlahKaryawan = \App\Models\Karyawan::whereHas('riwayatKontrak', function ($query) use ($jenis) {
        $query->where('jenis_kontrak', $jenis)
            ->where('is_active', 1);
    })->count();

    echo "- {$config['label']} ({$jenis}): {$jumlahKaryawan} karyawan\n";
    
    // Tampilkan tab meskipun jumlah 0 untuk konsistensi
    $tabs['kontrak_' . strtolower($jenis)] = [
        'label' => $config['label'],
        'badge' => $jumlahKaryawan,
        'color' => $config['color']
    ];
}

echo "\n📋 Tabs yang akan dibuat:\n";
echo "========================\n";
foreach ($tabs as $key => $tab) {
    echo "- Key: {$key}\n";
    echo "  Label: {$tab['label']}\n";
    echo "  Badge: {$tab['badge']}\n";
    echo "  Color: {$tab['color']}\n\n";
}

// Test sample karyawan dengan kontrak
echo "👥 Sample Karyawan dengan Kontrak Aktif:\n";
echo "========================================\n";

$karyawanDenganKontrak = \App\Models\Karyawan::with(['riwayatKontrak' => function($query) {
    $query->where('is_active', 1)->orderBy('tgl_mulai', 'desc');
}])
->whereHas('riwayatKontrak', function ($query) {
    $query->where('is_active', 1);
})
->limit(10)
->get();

foreach ($karyawanDenganKontrak as $karyawan) {
    $kontrak = $karyawan->riwayatKontrak->first();
    echo "- {$karyawan->nama_lengkap}: {$kontrak->jenis_kontrak}\n";
}

if ($karyawanDenganKontrak->count() == 0) {
    echo "❌ Tidak ada karyawan dengan kontrak aktif!\n";
    echo "\n🔧 Solusi:\n";
    echo "1. Jalankan: php fix-kontrak-data.php\n";
    echo "2. Atau manual set kontrak aktif di database\n";
} else {
    echo "\n✅ Ada {$karyawanDenganKontrak->count()} karyawan dengan kontrak aktif\n";
    echo "Tab filter seharusnya muncul!\n";
}

echo "\n🎯 Langkah Troubleshooting:\n";
echo "===========================\n";
echo "1. Pastikan ada data kontrak dengan is_active = 1\n";
echo "2. Clear cache: php artisan cache:clear\n";
echo "3. Refresh halaman Data Karyawan\n";
echo "4. Tab filter akan muncul setelah tab departemen\n";

echo "\n✅ Test completed!\n";
?>
