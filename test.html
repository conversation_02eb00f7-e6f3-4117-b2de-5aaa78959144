<!DOCTYPE html>

<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Organization Chart (Fixed Layout)</title>
    <style>
        /* ===== BODY & DASAR ===== */
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f4f8;
        }
        h2 {
            margin-bottom: 20px;
        }

```
    /* ===== ORG CHART STYLES ===== */
    .orgchart-tree {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        position: relative;
        overflow-x: auto;
        padding-bottom: 40px;
        min-height: 60vh;
    }

    .orgchart-node-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        /* margin horizontal dihapus, supaya gap Flexbox yang mengatur spacing */
        min-width: 180px;
    }

    .orgchart-children {
        position: absolute;
        top: calc(100% + 24px);
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        justify-content: center;
        align-items: flex-start;
        gap: 64px;     /* <-- gap diperbesar agar tidak overlap */
        white-space: nowrap;
    }

    /* Baris Ho<PERSON>al */
    .orgchart-horizontal-line {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        /* Lebar menyesuaikan lebar container + satu gap di kiri-kanan */
        width: calc(100% + 64px);
        height: 2px;
        background: rgba(0, 0, 0, 0.2);
        z-index: 1;
    }

    /* Garis Vertikal (turun) */
    .orgchart-vertical-line {
        position: absolute;
        top: 100%;
        left: 50%;
        width: 2px;
        height: 24px;
        background: rgba(0, 0, 0, 0.2);
        transform: translateX(-50%);
        z-index: 1;
    }

    /* CARD DEPARTEMEN / DIVISI / JABATAN */
    .org-card {
        background: white;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        text-align: center;
        min-width: 160px;
        min-height: 100px;
        position: relative;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        display: flex;
        flex-direction: column;
        justify-content: center;
        z-index: 2;
    }
    .org-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        border-color: #007bff;
    }

    .org-card.departemen {
        background: #007bff;
        color: white;
        font-size: 1.2em;
        font-weight: bold;
    }
    .org-card.divisi {
        background: #17a2b8;
        color: white;
    }
    .org-card.jabatan {
        background: #28a745;
        color: white;
    }

    /* TOMBOL Lihat Karyawan */
    .show-employees-btn {
        margin-top: 8px;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 0.85rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .show-employees-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
    }

    /* POPUP DAFTAR KARYAWAN */
    .employee-list {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        margin-top: 8px;
        width: 220px;
        max-width: 90vw;
        z-index: 10;
        display: none;   /* show/hide via JS */
    }
    .employee-item {
        padding: 8px 12px;
        border-bottom: 1px solid #e5e7eb;
    }
    .employee-item:last-child {
        border-bottom: none;
    }
    .employee-name {
        font-weight: 600;
        color: #1f2937;
    }
    .employee-nip {
        font-size: 0.85rem;
        color: #6b7280;
    }
    .employee-position {
        font-size: 0.85rem;
        color: #374151;
    }

    /* RESPONSIVE: jika layar sempit, tampilan menjadi vertikal  */
    @media (max-width: 900px) {
        .orgchart-tree {
            flex-direction: column;
            align-items: center;
        }
        .orgchart-horizontal-line {
            display: none;
        }
    }
</style>
```

</head>
<body>

````
<h2>Organization Chart (Fixed Layout)</h2>
<div class="orgchart-tree">

    <!-- ========== LEVEL 1: DEPARTEMEN ========== -->
    <div class="orgchart-node-wrapper">
        <div class="org-card departemen">
            <div>📁</div>
            <div class="org-card-title">Technology</div>
            <div class="org-card-count">8 Karyawan</div>
            <button class="show-employees-btn" onclick="toggleEmployees('dept-tech')">
                Lihat Semua Karyawan
            </button>
        </div>
        <div class="orgchart-vertical-line"></div>

        <!-- POPUP KARYAWAN DI DEPARTEMEN (Technology) -->
        <div id="dept-tech" class="employee-list">
            <div class="employee-item">
                <div class="employee-name">Alice Johnson</div>
                <div class="employee-nip">EMP0001</div>
                <div class="employee-position">Chief Executive Officer</div>
            </div>
            <div class="employee-item">
                <div class="employee-name">Bob Smith</div>
                <div class="employee-nip">EMP0002</div>
                <div class="employee-position">Chief Technical Officer</div>
            </div>
        </div>

        <!-- ========== LEVEL 2: DIVISI ========== -->
        <div class="orgchart-children">
            <div class="orgchart-horizontal-line"></div>

            <!-- Divisi 1: Development -->
            <div class="orgchart-node-wrapper">
                <div class="org-card divisi">
                    <div>📂</div>
                    <div class="org-card-title">Development</div>
                    <div class="org-card-count">3 Karyawan</div>
                    <button class="show-employees-btn" onclick="toggleEmployees('div-dev')">
                        Lihat Karyawan
                    </button>
                </div>
                <div class="orgchart-vertical-line"></div>

                <!-- POPUP KARYAWAN DIVISI (Development) -->
                <div id="div-dev" class="employee-list">
                    <div class="employee-item">
                        <div class="employee-name">Charlie Brown</div>
                        <div class="employee-nip">EMP0003</div>
                        <div class="employee-position">Senior Developer</div>
                    </div>
                    <div class="employee-item">
                        <div class="employee-name">David Green</div>
                        <div class="employee-nip">EMP0004</div>
                        <div class="employee-position">Junior Developer</div>
                    </div>
                    <div class="employee-item">
                        <div class="employee-name">Eva White</div>
                        <div class="employee-nip">EMP0005</div>
                        <div class="employee-position">Junior Developer</div>
                    </div>
                </div>

                <!-- ========== LEVEL 3: JABATAN DI DEVELOPMENT ========== -->
                <div class="orgchart-children" style="margin-top: 24px;">
                    <div class="orgchart-horizontal-line"></div>

                    <!-- Jabatan 1: Senior Developer -->
                    <div class="orgchart-node-wrapper">
                        <div class="org-card jabatan">
                            <div>🏷️</div>
                            <div class="org-card-title">Senior Developer</div>
                            <div class="org-card-count">1 Karyawan</div>
                            <button class="show-employees-btn" onclick="toggleEmployees('jab-srdev')">
                                Lihat Karyawan
                            </button>
                        </div>
                        <div id="jab-srdev" class="employee-list">
                            <div class="employee-item">
                                <div class="employee-name">Charlie Brown</div>
                                <div class="employee-nip">EMP0003</div>
                                <div class="employee-position">Senior Developer</div>
                            </div>
                        </div>
                    </div>

                    <!-- Jabatan 2: Junior Developer -->
                    <div class="orgchart-node-wrapper">
                        <div class="org-card jabatan">
                            <div>🏷️</div>
                            <div class="org-card-title">Junior Developer</div>
                            <div class="org-card-count">2 Karyawan</div>
                            <button class="show-employees-btn" onclick="toggleEmployees('jab-jrdev')">
                                Lihat Karyawan
                            </button>
                        </div>
                        <div id="jab-jrdev" class="employee-list">
                            <div class="employee-item">
                                <div class="employee-name">David Green</div>
                                <div class="employee-nip">EMP0004</div>
                                <div class="employee-position">Junior Developer</div>
                            </div>
                            <div class="employee-item">
                                <div class="employee-name">Eva White</div>
                                <div class="employee-nip">EMP0005</div>
                                <div class="employee-position">Junior Developer</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Divisi 2: QA -->
            <div class="orgchart-node-wrapper">
                <div class="org-card divisi">
                    <div>📂</div>
                    <div class="org-card-title">QA</div>
                    <div class="org-card-count">2 Karyawan</div>
                    <button class="show-employees-btn" onclick="toggleEmployees('div-qa')">
                        Lihat Karyawan
                    </button>
                </```
````
