<?php

// Simple test script to check if the API endpoint is working
$url = 'https://viera-filament.test/api/pos/sync/products';

// Initialize cURL
$ch = curl_init();

// Set cURL options
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For local development
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // For local development

// Execute the request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

// Close cURL
curl_close($ch);

// Display results
echo "HTTP Code: " . $httpCode . "\n";
echo "Error: " . ($error ?: 'None') . "\n";
echo "Response: " . $response . "\n";

// Test the test endpoint too
echo "\n--- Testing test endpoint ---\n";
$testUrl = 'https://viera-filament.test/api/pos/test/products';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $testUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

$testResponse = curl_exec($ch);
$testHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$testError = curl_error($ch);
curl_close($ch);

echo "Test HTTP Code: " . $testHttpCode . "\n";
echo "Test Error: " . ($testError ?: 'None') . "\n";
echo "Test Response: " . $testResponse . "\n";
