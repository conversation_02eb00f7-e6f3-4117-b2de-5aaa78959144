<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Tolerance Logic in Payroll...\n";

try {
    // Test PayrollService recalculateLatenessDeductions method
    echo "\n1. Testing PayrollService recalculateLatenessDeductions method...\n";
    
    $payrollService = new App\Services\PayrollService();
    $reflection = new ReflectionClass($payrollService);
    
    if ($reflection->hasMethod('recalculateLatenessDeductions')) {
        echo "✓ recalculateLatenessDeductions method exists\n";
    } else {
        echo "✗ recalculateLatenessDeductions method missing\n";
    }
    
    // Test PayrollTransaction with late attendance
    echo "\n2. Testing PayrollTransaction with late attendance...\n";
    
    $payrollTransaction = App\Models\PayrollTransaction::with(['karyawan', 'payrollPeriod'])->first();
    
    if ($payrollTransaction) {
        echo "✓ PayrollTransaction found: ID {$payrollTransaction->id}\n";
        
        // Test absensi relationships
        $lateAbsensi = $payrollTransaction->lateAbsensiRecords()->first();
        if ($lateAbsensi) {
            echo "✓ Late absensi found: ID {$lateAbsensi->id}\n";
            echo "  - Status: {$lateAbsensi->status}\n";
            echo "  - Tolerance given: " . ($lateAbsensi->is_tolerance_given ? 'Yes' : 'No') . "\n";
        } else {
            echo "ℹ No late absensi found for this payroll\n";
        }
        
        // Test current lateness deduction
        echo "  - Current lateness deduction: Rp " . number_format($payrollTransaction->potongan_keterlambatan, 0, ',', '.') . "\n";
        
    } else {
        echo "✗ No PayrollTransaction found\n";
    }
    
    // Test Absensi tolerance methods
    echo "\n3. Testing Absensi tolerance methods...\n";
    
    $lateAbsensi = App\Models\Absensi::where('status', 'terlambat')->first();
    
    if ($lateAbsensi) {
        echo "✓ Late Absensi found: ID {$lateAbsensi->id}\n";
        echo "  - Date: {$lateAbsensi->tanggal_absensi}\n";
        echo "  - Entry time: " . ($lateAbsensi->waktu_masuk ? $lateAbsensi->waktu_masuk->format('H:i') : 'N/A') . "\n";
        echo "  - Tolerance given: " . ($lateAbsensi->is_tolerance_given ? 'Yes' : 'No') . "\n";
        
        if ($lateAbsensi->is_tolerance_given) {
            echo "  - Tolerance reason: {$lateAbsensi->tolerance_reason}\n";
            echo "  - Approved by: " . ($lateAbsensi->toleranceApprovedBy->name ?? 'Unknown') . "\n";
        }
    } else {
        echo "ℹ No late absensi found for testing\n";
    }
    
    // Test PayrollDeduction for lateness
    echo "\n4. Testing PayrollDeduction for lateness...\n";
    
    $latenessDeduction = App\Models\PayrollDeduction::where('jenis_potongan', 'keterlambatan')->first();
    
    if ($latenessDeduction) {
        echo "✓ Lateness deduction found: ID {$latenessDeduction->id}\n";
        echo "  - Description: {$latenessDeduction->deskripsi}\n";
        echo "  - Amount: Rp " . number_format($latenessDeduction->nominal, 0, ',', '.') . "\n";
        echo "  - Reference ID: {$latenessDeduction->kode_referensi}\n";
        
        if ($latenessDeduction->kode_referensi) {
            $refAbsensi = App\Models\Absensi::find($latenessDeduction->kode_referensi);
            if ($refAbsensi) {
                echo "  - Referenced absensi tolerance: " . ($refAbsensi->is_tolerance_given ? 'Yes' : 'No') . "\n";
            }
        }
    } else {
        echo "ℹ No lateness deduction found\n";
    }
    
    echo "\n✅ All tolerance logic tests completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
