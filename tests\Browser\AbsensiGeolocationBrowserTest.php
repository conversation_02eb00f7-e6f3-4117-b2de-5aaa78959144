<?php

namespace Tests\Browser;

use Tests\DuskTestCase;
use <PERSON><PERSON>\Dusk\Browser;
use App\Models\User;
use App\Models\Karyawan;
use App\Models\Schedule;
use App\Models\Shift;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseMigrations;

class AbsensiGeolocationBrowserTest extends DuskTestCase
{
    use DatabaseMigrations;

    protected $user;
    protected $karyawan;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user and karyawan
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'karyawan'
        ]);

        $this->karyawan = Karyawan::factory()->create([
            'id_user' => $this->user->id,
            'nama' => 'Test Karyawan',
            'email' => '<EMAIL>'
        ]);

        // Create schedule for today
        $shift = Shift::factory()->create([
            'nama_shift' => 'Pagi',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00'
        ]);

        Schedule::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $shift->id,
            'tanggal_jadwal' => Carbon::today()->format('Y-m-d')
        ]);
    }

    /** @test */
    public function it_displays_geolocation_interface()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                ->visit('/karyawan/absensis/create')
                ->waitFor('#location-demo', 10)
                ->assertSee('Status Lokasi')
                ->assertPresent('button[onclick="getLocation()"]')
                ->assertPresent('button[onclick="useJakarta()"]')
                ->assertSeeIn('button[onclick="getLocation()"]', 'Dapatkan Lokasi')
                ->assertSeeIn('button[onclick="useJakarta()"]', 'Gunakan Jakarta');
        });
    }

    /** @test */
    public function it_can_use_jakarta_coordinates()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                ->visit('/karyawan/absensis/create')
                ->waitFor('#location-demo', 10)
                ->click('button[onclick="useJakarta()"]')
                ->waitUntilMissing('.bg-blue-50', 5)
                ->assertSeeIn('#location-demo', 'Menggunakan koordinat Jakarta')
                ->assertSeeIn('#location-demo', 'Latitude: -6.2')
                ->assertSeeIn('#location-demo', 'Longitude: 106.816666')
                ->assertInputValue('input[name="latitude"]', '-6.2')
                ->assertInputValue('input[name="longitude"]', '106.816666');
        });
    }

    /** @test */
    public function it_validates_form_with_coordinates()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                ->visit('/karyawan/absensis/create')
                ->waitFor('#location-demo', 10)
                ->click('button[onclick="useJakarta()"]')
                ->waitUntilMissing('.bg-blue-50', 5)
                ->type('textarea[name="keterangan"]', 'Test absensi dengan koordinat Jakarta')
                ->press('Create')
                ->waitForLocation('/karyawan/absensis', 10)
                ->assertPathIs('/karyawan/absensis');
        });
    }

    /** @test */
    public function it_shows_error_when_no_coordinates()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                ->visit('/karyawan/absensis/create')
                ->waitFor('#location-demo', 10)
                ->type('textarea[name="keterangan"]', 'Test tanpa koordinat')
                ->press('Create')
                ->waitForText('Lokasi harus dideteksi', 5)
                ->assertSee('Lokasi harus dideteksi untuk melakukan absensi');
        });
    }

    /** @test */
    public function it_handles_geolocation_button_click()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                ->visit('/karyawan/absensis/create')
                ->waitFor('#location-demo', 10)
                ->click('button[onclick="getLocation()"]')
                ->waitFor('.bg-yellow-50', 5)
                ->assertSeeIn('#location-demo', 'Mengambil lokasi Anda')
                ->assertSeeIn('#location-demo', 'Pastikan GPS aktif');
        });
    }

    /** @test */
    public function it_auto_starts_geolocation()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                ->visit('/karyawan/absensis/create')
                ->waitFor('#location-demo', 10)
                ->pause(2000) // Wait for auto-start
                ->assertDontSee('Klik tombol untuk mendapatkan lokasi');
        });
    }

    /** @test */
    public function it_displays_form_elements_correctly()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                ->visit('/karyawan/absensis/create')
                ->waitFor('#location-demo', 10)
                ->assertPresent('input[name="latitude"]')
                ->assertPresent('input[name="longitude"]')
                ->assertPresent('input[name="foto"]')
                ->assertPresent('textarea[name="keterangan"]')
                ->assertPresent('button[type="submit"]');
        });
    }

    /** @test */
    public function it_can_complete_full_absensi_flow()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                ->visit('/karyawan/absensis/create')
                ->waitFor('#location-demo', 10)
                ->click('button[onclick="useJakarta()"]')
                ->waitUntilMissing('.bg-blue-50', 5)
                ->assertSeeIn('#location-demo', 'Menggunakan koordinat Jakarta')
                ->type('textarea[name="keterangan"]', 'Absensi masuk hari ini')
                ->press('Create')
                ->waitForLocation('/karyawan/absensis', 10)
                ->assertPathIs('/karyawan/absensis')
                ->assertSee('Absensi');
        });
    }

    /** @test */
    public function it_shows_proper_validation_messages()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                ->visit('/karyawan/absensis/create')
                ->waitFor('#location-demo', 10)
                // Try to submit without location
                ->press('Create')
                ->waitForText('Lokasi harus dideteksi', 5)
                ->assertSee('Lokasi harus dideteksi untuk melakukan absensi')
                // Now add location and try again
                ->click('button[onclick="useJakarta()"]')
                ->waitUntilMissing('.bg-blue-50', 5)
                ->type('textarea[name="keterangan"]', 'Test dengan koordinat')
                ->press('Create')
                ->waitForLocation('/karyawan/absensis', 10);
        });
    }

    /** @test */
    public function it_preserves_coordinates_during_form_interaction()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                ->visit('/karyawan/absensis/create')
                ->waitFor('#location-demo', 10)
                ->click('button[onclick="useJakarta()"]')
                ->waitUntilMissing('.bg-blue-50', 5)
                ->assertInputValue('input[name="latitude"]', '-6.2')
                ->assertInputValue('input[name="longitude"]', '106.816666')
                ->type('textarea[name="keterangan"]', 'Test preserving coordinates')
                // Coordinates should still be there
                ->assertInputValue('input[name="latitude"]', '-6.2')
                ->assertInputValue('input[name="longitude"]', '106.816666');
        });
    }
}
