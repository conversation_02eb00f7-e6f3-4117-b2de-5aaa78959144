<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Karyawan;
use App\Models\Absensi;
use App\Models\Schedule;
use App\Models\Shift;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AbsensiGeolocationSimpleTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $karyawan;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::create([
            'name' => 'Test Karyawan',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'karyawan'
        ]);
        
        // Create karyawan record
        $this->karyawan = Karyawan::create([
            'id_user' => $this->user->id,
            'nama' => 'Test Karyawan',
            'email' => '<EMAIL>',
            'nomor_telepon' => '081234567890',
            'alamat' => 'Test Address',
            'tanggal_bergabung' => Carbon::now(),
            'status' => 'aktif'
        ]);

        // Create shift
        $shift = Shift::create([
            'nama_shift' => 'Pagi',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'toleransi_keterlambatan' => 15
        ]);

        // Create schedule for today
        Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $shift->id,
            'tanggal_jadwal' => Carbon::today()->format('Y-m-d'),
            'status' => 'scheduled'
        ]);
    }

    /** @test */
    public function it_can_access_absensi_create_page()
    {
        $response = $this->actingAs($this->user)
            ->get('/karyawan/absensis/create');

        $response->assertStatus(200);
        $response->assertSee('Status Lokasi');
        $response->assertSee('Dapatkan Lokasi');
        $response->assertSee('Gunakan Jakarta');
        $response->assertSee('Input Manual');
    }

    /** @test */
    public function it_validates_required_location_data()
    {
        $response = $this->actingAs($this->user)
            ->post('/karyawan/absensis', [
                'latitude' => '',
                'longitude' => '',
                'keterangan' => 'Test without location'
            ]);

        $response->assertSessionHasErrors(['latitude', 'longitude']);
    }

    /** @test */
    public function it_rejects_zero_coordinates()
    {
        $response = $this->actingAs($this->user)
            ->post('/karyawan/absensis', [
                'latitude' => '0',
                'longitude' => '0',
                'keterangan' => 'Test with zero coordinates'
            ]);

        $response->assertSessionHasErrors(['latitude', 'longitude']);
    }

    /** @test */
    public function it_accepts_valid_jakarta_coordinates()
    {
        $response = $this->actingAs($this->user)
            ->post('/karyawan/absensis', [
                'latitude' => '-6.200000',
                'longitude' => '106.816666',
                'keterangan' => 'Test with Jakarta coordinates'
            ]);

        $response->assertRedirect('/karyawan/absensis');
        
        $this->assertDatabaseHas('absensis', [
            'karyawan_id' => $this->karyawan->id,
            'lokasi_masuk' => '-6.200000,106.816666',
            'tanggal_absensi' => Carbon::today()->format('Y-m-d')
        ]);
    }

    /** @test */
    public function it_accepts_valid_custom_coordinates()
    {
        $response = $this->actingAs($this->user)
            ->post('/karyawan/absensis', [
                'latitude' => '-6.175110',
                'longitude' => '106.865036',
                'keterangan' => 'Test with custom coordinates'
            ]);

        $response->assertRedirect('/karyawan/absensis');
        
        $this->assertDatabaseHas('absensis', [
            'karyawan_id' => $this->karyawan->id,
            'lokasi_masuk' => '-6.175110,106.865036'
        ]);
    }

    /** @test */
    public function it_validates_coordinate_ranges()
    {
        // Test invalid latitude
        $response = $this->actingAs($this->user)
            ->post('/karyawan/absensis', [
                'latitude' => '91.0', // Invalid: > 90
                'longitude' => '106.816666',
                'keterangan' => 'Test invalid latitude'
            ]);

        $response->assertSessionHasErrors(['latitude']);

        // Test invalid longitude
        $response = $this->actingAs($this->user)
            ->post('/karyawan/absensis', [
                'latitude' => '-6.200000',
                'longitude' => '181.0', // Invalid: > 180
                'keterangan' => 'Test invalid longitude'
            ]);

        $response->assertSessionHasErrors(['longitude']);
    }

    /** @test */
    public function it_handles_check_in_and_check_out()
    {
        // First: Check in
        $response = $this->actingAs($this->user)
            ->post('/karyawan/absensis', [
                'latitude' => '-6.200000',
                'longitude' => '106.816666',
                'keterangan' => 'Check in test'
            ]);

        $response->assertRedirect('/karyawan/absensis');
        
        $absensi = Absensi::where('karyawan_id', $this->karyawan->id)
            ->where('tanggal_absensi', Carbon::today()->format('Y-m-d'))
            ->first();

        $this->assertNotNull($absensi);
        $this->assertNotNull($absensi->waktu_masuk);
        $this->assertNull($absensi->waktu_keluar);
        $this->assertEquals('-6.200000,106.816666', $absensi->lokasi_masuk);

        // Second: Check out
        $response = $this->actingAs($this->user)
            ->post('/karyawan/absensis', [
                'latitude' => '-6.201000',
                'longitude' => '106.817000',
                'keterangan' => 'Check out test'
            ]);

        $response->assertRedirect('/karyawan/absensis');
        
        $absensi->refresh();
        $this->assertNotNull($absensi->waktu_keluar);
        $this->assertEquals('-6.201000,106.817000', $absensi->lokasi_keluar);
    }

    /** @test */
    public function it_stores_precise_coordinates()
    {
        $testLat = '-6.123456';
        $testLng = '106.789012';

        $response = $this->actingAs($this->user)
            ->post('/karyawan/absensis', [
                'latitude' => $testLat,
                'longitude' => $testLng,
                'keterangan' => 'Test precise coordinates'
            ]);

        $response->assertRedirect('/karyawan/absensis');
        
        $absensi = Absensi::where('karyawan_id', $this->karyawan->id)->first();
        $this->assertEquals($testLat . ',' . $testLng, $absensi->lokasi_masuk);
    }

    /** @test */
    public function it_prevents_absensi_without_karyawan_record()
    {
        // Create user without karyawan record
        $userWithoutKaryawan = User::create([
            'name' => 'User Without Karyawan',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'karyawan'
        ]);

        $response = $this->actingAs($userWithoutKaryawan)
            ->get('/karyawan/absensis/create');

        // Should redirect due to missing karyawan record
        $response->assertRedirect('/karyawan/absensis');
    }
}
