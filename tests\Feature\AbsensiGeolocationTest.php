<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Karyawan;
use App\Models\Absensi;
use App\Models\Schedule;
use App\Models\Shift;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

class AbsensiGeolocationTest extends TestCase
{
    use RefreshDatabase;

    protected $karyawan;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user and karyawan
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'karyawan'
        ]);
        
        $this->karyawan = Karyawan::factory()->create([
            'id_user' => $this->user->id,
            'nama' => 'Test Karyawan',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function it_can_access_absensi_create_page()
    {
        $response = $this->actingAs($this->user)
            ->get('/karyawan/absensis/create');

        $response->assertStatus(200);
        $response->assertSee('Status Lokasi');
        $response->assertSee('Dapatkan Lokasi');
        $response->assertSee('Gunakan Jakarta');
    }

    /** @test */
    public function it_validates_required_location_data()
    {
        $this->actingAs($this->user);

        $response = $this->post('/karyawan/absensis', [
            'latitude' => '',
            'longitude' => '',
            'foto' => null,
            'keterangan' => 'Test absensi'
        ]);

        $response->assertSessionHasErrors(['latitude', 'longitude']);
    }

    /** @test */
    public function it_validates_location_coordinates_range()
    {
        $this->actingAs($this->user);

        // Test invalid latitude (out of range)
        $response = $this->post('/karyawan/absensis', [
            'latitude' => '91.0', // Invalid: > 90
            'longitude' => '106.816666',
            'foto' => null,
            'keterangan' => 'Test absensi'
        ]);

        $response->assertSessionHasErrors(['latitude']);

        // Test invalid longitude (out of range)
        $response = $this->post('/karyawan/absensis', [
            'latitude' => '-6.200000',
            'longitude' => '181.0', // Invalid: > 180
            'foto' => null,
            'keterangan' => 'Test absensi'
        ]);

        $response->assertSessionHasErrors(['longitude']);
    }

    /** @test */
    public function it_rejects_zero_coordinates()
    {
        $this->actingAs($this->user);

        $response = $this->post('/karyawan/absensis', [
            'latitude' => '0',
            'longitude' => '0',
            'foto' => null,
            'keterangan' => 'Test absensi'
        ]);

        $response->assertSessionHasErrors(['latitude', 'longitude']);
    }

    /** @test */
    public function it_accepts_valid_jakarta_coordinates()
    {
        $this->actingAs($this->user);

        // Create a schedule for today
        $shift = Shift::factory()->create([
            'nama_shift' => 'Pagi',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00'
        ]);

        Schedule::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $shift->id,
            'tanggal_jadwal' => Carbon::today()->format('Y-m-d')
        ]);

        $response = $this->post('/karyawan/absensis', [
            'latitude' => '-6.200000',
            'longitude' => '106.816666',
            'foto' => null,
            'keterangan' => 'Test absensi dengan koordinat Jakarta'
        ]);

        $response->assertRedirect();
        
        $this->assertDatabaseHas('absensis', [
            'karyawan_id' => $this->karyawan->id,
            'lokasi_masuk' => '-6.200000,106.816666',
            'tanggal_absensi' => Carbon::today()->format('Y-m-d')
        ]);
    }

    /** @test */
    public function it_accepts_valid_custom_coordinates()
    {
        $this->actingAs($this->user);

        // Create a schedule for today
        $shift = Shift::factory()->create();
        Schedule::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $shift->id,
            'tanggal_jadwal' => Carbon::today()->format('Y-m-d')
        ]);

        $response = $this->post('/karyawan/absensis', [
            'latitude' => '-6.175110',
            'longitude' => '106.865036',
            'foto' => null,
            'keterangan' => 'Test absensi dengan koordinat custom'
        ]);

        $response->assertRedirect();
        
        $this->assertDatabaseHas('absensis', [
            'karyawan_id' => $this->karyawan->id,
            'lokasi_masuk' => '-6.175110,106.865036'
        ]);
    }

    /** @test */
    public function it_handles_check_in_and_check_out_properly()
    {
        $this->actingAs($this->user);

        $shift = Shift::factory()->create();
        Schedule::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $shift->id,
            'tanggal_jadwal' => Carbon::today()->format('Y-m-d')
        ]);

        // First: Check in
        $response = $this->post('/karyawan/absensis', [
            'latitude' => '-6.200000',
            'longitude' => '106.816666',
            'foto' => null,
            'keterangan' => 'Check in'
        ]);

        $response->assertRedirect();
        
        $absensi = Absensi::where('karyawan_id', $this->karyawan->id)
            ->where('tanggal_absensi', Carbon::today()->format('Y-m-d'))
            ->first();

        $this->assertNotNull($absensi);
        $this->assertNotNull($absensi->waktu_masuk);
        $this->assertNull($absensi->waktu_keluar);
        $this->assertEquals('-6.200000,106.816666', $absensi->lokasi_masuk);

        // Second: Check out
        $response = $this->post('/karyawan/absensis', [
            'latitude' => '-6.201000',
            'longitude' => '106.817000',
            'foto' => null,
            'keterangan' => 'Check out'
        ]);

        $response->assertRedirect();
        
        $absensi->refresh();
        $this->assertNotNull($absensi->waktu_keluar);
        $this->assertEquals('-6.201000,106.817000', $absensi->lokasi_keluar);
    }

    /** @test */
    public function it_prevents_absensi_without_karyawan_record()
    {
        // Create user without karyawan record
        $userWithoutKaryawan = User::factory()->create([
            'role' => 'karyawan'
        ]);

        $response = $this->actingAs($userWithoutKaryawan)
            ->post('/karyawan/absensis', [
                'latitude' => '-6.200000',
                'longitude' => '106.816666',
                'foto' => null,
                'keterangan' => 'Test'
            ]);

        $response->assertRedirect();
        $this->assertEquals(0, Absensi::count());
    }

    /** @test */
    public function it_stores_location_data_correctly()
    {
        $this->actingAs($this->user);

        $shift = Shift::factory()->create();
        Schedule::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $shift->id,
            'tanggal_jadwal' => Carbon::today()->format('Y-m-d')
        ]);

        $testLat = '-6.123456';
        $testLng = '106.789012';

        $response = $this->post('/karyawan/absensis', [
            'latitude' => $testLat,
            'longitude' => $testLng,
            'foto' => null,
            'keterangan' => 'Test precise coordinates'
        ]);

        $response->assertRedirect();
        
        $absensi = Absensi::where('karyawan_id', $this->karyawan->id)->first();
        $this->assertEquals($testLat . ',' . $testLng, $absensi->lokasi_masuk);
    }
}
