<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Karyawan;
use App\Models\Entitas;
use App\Models\Departemen;
use App\Models\Divisi;
use App\Models\Jabatan;
use App\Models\Shift;
use App\Models\Schedule;
use App\Models\Absensi;
use App\Models\JadwalMasal;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Carbon\Carbon;

class AbsensiSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $supervisor;
    protected $karyawan;
    protected $entitas;
    protected $departemen;
    protected $divisi;
    protected $jabatan;
    protected $shift;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setupTestData();
    }

    protected function setupTestData()
    {
        // Create admin user
        $this->admin = User::create([
            'name' => 'Admin Test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin'
        ]);

        // Create supervisor user
        $this->supervisor = User::create([
            'name' => 'Supervisor Test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'supervisor'
        ]);

        // Create organizational structure
        $this->entitas = Entitas::create([
            'nama' => 'PT. Test Company',
            'alamat' => 'Jakarta',
            'keterangan' => 'Test company'
        ]);

        $this->departemen = Departemen::create([
            'nama_departemen' => 'IT Department',
            'keterangan' => 'Information Technology'
        ]);

        $this->divisi = Divisi::create([
            'nama_divisi' => 'Software Development',
            'id_departemen' => $this->departemen->id,
            'keterangan' => 'Software Development Division'
        ]);

        $this->jabatan = Jabatan::create([
            'nama_jabatan' => 'Software Engineer',
            'keterangan' => 'Software Engineer Position'
        ]);

        // Create shift
        $this->shift = Shift::create([
            'nama_shift' => 'Regular Shift',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'toleransi_keterlambatan' => 15,
            'is_active' => true
        ]);

        // Create karyawan
        $this->karyawan = Karyawan::create([
            'nama_lengkap' => 'John Doe',
            'nip' => 'EMP001',
            'nik' => '1234567890123456',
            'email' => 'john' . time() . '@test.com',
            'tanggal_lahir' => '1990-01-01',
            'jenis_kelamin' => 'Laki-laki',
            'id_entitas' => $this->entitas->id,
            'id_departemen' => $this->departemen->id,
            'id_divisi' => $this->divisi->id,
            'id_jabatan' => $this->jabatan->id,
            'supervisor_id' => $this->supervisor->id,
            'status_aktif' => 1
        ]);

        // Create user for karyawan
        $karyawanUser = User::create([
            'name' => 'John Doe',
            'email' => $this->karyawan->email,
            'password' => bcrypt('password'),
            'role' => 'karyawan'
        ]);

        $this->karyawan->update(['id_user' => $karyawanUser->id]);
    }

    /** @test */
    public function test_admin_can_access_admin_panel()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin');

        $response->assertStatus(200);
    }

    /** @test */
    public function test_supervisor_can_access_admin_panel()
    {
        $response = $this->actingAs($this->supervisor)
            ->get('/admin');

        $response->assertStatus(200);
    }

    /** @test */
    public function test_karyawan_cannot_access_admin_panel()
    {
        $karyawanUser = User::find($this->karyawan->id_user);

        $response = $this->actingAs($karyawanUser)
            ->get('/admin');

        $response->assertStatus(403);
    }

    /** @test */
    public function test_karyawan_can_access_karyawan_panel()
    {
        $karyawanUser = User::find($this->karyawan->id_user);

        $response = $this->actingAs($karyawanUser)
            ->get('/karyawan');

        $response->assertStatus(200);
    }

    /** @test */
    public function test_create_karyawan_with_new_email()
    {
        $this->actingAs($this->admin);

        $karyawanData = [
            'nama_lengkap' => 'Jane Smith',
            'nip' => 'EMP002',
            'nik' => '9876543210987654',
            'email' => '<EMAIL>',
            'tanggal_lahir' => '1992-05-15',
            'jenis_kelamin' => 'Perempuan',
            'id_entitas' => $this->entitas->id,
            'id_departemen' => $this->departemen->id,
            'id_divisi' => $this->divisi->id,
            'id_jabatan' => $this->jabatan->id,
            'status_aktif' => 1
        ];

        $karyawan = Karyawan::create($karyawanData);

        $this->assertDatabaseHas('karyawan', [
            'email' => '<EMAIL>',
            'nama_lengkap' => 'Jane Smith'
        ]);

        // Test user creation logic
        $existingUser = User::where('email', '<EMAIL>')->first();
        $this->assertNull($existingUser); // User should not exist yet

        // Simulate the CreateAction logic
        if (!empty($karyawanData['email'])) {
            $existingUser = User::where('email', $karyawan->email)->first();

            if (!$existingUser) {
                $user = User::create([
                    'name' => $karyawan->nama_lengkap,
                    'email' => $karyawan->email,
                    'password' => bcrypt('viera123'),
                    'role' => 'karyawan',
                ]);
                $karyawan->update(['id_user' => $user->id]);
            }
        }

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'role' => 'karyawan'
        ]);
    }

    /** @test */
    public function test_create_karyawan_with_existing_email()
    {
        $this->actingAs($this->admin);

        // Try to create karyawan with existing email
        $karyawanData = [
            'nama_lengkap' => 'John Duplicate',
            'nip' => 'EMP003',
            'nik' => '1111222233334444',
            'email' => '<EMAIL>', // This email already exists
            'tanggal_lahir' => '1988-03-20',
            'jenis_kelamin' => 'Laki-laki',
            'id_entitas' => $this->entitas->id,
            'id_departemen' => $this->departemen->id,
            'id_divisi' => $this->divisi->id,
            'id_jabatan' => $this->jabatan->id,
            'status_aktif' => 1
        ];

        $karyawan = Karyawan::create($karyawanData);

        // Simulate the CreateAction logic for existing email
        if (!empty($karyawanData['email'])) {
            $existingUser = User::where('email', $karyawan->email)->first();

            if ($existingUser) {
                $karyawan->update(['id_user' => $existingUser->id]);
            }
        }

        $this->assertDatabaseHas('karyawan', [
            'email' => '<EMAIL>',
            'nama_lengkap' => 'John Duplicate'
        ]);

        // Should link to existing user
        $this->assertEquals($this->karyawan->id_user, $karyawan->id_user);
    }

    /** @test */
    public function test_create_schedule_for_karyawan()
    {
        $this->actingAs($this->admin);

        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'is_active' => true
        ]);

        $this->assertDatabaseHas('jadwal_kerja', [
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today()->format('Y-m-d')
        ]);
    }

    /** @test */
    public function test_create_absensi_with_location()
    {
        $this->actingAs($this->admin);

        // Create schedule first
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'is_active' => true
        ]);

        $absensi = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => '08:00:00',
            'waktu_keluar' => '17:00:00',
            'status' => 'hadir',
            'lokasi_masuk' => '-6.200000,106.816666',
            'latitude_masuk' => -6.200000,
            'longitude_masuk' => 106.816666,
            'lokasi_keluar' => '-6.200100,106.816766',
            'latitude_keluar' => -6.200100,
            'longitude_keluar' => 106.816766,
            'foto_masuk' => 'absensi/masuk/test.jpg',
            'foto_keluar' => 'absensi/keluar/test.jpg',
            'approved_by' => $this->supervisor->id,
            'approved_at' => Carbon::now()
        ]);

        $this->assertDatabaseHas('absensi', [
            'karyawan_id' => $this->karyawan->id,
            'latitude_masuk' => -6.200000,
            'longitude_masuk' => 106.816666,
            'latitude_keluar' => -6.200100,
            'longitude_keluar' => 106.816766
        ]);

        // Test location data
        $this->assertEquals(-6.200000, $absensi->latitude_masuk);
        $this->assertEquals(106.816666, $absensi->longitude_masuk);
        $this->assertEquals(-6.200100, $absensi->latitude_keluar);
        $this->assertEquals(106.816766, $absensi->longitude_keluar);
    }

    /** @test */
    public function test_create_jadwal_masal_filtered_by_entitas()
    {
        $this->actingAs($this->admin);

        // Create another entitas and karyawan
        $entitas2 = Entitas::create([
            'nama' => 'PT. Another Company',
            'alamat' => 'Bandung',
            'keterangan' => 'Another test company'
        ]);

        $karyawan2 = Karyawan::create([
            'nama_lengkap' => 'Alice Johnson',
            'nip' => 'EMP004',
            'nik' => '5555666677778888',
            'email' => '<EMAIL>',
            'tanggal_lahir' => '1991-07-10',
            'jenis_kelamin' => 'Perempuan',
            'id_entitas' => $entitas2->id,
            'id_departemen' => $this->departemen->id,
            'id_divisi' => $this->divisi->id,
            'id_jabatan' => $this->jabatan->id,
            'status_aktif' => 1
        ]);

        // Create jadwal masal for first entitas only
        $jadwalMasal = JadwalMasal::create([
            'nama_jadwal' => 'Jadwal Test Entitas 1',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today()->addDays(7),
            'shift_id' => $this->shift->id,
            'keterangan' => 'Test jadwal masal untuk entitas 1'
        ]);

        // Attach only karyawan from first entitas
        $karyawanFromEntitas1 = Karyawan::where('id_entitas', $this->entitas->id)->pluck('id');
        $jadwalMasal->karyawan()->attach($karyawanFromEntitas1);

        $this->assertDatabaseHas('jadwal_masal', [
            'nama_jadwal' => 'Jadwal Test Entitas 1'
        ]);

        // Test that only karyawan from entitas 1 are attached
        $attachedKaryawan = $jadwalMasal->karyawan()->pluck('karyawan.id');
        $this->assertTrue($attachedKaryawan->contains($this->karyawan->id));
        $this->assertFalse($attachedKaryawan->contains($karyawan2->id));
    }

    /** @test */
    public function test_generate_individual_schedules_from_jadwal_masal()
    {
        $this->actingAs($this->admin);

        // Create jadwal masal
        $jadwalMasal = JadwalMasal::create([
            'nama_jadwal' => 'Jadwal Test Generate',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today()->addDays(2),
            'shift_id' => $this->shift->id,
            'keterangan' => 'Test generate individual schedules'
        ]);

        $jadwalMasal->karyawan()->attach([$this->karyawan->id]);

        // Generate individual schedules
        $startDate = Carbon::parse($jadwalMasal->tanggal_mulai);
        $endDate = Carbon::parse($jadwalMasal->tanggal_selesai);
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            foreach ($jadwalMasal->karyawan as $karyawan) {
                Schedule::create([
                    'karyawan_id' => $karyawan->id,
                    'shift_id' => $jadwalMasal->shift_id,
                    'tanggal_jadwal' => $currentDate->format('Y-m-d'),
                    'is_active' => true,
                    'keterangan' => "Generated from: {$jadwalMasal->nama_jadwal}"
                ]);
            }
            $currentDate->addDay();
        }

        // Test that individual schedules were created
        $expectedSchedules = $startDate->diffInDays($endDate) + 1; // +1 because inclusive
        $createdSchedules = Schedule::where('karyawan_id', $this->karyawan->id)
            ->whereBetween('tanggal_jadwal', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->count();

        $this->assertEquals($expectedSchedules, $createdSchedules);
    }

    /** @test */
    public function test_karyawan_entitas_relationship()
    {
        $this->assertInstanceOf(Entitas::class, $this->karyawan->entitas);
        $this->assertEquals($this->entitas->id, $this->karyawan->entitas->id);
        $this->assertEquals('PT. Test Company', $this->karyawan->entitas->nama);
    }

    /** @test */
    public function test_absensi_approval_workflow()
    {
        $this->actingAs($this->supervisor);

        // Create schedule
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'is_active' => true
        ]);

        // Create unapproved absensi
        $absensi = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => '08:00:00',
            'waktu_keluar' => '17:00:00',
            'status' => 'hadir',
            'latitude_masuk' => -6.200000,
            'longitude_masuk' => 106.816666
        ]);

        $this->assertNull($absensi->approved_at);
        $this->assertFalse($absensi->is_approved);

        // Approve absensi
        $absensi->update([
            'approved_by' => $this->supervisor->id,
            'approved_at' => Carbon::now()
        ]);

        $absensi->refresh();
        $this->assertNotNull($absensi->approved_at);
        $this->assertTrue($absensi->is_approved);
        $this->assertEquals($this->supervisor->id, $absensi->approved_by);
    }

    /** @test */
    public function test_role_based_access_control()
    {
        // Test admin access
        $this->actingAs($this->admin);
        $adminKaryawanCount = Karyawan::count();
        $this->assertGreaterThan(0, $adminKaryawanCount);

        // Test supervisor access (should see supervised employees)
        $this->actingAs($this->supervisor);
        $supervisedKaryawan = Karyawan::where('supervisor_id', $this->supervisor->id)->count();
        $this->assertGreaterThan(0, $supervisedKaryawan);

        // Test karyawan access (should only see own data)
        $karyawanUser = User::find($this->karyawan->id_user);
        $this->actingAs($karyawanUser);

        $ownKaryawan = Karyawan::where('id_user', $karyawanUser->id)->first();
        $this->assertNotNull($ownKaryawan);
        $this->assertEquals($this->karyawan->id, $ownKaryawan->id);
    }
}
