<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Karyawan;
use App\Models\Shift;
use App\Models\Schedule;
use App\Models\Absensi;
use App\Models\Jabatan;
use App\Models\Divisi;
use App\Models\Entitas;
use App\Models\Departemen;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;
use Carbon\Carbon;

class AttendanceGeolocationTest extends TestCase
{
    use RefreshDatabase;

    protected $entitas;
    protected $departemen;
    protected $divisi;
    protected $jabatan;
    protected $shift;

    protected function setUp(): void
    {
        parent::setUp();

        // Create basic organizational structure
        $this->createOrganizationalStructure();
        $this->createShift();

        // Setup fake storage
        Storage::fake('public');
    }

    protected function createOrganizationalStructure()
    {
        $this->entitas = Entitas::create([
            'nama' => 'PT Viera Anugrah Pertama',
            'alamat' => 'Jakarta',
            'keterangan' => 'Test entity for testing',
        ]);

        $this->departemen = Departemen::create([
            'nama_departemen' => 'IT Department',
            'created_by' => 1,
        ]);

        $this->divisi = Divisi::create([
            'nama_divisi' => 'Software Development',
            'id_departemen' => $this->departemen->id,
            'created_by' => 1,
        ]);

        $this->jabatan = Jabatan::create([
            'nama_jabatan' => 'Software Developer',
            'created_by' => 1,
        ]);
    }

    protected function createShift()
    {
        $this->shift = Shift::create([
            'nama_shift' => 'Shift Pagi',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'toleransi_keterlambatan' => 15,
            'is_active' => true,
            'created_by' => 1,
        ]);
    }

    protected function createKaryawan($user = null)
    {
        if (!$user) {
            $user = User::factory()->create(['role' => 'karyawan']);
        }

        return Karyawan::create([
            'id_user' => $user->id,
            'nip' => 'K' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT),
            'nama_lengkap' => 'Karyawan Test',
            'jenis_kelamin' => 'Laki-laki',
            'kota_lahir' => 'Jakarta',
            'tanggal_lahir' => '1990-01-01',
            'alamat' => 'Jl. Test No. 123',
            'nomor_telepon' => '08123456789',
            'email' => $user->email,
            'status_aktif' => 1,
            'id_entitas' => $this->entitas->id,
            'id_departemen' => $this->departemen->id,
            'id_divisi' => $this->divisi->id,
            'id_jabatan' => $this->jabatan->id,
            'created_by' => 1,
        ]);
    }

    protected function createSchedule($karyawan, $date = null)
    {
        return Schedule::create([
            'karyawan_id' => $karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => $date ?: Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => $this->shift->waktu_mulai,
            'waktu_keluar' => $this->shift->waktu_selesai,
            'status' => 'aktif',
        ]);
    }

    /** @test */
    public function attendance_form_requires_geolocation_data()
    {
        // Create user and employee
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);
        $schedule = $this->createSchedule($karyawan);

        // Login as karyawan
        $this->actingAs($user);

        // Create fake image
        $image = UploadedFile::fake()->image('selfie.jpg', 640, 480);

        // Try to submit attendance without geolocation
        $response = $this->post('/karyawan/absensis', [
            'karyawan_id' => $karyawan->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'foto' => $image,
            'keterangan' => 'Test attendance',
            // Missing latitude and longitude
        ]);

        // Should fail validation or be handled by beforeCreate
        $this->assertDatabaseMissing('absensi', [
            'karyawan_id' => $karyawan->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
        ]);
    }

    /** @test */
    public function attendance_can_be_created_with_valid_geolocation()
    {
        // Create user and employee
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);
        $schedule = $this->createSchedule($karyawan);

        // Login as karyawan
        $this->actingAs($user);

        // Create fake image
        $image = UploadedFile::fake()->image('selfie.jpg', 640, 480);

        // Valid Jakarta coordinates
        $latitude = -6.200000;
        $longitude = 106.816666;

        // Submit attendance with valid geolocation
        $response = $this->post('/karyawan/absensis', [
            'karyawan_id' => $karyawan->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'latitude' => $latitude,
            'longitude' => $longitude,
            'foto' => $image,
            'keterangan' => 'Test attendance with geolocation',
        ]);

        // Should create attendance record
        $this->assertDatabaseHas('absensi', [
            'karyawan_id' => $karyawan->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'lokasi_masuk' => $latitude . ',' . $longitude,
        ]);
    }

    /** @test */
    public function geolocation_coordinates_are_validated()
    {
        // Create user and employee
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);

        // Test various invalid coordinates
        $invalidCoordinates = [
            ['latitude' => 91, 'longitude' => 106.816666], // Invalid latitude > 90
            ['latitude' => -91, 'longitude' => 106.816666], // Invalid latitude < -90
            ['latitude' => -6.200000, 'longitude' => 181], // Invalid longitude > 180
            ['latitude' => -6.200000, 'longitude' => -181], // Invalid longitude < -180
            ['latitude' => 'invalid', 'longitude' => 106.816666], // Non-numeric latitude
            ['latitude' => -6.200000, 'longitude' => 'invalid'], // Non-numeric longitude
        ];

        foreach ($invalidCoordinates as $coords) {
            $attendance = new Absensi([
                'karyawan_id' => $karyawan->id,
                'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
                'lokasi_masuk' => $coords['latitude'] . ',' . $coords['longitude'],
                'waktu_masuk' => '08:00:00',
                'status' => 'hadir',
            ]);

            // For now, we just verify the format is stored
            // In a real implementation, you might want to add validation
            $this->assertIsString($attendance->lokasi_masuk);
        }
    }

    /** @test */
    public function image_upload_works_correctly()
    {
        // Create user and employee
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);
        $schedule = $this->createSchedule($karyawan);

        // Login as karyawan
        $this->actingAs($user);

        // Create fake image
        $image = UploadedFile::fake()->image('selfie.jpg', 640, 480);

        // Submit attendance with image
        $response = $this->post('/karyawan/absensis', [
            'karyawan_id' => $karyawan->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'latitude' => -6.200000,
            'longitude' => 106.816666,
            'foto' => $image,
            'keterangan' => 'Test with image upload',
        ]);

        // Verify image was stored
        $attendance = Absensi::where('karyawan_id', $karyawan->id)
            ->where('tanggal_absensi', Carbon::today()->format('Y-m-d'))
            ->first();

        if ($attendance && $attendance->foto_masuk) {
            Storage::disk('public')->assertExists($attendance->foto_masuk);
        }
    }

    /** @test */
    public function attendance_page_contains_geolocation_javascript()
    {
        // Create user and employee
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);

        // Login as karyawan
        $this->actingAs($user);

        // Access attendance create page
        $response = $this->get('/karyawan/absensis/create');

        // Check that the page loads successfully
        $response->assertStatus(200);

        // In a real implementation, you might check for specific JavaScript
        // or hidden input fields for latitude/longitude
        $response->assertSee('name="latitude"', false);
        $response->assertSee('name="longitude"', false);
    }

    /** @test */
    public function checkout_updates_existing_attendance_record()
    {
        // Create user and employee
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);
        $schedule = $this->createSchedule($karyawan);

        // Create existing check-in record
        $attendance = Absensi::create([
            'karyawan_id' => $karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => '08:00:00',
            'lokasi_masuk' => '-6.200000,106.816666',
            'foto_masuk' => 'checkin.jpg',
            'status' => 'hadir',
        ]);

        // Login as karyawan
        $this->actingAs($user);

        // Create fake checkout image
        $checkoutImage = UploadedFile::fake()->image('checkout.jpg', 640, 480);

        // Submit checkout
        $response = $this->post('/karyawan/absensis', [
            'karyawan_id' => $karyawan->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'latitude' => -6.201000,
            'longitude' => 106.817000,
            'foto' => $checkoutImage,
            'keterangan' => 'Checkout test',
        ]);

        // Verify the same record was updated, not a new one created
        $this->assertDatabaseCount('absensi', 1);

        $updatedAttendance = Absensi::find($attendance->id);
        $this->assertNotNull($updatedAttendance->waktu_keluar);
        $this->assertNotNull($updatedAttendance->lokasi_keluar);
        $this->assertNotNull($updatedAttendance->foto_keluar);
    }
}
