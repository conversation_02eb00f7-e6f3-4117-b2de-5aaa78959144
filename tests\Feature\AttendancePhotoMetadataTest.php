<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Karyawan;
use App\Models\Absensi;
use App\Models\Schedule;
use App\Models\Shift;
use App\Services\PhotoMetadataService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Carbon\Carbon;

class AttendancePhotoMetadataTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $karyawan;
    protected $shift;
    protected $schedule;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user and employee
        $this->user = User::factory()->create(['role' => 'karyawan']);
        $this->karyawan = Karyawan::factory()->create(['id_user' => $this->user->id]);

        // Create shift and schedule
        $this->shift = Shift::factory()->create([
            'nama_shift' => 'Regular',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'toleransi_keterlambatan' => 15
        ]);

        $this->schedule = Schedule::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today()->format('Y-m-d')
        ]);
    }

    /** @test */
    public function photo_metadata_service_can_extract_metadata()
    {
        $file = UploadedFile::fake()->image('test.jpg', 640, 480);
        $latitude = -6.200000;
        $longitude = 106.816666;

        $metadata = PhotoMetadataService::extractMetadata($file, $latitude, $longitude);

        $this->assertArrayHasKey('filename', $metadata);
        $this->assertArrayHasKey('size', $metadata);
        $this->assertArrayHasKey('mime_type', $metadata);
        $this->assertArrayHasKey('captured_at', $metadata);
        $this->assertArrayHasKey('latitude', $metadata);
        $this->assertArrayHasKey('longitude', $metadata);
        $this->assertArrayHasKey('status_kehadiran', $metadata);

        $this->assertEquals($latitude, $metadata['latitude']);
        $this->assertEquals($longitude, $metadata['longitude']);
        $this->assertEquals('test.jpg', $metadata['filename']);
        $this->assertEquals('image/jpeg', $metadata['mime_type']);
    }

    /** @test */
    public function attendance_status_is_determined_correctly()
    {
        // Test on time attendance
        Carbon::setTestNow(Carbon::today()->setTime(8, 0)); // Exactly on time
        $status = PhotoMetadataService::determineAttendanceStatus($this->karyawan->id);
        $this->assertEquals('Tepat Waktu', $status);

        // Test within tolerance
        Carbon::setTestNow(Carbon::today()->setTime(8, 10)); // 10 minutes late, within tolerance
        $status = PhotoMetadataService::determineAttendanceStatus($this->karyawan->id);
        $this->assertEquals('Tepat Waktu', $status);

        // Test late attendance
        Carbon::setTestNow(Carbon::today()->setTime(8, 20)); // 20 minutes late, beyond tolerance
        $status = PhotoMetadataService::determineAttendanceStatus($this->karyawan->id);
        $this->assertEquals('Telat', $status);
    }

    /** @test */
    public function metadata_is_formatted_correctly_for_display()
    {
        $metadata = [
            'latitude' => -6.200000,
            'longitude' => 106.816666,
            'captured_at' => '2024-01-20T08:00:00.000000Z',
            'status_kehadiran' => 'Tepat Waktu',
            'camera_make' => 'XIAOMI',
            'camera_model' => '13T'
        ];

        $formatted = PhotoMetadataService::formatMetadataForDisplay($metadata);

        $this->assertArrayHasKey('coordinates', $formatted);
        $this->assertArrayHasKey('datetime', $formatted);
        $this->assertArrayHasKey('status_kehadiran', $formatted);
        $this->assertArrayHasKey('camera', $formatted);

        $this->assertEquals('-6.200000°, 106.816666°', $formatted['coordinates']);
        $this->assertEquals('Tepat Waktu', $formatted['status_kehadiran']);
        $this->assertEquals('XIAOMI 13T', $formatted['camera']);
    }

    /** @test */
    public function attendance_can_be_created_with_photo_metadata()
    {
        $this->actingAs($this->user);

        $metadata = [
            'timestamp' => Carbon::now()->toISOString(),
            'latitude' => -6.200000,
            'longitude' => 106.816666,
            'status_kehadiran' => 'Tepat Waktu',
            'camera_info' => 'XIAOMI 13T'
        ];

        // Create attendance directly using model since we're testing the service
        $absensi = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $this->schedule->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => Carbon::now(),
            'latitude_masuk' => -6.200000,
            'longitude_masuk' => 106.816666,
            'foto_masuk' => 'test.jpg',
            'metadata_foto_masuk' => $metadata,
            'status' => 'hadir'
        ]);

        $this->assertDatabaseHas('absensi', [
            'karyawan_id' => $this->karyawan->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
        ]);

        $this->assertNotNull($absensi->metadata_foto_masuk);
        $this->assertIsArray($absensi->metadata_foto_masuk);
        $this->assertEquals('Tepat Waktu', $absensi->metadata_foto_masuk['status_kehadiran']);
    }

    /** @test */
    public function checkout_attendance_saves_metadata_correctly()
    {
        // First create check-in attendance
        $absensi = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $this->schedule->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => Carbon::now(),
            'latitude_masuk' => -6.200000,
            'longitude_masuk' => 106.816666,
            'foto_masuk' => 'test-checkin.jpg',
            'metadata_foto_masuk' => ['status_kehadiran' => 'Tepat Waktu'],
            'status' => 'hadir'
        ]);

        $this->actingAs($this->user);

        $metadata = [
            'timestamp' => Carbon::now()->toISOString(),
            'latitude' => -6.201000,
            'longitude' => 106.817000,
            'status_kehadiran' => 'Checkout',
            'camera_info' => 'XIAOMI 13T'
        ];

        // Update the existing attendance record for checkout
        $absensi->update([
            'waktu_keluar' => Carbon::now(),
            'latitude_keluar' => -6.201000,
            'longitude_keluar' => 106.817000,
            'foto_keluar' => 'checkout.jpg',
            'metadata_foto_keluar' => $metadata,
        ]);

        $absensi->refresh();

        $this->assertNotNull($absensi->waktu_keluar);
        $this->assertNotNull($absensi->metadata_foto_keluar);
        $this->assertIsArray($absensi->metadata_foto_keluar);
        $this->assertEquals('Checkout', $absensi->metadata_foto_keluar['status_kehadiran']);
    }

    /** @test */
    public function metadata_is_preserved_in_database()
    {
        $metadata = [
            'timestamp' => '2024-01-20T08:00:00.000000Z',
            'latitude' => -6.200000,
            'longitude' => 106.816666,
            'status_kehadiran' => 'Tepat Waktu',
            'camera_info' => 'XIAOMI 13T',
            'coordinates_display' => '-6.200000°, 106.816666°'
        ];

        $absensi = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $this->schedule->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => Carbon::now(),
            'latitude_masuk' => -6.200000,
            'longitude_masuk' => 106.816666,
            'foto_masuk' => 'test.jpg',
            'metadata_foto_masuk' => $metadata,
            'status' => 'hadir'
        ]);

        $this->assertDatabaseHas('absensi', [
            'id' => $absensi->id,
            'karyawan_id' => $this->karyawan->id,
        ]);

        $retrieved = Absensi::find($absensi->id);
        $this->assertEquals($metadata, $retrieved->metadata_foto_masuk);
        $this->assertEquals('Tepat Waktu', $retrieved->metadata_foto_masuk['status_kehadiran']);
    }
}
