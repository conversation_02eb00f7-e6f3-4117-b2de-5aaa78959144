<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Karyawan;
use App\Models\Shift;
use App\Models\Schedule;
use App\Models\Absensi;
use App\Models\Jabatan;
use App\Models\Divisi;
use App\Models\Entitas;
use App\Models\Departemen;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;
use Carbon\Carbon;

class AttendanceSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $entitas;
    protected $departemen;
    protected $divisi;
    protected $jabatan;
    protected $shift;

    protected function setUp(): void
    {
        parent::setUp();

        // Create basic organizational structure
        $this->createOrganizationalStructure();
        $this->createShift();
    }

    protected function createOrganizationalStructure()
    {
        // Create entitas
        $this->entitas = Entitas::create([
            'nama' => 'PT Viera Anugrah Pertama',
            'alamat' => 'Jakarta',
            'keterangan' => 'Test entity for testing',
        ]);

        // Create departemen
        $this->departemen = Departemen::create([
            'nama_departemen' => 'IT Department',
            'created_by' => 1,
        ]);

        // Create divisi
        $this->divisi = Divisi::create([
            'nama_divisi' => 'Software Development',
            'id_departemen' => $this->departemen->id,
            'created_by' => 1,
        ]);

        // Create jabatan
        $this->jabatan = Jabatan::create([
            'nama_jabatan' => 'Software Developer',
            'created_by' => 1,
        ]);
    }

    protected function createShift()
    {
        $this->shift = Shift::create([
            'nama_shift' => 'Shift Pagi',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'toleransi_keterlambatan' => 15,
            'is_active' => true,
            'created_by' => 1,
        ]);
    }

    protected function createKaryawan($user = null, $attributes = [])
    {
        if (!$user) {
            $user = User::factory()->create(['role' => 'karyawan']);
        }

        $defaultAttributes = [
            'id_user' => $user->id,
            'nip' => 'K' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT),
            'nama_lengkap' => 'Karyawan Test',
            'jenis_kelamin' => 'Laki-laki',
            'kota_lahir' => 'Jakarta',
            'tanggal_lahir' => '1990-01-01',
            'alamat' => 'Jl. Test No. 123',
            'nomor_telepon' => '08123456789',
            'email' => $user->email,
            'status_aktif' => 1,
            'id_entitas' => $this->entitas->id,
            'id_departemen' => $this->departemen->id,
            'id_divisi' => $this->divisi->id,
            'id_jabatan' => $this->jabatan->id,
            'created_by' => 1,
        ];

        return Karyawan::create(array_merge($defaultAttributes, $attributes));
    }

    protected function createSchedule($karyawan, $date = null, $shift = null)
    {
        return Schedule::create([
            'karyawan_id' => $karyawan->id,
            'shift_id' => $shift ? $shift->id : $this->shift->id,
            'tanggal_jadwal' => $date ?: Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => $this->shift->waktu_mulai,
            'waktu_keluar' => $this->shift->waktu_selesai,
            'status' => 'aktif',
        ]);
    }

    /** @test */
    public function karyawan_can_access_attendance_create_page()
    {
        // Create user and employee
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);

        // Login as karyawan
        $this->actingAs($user);

        // Access attendance create page
        $response = $this->get('/karyawan/absensis/create');

        // Verify response
        $response->assertStatus(200);
        $response->assertSee('Absensi');
    }

    /** @test */
    public function attendance_form_contains_required_fields()
    {
        // Create user and employee
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);

        // Login as karyawan
        $this->actingAs($user);

        // Access attendance create page
        $response = $this->get('/karyawan/absensis/create');

        // Verify form contains required fields
        $response->assertSee('name="latitude"', false);
        $response->assertSee('name="longitude"', false);
        $response->assertSee('name="foto"', false);
        $response->assertSee('name="keterangan"', false);
    }

    /** @test */
    public function karyawan_can_view_attendance_history()
    {
        // Create user and employee
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);

        // Create schedule and attendance
        $schedule = $this->createSchedule($karyawan);
        $attendance = Absensi::create([
            'karyawan_id' => $karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => '08:00:00',
            'waktu_keluar' => '17:00:00',
            'status' => 'hadir',
            'lokasi_masuk' => '-6.123456,106.789012',
            'lokasi_keluar' => '-6.123456,106.789012',
            'foto_masuk' => 'test-masuk.jpg',
            'foto_keluar' => 'test-keluar.jpg',
        ]);

        // Login as karyawan
        $this->actingAs($user);

        // Access attendance list page
        $response = $this->get('/karyawan/absensis');

        // Verify response contains attendance data
        $response->assertStatus(200);
        $response->assertSee('08:00');
        $response->assertSee('17:00');
        $response->assertSee('hadir');
    }

    /** @test */
    public function attendance_is_filtered_by_current_employee()
    {
        // Create two employees
        $user1 = User::factory()->create(['role' => 'karyawan']);
        $karyawan1 = $this->createKaryawan($user1);

        $user2 = User::factory()->create(['role' => 'karyawan']);
        $karyawan2 = $this->createKaryawan($user2);

        // Create schedules for both
        $schedule1 = $this->createSchedule($karyawan1);
        $schedule2 = $this->createSchedule($karyawan2);

        // Create attendance for both employees
        $attendance1 = Absensi::create([
            'karyawan_id' => $karyawan1->id,
            'jadwal_id' => $schedule1->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => '08:00:00',
            'status' => 'hadir',
            'lokasi_masuk' => '-6.123456,106.789012',
            'foto_masuk' => 'test1-masuk.jpg',
        ]);

        $attendance2 = Absensi::create([
            'karyawan_id' => $karyawan2->id,
            'jadwal_id' => $schedule2->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => '09:00:00',
            'status' => 'terlambat',
            'lokasi_masuk' => '-6.123456,106.789012',
            'foto_masuk' => 'test2-masuk.jpg',
        ]);

        // Login as first employee
        $this->actingAs($user1);

        // Access attendance list page
        $response = $this->get('/karyawan/absensis');

        // Should see only first employee's attendance
        $response->assertStatus(200);
        $response->assertSee('08:00');
        $response->assertDontSee('09:00');
    }

    /** @test */
    public function non_karyawan_cannot_access_attendance_pages()
    {
        // Create admin user
        $user = User::factory()->create(['role' => 'admin']);

        // Login as admin
        $this->actingAs($user);

        // Try to access attendance pages
        $listResponse = $this->get('/karyawan/absensis');
        $createResponse = $this->get('/karyawan/absensis/create');

        // Should be redirected to login
        $listResponse->assertRedirect('/karyawan/login');
        $createResponse->assertRedirect('/karyawan/login');
    }
}
