<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Karyawan;
use App\Models\CutiIzin;
use App\Models\Absensi;
use App\Models\Schedule;
use App\Models\Shift;
use App\Models\AturanKeterlambatan;
use App\Services\AttendanceService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AutomaticAttendanceTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Karyawan $karyawan;
    private Shift $shift;
    private Schedule $schedule;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user and employee
        $this->user = User::factory()->create([
            'role' => 'karyawan'
        ]);

        $this->karyawan = Karyawan::factory()->create([
            'id_user' => $this->user->id,
            'nama_lengkap' => 'Test Employee'
        ]);

        // Create test shift
        $this->shift = Shift::factory()->create([
            'nama_shift' => 'Regular Shift',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'toleransi_keterlambatan' => 15, // 15 minutes tolerance
            'is_split_shift' => false
        ]);

        // Create test schedule for today
        $this->schedule = Schedule::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today()
        ]);
    }

    /** @test */
    public function it_automatically_creates_attendance_records_when_leave_is_approved()
    {
        // Create a leave request
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today()->addDays(2),
            'jumlah_hari' => 3,
            'alasan' => 'Personal leave',
            'status' => 'pending'
        ]);

        // Approve the leave request
        $leave->update([
            'status' => 'approved',
            'approved_by' => $this->user->id,
            'approved_at' => now()
        ]);

        // Check that attendance records were created
        $attendanceRecords = Absensi::where('karyawan_id', $this->karyawan->id)
            ->whereBetween('tanggal_absensi', [
                Carbon::today()->format('Y-m-d'),
                Carbon::today()->addDays(2)->format('Y-m-d')
            ])
            ->get();

        $this->assertCount(3, $attendanceRecords);
        
        foreach ($attendanceRecords as $attendance) {
            $this->assertEquals('cuti', $attendance->status);
            $this->assertStringContains('Auto-generated from approved cuti', $attendance->keterangan);
            $this->assertEquals($this->user->id, $attendance->approved_by);
        }
    }

    /** @test */
    public function it_determines_attendance_status_correctly_based_on_timing()
    {
        // Test on-time attendance
        $onTimeStatus = AttendanceService::determineAttendanceStatus(
            $this->karyawan->id,
            Carbon::today()->setTime(8, 0) // Exactly on time
        );
        $this->assertEquals('hadir', $onTimeStatus);

        // Test within tolerance
        $toleranceStatus = AttendanceService::determineAttendanceStatus(
            $this->karyawan->id,
            Carbon::today()->setTime(8, 10) // 10 minutes late, within tolerance
        );
        $this->assertEquals('hadir', $toleranceStatus);

        // Test late attendance
        $lateStatus = AttendanceService::determineAttendanceStatus(
            $this->karyawan->id,
            Carbon::today()->setTime(8, 20) // 20 minutes late, beyond tolerance
        );
        $this->assertEquals('terlambat', $lateStatus);
    }

    /** @test */
    public function it_calculates_lateness_minutes_correctly()
    {
        // Create attendance record with late entry
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $this->schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 25), // 25 minutes late
            'status' => 'terlambat',
            'periode' => 1
        ]);

        $latenessMinutes = AttendanceService::calculateLatenessMinutes($attendance);
        
        // Should be 10 minutes late (25 minutes - 15 minutes tolerance)
        $this->assertEquals(10, $latenessMinutes);
    }

    /** @test */
    public function it_automatically_updates_attendance_status_when_created()
    {
        // Create attendance record with late entry time
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $this->schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 30), // 30 minutes late
            'status' => 'hadir', // Initially set as present
            'periode' => 1
        ]);

        // Refresh to get updated status
        $attendance->refresh();

        // Status should be automatically updated to 'terlambat'
        $this->assertEquals('terlambat', $attendance->status);
    }

    /** @test */
    public function it_calculates_lateness_deduction_with_rules()
    {
        // Create lateness rule
        $rule = AturanKeterlambatan::create([
            'nama_aturan' => 'Test Rule',
            'menit_dari' => 1,
            'menit_sampai' => 30,
            'jenis_denda' => 'per_menit',
            'denda_per_menit' => 1000, // 1000 per minute
            'is_active' => true
        ]);

        // Create late attendance
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $this->schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 25), // 25 minutes late
            'status' => 'terlambat',
            'periode' => 1
        ]);

        $deductionData = AttendanceService::calculateLatenessDeduction($attendance);

        $this->assertEquals(10, $deductionData['minutes_late']); // 10 minutes after tolerance
        $this->assertEquals(10000, $deductionData['deduction_amount']); // 10 * 1000
        $this->assertNotNull($deductionData['applicable_rule']);
        $this->assertEquals($rule->id, $deductionData['applicable_rule']->id);
    }

    /** @test */
    public function it_does_not_override_special_status_types()
    {
        // Create attendance with special status
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $this->schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 30), // Late time
            'status' => 'cuti', // Special status
            'periode' => 1
        ]);

        // Status should remain as 'cuti' and not be changed to 'terlambat'
        $this->assertEquals('cuti', $attendance->status);
    }
}
