<?php

namespace Tests\Feature;

use App\Models\CutiIzin;
use App\Models\Karyawan;
use App\Models\RiwayatKontrak;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CutiQuotaValidationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $karyawan;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user and karyawan
        $this->user = User::create([
            'name' => 'Test Employee',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'karyawan'
        ]);

        $this->karyawan = Karyawan::create([
            'nama_lengkap' => 'Test Employee',
            'nip' => 'TEST001',
            'nik' => '1234567890123456',
            'email' => '<EMAIL>',
            'nomor_telepon' => '081234567890',
            'alamat' => 'Test Address',
            'kota_lahir' => 'Test City',
            'tanggal_lahir' => '1990-01-01',
            'jenis_kelamin' => 'Laki-laki',
            'id_user' => $this->user->id,
            'status_aktif' => 1
        ]);
    }

    /** @test */
    public function pkwt_employee_can_take_leave_within_yearly_quota()
    {
        // Create PKWT contract
        RiwayatKontrak::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_kontrak' => 'PKWT',
            'no_kontrak' => 'PKWT001',
            'tgl_mulai' => Carbon::now()->subMonths(6),
            'tgl_selesai' => Carbon::now()->addMonths(6),
            'is_active' => 1
        ]);

        $cutiIzin = new CutiIzin([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::tomorrow(),
            'tanggal_selesai' => Carbon::tomorrow()->addDays(4), // 5 working days
            'jumlah_hari' => 5
        ]);

        $errors = $cutiIzin->validateDates();

        // Should not have blocking errors (only monthly warning is allowed)
        $blockingErrors = array_filter($errors, function ($error) {
            return !str_contains($error, '⚠️ Peringatan:');
        });

        $this->assertEmpty($blockingErrors);
    }

    /** @test */
    public function pkwt_employee_cannot_exceed_yearly_quota()
    {
        // Create PKWT contract
        RiwayatKontrak::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_kontrak' => 'PKWT',
            'no_kontrak' => 'PKWT001',
            'tgl_mulai' => Carbon::now()->subMonths(6),
            'tgl_selesai' => Carbon::now()->addMonths(6),
            'is_active' => 1
        ]);

        // Use up 10 days of leave
        CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::now()->subDays(20),
            'tanggal_selesai' => Carbon::now()->subDays(11),
            'jumlah_hari' => 10,
            'status' => 'approved'
        ]);

        // Try to take 5 more days (would exceed 12-day quota)
        $cutiIzin = new CutiIzin([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::tomorrow(),
            'tanggal_selesai' => Carbon::tomorrow()->addDays(4),
            'jumlah_hari' => 5
        ]);

        $errors = $cutiIzin->validateDates();

        $this->assertNotEmpty($errors);
        $this->assertTrue(
            collect($errors)->contains(function ($error) {
                return str_contains($error, 'Kuota cuti tahunan tidak mencukupi');
            })
        );
    }

    /** @test */
    public function freelance_employee_cannot_take_leave()
    {
        // Create Freelance contract
        RiwayatKontrak::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_kontrak' => 'Freelance',
            'no_kontrak' => 'FL001',
            'tgl_mulai' => Carbon::now()->subMonths(3),
            'tgl_selesai' => Carbon::now()->addMonths(3),
            'is_active' => 1
        ]);

        $cutiIzin = new CutiIzin([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::tomorrow(),
            'tanggal_selesai' => Carbon::tomorrow()->addDays(2),
            'jumlah_hari' => 3
        ]);

        $errors = $cutiIzin->validateDates();

        $this->assertNotEmpty($errors);
        $this->assertTrue(
            collect($errors)->contains(function ($error) {
                return str_contains($error, 'tidak memiliki hak cuti');
            })
        );
    }

    /** @test */
    public function probation_employee_cannot_take_leave()
    {
        // Create Probation contract
        RiwayatKontrak::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_kontrak' => 'Probation',
            'no_kontrak' => 'PROB001',
            'tgl_mulai' => Carbon::now()->subMonths(1),
            'tgl_selesai' => Carbon::now()->addMonths(2),
            'is_active' => 1
        ]);

        $cutiIzin = new CutiIzin([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::tomorrow(),
            'tanggal_selesai' => Carbon::tomorrow()->addDays(2),
            'jumlah_hari' => 3
        ]);

        $errors = $cutiIzin->validateDates();

        $this->assertNotEmpty($errors);
        $this->assertTrue(
            collect($errors)->contains(function ($error) {
                return str_contains($error, 'tidak memiliki hak cuti');
            })
        );
    }

    /** @test */
    public function pkwt_employee_gets_monthly_warning_when_exceeding_3_days_per_month()
    {
        // Create PKWT contract
        RiwayatKontrak::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_kontrak' => 'PKWT',
            'no_kontrak' => 'PKWT001',
            'tgl_mulai' => Carbon::now()->subMonths(6),
            'tgl_selesai' => Carbon::now()->addMonths(6),
            'is_active' => 1
        ]);

        // Use 2 days this month
        CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::now()->startOfMonth()->addDays(5),
            'tanggal_selesai' => Carbon::now()->startOfMonth()->addDays(6),
            'jumlah_hari' => 2,
            'status' => 'approved'
        ]);

        // Try to take 3 more days (would exceed 3-day monthly limit)
        $cutiIzin = new CutiIzin([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::tomorrow(),
            'tanggal_selesai' => Carbon::tomorrow()->addDays(2),
            'jumlah_hari' => 3
        ]);

        $errors = $cutiIzin->validateDates();

        // Should have monthly warning
        $this->assertTrue(
            collect($errors)->contains(function ($error) {
                return str_contains($error, '⚠️ Peringatan: Batas cuti bulanan');
            })
        );
    }

    /** @test */
    public function remaining_quota_calculation_is_correct()
    {
        // Create PKWTT contract
        RiwayatKontrak::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_kontrak' => 'PKWTT',
            'no_kontrak' => 'PKWTT001',
            'tgl_mulai' => Carbon::now()->subYear(),
            'is_active' => 1
        ]);

        // Use 7 days of leave this year
        CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::now()->subDays(20),
            'tanggal_selesai' => Carbon::now()->subDays(14),
            'jumlah_hari' => 7,
            'status' => 'approved'
        ]);

        $cutiModel = new CutiIzin();
        $remainingQuota = $cutiModel->getRemainingLeaveQuota($this->karyawan->id);

        $this->assertEquals(5, $remainingQuota); // 12 - 7 = 5
    }
}
