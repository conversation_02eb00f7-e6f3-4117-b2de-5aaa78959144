<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Karyawan;
use App\Models\Entitas;
use App\Models\Absensi;
use App\Services\GeofencingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GeofencingValidationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $karyawan;
    protected $entitas;

    protected function setUp(): void
    {
        parent::setUp();

        // Create entitas with geofencing enabled
        $this->entitas = Entitas::create([
            'nama' => 'Kantor Pusat',
            'alamat' => 'Jakarta Pusat',
            'latitude' => -6.200000,
            'longitude' => 106.816666,
            'radius' => 100, // 100 meters
            'enable_geofencing' => true,
        ]);

        // Create user and karyawan
        $this->user = User::create([
            'name' => 'Test Employee',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'karyawan',
        ]);

        $this->karyawan = Karyawan::create([
            'nama_lengkap' => 'Test Employee',
            'email' => '<EMAIL>',
            'id_user' => $this->user->id,
            'id_entitas' => $this->entitas->id,
            'status_aktif' => 1, // 1 = aktif
            'nip' => '12345',
            'nik' => '1234567890123456',
        ]);
    }

    /** @test */
    public function it_allows_attendance_within_radius()
    {
        // Coordinates within 100m radius of office
        $userLat = -6.200050; // ~5.5m from office
        $userLng = 106.816666;

        $validation = GeofencingService::validateAttendanceLocation(
            $this->karyawan,
            $userLat,
            $userLng
        );

        $this->assertTrue($validation['allowed']);
        $this->assertLessThan(100, $validation['distance']);
        $this->assertEquals(100, $validation['radius']);
        $this->assertEquals('Kantor Pusat', $validation['entitas_name']);
    }

    /** @test */
    public function it_rejects_attendance_outside_radius()
    {
        // Coordinates outside 100m radius of office
        $userLat = -6.202000; // ~222m from office
        $userLng = 106.816666;

        $validation = GeofencingService::validateAttendanceLocation(
            $this->karyawan,
            $userLat,
            $userLng
        );

        $this->assertFalse($validation['allowed']);
        $this->assertGreaterThan(100, $validation['distance']);
        $this->assertEquals(100, $validation['radius']);
        $this->assertStringContainsString('Anda berada', $validation['message']);
    }

    /** @test */
    public function it_allows_attendance_when_geofencing_disabled()
    {
        // Disable geofencing
        $this->entitas->update(['enable_geofencing' => false]);

        // Use coordinates far from office
        $userLat = -6.300000; // ~11km from office
        $userLng = 106.900000;

        $validation = GeofencingService::validateAttendanceLocation(
            $this->karyawan,
            $userLat,
            $userLng
        );

        $this->assertTrue($validation['allowed']);
        $this->assertStringContainsString('Geofencing disabled', $validation['message']);
    }

    /** @test */
    public function it_handles_missing_entitas_coordinates()
    {
        // Remove coordinates from entitas
        $this->entitas->update([
            'latitude' => null,
            'longitude' => null,
        ]);

        $validation = GeofencingService::validateAttendanceLocation(
            $this->karyawan,
            -6.200000,
            106.816666
        );

        $this->assertTrue($validation['allowed']);
        $this->assertStringContainsString('coordinates not set', $validation['message']);
    }

    /** @test */
    public function it_validates_attendance_location_via_api()
    {
        $this->actingAs($this->user);

        // Test valid location
        $response = $this->postJson('/karyawan/validate-geofencing', [
            'latitude' => -6.200050,
            'longitude' => 106.816666,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'allowed' => true,
                'entitas_name' => 'Kantor Pusat',
            ]);

        // Test invalid location
        $response = $this->postJson('/karyawan/validate-geofencing', [
            'latitude' => -6.202000,
            'longitude' => 106.816666,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'allowed' => false,
            ]);
    }

    /** @test */
    public function it_validates_coordinates_format()
    {
        $this->actingAs($this->user);

        // Test invalid latitude
        $response = $this->postJson('/karyawan/validate-geofencing', [
            'latitude' => 91, // Invalid latitude
            'longitude' => 106.816666,
        ]);

        $response->assertStatus(422);

        // Test invalid longitude
        $response = $this->postJson('/karyawan/validate-geofencing', [
            'latitude' => -6.200000,
            'longitude' => 181, // Invalid longitude
        ]);

        $response->assertStatus(422);
    }

    /** @test */
    public function it_prevents_attendance_creation_with_invalid_location()
    {
        $this->actingAs($this->user);

        // Try to create attendance with invalid location
        $attendanceData = [
            'latitude' => -6.202000, // Outside radius
            'longitude' => 106.816666,
            'foto_absensi' => 'test.jpg',
        ];

        $validation = Absensi::validateGeofencing(
            $this->karyawan->id,
            $attendanceData['latitude'],
            $attendanceData['longitude']
        );

        $this->assertFalse($validation['allowed']);
        $this->assertStringContainsString('Anda berada', $validation['message']);
    }

    /** @test */
    public function it_calculates_distance_correctly()
    {
        // Test distance calculation
        $distance = GeofencingService::calculateDistance(
            -6.200000,
            106.816666, // Office location
            -6.201000,
            106.816666  // 1km south
        );

        // Should be approximately 111 meters (1 degree latitude ≈ 111km)
        $this->assertGreaterThan(100, $distance);
        $this->assertLessThan(120, $distance);
    }

    /** @test */
    public function it_gets_workplace_location_info()
    {
        $locationInfo = GeofencingService::getAttendanceLocationInfo($this->karyawan);

        $this->assertTrue($locationInfo['has_location']);
        $this->assertEquals('Kantor Pusat', $locationInfo['entitas_name']);
        $this->assertEquals('-6.20000000, 106.81666600', $locationInfo['coordinates']);
        $this->assertEquals(100, $locationInfo['radius']);
        $this->assertStringContainsString('100m', $locationInfo['message']);
    }
}
