<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Karyawan;
use App\Models\Entitas;
use App\Models\Departemen;
use App\Models\Divisi;
use App\Models\Jabatan;
use App\Models\Shift;
use App\Models\Schedule;
use App\Models\Absensi;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class GeolocationTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $karyawan;
    protected $shift;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setupTestData();
    }

    protected function setupTestData()
    {
        // Create admin user
        $this->admin = User::create([
            'name' => 'Admin Test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin'
        ]);

        // Create organizational structure
        $entitas = Entitas::create([
            'nama' => 'PT. Test Company',
            'alamat' => 'Jakarta',
            'keterangan' => 'Test company'
        ]);

        $departemen = Departemen::create([
            'nama_departemen' => 'IT Department',
            'keterangan' => 'Information Technology'
        ]);

        $divisi = Divisi::create([
            'nama_divisi' => 'Software Development',
            'id_departemen' => $departemen->id,
            'keterangan' => 'Software Development Division'
        ]);

        $jabatan = Jabatan::create([
            'nama_jabatan' => 'Software Engineer',
            'keterangan' => 'Software Engineer Position'
        ]);

        // Create shift
        $this->shift = Shift::create([
            'nama_shift' => 'Regular Shift',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'toleransi_keterlambatan' => 15,
            'is_active' => true
        ]);

        // Create karyawan
        $this->karyawan = Karyawan::create([
            'nama_lengkap' => 'John Doe',
            'nip' => 'EMP001',
            'nik' => '1234567890123456',
            'email' => '<EMAIL>',
            'tanggal_lahir' => '1990-01-01',
            'jenis_kelamin' => 'Laki-laki',
            'id_entitas' => $entitas->id,
            'id_departemen' => $departemen->id,
            'id_divisi' => $divisi->id,
            'id_jabatan' => $jabatan->id,
            'status_aktif' => 1
        ]);
    }

    /** @test */
    public function test_absensi_stores_location_data_correctly()
    {
        $this->actingAs($this->admin);

        // Create schedule
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'is_active' => true
        ]);

        // Test Jakarta coordinates
        $jakartaLat = -6.200000;
        $jakartaLng = 106.816666;

        $absensi = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => '08:00:00',
            'status' => 'hadir',
            'lokasi_masuk' => "{$jakartaLat},{$jakartaLng}",
            'latitude_masuk' => $jakartaLat,
            'longitude_masuk' => $jakartaLng,
        ]);

        $this->assertDatabaseHas('absensi', [
            'karyawan_id' => $this->karyawan->id,
            'latitude_masuk' => $jakartaLat,
            'longitude_masuk' => $jakartaLng,
            'lokasi_masuk' => "{$jakartaLat},{$jakartaLng}"
        ]);

        // Test precision
        $this->assertEquals($jakartaLat, $absensi->latitude_masuk);
        $this->assertEquals($jakartaLng, $absensi->longitude_masuk);
    }

    /** @test */
    public function test_absensi_handles_check_in_and_check_out_locations()
    {
        $this->actingAs($this->admin);

        // Create schedule
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'is_active' => true
        ]);

        // Different locations for check-in and check-out
        $checkinLat = -6.200000;
        $checkinLng = 106.816666;
        $checkoutLat = -6.200100;
        $checkoutLng = 106.816766;

        $absensi = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => '08:00:00',
            'waktu_keluar' => '17:00:00',
            'status' => 'hadir',
            'lokasi_masuk' => "{$checkinLat},{$checkinLng}",
            'latitude_masuk' => $checkinLat,
            'longitude_masuk' => $checkinLng,
            'lokasi_keluar' => "{$checkoutLat},{$checkoutLng}",
            'latitude_keluar' => $checkoutLat,
            'longitude_keluar' => $checkoutLng,
        ]);

        // Test both locations are stored
        $this->assertEquals($checkinLat, $absensi->latitude_masuk);
        $this->assertEquals($checkinLng, $absensi->longitude_masuk);
        $this->assertEquals($checkoutLat, $absensi->latitude_keluar);
        $this->assertEquals($checkoutLng, $absensi->longitude_keluar);
    }

    /** @test */
    public function test_location_data_validation()
    {
        $this->actingAs($this->admin);

        // Create schedule
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'is_active' => true
        ]);

        // Test valid coordinates
        $validLat = -6.200000;
        $validLng = 106.816666;

        $absensi = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => '08:00:00',
            'status' => 'hadir',
            'latitude_masuk' => $validLat,
            'longitude_masuk' => $validLng,
        ]);

        $this->assertNotNull($absensi->latitude_masuk);
        $this->assertNotNull($absensi->longitude_masuk);
        $this->assertTrue(is_numeric($absensi->latitude_masuk));
        $this->assertTrue(is_numeric($absensi->longitude_masuk));

        // Test latitude range (-90 to 90)
        $this->assertGreaterThanOrEqual(-90, $absensi->latitude_masuk);
        $this->assertLessThanOrEqual(90, $absensi->latitude_masuk);

        // Test longitude range (-180 to 180)
        $this->assertGreaterThanOrEqual(-180, $absensi->longitude_masuk);
        $this->assertLessThanOrEqual(180, $absensi->longitude_masuk);
    }

    /** @test */
    public function test_location_data_can_be_null()
    {
        $this->actingAs($this->admin);

        // Create schedule
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'is_active' => true
        ]);

        // Create absensi without location data
        $absensi = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => '08:00:00',
            'status' => 'hadir',
        ]);

        $this->assertNull($absensi->latitude_masuk);
        $this->assertNull($absensi->longitude_masuk);
        $this->assertNull($absensi->latitude_keluar);
        $this->assertNull($absensi->longitude_keluar);
    }

    /** @test */
    public function test_multiple_location_formats()
    {
        $this->actingAs($this->admin);

        // Create schedule
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'is_active' => true
        ]);

        // Test different coordinate formats
        $testCases = [
            // Jakarta coordinates
            ['lat' => -6.200000, 'lng' => 106.816666, 'location' => 'Jakarta Pusat'],
            // Bandung coordinates
            ['lat' => -6.917464, 'lng' => 107.619123, 'location' => 'Bandung'],
            // Surabaya coordinates
            ['lat' => -7.257472, 'lng' => 112.752090, 'location' => 'Surabaya'],
        ];

        foreach ($testCases as $index => $testCase) {
            $absensi = Absensi::create([
                'karyawan_id' => $this->karyawan->id,
                'jadwal_id' => $schedule->id,
                'tanggal_absensi' => Carbon::today()->addDays($index),
                'waktu_masuk' => '08:00:00',
                'status' => 'hadir',
                'latitude_masuk' => $testCase['lat'],
                'longitude_masuk' => $testCase['lng'],
                'lokasi_masuk' => $testCase['location'],
            ]);

            $this->assertEquals($testCase['lat'], $absensi->latitude_masuk);
            $this->assertEquals($testCase['lng'], $absensi->longitude_masuk);
            $this->assertEquals($testCase['location'], $absensi->lokasi_masuk);
        }
    }

    /** @test */
    public function test_location_precision()
    {
        $this->actingAs($this->admin);

        // Create schedule
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'is_active' => true
        ]);

        // Test high precision coordinates (8 decimal places)
        $highPrecisionLat = -6.20000123;
        $highPrecisionLng = 106.81666789;

        $absensi = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => '08:00:00',
            'status' => 'hadir',
            'latitude_masuk' => $highPrecisionLat,
            'longitude_masuk' => $highPrecisionLng,
        ]);

        // Test that precision is maintained
        $this->assertEquals($highPrecisionLat, $absensi->latitude_masuk);
        $this->assertEquals($highPrecisionLng, $absensi->longitude_masuk);
    }

    /** @test */
    public function test_location_data_for_different_attendance_statuses()
    {
        $this->actingAs($this->admin);

        // Create schedule
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'is_active' => true
        ]);

        $statuses = ['hadir', 'terlambat', 'izin', 'sakit'];
        $lat = -6.200000;
        $lng = 106.816666;

        foreach ($statuses as $index => $status) {
            $absensi = Absensi::create([
                'karyawan_id' => $this->karyawan->id,
                'jadwal_id' => $schedule->id,
                'tanggal_absensi' => Carbon::today()->addDays($index),
                'waktu_masuk' => '08:00:00',
                'status' => $status,
                'latitude_masuk' => $lat,
                'longitude_masuk' => $lng,
            ]);

            $this->assertEquals($status, $absensi->status);
            $this->assertEquals($lat, $absensi->latitude_masuk);
            $this->assertEquals($lng, $absensi->longitude_masuk);
        }
    }
}
