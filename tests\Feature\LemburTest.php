<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Lembur;
use App\Models\Karyawan;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Carbon\Carbon;

class LemburTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $karyawan;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create([
            'role' => 'admin'
        ]);
        
        // Create test karyawan
        $this->karyawan = Karyawan::factory()->create();
    }

    /** @test */
    public function it_can_create_lembur_record()
    {
        $lemburData = [
            'karyawan_id' => $this->karyawan->id,
            'tanggal' => now()->format('Y-m-d'),
            'jumlah_jam' => 2.5,
            'deskripsi' => 'Test lembur description',
            'created_by' => $this->user->id,
        ];

        $lembur = Lembur::create($lemburData);

        $this->assertDatabaseHas('lembur', [
            'karyawan_id' => $this->karyawan->id,
            'jumlah_jam' => 2.5,
            'deskripsi' => 'Test lembur description',
        ]);

        $this->assertEquals($this->karyawan->id, $lembur->karyawan_id);
        $this->assertEquals(2.5, $lembur->jumlah_jam);
    }

    /** @test */
    public function it_has_relationship_with_karyawan()
    {
        $lembur = Lembur::factory()->create([
            'karyawan_id' => $this->karyawan->id,
        ]);

        $this->assertInstanceOf(Karyawan::class, $lembur->karyawan);
        $this->assertEquals($this->karyawan->id, $lembur->karyawan->id);
    }

    /** @test */
    public function it_has_relationship_with_creator()
    {
        $lembur = Lembur::factory()->create([
            'created_by' => $this->user->id,
        ]);

        $this->assertInstanceOf(User::class, $lembur->creator);
        $this->assertEquals($this->user->id, $lembur->creator->id);
    }

    /** @test */
    public function it_can_filter_by_month()
    {
        // Create lembur for current month
        $currentMonthLembur = Lembur::factory()->create([
            'tanggal' => now(),
            'karyawan_id' => $this->karyawan->id,
        ]);

        // Create lembur for last month
        $lastMonthLembur = Lembur::factory()->create([
            'tanggal' => now()->subMonth(),
            'karyawan_id' => $this->karyawan->id,
        ]);

        $currentMonthResults = Lembur::filterByMonth(now()->month, now()->year)->get();
        $lastMonthResults = Lembur::filterByMonth(now()->subMonth()->month, now()->subMonth()->year)->get();

        $this->assertTrue($currentMonthResults->contains($currentMonthLembur));
        $this->assertFalse($currentMonthResults->contains($lastMonthLembur));
        
        $this->assertTrue($lastMonthResults->contains($lastMonthLembur));
        $this->assertFalse($lastMonthResults->contains($currentMonthLembur));
    }

    /** @test */
    public function it_can_filter_by_karyawan()
    {
        $anotherKaryawan = Karyawan::factory()->create();

        $lembur1 = Lembur::factory()->create(['karyawan_id' => $this->karyawan->id]);
        $lembur2 = Lembur::factory()->create(['karyawan_id' => $anotherKaryawan->id]);

        $results = Lembur::filterByKaryawan($this->karyawan->id)->get();

        $this->assertTrue($results->contains($lembur1));
        $this->assertFalse($results->contains($lembur2));
    }

    /** @test */
    public function it_uses_soft_deletes()
    {
        $lembur = Lembur::factory()->create();
        
        $lembur->delete();
        
        $this->assertSoftDeleted('lembur', ['id' => $lembur->id]);
        $this->assertNotNull($lembur->fresh()->deleted_at);
    }

    /** @test */
    public function karyawan_has_lembur_relationship()
    {
        $lembur = Lembur::factory()->create([
            'karyawan_id' => $this->karyawan->id,
        ]);

        $this->assertTrue($this->karyawan->lembur->contains($lembur));
    }

    /** @test */
    public function it_can_calculate_monthly_total_hours()
    {
        // Create multiple lembur records for current month
        Lembur::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'tanggal' => now(),
            'jumlah_jam' => 2.5,
        ]);

        Lembur::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'tanggal' => now()->addDay(),
            'jumlah_jam' => 3.0,
        ]);

        // Create lembur for different month (should not be included)
        Lembur::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'tanggal' => now()->subMonth(),
            'jumlah_jam' => 1.5,
        ]);

        $totalHours = Lembur::where('karyawan_id', $this->karyawan->id)
            ->whereMonth('tanggal', now()->month)
            ->whereYear('tanggal', now()->year)
            ->sum('jumlah_jam');

        $this->assertEquals(5.5, $totalHours);
    }

    /** @test */
    public function it_validates_maximum_hours_per_month()
    {
        // This test would be implemented when validation is added to the model
        // For now, we just test that we can create records up to reasonable limits
        
        $totalHours = 0;
        $records = [];
        
        // Create records totaling 39 hours (under limit)
        for ($i = 0; $i < 13; $i++) {
            $hours = 3.0;
            $records[] = Lembur::factory()->create([
                'karyawan_id' => $this->karyawan->id,
                'tanggal' => now()->addDays($i),
                'jumlah_jam' => $hours,
            ]);
            $totalHours += $hours;
        }

        $this->assertEquals(39.0, $totalHours);
        $this->assertCount(13, $records);
    }
}
