<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Karyawan;
use App\Models\Departemen;
use App\Models\Divisi;
use App\Models\Entitas;
use App\Models\Jabatan;
use App\Models\MutasiPromosiDemosi;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;

class MutasiPromosiDemosiDependentDropdownTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $karyawan;
    protected $departemen1;
    protected $departemen2;
    protected $divisi1;
    protected $divisi2;
    protected $entitas;
    protected $jabatan;

    protected function setUp(): void
    {
        parent::setUp();

        // Disable model events to avoid observer issues in tests
        Event::fake();

        // Create admin user
        $this->admin = User::create([
            'name' => 'Admin Test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin'
        ]);

        // Create test data
        $this->entitas = Entitas::create([
            'nama' => 'Test Entitas',
            'alamat' => 'Test Address',
            'latitude' => -6.200000,
            'longitude' => 106.816666,
            'radius' => 100,
            'enable_geofencing' => true,
        ]);

        $this->jabatan = Jabatan::create([
            'nama_jabatan' => 'Test Jabatan',
            'deskripsi' => 'Test position',
            'created_by' => 1,
        ]);

        $this->departemen1 = Departemen::create(['nama_departemen' => 'IT Department']);
        $this->departemen2 = Departemen::create(['nama_departemen' => 'HR Department']);

        $this->divisi1 = Divisi::create([
            'nama_divisi' => 'Software Development',
            'departemen_id' => $this->departemen1->id
        ]);

        $this->divisi2 = Divisi::create([
            'nama_divisi' => 'Human Resources',
            'departemen_id' => $this->departemen2->id
        ]);

        $this->karyawan = Karyawan::create([
            'nama_lengkap' => 'Test Employee',
            'nip' => 'K001',
            'nik' => '1234567890123456',
            'email' => '<EMAIL>',
            'nomor_telepon' => '08123456789',
            'alamat' => 'Test Address',
            'kota_lahir' => 'Jakarta',
            'tanggal_lahir' => '1990-01-01',
            'jenis_kelamin' => 'Laki-laki',
            'status_pernikahan' => 'Belum Menikah',
            'status_aktif' => 1,
            'id_entitas' => $this->entitas->id,
            'id_departemen' => $this->departemen1->id,
            'id_divisi' => $this->divisi1->id,
            'id_jabatan' => $this->jabatan->id,
            'created_by' => 1,
        ]);
    }

    /** @test */
    public function it_can_create_mutasi_with_correct_departemen_divisi_relationship()
    {
        // Test that we can create a mutasi with correct departemen-divisi relationship
        $mutasi = MutasiPromosiDemosi::create([
            'karyawan_id' => $this->karyawan->id,
            'tipe' => 'promosi',
            'entitas_id' => $this->entitas->id,
            'departemen_id' => $this->departemen2->id,
            'divisi_id' => $this->divisi2->id, // divisi2 belongs to departemen2
            'jabatan_id' => $this->jabatan->id,
            'tanggal_efektif' => now(),
            'alasan' => 'Test promosi dengan dependent dropdown',
            'is_active' => true,
        ]);

        $this->assertDatabaseHas('mutasi_promosi_demosi', [
            'karyawan_id' => $this->karyawan->id,
            'tipe' => 'promosi',
            'departemen_id' => $this->departemen2->id,
            'divisi_id' => $this->divisi2->id,
            'alasan' => 'Test promosi dengan dependent dropdown',
        ]);

        // Verify the relationships are correct
        $this->assertEquals($this->departemen2->id, $mutasi->divisi->departemen_id);
    }

    /** @test */
    public function it_filters_divisi_by_departemen_correctly()
    {
        // Create additional divisi for testing
        $divisiIT1 = Divisi::create([
            'nama_divisi' => 'Backend Development',
            'departemen_id' => $this->departemen1->id
        ]);

        $divisiIT2 = Divisi::create([
            'nama_divisi' => 'Frontend Development',
            'departemen_id' => $this->departemen1->id
        ]);

        $divisiHR1 = Divisi::create([
            'nama_divisi' => 'Recruitment',
            'departemen_id' => $this->departemen2->id
        ]);

        // Test filtering divisi by departemen1 (IT Department)
        $divisiIT = Divisi::where('departemen_id', $this->departemen1->id)->get();
        $this->assertCount(3, $divisiIT); // divisi1, divisiIT1, divisiIT2
        $this->assertTrue($divisiIT->contains('id', $this->divisi1->id));
        $this->assertTrue($divisiIT->contains('id', $divisiIT1->id));
        $this->assertTrue($divisiIT->contains('id', $divisiIT2->id));

        // Test filtering divisi by departemen2 (HR Department)
        $divisiHR = Divisi::where('departemen_id', $this->departemen2->id)->get();
        $this->assertCount(2, $divisiHR); // divisi2, divisiHR1
        $this->assertTrue($divisiHR->contains('id', $this->divisi2->id));
        $this->assertTrue($divisiHR->contains('id', $divisiHR1->id));
    }

    /** @test */
    public function it_validates_departemen_divisi_consistency()
    {
        // Create a divisi that belongs to departemen1
        $divisiFromDept1 = Divisi::create([
            'nama_divisi' => 'Test Divisi Dept 1',
            'departemen_id' => $this->departemen1->id
        ]);

        // This should work - correct departemen-divisi relationship
        $validMutasi = MutasiPromosiDemosi::create([
            'karyawan_id' => $this->karyawan->id,
            'tipe' => 'mutasi',
            'entitas_id' => $this->entitas->id,
            'departemen_id' => $this->departemen1->id,
            'divisi_id' => $divisiFromDept1->id,
            'jabatan_id' => $this->jabatan->id,
            'tanggal_efektif' => now(),
            'alasan' => 'Valid mutasi',
            'is_active' => false,
        ]);

        $this->assertDatabaseHas('mutasi_promosi_demosi', [
            'id' => $validMutasi->id,
            'departemen_id' => $this->departemen1->id,
            'divisi_id' => $divisiFromDept1->id,
        ]);

        // Verify the relationship is correct
        $this->assertEquals($this->departemen1->id, $validMutasi->divisi->departemen_id);
    }

    /** @test */
    public function it_can_create_mutasi_record_with_all_required_fields()
    {
        // Create a mutasi record with all required fields
        $mutasiData = [
            'karyawan_id' => $this->karyawan->id,
            'tipe' => 'mutasi',
            'entitas_id' => $this->entitas->id,
            'departemen_id' => $this->departemen2->id,
            'divisi_id' => $this->divisi2->id,
            'jabatan_id' => $this->jabatan->id,
            'tanggal_efektif' => now(),
            'alasan' => 'Test mutasi',
            'is_active' => true,
        ];

        $mutasi = MutasiPromosiDemosi::create($mutasiData);

        // Verify the mutasi was created with correct data
        $this->assertDatabaseHas('mutasi_promosi_demosi', [
            'karyawan_id' => $this->karyawan->id,
            'tipe' => 'mutasi',
            'departemen_id' => $this->departemen2->id,
            'divisi_id' => $this->divisi2->id,
            'alasan' => 'Test mutasi',
            'is_active' => true,
        ]);

        // Verify relationships work correctly
        $this->assertEquals($this->karyawan->id, $mutasi->karyawan->id);
        $this->assertEquals($this->departemen2->id, $mutasi->departemen->id);
        $this->assertEquals($this->divisi2->id, $mutasi->divisi->id);
        $this->assertEquals($this->jabatan->id, $mutasi->jabatan->id);
        $this->assertEquals($this->entitas->id, $mutasi->entitas->id);
    }
}
