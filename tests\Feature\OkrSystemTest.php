<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Objective;
use App\Models\KeyResult;
use App\Models\Tactic;
use App\Models\Task;
use App\Models\OkrPeriod;
use App\Models\Departemen;
use App\Models\Divisi;
use App\Services\OkrProgressTracker;
use Carbon\Carbon;

class OkrSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $departemen;
    protected $divisi;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'admin',
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
        ]);

        // Create test departemen and divisi
        $this->departemen = Departemen::factory()->create([
            'nama_departemen' => 'IT Department'
        ]);

        $this->divisi = Divisi::factory()->create([
            'nama_divisi' => 'Development',
            'departemen_id' => $this->departemen->id
        ]);

        $this->actingAs($this->user);
    }

    /** @test */
    public function it_can_create_an_objective()
    {
        $objectiveData = [
            'nama_objective' => 'Test Objective',
            'deskripsi' => 'This is a test objective',
            'periode_mulai' => Carbon::now()->startOfQuarter(),
            'periode_selesai' => Carbon::now()->endOfQuarter(),
            'status' => 'active',
            'progress_percentage' => 0,
            'target_completion' => Carbon::now()->addMonths(2),
            'owner_id' => $this->user->id,
            'departemen_id' => $this->departemen->id,
            'divisi_id' => $this->divisi->id,
            'created_by' => $this->user->id,
        ];

        $objective = Objective::create($objectiveData);

        $this->assertDatabaseHas('objectives', [
            'nama_objective' => 'Test Objective',
            'owner_id' => $this->user->id,
        ]);

        $this->assertEquals('Test Objective', $objective->nama_objective);
        $this->assertEquals($this->user->id, $objective->owner_id);
    }

    /** @test */
    public function it_can_create_key_results_for_objective()
    {
        $objective = Objective::factory()->create([
            'owner_id' => $this->user->id,
            'departemen_id' => $this->departemen->id,
            'divisi_id' => $this->divisi->id,
            'created_by' => $this->user->id,
        ]);

        $keyResultData = [
            'objective_id' => $objective->id,
            'nama_key_result' => 'Test Key Result',
            'deskripsi' => 'This is a test key result',
            'tipe_metrik' => 'percentage',
            'target_value' => 100,
            'current_value' => 50,
            'unit_measurement' => '%',
            'status' => 'in_progress',
            'due_date' => Carbon::now()->addMonth(),
            'created_by' => $this->user->id,
        ];

        $keyResult = KeyResult::create($keyResultData);

        $this->assertDatabaseHas('key_results', [
            'nama_key_result' => 'Test Key Result',
            'objective_id' => $objective->id,
        ]);

        $this->assertEquals(50, $keyResult->progress_percentage);
    }

    /** @test */
    public function it_can_create_tactics_for_objective()
    {
        $objective = Objective::factory()->create([
            'owner_id' => $this->user->id,
            'departemen_id' => $this->departemen->id,
            'divisi_id' => $this->divisi->id,
            'created_by' => $this->user->id,
        ]);

        $tacticData = [
            'objective_id' => $objective->id,
            'nama_tactic' => 'Test Tactic',
            'deskripsi' => 'This is a test tactic',
            'priority' => 'high',
            'status' => 'in_progress',
            'progress_percentage' => 25,
            'start_date' => Carbon::now(),
            'due_date' => Carbon::now()->addWeeks(2),
            'assigned_to' => $this->user->id,
            'estimated_hours' => 40,
            'actual_hours' => 10,
            'created_by' => $this->user->id,
        ];

        $tactic = Tactic::create($tacticData);

        $this->assertDatabaseHas('tactics', [
            'nama_tactic' => 'Test Tactic',
            'objective_id' => $objective->id,
        ]);

        $this->assertEquals('high', $tactic->priority);
        $this->assertEquals(25, $tactic->progress_percentage);
    }

    /** @test */
    public function it_calculates_objective_progress_correctly()
    {
        $objective = Objective::factory()->create([
            'owner_id' => $this->user->id,
            'created_by' => $this->user->id,
        ]);

        // Create key results with different progress
        KeyResult::factory()->create([
            'objective_id' => $objective->id,
            'progress_percentage' => 80,
            'created_by' => $this->user->id,
        ]);

        KeyResult::factory()->create([
            'objective_id' => $objective->id,
            'progress_percentage' => 60,
            'created_by' => $this->user->id,
        ]);

        // Create tactics with different progress
        Tactic::factory()->create([
            'objective_id' => $objective->id,
            'progress_percentage' => 70,
            'created_by' => $this->user->id,
        ]);

        Tactic::factory()->create([
            'objective_id' => $objective->id,
            'progress_percentage' => 50,
            'created_by' => $this->user->id,
        ]);

        $calculatedProgress = $objective->calculateProgress();

        // Expected: (80+60)/2 * 0.7 + (70+50)/2 * 0.3 = 70 * 0.7 + 60 * 0.3 = 49 + 18 = 67
        $this->assertEquals(67, $calculatedProgress);
    }
}
