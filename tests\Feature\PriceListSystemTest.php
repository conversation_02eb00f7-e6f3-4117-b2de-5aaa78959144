<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\Product;
use App\Models\Outlet;
use App\Models\PriceList;
use App\Models\Category;
use App\Services\PriceResolutionService;

class PriceListSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $product;
    protected $outlet;
    protected $priceResolutionService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->user = User::factory()->create([
            'role' => 'admin'
        ]);

        $category = Category::create([
            'name' => 'Test Category',
            'description' => 'Test category for price list testing'
        ]);

        $this->product = Product::create([
            'name' => 'Test Product',
            'sku' => 'TEST001',
            'price' => 100000,
            'cost_price' => 75000,
            'category_id' => $category->id,
            'is_active' => true,
        ]);

        $this->outlet = Outlet::create([
            'name' => 'Test Outlet',
            'code' => 'TEST001',
            'type' => 'toko',
            'is_active' => true,
        ]);

        $this->priceResolutionService = app(PriceResolutionService::class);
    }

    public function test_global_price_list_creation()
    {
        // Create global price list
        $globalPriceList = PriceList::create([
            'name' => 'Global Price List',
            'code' => 'GLOBAL',
            'is_global' => true,
            'is_active' => true,
            'created_by' => $this->user->id,
        ]);

        // Add product to global price list
        $globalPriceList->addProduct($this->product->id, 95000, 75000);

        $this->assertTrue($globalPriceList->is_global);
        $this->assertEquals(1, $globalPriceList->activeItems()->count());
    }

    public function test_outlet_specific_price_list()
    {
        // Create outlet-specific price list
        $outletPriceList = PriceList::create([
            'name' => 'VIP Price List',
            'code' => 'VIP001',
            'is_global' => false,
            'is_active' => true,
            'created_by' => $this->user->id,
        ]);

        // Add product with special price
        $outletPriceList->addProduct($this->product->id, 85000, 75000);

        // Assign price list to outlet
        $this->outlet->assignPriceList($outletPriceList->id, 1);

        $this->assertEquals(1, $this->outlet->activePriceLists()->count());
        $this->assertEquals(85000, $outletPriceList->getPriceForProduct($this->product->id));
    }

    public function test_price_resolution_priority()
    {
        // Create global price list
        $globalPriceList = PriceList::create([
            'name' => 'Global Price List',
            'code' => 'GLOBAL',
            'is_global' => true,
            'is_active' => true,
            'created_by' => $this->user->id,
        ]);
        $globalPriceList->addProduct($this->product->id, 95000, 75000);

        // Create outlet-specific price list
        $outletPriceList = PriceList::create([
            'name' => 'VIP Price List',
            'code' => 'VIP001',
            'is_global' => false,
            'is_active' => true,
            'created_by' => $this->user->id,
        ]);
        $outletPriceList->addProduct($this->product->id, 85000, 75000);

        // Assign price list to outlet
        $this->outlet->assignPriceList($outletPriceList->id, 1);

        // Test price resolution - should use outlet-specific price
        $priceInfo = $this->priceResolutionService->getProductPrice($this->product->id, $this->outlet->id);
        
        $this->assertEquals(85000, $priceInfo['price']);
        $this->assertEquals('outlet_price_list', $priceInfo['source']);
        $this->assertEquals($outletPriceList->id, $priceInfo['price_list_id']);
    }

    public function test_fallback_to_global_price_list()
    {
        // Create global price list
        $globalPriceList = PriceList::create([
            'name' => 'Global Price List',
            'code' => 'GLOBAL',
            'is_global' => true,
            'is_active' => true,
            'created_by' => $this->user->id,
        ]);
        $globalPriceList->addProduct($this->product->id, 95000, 75000);

        // Create outlet-specific price list without this product
        $outletPriceList = PriceList::create([
            'name' => 'VIP Price List',
            'code' => 'VIP001',
            'is_global' => false,
            'is_active' => true,
            'created_by' => $this->user->id,
        ]);
        // Don't add the product to outlet price list

        // Assign price list to outlet
        $this->outlet->assignPriceList($outletPriceList->id, 1);

        // Test price resolution - should fallback to global price
        $priceInfo = $this->priceResolutionService->getProductPrice($this->product->id, $this->outlet->id);
        
        $this->assertEquals(95000, $priceInfo['price']);
        $this->assertEquals('global_price_list', $priceInfo['source']);
        $this->assertEquals($globalPriceList->id, $priceInfo['price_list_id']);
    }

    public function test_fallback_to_product_default()
    {
        // No price lists assigned
        
        // Test price resolution - should fallback to product default
        $priceInfo = $this->priceResolutionService->getProductPrice($this->product->id, $this->outlet->id);
        
        $this->assertEquals(100000, $priceInfo['price']);
        $this->assertEquals('product_default', $priceInfo['source']);
        $this->assertNull($priceInfo['price_list_id']);
    }

    public function test_api_get_product_prices()
    {
        // Create API token
        $token = $this->user->createToken('test-token', ['pos:read'])->plainTextToken;

        // Create global price list
        $globalPriceList = PriceList::create([
            'name' => 'Global Price List',
            'code' => 'GLOBAL',
            'is_global' => true,
            'is_active' => true,
            'created_by' => $this->user->id,
        ]);
        $globalPriceList->addProduct($this->product->id, 95000, 75000);

        // Test API endpoint
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json',
        ])->get("/api/pos/prices/products?outlet_id={$this->outlet->id}");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'outlet_price',
                    'price_source',
                    'price_list_name',
                ]
            ],
            'pagination'
        ]);

        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals(95000, $data[0]['outlet_price']);
        $this->assertEquals('global_price_list', $data[0]['price_source']);
    }

    public function test_price_list_validation()
    {
        $validation = $this->priceResolutionService->validateOutletPriceConfiguration($this->outlet->id);
        
        $this->assertTrue($validation['valid']);
        $this->assertContains('No price lists assigned to this outlet', $validation['warnings']);
    }
}
