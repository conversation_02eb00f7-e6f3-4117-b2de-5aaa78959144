<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Warehouse;
use App\Models\Produk;
use App\Models\InventoryStock;
use App\Models\StockMovement;
use App\Models\SalesOrder;
use App\Models\PurchaseOrder;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Entitas;
use App\Models\User;

class WarehouseSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    }

    /** @test */
    public function it_can_access_warehouse_panel()
    {
        $response = $this->get('/warehouse');
        $response->assertStatus(200);
    }

    /** @test */
    public function it_can_create_warehouse()
    {
        $warehouseData = [
            'name' => 'Test Warehouse',
            'code' => 'TWH-001',
            'address' => 'Test Address',
            'phone' => '123456789',
            'manager_name' => 'Test Manager',
            'is_active' => true,
        ];

        $warehouse = Warehouse::create($warehouseData);
        
        $this->assertDatabaseHas('warehouses', [
            'name' => 'Test Warehouse',
            'code' => 'TWH-001',
        ]);
    }

    /** @test */
    public function it_can_create_inventory_stock()
    {
        $warehouse = Warehouse::factory()->create();
        $product = Produk::factory()->create();
        $entitas = Entitas::factory()->create();

        $stockData = [
            'product_id' => $product->id,
            'warehouse_id' => $warehouse->id,
            'entitas_id' => $entitas->id,
            'quantity' => 100,
            'available_quantity' => 90,
            'on_hold_quantity' => 5,
            'reserved_quantity' => 5,
            'average_cost' => 50000,
            'total_value' => 5000000,
            'minimum_stock' => 10,
            'maximum_stock' => 500,
            'reorder_point' => 20,
            'safety_stock' => 15,
        ];

        $stock = InventoryStock::create($stockData);
        
        $this->assertDatabaseHas('inventory_stocks', [
            'product_id' => $product->id,
            'warehouse_id' => $warehouse->id,
            'quantity' => 100,
        ]);
    }

    /** @test */
    public function it_can_create_stock_movement()
    {
        $warehouse = Warehouse::factory()->create();
        $product = Produk::factory()->create();
        $entitas = Entitas::factory()->create();

        $movementData = [
            'movement_number' => 'MOV20250723001',
            'movement_date' => now(),
            'movement_type' => 'Purchase_Receipt',
            'product_id' => $product->id,
            'warehouse_id' => $warehouse->id,
            'entitas_id' => $entitas->id,
            'quantity' => 50,
            'unit_cost' => 45000,
            'total_value' => 2250000,
            'reference_number' => 'PO-001',
            'notes' => 'Test movement',
            'created_by' => $this->user->id,
        ];

        $movement = StockMovement::create($movementData);
        
        $this->assertDatabaseHas('stock_movements', [
            'movement_number' => 'MOV20250723001',
            'product_id' => $product->id,
            'quantity' => 50,
        ]);
    }

    /** @test */
    public function it_can_create_sales_order()
    {
        $customer = Customer::factory()->create();
        $warehouse = Warehouse::factory()->create();
        $entitas = Entitas::factory()->create();

        $salesOrderData = [
            'so_number' => 'SO20250723001',
            'so_date' => now(),
            'customer_id' => $customer->id,
            'warehouse_id' => $warehouse->id,
            'entitas_id' => $entitas->id,
            'subtotal' => 1000000,
            'tax_amount' => 100000,
            'discount_amount' => 50000,
            'total_amount' => 1050000,
            'status' => 'Draft',
            'created_by' => $this->user->id,
        ];

        $salesOrder = SalesOrder::create($salesOrderData);
        
        $this->assertDatabaseHas('sales_orders', [
            'so_number' => 'SO20250723001',
            'customer_id' => $customer->id,
            'total_amount' => 1050000,
        ]);
    }

    /** @test */
    public function it_can_create_purchase_order()
    {
        $supplier = Supplier::factory()->create();
        $warehouse = Warehouse::factory()->create();
        $entitas = Entitas::factory()->create();

        $purchaseOrderData = [
            'po_number' => 'PO20250723001',
            'po_date' => now(),
            'supplier_id' => $supplier->id,
            'warehouse_id' => $warehouse->id,
            'entitas_id' => $entitas->id,
            'subtotal' => 2000000,
            'tax_amount' => 200000,
            'discount_amount' => 100000,
            'total_amount' => 2100000,
            'status' => 'Draft',
            'created_by' => $this->user->id,
        ];

        $purchaseOrder = PurchaseOrder::create($purchaseOrderData);
        
        $this->assertDatabaseHas('purchase_orders', [
            'po_number' => 'PO20250723001',
            'supplier_id' => $supplier->id,
            'total_amount' => 2100000,
        ]);
    }

    /** @test */
    public function it_can_test_model_relationships()
    {
        $warehouse = Warehouse::factory()->create();
        $product = Produk::factory()->create();
        $entitas = Entitas::factory()->create();
        
        $stock = InventoryStock::factory()->create([
            'product_id' => $product->id,
            'warehouse_id' => $warehouse->id,
            'entitas_id' => $entitas->id,
        ]);

        // Test relationships
        $this->assertEquals($product->id, $stock->product->id);
        $this->assertEquals($warehouse->id, $stock->warehouse->id);
        $this->assertEquals($entitas->id, $stock->entitas->id);
        
        // Test reverse relationships
        $this->assertTrue($warehouse->inventoryStocks->contains($stock));
        $this->assertTrue($product->inventoryStocks->contains($stock));
    }

    /** @test */
    public function it_can_calculate_stock_status()
    {
        $warehouse = Warehouse::factory()->create();
        $product = Produk::factory()->create();
        $entitas = Entitas::factory()->create();
        
        $stock = InventoryStock::factory()->create([
            'product_id' => $product->id,
            'warehouse_id' => $warehouse->id,
            'entitas_id' => $entitas->id,
            'quantity' => 5,
            'available_quantity' => 5,
            'minimum_stock' => 10,
            'reorder_point' => 15,
        ]);

        $this->assertEquals('Reorder Required', $stock->available_stock_status);
    }

    /** @test */
    public function it_can_reserve_and_release_stock()
    {
        $warehouse = Warehouse::factory()->create();
        $product = Produk::factory()->create();
        $entitas = Entitas::factory()->create();
        
        $stock = InventoryStock::factory()->create([
            'product_id' => $product->id,
            'warehouse_id' => $warehouse->id,
            'entitas_id' => $entitas->id,
            'quantity' => 100,
            'available_quantity' => 100,
            'reserved_quantity' => 0,
        ]);

        // Test reservation
        $stock->reserveQuantity(20);
        $this->assertEquals(80, $stock->available_quantity);
        $this->assertEquals(20, $stock->reserved_quantity);

        // Test release
        $stock->releaseReservedQuantity(10);
        $this->assertEquals(90, $stock->available_quantity);
        $this->assertEquals(10, $stock->reserved_quantity);
    }
}
