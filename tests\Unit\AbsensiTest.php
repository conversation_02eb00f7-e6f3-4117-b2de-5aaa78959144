<?php

namespace Tests\Unit;

use App\Models\Absensi;
use App\Models\Karyawan;
use App\Models\User;
use App\Models\Schedule;
use App\Models\Shift;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class AbsensiTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function absensi_belongs_to_karyawan()
    {
        // Buat user dan karyawan
        $user = User::factory()->create([
            'role' => 'karyawan'
        ]);

        $karyawan = Karyawan::create([
            'id_user' => $user->id,
            'nip' => 'K001',
            'nama_lengkap' => 'Karyawan Test',
            'jenis_kelamin' => 'L',
            'tempat_lahir' => 'Jakarta',
            'tanggal_lahir' => '1990-01-01',
            'alamat' => 'Jl. Test No. 123',
            'nomor_telepon' => '08123456789',
            'email' => '<EMAIL>',
            'tanggal_masuk' => '2023-01-01',
            'status_aktif' => 1,
        ]);

        // Buat absensi
        $absensi = Absensi::create([
            'karyawan_id' => $karyawan->id,
            'tanggal_absensi' => '2023-06-01',
            'waktu_masuk' => '08:00:00',
            'waktu_keluar' => '17:00:00',
            'status' => 'hadir',
            'lokasi_masuk' => '-6.123,106.456',
            'lokasi_keluar' => '-6.123,106.456',
            'foto_masuk' => 'masuk.jpg',
            'foto_keluar' => 'keluar.jpg',
        ]);

        // Verifikasi relasi
        $this->assertEquals($karyawan->id, $absensi->karyawan->id);

        // Refresh karyawan to load relationship
        $karyawan->refresh();
        $this->assertTrue($karyawan->absensi->contains($absensi));
    }

    /** @test */
    public function absensi_belongs_to_jadwal()
    {
        // Buat user dan karyawan
        $user = User::factory()->create([
            'role' => 'karyawan'
        ]);

        $karyawan = Karyawan::create([
            'id_user' => $user->id,
            'nip' => 'K001',
            'nama_lengkap' => 'Karyawan Test',
            'jenis_kelamin' => 'L',
            'tempat_lahir' => 'Jakarta',
            'tanggal_lahir' => '1990-01-01',
            'alamat' => 'Jl. Test No. 123',
            'nomor_telepon' => '08123456789',
            'email' => '<EMAIL>',
            'tanggal_masuk' => '2023-01-01',
            'status_aktif' => 1,
        ]);

        // Buat shift
        $shift = Shift::create([
            'nama_shift' => 'Shift Pagi',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'toleransi_keterlambatan' => 15,
        ]);

        // Buat jadwal
        $jadwal = Schedule::create([
            'karyawan_id' => $karyawan->id,
            'shift_id' => $shift->id,
            'tanggal_jadwal' => '2023-06-01',
            'status' => 'aktif',
        ]);

        // Buat absensi
        $absensi = Absensi::create([
            'karyawan_id' => $karyawan->id,
            'jadwal_id' => $jadwal->id,
            'tanggal_absensi' => '2023-06-01',
            'waktu_masuk' => '08:00:00',
            'waktu_keluar' => '17:00:00',
            'status' => 'hadir',
            'lokasi_masuk' => '-6.123,106.456',
            'lokasi_keluar' => '-6.123,106.456',
            'foto_masuk' => 'masuk.jpg',
            'foto_keluar' => 'keluar.jpg',
        ]);

        // Verifikasi relasi
        $this->assertEquals($jadwal->id, $absensi->jadwal->id);
    }

    /** @test */
    public function absensi_status_is_terlambat_when_late()
    {
        // Buat user dan karyawan
        $user = User::factory()->create([
            'role' => 'karyawan'
        ]);

        $karyawan = Karyawan::create([
            'id_user' => $user->id,
            'nip' => 'K001',
            'nama_lengkap' => 'Karyawan Test',
            'jenis_kelamin' => 'L',
            'tempat_lahir' => 'Jakarta',
            'tanggal_lahir' => '1990-01-01',
            'alamat' => 'Jl. Test No. 123',
            'nomor_telepon' => '08123456789',
            'email' => '<EMAIL>',
            'tanggal_masuk' => '2023-01-01',
            'status_aktif' => 1,
        ]);

        // Buat shift
        $shift = Shift::create([
            'nama_shift' => 'Shift Pagi',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'toleransi_keterlambatan' => 15,
        ]);

        // Buat jadwal
        $jadwal = Schedule::create([
            'karyawan_id' => $karyawan->id,
            'shift_id' => $shift->id,
            'tanggal_jadwal' => Carbon::today()->format('Y-m-d'),
            'status' => 'aktif',
        ]);

        // Buat absensi terlambat (lebih dari toleransi)
        $absensi = Absensi::create([
            'karyawan_id' => $karyawan->id,
            'jadwal_id' => $jadwal->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => '08:20:00', // Terlambat 20 menit (toleransi 15 menit)
            'status' => 'terlambat',
            'lokasi_masuk' => '-6.123,106.456',
            'foto_masuk' => 'masuk.jpg',
        ]);

        // Verifikasi status
        $this->assertEquals('terlambat', $absensi->status);
    }
}
