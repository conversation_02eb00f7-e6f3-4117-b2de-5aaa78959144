<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\Karyawan;
use App\Models\Shift;
use App\Models\Schedule;
use App\Models\Absensi;
use App\Models\Jabatan;
use App\Models\Divisi;
use App\Models\Entitas;
use App\Models\Departemen;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class AttendanceBusinessLogicTest extends TestCase
{
    use RefreshDatabase;

    protected $entitas;
    protected $departemen;
    protected $divisi;
    protected $jabatan;
    protected $shift;

    protected function setUp(): void
    {
        parent::setUp();
        $this->createOrganizationalStructure();
        $this->createShift();
    }

    protected function createOrganizationalStructure()
    {
        $this->entitas = Entitas::create([
            'nama' => 'PT Viera Anugrah Pertama',
            'alamat' => 'Jakarta',
            'keterangan' => 'Test entity for testing',
        ]);

        $this->departemen = Departemen::create([
            'nama_departemen' => 'IT Department',
            'created_by' => 1,
        ]);

        $this->divisi = Divisi::create([
            'nama_divisi' => 'Software Development',
            'id_departemen' => $this->departemen->id,
            'created_by' => 1,
        ]);

        $this->jabatan = Jabatan::create([
            'nama_jabatan' => 'Software Developer',
            'created_by' => 1,
        ]);
    }

    protected function createShift()
    {
        $this->shift = Shift::create([
            'nama_shift' => 'Shift Pagi',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'toleransi_keterlambatan' => 15,
            'is_active' => true,
            'created_by' => 1,
        ]);
    }

    protected function createKaryawan($user = null)
    {
        if (!$user) {
            $user = User::factory()->create(['role' => 'karyawan']);
        }

        return Karyawan::create([
            'id_user' => $user->id,
            'nip' => 'K' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT),
            'nama_lengkap' => 'Karyawan Test',
            'jenis_kelamin' => 'Laki-laki',
            'kota_lahir' => 'Jakarta',
            'tanggal_lahir' => '1990-01-01',
            'alamat' => 'Jl. Test No. 123',
            'nomor_telepon' => '08123456789',
            'email' => $user->email,
            'status_aktif' => 1,
            'id_entitas' => $this->entitas->id,
            'id_departemen' => $this->departemen->id,
            'id_divisi' => $this->divisi->id,
            'id_jabatan' => $this->jabatan->id,
            'created_by' => 1,
        ]);
    }

    protected function createSchedule($karyawan, $date = null)
    {
        return Schedule::create([
            'karyawan_id' => $karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => $date ?: Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => $this->shift->waktu_mulai,
            'waktu_keluar' => $this->shift->waktu_selesai,
            'status' => 'aktif',
        ]);
    }

    /** @test */
    public function attendance_can_be_created_with_geolocation_data()
    {
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);
        $schedule = $this->createSchedule($karyawan);

        $attendance = Absensi::create([
            'karyawan_id' => $karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => '08:00:00',
            'lokasi_masuk' => '-6.200000,106.816666',
            'foto_masuk' => 'test-selfie.jpg',
            'status' => 'hadir',
        ]);

        $this->assertDatabaseHas('absensi', [
            'karyawan_id' => $karyawan->id,
            'lokasi_masuk' => '-6.200000,106.816666',
            'status' => 'hadir',
        ]);

        // Test geolocation parsing
        $location = explode(',', $attendance->lokasi_masuk);
        $this->assertCount(2, $location);
        $this->assertEquals(-6.200000, (float)$location[0]);
        $this->assertEquals(106.816666, (float)$location[1]);
    }

    /** @test */
    public function attendance_status_is_determined_by_check_in_time()
    {
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);
        $schedule = $this->createSchedule($karyawan);

        // Test on-time attendance
        $onTimeAttendance = Absensi::create([
            'karyawan_id' => $karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => '08:00:00', // Exactly on time
            'lokasi_masuk' => '-6.200000,106.816666',
            'foto_masuk' => 'test-selfie.jpg',
            'status' => 'hadir',
        ]);

        $this->assertEquals('hadir', $onTimeAttendance->status);

        // Test late attendance (beyond tolerance)
        $lateAttendance = Absensi::create([
            'karyawan_id' => $karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::tomorrow()->format('Y-m-d'),
            'waktu_masuk' => '08:20:00', // 20 minutes late (tolerance is 15)
            'lokasi_masuk' => '-6.200000,106.816666',
            'foto_masuk' => 'test-selfie.jpg',
            'status' => 'terlambat',
        ]);

        $this->assertEquals('terlambat', $lateAttendance->status);
    }

    /** @test */
    public function attendance_can_handle_check_in_and_check_out()
    {
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);
        $schedule = $this->createSchedule($karyawan);

        // Create check-in record
        $attendance = Absensi::create([
            'karyawan_id' => $karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => '08:00:00',
            'lokasi_masuk' => '-6.200000,106.816666',
            'foto_masuk' => 'checkin-selfie.jpg',
            'status' => 'hadir',
        ]);

        // Update with check-out
        $attendance->update([
            'waktu_keluar' => '17:00:00',
            'lokasi_keluar' => '-6.201000,106.817000',
            'foto_keluar' => 'checkout-selfie.jpg',
        ]);

        $this->assertNotNull($attendance->waktu_masuk);
        $this->assertNotNull($attendance->waktu_keluar);
        $this->assertNotNull($attendance->lokasi_masuk);
        $this->assertNotNull($attendance->lokasi_keluar);
        $this->assertNotNull($attendance->foto_masuk);
        $this->assertNotNull($attendance->foto_keluar);
    }

    /** @test */
    public function attendance_calculates_work_duration_correctly()
    {
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);

        $attendance = Absensi::create([
            'karyawan_id' => $karyawan->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => '08:00:00',
            'waktu_keluar' => '17:00:00',
            'lokasi_masuk' => '-6.200000,106.816666',
            'lokasi_keluar' => '-6.200000,106.816666',
            'foto_masuk' => 'test-selfie.jpg',
            'foto_keluar' => 'test-selfie.jpg',
            'status' => 'hadir',
        ]);

        // 9 hours = 540 minutes
        $this->assertEquals(540, $attendance->durasi_kerja);
    }

    /** @test */
    public function attendance_prevents_duplicate_daily_records()
    {
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);
        $schedule = $this->createSchedule($karyawan);

        // Create first attendance
        $firstAttendance = Absensi::create([
            'karyawan_id' => $karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => '08:00:00',
            'lokasi_masuk' => '-6.200000,106.816666',
            'foto_masuk' => 'test-selfie.jpg',
            'status' => 'hadir',
        ]);

        // Check that only one record exists for today
        $todayAttendance = Absensi::where('karyawan_id', $karyawan->id)
            ->where('tanggal_absensi', Carbon::today()->format('Y-m-d'))
            ->get();

        $this->assertCount(1, $todayAttendance);
    }

    /** @test */
    public function attendance_can_be_approved_by_supervisor()
    {
        $supervisor = User::factory()->create(['role' => 'supervisor']);
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);

        $attendance = Absensi::create([
            'karyawan_id' => $karyawan->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => '08:00:00',
            'waktu_keluar' => '17:00:00',
            'lokasi_masuk' => '-6.200000,106.816666',
            'lokasi_keluar' => '-6.200000,106.816666',
            'foto_masuk' => 'test-selfie.jpg',
            'foto_keluar' => 'test-selfie.jpg',
            'status' => 'hadir',
        ]);

        // Approve attendance
        $attendance->update([
            'approved_by' => $supervisor->id,
            'approved_at' => Carbon::now(),
        ]);

        $this->assertEquals($supervisor->id, $attendance->approved_by);
        $this->assertNotNull($attendance->approved_at);
        $this->assertTrue($attendance->is_approved);
    }

    /** @test */
    public function attendance_validates_geolocation_coordinates()
    {
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);

        // Test valid coordinates
        $validCoordinates = [
            '-6.200000,106.816666', // Jakarta
            '0,0', // Equator/Prime Meridian
            '90,180', // North Pole, International Date Line
            '-90,-180', // South Pole, opposite side
        ];

        foreach ($validCoordinates as $coordinates) {
            $attendance = Absensi::create([
                'karyawan_id' => $karyawan->id,
                'tanggal_absensi' => Carbon::today()->addDays(rand(1, 10))->format('Y-m-d'),
                'waktu_masuk' => '08:00:00',
                'lokasi_masuk' => $coordinates,
                'foto_masuk' => 'test-selfie.jpg',
                'status' => 'hadir',
            ]);

            $this->assertEquals($coordinates, $attendance->lokasi_masuk);
            
            // Verify coordinates can be parsed
            $location = explode(',', $attendance->lokasi_masuk);
            $this->assertCount(2, $location);
            $this->assertTrue(is_numeric($location[0]));
            $this->assertTrue(is_numeric($location[1]));
        }
    }

    /** @test */
    public function attendance_handles_different_shift_schedules()
    {
        $user = User::factory()->create(['role' => 'karyawan']);
        $karyawan = $this->createKaryawan($user);

        // Create night shift
        $nightShift = Shift::create([
            'nama_shift' => 'Shift Malam',
            'waktu_mulai' => '22:00:00',
            'waktu_selesai' => '06:00:00', // Next day
            'toleransi_keterlambatan' => 10,
            'is_active' => true,
            'created_by' => 1,
        ]);

        $nightSchedule = Schedule::create([
            'karyawan_id' => $karyawan->id,
            'shift_id' => $nightShift->id,
            'tanggal_jadwal' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => $nightShift->waktu_mulai,
            'waktu_keluar' => $nightShift->waktu_selesai,
            'status' => 'aktif',
        ]);

        $attendance = Absensi::create([
            'karyawan_id' => $karyawan->id,
            'jadwal_id' => $nightSchedule->id,
            'tanggal_absensi' => Carbon::today()->format('Y-m-d'),
            'waktu_masuk' => '22:00:00',
            'waktu_keluar' => '06:00:00',
            'lokasi_masuk' => '-6.200000,106.816666',
            'lokasi_keluar' => '-6.200000,106.816666',
            'foto_masuk' => 'test-selfie.jpg',
            'foto_keluar' => 'test-selfie.jpg',
            'status' => 'hadir',
        ]);

        $this->assertEquals('hadir', $attendance->status);
        $this->assertEquals($nightSchedule->id, $attendance->jadwal_id);
        
        // Night shift duration calculation (8 hours across midnight)
        $this->assertEquals(480, $attendance->durasi_kerja); // 8 hours = 480 minutes
    }
}
