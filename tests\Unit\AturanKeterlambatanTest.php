<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\AturanKeterlambatan;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AturanKeterlambatanTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_calculate_nominal_tetap_penalty()
    {
        $aturan = AturanKeterlambatan::create([
            'nama_aturan' => 'Test Nominal Tetap',
            'menit_dari' => 1,
            'menit_sampai' => 15,
            'denda_nominal' => 50000,
            'jenis_denda' => 'nominal_tetap',
            'is_active' => true,
        ]);

        $denda = $aturan->hitungDenda(10, 5000000);
        $this->assertEquals(50000, $denda);
    }

    /** @test */
    public function it_can_calculate_per_minute_penalty()
    {
        $aturan = AturanKeterlambatan::create([
            'nama_aturan' => 'Test Per Menit',
            'menit_dari' => 16,
            'menit_sampai' => 30,
            'denda_per_menit' => 2000,
            'jenis_denda' => 'per_menit',
            'is_active' => true,
        ]);

        $denda = $aturan->hitungDenda(20, 5000000);
        $this->assertEquals(40000, $denda); // 20 menit * 2000
    }

    /** @test */
    public function it_can_calculate_percentage_penalty()
    {
        $aturan = AturanKeterlambatan::create([
            'nama_aturan' => 'Test Persentase',
            'menit_dari' => 31,
            'menit_sampai' => null,
            'persentase_denda' => 0.5,
            'jenis_denda' => 'persentase_gaji',
            'is_active' => true,
        ]);

        $denda = $aturan->hitungDenda(45, 5000000);
        $this->assertEquals(25000, $denda); // 0.5% dari 5.000.000
    }

    /** @test */
    public function it_returns_zero_for_minutes_below_range()
    {
        $aturan = AturanKeterlambatan::create([
            'nama_aturan' => 'Test Range',
            'menit_dari' => 10,
            'menit_sampai' => 20,
            'denda_nominal' => 50000,
            'jenis_denda' => 'nominal_tetap',
            'is_active' => true,
        ]);

        $denda = $aturan->hitungDenda(5, 5000000);
        $this->assertEquals(0, $denda);
    }

    /** @test */
    public function it_returns_zero_for_minutes_above_range()
    {
        $aturan = AturanKeterlambatan::create([
            'nama_aturan' => 'Test Range',
            'menit_dari' => 10,
            'menit_sampai' => 20,
            'denda_nominal' => 50000,
            'jenis_denda' => 'nominal_tetap',
            'is_active' => true,
        ]);

        $denda = $aturan->hitungDenda(25, 5000000);
        $this->assertEquals(0, $denda);
    }

    /** @test */
    public function it_can_check_if_applicable_for_lateness()
    {
        $aturan = AturanKeterlambatan::create([
            'nama_aturan' => 'Test Applicable',
            'menit_dari' => 10,
            'menit_sampai' => 20,
            'denda_nominal' => 50000,
            'jenis_denda' => 'nominal_tetap',
            'is_active' => true,
        ]);

        $this->assertTrue($aturan->isApplicable(15));
        $this->assertFalse($aturan->isApplicable(5));
        $this->assertFalse($aturan->isApplicable(25));
    }

    /** @test */
    public function it_can_find_rules_for_lateness_scope()
    {
        AturanKeterlambatan::create([
            'nama_aturan' => 'Rule 1',
            'menit_dari' => 1,
            'menit_sampai' => 15,
            'denda_nominal' => 25000,
            'jenis_denda' => 'nominal_tetap',
            'is_active' => true,
        ]);

        AturanKeterlambatan::create([
            'nama_aturan' => 'Rule 2',
            'menit_dari' => 16,
            'menit_sampai' => 30,
            'denda_nominal' => 50000,
            'jenis_denda' => 'nominal_tetap',
            'is_active' => true,
        ]);

        AturanKeterlambatan::create([
            'nama_aturan' => 'Rule 3',
            'menit_dari' => 31,
            'menit_sampai' => null,
            'denda_nominal' => 100000,
            'jenis_denda' => 'nominal_tetap',
            'is_active' => true,
        ]);

        // Test untuk 10 menit terlambat
        $rule = AturanKeterlambatan::active()->forLateness(10)->first();
        $this->assertEquals('Rule 1', $rule->nama_aturan);

        // Test untuk 25 menit terlambat
        $rule = AturanKeterlambatan::active()->forLateness(25)->first();
        $this->assertEquals('Rule 2', $rule->nama_aturan);

        // Test untuk 45 menit terlambat
        $rule = AturanKeterlambatan::active()->forLateness(45)->first();
        $this->assertEquals('Rule 3', $rule->nama_aturan);
    }

    /** @test */
    public function it_returns_zero_for_percentage_penalty_without_salary()
    {
        $aturan = AturanKeterlambatan::create([
            'nama_aturan' => 'Test Persentase No Salary',
            'menit_dari' => 1,
            'menit_sampai' => null,
            'persentase_denda' => 1.0,
            'jenis_denda' => 'persentase_gaji',
            'is_active' => true,
        ]);

        $denda = $aturan->hitungDenda(10, 0);
        $this->assertEquals(0, $denda);
    }

    /** @test */
    public function it_formats_range_waktu_correctly()
    {
        $aturanWithRange = AturanKeterlambatan::create([
            'nama_aturan' => 'With Range',
            'menit_dari' => 1,
            'menit_sampai' => 15,
            'denda_nominal' => 25000,
            'jenis_denda' => 'nominal_tetap',
            'is_active' => true,
        ]);

        $aturanWithoutRange = AturanKeterlambatan::create([
            'nama_aturan' => 'Without Range',
            'menit_dari' => 16,
            'menit_sampai' => null,
            'denda_nominal' => 50000,
            'jenis_denda' => 'nominal_tetap',
            'is_active' => true,
        ]);

        $this->assertEquals('1 - 15 menit', $aturanWithRange->range_waktu);
        $this->assertEquals('≥ 16 menit', $aturanWithoutRange->range_waktu);
    }

    /** @test */
    public function it_formats_denda_correctly()
    {
        $nominalTetap = AturanKeterlambatan::create([
            'nama_aturan' => 'Nominal Tetap',
            'menit_dari' => 1,
            'menit_sampai' => 15,
            'denda_nominal' => 50000,
            'jenis_denda' => 'nominal_tetap',
            'is_active' => true,
        ]);

        $perMenit = AturanKeterlambatan::create([
            'nama_aturan' => 'Per Menit',
            'menit_dari' => 16,
            'menit_sampai' => 30,
            'denda_per_menit' => 2000,
            'jenis_denda' => 'per_menit',
            'is_active' => true,
        ]);

        $persentase = AturanKeterlambatan::create([
            'nama_aturan' => 'Persentase',
            'menit_dari' => 31,
            'menit_sampai' => null,
            'persentase_denda' => 1.5,
            'jenis_denda' => 'persentase_gaji',
            'is_active' => true,
        ]);

        $this->assertEquals('Rp 50.000', $nominalTetap->formatted_denda);
        $this->assertEquals('Rp 2.000 per menit', $perMenit->formatted_denda);
        $this->assertEquals('1.50% dari gaji pokok', $persentase->formatted_denda);
    }
}
