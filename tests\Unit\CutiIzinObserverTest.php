<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\Karyawan;
use App\Models\CutiIzin;
use App\Models\Absensi;
use App\Models\Schedule;
use App\Models\Shift;
use App\Observers\CutiIzinObserver;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CutiIzinObserverTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Karyawan $karyawan;
    private Shift $shift;
    private Schedule $schedule;
    private CutiIzinObserver $observer;

    protected function setUp(): void
    {
        parent::setUp();

        $this->observer = new CutiIzinObserver();

        // Create test user and employee
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'manager_hrd'
        ]);

        $this->karyawan = Karyawan::factory()->create([
            'id_user' => $this->user->id,
            'nama_lengkap' => 'Test Employee',
            'nip' => 'EMP001'
        ]);

        // Create test shift
        $this->shift = Shift::factory()->create([
            'nama_shift' => 'Regular Shift',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'toleransi_keterlambatan' => 15,
            'is_split_shift' => false
        ]);

        // Create test schedule for today and next few days
        for ($i = 0; $i < 5; $i++) {
            Schedule::factory()->create([
                'karyawan_id' => $this->karyawan->id,
                'shift_id' => $this->shift->id,
                'entitas_id' => $this->karyawan->id_entitas,
                'tanggal_jadwal' => Carbon::today()->addDays($i)
            ]);
        }
    }

    /** @test */
    public function it_creates_attendance_records_when_leave_is_approved()
    {
        // Create a leave request
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today()->addDays(2),
            'jumlah_hari' => 3,
            'alasan' => 'Personal leave',
            'status' => 'pending'
        ]);

        // Approve the leave request (this should trigger the observer)
        $leave->update([
            'status' => 'approved',
            'approved_by' => $this->user->id,
            'approved_at' => now()
        ]);

        // Check that attendance records were created
        $attendanceRecords = Absensi::where('karyawan_id', $this->karyawan->id)
            ->whereBetween('tanggal_absensi', [
                Carbon::today()->format('Y-m-d'),
                Carbon::today()->addDays(2)->format('Y-m-d')
            ])
            ->get();

        $this->assertCount(3, $attendanceRecords);

        foreach ($attendanceRecords as $attendance) {
            $this->assertEquals('cuti', $attendance->status);
            $this->assertStringContainsString('Auto-generated from approved cuti', $attendance->keterangan);
            $this->assertEquals($this->user->id, $attendance->approved_by);
            $this->assertNotNull($attendance->approved_at);
        }
    }

    /** @test */
    public function it_sets_correct_status_based_on_leave_type()
    {
        $leaveTypes = [
            'cuti' => 'cuti',
            'izin' => 'izin',
            'sakit' => 'sakit'
        ];

        $dayOffset = 10;
        foreach ($leaveTypes as $jenisPermohonan => $expectedStatus) {
            // Create leave request with different dates for each type
            $leave = CutiIzin::create([
                'karyawan_id' => $this->karyawan->id,
                'jenis_permohonan' => $jenisPermohonan,
                'tanggal_mulai' => Carbon::today()->addDays($dayOffset),
                'tanggal_selesai' => Carbon::today()->addDays($dayOffset),
                'jumlah_hari' => 1,
                'alasan' => 'Test ' . $jenisPermohonan,
                'status' => 'pending'
            ]);

            // Approve the leave
            $leave->update([
                'status' => 'approved',
                'approved_by' => $this->user->id,
                'approved_at' => now()
            ]);

            // Check attendance record
            $attendance = Absensi::where('karyawan_id', $this->karyawan->id)
                ->whereDate('tanggal_absensi', Carbon::today()->addDays($dayOffset))
                ->first();

            $this->assertNotNull($attendance, "Attendance record not found for {$jenisPermohonan}");
            $this->assertEquals($expectedStatus, $attendance->status, "Status mismatch for {$jenisPermohonan}");

            $dayOffset++; // Use different date for next iteration
        }
    }

    /** @test */
    public function it_updates_existing_attendance_records_with_generic_status()
    {
        // Create existing attendance record with generic status
        $existingAttendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'tanggal_absensi' => Carbon::today(),
            'status' => 'hadir',
            'keterangan' => 'Manual entry'
        ]);

        // Create and approve leave request
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today(),
            'jumlah_hari' => 1,
            'alasan' => 'Personal leave',
            'status' => 'pending'
        ]);

        $leave->update([
            'status' => 'approved',
            'approved_by' => $this->user->id,
            'approved_at' => now()
        ]);

        // Check that existing record was updated
        $existingAttendance->refresh();
        $this->assertEquals('cuti', $existingAttendance->status);
        $this->assertStringContainsString('Auto-generated from approved cuti', $existingAttendance->keterangan);
    }

    /** @test */
    public function it_skips_existing_attendance_records_with_special_status()
    {
        // Create existing attendance record with special status
        $existingAttendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'tanggal_absensi' => Carbon::today(),
            'status' => 'izin',
            'keterangan' => 'Previous leave'
        ]);

        // Create and approve leave request
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today(),
            'jumlah_hari' => 1,
            'alasan' => 'Personal leave',
            'status' => 'pending'
        ]);

        $leave->update([
            'status' => 'approved',
            'approved_by' => $this->user->id,
            'approved_at' => now()
        ]);

        // Check that existing record was NOT updated
        $existingAttendance->refresh();
        $this->assertEquals('izin', $existingAttendance->status);
        $this->assertEquals('Previous leave', $existingAttendance->keterangan);
    }

    /** @test */
    public function it_does_not_create_records_when_status_is_not_approved()
    {
        // Create leave request and set to rejected
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today(),
            'jumlah_hari' => 1,
            'alasan' => 'Personal leave',
            'status' => 'pending'
        ]);

        $leave->update([
            'status' => 'rejected',
            'approved_by' => $this->user->id,
            'approved_at' => now(),
            'rejection_reason' => 'Not approved'
        ]);

        // Check that no attendance records were created
        $attendanceCount = Absensi::where('karyawan_id', $this->karyawan->id)
            ->whereDate('tanggal_absensi', Carbon::today())
            ->count();

        $this->assertEquals(0, $attendanceCount);
    }

    /** @test */
    public function it_links_attendance_to_schedule_when_available()
    {
        // Create leave request
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today(),
            'jumlah_hari' => 1,
            'alasan' => 'Personal leave',
            'status' => 'pending'
        ]);

        $leave->update([
            'status' => 'approved',
            'approved_by' => $this->user->id,
            'approved_at' => now()
        ]);

        // Check that attendance record is linked to schedule
        $attendance = Absensi::where('karyawan_id', $this->karyawan->id)
            ->whereDate('tanggal_absensi', Carbon::today())
            ->first();

        $this->assertNotNull($attendance);
        $this->assertNotNull($attendance->jadwal_id);

        $schedule = Schedule::where('karyawan_id', $this->karyawan->id)
            ->whereDate('tanggal_jadwal', Carbon::today())
            ->first();

        $this->assertEquals($schedule->id, $attendance->jadwal_id);
    }

    /** @test */
    public function it_handles_leave_spanning_multiple_days()
    {
        // Create 7-day leave request
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today()->addDays(6),
            'jumlah_hari' => 7,
            'alasan' => 'Long vacation',
            'status' => 'pending'
        ]);

        $leave->update([
            'status' => 'approved',
            'approved_by' => $this->user->id,
            'approved_at' => now()
        ]);

        // Check that 7 attendance records were created
        $attendanceRecords = Absensi::where('karyawan_id', $this->karyawan->id)
            ->whereBetween('tanggal_absensi', [
                Carbon::today()->format('Y-m-d'),
                Carbon::today()->addDays(6)->format('Y-m-d')
            ])
            ->get();

        $this->assertCount(7, $attendanceRecords);

        // Check each record has correct status and dates
        $currentDate = Carbon::today();
        foreach ($attendanceRecords as $attendance) {
            $this->assertEquals('cuti', $attendance->status);
            $this->assertEquals($currentDate->format('Y-m-d'), $attendance->tanggal_absensi->format('Y-m-d'));
            $currentDate->addDay();
        }
    }
}
