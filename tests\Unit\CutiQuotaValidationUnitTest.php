<?php

namespace Tests\Unit;

use App\Models\CutiIzin;
use App\Models\Karyawan;
use App\Models\RiwayatKontrak;
use Carbon\Carbon;
use PHPUnit\Framework\TestCase;

class CutiQuotaValidationUnitTest extends TestCase
{
    /** @test */
    public function it_calculates_working_days_correctly()
    {
        $cutiIzin = new CutiIzin([
            'tanggal_mulai' => '2025-01-20', // Monday
            'tanggal_selesai' => '2025-01-24', // Friday
        ]);

        $workingDays = $cutiIzin->calculateJumlahHari();
        
        $this->assertEquals(5, $workingDays);
    }

    /** @test */
    public function it_excludes_weekends_from_working_days()
    {
        $cutiIzin = new CutiIzin([
            'tanggal_mulai' => '2025-01-20', // Monday
            'tanggal_selesai' => '2025-01-26', // Sunday (next week)
        ]);

        $workingDays = $cutiIzin->calculateJumlahHari();
        
        // Monday to Friday (5 days) + Monday (1 day) = 6 working days
        $this->assertEquals(6, $workingDays);
    }

    /** @test */
    public function it_handles_single_day_leave()
    {
        $cutiIzin = new CutiIzin([
            'tanggal_mulai' => '2025-01-20', // Monday
            'tanggal_selesai' => '2025-01-20', // Same Monday
        ]);

        $workingDays = $cutiIzin->calculateJumlahHari();
        
        $this->assertEquals(1, $workingDays);
    }

    /** @test */
    public function it_returns_zero_for_weekend_only_leave()
    {
        $cutiIzin = new CutiIzin([
            'tanggal_mulai' => '2025-01-25', // Saturday
            'tanggal_selesai' => '2025-01-26', // Sunday
        ]);

        $workingDays = $cutiIzin->calculateJumlahHari();
        
        $this->assertEquals(0, $workingDays);
    }

    /** @test */
    public function it_validates_past_dates()
    {
        $cutiIzin = new CutiIzin([
            'karyawan_id' => 1,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::yesterday(),
            'tanggal_selesai' => Carbon::today(),
        ]);

        $errors = $cutiIzin->validateDates();
        
        $this->assertNotEmpty($errors);
        $this->assertContains('Tanggal mulai tidak boleh di masa lalu.', $errors);
    }

    /** @test */
    public function it_validates_end_date_before_start_date()
    {
        $cutiIzin = new CutiIzin([
            'karyawan_id' => 1,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::tomorrow()->addDays(2),
            'tanggal_selesai' => Carbon::tomorrow(),
        ]);

        $errors = $cutiIzin->validateDates();
        
        $this->assertNotEmpty($errors);
        $this->assertContains('Tanggal selesai tidak boleh sebelum tanggal mulai.', $errors);
    }

    /** @test */
    public function it_allows_valid_future_dates()
    {
        $cutiIzin = new CutiIzin([
            'karyawan_id' => 1,
            'jenis_permohonan' => 'izin', // Not cuti, so no quota validation
            'tanggal_mulai' => Carbon::tomorrow(),
            'tanggal_selesai' => Carbon::tomorrow()->addDays(2),
        ]);

        $errors = $cutiIzin->validateDates();
        
        // Should be empty for izin (no quota validation)
        $this->assertEmpty($errors);
    }
}
