<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\Karyawan;
use App\Models\Absensi;
use App\Models\Schedule;
use App\Models\Shift;
use App\Models\Entitas;
use App\Models\JadwalMasal;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EntitasJadwalIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Karyawan $karyawan;
    private Entitas $entitasUtama;
    private Entitas $entitasLain;
    private Shift $shift;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'karyawan'
        ]);

        // Create test entitas
        $this->entitasUtama = Entitas::factory()->create([
            'nama' => 'Toko Utama',
            'alamat' => 'Jl. Utama No. 1',
            'latitude' => -6.200000,
            'longitude' => 106.816666,
            'radius' => 100,
            'enable_geofencing' => true
        ]);

        $this->entitasLain = Entitas::factory()->create([
            'nama' => 'Toko Cabang',
            'alamat' => 'Jl. Cabang No. 2',
            'latitude' => -6.175110,
            'longitude' => 106.865036,
            'radius' => 150,
            'enable_geofencing' => true
        ]);

        // Create test employee with entitas utama
        $this->karyawan = Karyawan::factory()->create([
            'id_user' => $this->user->id,
            'nama_lengkap' => 'Test Employee',
            'nip' => 'EMP001',
            'id_entitas' => $this->entitasUtama->id
        ]);

        // Create test shift
        $this->shift = Shift::factory()->create([
            'nama_shift' => 'Regular Shift',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'toleransi_keterlambatan' => 15,
            'is_split_shift' => false
        ]);
    }

    /** @test */
    public function it_uses_entitas_from_schedule_for_absensi()
    {
        // Create schedule with different entitas than karyawan's main entitas
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'entitas_id' => $this->entitasLain->id, // Different from karyawan's entitas
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today()
        ]);

        // Create attendance record
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 0),
            'status' => 'hadir',
            'periode' => 1
        ]);

        // Should use entitas from schedule, not from karyawan
        $this->assertEquals($this->entitasLain->id, $attendance->entitas_id);
        $this->assertEquals($this->entitasLain->nama, $attendance->entitas_name);
        $this->assertEquals($this->entitasLain->id, $attendance->entitas->id);
    }

    /** @test */
    public function it_falls_back_to_karyawan_entitas_when_schedule_has_no_entitas()
    {
        // Create schedule without entitas_id
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'entitas_id' => null, // No entitas in schedule
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today()
        ]);

        // Create attendance record
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 0),
            'status' => 'hadir',
            'periode' => 1
        ]);

        // Should fall back to karyawan's entitas
        $this->assertEquals($this->entitasUtama->id, $attendance->entitas_id);
        $this->assertEquals($this->entitasUtama->nama, $attendance->entitas_name);
        $this->assertEquals($this->entitasUtama->id, $attendance->entitas->id);
    }

    /** @test */
    public function it_returns_null_when_no_entitas_available()
    {
        // Update karyawan to have no entitas
        $this->karyawan->update(['id_entitas' => null]);

        // Create schedule without entitas_id
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'entitas_id' => null,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today()
        ]);

        // Create attendance record
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 0),
            'status' => 'hadir',
            'periode' => 1
        ]);

        // Should return null for all entitas attributes
        $this->assertNull($attendance->entitas_id);
        $this->assertNull($attendance->entitas_name);
        $this->assertNull($attendance->entitas);
    }

    /** @test */
    public function it_validates_geofencing_based_on_schedule_entitas()
    {
        // Create schedule with entitas that has geofencing enabled
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'entitas_id' => $this->entitasLain->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today()
        ]);

        // Create attendance record with coordinates within radius of entitasLain
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 0),
            'latitude_masuk' => -6.175110, // Same as entitasLain
            'longitude_masuk' => 106.865036, // Same as entitasLain
            'status' => 'hadir',
            'periode' => 1
        ]);

        // Should validate against entitasLain, not entitasUtama
        $this->assertTrue($attendance->is_location_valid);
        $this->assertEquals(0, $attendance->distance_from_entitas); // Exact location
    }

    /** @test */
    public function it_handles_jadwal_masal_with_entitas()
    {
        // Create jadwal masal with specific entitas
        $jadwalMasal = JadwalMasal::create([
            'nama_jadwal' => 'Jadwal Test',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today()->addDays(6),
            'shift_id' => $this->shift->id,
            'entitas_id' => $this->entitasLain->id,
            'created_by' => $this->user->id,
            'keterangan' => 'Test jadwal masal'
        ]);

        // Check relationship
        $this->assertEquals($this->entitasLain->id, $jadwalMasal->entitas_id);
        $this->assertEquals($this->entitasLain->nama, $jadwalMasal->entitas->nama);
    }

    /** @test */
    public function it_loads_entitas_relationship_in_schedule()
    {
        // Create schedule with entitas
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'entitas_id' => $this->entitasLain->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today()
        ]);

        // Load with entitas relationship
        $scheduleWithEntitas = Schedule::with('entitas')->find($schedule->id);

        $this->assertNotNull($scheduleWithEntitas->entitas);
        $this->assertEquals($this->entitasLain->nama, $scheduleWithEntitas->entitas->nama);
    }

    /** @test */
    public function it_handles_attendance_without_schedule()
    {
        // Create attendance without schedule
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => null,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 0),
            'status' => 'hadir',
            'periode' => 1
        ]);

        // Should fall back to karyawan's entitas
        $this->assertEquals($this->entitasUtama->id, $attendance->entitas_id);
        $this->assertEquals($this->entitasUtama->nama, $attendance->entitas_name);
    }

    /** @test */
    public function it_prioritizes_schedule_entitas_over_karyawan_entitas()
    {
        // Create schedule with different entitas
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'entitas_id' => $this->entitasLain->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today()
        ]);

        // Create attendance
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'status' => 'hadir'
        ]);

        // Should prioritize schedule entitas over karyawan entitas
        $this->assertEquals($this->entitasLain->id, $attendance->entitas_id);
        $this->assertNotEquals($this->entitasUtama->id, $attendance->entitas_id);
        
        // Verify it's using schedule entitas, not karyawan entitas
        $this->assertEquals($schedule->entitas_id, $attendance->entitas_id);
        $this->assertNotEquals($this->karyawan->id_entitas, $attendance->entitas_id);
    }

    /** @test */
    public function it_calculates_distance_from_correct_entitas()
    {
        // Create schedule with entitasLain
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'entitas_id' => $this->entitasLain->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today()
        ]);

        // Create attendance with coordinates closer to entitasUtama but should validate against entitasLain
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'latitude_masuk' => -6.200000, // Closer to entitasUtama
            'longitude_masuk' => 106.816666, // Closer to entitasUtama
            'status' => 'hadir'
        ]);

        // Should calculate distance from entitasLain (from schedule), not entitasUtama (from karyawan)
        $distance = $attendance->distance_from_entitas;
        $this->assertNotNull($distance);
        $this->assertGreaterThan(0, $distance); // Should be > 0 since coordinates are different
    }

    /** @test */
    public function it_handles_geofencing_validation_with_schedule_entitas()
    {
        // Create schedule with entitas that has different geofencing settings
        $entitasNoGeofencing = Entitas::factory()->create([
            'nama' => 'Toko No Geofencing',
            'enable_geofencing' => false
        ]);

        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan->id,
            'entitas_id' => $entitasNoGeofencing->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today()
        ]);

        // Create attendance with any coordinates
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'latitude_masuk' => -7.000000, // Far from any entitas
            'longitude_masuk' => 107.000000, // Far from any entitas
            'status' => 'hadir'
        ]);

        // Should be valid because schedule's entitas has geofencing disabled
        $this->assertTrue($attendance->is_location_valid);
    }
}
