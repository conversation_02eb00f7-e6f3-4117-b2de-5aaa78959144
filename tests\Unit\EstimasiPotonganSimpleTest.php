<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\AturanKeterlambatan;
use Illuminate\Foundation\Testing\RefreshDatabase;

class EstimasiPotonganSimpleTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_calculate_simple_penalty_estimation()
    {
        // Create a simple rule
        $aturan = AturanKeterlambatan::create([
            'nama_aturan' => 'Test Simple Rule',
            'menit_dari' => 1,
            'menit_sampai' => 30,
            'denda_nominal' => 50000,
            'jenis_denda' => 'nominal_tetap',
            'is_active' => true,
        ]);

        // Test calculation
        $menitTerlambat = 15;
        $gajiPokok = 5000000;
        
        $estimasiPotongan = $aturan->hitungDenda($menitTerlambat, $gajiPokok);
        
        $this->assertEquals(50000, $estimasiPotongan);
    }

    /** @test */
    public function it_finds_correct_rule_for_lateness()
    {
        // Create multiple rules
        AturanKeterlambatan::create([
            'nama_aturan' => 'Rule 1-15',
            'menit_dari' => 1,
            'menit_sampai' => 15,
            'denda_nominal' => 25000,
            'jenis_denda' => 'nominal_tetap',
            'is_active' => true,
        ]);

        AturanKeterlambatan::create([
            'nama_aturan' => 'Rule 16-30',
            'menit_dari' => 16,
            'menit_sampai' => 30,
            'denda_nominal' => 50000,
            'jenis_denda' => 'nominal_tetap',
            'is_active' => true,
        ]);

        // Test finding rule for 10 minutes late
        $rule = AturanKeterlambatan::where('is_active', true)
            ->where('menit_dari', '<=', 10)
            ->where(function ($query) {
                $query->whereNull('menit_sampai')
                      ->orWhere('menit_sampai', '>=', 10);
            })
            ->orderBy('menit_dari', 'desc')
            ->first();

        $this->assertNotNull($rule);
        $this->assertEquals('Rule 1-15', $rule->nama_aturan);
        $this->assertEquals(25000, $rule->hitungDenda(10, 5000000));

        // Test finding rule for 25 minutes late
        $rule = AturanKeterlambatan::where('is_active', true)
            ->where('menit_dari', '<=', 25)
            ->where(function ($query) {
                $query->whereNull('menit_sampai')
                      ->orWhere('menit_sampai', '>=', 25);
            })
            ->orderBy('menit_dari', 'desc')
            ->first();

        $this->assertNotNull($rule);
        $this->assertEquals('Rule 16-30', $rule->nama_aturan);
        $this->assertEquals(50000, $rule->hitungDenda(25, 5000000));
    }

    /** @test */
    public function it_returns_zero_for_no_applicable_rule()
    {
        // Create rule for 10-20 minutes
        AturanKeterlambatan::create([
            'nama_aturan' => 'Rule 10-20',
            'menit_dari' => 10,
            'menit_sampai' => 20,
            'denda_nominal' => 50000,
            'jenis_denda' => 'nominal_tetap',
            'is_active' => true,
        ]);

        // Test for 5 minutes (below range)
        $rule = AturanKeterlambatan::where('is_active', true)
            ->where('menit_dari', '<=', 5)
            ->where(function ($query) {
                $query->whereNull('menit_sampai')
                      ->orWhere('menit_sampai', '>=', 5);
            })
            ->first();

        $this->assertNull($rule);

        // Test for 25 minutes (above range)
        $rule = AturanKeterlambatan::where('is_active', true)
            ->where('menit_dari', '<=', 25)
            ->where(function ($query) {
                $query->whereNull('menit_sampai')
                      ->orWhere('menit_sampai', '>=', 25);
            })
            ->first();

        $this->assertNull($rule);
    }

    /** @test */
    public function it_calculates_per_minute_penalty()
    {
        $aturan = AturanKeterlambatan::create([
            'nama_aturan' => 'Per Minute Rule',
            'menit_dari' => 1,
            'menit_sampai' => null,
            'denda_per_menit' => 2000,
            'jenis_denda' => 'per_menit',
            'is_active' => true,
        ]);

        $estimasiPotongan = $aturan->hitungDenda(30, 5000000);
        $this->assertEquals(60000, $estimasiPotongan); // 30 * 2000
    }

    /** @test */
    public function it_calculates_percentage_penalty()
    {
        $aturan = AturanKeterlambatan::create([
            'nama_aturan' => 'Percentage Rule',
            'menit_dari' => 1,
            'menit_sampai' => null,
            'persentase_denda' => 1.0,
            'jenis_denda' => 'persentase_gaji',
            'is_active' => true,
        ]);

        $estimasiPotongan = $aturan->hitungDenda(30, 5000000);
        $this->assertEquals(50000, $estimasiPotongan); // 1% of 5,000,000
    }
}
