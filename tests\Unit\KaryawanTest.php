<?php

namespace Tests\Unit;

use App\Models\Karyawan;
use App\Models\User;
use App\Models\Divisi;
use App\Models\Jabatan;
use App\Models\Entitas;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class KaryawanTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function karyawan_belongs_to_user()
    {
        // Buat user
        $user = User::factory()->create([
            'role' => 'karyawan'
        ]);

        // Buat entitas, divisi, dan jabatan
        $entitas = Entitas::create([
            'nama' => 'PT Test',
            'alamat' => 'Jl. Test',
            'telepon' => '08123456789',
            'email' => '<EMAIL>',
        ]);

        $divisi = Divisi::create([
            'nama_divisi' => 'Divisi Test',
            'kode_divisi' => 'DIV-TEST',
            'departemen_id' => 1,
        ]);

        $jabatan = Jabatan::create([
            'nama_jabatan' => 'Jabatan Test',
            'kode_jabatan' => 'JAB-TEST',
            'level' => 1,
        ]);

        // Buat karyawan
        $karyawan = Karyawan::create([
            'id_user' => $user->id,
            'nip' => 'K001',
            'nama_lengkap' => 'Karyawan Test',
            'jenis_kelamin' => 'L',
            'tempat_lahir' => 'Jakarta',
            'tanggal_lahir' => '1990-01-01',
            'alamat' => 'Jl. Test No. 123',
            'nomor_telepon' => '08123456789',
            'email' => '<EMAIL>',
            'tanggal_masuk' => '2023-01-01',
            'status_aktif' => 1,
            'entitas_id' => $entitas->id,
            'divisi_id' => $divisi->id,
            'jabatan_id' => $jabatan->id,
        ]);

        // Verifikasi relasi
        $this->assertEquals($user->id, $karyawan->user->id);
        $this->assertEquals($karyawan->id, $user->karyawan->id);
    }

    /** @test */
    public function karyawan_has_correct_relations()
    {
        // Buat user
        $user = User::factory()->create([
            'role' => 'karyawan'
        ]);

        // Buat entitas, divisi, dan jabatan
        $entitas = Entitas::create([
            'nama' => 'PT Test',
            'alamat' => 'Jl. Test',
            'telepon' => '08123456789',
            'email' => '<EMAIL>',
        ]);

        $divisi = Divisi::create([
            'nama_divisi' => 'Divisi Test',
            'kode_divisi' => 'DIV-TEST',
            'departemen_id' => 1,
        ]);

        $jabatan = Jabatan::create([
            'nama_jabatan' => 'Jabatan Test',
            'kode_jabatan' => 'JAB-TEST',
            'level' => 1,
        ]);

        // Buat karyawan
        $karyawan = Karyawan::create([
            'id_user' => $user->id,
            'nip' => 'K001',
            'nama_lengkap' => 'Karyawan Test',
            'jenis_kelamin' => 'L',
            'tempat_lahir' => 'Jakarta',
            'tanggal_lahir' => '1990-01-01',
            'alamat' => 'Jl. Test No. 123',
            'nomor_telepon' => '08123456789',
            'email' => '<EMAIL>',
            'tanggal_masuk' => '2023-01-01',
            'status_aktif' => 1,
            'entitas_id' => $entitas->id,
            'divisi_id' => $divisi->id,
            'jabatan_id' => $jabatan->id,
        ]);

        // Verifikasi relasi
        $this->assertEquals($entitas->id, $karyawan->entitas->id);
        $this->assertEquals($divisi->id, $karyawan->divisi->id);
        $this->assertEquals($jabatan->id, $karyawan->jabatan->id);
    }
}
