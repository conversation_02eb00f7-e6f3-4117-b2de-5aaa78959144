<?php

namespace Tests\Unit;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Notifications\DatabaseNotification;
use Tests\TestCase;
use Illuminate\Support\Str;

class NotificationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function database_notifications_can_be_created()
    {
        // Buat user
        $user = User::factory()->create();

        // Buat notifikasi
        $notification = [
            'id' => Str::uuid()->toString(),
            'type' => 'App\\Notifications\\TestNotification',
            'notifiable_type' => get_class($user),
            'notifiable_id' => $user->id,
            'data' => json_encode([
                'message' => 'Test notification',
                'action' => 'test',
            ]),
            'read_at' => null,
        ];

        // Simpan notifikasi ke database
        DatabaseNotification::create($notification);

        // Verifikasi notifikasi tersimpan
        $this->assertDatabaseHas('notifications', [
            'id' => $notification['id'],
            'notifiable_id' => $user->id,
        ]);

        // Verifikasi user memiliki notifikasi
        $this->assertEquals(1, $user->notifications()->count());
    }

    /** @test */
    public function notifications_can_be_marked_as_read()
    {
        // Buat user
        $user = User::factory()->create();

        // Buat notifikasi
        $notification = [
            'id' => Str::uuid()->toString(),
            'type' => 'App\\Notifications\\TestNotification',
            'notifiable_type' => get_class($user),
            'notifiable_id' => $user->id,
            'data' => json_encode([
                'message' => 'Test notification',
                'action' => 'test',
            ]),
            'read_at' => null,
        ];

        // Simpan notifikasi ke database
        $dbNotification = DatabaseNotification::create($notification);

        // Tandai notifikasi sebagai sudah dibaca
        $dbNotification->markAsRead();

        // Verifikasi notifikasi sudah dibaca
        $this->assertNotNull($dbNotification->fresh()->read_at);
    }
}
