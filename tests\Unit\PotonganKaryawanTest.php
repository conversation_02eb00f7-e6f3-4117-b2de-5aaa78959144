<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\PotonganKaryawan;
use App\Models\Karyawan;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class PotonganKaryawanTest extends TestCase
{
    use DatabaseTransactions;

    protected $karyawan;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create();

        // Create test karyawan
        $this->karyawan = Karyawan::factory()->create();

        // Set authenticated user
        $this->actingAs($this->user);
    }

    public function test_can_create_potongan_karyawan()
    {
        $potonganData = [
            'karyawan_id' => $this->karyawan->id,
            'jenis_potongan' => 'kasir',
            'nominal' => 50000,
            'bulan_potongan' => '2025-08',
            'keterangan' => 'Potongan kasir bulan Agustus',
        ];

        $potongan = PotonganKaryawan::create($potonganData);

        $this->assertInstanceOf(PotonganKaryawan::class, $potongan);
        $this->assertEquals($potonganData['karyawan_id'], $potongan->karyawan_id);
        $this->assertEquals($potonganData['jenis_potongan'], $potongan->jenis_potongan);
        $this->assertEquals($potonganData['nominal'], $potongan->nominal);
        $this->assertEquals($potonganData['bulan_potongan'], $potongan->bulan_potongan);
        $this->assertEquals($this->user->id, $potongan->created_by);
    }

    public function test_formatted_nominal_accessor()
    {
        $potongan = PotonganKaryawan::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_potongan' => 'stok_opname',
            'nominal' => 75000,
            'bulan_potongan' => '2025-08',
        ]);

        $this->assertEquals('Rp 75.000', $potongan->formatted_nominal);
    }

    public function test_jenis_label_accessor()
    {
        $testCases = [
            'kasir' => 'Potongan Kasir',
            'stok_opname' => 'Potongan Stok Opname',
            'retur' => 'Potongan Retur',
            'kasbon' => 'Potongan Kasbon',
        ];

        foreach ($testCases as $jenis => $expectedLabel) {
            $potongan = PotonganKaryawan::create([
                'karyawan_id' => $this->karyawan->id,
                'jenis_potongan' => $jenis,
                'nominal' => 50000,
                'bulan_potongan' => '2025-08',
            ]);

            $this->assertEquals($expectedLabel, $potongan->jenis_label);
        }
    }

    public function test_formatted_bulan_accessor()
    {
        $potongan = PotonganKaryawan::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_potongan' => 'kasir',
            'nominal' => 50000,
            'bulan_potongan' => '2025-08',
        ]);

        $this->assertEquals('Agustus 2025', $potongan->formatted_bulan);
    }

    public function test_scope_by_jenis()
    {
        // Create different types of potongan
        PotonganKaryawan::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_potongan' => 'kasir',
            'nominal' => 50000,
            'bulan_potongan' => '2025-08',
        ]);

        PotonganKaryawan::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_potongan' => 'retur',
            'nominal' => 30000,
            'bulan_potongan' => '2025-08',
        ]);

        $kasirPotongan = PotonganKaryawan::kasir()->get();
        $returPotongan = PotonganKaryawan::retur()->get();

        $this->assertCount(1, $kasirPotongan);
        $this->assertCount(1, $returPotongan);
        $this->assertEquals('kasir', $kasirPotongan->first()->jenis_potongan);
        $this->assertEquals('retur', $returPotongan->first()->jenis_potongan);
    }

    public function test_scope_by_bulan()
    {
        // Create potongan for different months
        PotonganKaryawan::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_potongan' => 'kasir',
            'nominal' => 50000,
            'bulan_potongan' => '2025-08',
        ]);

        PotonganKaryawan::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_potongan' => 'kasir',
            'nominal' => 60000,
            'bulan_potongan' => '2025-09',
        ]);

        $agustusPotongan = PotonganKaryawan::byBulan('2025-08')->get();
        $septemberPotongan = PotonganKaryawan::byBulan('2025-09')->get();

        $this->assertCount(1, $agustusPotongan);
        $this->assertCount(1, $septemberPotongan);
        $this->assertEquals('2025-08', $agustusPotongan->first()->bulan_potongan);
        $this->assertEquals('2025-09', $septemberPotongan->first()->bulan_potongan);
    }

    public function test_karyawan_relationship()
    {
        $potongan = PotonganKaryawan::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_potongan' => 'kasir',
            'nominal' => 50000,
            'bulan_potongan' => '2025-08',
        ]);

        $this->assertInstanceOf(Karyawan::class, $potongan->karyawan);
        $this->assertEquals($this->karyawan->id, $potongan->karyawan->id);
    }

    public function test_creator_relationship()
    {
        $potongan = PotonganKaryawan::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_potongan' => 'kasir',
            'nominal' => 50000,
            'bulan_potongan' => '2025-08',
        ]);

        $this->assertInstanceOf(User::class, $potongan->creator);
        $this->assertEquals($this->user->id, $potongan->creator->id);
    }
}
