<?php

namespace Tests\Unit;

use App\Console\Commands\ProcessApprovedLeave;
use App\Models\User;
use App\Models\Karyawan;
use App\Models\CutiIzin;
use App\Models\Absensi;
use App\Models\Schedule;
use App\Models\Shift;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProcessApprovedLeaveCommandTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Karyawan $karyawan;
    private Shift $shift;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user and employee
        $this->user = User::factory()->create([
            'name' => 'Test Manager',
            'email' => '<EMAIL>',
            'role' => 'manager_hrd'
        ]);

        $this->karyawan = Karyawan::factory()->create([
            'id_user' => $this->user->id,
            'nama_lengkap' => 'Test Employee',
            'nip' => 'EMP001'
        ]);

        // Create test shift
        $this->shift = Shift::factory()->create([
            'nama_shift' => 'Regular Shift',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'toleransi_keterlambatan' => 15,
            'is_split_shift' => false
        ]);

        // Create schedules for the next 10 days
        for ($i = 0; $i < 10; $i++) {
            Schedule::factory()->create([
                'karyawan_id' => $this->karyawan->id,
                'shift_id' => $this->shift->id,
                'entitas_id' => $this->karyawan->id_entitas,
                'tanggal_jadwal' => Carbon::today()->addDays($i)
            ]);
        }
    }

    /** @test */
    public function it_processes_approved_leave_requests()
    {
        // Create approved leave request
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today()->addDays(2),
            'jumlah_hari' => 3,
            'alasan' => 'Personal leave',
            'status' => 'approved',
            'approved_by' => $this->user->id,
            'approved_at' => now()
        ]);

        // Run command in dry-run mode
        $this->artisan('attendance:process-approved-leave', [
            '--date' => Carbon::today()->format('Y-m-d'),
            '--dry-run' => true
        ])
            ->expectsOutput('🔍 DRY RUN MODE - No changes will be made')
            ->assertExitCode(0);

        // No attendance records should be created in dry-run
        $attendanceCount = Absensi::where('karyawan_id', $this->karyawan->id)->count();
        $this->assertEquals(0, $attendanceCount);
    }

    /** @test */
    public function it_creates_attendance_records_for_approved_leave()
    {
        // Create approved leave request
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today()->addDays(2),
            'jumlah_hari' => 3,
            'alasan' => 'Personal leave',
            'status' => 'approved',
            'approved_by' => $this->user->id,
            'approved_at' => now()
        ]);

        // Run command without dry-run
        $this->artisan('attendance:process-approved-leave', [
            '--date' => Carbon::today()->format('Y-m-d')
        ])
            ->assertExitCode(0);

        // Check that attendance records were created
        $attendanceRecords = Absensi::where('karyawan_id', $this->karyawan->id)
            ->whereBetween('tanggal_absensi', [
                Carbon::today()->format('Y-m-d'),
                Carbon::today()->addDays(2)->format('Y-m-d')
            ])
            ->get();

        $this->assertCount(3, $attendanceRecords);

        foreach ($attendanceRecords as $attendance) {
            $this->assertEquals('cuti', $attendance->status);
            $this->assertStringContainsString('Auto-generated from approved cuti', $attendance->keterangan);
        }
    }

    /** @test */
    public function it_processes_different_leave_types_correctly()
    {
        $leaveTypes = [
            'cuti' => 'cuti',
            'izin' => 'izin',
            'sakit' => 'sakit'
        ];

        $dayOffset = 5;
        foreach ($leaveTypes as $jenisPermohonan => $expectedStatus) {
            // Create approved leave request with different dates
            $leave = CutiIzin::create([
                'karyawan_id' => $this->karyawan->id,
                'jenis_permohonan' => $jenisPermohonan,
                'tanggal_mulai' => Carbon::today()->addDays($dayOffset),
                'tanggal_selesai' => Carbon::today()->addDays($dayOffset),
                'jumlah_hari' => 1,
                'alasan' => 'Test ' . $jenisPermohonan,
                'status' => 'approved',
                'approved_by' => $this->user->id,
                'approved_at' => now()
            ]);

            // Run command
            $this->artisan('attendance:process-approved-leave', [
                '--date' => Carbon::today()->addDays($dayOffset)->format('Y-m-d')
            ]);

            // Check attendance record
            $attendance = Absensi::where('karyawan_id', $this->karyawan->id)
                ->whereDate('tanggal_absensi', Carbon::today()->addDays($dayOffset))
                ->where('status', $expectedStatus)
                ->first();

            $this->assertNotNull($attendance, "Attendance record not found for {$jenisPermohonan} on day offset {$dayOffset}");
            $this->assertEquals($expectedStatus, $attendance->status);

            $dayOffset++; // Use different date for next iteration
        }
    }

    /** @test */
    public function it_updates_existing_attendance_with_generic_status()
    {
        // Create existing attendance with generic status
        $existingAttendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'tanggal_absensi' => Carbon::today(),
            'status' => 'hadir',
            'keterangan' => 'Manual entry'
        ]);

        // Create approved leave request
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today(),
            'jumlah_hari' => 1,
            'alasan' => 'Personal leave',
            'status' => 'approved',
            'approved_by' => $this->user->id,
            'approved_at' => now()
        ]);

        // Run command
        $this->artisan('attendance:process-approved-leave', [
            '--date' => Carbon::today()->format('Y-m-d')
        ]);

        // Check that existing record was updated
        $existingAttendance->refresh();
        $this->assertEquals('cuti', $existingAttendance->status);
        $this->assertStringContainsString('Auto-generated from approved cuti', $existingAttendance->keterangan);
    }

    /** @test */
    public function it_skips_existing_attendance_with_special_status()
    {
        // Create existing attendance with special status
        $existingAttendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'tanggal_absensi' => Carbon::today(),
            'status' => 'izin',
            'keterangan' => 'Previous leave'
        ]);

        // Create approved leave request
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today(),
            'jumlah_hari' => 1,
            'alasan' => 'Personal leave',
            'status' => 'approved',
            'approved_by' => $this->user->id,
            'approved_at' => now()
        ]);

        // Run command
        $this->artisan('attendance:process-approved-leave', [
            '--date' => Carbon::today()->format('Y-m-d')
        ]);

        // Check that existing record was NOT updated
        $existingAttendance->refresh();
        $this->assertEquals('izin', $existingAttendance->status);
        $this->assertEquals('Previous leave', $existingAttendance->keterangan);
    }

    /** @test */
    public function it_processes_leave_spanning_multiple_days()
    {
        // Create approved leave request for 5 days
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today()->addDays(4),
            'jumlah_hari' => 5,
            'alasan' => 'Long vacation',
            'status' => 'approved',
            'approved_by' => $this->user->id,
            'approved_at' => now()
        ]);

        // Run command for date range
        $this->artisan('attendance:process-approved-leave', [
            '--days' => 10
        ]);

        // Check that 5 attendance records were created
        $attendanceRecords = Absensi::where('karyawan_id', $this->karyawan->id)
            ->whereBetween('tanggal_absensi', [
                Carbon::today()->format('Y-m-d'),
                Carbon::today()->addDays(4)->format('Y-m-d')
            ])
            ->get();

        $this->assertCount(5, $attendanceRecords);

        foreach ($attendanceRecords as $attendance) {
            $this->assertEquals('cuti', $attendance->status);
        }
    }

    /** @test */
    public function it_ignores_pending_leave_requests()
    {
        // Create pending leave request
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today(),
            'jumlah_hari' => 1,
            'alasan' => 'Personal leave',
            'status' => 'pending' // Not approved
        ]);

        // Run command
        $this->artisan('attendance:process-approved-leave', [
            '--date' => Carbon::today()->format('Y-m-d')
        ]);

        // Check that no attendance records were created
        $attendanceCount = Absensi::where('karyawan_id', $this->karyawan->id)->count();
        $this->assertEquals(0, $attendanceCount);
    }

    /** @test */
    public function it_ignores_rejected_leave_requests()
    {
        // Create rejected leave request
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today(),
            'jumlah_hari' => 1,
            'alasan' => 'Personal leave',
            'status' => 'rejected',
            'approved_by' => $this->user->id,
            'approved_at' => now(),
            'rejection_reason' => 'Not approved'
        ]);

        // Run command
        $this->artisan('attendance:process-approved-leave', [
            '--date' => Carbon::today()->format('Y-m-d')
        ]);

        // Check that no attendance records were created
        $attendanceCount = Absensi::where('karyawan_id', $this->karyawan->id)->count();
        $this->assertEquals(0, $attendanceCount);
    }

    /** @test */
    public function it_links_attendance_to_schedule_when_available()
    {
        // Create approved leave request
        $leave = CutiIzin::create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today(),
            'jumlah_hari' => 1,
            'alasan' => 'Personal leave',
            'status' => 'approved',
            'approved_by' => $this->user->id,
            'approved_at' => now()
        ]);

        // Run command
        $this->artisan('attendance:process-approved-leave', [
            '--date' => Carbon::today()->format('Y-m-d')
        ]);

        // Check that attendance record is linked to schedule
        $attendance = Absensi::where('karyawan_id', $this->karyawan->id)
            ->whereDate('tanggal_absensi', Carbon::today())
            ->first();

        $this->assertNotNull($attendance);
        $this->assertNotNull($attendance->jadwal_id);

        $schedule = Schedule::where('karyawan_id', $this->karyawan->id)
            ->whereDate('tanggal_jadwal', Carbon::today())
            ->first();

        $this->assertEquals($schedule->id, $attendance->jadwal_id);
    }
}
