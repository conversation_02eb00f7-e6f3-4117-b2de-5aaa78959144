<?php

namespace Tests\Unit;

use App\Console\Commands\UpdateJadwalEntitas;
use App\Models\User;
use App\Models\Karyawan;
use App\Models\Schedule;
use App\Models\Shift;
use App\Models\Entitas;
use App\Models\JadwalMasal;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class UpdateJadwalEntitasCommandTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Karyawan $karyawan1;
    private Karyawan $karyawan2;
    private Entitas $entitas1;
    private Entitas $entitas2;
    private Shift $shift;

    protected function setUp(): void
    {
        parent::setUp();

        // Disable observers to avoid role assignment issues in tests
        \App\Models\Karyawan::unsetEventDispatcher();

        // Create test entitas
        $this->entitas1 = Entitas::factory()->create([
            'nama' => 'Toko A',
            'alamat' => 'Jl. A No. 1'
        ]);

        $this->entitas2 = Entitas::factory()->create([
            'nama' => 'Toko B',
            'alamat' => 'Jl. B No. 2'
        ]);

        // Create test user
        $this->user = User::factory()->create([
            'name' => 'Test Manager',
            'email' => '<EMAIL>',
            'role' => 'manager_hrd'
        ]);

        // Create test employees
        $this->karyawan1 = Karyawan::factory()->create([
            'nama_lengkap' => 'Employee 1',
            'nip' => 'EMP001',
            'id_entitas' => $this->entitas1->id
        ]);

        $this->karyawan2 = Karyawan::factory()->create([
            'nama_lengkap' => 'Employee 2',
            'nip' => 'EMP002',
            'id_entitas' => $this->entitas2->id
        ]);

        // Create test shift
        $this->shift = Shift::factory()->create([
            'nama_shift' => 'Regular Shift',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00'
        ]);
    }

    /** @test */
    public function it_shows_current_statistics_correctly()
    {
        // Create schedules without entitas_id
        Schedule::create([
            'karyawan_id' => $this->karyawan1->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'entitas_id' => null
        ]);

        Schedule::create([
            'karyawan_id' => $this->karyawan2->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'entitas_id' => $this->entitas1->id // Already has entitas
        ]);

        // Create jadwal masal without entitas_id
        JadwalMasal::create([
            'nama_jadwal' => 'Test Jadwal',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today()->addDays(6),
            'shift_id' => $this->shift->id,
            'created_by' => $this->user->id,
            'entitas_id' => null
        ]);

        // Run command in dry-run mode
        $this->artisan('jadwal:update-entitas', ['--dry-run' => true])
            ->expectsOutput('🔍 DRY RUN MODE - No changes will be made')
            ->expectsOutputToContain('📊 Current Statistics:')
            ->expectsOutputToContain('📅 Jadwal Kerja:')
            ->expectsOutputToContain('Total: 2')
            ->expectsOutputToContain('With entitas_id: 1')
            ->expectsOutputToContain('📋 Jadwal Masal:')
            ->expectsOutputToContain('Total: 1')
            ->expectsOutputToContain('🏢 Available Entitas: 2')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_updates_jadwal_kerja_with_karyawan_entitas()
    {
        // Create schedule without entitas_id
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan1->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'entitas_id' => null
        ]);

        // Run command
        $this->artisan('jadwal:update-entitas', ['--force' => true])
            ->expectsOutput('🚀 Starting Jadwal Entitas Update...')
            ->expectsOutputToContain('📅 Updating jadwal_kerja with entitas_id...')
            ->assertExitCode(0);

        // Check that schedule was updated
        $schedule->refresh();
        $this->assertEquals($this->entitas1->id, $schedule->entitas_id);
    }

    /** @test */
    public function it_updates_jadwal_masal_based_on_assigned_karyawan()
    {
        // Create jadwal masal without entitas_id
        $jadwalMasal = JadwalMasal::create([
            'nama_jadwal' => 'Test Jadwal',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today()->addDays(6),
            'shift_id' => $this->shift->id,
            'created_by' => $this->user->id,
            'entitas_id' => null
        ]);

        // Assign karyawan to jadwal masal
        DB::table('jadwal_masal_karyawan')->insert([
            'jadwal_masal_id' => $jadwalMasal->id,
            'karyawan_id' => $this->karyawan1->id,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        DB::table('jadwal_masal_karyawan')->insert([
            'jadwal_masal_id' => $jadwalMasal->id,
            'karyawan_id' => $this->karyawan2->id,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Run command
        $this->artisan('jadwal:update-entitas', ['--force' => true])
            ->assertExitCode(0);

        // Check that jadwal masal was updated (should use most common entitas)
        $jadwalMasal->refresh();
        $this->assertNotNull($jadwalMasal->entitas_id);
        $this->assertContains($jadwalMasal->entitas_id, [$this->entitas1->id, $this->entitas2->id]);
    }

    /** @test */
    public function it_handles_karyawan_without_entitas()
    {
        // Create karyawan without entitas
        $karyawanNoEntitas = Karyawan::factory()->create([
            'nama_lengkap' => 'Employee No Entitas',
            'nip' => 'EMP003',
            'id_entitas' => null
        ]);

        // Create schedule for this karyawan
        $schedule = Schedule::create([
            'karyawan_id' => $karyawanNoEntitas->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'entitas_id' => null
        ]);

        // Run command
        $this->artisan('jadwal:update-entitas', ['--force' => true])
            ->expectsOutputToContain('📅 Updating jadwal_kerja with entitas_id...')
            ->assertExitCode(0);

        // Should assign to first available entitas
        $schedule->refresh();
        $this->assertEquals($this->entitas1->id, $schedule->entitas_id);
    }

    /** @test */
    public function it_handles_orphaned_schedules()
    {
        // Skip this test since foreign key constraints prevent orphaned schedules
        // In real application, this would be handled by database constraints
        $this->markTestSkipped('Foreign key constraints prevent orphaned schedules in test environment');
    }

    /** @test */
    public function it_skips_schedules_that_already_have_entitas()
    {
        // Create schedule with entitas_id already set
        $schedule = Schedule::create([
            'karyawan_id' => $this->karyawan1->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'entitas_id' => $this->entitas2->id // Already set to different entitas
        ]);

        $originalEntitasId = $schedule->entitas_id;

        // Run command without force, expect confirmation and answer yes
        $this->artisan('jadwal:update-entitas')
            ->expectsConfirmation('Do you want to proceed with updating entitas_id?', 'yes')
            ->expectsOutputToContain('✓ All jadwal_kerja records already have entitas_id set')
            ->assertExitCode(0);

        // Should not change existing entitas_id
        $schedule->refresh();
        $this->assertEquals($originalEntitasId, $schedule->entitas_id);
    }

    /** @test */
    public function it_shows_final_statistics()
    {
        // Create some schedules
        Schedule::create([
            'karyawan_id' => $this->karyawan1->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'entitas_id' => null
        ]);

        Schedule::create([
            'karyawan_id' => $this->karyawan2->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::tomorrow(),
            'entitas_id' => null
        ]);

        // Run command
        $this->artisan('jadwal:update-entitas', ['--force' => true])
            ->expectsOutputToContain('📊 Final Statistics:')
            ->expectsOutputToContain('📅 Jadwal Kerja: 2/2 have entitas_id')
            ->expectsOutputToContain('🏢 Schedule distribution by entitas:')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_requires_migration_to_be_run_first()
    {
        // This test would need to be run on a database without the migration
        // For now, we'll just verify the command checks for the column
        $this->artisan('jadwal:update-entitas', ['--dry-run' => true])
            ->assertExitCode(0); // Should succeed if migration is run
    }

    /** @test */
    public function it_simulates_dry_run_correctly()
    {
        // Create schedules without entitas_id
        Schedule::create([
            'karyawan_id' => $this->karyawan1->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => Carbon::today(),
            'entitas_id' => null
        ]);

        JadwalMasal::create([
            'nama_jadwal' => 'Test Jadwal',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today()->addDays(6),
            'shift_id' => $this->shift->id,
            'created_by' => $this->user->id,
            'entitas_id' => null
        ]);

        // Run dry-run
        $this->artisan('jadwal:update-entitas', ['--dry-run' => true])
            ->expectsOutput('🔍 Simulating changes...')
            ->expectsOutputToContain('📅 Would update 1 jadwal_kerja records')
            ->expectsOutputToContain('📋 Would update 1 jadwal_masal records')
            ->expectsOutputToContain('🏢 Available entitas for assignment:')
            ->expectsOutputToContain('- Toko A (ID: ' . $this->entitas1->id . ')')
            ->expectsOutputToContain('- Toko B (ID: ' . $this->entitas2->id . ')')
            ->assertExitCode(0);

        // Verify no changes were made
        $schedule = Schedule::first();
        $jadwalMasal = JadwalMasal::first();

        $this->assertNull($schedule->entitas_id);
        $this->assertNull($jadwalMasal->entitas_id);
    }
}
