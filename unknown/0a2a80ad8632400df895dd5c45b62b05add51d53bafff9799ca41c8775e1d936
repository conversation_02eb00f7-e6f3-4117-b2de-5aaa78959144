<div class="space-y-4">
    <!-- Transaction Header -->
    <div class="bg-gray-50 rounded-lg p-4">
        <div class="grid grid-cols-2 gap-4">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">{{ $record->transaction_number }}</h3>
                <p class="text-sm text-gray-600">{{ $record->transaction_date->format('d/m/Y H:i:s') }}</p>
            </div>
            <div class="text-right">
                <p class="text-xl font-bold text-green-600">Rp {{ number_format($record->net_amount, 0, ',', '.') }}</p>
                <p class="text-sm text-gray-600">{{ $record->payment_method_label }}</p>
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Produk</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kategori</th>
                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Harga Satuan</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Diskon</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @forelse($record->posTransactionItems as $item)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900">{{ $item->product->name }}</div>
                            @if($item->product->sku)
                                <div class="text-xs text-gray-500">SKU: {{ $item->product->sku }}</div>
                            @endif
                        </td>
                        <td class="px-6 py-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ $item->product->category->name ?? 'N/A' }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-center">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {{ $item->quantity }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-right text-sm text-gray-900">
                            Rp {{ number_format($item->unit_price, 0, ',', '.') }}
                        </td>
                        <td class="px-6 py-4 text-right text-sm">
                            @if($item->discount_per_item > 0)
                                <span class="text-red-600">-Rp {{ number_format($item->discount_per_item, 0, ',', '.') }}</span>
                            @else
                                <span class="text-gray-400">-</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 text-right text-sm font-medium text-gray-900">
                            Rp {{ number_format($item->total_price, 0, ',', '.') }}
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            Tidak ada item dalam transaksi ini
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Summary -->
    <div class="bg-gray-50 rounded-lg p-4">
        <div class="grid grid-cols-2 gap-4">
            <div>
                <h4 class="font-medium text-gray-900 mb-2">Ringkasan Item</h4>
                <div class="space-y-1 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total Item:</span>
                        <span class="font-medium">{{ $record->posTransactionItems->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total Quantity:</span>
                        <span class="font-medium">{{ $record->posTransactionItems->sum('quantity') }}</span>
                    </div>
                    @if($record->posTransactionItems->sum('discount_per_item') > 0)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Total Diskon Item:</span>
                            <span class="font-medium text-red-600">-Rp {{ number_format($record->posTransactionItems->sum('total_discount'), 0, ',', '.') }}</span>
                        </div>
                    @endif
                </div>
            </div>
            
            <div>
                <h4 class="font-medium text-gray-900 mb-2">Total Transaksi</h4>
                <div class="space-y-1 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Sub Total:</span>
                        <span class="font-medium">Rp {{ number_format($record->total_amount, 0, ',', '.') }}</span>
                    </div>
                    @if($record->discount_amount > 0)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Diskon Transaksi:</span>
                            <span class="font-medium text-red-600">-Rp {{ number_format($record->discount_amount, 0, ',', '.') }}</span>
                        </div>
                    @endif
                    @if($record->tax_amount > 0)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Pajak:</span>
                            <span class="font-medium">Rp {{ number_format($record->tax_amount, 0, ',', '.') }}</span>
                        </div>
                    @endif
                    <hr class="my-2">
                    <div class="flex justify-between">
                        <span class="text-base font-medium text-gray-900">Total Bersih:</span>
                        <span class="text-base font-bold text-green-600">Rp {{ number_format($record->net_amount, 0, ',', '.') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Categories Summary -->
    @if($record->posTransactionItems->isNotEmpty())
        <div class="bg-blue-50 rounded-lg p-4">
            <h4 class="font-medium text-gray-900 mb-3">Kategori Produk</h4>
            <div class="flex flex-wrap gap-2">
                @foreach($record->posTransactionItems->groupBy('product.category.name') as $categoryName => $items)
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        {{ $categoryName ?? 'Tanpa Kategori' }}
                        <span class="ml-1 text-xs">({{ $items->count() }} item)</span>
                    </span>
                @endforeach
            </div>
        </div>
    @endif
</div>
