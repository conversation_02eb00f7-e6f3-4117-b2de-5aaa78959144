# Manager Role Implementation

Dokumentasi implementasi role "manager" baru dalam sistem HRD.

## 🎯 Tujuan Implementasi

Menambahkan role "manager" sebagai level akses baru antara supervisor dan admin dengan permissions yang sesuai untuk:
1. **Manajemen karyawan** di level departemen/divisi
2. **Approval attendance** dan leave requests
3. **Akses panel admin** dengan batasan tertentu
4. **Monitoring dan reporting** untuk area yang dikelola

## 📋 Fitur yang Diimplementasikan

### 1. Enum UserRole Update

**File:** `app/Enums/UserRole.php`

**Perubahan:**
- ✅ Menambahkan `case MANAGER = 'manager'`
- ✅ Update method `label()` untuk include "Manager"
- ✅ Update permissions methods:
  - `canAccessAdmin()` - Manager dapat akses admin panel
  - `canManageEmployees()` - Manager dapat manage karyawan
  - `canApproveAttendance()` - Manager dapat approve attendance

**Hierarchy Role:**
```
ADMIN (highest) → SUPERVISOR → MANAGER → KARYAWAN (lowest)
```

### 2. Database Migration

**File:** `database/migrations/2025_07_11_104008_add_manager_role_to_users_table.php`

**Fitur:**
- ✅ Update comment field role untuk include "manager"
- ✅ Auto-update existing users dengan pattern email/nama manager
- ✅ Rollback support untuk convert manager → supervisor

**SQL Changes:**
```sql
-- Update role column comment
ALTER TABLE users MODIFY COLUMN role VARCHAR(50) DEFAULT 'karyawan' 
COMMENT 'User role: admin, supervisor, manager, karyawan';

-- Auto-update users with manager patterns
UPDATE users SET role = 'manager' 
WHERE (email LIKE '%manager%' OR name LIKE '%Manager%') 
AND role != 'admin';
```

### 3. Seeder Implementation

**File:** `database/seeders/ManagerRoleSeeder.php`

**Fitur:**
- ✅ Create default manager users
- ✅ Add manager role to reference table (jika ada)
- ✅ Prevent duplicate creation
- ✅ Update existing users to manager role

**Default Manager Users:**
1. **Manager HRD** - `<EMAIL>`
2. **Manager Operasional** - `<EMAIL>`
3. **Manager Keuangan** - `<EMAIL>`
4. **Manager IT** - `<EMAIL>`

**Default Password:** `password123`

### 4. UI Updates

**File:** `app/Filament/Resources/UserResource.php`

**Perubahan:**
- ✅ Badge color untuk role manager: `info` (biru)
- ✅ Dropdown options include "manager"

**Color Scheme:**
- 🔴 **Admin**: `danger` (red)
- 🟡 **Supervisor**: `warning` (yellow)
- 🔵 **Manager**: `info` (blue)
- 🟢 **Karyawan**: `success` (green)

## 🚀 Cara Menjalankan

### 1. Jalankan Migration
```bash
php artisan migrate --path=database/migrations/2025_07_11_104008_add_manager_role_to_users_table.php
```

### 2. Jalankan Seeder
```bash
php artisan db:seed --class=ManagerRoleSeeder
```

### 3. Verifikasi Implementation
```bash
# Check users table
php artisan tinker
>>> App\Models\User::where('role', 'manager')->get()

# Check enum
>>> App\Enums\UserRole::MANAGER->value
>>> App\Enums\UserRole::MANAGER->label()
```

## 🔐 Permissions & Access Control

### Manager Role Permissions

**✅ Dapat Akses:**
- Admin panel (Filament)
- Manage karyawan di departemen/divisi
- Approve attendance requests
- Approve leave requests
- View reports dan analytics
- Manage jadwal kerja
- Access absensi management

**❌ Tidak Dapat Akses:**
- System settings (admin only)
- User role management (admin only)
- Global configuration (admin only)
- Financial settings (admin only)

### Implementation di Code

```php
// Check if user can access admin panel
if (auth()->user()->role->canAccessAdmin()) {
    // Manager, Supervisor, Admin dapat akses
}

// Check if user can manage employees
if (auth()->user()->role->canManageEmployees()) {
    // Manager, Supervisor, Admin dapat manage
}

// Check if user can approve attendance
if (auth()->user()->role->canApproveAttendance()) {
    // Manager, Supervisor, Admin dapat approve
}
```

## 📊 Use Cases

### 1. Manager HRD
- Manage semua karyawan
- Approve cuti dan izin
- Generate laporan attendance
- Manage jadwal kerja masal

### 2. Manager Operasional
- Manage karyawan operasional
- Monitor attendance real-time
- Approve overtime requests
- Manage shift schedules

### 3. Manager Keuangan
- View payroll reports
- Approve expense claims
- Monitor tardiness penalties
- Generate financial reports

### 4. Manager IT
- Manage system users
- Monitor system logs
- Approve technical requests
- Manage digital assets

## 🧪 Testing

### Manual Testing

1. **Login sebagai Manager:**
   ```
   Email: <EMAIL>
   Password: password123
   ```

2. **Verifikasi Access:**
   - ✅ Dapat akses admin panel
   - ✅ Dapat melihat menu karyawan
   - ✅ Dapat approve attendance
   - ❌ Tidak dapat akses system settings

3. **Test Role Badge:**
   - Buka User Management
   - Verifikasi badge manager berwarna biru

### Automated Testing

```php
// Test enum functionality
public function test_manager_role_enum()
{
    $this->assertEquals('manager', UserRole::MANAGER->value);
    $this->assertEquals('Manager', UserRole::MANAGER->label());
    $this->assertTrue(UserRole::MANAGER->canAccessAdmin());
}

// Test user creation
public function test_manager_user_creation()
{
    $manager = User::factory()->create(['role' => 'manager']);
    $this->assertTrue($manager->role->canManageEmployees());
}
```

## 🔄 Migration & Rollback

### Forward Migration
```bash
php artisan migrate --path=database/migrations/2025_07_11_104008_add_manager_role_to_users_table.php
```

### Rollback (jika diperlukan)
```bash
php artisan migrate:rollback --path=database/migrations/2025_07_11_104008_add_manager_role_to_users_table.php
```

**Rollback Effect:**
- Semua user dengan role 'manager' akan diubah ke 'supervisor'
- Comment field role dikembalikan ke versi lama

## 📝 Catatan Implementasi

### 1. Backward Compatibility
- ✅ Tidak mengubah existing functionality
- ✅ Existing roles tetap berfungsi normal
- ✅ Database schema compatible

### 2. Security Considerations
- ✅ Manager role memiliki permissions terbatas
- ✅ Tidak dapat akses system-critical settings
- ✅ Audit trail untuk role changes

### 3. Performance Impact
- ✅ Minimal impact pada query performance
- ✅ Index pada role field sudah ada
- ✅ Enum caching berfungsi normal

## 🔮 Future Enhancements

1. **Role-based Menu Filtering**
   - Dynamic menu berdasarkan role
   - Hide/show menu items per role

2. **Department-specific Permissions**
   - Manager hanya manage karyawan di departemennya
   - Granular permissions per departemen

3. **Approval Workflow**
   - Multi-level approval dengan manager
   - Escalation rules untuk approval

4. **Dashboard Customization**
   - Role-specific dashboard widgets
   - Manager-specific KPIs dan metrics

## ✅ Checklist Implementasi

- [x] Update UserRole enum
- [x] Create database migration
- [x] Create seeder for manager users
- [x] Update UI badge colors
- [x] Test migration execution
- [x] Test seeder execution
- [x] Verify permissions functionality
- [x] Create documentation
- [ ] Create unit tests (optional)
- [ ] Update user manual (optional)

## 🎉 Kesimpulan

Role "manager" telah berhasil diimplementasikan dengan:
- ✅ **4 default manager users** telah dibuat
- ✅ **Permissions yang sesuai** untuk level manager
- ✅ **UI updates** dengan badge color yang tepat
- ✅ **Database migration** yang aman dan reversible
- ✅ **Backward compatibility** terjaga

Manager role sekarang siap digunakan dan dapat diassign ke users yang memerlukan akses level menengah antara supervisor dan admin.
