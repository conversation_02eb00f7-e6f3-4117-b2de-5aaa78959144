# 🕐 Fitur Lembur - Viera Filament

## 📋 Overview
Fitur lembur adalah sistem manajemen jam kerja lembur karyawan yang terintegrasi dengan sistem HRD Viera Filament. Fitur ini memungkinkan pencatatan, monitoring, dan analisis data lembur karyawan dengan interface yang user-friendly.

## ✨ Fitur Utama

### 🎯 Core Features
- ✅ **CRUD Lembur**: Create, Read, Update, Delete data lembur
- ✅ **Validasi Cerdas**: Validasi weekend, batas waktu, dan batas jam bulanan
- ✅ **Pencarian Canggih**: Filter berdasarkan karyawan, departemen, dan periode
- ✅ **Soft Delete**: Data tidak hilang permanen, dapat di-restore
- ✅ **Audit Trail**: Tracking siapa yang input/edit data

### 📊 Analytics & Reporting
- ✅ **Widget Statistik**: 4 widget utama di dashboard
- ✅ **Modal Statistik**: Detail statistik dengan top 5 karyawan
- ✅ **Total Jam per Karyawan**: Modal detail jam lembur per karyawan
- ✅ **Bulk Actions**: Hitung total jam dari multiple records

### 🔧 Advanced Features
- ✅ **Live Validation**: Real-time feedback saat input data
- ✅ **Smart Helper Text**: Informasi kontekstual di form
- ✅ **Responsive Design**: Tampilan optimal di semua device
- ✅ **Dark Mode Support**: Mendukung tema gelap

## 🚀 Quick Start

### 1. Akses Menu Lembur
```
Admin Panel → Jadwal & Absensi → Data Lembur
```

### 2. Tambah Data Lembur
1. Klik tombol **"Tambah Lembur"**
2. Pilih **Karyawan** (akan muncul info jam lembur bulan ini)
3. Pilih **Tanggal** (tidak boleh weekend atau >30 hari lalu)
4. Input **Jumlah Jam** (0.5 - 12 jam, max 40 jam/bulan)
5. Isi **Deskripsi** (opsional)
6. Klik **"Simpan"**

### 3. Lihat Statistik
- **Widget Dashboard**: Otomatis tampil di halaman list
- **Modal Statistik**: Klik tombol "Lihat Statistik"
- **Total Jam Karyawan**: Klik action "Total Jam" di tabel

## 📁 Struktur File

```
app/
├── Models/
│   └── Lembur.php                          # Model utama
├── Filament/Resources/
│   ├── LemburResource.php                  # Resource utama
│   ├── LemburResource/Pages/
│   │   ├── ListLemburs.php                 # Halaman list + widget
│   │   ├── CreateLembur.php                # Halaman create
│   │   ├── ViewLembur.php                  # Halaman view
│   │   └── EditLembur.php                  # Halaman edit
│   └── LemburResource/Widgets/
│       └── LemburStatsWidget.php           # Widget statistik

database/
├── migrations/
│   └── 2025_07_21_152316_create_lembur_table.php
└── seeders/
    └── LemburSeeder.php                    # Sample data

resources/views/filament/resources/lembur/
├── total-jam-modal.blade.php               # Modal total jam karyawan
└── statistik-modal.blade.php               # Modal statistik detail

docs/
├── fitur-lembur.md                         # Dokumentasi lengkap
└── README-LEMBUR.md                        # File ini
```

## 🎨 Screenshots & UI

### Dashboard Widget
- 4 card statistik dengan trend indicator
- Chart mini untuk visualisasi
- Color coding untuk status

### Form Input
- Live validation dengan feedback real-time
- Helper text yang informatif
- Dropdown dengan search dan preload

### Table View
- Sortable columns
- Advanced filters (karyawan, departemen, bulan)
- Bulk actions untuk multiple records
- Responsive design

## 🔒 Validasi & Business Rules

### Validasi Form
```php
// Tanggal
- Tidak boleh weekend
- Maksimal hari ini
- Tidak boleh >30 hari lalu

// Jumlah Jam
- Minimal 0.5 jam
- Maksimal 12 jam per hari
- Maksimal 40 jam per bulan per karyawan

// Karyawan
- Wajib dipilih
- Harus karyawan aktif
```

### Business Logic
- **Soft Delete**: Data tidak hilang permanen
- **Audit Trail**: Tracking created_by dan timestamps
- **Monthly Limit**: Warning jika >40 jam/bulan
- **Weekend Block**: Tidak boleh input di weekend

## 📊 Database Schema

```sql
CREATE TABLE `lembur` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `karyawan_id` bigint unsigned NOT NULL,
  `tanggal` date NOT NULL,
  `jumlah_jam` decimal(5,2) NOT NULL COMMENT 'Jumlah jam lembur',
  `deskripsi` text COMMENT 'Deskripsi pekerjaan lembur',
  `created_by` bigint unsigned NULL,
  `created_at` timestamp NULL,
  `updated_at` timestamp NULL,
  `deleted_at` timestamp NULL,
  PRIMARY KEY (`id`),
  KEY `idx_lembur_karyawan_tanggal` (`karyawan_id`,`tanggal`),
  KEY `idx_lembur_tanggal` (`tanggal`),
  CONSTRAINT `fk_lembur_karyawan` FOREIGN KEY (`karyawan_id`) REFERENCES `karyawan` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_lembur_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
);
```

## 🔧 Kustomisasi

### Mengubah Batas Jam Bulanan
```php
// Di LemburResource.php, method form()
->rule(function ($get) {
    return function ($attribute, $value, $fail) use ($get) {
        // Ubah 40 menjadi nilai yang diinginkan
        if ($totalJamBulanIni + $value > 40) {
            $fail('Melebihi batas 40 jam per bulan');
        }
    };
})
```

### Menambah Validasi Custom
```php
// Tambahkan di form component
->rule(function () {
    return function ($attribute, $value, $fail) {
        // Custom validation logic
        if (/* kondisi */) {
            $fail('Pesan error custom');
        }
    };
})
```

## 🐛 Troubleshooting

### Error: Table 'lembur' doesn't exist
```bash
php artisan migrate
```

### Error: Class 'LemburResource' not found
```bash
php artisan optimize:clear
composer dump-autoload
```

### Widget tidak muncul
Pastikan widget sudah didaftarkan di `ListLemburs.php`:
```php
protected function getHeaderWidgets(): array
{
    return [
        LemburStatsWidget::class,
    ];
}
```

## 🚀 Deployment

### Production Checklist
- [ ] Jalankan migrasi: `php artisan migrate --force`
- [ ] Clear cache: `php artisan optimize:clear`
- [ ] Set permissions untuk storage
- [ ] Backup database sebelum deploy
- [ ] Test semua fitur di staging

### Performance Tips
- Index database sudah optimal
- Gunakan eager loading untuk relasi
- Cache query yang sering digunakan
- Monitor slow queries

## 📞 Support

Jika ada pertanyaan atau issue:
1. Cek dokumentasi lengkap di `docs/fitur-lembur.md`
2. Lihat log error di `storage/logs/laravel.log`
3. Gunakan Laravel Debugbar untuk debugging
4. Contact: Developer Team

---

**Version**: 1.0.0  
**Last Updated**: {{ now()->format('d M Y') }}  
**Compatibility**: Laravel 10+, Filament 3+
