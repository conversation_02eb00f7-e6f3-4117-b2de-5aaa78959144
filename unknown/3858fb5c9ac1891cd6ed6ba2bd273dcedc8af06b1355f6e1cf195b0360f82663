<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\Karyawan;
use App\Models\Absensi;
use App\Models\Schedule;
use App\Models\Shift;
use App\Models\AturanKeterlambatan;
use App\Services\AttendanceService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AttendanceServiceTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Karyawan $karyawan;
    private Shift $regularShift;
    private Shift $splitShift;
    private Schedule $schedule;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user and employee
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'karyawan'
        ]);

        $this->karyawan = Karyawan::factory()->create([
            'id_user' => $this->user->id,
            'nama_lengkap' => 'Test Employee',
            'nip' => 'EMP001'
        ]);

        // Create regular shift (08:00-17:00, 15 minutes tolerance)
        $this->regularShift = Shift::factory()->create([
            'nama_shift' => 'Regular Shift',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'toleransi_keterlambatan' => 15,
            'is_split_shift' => false
        ]);

        // Create split shift
        $this->splitShift = Shift::factory()->create([
            'nama_shift' => 'Split Shift',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '12:00:00',
            'waktu_mulai_periode2' => '13:00:00',
            'waktu_selesai_periode2' => '17:00:00',
            'toleransi_keterlambatan' => 10,
            'toleransi_keterlambatan_periode2' => 15,
            'is_split_shift' => true
        ]);

        // Create test schedule for today
        $this->schedule = Schedule::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->regularShift->id,
            'entitas_id' => $this->karyawan->id_entitas,
            'tanggal_jadwal' => Carbon::today()
        ]);
    }

    /** @test */
    public function it_determines_on_time_status_for_regular_shift()
    {
        // Test exactly on time
        $status = AttendanceService::determineAttendanceStatus(
            $this->karyawan->id,
            Carbon::today()->setTime(8, 0) // 08:00
        );
        $this->assertEquals('hadir', $status);

        // Test within tolerance (5 minutes late)
        $status = AttendanceService::determineAttendanceStatus(
            $this->karyawan->id,
            Carbon::today()->setTime(8, 5) // 08:05
        );
        $this->assertEquals('hadir', $status);

        // Test at tolerance limit (15 minutes late)
        $status = AttendanceService::determineAttendanceStatus(
            $this->karyawan->id,
            Carbon::today()->setTime(8, 15) // 08:15
        );
        $this->assertEquals('hadir', $status);
    }

    /** @test */
    public function it_determines_late_status_for_regular_shift()
    {
        // Test beyond tolerance (16 minutes late)
        $status = AttendanceService::determineAttendanceStatus(
            $this->karyawan->id,
            Carbon::today()->setTime(8, 16) // 08:16
        );
        $this->assertEquals('terlambat', $status);

        // Test significantly late (30 minutes)
        $status = AttendanceService::determineAttendanceStatus(
            $this->karyawan->id,
            Carbon::today()->setTime(8, 30) // 08:30
        );
        $this->assertEquals('terlambat', $status);
    }

    /** @test */
    public function it_determines_status_for_split_shift()
    {
        // Update schedule to use split shift
        $this->schedule->update(['shift_id' => $this->splitShift->id]);

        // Test period 1 - on time
        $status = AttendanceService::determineAttendanceStatus(
            $this->karyawan->id,
            Carbon::today()->setTime(8, 5), // 08:05 (within 10 min tolerance)
            1
        );
        $this->assertEquals('hadir', $status);

        // Test period 1 - late
        $status = AttendanceService::determineAttendanceStatus(
            $this->karyawan->id,
            Carbon::today()->setTime(8, 15), // 08:15 (beyond 10 min tolerance)
            1
        );
        $this->assertEquals('terlambat', $status);

        // Test period 2 - on time
        $status = AttendanceService::determineAttendanceStatus(
            $this->karyawan->id,
            Carbon::today()->setTime(13, 10), // 13:10 (within 15 min tolerance)
            2
        );
        $this->assertEquals('hadir', $status);

        // Test period 2 - late
        $status = AttendanceService::determineAttendanceStatus(
            $this->karyawan->id,
            Carbon::today()->setTime(13, 20), // 13:20 (beyond 15 min tolerance)
            2
        );
        $this->assertEquals('terlambat', $status);
    }

    /** @test */
    public function it_calculates_lateness_minutes_correctly()
    {
        // Create attendance record with late entry
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $this->schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 25), // 25 minutes after start
            'status' => 'terlambat',
            'periode' => 1
        ]);

        $latenessMinutes = AttendanceService::calculateLatenessMinutes($attendance);

        // Should be 10 minutes late (25 minutes - 15 minutes tolerance)
        $this->assertEquals(10, $latenessMinutes);
    }

    /** @test */
    public function it_calculates_zero_lateness_for_on_time_attendance()
    {
        // Create on-time attendance record
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $this->schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 10), // Within tolerance
            'status' => 'hadir',
            'periode' => 1
        ]);

        $latenessMinutes = AttendanceService::calculateLatenessMinutes($attendance);
        $this->assertEquals(0, $latenessMinutes);
    }

    /** @test */
    public function it_calculates_lateness_deduction_with_fixed_amount_rule()
    {
        // Create fixed amount rule
        $rule = AturanKeterlambatan::create([
            'nama_aturan' => 'Fixed Amount Rule',
            'menit_dari' => 1,
            'menit_sampai' => 30,
            'jenis_denda' => 'nominal_tetap',
            'denda_nominal' => 50000,
            'is_active' => true
        ]);

        // Create late attendance
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $this->schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 25),
            'status' => 'terlambat',
            'periode' => 1
        ]);

        $deductionData = AttendanceService::calculateLatenessDeduction($attendance);

        $this->assertEquals(10, $deductionData['minutes_late']);
        $this->assertEquals(50000, $deductionData['deduction_amount']);
        $this->assertNotNull($deductionData['applicable_rule']);
        $this->assertEquals($rule->id, $deductionData['applicable_rule']->id);
    }

    /** @test */
    public function it_calculates_lateness_deduction_with_per_minute_rule()
    {
        // Create per minute rule
        $rule = AturanKeterlambatan::create([
            'nama_aturan' => 'Per Minute Rule',
            'menit_dari' => 1,
            'menit_sampai' => 60,
            'jenis_denda' => 'per_menit',
            'denda_per_menit' => 2000,
            'is_active' => true
        ]);

        // Create late attendance (20 minutes late after tolerance)
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $this->schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 35), // 35 min after start = 20 min late
            'status' => 'terlambat',
            'periode' => 1
        ]);

        $deductionData = AttendanceService::calculateLatenessDeduction($attendance);

        $this->assertEquals(20, $deductionData['minutes_late']);
        $this->assertEquals(40000, $deductionData['deduction_amount']); // 20 * 2000
        $this->assertEquals($rule->id, $deductionData['applicable_rule']->id);
    }

    /** @test */
    public function it_calculates_lateness_deduction_with_percentage_rule()
    {
        // Create percentage rule
        $rule = AturanKeterlambatan::create([
            'nama_aturan' => 'Percentage Rule',
            'menit_dari' => 1,
            'menit_sampai' => 30,
            'jenis_denda' => 'persentase_gaji',
            'persentase_denda' => 5, // 5%
            'is_active' => true
        ]);

        // Create late attendance
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $this->schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 25),
            'status' => 'terlambat',
            'periode' => 1
        ]);

        $gajiPokok = 5000000; // 5 million
        $deductionData = AttendanceService::calculateLatenessDeduction($attendance, $gajiPokok);

        $this->assertEquals(10, $deductionData['minutes_late']);
        $this->assertEquals(250000, $deductionData['deduction_amount']); // 5% of 5M
        $this->assertEquals($rule->id, $deductionData['applicable_rule']->id);
    }

    /** @test */
    public function it_returns_zero_deduction_for_on_time_attendance()
    {
        // Create on-time attendance
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $this->schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 10),
            'status' => 'hadir',
            'periode' => 1
        ]);

        $deductionData = AttendanceService::calculateLatenessDeduction($attendance);

        $this->assertEquals(0, $deductionData['minutes_late']);
        $this->assertEquals(0, $deductionData['deduction_amount']);
        $this->assertNull($deductionData['applicable_rule']);
    }

    /** @test */
    public function it_returns_default_status_when_no_schedule_exists()
    {
        // Delete the schedule
        $this->schedule->delete();

        $status = AttendanceService::determineAttendanceStatus(
            $this->karyawan->id,
            Carbon::today()->setTime(8, 30)
        );

        $this->assertEquals('hadir', $status); // Default status
    }

    /** @test */
    public function it_handles_attendance_without_schedule_in_lateness_calculation()
    {
        // Create attendance without schedule
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => null,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 30),
            'status' => 'hadir',
            'periode' => 1
        ]);

        $latenessMinutes = AttendanceService::calculateLatenessMinutes($attendance);
        $this->assertEquals(0, $latenessMinutes);
    }

    /** @test */
    public function it_processes_attendance_record_completely()
    {
        // Create late attendance
        $attendance = Absensi::create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $this->schedule->id,
            'tanggal_absensi' => Carbon::today(),
            'waktu_masuk' => Carbon::today()->setTime(8, 25),
            'status' => 'hadir', // Will be updated
            'periode' => 1
        ]);

        // Create lateness rule
        AturanKeterlambatan::create([
            'nama_aturan' => 'Test Rule',
            'menit_dari' => 1,
            'menit_sampai' => 30,
            'jenis_denda' => 'per_menit',
            'denda_per_menit' => 1000,
            'is_active' => true
        ]);

        $result = AttendanceService::processAttendanceRecord($attendance);

        $this->assertTrue($result['status_updated']);
        $this->assertArrayHasKey('lateness_data', $result);
        $this->assertEquals(10, $result['lateness_data']['minutes_late']);
        $this->assertEquals(10000, $result['lateness_data']['deduction_amount']);
    }
}
